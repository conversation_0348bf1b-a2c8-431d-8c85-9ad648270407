swagger: "2.0"
info:
  title: Admin API
  description: >
    Admin API the BFF for the Pigeon Admin UI.
  version: 1.0.72
  contact:
    name: 'Pigeon Team'
    url: 'https://confluence.agile.bns/display/PIGEON'
  x-implemented-by:
    - Canada
basePath: /v1
schemes:
  - http
  - https
produces:
  - application/json
consumes:
  - application/json
securityDefinitions:
  oauth2:
    type: oauth2
    flow: application
    tokenUrl: https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/token
    scopes:
      ca:baas:rules:read: Grants a read access to rules
      ca:baas:rules:write: Grants a write access to rules
      ca:baas:alert-rules:read: <PERSON> a read access to alert rules
      ca:baas:alert-rules:write: <PERSON> a write access to alert rules
      ca:baas:campaign-rules:read: Grant a read access to campaign rules
      ca:baas:campaign-rules:write: <PERSON> a write access to campaign rules
paths:
  /variable-mappings/sets:
    get:
      description: >
        Returns a list of variable mapping sets
      security:
        - oauth2: ['ca:baas:rules:read']
      tags:
        - variable mappings
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/v1.VariableMappingSetsResponse'
        '401':
          description: Unauthorized
          schema:
            $ref: '#/definitions/UnauthorizedErrorResponse'
        '403':
          description: Forbidden
          schema:
            $ref: '#/definitions/ForbiddenErrorResponse'
        '500':
          description: Internal server error
          schema:
            $ref: '#/definitions/InternalServerErrorResponse'
  /variable-mappings:
    get:
      description: >
        Returns a list of variable mappings
      security:
        - oauth2: ['ca:baas:rules:read']
      tags:
        - variable mappings
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/v1.VariableMappingsResponse'
        '401':
          description: Unauthorized
          schema:
            $ref: '#/definitions/UnauthorizedErrorResponse'
        '403':
          description: Forbidden
          schema:
            $ref: '#/definitions/ForbiddenErrorResponse'
        '500':
          description: Internal server error
          schema:
            $ref: '#/definitions/InternalServerErrorResponse'
  /variable-mappings/types:
    get:
      description: >
        Returns a list of variable mapping types
      security:
        - oauth2: ['ca:baas:rules:read']
      tags:
        - variable mappings
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/v1.VariableMappingTypesResponse'
        '401':
          description: Unauthorized
          schema:
            $ref: '#/definitions/UnauthorizedErrorResponse'
        '403':
          description: Forbidden
          schema:
            $ref: '#/definitions/ForbiddenErrorResponse'
        '500':
          description: Internal server error
          schema:
            $ref: '#/definitions/InternalServerErrorResponse'

  /variable-mappings/sets/{variable_set_id}:
    parameters:
      - $ref: '#/parameters/variable_set_id'
    patch:
      description: >
        Updates the specified variable_set_id
      security:
        - oauth2: ['ca:baas:rules:read']
      tags:
        - variable mappings
      responses:
        '204':
          description: Success
        '401':
          description: Unauthorized
          schema:
            $ref: '#/definitions/UnauthorizedErrorResponse'
        '403':
          description: Forbidden
          schema:
            $ref: '#/definitions/ForbiddenErrorResponse'
        '500':
          description: Internal server error
          schema:
            $ref: '#/definitions/InternalServerErrorResponse'
  
    get:
      description: >
        Gets the specified  variable_set_id
      security:
        - oauth2: ['ca:baas:rules:read']
      tags:
        - variable mappings
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/v1.VariableMappingSetResponse'
        '401':
          description: Unauthorized
          schema:
            $ref: '#/definitions/UnauthorizedErrorResponse'
        '403':
          description: Forbidden
          schema:
            $ref: '#/definitions/ForbiddenErrorResponse'
        '500':
          description: Internal server error
          schema:
            $ref: '#/definitions/InternalServerErrorResponse' 
  /v2/variable-mappings/sets:
    post:
      parameters:
        - $ref: '#/parameters/authorization'
        - $ref: '#/parameters/variableMappingSetCreateBody'
      description: >
        This endpoint is to only create a new variable mapping set draft from an active mapping set.
      security:
        - oauth2: ['ca:baas:rules:write']
      tags:
        - variable mappings
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/v1.VariableMappingSetResponse'
        '400':
          description: Bad request
          schema:
            $ref: '#/definitions/BadRequestErrorResponse'
        '401':
          description: Unauthorized
          schema:
            $ref: '#/definitions/UnauthorizedErrorResponse'
        '403':
          description: Forbidden
          schema:
            $ref: '#/definitions/ForbiddenErrorResponse'
        '500':
          description: Internal server error
          schema:
            $ref: '#/definitions/InternalServerErrorResponse'   

  /v2/variable-mappings/sets/{variable_set_id}:
    patch:
      parameters:
        - $ref: '#/parameters/variable_set_id'
        - $ref: '#/parameters/authorization'
        - $ref: '#/parameters/variableMappingSetUpdateBody'
      description: >
        This endpoint is only used to update the status of a variable set. Use this endpoint when you want to 
        publish a draft, reject a draft, unpublish a draft, approve a draft or delete a draft. This endpoint requires the consumer to
        pass in status and optionally description to move the variable set along the workflow.
      security:
        - oauth2: ['ca:baas:rules:write']
      tags:
        - variable mappings
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/v1.VariableMappingSetResponse'
        '400':
          description: Bad request
          schema:
            $ref: '#/definitions/BadRequestErrorResponse'
        '401':
          description: Unauthorized
          schema:
            $ref: '#/definitions/UnauthorizedErrorResponse'
        '403':
          description: Forbidden
          schema:
            $ref: '#/definitions/ForbiddenErrorResponse'
        '500':
          description: Internal server error
          schema:
            $ref: '#/definitions/InternalServerErrorResponse' 
    put:
      parameters:
        - $ref: '#/parameters/variable_set_id'
        - $ref: '#/parameters/authorization'
        - $ref: '#/parameters/variableMappingSetUpdateBody'
      description: >
        This endpoint is only used to update the variable mappings for a DRAFT variable set. It is not to update the status or in other words
        this endpoint is not concerned about moving the variable set through the workflow rather only editing the variable set draft itself.
      security:
        - oauth2: ['ca:baas:rules:write']
      tags:
        - variable mappings
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/v1.VariableMappingSetResponse'
        '400':
          description: Bad request
          schema:
            $ref: '#/definitions/BadRequestErrorResponse'
        '401':
          description: Unauthorized
          schema:
            $ref: '#/definitions/UnauthorizedErrorResponse'
        '403':
          description: Forbidden
          schema:
            $ref: '#/definitions/ForbiddenErrorResponse'
        '500':
          description: Internal server error
          schema:
            $ref: '#/definitions/InternalServerErrorResponse' 

parameters:
  authorization:
    name: Authorization
    description: JSON Web Token
    in: header
    type: string
    format: '^Bearer\s(.*)$'
    required: false
  variable_set_id:
    name: variable_set_id
    description: Unique variable Set Id
    in: path
    type: string
    required: true  
  variableMappingSetCreateBody:
    name: variableMappingSetCreateBody
    description: Request body to create a new variable mapping set draft
    in: body
    schema:
      $ref: '#/definitions/v2.CreateVariableMappingRequest'
    required: true
  variableMappingSetUpdateBody:
    name: variableMappingSetUpdateBody
    description: Request body to update an existing variable mapping set draft
    in: body
    schema:
      $ref: '#/definitions/v2.UpdateVariableMappingRequest'
    required: true
  variableMappingSetPatchBody:
    name: variableMappingSetPatchBody
    description: Request body to update the status of a variable mapping set draft
    in: body
    schema:
      $ref: '#/definitions/v2.PatchVariableMappingRequest'
    required: true

definitions:
  v1.VariableMappingSetsResponse:
    description: Variable Mappings response
    type: object
    properties:
      data:
        $ref: '#/definitions/v1.VariableMappingsList'
      notifications:
        type: array
        items:
          $ref: '#/definitions/ErrorResponse'
  v1.VariableMappingsResponse:
    description: Variable Mappings response
    type: object
    properties:
      data:
        $ref: '#/definitions/v1.variables'
      notifications:
        type: array
        items:
          $ref: '#/definitions/ErrorResponse'
  v1.VariableMappingTypesResponse:
    description: Variable Mappings Types response
    type: object
    properties:
      data:
        $ref: '#/definitions/v1.variableType'
      notifications:
        type: array
        items:
          $ref: '#/definitions/ErrorResponse' 
  v1.VariableMappingSetResponse:
    description: Variable Mapping response
    type: object
    properties:
      data:
        $ref: '#/definitions/v1.VariableMapping'
      notifications:
        type: array
        items:
          $ref: '#/definitions/ErrorResponse'               
  v1.VariableMappingsList:
      type: array
      description: Variable Mappings List
      items:
        $ref: '#/definitions/v1.VariableMapping'  
  v1.variables:
    description: Variables mapping  response
    type: object
    properties:
      variable_type:
        description: The data type of this variable
        type: string
        example: text
        enum: [text, currency, date, number]
      variable_campaign:
        description: variable_campaign
        type: string
        example: cust_full_name  
      variable_template:
        description: variable_template
        type: string
        example: SOLUI_CUST_FULL_NAME_END  
  v1.variableType:
    description: Variables mapping Types response
    type: object
    properties:
      id:
        description: variable type id
        type: integer
        example: 1
      name:
        description: variable name
        type: string
        example: "date"
      description:
        description: variable description
        type: string
        example: "Date (MMDDYYY)"           
  v1.VariableMapping:
    type: object
    description: Variable Mapping Model which identifies the variable name, source and source field name
    properties:
      variable_set_id:
        description: Variable set id
        type: integer
        example: 1
      created_at:
        description: Variable created time
        type: string
        example: "2020-12-30T16:39:22.481Z"
      created_by:
        description: Author for variable name creation
        type: string
        example: s6983282
      approver_sid:
        description: The sId of the user who is selected to be the approver for the variable mapping set.
        type: string
        example: s6983282
      status:
        description: Status of the variable
        type: string
        example: active   
      updated_at:
        description: Variable updated time
        type: string
        example: "2020-12-30T17:39:22.481Z"
      updated_by:
        description: Author for variable name updation
        type: string
        example: "s7646571" 
      variables:
        type: array
        items:
          $ref: '#/definitions/v1.variables'  
  v2.CreateVariableMappingRequest:
    type: object
    description: Request body to create a new variable mapping set draft
    properties:
      approver_sid:
        type: string
        description: The sId of the user who is selected to be the approver for the variable mapping set. Needs to be valid sId format.
        example: "s9999999"
      description:
        type: string
        description: The comment that the user added when creating the variable mapping draft
        example: "add new mapping"
      variables:
        type: array
        items:
          $ref: '#/definitions/v1.variables'  
    required:
      - created_by_sid
      - variables
  v2.UpdateVariableMappingRequest:
    type: object
    description: Request body to update an existing variable mapping set draft
    properties:
      approver_sid:
        type: string
        description: The sId of the user who is selected to be the approver for the variable mapping set. Needs to be valid sId format.
        example: "s9999999"
      description:
        type: string
        description: The comment that the user added when updating the variable mapping draft
        example: "fix typo"
      variables:
        type: array
        items:
          $ref: '#/definitions/v1.variables'  
    required:
      - variables
  v2.PatchVariableMappingRequest:
    type: object
    description: Request body to update the status of an existing variable mapping set
    properties:
      approver_sid:
        type: string
        description: The sId of the user who is selected to be the approver for the variable mapping set. Needs to be valid sId format.
        example: "s9999999"
      description:
        type: string
        description: The comment that the user added when updating the variable mapping set
        example: "approved"
      status:
        type: string
        description: The status to update the variable mapping set to.
        example: "pending"
        enum: ["active", "pending", "draft", "deleted"]
    required:
      - status
  BadRequestErrorResponse:
    allOf:
      - $ref: '#/definitions/ErrorResponse'
    example:
      code: 'HTTP_BAD_REQUEST'
      message: 'Validation error'
      uuid: '7b248dec-44f3-4d79-a075-af39aaa04564'
      timestamp: '2018-08-02T14:18:49.423Z'
      metadata: ['Rule name is missing']
  UnauthorizedErrorResponse:
    allOf:
      - $ref: '#/definitions/ErrorResponse'
    example:
      code: 'HTTP_UNAUTHORIZED'
      message: 'Authentication error'
      uuid: '7b248dec-44f3-4d79-a075-af39aaa04564'
      timestamp: '2018-08-02T14:18:49.423Z'
      metadata: ['JSON Web Token is missing']
  ForbiddenErrorResponse:
    allOf:
      - $ref: '#/definitions/ErrorResponse'
    example:
      code: 'HTTP_FORBIDDEN'
      message: 'Authorization error'
      uuid: '7b248dec-44f3-4d79-a075-af39aaa04564'
      timestamp: '2018-08-02T14:18:49.423Z'
      metadata: ['No permission to do something']
  InternalServerErrorResponse:
    allOf:
      - $ref: '#/definitions/ErrorResponse'
    example:
      code: 'HTTP_INTERNAL_SERVER_ERROR'
      message: 'Internal error'
      uuid: '7b248dec-44f3-4d79-a075-af39aaa04564'
      timestamp: '2018-08-02T14:18:49.423Z'
      metadata: []
  ErrorResponse:
    description: Error
    properties:
      code:
        description: Error code
        type: string
      message:
        description: Error message
        type: string
      uuid:
        description: Unique error identifier
        type: string
      timestamp:
        description: Error timestamp
        type: string
        format: date-time
      metadata:
        description: Optional context specific information
        type: array
        items:
          type: string
