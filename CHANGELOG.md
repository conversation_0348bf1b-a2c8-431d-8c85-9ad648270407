# Changelog

All notable changes to this project will be documented in this file. See
[standard-version](https://github.com/conventional-changelog/standard-version)
for commit guidelines.

### [1.25.1](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/compare/v25.0.0-16...v1.25.1) (2025-09-25)

## [1.25.0](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/compare/v25.0.0-rc.1-25-0.1...v1.25.0) (2025-09-25)

## [11.0.0-6](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/compare/v11.0.0-5...v11.0.0-6) (2025-09-16)

### Bug Fixes

- aquasec 'Sensitive Data' in 3rd party test private keys
  ([5995c5b](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/commit/5995c5bc8877f32880b650ccbb68f371cd8cb829))

## [11.0.0-5](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/compare/v11.0.0-4...v11.0.0-5) (2025-09-15)

### Bug Fixes

- aquasec critical and high vulnerabilities
  ([e2796a2](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/commit/e2796a2ebfb9d086f85375bfe37abd021778b087))

## [11.0.0-4](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/compare/v11.0.0-3...v11.0.0-4) (2025-09-09)

## [11.0.0-3](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/compare/v11.0.0-2...v11.0.0-3) (2025-09-09)

## [11.0.0-2](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/compare/v11.0.0-1...v11.0.0-2) (2025-09-09)

## [11.0.0-1](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/compare/v11.0.0-rc.sept-2025.17...v11.0.0-1) (2025-09-08)

## [11.0.0-rc.sept-2025.17](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/compare/v11.0.0...v11.0.0-rc.sept-2025.17) (2025-09-05)

### Bug Fixes

- **PIGEON-5762:** fix render tag in target product
  ([4eba08f](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/commit/4eba08ff5445b37129c8109c96fd226efae9cd56))

## [11.0.0-rc.sept-2025.11](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/compare/v11.0.0-rc.sept-2025.6...v11.0.0-rc.sept-2025.11) (2025-09-04)

### Bug Fixes

- **PIGEON-5773:** remove slack service fixes checkmarx vulnerabilities
  ([af267a5](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/commit/af267a55760602e01773c51e9939979ecb0083f3))

## [11.0.0-rc.sept-2025.6](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/compare/v11.0.0-rc.sept-2025.3...v11.0.0-rc.sept-2025.6) (2025-09-03)

### Bug Fixes

- **PIGEON-5629:** orion inbox component
  ([b9f8852](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/commit/b9f88522b3093fbc62a0a4ce5e130818e0c6159d))

## [11.0.0-rc.sept-2025.3](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/compare/v11.0.0-rc.sept-2025.1...v11.0.0-rc.sept-2025.3) (2025-09-03)

### Bug Fixes

- fix rule list test cases
  ([a29918a](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/commit/a29918a23b18673ffa984207c4255ce4fb3804f2))

## [11.0.0-rc.sept-2025.1](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/compare/v25.0.0-rc.sept-2025.101...v11.0.0-rc.sept-2025.1) (2025-08-27)

### Bug Fixes

- **PIGEON-5762,PIGEON-5763:** fix product group issue
  ([5693aca](https://bitbucket.agile.bns:7999/pigeon/admin-atlas/commit/5693aca678cfa4ea3fa1d14c406fbc40babb6c62))

## [1.24.0](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.23.1...1.24.0) (2025-06-24)

### Bug Fixes

- **PIGEON-5628:** remove campaign id filter in placement and access pages
  ([3768561](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3768561ac22857a326c905201cf62b0e0529aed2))

### [1.23.1](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.21.3...1.23.1) (2025-05-23)

### Features

- **PIGEON-5522:** reorganize the URL parameters order in message center
  ([80ebbe8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/80ebbe8121f1ff3dc81ae24b7b9bcbba9e761fba))

## [1.23.0](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.21.3...1.23.0) (2025-04-08)

### Features

- **PIGEON-5522:** reorganize the URL parameters order in message center
  ([80ebbe8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/80ebbe8121f1ff3dc81ae24b7b9bcbba9e761fba))

### [1.21.3](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.21.2...1.21.3) (2025-03-21)

### [1.21.2](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.21.1...1.21.2) (2025-03-14)

## [1.21.1](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.20.2...1.21.1) (2025-02-28)

### [1.20.2](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.20.1...1.20.2) (2025-01-31)

### Bug Fixes

- **PIGEON-5333:** increase window size sol, storefront preview
  ([44ef9c1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/44ef9c1ae9416364cfa91854158447ee113b8a00))

### [1.20.1](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.20.0...1.20.1) (2025-01-14)

### Features

- boilerplate for message centre form
  ([012a0ed](https://bitbucket.agile.bns:7999/pigeon/admin/commit/012a0edd18544503f719a741de1ccd4a5634a326))
- **PIGEON-4986:** added base list page
  ([c784ef6](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c784ef6ab0b0cbf0aa87e1c6e48732d59ef842a9))
- **PIGEON-4986:** added message centre tabs in Main and sub navigation
  ([fbaed41](https://bitbucket.agile.bns:7999/pigeon/admin/commit/fbaed410c4dc27438804fa4d80f0268719b2db54))
- **PIGEON-4987:** campaign information card
  ([528ef8b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/528ef8ba9a9e5918aef92e21ae22b08d536c5e9e))
- **PIGEON-4988:** disable time for datepicker
  ([c503a32](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c503a320c6b9c2dc887893e06352cf1ea31e6b07))
- **PIGEON-4988:** message settings fields
  ([c798c7e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c798c7e8aaf06255b614e2c15c837e210ffe30dc))
- **PIGEON-4988:** targeting fields
  ([922504d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/922504d3b24845863911694e2e65782934c0e01a))
- **PIGEON-4988:** targeting fields fix css
  ([c607a61](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c607a612ea2a0ded5fe9584b909cda941139cb5b))
- **PIGEON-4988:** targeting form
  ([12a8995](https://bitbucket.agile.bns:7999/pigeon/admin/commit/12a89954a5107bbd03c2a36b9d87236fb9bbfd6d))
- **PIGEON-4990:** message centre create form
  ([9942fed](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9942fed019a8fc83cad8df76f8f75768716413b9))
- **PIGEON-4990:** update form fields name and validation
  ([ad8fa18](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ad8fa1890fda796a09c8de8924f48ed5a931cdc2))
- **PIGEON-4990:** update form fields name and validation
  ([0602e71](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0602e7180aedba8b15fd6b62a4f4a958dd6b8142))
- **PIGEON-4990:** update params, schema
  ([42da902](https://bitbucket.agile.bns:7999/pigeon/admin/commit/42da9022e4c1601f01234e19cba01122d2a7ccec))
- **PIGEON-4991:** campaigns list screen for message centre
  ([b80ee42](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b80ee42791492a0fe6fd0e3b76748a3cf24de237))
- **PIGEON-4992:** add export feature in messages page
  ([94e8a9c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/94e8a9cb545b4c55606cba920e3e4a06cfdaf13d))
- **PIGEON-4994:** add list screen for message centre message tab
  ([3293da0](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3293da09b2857e44f2080abf8f86583cc1408067))
- **PIGEON-4994:** add search filter to message centre messages list screen
  ([50cd1f8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/50cd1f81430e4501276b042a1dca4c623bf10756))
- **PIGEON-4994:** add search filter to message centre messages list screen
  ([378ad3f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/378ad3f72554863f4dff2cad2b64a4c4914e0a4a))
- **PIGEON-4994:** add table and pagination for message centre messages
  ([21931dd](https://bitbucket.agile.bns:7999/pigeon/admin/commit/21931ddd29da0c8cd436cc058a9f87b86bc8012d))
- **PIGEON-4994:** add table and pagination for message centre messages
  ([99cba03](https://bitbucket.agile.bns:7999/pigeon/admin/commit/99cba03b7779f58517c32422375047b86fdc1f8d))
- **PIGEON-4994:** initial screen should be empty, only display results based on
  provided filters
  ([66d2193](https://bitbucket.agile.bns:7999/pigeon/admin/commit/66d219368fd018f3e19cac0c392739607b6d8668))
- **PIGEON-4995:** add filter and export to messages list page
  ([a1a3752](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a1a3752348bb72eeebc378763bbaa4fac89a68a4))
- **PIGEON-4995:** open actions menu for only one message
  ([116e25f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/116e25f588b61eaa5ebc8c2f26a6a3d22c0af634))
- **PIGEON-4996:** add action menu and right side panel for customer message
  screen
  ([efcf38d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/efcf38d65c44f181110f7056bd08f8794220f6d6))
- **PIGEON-4997:** message details form, rename files
  ([1bb3d06](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1bb3d062ad47b386e2fcdbc2e8d863d3d29fe35c))
- **PIGEON-4998:** added redirect link from messages screen to campaigns
  ([219d507](https://bitbucket.agile.bns:7999/pigeon/admin/commit/219d507f3c27f7150f38fb70e56cbba0aca53577))
- **PIGEON-4998:** message centre redirect with filter by campaign id
  ([30df38d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/30df38d9f2e1281a25bde5b805463dc7eac9a5a2))
- **PIGEON-5005:** added message centre access config
  ([f08f477](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f08f4770ce200ca4ca36a29dcb79c271e97e7d70))
- **PIGEON-5295:** updated last updated date filter
  ([e93226e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e93226e6000366194f9ad97d607853dde1e470f0))
- **PIGEON-5341:** add expired status filter
  ([8e36b87](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8e36b87783e4fcd3d97bedbd70ae9a06b73ca00d))
- **PIGEON-5341:** show expired status when end date is passed
  ([1c8872c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1c8872c36df5d9e6dc45a89cbc5c6d50f41dbb5f))
- **PIGEON-5342, PIGEON-5372:** delete campaign, bugfix msg_text not being saved
  ([2bad6ad](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2bad6ad57edfe713e1e4c28865a16c341115b51f))

### Bug Fixes

- empty search query not reloading, and footer overlay
  ([50e4398](https://bitbucket.agile.bns:7999/pigeon/admin/commit/50e4398c7badcd2c8e67fc40c70b945ba92bba48))
- **PIGEON-4994:** fix test case
  ([66769bb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/66769bb352f413b7321c3ac189ca8a380876b5b2))
- **PIGEON-4994:** fix test case
  ([876c8e8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/876c8e8dde1321ed520c0baefda83ba55ed4f3f9))
- **PIGEON-5051:** fix pages not mandatory during rule type containers
  ([8788404](https://bitbucket.agile.bns:7999/pigeon/admin/commit/878840410f62739c7ae986d33867a8558ea6030f))
- **PIGEON-5051:** make pages selection during container creation optional
  ([8876ffa](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8876ffae90a865196a45411b4c39ff3fe5c5e3f7))
- **PIGEON-5092:** fix uat urls, back button, and validations
  ([a097495](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a09749579732d6bc09fb8382933dcdbedbe554ec))
- **PIGEON-5092:** messages api updates, form changes
  ([ca7353a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ca7353af0e66e0ccd99e8f4f9d478ee905df6d88))
- **PIGEON-5092:** update message centre apis, env urls, bug fixes
  ([b9c1d65](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b9c1d657cc2adbbbb061fadaf41fc6fab15be7ca))
- **PIGEON-5268:** dropdown bug fix
  ([1cef369](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1cef369519e15ed8a85466eb33a4c4b31499af95))
- **PIGEON-5268:** update dropdown descriptions
  ([84e85af](https://bitbucket.agile.bns:7999/pigeon/admin/commit/84e85afe7189023feea94d406ad3c02d6580a7de))
- **PIGEON-5295:** added last update date column to messages listing
  ([774b6e8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/774b6e861b922cc31a44b51b5328091af8937fb4))
- **PIGEON-5297:** fix typo issue for column 'Name' -> 'Campaign Name' in export
  file
  ([ccf70a8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ccf70a8690d5f8ff518525cd6abed0388efd2dfe))
- **PIGEON-5306:** add custom search functionality for messages/offers
  ([80fa0ae](https://bitbucket.agile.bns:7999/pigeon/admin/commit/80fa0aeb79ed388d0b81f949fee74108bbc3768e))
- **PIGEON-5325:** adjust position of 'Clear all' button to match design on
  Figma
  ([bc3e383](https://bitbucket.agile.bns:7999/pigeon/admin/commit/bc3e383b2f79fa4b283e2d520101aee30150e192))
- **PIGEON-5326 & PIGEON-5327:** fix date filters and 'Clear all' functionality
  ([814c003](https://bitbucket.agile.bns:7999/pigeon/admin/commit/814c00396950b4037afdd52676066aba2f78c3bb))
- **PIGEON-5328, PIGEON-5329, PIGEON-5332:** details screen and side panel
  issues
  ([b89a7a9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b89a7a94be699c9a31f0c0f677ae95e60a4127ba))
- **PIGEON-5331:** update fields on sidePanel and campaign routes to match with
  campaign mgmt api
  ([e8f5bf7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e8f5bf7146b6a41c7e54b181a2067152e98b71a9))
- **PIGEON-5334 & PIGEON-5299:** fix empty date error messsage and date
  comparison validation logic
  ([898be16](https://bitbucket.agile.bns:7999/pigeon/admin/commit/898be166e0acaec6d377511539bd1689b1c50693))
- **PIGEON-5335 & PIGEON-5336:** required fields error messages and KT Campaign
  label
  ([22554fa](https://bitbucket.agile.bns:7999/pigeon/admin/commit/22554fa97f4ad966e02211fd351db6e6a0b4385b))
- **PIGEON-5337:** invalid error message for date fields fix
  ([fadd325](https://bitbucket.agile.bns:7999/pigeon/admin/commit/fadd325af6568a4d15ea84d88b609d8a4255d022))
- **PIGEON-5343:** end date validation for 'Edit Message' screen
  ([bddd0e8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/bddd0e8fd19403f4220a81c9c2c53711fc1f7905))
- **PIGEON-5355:** messages listing export function issue
  ([289d37e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/289d37e39f1d396f6c832778796608a4404204cd))
- **PIGEON-5365:** error when perform 'Suspended' and 'Activate' actions
  ([073ac9e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/073ac9e7ca150b1c7bbaea2197b7bf8a8fb87cc6))
- **PIGEON-5366:** 'Duplicate' action is editing existing record not creating a
  copy
  ([866bce9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/866bce956e361dccf592b062d3d45047c8329d1d))
- **PIGEON-5367:** 'Message Display Parameters' being pushed out of container
  ([bb289cf](https://bitbucket.agile.bns:7999/pigeon/admin/commit/bb289cf9fbd685cc3da525cba6c0d80b413268fd))
- **PIGEON-5378:** 'Delete' status should not be in the campaign filters
  ([a31651c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a31651c8a934e92bd8a737a5d072e4fc4839dfd5))
- **PIGEON-5379:** update error message for duplicate campaign
  ([33c7d23](https://bitbucket.agile.bns:7999/pigeon/admin/commit/33c7d235a1a4bc0d5badbc6ec8d8ac6bbd0cc4d5))
- **PIGEON-5387:** side bar in campaigns listing screen not display url params
  correctly
  ([9926d18](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9926d1881a70012659a441b1abe8766746c07938))

## [1.20.0](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.19.1...1.20.0) (2024-11-26)

### Features

- **PIGEON-4940:** update buildpack version and stack for node v20.15.1
  ([0bc9e06](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0bc9e061c613eaca4d3897dc027d70569c475a6c))

### [1.19.1](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.19.0...1.19.1) (2024-11-13)

## [1.19.0](https://bitbucket.agile.bns:7999/pigeon/admin/compare/v1.2.0...v1.19.0) (2024-11-13)

### Features

- **PIGEON-5289:** switch pigeon API to use marvel product service v2 endpoint
  ([eafb742](https://bitbucket.agile.bns:7999/pigeon/admin/commit/eafb742ef351db13734104f0e41524c07e96a509))

### [1.18.2](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.18.1...1.18.2) (2024-10-09)

### [1.18.1](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.18.0...1.18.1) (2024-10-08)

## [1.18.0](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.16.0...1.18.0) (2024-10-04)

### Bug Fixes

- **PIGEON-5022:** unable to edit other fields in duplicate campaign if 'pages'
  is empty
  ([7031183](https://bitbucket.agile.bns:7999/pigeon/admin/commit/703118305073c78c366e1e05eedeceef25cb8ea6))
- **PIGEON-5277:** reset rule_type to null if application selection change
  ([fd30171](https://bitbucket.agile.bns:7999/pigeon/admin/commit/fd30171555ed8bc165c61a4c8e2ce45e6db22a21))

## [1.17.0](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.16.0...1.17.0) (2024-07-25)

### Bug Fixes

- **PIGEON-5022:** unable to edit other fields in duplicate campaign if 'pages'
  is empty
  ([7031183](https://bitbucket.agile.bns/projects/pigeon/repos/admin/commits/703118305073c78c366e1e05eedeceef25cb8ea6))

## [1.16.0](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.15.2...1.16.0) (2024-05-31)

### Bug Fixes

- **PIGEON-4983:** add notes for orion campaign box container
  ([cbbf73d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/cbbf73d045ba702b463bf080247f679663dba91d))

## [1.15.0](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.14.0...1.15.0) (2024-04-25)

### Features

- **PIGEON-4902:** add service, version to health check
  ([684724d](https://bitbucket.agile.bns/scm/pigeon/admin/commit/684724d8848e9d47caa18c3174bcc13353cef1b5))
- **PIGEON-4918:** enable isNew for standing campaign
  ([d9432c5](https://bitbucket.agile.bns/scm/pigeon/admin/commit/d9432c5693c427dab4edb7ab2fc17b6e6bdd56cd))
- **PIGEON-4920:** language label
  ([36e1412](https://bitbucket.agile.bns/scm/pigeon/admin/commit/36e1412031ccfdacde8be92a897edd3f557943b1))
- **PIGEON-4920:** support targeting rules by languages
  ([33313e5](https://bitbucket.agile.bns/scm/pigeon/admin/commit/33313e512ad1bc1a70cc5539355f0b02d4c6582e))
- **PIGEON-4921:** language dropdown filter with targeted languages of rule
  ([724258b](https://bitbucket.agile.bns/scm/pigeon/admin/commit/724258b25cf6c7cd8606d648b620ace25aedf4d7))

### Bug Fixes

- **PIGEON-4931:** limit the application, page and container ID changes
  ([b3832c5](https://bitbucket.agile.bns/scm/pigeon/admin/commit/b3832c5bfde276ac109b57f36d7a5a351cb30b2a))

## [1.14.0](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.13.2...1.14.0) (2024-03-25)

### Features

- **PIGEON-3631:** added toggle to show new badge
  ([66e2645](https://bitbucket.agile.bns/scm/pigeon/admin/commit/66e2645881a0dcdbb501a86b509ef62dd91995ac))
- **PIGEON-4561:** enforce code coverage min in jest config
  ([9a44d3b](https://bitbucket.agile.bns/scm/pigeon/admin/commit/9a44d3b3756a4d73e6b2f9fc6f2d773aea2d88e8))
- **PIGEON-4736:** fix tests and package
  ([55798b6](https://bitbucket.agile.bns/scm/pigeon/admin/commit/55798b6dfa3fcf6d26fbcc093a3ec60052e5adc3))
- **PIGEON-4736:** update alterts template neo
  ([1a44f03](https://bitbucket.agile.bns/scm/pigeon/admin/commit/1a44f037bdfde49725f238d57fd2fd12f7d7475a))
- **PIGEON-4889:** removed accp auth & passport env variables
  ([b18eccd](https://bitbucket.agile.bns/scm/pigeon/admin/commit/b18eccd4c7e1e17031cf073db3afcfe501839771))

### Bug Fixes

- **PIGEON-4196:** fix secondary button height in team confirmation modal
  ([f4917ff](https://bitbucket.agile.bns/scm/pigeon/admin/commit/f4917ff2799c9a6cbcb61aca31ed64e5faae4ecb))
- **PIGEON-4394:** redirect to base url and dispatch userauth
  ([32abea5](https://bitbucket.agile.bns/scm/pigeon/admin/commit/32abea53790f4951d605e99a13ffe307b6350b32))
- **PIGEON-4741:** fix stale data issue
  ([d3372b8](https://bitbucket.agile.bns/scm/pigeon/admin/commit/d3372b89a12b6b667486d6b59f5cfbb3169933cd))
- **PIGEON-4905:** improve error message for non existing users
  ([5c770c2](https://bitbucket.agile.bns/scm/pigeon/admin/commit/5c770c226553705b8476c9d14e3c85b1221e6994))
- **PIGEON-4905:** spelling
  ([8d44d1d](https://bitbucket.agile.bns/scm/pigeon/admin/commit/8d44d1d11b1d91cca80b1ebca0b27fae4c9e095e))
- **PIGEON-4905:** use BadRequestError, and try catch
  ([ebef02f](https://bitbucket.agile.bns/scm/pigeon/admin/commit/ebef02fd431fd3c729c16fdf1df5a50606219bf2))
- **PIGEON-4905:** use CustomError to send client message
  ([c2379f9](https://bitbucket.agile.bns/scm/pigeon/admin/commit/c2379f9ba3844a0fbdfe60f752217ad2c5df8300))
- **PIGEON-4906:** fix advanced targeting section disappearing if application is
  changed
  ([7b43509](https://bitbucket.agile.bns/scm/pigeon/admin/commit/7b43509bfbf889f0c46b57b6e47e16fbbfd4d500))
- **PIGEON-4909:** fix blank fields in edit application screen
  ([31e1476](https://bitbucket.agile.bns/scm/pigeon/admin/commit/31e14767a190081886327fed4ff184cce29035b1))
- **PIGEON-4910:** fix displaying wrong application id on application detail
  page
  ([7f3394b](https://bitbucket.agile.bns/scm/pigeon/admin/commit/7f3394b62b46d4bf796fe7ca95f831678336dd25))
- **PIGEON-4914:** update ABM ODP French version selectedText - bump PWR version
  ([61eb7b6](https://bitbucket.agile.bns/scm/pigeon/admin/commit/61eb7b6f86517d5884556f8e2551ed2edfa68ecd))
- **PIGEON-4915:** fixes forms being disabled
  ([8a74e5b](https://bitbucket.agile.bns/scm/pigeon/admin/commit/8a74e5b7b6574595d8b32f13716933f0f55062ae))

### [1.13.2](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.13.1...1.13.2) (2024-03-12)

### Bug Fixes

- **PIGEON-4930:** fix start and end dates on creating new rule
  ([da20796](https://bitbucket.agile.bns:7999/pigeon/admin/commit/da207969948f8d67f32ac1bb2821476c48dc173c))

### [1.13.1](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.13.0...1.13.1) (2024-02-20)

## [1.13.0](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.12.1...1.13.0) (2024-02-14)

### Features

- **PIGEON-3544:** refactor front end unit test to handle exceptions
  ([51d852b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/51d852b39cc6c056fff0a2ae269d298b4a9dd3c1))
- **PIGEON-3544:** refactor front end unit test to handle exceptions
  ([0ccc0f9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0ccc0f9be8296fb80a91c5cc5de549cad4b20102))
- **PIGEON-4734:** add transaction support to remaining page service endpoints
  ([8a0bc68](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8a0bc68f2c1df5235b0b839d3f03314a33c156fd))
- **PIGEON-4823:** generate group name for products missing parents
  ([a915d2f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a915d2f2ffc7c1992f27251832976e06b69f3913))
- **PIGEON-4823:** product book auto update
  ([9d2093c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9d2093cc892308d4ff08f981122f4911e855e82e))
- **PIGEON-4871:** updated node version to 18.17.1
  ([87fb46c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/87fb46c54fc912f52649e25bd1ff1c4bfe1416ef))

### Bug Fixes

- **PIGEON-4294:** fix memory leak on adding team owner
  ([5a13db5](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5a13db5863e495bac3eea6198fbd6b39f9d51213))
- **PIGEON-4294:** fix memory leak on adding team owner
  ([dc60a5a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/dc60a5abaae414839510a2152794cc7d37e621af))
- **PIGEON-4394:** redirect to base url and dispatch userauth
  ([6de3197](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6de3197a70399356ea1fc8832bb43f9b6cb48dd0))
- **PIGEON-4741:** fix stale data issue
  ([6b694bf](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6b694bfe78b3e76ff229c2507b2ad2e977166e25))
- **PIGEON-4867:** check for rulesubtype before adding to db - create
  application
  ([e0d0e51](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e0d0e51bec1b2527e9c1db9183b977c018840427))
- **PIGEON-4872:** fix slack notification on rule assignment step
  ([a086d4a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a086d4adbc0bcb50824f6d0f7241cbadb610b765))
- **PIGEON-4877:** migrate to react hook form for application details
  ([ab4c498](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ab4c498eb5eeb53d54048c4dbf67f1d0be4580ed))
- **PIGEON-4877:** set application ID error when creating/editing applications
  ([0153100](https://bitbucket.agile.bns:7999/pigeon/admin/commit/01531009f76bedabd455d34732bf7337216c2850))
- **PIGEON-4878:** fix disabled screen after switching app when duplicating
  alert
  ([a86b7bc](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a86b7bc07cb6b20db03c94ee02b269223ae78b94))
- **PIGEON-4879, PIGEON-4880:** fix infinite field register/unreg by declaring
  validation fn outside
  ([6210536](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6210536d2fa6633e1a4bd0ca8a292536fd5e28d3))
- **PIGEON-4890:** pass wamLogoutUrl on logout
  ([eef3832](https://bitbucket.agile.bns:7999/pigeon/admin/commit/eef3832fd31385e78a498ecc663e609744de41c9))

### [1.12.1](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.12.0...1.12.1) (2024-01-15)

### Bug Fixes

- **PIGEON-4878:** fix disabled screen after switching app when duplicating
  alert
  ([a3a7b89](https://bitbucket.agile.bns/scm/pigeon/admin/commit/a3a7b89b8c50f0408bbd96675922f2c8375aca4d))
- **PIGEON-4879, PIGEON-4880:** fix infinite field register/unreg by declaring
  validation fn outside
  ([68a4dfd](https://bitbucket.agile.bns/scm/pigeon/admin/commit/68a4dfd3179c9f3fbd0cf7fe282b149e5e32b6ee))

## [1.12.0](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.11.3...1.12.0) (2024-01-09)

### Features

- **PIGEON-4599:** enforce lowercase application id
  ([bb62e54](https://bitbucket.agile.bns/scm/pigeon/admin/commit/bb62e5456067743055d93ec5ea0af520c56be03e))

### Bug Fixes

- **PIGEON-3858:** bump PWR version to 2.3.23
  ([d967a0a](https://bitbucket.agile.bns/scm/pigeon/admin/commit/d967a0af6162749fa855234acf62c51f6ca70f6a))
- **PIGEON-4269:** disable access for all items and group
  ([b5ba39d](https://bitbucket.agile.bns/scm/pigeon/admin/commit/b5ba39d72dbf308fdf8b84e04561b69a77cd731f))
- **PIGEON-4269:** make all access and manage enabled for Pigeon team
  ([2561262](https://bitbucket.agile.bns/scm/pigeon/admin/commit/25612627872ccc08da1e5df6a8d8937e23b5f67b))
- **PIGEON-4269:** remove ensureValidPermissions
  ([045f1b4](https://bitbucket.agile.bns/scm/pigeon/admin/commit/045f1b4f15f1d0d0656622d6f6524c0b79967830))
- **PIGEON-4335:** remove dispatch
  ([92f9cdb](https://bitbucket.agile.bns/scm/pigeon/admin/commit/92f9cdbcb38eeee5321c9302041006100cf821d2))
- **PIGEON-4335:** remove getTeam api call
  ([a06fdae](https://bitbucket.agile.bns/scm/pigeon/admin/commit/a06fdae5b102874e1811c1365a027f0e342fa00a))
- **PIGEON-4335:** remove teamName prop
  ([1460d6c](https://bitbucket.agile.bns/scm/pigeon/admin/commit/1460d6c71ea6414df6f5c50eb6bdfe85b5ce7a79))
- **PIGEON-4335:** use one getTeams call
  ([60b218b](https://bitbucket.agile.bns/scm/pigeon/admin/commit/60b218bf6b9bdd8f8b7bd8d8fd0b6c48ed443667))
- **PIGEON-4553:** add validation for application name
  ([53c1e49](https://bitbucket.agile.bns/scm/pigeon/admin/commit/53c1e49afcf319f9f08ad621200ea7ce843b59ce))
- **PIGEON-4553:** generate and disable placement ids
  ([60388ed](https://bitbucket.agile.bns/scm/pigeon/admin/commit/60388edcd9ad1512dc35e370c1b205b732cc317d))
- **PIGEON-4736:** add alert templates
  ([e4a4427](https://bitbucket.agile.bns/scm/pigeon/admin/commit/e4a4427a5f02011adead54b932a52db437ee3ce1))
- **PIGEON-4808:** add campain id validation on creating new rule
  ([26ea4a5](https://bitbucket.agile.bns/scm/pigeon/admin/commit/26ea4a529ac4591f596ef6e75a1b5a1cad8b03ff))
- **PIGEON-4835:** fix click outside the action list item on list page
  ([c1eff58](https://bitbucket.agile.bns/scm/pigeon/admin/commit/c1eff5884d3b3c351fdf28323d48aec6e6596852))
- **PIGEON-4836:** fix overlapping in content table on creating new storefront
  campaign
  ([c9a1a70](https://bitbucket.agile.bns/scm/pigeon/admin/commit/c9a1a70b54c8ac82eb2e8587ce35180600ffe556))
- **PIGEON-4837:** fix overlapping scrollbar on side panal
  ([c5ffc29](https://bitbucket.agile.bns/scm/pigeon/admin/commit/c5ffc29f9012bb1458627eff86f8923a1c0dc98e))
- **PIGEON-4847:** use thunk middleware to discard post mid-session kickout
  alerts
  ([303087b](https://bitbucket.agile.bns/scm/pigeon/admin/commit/303087b108049ba1d718ed32a26534f9156e541e))
- **PIGEON-4850:** fix load team details page
  ([2170812](https://bitbucket.agile.bns/scm/pigeon/admin/commit/21708127d891569998aa322a267a42446c512131))

### [1.11.3](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.11.2...1.11.3) (2023-11-29)

### Bug Fixes

- **PIGEON-4846:** reset content modal form on container change
  ([bc7aa29](https://bitbucket.agile.bns:7999/pigeon/admin/commit/bc7aa291267d8e04c26134ee4b01b8c4ce632b87))

### [1.11.2](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.11.1...1.11.2) (2023-11-21)

### [1.11.1](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.10.2...1.11.1) (2023-11-21)

## [1.11.0](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.10.2...1.11.0) (2023-11-20)

### Features

- **PIGEON-2859 PIGEON-4513:** migrate to wam sso
  ([85eb20a](https://bitbucket.agile.bns/scm/pigeon/admin/commit/85eb20af9667ccec915447dd044a0ef702acf44d))
- **PIGEON-4802:** enable dynamic options for content preview language selection
  ([1ef2048](https://bitbucket.agile.bns/scm/pigeon/admin/commit/1ef2048ddcb76b2bec045f630537142ea8ee4dd5))

### Bug Fixes

- check application/rule type combination when creating & updating rules from
  backend
  ([ee06fd3](https://bitbucket.agile.bns/scm/pigeon/admin/commit/ee06fd34a2e3b1d3cd47c010ac4556c6fe0b367e))
- **PIGEON-4237:** fix dark mode font is too faint
  ([86de823](https://bitbucket.agile.bns/scm/pigeon/admin/commit/86de82360b5b51f5d963eef9770b986aa29310a7))
- **PIGEON-4818:** pass campaign type for active and deactive
  ([dc98823](https://bitbucket.agile.bns/scm/pigeon/admin/commit/dc98823d1d9b510c05491be0f0514d9dec65248e))
- **PIGEON-4818:** update updatePartialSchema
  ([3f0d8c0](https://bitbucket.agile.bns/scm/pigeon/admin/commit/3f0d8c0bc62aaf288ab779282a1c257a0352449f))

### [1.10.2](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.10.1...1.10.2) (2023-10-23)

### [1.10.1](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.10.0...1.10.1) (2023-10-11)

## [1.10.0](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.9.0...1.10.0) (2023-10-11)

### Features

- increate sonar coverage
  ([edd45ac](https://bitbucket.agile.bns/scm/pigeon/admin/commit/edd45ace113f7ba959dbe4f63f4018b4594ea95c))
- **PIGEON-3575:** fix sidepanel style
  ([6ecbb86](https://bitbucket.agile.bns/scm/pigeon/admin/commit/6ecbb8619100037ba1c775b9fd225600a8ebbb94))
- **PIGEON-3575:** fix sidepanel style
  ([010a7e7](https://bitbucket.agile.bns/scm/pigeon/admin/commit/010a7e74f052e27cbb5d815d99dd0f12359b762c))
- **PIGEON-3575:** fix sidepanel width + sort tabel
  ([2593409](https://bitbucket.agile.bns/scm/pigeon/admin/commit/2593409563952a7f833f133bd7244cef91c039a2))
- **PIGEON-3575:** fix sidepanel width + sort tabel
  ([bfd31ef](https://bitbucket.agile.bns/scm/pigeon/admin/commit/bfd31ef0fde35515404207aa70b5d984d35b0b5c))
- **PIGEON-3575:** udpate canvas 13
  ([23126be](https://bitbucket.agile.bns/scm/pigeon/admin/commit/23126befbfa642c78b82497a1d222bbb39ecf5ae))
- **PIGEON-3575:** udpate canvas 13
  ([e9c851b](https://bitbucket.agile.bns/scm/pigeon/admin/commit/e9c851b5d5cdf4b4c7e9557e26ca818239b61e9d))
- **PIGEON-3575:** update package.json
  ([b2b5dfc](https://bitbucket.agile.bns/scm/pigeon/admin/commit/b2b5dfc88373522f76df63f0278b770d6abc5acb))
- **PIGEON-3575:** update sanpshots
  ([484bfab](https://bitbucket.agile.bns/scm/pigeon/admin/commit/484bfabb1e5a9bf72570becf6b51641a02b1b453))
- **PIGEON-4652:** handle sub navigation bar + useredirect hook
  ([443c6db](https://bitbucket.agile.bns/scm/pigeon/admin/commit/443c6db634e0de8fa448c98485df9cbb28123502))
- **PIGEON-4652:** handle sub navigation bar + useredirect hook
  ([7c1b85b](https://bitbucket.agile.bns/scm/pigeon/admin/commit/7c1b85b951ffb2e482596c0f46b6e90e5a7e926c))
- **PIGEON-4654:** add type property for approve and activate requests
  ([8dca089](https://bitbucket.agile.bns/scm/pigeon/admin/commit/8dca089a5fb020228f44098385617c600119b60c))
- **PIGEON-4654:** create and view ccau campaigns
  ([d4edfed](https://bitbucket.agile.bns/scm/pigeon/admin/commit/d4edfed9a7922c1adf8ac1b3ca41205070aed318))
- **PIGEON-4654:** unit test for CCAU, fix label, send type for update
  ([25e4516](https://bitbucket.agile.bns/scm/pigeon/admin/commit/25e45169b27b696536508307b6cb8105cbd51fc4))
- **PIGEON-4658:** fetch rules by type ccau_campaign or campaign
  ([1cf1592](https://bitbucket.agile.bns/scm/pigeon/admin/commit/1cf1592e5da51b9c999424f4f8ca6bee93f78c0c))
- **PIGEON-4664:** add ccau to rule types on application details screen
  ([9ad0b34](https://bitbucket.agile.bns/scm/pigeon/admin/commit/9ad0b341e9386fe5d828b079b27a0c84b4b51c7f))
- **PIGEON-4664:** add ccau to rule types on application details screen
  ([fb24cf7](https://bitbucket.agile.bns/scm/pigeon/admin/commit/fb24cf75b8d96e75e9994925824b3d687fc1702f))
- **PIGEON-4709:** add download pdf feature using image or html convertor
  ([1178c05](https://bitbucket.agile.bns/scm/pigeon/admin/commit/1178c056cd2f11d863ccb33eedfefd5039f240d0))
- **PIGEON-4709:** add download pdf feature using image or html convertor
  ([04b8f77](https://bitbucket.agile.bns/scm/pigeon/admin/commit/04b8f77045eeca449e50c254d4ee9d7ee0e0a09b))
- **PIGEON-4709:** fix formatimg the preview file
  ([d521af8](https://bitbucket.agile.bns/scm/pigeon/admin/commit/d521af8c94802dfeb53b6ec80f8946c48463681a))
- **PIGEON-4709:** remove --u from test script
  ([af1048e](https://bitbucket.agile.bns/scm/pigeon/admin/commit/af1048efcfd7942c69d6c9c2fdcd8753ff18b73a))
- **PIGEON-4716:** add ability to preview intercept
  ([d52803a](https://bitbucket.agile.bns/scm/pigeon/admin/commit/d52803a50b27b5c75ba0beaff8fbdaed51f88cda))
- update canEdit function to include CCAU changes
  ([c49606e](https://bitbucket.agile.bns/scm/pigeon/admin/commit/c49606eb5f019deabe631cd84f31e6fe5b66036a))

### Bug Fixes

- fix creating container
  ([9787afd](https://bitbucket.agile.bns/scm/pigeon/admin/commit/9787afd51fd3af95e97374ca9c67b04bb0013375))
- **PIGEON-3301:** reset date filter on clear all
  ([88de7ab](https://bitbucket.agile.bns/scm/pigeon/admin/commit/88de7ab86a33655ab56f3dc9a0ef787f99c0566a))
- **PIGEON-3575:** fix conflict
  ([dd116ca](https://bitbucket.agile.bns/scm/pigeon/admin/commit/dd116ca364d194fa62c9ab19311c42e6e83c0c10))
- **PIGEON-3575:** fix conflict
  ([d37edc3](https://bitbucket.agile.bns/scm/pigeon/admin/commit/d37edc3b03cf98e8f8d33a965c6f75bb8f5ba8f4))
- **PIGEON-3855:** upgrade knex and tedious to remove chain of blackduck medium
  vulnerabilities
  ([a4a2e76](https://bitbucket.agile.bns/scm/pigeon/admin/commit/a4a2e762b7653e45f3e0a0fb80adcbb4ff011d92))
- **PIGEON-4061:** fix the postion of the action menu in the rules list
  ([a8556c7](https://bitbucket.agile.bns/scm/pigeon/admin/commit/a8556c7134cbef621cbdb6772e2a6bc830161787))
- **PIGEON-4343:** remove unnecessary await
  ([bfc2df0](https://bitbucket.agile.bns/scm/pigeon/admin/commit/bfc2df076b629ea4ee323fe8b9cf085a244cc74a))
- **PIGEON-4343:** remove unnecessary await
  ([96dc547](https://bitbucket.agile.bns/scm/pigeon/admin/commit/96dc547f079268181efbdd0da5462819eb015f76))
- **PIGEON-4393:** reactivation modal for list and edit pages
  ([6e43e65](https://bitbucket.agile.bns/scm/pigeon/admin/commit/6e43e65cfcf33d85ac892b09c031c1b577a389c4))
- **PIGEON-4404:** fix page title for sol and storefront
  ([05b3b03](https://bitbucket.agile.bns/scm/pigeon/admin/commit/05b3b03f135f00d9219fc371520c72f50a6aa3ee))
- **PIGEON-4405:** add comma to the modal text create campaign
  ([029909b](https://bitbucket.agile.bns/scm/pigeon/admin/commit/029909b63fa0d236442e29a2953cb7cc34780fa5))
- **PIGEON-4500:** add email and sid validation for team owner user add
  ([35daab3](https://bitbucket.agile.bns/scm/pigeon/admin/commit/35daab30490a54d4ab4cbf85b6cbc6b1b830c7c5))
- **PIGEON-4500:** add email and sid validation for team owner user add
  ([74f309f](https://bitbucket.agile.bns/scm/pigeon/admin/commit/74f309ff077e8b082f549af1ff4f14416206829c))
- **PIGEON-4520:** resolve code smells
  ([3903a1a](https://bitbucket.agile.bns/scm/pigeon/admin/commit/3903a1a9ea1855ff72b422df9e1eeaa9063a5595))
- **PIGEON-4520:** resolve sonar code smell
  ([62d2966](https://bitbucket.agile.bns/scm/pigeon/admin/commit/62d2966606e13f8b523a9e136b0f46a7a96fdb07))
- **PIGEON-4520:** resolve sonar code smell
  ([233ba5a](https://bitbucket.agile.bns/scm/pigeon/admin/commit/233ba5a783ed55f6bcbcd50fd197011206c4dec3))
- **PIGEON-4547:** keep max content column width for 200 zoom
  ([694e746](https://bitbucket.agile.bns/scm/pigeon/admin/commit/694e746f5847826d8e215e068f712834a03523cd))
- **PIGEON-4547:** keep max content column width for 200 zoom
  ([7624707](https://bitbucket.agile.bns/scm/pigeon/admin/commit/7624707585a61c3c8b43477058b7cf9277f7c8da))
- **PIGEON-4547:** update column width for campaign name and status
  ([02f8307](https://bitbucket.agile.bns/scm/pigeon/admin/commit/02f8307fc251a6530d5b2528656ffff1ffa53af8))
- **PIGEON-4547:** update column width for campaign name and status
  ([6af13bf](https://bitbucket.agile.bns/scm/pigeon/admin/commit/6af13bf40877d5c75743c0a8300b911bc5fe6e04))
- **PIGEON-4547:** update css to fit max content on table column
  ([b60aa15](https://bitbucket.agile.bns/scm/pigeon/admin/commit/b60aa152774d821d2afaf28a9fb271d90f6e30a0))
- **PIGEON-4547:** update css to fit max content on table column
  ([3997eb3](https://bitbucket.agile.bns/scm/pigeon/admin/commit/3997eb31419bb39bf803173a7f2f47f976213c0e))
- **PIGEON-4581:** add select view and manage all access creating team
  ([ed01fa5](https://bitbucket.agile.bns/scm/pigeon/admin/commit/ed01fa53de10cbf506da4254f2d876ff8ce0921e))
- **PIGEON-4581:** indeterminate checkbox if some groups are selected
  ([8f1270e](https://bitbucket.agile.bns/scm/pigeon/admin/commit/8f1270e379888ee1714b36d4d45e5249021c3ceb))
- **PIGEON-4652:** add useful utility for access and permission
  ([a4f4a83](https://bitbucket.agile.bns/scm/pigeon/admin/commit/a4f4a83f48d329afff2f12f94be2b14f903417c8))
- **PIGEON-4652:** add useful utility for access and permission
  ([9fd801e](https://bitbucket.agile.bns/scm/pigeon/admin/commit/9fd801ee69357dde86cf00e49baee096702f6f33))
- **PIGEON-4652:** fix conflict
  ([7aef1f9](https://bitbucket.agile.bns/scm/pigeon/admin/commit/7aef1f947560abe2fb027c725ff1202e1d56a3a5))
- **PIGEON-4652:** fix conflict
  ([f416eeb](https://bitbucket.agile.bns/scm/pigeon/admin/commit/f416eeb74e5ff85db8d2c420b1a0d8830ed09f45))
- **PIGEON-4652:** redirect user to route they have permission to
  ([4c8dbca](https://bitbucket.agile.bns/scm/pigeon/admin/commit/4c8dbcad0988dc109792dca3740805d679cf9e42))
- **PIGEON-4652:** remove unused join from permission service
  ([d07b6f7](https://bitbucket.agile.bns/scm/pigeon/admin/commit/d07b6f7860342d9f7720df18f30083f18f132fbe))
- **PIGEON-4652:** remove unused join from permission service
  ([14e45ac](https://bitbucket.agile.bns/scm/pigeon/admin/commit/14e45ac1f256537885144f3edd52215e73e2b479))
- **PIGEON-4652:** remove unused key from access object
  ([e5d5ccb](https://bitbucket.agile.bns/scm/pigeon/admin/commit/e5d5ccb4bf866b925b819255ff05a862cb89360f))
- **PIGEON-4652:** remove unused key from access object
  ([c2a8a60](https://bitbucket.agile.bns/scm/pigeon/admin/commit/c2a8a604661e58b1016fb7267d486a26622e69d4))
- **PIGEON-4652:** update sub navigation CCAU title
  ([4233d05](https://bitbucket.agile.bns/scm/pigeon/admin/commit/4233d059e68bd50b57168ad1810753e53dc2d1d2))
- **PIGEON-4656:** error message for updating alerts
  ([723b0d6](https://bitbucket.agile.bns/scm/pigeon/admin/commit/723b0d6cf109e5199a4f1104cb7cfff4ceb3ade0))
- **PIGEON-4656:** update error message for duplicate alerts
  ([2284f42](https://bitbucket.agile.bns/scm/pigeon/admin/commit/2284f42c632eb7e770a8712e4480a30ec400bde0))
- **PIGEON-4656:** update error response for duplicate rule name while updating
  ([618fd10](https://bitbucket.agile.bns/scm/pigeon/admin/commit/618fd106b3fc6cacf89cfb7d287bc986d67d8d33))
- **PIGEON-4664:** capitalize first word for ccau_camapign rule type
  ([60e9feb](https://bitbucket.agile.bns/scm/pigeon/admin/commit/60e9feb01617f7202417a703d057bc31173f2486))
- **PIGEON-4664:** label typo CCAU Campaigns
  ([2bbce43](https://bitbucket.agile.bns/scm/pigeon/admin/commit/2bbce4330683d858c16304ff00d1bd23032c127a))
- **PIGEON-4664:** update error message and label typo
  ([495b09b](https://bitbucket.agile.bns/scm/pigeon/admin/commit/495b09ba30621ec8a2b4f261b0c16c6d5a020095))
- **PIGEON-4676:** mask pii on sensitive route transaction logs
  ([1b58b15](https://bitbucket.agile.bns/scm/pigeon/admin/commit/1b58b15c25c013c399b0156bb78183afaee3bc6b))
- **PIGEON-4701:** check if the form is valid on form submit
  ([3fc0c47](https://bitbucket.agile.bns/scm/pigeon/admin/commit/3fc0c47b7d32294aaaa9c09475f90df91a8a6749))
- **PIGEON-4710:** check if the form is valid for modal display
  ([ea42c7c](https://bitbucket.agile.bns/scm/pigeon/admin/commit/ea42c7c768c77421071bf671a02238abd077decd))
- **PIGEON-4719:** bff svcs sonar maintenace issues
  ([622ed07](https://bitbucket.agile.bns/scm/pigeon/admin/commit/622ed0765863809ae67c645bb1c88b0614122420))
- **PIGEON-4741:** clear pages and external ref on application change for rule
  creation
  ([e027396](https://bitbucket.agile.bns/scm/pigeon/admin/commit/e02739676d7d6d570749a303ebb22809e278b729))
- **PIGEON-4767:** add validation to pages and content type for container
  creation
  ([1b0bff1](https://bitbucket.agile.bns/scm/pigeon/admin/commit/1b0bff1440c2c52a6ec2fab86ab01deb450ed8cb))
- **PIGEON-4775:** allow content types to be retrieved from spaces other than
  canadian space
  ([d79a244](https://bitbucket.agile.bns/scm/pigeon/admin/commit/d79a244b52f31c7c913b2f2e5c5c93c5c7aa0342))
- **PIGEON-4775:** convert formvalue aplication to number
  ([066a0f2](https://bitbucket.agile.bns/scm/pigeon/admin/commit/066a0f280f6bc5cad1887a6559e7750bab407d46))
- **PIGEON-4775:** spelling mistake
  ([0e6458b](https://bitbucket.agile.bns/scm/pigeon/admin/commit/0e6458b34724a122aaaaf07590de48ac364d6bef))
- **PIGEON-4776:** align table column text
  ([e51a854](https://bitbucket.agile.bns/scm/pigeon/admin/commit/e51a854152a6b797448d98dbe14be11a10d4e241))
- **PIGEON-4785:** fix pdf icons
  ([83f0537](https://bitbucket.agile.bns/scm/pigeon/admin/commit/83f053790e5719d1bc2c1e219e0963c7eafabdb5))
- **PIGEON-4785:** fix pdf icons
  ([510b42d](https://bitbucket.agile.bns/scm/pigeon/admin/commit/510b42d39b57272d30ed59b3f4dfca954d08a132))
- **PIGEON-4792:** fix table in rules list
  ([d980961](https://bitbucket.agile.bns/scm/pigeon/admin/commit/d9809615d843dbb12e245ac71c2a19753153a74b))
- **PIGEON-4793:** re route to list page if rule is accessed with incorrect url
  rule type
  ([bf1f3e3](https://bitbucket.agile.bns/scm/pigeon/admin/commit/bf1f3e36db1b603bef83b609ca25cae551b76cf3))
- **PIGEON-4798:** fix render canadian rules under cca tab
  ([b59436c](https://bitbucket.agile.bns/scm/pigeon/admin/commit/b59436cf8779fbffff67e1394f62394296641dfa))
- **PIGEON-4800:** ccau rules appear when sorting canadian rules
  ([1c0b64d](https://bitbucket.agile.bns/scm/pigeon/admin/commit/1c0b64db5e47092cb51b7abb26fe8a39ca4100f8))
- remove --u from test script
  ([e18b7c8](https://bitbucket.agile.bns/scm/pigeon/admin/commit/e18b7c8a83e189720c295362183a5b9a9894d42e))

## [1.9.0](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.8.3...1.9.0) (2023-08-25)

### Bug Fixes

- **PIGEON-4771:** account for rule list page ruleSubType access check
  ([d515c65](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d515c6551e28f629eefbbac663d2364fb1230c29))
- **PIGEON-4771:** update rule substype access check condition
  ([ca1b3cb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ca1b3cbe7ffb329196095403806a697e0182eb64))
- **PIGEON-4771:** update setPageActivation function params order
  ([33941c4](https://bitbucket.agile.bns:7999/pigeon/admin/commit/33941c4c08e4d15ba206e04cc723deb77f0cea50))

### [1.8.3](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.8.2...1.8.3) (2023-08-18)

### Features

- increate sonar coverage
  ([98fe643](https://bitbucket.agile.bns:7999/pigeon/admin/commit/98fe643018825fd8b92a58214244f88f9585de24))
- **PIGEON-4709:** add download pdf feature using image or html convertor
  ([e54e5b3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e54e5b3d03fefd996fe09ae18a89204d2eb4d5d3))
- **PIGEON-4709:** add download pdf feature using image or html convertor
  ([a342855](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a34285556c1cd68699117dc34df0b2290ec2e056))
- **PIGEON-4709:** fix formatimg the preview file
  ([f26a99b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f26a99bc7805daeaaf6562ee83cccf0d41c63bd9))
- **PIGEON-4709:** remove --u from test script
  ([bcea9e5](https://bitbucket.agile.bns:7999/pigeon/admin/commit/bcea9e5df204484528b47f97d74f719e1404f09a))

### Bug Fixes

- add more test cases to increate the coverage
  ([7e17908](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7e17908ae9413ec0a8589055be0dc73758914596))
- add more test cases to increate the coverage
  ([35df02e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/35df02eaa5063bc712f3274900b093949bbab323))
- fix creating page
  ([51fd0ef](https://bitbucket.agile.bns:7999/pigeon/admin/commit/51fd0ef53b6fe70445634fd27103584ba8e202d1))
- fix lint
  ([4023730](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4023730d755fa40eecabf15b4979420ee339148b))
- fix ruleService param
  ([50f413a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/50f413a8a2f5dfe86349913151663df762ef0500))
- fix scrollbar on abm preview to dowload pdf
  ([94f9ac2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/94f9ac29d08b6d97ddbdf55951d3febec7af57db))
- pdf add proxy for images
  ([84d6d42](https://bitbucket.agile.bns:7999/pigeon/admin/commit/84d6d425cd1684a1a0aeb071a9a980a9d5913157))
- **PIGEON-3855:** fix knex 2 major version migration breaking changes
  ([63d3fa0](https://bitbucket.agile.bns:7999/pigeon/admin/commit/63d3fa0220232b750a0425a22b886e3f8963ae79))
- **PIGEON-3855:** upgrade knex and tedious to remove chain of blackduck medium
  vulnerabilities
  ([f2081bc](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f2081bc23e2bd52aefa8298f779a5495001cbac2))
- **PIGEON-4061:** fix the postion of the action menu in the rules list
  ([4ea0a29](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4ea0a2983ff971e3fa63dd9670d50736f575701d))
- **PIGEON-4187:** disable the snakbar oncase of no changes
  ([8267316](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8267316f9b7801f391c8fa53fdff894a24c3653c))
- **PIGEON-4187:** disable the snakbar oncase of no changes
  ([4109500](https://bitbucket.agile.bns:7999/pigeon/admin/commit/410950053f771eb5dc69c9d6961efc7c552df7cb))
- **PIGEON-4187:** disable the snakbar oncase of no changes
  ([eb71058](https://bitbucket.agile.bns:7999/pigeon/admin/commit/eb71058ccd4433796ee98fb81f8bd483cd8f24fb))
- **PIGEON-4187:** display no changes and don't redirect the user to the list
  page
  ([c04f7f7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c04f7f74e18ba2f5f348b6fdfb2a3f16f58b2b0d))
- **PIGEON-4187:** display no changes and don't redirect the user to the list
  page
  ([fe72f85](https://bitbucket.agile.bns:7999/pigeon/admin/commit/fe72f850f88cdbf208634cd6eea2c5f7223ce132))
- **PIGEON-4187:** display no changes and don't redirect the user to the list
  page
  ([8dbd796](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8dbd796ed790d845499395e50c54905480726437))
- **PIGEON-4187:** display no changes to be saved snakbar
  ([3df8f53](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3df8f539ca8d0be14b1a5f5f87f01241ae1d628e))
- **PIGEON-4187:** display no changes to be saved snakbar
  ([0ab30da](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0ab30da033650d58e85d4a0ba33831c14d625434))
- **PIGEON-4187:** display no changes to be saved snakbar
  ([f26e242](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f26e242bfe9aa8c5a1cb53e427834526971c05df))
- **PIGEON-4291:** rollback edit team transaction on duplicated team name
  ([69253b4](https://bitbucket.agile.bns:7999/pigeon/admin/commit/69253b4d286661f7c2ef70eef06a20eb9a1bced5))
- **PIGEON-4296:** disable existing team owners from team owner modal
  ([4b9da1f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4b9da1fc85122388e80d5f3fd4f03db6d54d7352))
- **PIGEON-4392:** reactivate modal for list and edit container
  ([3b4ada1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3b4ada1d506fec2d052479f8b02fafa3d8793010))
- **PIGEON-4392:** reactivate modal for list and edit container
  ([9f89b18](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9f89b1884cc6729bc60c69d208df0150201cd8fc))
- **PIGEON-4393:** reactivation modal for list and edit pages
  ([0c764d7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0c764d78cc75731c860d5af505e6bd9afff39bd1))
- **PIGEON-4404:** fix page title for sol and storefront
  ([296f8f4](https://bitbucket.agile.bns:7999/pigeon/admin/commit/296f8f45a8c6e731004bf0290fa939980af7222d))
- **PIGEON-4405:** add comma to the modal text create campaign
  ([f4cd2d3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f4cd2d393c229d621f64312df72573f6116b97aa))
- **PIGEON-4407:** filter var map assignee by team
  ([e34c78a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e34c78ac5e47e5ea4fca26957af49264e407c70e))
- **PIGEON-4457:** fix some ui issue on container details page
  ([7b82548](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7b825488543f9e66e399b2d125005a3348037b88))
- **PIGEON-4457:** fix some ui issue on container details page
  ([dc9a4ab](https://bitbucket.agile.bns:7999/pigeon/admin/commit/dc9a4ab6cc6e0a0bfcdf60ceced335b403a38f75))
- **PIGEON-4457:** fix some ui issue on container details page
  ([6d720da](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6d720da7a3e1b79cdc2787081f974f35f00aa9a7))
- **PIGEON-4457:** fix the order of rule type in continaters page
  ([e599afe](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e599afe4f675caf31e17f50fb2147202dd700065))
- **PIGEON-4457:** fix the order of rule type in continaters page
  ([6003837](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6003837b90553aba7ea9db86f983bb6f1734a1f5))
- **PIGEON-4457:** fix the order of rule type in continaters page
  ([3695af4](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3695af4d3dd43d35916914edbcff312214efbdf1))
- **PIGEON-4458:** fix description labe on pages details page
  ([5895ca1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5895ca14649e5ebac221c20618390692a76c43b8))
- **PIGEON-4458:** fix description labe on pages details page
  ([c47db9b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c47db9bda2e1d00c5fc6c8a954b81e3834b43c64))
- **PIGEON-4458:** fix description labe on pages details page
  ([135183e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/135183eed19bcac413852096c012fae31855a914))
- **PIGEON-4460:** fix platforms
  ([016aab3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/016aab3a7635690f277fbcf1cce3b40c890e7240))
- **PIGEON-4460:** fix platforms
  ([d16de70](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d16de70a9213bbdf7c110a1053c1feb8f5b9edb0))
- **PIGEON-4460:** fix the order of platform in application page + edit the
  title
  ([f484cb1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f484cb1fc3c15e2c86e6b9fa40ad66ac798aecce))
- **PIGEON-4460:** fix the order of platform in application page + edit the
  title
  ([9528bc3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9528bc3c478f1b7e09569e4bf3abacc46515adac))
- **PIGEON-4460:** fix ui issues with application details page
  ([38221b9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/38221b9c83bedf5bdd537e4741f2d73e5fbdf06e))
- **PIGEON-4460:** fix ui issues with application details page
  ([335def1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/335def11e129793d35221c2f6cbb58d78045cf93))
- **PIGEON-4460:** fix ui issues with application details page
  ([a3b6a74](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a3b6a742e7915701800338f4b751302595dcefd5))
- **PIGEON-4520:** move parameter active after parameters without default value
  for page activation
  ([4432584](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4432584b858f4e6c32c40b8fae862284614f3aad))
- **PIGEON-4520:** move parameter active after parameters without default value
  for page activation
  ([f4f47ce](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f4f47ce2a872ad9d170cf778f2ca9c0d30cba509))
- **PIGEON-4520:** rename variables which potentially hide a variable declared
  in an outer scope
  ([0cfa4b2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0cfa4b26c8cf6699f798f5b006c7ad5e838cb739))
- **PIGEON-4520:** rename variables which potentially hide a variable declared
  in an outer scope
  ([1a27f4b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1a27f4badc0021c755b537a8082816fee75a15e4))
- **PIGEON-4520:** rename variables which potentially hide a variable declared
  in an outer scope
  ([20e466c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/20e466cb10d93cd9fee4d7902c553f5819baa181))
- **PIGEON-4520:** rename variables which potentially hide a variable declared
  in an outer scope
  ([1417861](https://bitbucket.agile.bns:7999/pigeon/admin/commit/14178619fc7f0ae469e39dc8187d67561c8fddef))
- **PIGEON-4520:** rename variables which potentially hide a variable declared
  in an outer scope
  ([b0b5c00](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b0b5c00c2c7780c3a8e6c9afd4bd0cf86d3f3dfc))
- **PIGEON-4520:** rename variables which potentially hide a variable declared
  in an outer scope
  ([aa04be7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/aa04be7e30d2a19c714e323aa3f0a359d628441b))
- **PIGEON-4520:** resolve code smells
  ([4cf298d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4cf298d9ccb55a683865ca2c9e4ffa7708a44121))
- **PIGEON-4541:** sort containers and pages with applications
  ([d50bfdb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d50bfdbc8e53745a58e84faa28992bfcc3475950))
- **PIGEON-4541:** sort containers and pages with applications
  ([8ddec75](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8ddec75ddd56f2da5e25cf29d627a976d9a245cf))
- **PIGEON-4568:** get pages/container/application based on the view permissions
  for current page
  ([185c11f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/185c11fe6c5c1920c466bdc41abf2f0f4f0b5e20))
- **PIGEON-4568:** get pages/container/application based on the view permissions
  for current page
  ([45a8a26](https://bitbucket.agile.bns:7999/pigeon/admin/commit/45a8a261486b0ca02bc3f4b9103696ac9b01b89f))
- **PIGEON-4601:** autosuggest field to take full width for sol and storefront
  ([f550b0e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f550b0e3f2025c44ca81372cf07d44cda40710c1))
- **PIGEON-4601:** autosuggest field to take full width for sol and storefront
  ([1596b63](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1596b639e7d9f9bad377e49fe3acdbc3a73e9071))
- **PIGEON-4676:** mask pii on sensitive route transaction logs
  ([513a5ff](https://bitbucket.agile.bns:7999/pigeon/admin/commit/513a5ff28fb45612648cd9af688e54df6f0159fb))
- **PIGEON-4676:** mask pii on sensitive route transaction logs
  ([b024316](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b024316b2921233d15eed5127192787ffc61e7de))
- **PIGEON-4701:** check if the form is valid on form submit
  ([916ded5](https://bitbucket.agile.bns:7999/pigeon/admin/commit/916ded5bd472f9db15d8ea311c41aa5680428131))
- **PIGEON-4710:** check if the form is valid for modal display
  ([32305fb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/32305fb06b119046c93dcf3c07700d7caeb5d5e3))
- **PIGEON-4710:** convert formvalue application to integer while filtering the
  ruletypes
  ([2864e29](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2864e298e234c262b3174265a47b21b8bb3681c1))
- **PIGEON-4710:** convert formvalue application to integer while filtering the
  ruletypes
  ([d651520](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d651520ac15f3726e30e130d3606a67ca4bd6cf0))
- **PIGEON-4719:** bff svcs sonar maintenace issues
  ([5b90155](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5b90155a13d90f34a0dc59b8e78fc734e9f08cc9))
- **PIGEON-4730:** remove reverse from ruletype filtering for create container
  ([6032481](https://bitbucket.agile.bns:7999/pigeon/admin/commit/60324815b6d4816caa040aea14f9d7178b71ab5e))
- **PIGEON-4730:** set default checked for rule type create container
  ([d9ee31b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d9ee31bc5d1a328c42d32387e77153f381b5da83))
- **PIGEON-4730:** update validation on radio field container creation
  ([51d5212](https://bitbucket.agile.bns:7999/pigeon/admin/commit/51d52124e1811845ff642eadbbb7909d242bbd26))
- **PIGEON-4738:** fix render svg icons
  ([2777243](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2777243dbab547d8dffde111ac530838c25a75e1))
- **PIGEON-4739:** fix cut off text on pdf
  ([95e8cb5](https://bitbucket.agile.bns:7999/pigeon/admin/commit/95e8cb572da104a1ef63d739f3465e92cb4baf47))
- **PIGEON-4741:** return true for user page access if rule page is undefined
  ([cd95f05](https://bitbucket.agile.bns:7999/pigeon/admin/commit/cd95f05e23ead897240f17b2cd91f2bfae485fc8))
- **PIGEON-4743:** fix alert preview position
  ([cd5bafc](https://bitbucket.agile.bns:7999/pigeon/admin/commit/cd5bafcef62de41353359fa8b73d0deec4370b0a))
- **PIGEON-4761:** map container ids with page id
  ([a9dc977](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a9dc97781cd8617154822e85975b552d6da9fc3d))
- **PIGEON-4763:** map page ids with container id
  ([c198601](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c198601b43342fd99595abb6128728d280ffcd64))
- **PIGEON-4764:** map team ids to owners
  ([c9b8a9a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c9b8a9acbb60d74af16e5b85d4fff8b809b3fe88))
- **PIGEON-4768:** fix height of pft
  ([740cdbe](https://bitbucket.agile.bns:7999/pigeon/admin/commit/740cdbebbc7a33115ac5d516e1e8ebcd9601c288))
- remove --u
  ([a12d3a9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a12d3a94d2dfe415803ee3637ca707acb43e4105))
- resolve the conflict
  ([3dcff9c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3dcff9ca0781e9fc0c101acad400f296ef21dc45))
- update release/1.9 with main and resolve the conflict
  ([a447fa9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a447fa97e1e9d990e49fceeb3d5ae9669479f9f2))

### [1.8.2](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.8.1...1.8.2) (2023-06-26)

### [1.8.1](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.8.0...1.8.1) (2023-06-13)

### Bug Fixes

- **PIGEON-4701 PIGEON-4702:** fix container details loading & filter out SOL
  containers w/o pages
  ([7a051f8](https://bitbucket.agile.bns/scm/pigeon/admin/commit/7a051f8eb823f20ccb69c54d2ae9e37c9719115b))

## [1.8.0](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.7.2...1.8.0) (2023-06-12)

### Features

- **PIGEON-2479:** backend sorting & pagination for placement pages
  ([ce6e005](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ce6e0055f93da20db25178da0c244d1252dfd4a2))
- **PIGEON-4161:** re-enable confirm owner modal
  ([70b6d6e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/70b6d6e82b0771896569ca9383b16aac46ae8fbe))
- **PIGEON-4279:** perform backend pagination & sorting for access pages
  ([8086146](https://bitbucket.agile.bns:7999/pigeon/admin/commit/80861466f259e58091cdf11b13e59ca62e5df930))

### Bug Fixes

- **PIGEON-4101:** add tooltip to status column header for application container
  and pages
  ([e70b642](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e70b64223e0bbfc9d076ac2af82075b1112457fb))
- **PIGEON-4233:** fix clear all button on users and roles pages
  ([cc9713e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/cc9713e97d43d05a50bdc1ed38791b0415317e5f))
- **PIGEON-4260:** placeholder text update for name field team owner modal
  ([03ff682](https://bitbucket.agile.bns:7999/pigeon/admin/commit/03ff682a6d9942d51416d387ffb6b075757e4191))
- **PIGEON-4260:** reorder input fields for team owner modal as design per DS
  ([8d3dd5a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8d3dd5ae016c655367d5316c670e0b15877182c9))
- **PIGEON-4306:** dont deactivate modal on app change to an inactive app
  ([c923789](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c92378981a823ede73d7984c89331bd30eee6242))
- **PIGEON-4306:** dont display confirmation modal when creating new inactive
  container
  ([2694b6a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2694b6a0d8ff3191ae32ae68ce993f9d13223dfc))
- **PIGEON-4306:** dont display confirmation modal when creating new inactive
  role
  ([51f457f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/51f457f959b52b9cfb5adba8a19e9feb05a44d44))
- **PIGEON-4307:** dont deactivate modal on app change to an inactive app
  ([d21c3b3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d21c3b3199ad1c4ad5bc2187d8fb27284590f14d))
- **PIGEON-4319:** enable db transactions on first test endpoint
  ([3f9b4ee](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3f9b4eeea0170b3e2684ca591c9a5a19e4defbcd))
- **PIGEON-4369:** fix sack message for edit/create containers
  ([ca82bf2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ca82bf21c8307efc18cc9c39e1fdfa8244c205d6))
- **PIGEON-4369:** fix sack message for edit/create pages
  ([5e6c342](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5e6c34229a4dd7bfa5e73ce4ade8c336365a77ec))
- **PIGEON-4370:** fix snackbar on editing container
  ([260766e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/260766e8d3a497348c2be53a7546fc6b1b7a925a))
- **PIGEON-4370:** fix test case in case of updateing container
  ([9dce400](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9dce400512f0160173b5e26b61b60fe80eff27e8))
- **PIGEON-4370:** remove extra period
  ([f12cfbb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f12cfbb88a2df4541092764706c947fc3186c369))
- **PIGEON-4385:** fix reactivate user modal in details page + list page
  ([72885d0](https://bitbucket.agile.bns:7999/pigeon/admin/commit/72885d04a5f603b6725b428714df121b7cb59626))
- **PIGEON-4386:** add test case for reactive modal
  ([af65973](https://bitbucket.agile.bns:7999/pigeon/admin/commit/af659730bf1f56393547e2eb5ab851ca51744c9c))
- **PIGEON-4386:** fix reactive modal in roles list + role details
  ([64972ce](https://bitbucket.agile.bns:7999/pigeon/admin/commit/64972ce0ac611a8df4aa215a450eb64a386db40c))
- **PIGEON-4387:** add snackbar message for activation deactivation of
  application
  ([f7f58e1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f7f58e19f8659924aa73cf9cf4acf5002987ac5e))
- **PIGEON-4387:** add snackbar message for creating updating of application
  ([e799818](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e79981807e5944b7e9dad880e832ff984ff09854))
- **PIGEON-4388:** snackbar message updated for creating editing team
  ([20b22ab](https://bitbucket.agile.bns:7999/pigeon/admin/commit/20b22abd4b2f767ff09ec6bd5ea80cd594ac08a4))
- **PIGEON-4388:** update snackbar message for updating team
  ([9797013](https://bitbucket.agile.bns:7999/pigeon/admin/commit/979701348340282339c5fdc3ddcda8efb0d4c692))
- **PIGEON-4388:** update snackbar messages for updating creating team
  ([fdb05d0](https://bitbucket.agile.bns:7999/pigeon/admin/commit/fdb05d0cd913ebf70bbdbf6cc0cb856eef898031))
- **PIGEON-4396:** remove period from snackbar messages
  ([97c57e7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/97c57e7aa7b9bf30f3bf521f93d7c304383ba22e))
- **PIGEON-4396:** remove period from snackbar messages
  ([ec16c99](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ec16c99d961c8c2509b6909f0296f3f4b704376d))
- **PIGEON-4415:** reactivation modal condition update for user details
  ([83eede2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/83eede2a66ee22d956a90cc0343f7d35026afb62))
- **PIGEON-4416:** dont display deativation modal if inactive team is selected
  create role
  ([4de3855](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4de3855533e0b687eb24de835ab4300c29eb8eef))
- **PIGEON-4417:** display confirmation modal when switched to an inactive team
  ([ccd0809](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ccd0809c03856bd58e3223f1076f5fd0665b371f))
- **PIGEON-4511:** add spacing between description and error banner in edit
  variable set
  ([7c4dd2f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7c4dd2f1b40bb5dbcec8abdd13c46b24bfb7447e))
- **PIGEON-4527:** reset status and start and end date on rules list screen
  ([df2b6c3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/df2b6c324ae8ddc66a5c020e7dfee2a81d7f5777))
- **PIGEON-4564:** fix permission for mange / review / edit rules
  ([780771d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/780771d83e3740f916c5e3191ccf69e39de4c9eb))
- **PIGEON-4564:** fix the action button in the side panel in list screen
  ([6910c51](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6910c51143b18ab78e6b35339aaabe2706f3e7ea))
- **PIGEON-4564:** fix the readability for canEditRule function
  ([142b0eb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/142b0eb26b0edda73ee19012d679245ae0405ccf))
- **PIGEON-4564:** move canEditRule to separate function for readability
  ([2309013](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2309013e33822df9d589f040efd6780e7bae38ed))
- **PIGEON-4564:** support check creating alert/campaign
  ([e16e17d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e16e17d2cf0deaff5730c95aabf8bc6ec050e0fe))
- **PIGEON-4564:** support check creating alert/campaign
  ([47db095](https://bitbucket.agile.bns:7999/pigeon/admin/commit/47db0959a31837b337e0ba87fa5c07966c571521))
- **PIGEON-4579:** reset status filter when role is changed, user details
  ([cd64592](https://bitbucket.agile.bns:7999/pigeon/admin/commit/cd64592fb24f4323d78cb4bb4fb784b792a44eaa))
- **PIGEON-4583:** rules can be edited by teams with only view access
  ([60361ce](https://bitbucket.agile.bns:7999/pigeon/admin/commit/60361cebd07fc5169b6bb3f437f7fe836f2c5e0a))
- **PIGEON-4587:** admin preview sol image duplication
  ([d3e4b0e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d3e4b0e6ad577425753f4835ff611309558ad9aa))
- **PIGEON-4588:** reset search filter on tab switch sol storefront
  ([9bb3e35](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9bb3e35a474c82d6d7712e5e4fb975b06c3c9c65))
- **PIGEON-4591:** add unit test for validation route and service
  ([419f54e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/419f54e2ed0931f3f02baf02df45a91341c245dd))
- **PIGEON-4591:** check for dirty fields before calling validation endpoint
  ([09bd95c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/09bd95c2cd49afae47d124f905c546cafe60b449))
- **PIGEON-4591:** create endpoint to validate uniqueness
  ([a1e3828](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a1e38289053d19fbef466b4490ae9c003e78bd7e))
- **PIGEON-4591:** map table column with query param for validation
  ([e013fc5](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e013fc50e40b7fc066bce9f8b5a84ccca12fe7ea))
- **PIGEON-4591:** resolve key management fortify failure
  ([7f4c233](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7f4c2331929b0a12afdcbe8a025e2be7dc9aeaa0))
- **PIGEON-4591:** resolve validation and form submission issue for user details
  ([edc0b40](https://bitbucket.agile.bns:7999/pigeon/admin/commit/edc0b404a306959d546075c8e0b3666b90751efc))
- **PIGEON-4591:** update map join table to be an array and contain join
  parameter
  ([c550453](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c55045384a85bcd41365377afbe8ad1bda47f298))
- **PIGEON-4591:** update sql query to include cross table validation check
  ([81872da](https://bitbucket.agile.bns:7999/pigeon/admin/commit/81872daa360e27f3428b3f11e3c8215d13d771ad))
- **PIGEON-4592:** fix break link between container and pages
  ([e336fba](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e336fbada2e5dc94b8e66f48e358bb140551b4a0))
- **PIGEON-4593:** add validation to the backend to prevent adding manage teams
  permission
  ([e2e7d8f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e2e7d8f8f24518189ec8d9d55f90860c151f3d24))
- **PIGEON-4593:** make manage all teams disabled
  ([ce38fb6](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ce38fb678500ec209865f9709ec6e705fbaad33e))
- **PIGEON-4593:** restyle updateTeam and createTeam functions
  ([79ea5c4](https://bitbucket.agile.bns:7999/pigeon/admin/commit/79ea5c461a5776ea04d1a7dd1f26fc3d45f65932))
- **PIGEON-4600:** send bad request error for duplicate duplicate key error
  ([eb22c24](https://bitbucket.agile.bns:7999/pigeon/admin/commit/eb22c24d7f9517d9962a63a6540a861576c06f15))
- **PIGEON-4600:** send duplicate text error message for exisitng sol storefront
  campaigns
  ([4478463](https://bitbucket.agile.bns:7999/pigeon/admin/commit/44784632a2217d94ef49bf6b5df63dfa49cac2f9))
- **PIGEON-4602:** disable contenyType and pages field if form is disabled -
  edit container
  ([8d56439](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8d5643935ed576bae8e50ddf11344115f9241083))
- **PIGEON4306:** dont display deactivation modal for new inactive container
  page or role
  ([31e138a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/31e138a3337e2da33af6c88ef27976f8e8e3c70c))
- update components to expect array structure from redux store
  ([5e52bb2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5e52bb24a1bdc777eb4a91d7f7f62964b0f7af9c))
- update components to expect array structure from redux store
  ([b36f0df](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b36f0dff91bbacd4b313eb27551fd26f7af8d110))

### [1.7.2](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.7.0...1.7.2) (2023-05-19)

### [1.7.1](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.7.0...1.7.1) (2023-05-18)

## [1.7.0](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.6.1...1.7.0) (2023-05-10)

### Bug Fixes

- **PIGEON-4282:** add local-rate to variable maapping type
  ([6211fd5](https://bitbucket.agile.bns/scm/pigeon/admin/commit/6211fd57527b2259bae0c48951e15593256312d9))
- **PIGEON-4282:** update validation for variable mapping update to include
  locale-rate
  ([2a1c926](https://bitbucket.agile.bns/scm/pigeon/admin/commit/2a1c9264164d0ff2127becae291e6c8c025f8791))

### [1.6.1](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.6.0...1.6.1) (2023-04-13)

## [1.6.0](https://bitbucket.agile.bns/scm/pigeon/admin/compare/1.5.1...1.6.0) (2023-04-12)

### Features

- **PIGEON-4506:** pentest lack of rate limiting
  ([6514565](https://bitbucket.agile.bns/scm/pigeon/admin/commit/65145659d879a780f03fd031826043c1ccd30bcb))

### Bug Fixes

- address critical sonar issue
  ([985040a](https://bitbucket.agile.bns/scm/pigeon/admin/commit/985040a3c1099a4441ad304b78aec25d5e9ec91b))
- only trigger app, page, container fetching on page load
  ([3e5065d](https://bitbucket.agile.bns/scm/pigeon/admin/commit/3e5065db65a3c3d925e960005a4e86aa13a031fd))
- **PIGEON-4502:** add middleware to handle body parser
  ([6ad9157](https://bitbucket.agile.bns/scm/pigeon/admin/commit/6ad91574eac110ccf52cc0ee3f2740fd7a86cf88))
- **PIGEON-4502:** apply same erro handler from ather service
  ([85e5db5](https://bitbucket.agile.bns/scm/pigeon/admin/commit/85e5db54118c1291ed21015516d629fb69e43895))
- **PIGEON-4502:** chamhe the error message to fix Improper Error Handling -
  Pentest
  ([be5f9d8](https://bitbucket.agile.bns/scm/pigeon/admin/commit/be5f9d86177e3b3d8e1a00ecb414f26e4b865a4d))
- **PIGEON-4502:** change the error message in case of 400 error - Pentest
  ([7f62a2d](https://bitbucket.agile.bns/scm/pigeon/admin/commit/7f62a2db4614371bdda96b4ca8707d7b6ad87d6b))
- **PIGEON-4502:** fix test case on biday parser middleware
  ([feff810](https://bitbucket.agile.bns/scm/pigeon/admin/commit/feff810400e301b6010ec138269fe8149a1ff7ec))
- **PIGEON-4502:** fix wrong team id formt error on getting single team
  ([ca2791b](https://bitbucket.agile.bns/scm/pigeon/admin/commit/ca2791b60c84d4f764580361d0243b9acea2b784))
- **PIGEON-4560:** fix validatiopn on start date and end date on rules screen
  ([880cfa4](https://bitbucket.agile.bns/scm/pigeon/admin/commit/880cfa4c288c5de532c9a4209dc7eb629b7d1f71))

### [1.5.1](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.5.0...1.5.1) (2023-03-03)

## [1.5.0](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.3.74...1.5.0) (2023-03-02)

### Features

- [PIGEON-3144][PIGEON-3148] Adding and Listing Teams:
  ([284c1ca](https://bitbucket.agile.bns:7999/pigeon/admin/commit/284c1ca43502384755a640c18edb9c2786ca1b60))
- [PIGEON-3147] Deactivate a Team
  ([b6fd977](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b6fd9770e85d0562f5761a91027b099120a3521b))
- [PIGEON-3150] Application-Teams Functionality
  ([fd32175](https://bitbucket.agile.bns:7999/pigeon/admin/commit/fd3217589c83284d489971977d966a40337c1ca2))
- [PIGEON-3153][PIGEON-3156] Entity Listing Page Updates
  ([567c38c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/567c38cb659fb2fe212993e50af4e351fe8b715a))
- [PIGEON-3155][PIGEON-3151][PIGEON-3154] Team Permission Entities
  ([20e8f33](https://bitbucket.agile.bns:7999/pigeon/admin/commit/20e8f33c10f1606dc4eff2e2810da7b6581f8235))
- [PIGEON-3162] Teams Roles Table
  ([9cc21ce](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9cc21cea5c26663e47e38330b14d8aa045ffcf6f))
- **3869:** add correct permission to the backend
  ([3cb26dc](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3cb26dc8cbe267db4aacc006e603b04b75699370))
- access and permissions for teams
  ([37774c9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/37774c9f42cc502fbc30bd98d7751bace1ba18db))
- access componant
  ([39f0268](https://bitbucket.agile.bns:7999/pigeon/admin/commit/39f02683d530c18a9d34be002b4253d3d85a18d6))
- add Functionality + applictaiions col to teams list
  ([62ce3ef](https://bitbucket.agile.bns:7999/pigeon/admin/commit/62ce3efc399a97ece193868c622355596b564f7c))
- add readonly option
  ([13a2855](https://bitbucket.agile.bns:7999/pigeon/admin/commit/13a285531a60dd42aa6f246ff26a755375502595))
- add role table + test cases
  ([bba2216](https://bitbucket.agile.bns:7999/pigeon/admin/commit/bba2216b9c252a1188b329a29afccbade4acb939))
- add some test cases
  ([8aea062](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8aea0626306f24329ef7d5de7808201f87c80a85))
- add SpecialCase
  ([e764dfe](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e764dfe5100f929504c124ec94b7d2d94a2e0fa4))
- add test case to the team owner
  ([e864502](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e864502f2b541a80fb140d29d5780b8f1e9d2500))
- add test cases for access table component
  ([851c956](https://bitbucket.agile.bns:7999/pigeon/admin/commit/851c956c624207477a6ce6837e3d08280d8018ab))
- add test cases to access componant
  ([cf1c26f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/cf1c26fbbdc63bae368b47fbfdfb05be90b51653))
- add users table
  ([202e642](https://bitbucket.agile.bns:7999/pigeon/admin/commit/202e64248fc8d151023a3bdf32daa4026dc50521))
- added reactivate children checkbox to reactivate team/application modal
  ([0660122](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0660122437acb068f01e9ec7f85cc99323cf556f))
- Adding email field error validation
  ([695d820](https://bitbucket.agile.bns:7999/pigeon/admin/commit/695d82062c5f18123c03750ee4bbe5ae6ddd18eb))
- align to database schema, filter sol and storefront rules by access
  ([3f72222](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3f72222102f6aacb008d1d74354e37d10ab34bf0))
- bump pwr version
  ([4db79d7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4db79d765e4150cbbbfef6ac14249777fadd7498))
- bump pwr version
  ([734e590](https://bitbucket.agile.bns:7999/pigeon/admin/commit/734e5908ed502fc5c0dd1a39e97d7ce1bf389d77))
- cleanup
  ([dbe8dc7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/dbe8dc74dd59f276cbd4f073f5200b81dfc9c3c2))
- connect access component to backend
  ([da6d59a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/da6d59ad95c83f7aa7d616d086dc282c540084cf))
- create user team screen
  ([f7969fb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f7969fb4a41b0c06b3e2689ca69b116f21ba4d0f))
- disable status switch if only team owner
  ([49a3716](https://bitbucket.agile.bns:7999/pigeon/admin/commit/49a37161bbac165acf8930f7b47f69dc817a9190))
- enhance field error validation
  ([55a3375](https://bitbucket.agile.bns:7999/pigeon/admin/commit/55a3375f66e6911a008780c982a0e5babab58617))
- fix edit bug in access component
  ([d36aeb2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d36aeb25934a3c0efba49406c81f0c85c9ccd0db))
- fix rdtToday issue
  ([f227070](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f2270701400ccb6aa2197d2432254b870f1cc2c8))
- fix routes
  ([4fb8eb6](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4fb8eb6ac82d0b765bc58edf8eacff5d0c103a00))
- fix ruleSubType in access tabel
  ([b5e8ee7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b5e8ee77d5e3732ba6baa82a87a1cb0daa5da971))
- fix small bug on saving team access
  ([38c9e77](https://bitbucket.agile.bns:7999/pigeon/admin/commit/38c9e77fc527647c6704df7e944925216ad1b686))
- initiate api calls when opening filter or side panel instead of campaign page
  load
  ([cbfa9fd](https://bitbucket.agile.bns:7999/pigeon/admin/commit/cbfa9fd3ccf4dc7f2a77560557f9ea8fa7578ad0))
- jest run in band for resource constrained build systems
  ([a17cbdb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a17cbdb66e59f54f46bbc8b2fb0a8a3047870e92))
- lint fixes
  ([7f7c360](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7f7c360802a8f4b61992b8bd55e140b78fb5e946))
- merge with team feature
  ([0cb8029](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0cb80294a867b8e5f282194bda401619b7ff64b8))
- more readability for the access table
  ([08ad6f9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/08ad6f998e855209fa00f1c76203332ec935c0d3))
- **PIGEON-3149:** switch user from one team to another - part 1
  ([2262fee](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2262feef39f4c41d82fcd0a30da593e33814d519))
- **PIGEON-3149:** switch users roles from multiselect to checkboxes
  ([e383ee8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e383ee84d3c832ccb43427864f01c45532b94ee4))
- **PIGEON-3159:** create user - teams
  ([7afd8bc](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7afd8bcac61a4b0b45e8827b32a7740e492a1790))
- **PIGEON-3164:** extend advanced targeting component for teams permissions
  ([edd3bfe](https://bitbucket.agile.bns:7999/pigeon/admin/commit/edd3bfe9dc0feaa616801ae51328fc104129f48e))
- **PIGEON-3404:** convert user page to use react hook forms
  ([61ae3e1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/61ae3e1e8f5c5e6200c5a50ec49c45b933b8452b))
- **PIGEON-3404:** use new permissions for applications, teams, users, and roles
  ([5196b8f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5196b8f6935d89d7dae158dbd3e7d71ae1ada0d3))
- **PIGEON-3815:** add team ownship modal
  ([460ca06](https://bitbucket.agile.bns:7999/pigeon/admin/commit/460ca06c8255d06133a16c4a53da628a6797de97))
- **PIGEON-3815:** add team_id to roles + users
  ([7915933](https://bitbucket.agile.bns:7999/pigeon/admin/commit/79159338870dbfda31bbd3e5f41b7814248b6ed2))
- **PIGEON-3815:** add test cases
  ([017ae61](https://bitbucket.agile.bns:7999/pigeon/admin/commit/017ae61d559b81404e2c248a48f9cbdd3eeacc25))
- **PIGEON-3815:** check if user is already in a team
  ([529fe0d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/529fe0df96b480e2df79a16bb805675da794b546))
- **PIGEON-3815:** update test case
  ([724e5e2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/724e5e27d3fcc8ab2304a4c72093726d9b5a41ab))
- **PIGEON-3837:** add default permission when creating team
  ([849c713](https://bitbucket.agile.bns:7999/pigeon/admin/commit/849c713feb147b5c6588b30e2ecd53566bd3ab53))
- **PIGEON-3837:** added base permissions for new structure
  ([0510964](https://bitbucket.agile.bns:7999/pigeon/admin/commit/051096463b6835d7719b1396ecec7373d48d4220))
- **PIGEON-3837:** handle permission backend
  ([87b10c2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/87b10c29294faae952549843c73c9c7332517d48))
- **PIGEON-3838:** add create by team in the side sheet
  ([a8ae016](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a8ae016b75d1a43c6d8bde62d5bf4f18e3cb9ba5))
- **PIGEON-3838:** fix lint
  ([0e04014](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0e040143635372e44707bac58797bffbb2ec3002))
- **PIGEON-3838:** rename full_name to name
  ([8ab6f19](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8ab6f196daa4ec52b1c45227e419ec9c7e6920ce))
- **PIGEON-3859:** teams logic for applications, pages and containers
  ([76c5f7a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/76c5f7a8f272de5dd0ea8408d52cee69d44dabec))
- **PIGEON-3864:** default to pigeon when application ownerteam is deactivated &
  reactivation modal
  ([23d38d3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/23d38d30a7af281a6d66e0df4fb8d0561238640e))
- **PIGEON-3869:** add permissions to variable mapping
  ([db2d80d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/db2d80d5f025eb5859af6e2138d3a1a3e9799f2e))
- **PIGEON-4008:** add option to reactivate children when reactivating team or
  app from details page
  ([cec9f4e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/cec9f4e268a4cec96bf0c7f4db1aa2f94e3e7dc0))
- **PIGEON-4049:** support teams integrations tests
  ([570d29e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/570d29e9e98d41d1eaf4253f1213a7a023b7c322))
- **PIGEON-4217:** explicit jenkins node version as workaround for invalid
  pipeline defaults
  ([20b594a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/20b594ad63ffd3ed44c16c2e184c771789ae9e0a))
- **PIGEON-4469:** only store manage permission in db
  ([613f0ef](https://bitbucket.agile.bns:7999/pigeon/admin/commit/613f0ef03cde483bc4e802b1091741c9f6a6973f))
- **PIGEON-4476:** gitversion support
  ([62bf00b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/62bf00b38101a8bea105413735585de5b0e848f4))
- refactor access component
  ([83812f0](https://bitbucket.agile.bns:7999/pigeon/admin/commit/83812f09908ee2ed8ae19c329612936dd1ad3cd3))
- teams user changes draft version
  ([cf3ca86](https://bitbucket.agile.bns:7999/pigeon/admin/commit/cf3ca86eb71f12c6f4590ae896c803b23754b041))
- uncomment test case and clean up
  ([c11068f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c11068f5ff9a48a1e607a4275320866d81b1ee3e))
- update cancel button type on placement pages
  ([d9e7bf0](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d9e7bf00887aef3ae83b30502dd0a43636186374))
- update unit test listContainer
  ([a90618a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a90618af0e293f997777db532e910ac2c9a7cdb8))
- use new post endpoint for fetching campaign rules
  ([d720a5a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d720a5aa23c774bc9c40ba4007c5a33ca8042265))
- use new post endpoint to fetch sol storefront rules by access
  ([79a74d0](https://bitbucket.agile.bns:7999/pigeon/admin/commit/79a74d0afb06a2587bf9cc2248fbf63241d2b35d))
- working changes
  ([a11d7f9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a11d7f953c4607a63b0f3919718a9cce864c8ea6))

### Bug Fixes

- [PIGEON-3144] Fixed console warnings
  ([476f0a7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/476f0a7ca061b2bf6c650fdaebb98d6c66362db5))
- [PIGEON-3162] Removed indeterminate state from top level checkboxes, fixed
  select all bug, enhanced uncheck functionality
  ([70e3fe6](https://bitbucket.agile.bns:7999/pigeon/admin/commit/70e3fe644dd8d2671e275237c51d6885cee261a9))
- [PIGEON-3400] Permissions table in code
  ([2c2a8c7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2c2a8c798d5325d8160f624257d11bc0c572c9cb))
- add condition to useManage data for mapPages
  ([c7514c1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c7514c113cbac760582c0d57164e87cf318a1c3d))
- add confimration modal application details
  ([6bebfd2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6bebfd2296f8cbfc0586f8a71e46ff173eb33a76))
- add filter query on activation deactivation for containers pages
  ([d104aa2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d104aa2156b4d9ed31498a17f09b5543a954b0ec))
- add filters when user is activated or deactivated for user list
  ([920a28f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/920a28fb4e3c090487f2f47d17f5f3a4b0de0e3b))
- add inactive in dropdown roles
  ([ad71789](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ad71789de158e2b9edf1bd7aab321619c6b5e36e))
- add more test cases to useRedirect hook
  ([e10ba30](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e10ba301698aedaf0ac41adbd2ff3027c8f59b8d))
- add more test cases to useRedirect hook
  ([d5ebd7a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d5ebd7a032a315ebd8ac35d03ebcff2f2e12c28a))
- add new line for tooltip text
  ([d49a84e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d49a84e3b311ae58ac8228b415da943750cc845e))
- add permission to get application container page to create new alert
  ([03d0c20](https://bitbucket.agile.bns:7999/pigeon/admin/commit/03d0c2061ccded39b69bec824160e7e281685397))
- add return if user deactivated on user details page
  ([49e0051](https://bitbucket.agile.bns:7999/pigeon/admin/commit/49e00512f604343de0e4f3fe63a1d3cd29d9275d))
- add search functionality to backend for get all users route
  ([e3da021](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e3da0211c81611eba1a6043e15e66340b0e9b2a1))
- add set modal visible prop to deactivation modal
  ([9546682](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9546682d8906db088d7645562b4f6514669d9c00))
- add timeout for roles search
  ([004c046](https://bitbucket.agile.bns:7999/pigeon/admin/commit/004c046745ac58d719b5ddf2f0e747a9a8f9d408))
- add timeout for user search
  ([916cfe4](https://bitbucket.agile.bns:7999/pigeon/admin/commit/916cfe48fa2451820d91618757c9202e428a1f06))
- add unit test to increase coverage
  ([3250aee](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3250aee77e6818cbf77bbb80d5818971087da1ab))
- add unit test to increase coverage
  ([f986221](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f986221e8b239098fb13a924d2d52b5f26b5188b))
- add validation for unique sid and email to create user
  ([0b8f534](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0b8f534646b8111c63b0bedc8f23c2df3d5dc2c8))
- add validation to team owner if the same owner is deleted and added again
  ([45a1022](https://bitbucket.agile.bns:7999/pigeon/admin/commit/45a10226bb8af01df739897e85ffe8fc1cc4eb11))
- add viewer role to updated owner
  ([8cfceba](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8cfceba42f4f7af76c4d2efb00167a68f98b465f))
- added condition to tooltip
  ([9f30d40](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9f30d40fc8e55dcdeefa5f9293815a7e92bbbe5f))
- addressed FK failure, delete permissions off roles team no longer has access
  to
  ([97fc096](https://bitbucket.agile.bns:7999/pigeon/admin/commit/97fc0962d788890d9d4970b9ae5d40957a42005e))
- **bugfix/PIGEON-4239:** fix checkbox default status in reactivate team modal
  ([106238c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/106238c91e13e0d89f0b438ed9f6fc2e8cfee5fa))
- cache control header added
  ([a9df5a5](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a9df5a532f10cc2511ef32ab8c6456c24f92c7d5))
- cache control header added
  ([1d7d8fb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1d7d8fb26507412239cff728ff401d98d57421b2))
- cache control headers added
  ([243efca](https://bitbucket.agile.bns:7999/pigeon/admin/commit/243efca505a7b3bd23b5d1a0994eeed38875d6b2))
- cancel modal fix teams and roles
  ([9eeb739](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9eeb739fae18babd390e86cd320c28bc2a13f164))
- change page limit for user and roles list
  ([04fd05b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/04fd05be5250bc7fc32971f36d68d3af3dcf9b34))
- change pagination border type to card for teams users and roles page
  ([383945a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/383945a874eba53835366c584db2f35546faf63e))
- change start date picker position to avoid overlaps
  ([9791ad1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9791ad1ba95c497274ab48724c75d584b9d0c699))
- change status to action team owner column
  ([1ea1e0f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1ea1e0f7f3c6595e55f48c6cfc12872ee56e93a7))
- check manage access on rule details pages
  ([e6006ef](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e6006ef060d6d580d26b132424d7ef2210352a4c))
- checkbox disabled prop added, tooltip added user details page
  ([cb88117](https://bitbucket.agile.bns:7999/pigeon/admin/commit/cb881177497ab8d66e1580d35028e00ddf5de7b0))
- checkbox inline false
  ([1fd56bb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1fd56bbbe3672b4833004ba28a29ee36ed7b017f))
- code refactor for assignees list from the same team
  ([864406a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/864406a363de003efbcf44002ad24ed0c5fe5156))
- confirm team owners modal
  ([f74db29](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f74db298bb36de5b47a4cf0cf27f2fac36046a50))
- conflict resolved
  ([014cdde](https://bitbucket.agile.bns:7999/pigeon/admin/commit/014cddee2ece8cb0e2cf2bc83eaab8700203fdb0))
- conflict resolved, user details, userRolesTooltip
  ([d7b0a3e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d7b0a3e15f0fd646777d9cc3c6933f7d0d1b3a3a))
- conflicts resolved
  ([938444e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/938444ebdace0906b843a05ebbcefc0e2811fe0c))
- conflicts resolved
  ([58614d3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/58614d3dd92f1013e1a895dce334211a45a6df1a))
- conflicts resolved
  ([d348a23](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d348a239b17d587fa9b727bf108a5e8410bcbcac))
- conflicts resolved
  ([3e5dd04](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3e5dd049a09f4390169f028c49c0b8004bac0e22))
- continue and cancel button added when only same team user added as team owner
  ([2cfa902](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2cfa902c3b8ef0e86e99beac1a869dcdb087fa7e))
- custom tooltip for InputSelectField
  ([c5b8294](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c5b8294749b096915b0f9c5777fbc017b08d285f))
- custom tooltip for InputSelectField
  ([80420c4](https://bitbucket.agile.bns:7999/pigeon/admin/commit/80420c4da7f6d4f58a859efc0e5c0fa8119a3447))
- deactivation modal
  ([99533a2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/99533a2188fdb933159cabfa3697df04587de948))
- deactivation modal added
  ([746d60f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/746d60f98cc3a1117ab0ad105fa8f270724a65cf))
- deactivation modal added
  ([8a4a133](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8a4a1330f1aa55e69e42d6c7c4bc9354be20d036))
- dispatch added for update create teams
  ([eef7ef2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/eef7ef2a09df9aef1a7f1c34766413e32e5e69af))
- dispatch change role details status
  ([0ed5b9d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0ed5b9d3e2a13266a03b1d94142adea74cc4347c))
- display users team in rules table for alerts
  ([e02afb8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e02afb847c9e228a26b814a8c7fa00f5bcb11083))
- empty array approveres
  ([689ef8a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/689ef8a0ab69b5c1084b7a19e3a2d8e36af37c73))
- empty array approveres
  ([11ca4ea](https://bitbucket.agile.bns:7999/pigeon/admin/commit/11ca4ea38300e7cfe2709c996ef92e177f2e1918))
- error messages added
  ([eabeb87](https://bitbucket.agile.bns:7999/pigeon/admin/commit/eabeb87cbc720dd1580bc4e352ac6d08a68a9cda))
- feature/teams merge
  ([ce6e7a7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ce6e7a7ec1934e7d8ccc19991df69b23756b65f6))
- filter queries added to get roles
  ([a078f1b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a078f1b0027d9b5073d15f6e255c62ea067c6090))
- find condition updated for selectedContainer for campaigns
  ([a8b7cd6](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a8b7cd69d4b4a2d8c31fd00494d58762f7f5a847))
- fix access users to campaigns + fix the redirect logic
  ([2bb3ffe](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2bb3ffe8c08a0618a824fe1dd0d026acfb906b9d))
- fix active/deactive users within the team
  ([a7a6d6e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a7a6d6e65839929d38913d438e14f0a415a62b9b))
- fix pending tab under variable mapping
  ([b3fdce2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b3fdce2dab202893d8dbfb56c943ea4f4ede3c64))
- fix skipAllExceptTeamOwners flag typo, fix update team call on update owner
  roles
  ([598f16d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/598f16dca58caf2ae1f925a0c635b501ad5fe9dd))
- fix sorting for roles list
  ([5b9f0f9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5b9f0f99bc5307b61f562615ece6440ab01fc8e8))
- fix unit test for permissions table
  ([70a9662](https://bitbucket.agile.bns:7999/pigeon/admin/commit/70a96624154f3941eec525116060eeb67c766194))
- fix-black-screen-once-change-application
  ([bb1e2c9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/bb1e2c90df9e7490df540a2b1028af22d43169b7))
- fixed typo in teams skip access flag
  ([89b5729](https://bitbucket.agile.bns:7999/pigeon/admin/commit/89b5729470bbde063ac43acd6431c011fbbe3de7))
- font size fix create teams
  ([47cd534](https://bitbucket.agile.bns:7999/pigeon/admin/commit/47cd534c0500ab236be1d05c6d5a2ddf5841182d))
- fontsize and text for no matching criteria teams list page
  ([3e584ca](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3e584caaa9b39fec3a309a6cdcc50fa0208bc5dd))
- get assignees logic moved to backend
  ([4270af4](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4270af4574769171ef6c925fedad5de4259c6727))
- get campaign-users route unused params removed
  ([a14ee1b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a14ee1b61becb4cd44bfcc0f49e7efcc2c94a65e))
- get full list if page limit is not defined
  ([0d96287](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0d96287761701b3475bf1e6c6deee606421f0e85))
- get teams, team id query
  ([0a8248e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0a8248e8cf67539d28d62ae214424528f9da8a34))
- give pigeon access when new application, page, or container is created
  ([e3d21e4](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e3d21e48721a43a12fc305eaef306745a91c407b))
- group title updated
  ([678580d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/678580d3a12dac9bcfa504d7fa23795572b7fddb))
- hide create user button
  ([03b3484](https://bitbucket.agile.bns:7999/pigeon/admin/commit/03b3484e6f69f4d7f62a2fce820dea04a8117ec1))
- hide team owner subtitle
  ([de5b610](https://bitbucket.agile.bns:7999/pigeon/admin/commit/de5b61046616ac8dc4dae9feb26d97917289c64c))
- inactive application tooltip for containers
  ([d02f740](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d02f740cd9a2ef49bec84585fa7617ea3ee00113))
- increase payload limit
  ([579b47b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/579b47b124edc1131f25f79d6409095bbc5d4b80))
- initialize previous state
  ([2352690](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2352690387373224b80bf8a838c47b389ed3bfae))
- lint
  ([8a1637b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8a1637b3c2ba28ce3f68c10985d0ccf0d1f3908c))
- list page interim status tooltip
  ([22ebc6c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/22ebc6c5ade1bda14a13e9ef8b6e5d15f68bc9a7))
- loader added on teams details page
  ([8df1061](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8df10613e5d6f865b2e248cca2bfa8e3b6a5f3df))
- lower case email validation text for create user
  ([584fc1b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/584fc1b858b61e9d7214e0d755c00ba93fe688db))
- manage permissions to lower case
  ([bf641f2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/bf641f230bc63404b54288cc9fe281a96c9a9370))
- map pages check for useManage Data teams details
  ([0548aad](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0548aadf5805b606275f5506703d29257249d44b))
- margin top team owner confirmation modal
  ([54f5ff4](https://bitbucket.agile.bns:7999/pigeon/admin/commit/54f5ff49424b6e006813171c9de906968b460d4d))
- modify getTeams to get team owners only for users list page
  ([3362ba6](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3362ba6d61c9319123f1d7df8faaab70bf5109c5))
- name validation added to front end roles
  ([34dc89f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/34dc89f3785942ad5aea6e6150751a0f160960c9))
- name validation added to front end roles
  ([5a68d17](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5a68d17a4a80cb38c193e53e16b92660ad912f58))
- no blur for input select field component
  ([3c32e1c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3c32e1c2f7e736f2c62fbc12bb09f154e11afb8d))
- no team owner changed to textbody
  ([a4abcc2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a4abcc20db85be755044f35260ea6b6d6f68bd8e))
- only trigger deactivation modal if user has deactivated the user
  ([93d95ca](https://bitbucket.agile.bns:7999/pigeon/admin/commit/93d95ca072c08ddb523a2992de44dbf36a203662))
- page limit added to front end for roles list
  ([da41c71](https://bitbucket.agile.bns:7999/pigeon/admin/commit/da41c71acd23096846d728946053368893c8dd2d))
- page limit decalartion on the front end for user list
  ([f652cff](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f652cffbf1956b574f46f8525fd61a3569efffb7))
- pass autheticated teamId when clearing all filters on user and roles page
  ([c94a1f0](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c94a1f094dcd39e47a18a66da61dcba40cfc1051))
- pass teamID as query for users without application view super permission
  ([fec4c9f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/fec4c9f2e5072f5c9e060fa12b971d63360570e0))
- period added to teams user and roles snackbar
  ([b7d1043](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b7d104378c6a117c3e20c30d35d416c00ccf01a9))
- **PIGEN-4292:** alphabetize teams list in filter dropdown for roles list page
  ([d0855cc](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d0855cceeacebc77172de6ec70b68d865c2a50c8))
- **PIGEN-4293:** dropdown list entries in alphabetical order users list
  ([2d2f8ac](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2d2f8ac49c7b68462dfc67dbbf5ceeb0c026760d))
- **PIGEN-4300:** dropdown list entries in alphabetical order container list
  ([26f5aca](https://bitbucket.agile.bns:7999/pigeon/admin/commit/26f5aca84693f8059d993c7a449a9719aec12440))
- **PIGEN-4300:** dropdown list entries in alphabetical order container list
  ([7aeb462](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7aeb462e2b6970e5f7515b96e9ba5d8736285f73))
- **PIGEN-4301:** dropdown list entries in alphabetical order pages and teams
  list
  ([b25ac9f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b25ac9f1c5a60d4fdfdad16d9a1f260d129b28e0))
- **PIGEOJN-4427:** pass teamId to query for activation deactivation placement
  pages
  ([61b4b52](https://bitbucket.agile.bns:7999/pigeon/admin/commit/61b4b52bf77f71f1ea391f9778fca25fee06964e))
- pigeon 4108 - 4127
  ([a4d26bd](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a4d26bd57070239bd94905ad421d45fad9a83048))
- **PIGEON-4025:** remove error message on logut
  ([107674d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/107674d2cd1b81ecd55dfecf0737f96707a1dbe3))
- **PIGEON-4025:** remove error message on logut
  ([fd6d4d9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/fd6d4d9d5957998c752af704901acab1520915cd))
- **PIGEON-4067:** fix team owner and make it use name not fname & lname
  ([f794456](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f7944560f4ea18b02a594c44cca3774a6c4a0e47))
- **PIGEON-4074:** use full permissions on permission diff check
  ([c3539d0](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c3539d038b6a96b77c21b043214b47c4179e5513))
- **PIGEON-4078:** filter campaign filter options by access
  ([95e5a39](https://bitbucket.agile.bns:7999/pigeon/admin/commit/95e5a391c492f378629da661e635ac7e2e0182e9))
- **PIGEON-4085:** remove unexpected alert banner for mid-session deactivated
  user
  ([6f69b3a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6f69b3a2af962225ab0b83386558619337b5abf4))
- **PIGEON-4095:** status toggle & tooltip functionality for page container
  details of disabled app
  ([bc585f2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/bc585f276607f496f0cf0f26b82fd65e7625ba75))
- **PIGEON-4099:** fix placeholder incorrect spelling
  ([82667c3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/82667c38db5cb12df8e86cdba98a22611786341b))
- **PIGEON-4110:** fix missing search + filter teams
  ([5b9e570](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5b9e570a8aec7665da7da14c29c7032cda2bcb71))
- **PIGEON-4112:** fix sortable two columns in teams page
  ([393674b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/393674bbb771372eb2706879ff14734e6e21915c))
- **PIGEON-4113:** fix Tooltip on teams page
  ([1d83e72](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1d83e72dc1ef90f8d27947ad2789196f8482757b))
- **PIGEON-4114:** fix add owner team modal zoom
  ([8f6d965](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8f6d9656690a4c2a9a5023e03837dce072d61318))
- **PIGEON-4115:** new label for deactivate sole owner warning
  ([86f6592](https://bitbucket.agile.bns:7999/pigeon/admin/commit/86f6592ea50457611d8daa71b5bbebc0948a95d4))
- **PIGEON-4118:** team placeholder and validation are missing
  ([3a4df82](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3a4df826028546e3f4c71f8e073875fb2b7022ce))
- **PIGEON-4122:** make status sortable on roles table
  ([1f1463d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1f1463dbd00d61f4f0c9f1496c0477ae0be39a58))
- **PIGEON-4122:** remove extra console.log
  ([f110f84](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f110f848a8c73b0c32c123a916cd44ef35e0e493))
- **PIGEON-4123:** fix filter functionality on roles page
  ([74896cc](https://bitbucket.agile.bns:7999/pigeon/admin/commit/74896cc44b214526fbe6477b7b91850b74b51f17))
- **PIGEON-4125:** fix add autocomplete feature to email + name
  ([c080286](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c080286469d455a7e5d69c8792eb025edbb6a828))
- **PIGEON-4125:** fix error conditions
  ([d384cdb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d384cdbc2d9e8a4eac53f6a7a5988747fc3645f9))
- **PIGEON-4126:** fix error message on teams screen
  ([8f78965](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8f789652e1ec25c19e4d6e2ab7eb3bad9a8f0622))
- **PIGEON-4131:** fix verbiage with roles page field validations
  ([9a7c8a3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9a7c8a343227f8f48280d4eb231c00c864b07539))
- **PIGEON-4133:** fix font size/style in tooltip role page
  ([4e21e58](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4e21e586f31679c8a4e5071921c4d6386e0d7698))
- **PIGEON-4133:** fix postion of the tooltip in access table
  ([7d9edd3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7d9edd3c18d718820ebd0934160524682b2d3c38))
- **PIGEON-4136:** add msg body to role deactivation warning modal
  ([0c33f9a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0c33f9a931d67ca9055f25106a3df7a69323f78f))
- **PIGEON-4137:** trigger user deactivation prompt when deactivating associated
  role
  ([2cd3e05](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2cd3e05d0ef119022c64235676763c4dd4154f32))
- **PIGEON-4150:** draft variable mapping permission fix
  ([fa36d82](https://bitbucket.agile.bns:7999/pigeon/admin/commit/fa36d826d0af1bd5ae8234033f15915c9155d64c))
- **PIGEON-4151:** change css class name
  ([47d81df](https://bitbucket.agile.bns:7999/pigeon/admin/commit/47d81dfe0e2990b1784ec175c8297d892c04b28b))
- **PIGEON-4151:** fix alignment of 2sv options
  ([145f491](https://bitbucket.agile.bns:7999/pigeon/admin/commit/145f4916856f7e8add9861877a9a64ab78dd22d9))
- **PIGEON-4151:** fix missing . at the end of the tooltip
  ([8b208eb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8b208eb5d0e34b7ec8ba5e2965682501a8e23cab))
- **PIGEON-4151:** fix missing . at the end of the tooltip
  ([a121db8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a121db8940a4344ddf6d7c2e42ecd815cb0e120f))
- **PIGEON-4151:** fix tooltip desigh for role screen
  ([750dd2d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/750dd2df092957f284c9f0020f9c71bf130e1e0e))
- **PIGEON-4159:** add missing . at the end of the error message
  ([e595994](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e595994463d115335b2157a4eb36e04813307abb))
- **PIGEON-4161:** new ux for confirm owner modal addressing various bugs
  ([9cfc1f9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9cfc1f99f8a068cfcdfc87f4d6982ec32c65171f))
- **PIGEON-4164:** fix tooltip for the team details page
  ([a2638fd](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a2638fd0cfb399d2c76beaeca9e4249bea4a1533))
- **PIGEON-4164:** replace role word with team in tooltip on team page
  ([73499cb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/73499cbf5a737b205de7879df2db2f1b1b589809))
- **PIGEON-4166:** use placeholder permissions if role loses all permissions
  when updating a team
  ([6bc6676](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6bc6676176dbda7451e5b79577ffe9b84d7088af))
- **PIGEON-4168:** filter pages in dropdown by access when creating a rule
  ([a6ec41b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a6ec41bf770031c663feb8f453b6b9ed3e5c0ba5))
- **PIGEON-4169:** fix postion of staus column on roles table
  ([509cc9c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/509cc9c3fe776357de1aa9efded2e8f28ef89341))
- **PIGEON-4172:** fix unexpectedly listed in the Nova - Alerts Pages
  ([5ec1a06](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5ec1a068cc686cfc7cf46e8d0ffa686f078d01ae))
- **PIGEON-4175:** fix placeholder color in creating role
  ([b33ed71](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b33ed71f63e0b74f6f6ddd9071fcd3e4bdea08f5))
- **PIGEON-4176:** fix lint
  ([c35eba0](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c35eba017dc7d6a7cc70c172badd80bae2a4cd0d))
- **PIGEON-4176:** hide autoComplete div in case of no result
  ([65e4efd](https://bitbucket.agile.bns:7999/pigeon/admin/commit/65e4efdf9e8fc9707732b6ee4650ecfc9f4506e2))
- **PIGEON-4183:** pass teamId in query for users without view super
  applications pages containers
  ([2fa8d83](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2fa8d8316ccf247f2147611fdbd4b508ba145053))
- **PIGEON-4183:** pass teamid to get applications for users list page
  ([338e305](https://bitbucket.agile.bns:7999/pigeon/admin/commit/338e305e37f22fddc63be0548efa1e5f438674a7))
- **PIGEON-4186:** filter alert list application filter by access
  ([b9c5e23](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b9c5e2304b2c8481a63391308f0caaeda1bed903))
- **PIGEON-4189:** prevent sole owner of inactive team from switching teams
  ([72bceb4](https://bitbucket.agile.bns:7999/pigeon/admin/commit/72bceb42ce79ca469d19625fe23d76123f21b566))
- **PIGEON-4194:** fix checkboxes status on permissions table
  ([0da5f78](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0da5f78b251e6d4e987384eb781c5e0bef6e1424))
- **PIGEON-4195:** hide disabled permissions on role page
  ([e933978](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e933978038b22e086e4af128574c23fa9e6c6c7d))
- **PIGEON-4198:** pega variable mapping permission fix
  ([2c65f21](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2c65f2107c877f409282a815504fe6c0b7288796))
- **PIGEON-4199:** fix new user on team owner
  ([905b683](https://bitbucket.agile.bns:7999/pigeon/admin/commit/905b68313e2a0ab787a927f8d7fef933ad263c01))
- **PIGEON-4199:** fix new user on team owner
  ([fe14f4c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/fe14f4ca5719b89bcfb3d969261f48683c39ed50))
- **PIGEON-4201:** sort page list by name and id
  ([fe49b98](https://bitbucket.agile.bns:7999/pigeon/admin/commit/fe49b98ebe75f91abca772f566f5bed744e42c48))
- **PIGEON-4201:** sort page list by name and id
  ([61640c2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/61640c24227962424f77a5bc93b0eaa1c9c35bf0))
- **PIGEON-4202:** fix missing Pagination from teams page
  ([ac99859](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ac99859ac701e763a12f2e3242f2ba9fc33c1fc3))
- **PIGEON-4203:** sort container list by name id and ruletype
  ([0878fea](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0878fea4debd3762ae008f86ec872c595544eafe))
- **PIGEON-4203:** sort container list by name id and ruletype
  ([dca9ce1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/dca9ce1231e0c5512f39deb557f92d96f7010408))
- **PIGEON-4204:** add css, text overflowwrap for containers table
  ([17f0e16](https://bitbucket.agile.bns:7999/pigeon/admin/commit/17f0e161742517628477d821c042880acc16f81b))
- **PIGEON-4205:** fix access table accessability
  ([5b254d5](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5b254d56d48febf9293683d7db68fd20ee8f7a6a))
- **PIGEON-4205:** fix test cases
  ([f0ea2ea](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f0ea2ea9ca52a3d1b2492835c91d737745fd1f4f))
- **PIGEON-4205:** fix test cases
  ([6c70017](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6c700179b0505b7d28d651d875ed2754681490de))
- **PIGEON-4208:** fix no result text + font in user + roles pages
  ([8df77fa](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8df77fae2770c7caa7d42273ba908199bd0b2091))
- **PIGEON-4209:** fix scroll to top on changing routes
  ([74a1518](https://bitbucket.agile.bns:7999/pigeon/admin/commit/74a151890ed65b9d17cd950984ec7a99cf59e8df))
- **PIGEON-4214:** disable toggle if user team is inactive
  ([39d85f9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/39d85f9124cf64f0d0665de4089a418f9ccf778e))
- **PIGEON-4215:** fix clear button in teams list pages
  ([4404332](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4404332a57d3a324b4a4ef37ac112317af9eb598))
- **PIGEON-4216:** add missing access updates on placement change
  ([c02cfb8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c02cfb8bd12a7811ecbb124f9178f811dede1df1))
- **PIGEON-4217:** prevent unnecessary team switch query
  ([6aee699](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6aee699307b381a70677652e0eff74756b5be4f7))
- **PIGEON-4218:** fix initial selections for editimg team
  ([596f28d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/596f28d0e2f523db48ea0f52da282464a7258914))
- **PIGEON-4218:** typo state teamData teams details page
  ([cd7b045](https://bitbucket.agile.bns:7999/pigeon/admin/commit/cd7b0451d04897f01968222ba80a883f88dbeb60))
- **PIGEON-4219:** allow users with teams access to fetch containers & pages
  ([1087303](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1087303111ffea97c4409d8159a4e413e67e1f83))
- **PIGEON-4220:** add snakbar to teams + users pages
  ([177c80a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/177c80aff8e686132c14857c041cd1f1053f770b))
- **PIGEON-4220:** fix extra . at the end of the snakbar
  ([271f7cc](https://bitbucket.agile.bns:7999/pigeon/admin/commit/271f7cc89e4f6ca95515df2abee5cd0cf70c8a76))
- **PIGEON-4220:** fix snackbar message on role pages and users pages
  ([35771d1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/35771d10abbf42c4ca7063e13c405f9e693e6f3b))
- **PIGEON-4220:** make the first word bold
  ([005f36d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/005f36d88a8e1eb564595dceb836ee86f2044d12))
- **PIGEON-4223:** fix clear button on roles , users pages
  ([68f16a8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/68f16a8dfca76ce90578e29e283f3cf45d5ba214))
- **PIGEON-4223:** fix search functionality on roles + users pages
  ([e9cf9d8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e9cf9d8582ecb206a5e9ff2fa0c31c61eb6e92b3))
- **PIGEON-4226:** fix snakbar messages on Roles pages
  ([5460a6e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5460a6ed02ab7fd42447b840d3f8e9e0d28ed89f))
- **PIGEON-4227:** on change team in create role page check the status togg;e
  ([d0c0eb5](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d0c0eb5bd5d05d1395ed673485c224195dede691))
- **PIGEON-4231:** lowercase app id
  ([6978135](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6978135e525c3bb28b78eab96023e37f61da948c))
- **PIGEON-4233:** fix clear button style
  ([d52862b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d52862b319d9b4b4a51ca1800eed27b8b4b7d4bf))
- **PIGEON-4233:** fix filter teams on roles/users pages
  ([d53fe5d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d53fe5dada968dfec79e009528e4c628ce62b2ce))
- **PIGEON-4238:** fix viewer role on updating team
  ([d51fcb5](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d51fcb583ca5316050f82c90488e6f2bab076559))
- **PIGEON-4239:** fix checkbox is selected by default in reactivate team modal
  ([c74f3bb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c74f3bb316064a570ef3f54cb89bc6445d444a97))
- **PIGEON-4240:** fix team_id on creating user
  ([5d2b24e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5d2b24e715ec49a25ef6d6180e7a4e7b1b80c03f))
- **PIGEON-4240:** fix team_id on creating user should be added in case if the
  user can't edit all
  ([0e45807](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0e45807fae9c47fdd14618960d258a62a07f8529))
- **PIGEON-4242:** fix displaying clear all button in case of external user
  ([3d0fc30](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3d0fc30ff00170976fdcb08f2220c79f2b25980a))
- **PIGEON-4242:** fix error on search for users without teams
  ([a92c797](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a92c7970445b3737ffc7709c79b2dc511ac91b2c))
- **PIGEON-4246:** fix empty fields on user details page
  ([1f5048d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1f5048d70a7519da412fe83a3b5aca71d335cbd7))
- **PIGEON-4247:** fix Variable Mapping text
  ([f11e63c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f11e63cb384fd1103d47e58032e6ddddd263c7eb))
- **PIGEON-4249:** disable list view toggle for inactive teams' roles
  ([98621ea](https://bitbucket.agile.bns:7999/pigeon/admin/commit/98621eac8e623eabc8477d7a5ef4e6baafc6be10))
- **PIGEON-4251:** reflect team status toggle value in toggle label
  ([ab0ee8b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ab0ee8b2b4e32e3f355667ad07bf3b59f8c38a5c))
- **PIGEON-4254:** fix snackbar width
  ([ec35b86](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ec35b861b6b36cd93eaae2bbb8d3cb81718748de))
- **PIGEON-4259:** add getuserIds inside promise all
  ([010f0b5](https://bitbucket.agile.bns:7999/pigeon/admin/commit/010f0b5affe562d52203856fde5b97feb9c317b9))
- **PIGEON-4259:** fix updating team with new user
  ([8a81c67](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8a81c67bc8d90e0684ad78f07761de7bf2bb6129))
- **PIGEON-4263:** add use user hook + fix sending wrong role to back end
  ([d57c1ef](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d57c1ef097e9c4bbb2c7f6c4e1a26480fd90432d))
- **PIGEON-4263:** fix small bug on changing team when creating a user
  ([b77ce15](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b77ce159ffee957f3e0ed5ed8135c80d839c0086))
- **PIGEON-4271:** sanitize invalid roles on user page submit
  ([e6c0977](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e6c0977ef09dacab1340d343dce2a54cb74d616c))
- **PIGEON-4272:** fix order of containers and pages under access table
  ([41a3db8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/41a3db85d89cbbd9c7c4e3f1bbc114519cdf21f6))
- **PIGEON-4284:** display pages in team access dropdown only when linked to
  container
  ([16ddb9f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/16ddb9f39cf4b9ae219a8ae6e31561a0b9968948))
- **PIGEON-4284:** handle case where is page details are undefined useManageData
  ([9d3b2d1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9d3b2d1190688cd26d0b92fc26aa2ed458ed73f6))
- **PIGEON-4290:** reduce the joins number by removing the application join
  ([71200d8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/71200d8a68b49b9c76ccf9b822d7d144033418d9))
- **PIGEON-4290:** remove ownedApplications from backend and add it to frontend
  ([160549f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/160549fce1ac125ee7d7b1f0aef575b07b204985))
- **PIGEON-4290:** remove unnecessary else if in get teams
  ([16ef24c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/16ef24c75014019f992f9b01a48f8e4ad93c3616))
- **PIGEON-4304:** disable reject and publish for for status reviewed for
  inactive applications
  ([b23ae62](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b23ae62d3d6930d1b8cab705261108a84f975391))
- **PIGEON-4308:** fix initial selection on creating user using external user
  ([528ac4c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/528ac4c8017f81658521a33ab17abf582fe4dd0e))
- **PIGEON-4310:** check application items to find selected app
  ([d076056](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d076056b8465ee6f603dc2e86f3f0765f5e95b37))
- **PIGEON-4310:** merge conflicts resolved
  ([8fad9b7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8fad9b738de975ae46413868fd902c68d15e7a03))
- **PIGEON-4310:** remove activate button if application pages and container are
  inactive for a rule
  ([48d7566](https://bitbucket.agile.bns:7999/pigeon/admin/commit/48d756666255a12835411eed085f989cca52ad54))
- **PIGEON-4310:** remove redundant check showActionItem, update
  toggleFilterSection
  ([f481c84](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f481c8406221dc25a48012d22e69fe6c1f6b426c))
- **PIGEON-4310:** update activate button display condition
  ([c25208f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c25208f6fb7325743538b930354e95e1c809ec94))
- **PIGEON-4313:** fix mobile view hamburger menu signout link not activating
  ([547ff98](https://bitbucket.agile.bns:7999/pigeon/admin/commit/547ff982f6c0e25835a215aa5a21b5fe54d1ec1f))
- **PIGEON-4320:** filter roles according to selected team on users screen
  ([2b666ab](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2b666ab6c8e9bbceabf402cbe915dd15dcc3d7e8))
- **PIGEON-4320:** fix filter on change team in users page
  ([86323f7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/86323f7fb0d345f7d9e08fac71b163169b6c7d08))
- **PIGEON-4320:** fix filter on change team in users page
  ([d116500](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d116500caa85c9ea6d79ed3cb38ef784503c65fa))
- **PIGEON-4320:** remove unnecessary numeric data in users page under role
  filter
  ([86f4224](https://bitbucket.agile.bns:7999/pigeon/admin/commit/86f42241d2aa179201b3621960176ef7dc1ec69a))
- **PIGEON-4325, PIGEON-4350:** disable access table, add sol redirect before
  rendering page
  ([8d3d25b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8d3d25be1cc9527c9bc15b182b0d5c0c55e0f21e))
- **PIGEON-4328:** update campaign assignee logic
  ([639bb2a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/639bb2a5fdee090347130e15392384c664bbb454))
- **PIGEON-4338:** fix status toggle not synchronized to form state
  ([ddda94e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ddda94e2cfc9f5c07d048aee4a96212c3116ed0f))
- **PIGEON-4341:** fix selecting own team on updating team
  ([0283606](https://bitbucket.agile.bns:7999/pigeon/admin/commit/02836068e73b5662e58cb957de78f99f8fa217f3))
- **PIGEON-4352:** dynamic status toggle
  ([13f3627](https://bitbucket.agile.bns:7999/pigeon/admin/commit/13f36271e3df4aa891d46586ace3261098230bcf))
- **PIGEON-4353:** sol details page crashes when user has view access only
  ([cd40298](https://bitbucket.agile.bns:7999/pigeon/admin/commit/cd4029880cb299690026a00132e654c28c9ca82e))
- **PIGEON-4353:** unable to view campaign if no manage access
  ([544de86](https://bitbucket.agile.bns:7999/pigeon/admin/commit/544de86111039024e123d359f305fa20e8d70dd0))
- **PIGEON-4360:** fix missing viewer permissions when create/update application
  ([936db34](https://bitbucket.agile.bns:7999/pigeon/admin/commit/936db340ead2d576a21586e097c17846b6f81bae))
- **PIGEON-4361:** fix app name validation causing too many ui updates
  ([63d38f3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/63d38f39ca6101ea47f15b960555b3f35a8938e8))
- **PIGEON-4374:** update sql statement to get role details for a specific
  roleId
  ([23ea7ad](https://bitbucket.agile.bns:7999/pigeon/admin/commit/23ea7ada583c29e4b676788efa669047f38e5284))
- **PIGEON-4375:** show rule application if view only
  ([128bc81](https://bitbucket.agile.bns:7999/pigeon/admin/commit/128bc810963e8621ce0aa5bdc157a97818e91e84))
- **PIGEON-4382:** add condition to check if teh user can create new campaign
  ([5f4377d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5f4377d50679ef252f666a0fa3480f04d2a11ba6))
- **PIGEON-4382:** fix condition to determine if the user can create campaign
  ([a49b45e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a49b45e2c7eb24323cb9154954e801a3b11e325d))
- **PIGEON-4398:** handle null values for SORT_AND_PAGINATE function
  ([a1d05c1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a1d05c1b1d87900703ada11524efffb0d7b283c4))
- **PIGEON-4398:** handle null values for SORT_AND_PAGINATE function
  ([7ea1cc7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7ea1cc772acc8d5fe5511a555e39b5bcc025b476))
- **PIGEON-4398:** handle null values for SORT_AND_PAGINATE function
  ([57703a3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/57703a35aa2d3d3c3d5a47d0c79fac63b9d73627))
- **PIGEON-4399:** pass teamId in query for user without teams view super
  permission
  ([a15568b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a15568b9c62b11c1a26e88e16343c72f63157cb3))
- **PIGEON-4402:** fix failing test, from latest release branch
  ([ea929d6](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ea929d617a5e8c008dccee8beb2e8a653867e3ea))
- **PIGEON-4402:** sort teams list for entire data set
  ([9c9dbbf](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9c9dbbfd6b3cec404b2bfabe310260eac76df98f))
- **PIGEON-4402:** unit test for sorting teams list
  ([3aeae9a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3aeae9a85bad4c51abb17907ec226bcc51f707c1))
- **PIGEON-4409:** role page role section blank for super users
  ([c563bd6](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c563bd6df914b946f3ed658557433c608fa7a771))
- **PIGEON-4410:** clear container selection on application change
  ([0108937](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0108937d812835ecf8a410288404b93b12c7268e))
- **PIGEON-4410:** reset content modal form on application change
  ([5b247fd](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5b247fd72341530171b2ff59c4d5aa25bb5cd742))
- **PIGEON-4414:** add ability to select owner using keyboard ( downward )
  ([96d8abc](https://bitbucket.agile.bns:7999/pigeon/admin/commit/96d8abc51ef16a77a850100995cb675e9a23b4c1))
- **PIGEON-4414:** change the style of owner modal
  ([6b25c40](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6b25c40011a4ba6b26a883f40168dcb94f550143))
- **PIGEON-4414:** use keyboard to select owner from the autocomplete suggest
  list
  ([50ea136](https://bitbucket.agile.bns:7999/pigeon/admin/commit/50ea136c43bd4da353932c64aa14299a35c64ac2))
- **PIGEON-4419:** add owners to the filters
  ([22b3b87](https://bitbucket.agile.bns:7999/pigeon/admin/commit/22b3b87ef13c0c9dbabb643837cf5b14751d2782))
- **PIGEON-4419:** filter ownership options when application filter is selected
  ([10a61c7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/10a61c7557d39050358aaea7ae69c93a24438037))
- **PIGEON-4419:** fix container filter in pages page
  ([cb70e48](https://bitbucket.agile.bns:7999/pigeon/admin/commit/cb70e4841764d78e463396d7ec0138a511956769))
- **PIGEON-4419:** fix dependency array on teams page
  ([b168959](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b1689593dd63d83424171ba0b4f4f751c30ea34d))
- **PIGEON-4419:** reset filter on teams pages
  ([135e9ca](https://bitbucket.agile.bns:7999/pigeon/admin/commit/135e9ca48f049839622bf9080cb49ba4228f5e24))
- **PIGEON-4419:** sort ownership list by name on team list filter
  ([db6cfa9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/db6cfa930ceb2ce860a02f624d12f5781251becc))
- **PIGEON-4420, PIGEON-4421:** reduce filter options based on app selection
  ([4c63c58](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4c63c58cba43bbf0239412e3d0367c62002aa510))
- **PIGEON-4420:** add unit test cases for reducing filter options based on app
  selection
  ([8c9f8fa](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8c9f8fa4c5749307c071eb1bca7f2306e35c7785))
- **PIGEON-4422:** add missing status toggle snackbars on container and pages
  lists
  ([5448f86](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5448f86b7b5ab53594b8f5cb39a61726f00f5c31))
- **PIGEON-4431:** dont dispay the list until isLoading is true
  ([7fe1045](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7fe1045bcb5f40ac0e425aeafbedb0a1135cf7a7))
- **PIGEON-4436:** pass team id in query if user cannot viewAll or editAll
  ([709d6b5](https://bitbucket.agile.bns:7999/pigeon/admin/commit/709d6b5926358ffd9ba020f152017c6136d94376))
- **PIGEON-4439:** fix filter on rules page
  ([ce9c6cc](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ce9c6cc3e8d4ff0fd486ade0fbd9974330c33957))
- **PIGEON-4439:** fix filters on rules list page when select page
  ([5778248](https://bitbucket.agile.bns:7999/pigeon/admin/commit/57782484420b67acb3fed3e50c7029348d6ecf89))
- **PIGEON-4439:** fix render the filter in rules screen
  ([ac3f3d1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ac3f3d1bfaa4264e9018b81449b5a8814ac9e3bc))
- **PIGEON-4439:** fix render the filter in rules screen on change page number
  ([8941772](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8941772cb8f4c71391750e49f6899b816552d536))
- **PIGEON-4439:** fix render the filter in rules screen on change page number
  ([497cd05](https://bitbucket.agile.bns:7999/pigeon/admin/commit/497cd051b3cb72369a4a3df415b2a46480778577))
- **PIGEON-4440:** alerts page
  ([142bf50](https://bitbucket.agile.bns:7999/pigeon/admin/commit/142bf50d77d5e08a752fe415347410cf059c0d89))
- **PIGEON-4440:** fix create team page
  ([d651931](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d6519311531832f4577690e2abc71029bc60d8b5))
- **PIGEON-4445:** fix lint
  ([06daabb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/06daabb5918945e2be9cdb74db21d203743fbf79))
- **PIGEON-4445:** refactor filterChanged function in listContainer to reset the
  status filter
  ([3706a8a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3706a8adca8ec420189036e6d1822e1a9506f58c))
- **PIGEON-4445:** reset status on changing application
  ([20ac5f4](https://bitbucket.agile.bns:7999/pigeon/admin/commit/20ac5f45d60c0465ecd42c34b2585dd7607d90cc))
- **PIGEON-4446:** fix droplits of owners in teams page
  ([42a8cdb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/42a8cdbc9c57ed0a335d8ea6618d44b14858aee0))
- **PIGEON-4446:** only display owners of the teams not all users
  ([5d08db8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5d08db8d9ced38e7f239502a4987503838c3bba4))
- **PIGEON-4452:** fix permissions condition according to the type(rule/alert)
  ([0b793d6](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0b793d63619ace2e7935431fb2450da3c9440bb6))
- **PIGEON-4453:** add new middleware to prevent call the delete route on prod
  ([cd58197](https://bitbucket.agile.bns:7999/pigeon/admin/commit/cd581973a400e2562a79e57d24812aa5fc2338c2))
- **PIGEON-4453:** fix type form prod to prd in delete route middleware
  ([b3a5d8c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b3a5d8cde495289657f8b749fba4c07de584fb39))
- **PIGEON-4453:** remove delete container request
  ([b8f3690](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b8f3690a26901bd3d608fb69d031ec84749895eb))
- **PIGEON-4454:** add new middleware to prevent call the delete route on prod
  ([9033108](https://bitbucket.agile.bns:7999/pigeon/admin/commit/903310807ef019ffd9a3d1ce01daa48e6e1c156d))
- **PIGEON-4454:** remove delete page button
  ([2c654bb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2c654bb357a676cd5508acdaaa3c2b778bcda522))
- **PIGEON-4463:** enable validation step on submit application page
  ([151a964](https://bitbucket.agile.bns:7999/pigeon/admin/commit/151a964075f0ac9b6c829ad861ae4767a9fa9bab))
- **PIGEON-4470:** use js to get diff between new access and current access
  ([ad4fcb6](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ad4fcb614fd51cb627aee5ca78d0e30a28734955))
- **PIGEON-4471:** resolve functionalities on backend to reduce response payload
  size
  ([7358ced](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7358cedf9ca75b19a3942e385d6b0f9440cfadb6))
- **PIGEON-4471:** skip owners query for get teams call, use data already
  available on frontend
  ([31a8603](https://bitbucket.agile.bns:7999/pigeon/admin/commit/31a8603153b86234a3f14e7f84a1566cb841bb15))
- **PIGEON-4482:** add secure cookie flag to ist manifest file
  ([794099f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/794099f55c955345ccfb9c438bf51e8674dc36d0))
- **PIGEON-4482:** add secure cookie flag to uat manifest file
  ([5f0409f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5f0409ff932af95e4f9d1351278f6c21813ae6dd))
- **PIGEON-4482:** disable user details form is canEditAll is false
  ([2392c7d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2392c7d5c7331434c3b4ce5d895ff0a9e34488f8))
- **PIGEON-4483:** bump moment version
  ([73c7f27](https://bitbucket.agile.bns:7999/pigeon/admin/commit/73c7f27a0366cbb51cc91ffe6fc2a972b64a860d))
- **PIGEON-4499:** add create users fn for bulk user creation
  ([50dbf81](https://bitbucket.agile.bns:7999/pigeon/admin/commit/50dbf811c9cedd29f3deb857ccd8d69b8afc5892))
- **PIGEON-4499:** add missing base role for users created on edit teams page
  ([641726c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/641726cfc7c0d27180422056bbe1594e3074c1a5))
- **PIGEON-4511:** update the variable set mapping textarea heading and make it
  required
  ([fe9027f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/fe9027f5240fcd97486a9e902736d4af28dcc355))
- **PIGEON-4526:** fix owner filter on change Capabilities filter
  ([2ca3a13](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2ca3a137d8c750a9c648b24319937a72978626a6))
- **PIGEON-4531:** disable form when userTeam is undefined while getUser is
  called
  ([db69a54](https://bitbucket.agile.bns:7999/pigeon/admin/commit/db69a54c083a62f81f05c1aaaedd117767060bf1))
- **PIGEON-4531:** remove error message incase reviewing external user
  ([572fdb1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/572fdb1b8a94a6aa015e18d3c58309f8798d3b19))
- **PIGEON-4531:** wrap if user team condition around active team mates call
  ([a5a3acf](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a5a3acfde089be86dca14a1da780c8945df5ffe8))
- **PIGEON-4537:** fix page crash onreload when draft variable page has draft
  set
  ([7497409](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7497409f862340f0e2241b524e2fc9ff3afd1d86))
- **PIGEON-4539:** return core roles already available
  ([a0cc87c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a0cc87cd08933ccbee2326607958ffed5b49a587))
- **PIGEON-4543:** fix table header style on open tooltip containers and pages
  list page
  ([277f576](https://bitbucket.agile.bns:7999/pigeon/admin/commit/277f576d184258a2d134d05694b1fc0825e5f031))
- **PIGEON-4543:** fix tooltip postion on pages and containers list pages
  ([0a6fe62](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0a6fe62f9278b7a8e7ba3d2cdf6dcfd3682128bb))
- **PIGEON-4546:** fix creating user if the current user has user_manage
  permission
  ([a279e97](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a279e977be7043715ea77089253049abef81b6ce))
- **PIGEON-4547:** wait for assignees to be loaded before rendering draft vars
  page
  ([9c7d068](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9c7d0689f1f853ffa47f1aca789bdd459f4ed54d))
- **PIGEON-4552:** return application error message while saving
  ([9aa8964](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9aa89648e486c4b74428e8419bdd70eeaa5ac2fe))
- **PIGEON-4554:** add diff ib rules sub type when edit application
  ([9e8dc78](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9e8dc7897c4e82dd2bfeac9f46c56001b7f67536))
- **PIGEON-4555:** create a tech debt for removeing the ruleTypeId in
  access_page table
  ([aad83fd](https://bitbucket.agile.bns:7999/pigeon/admin/commit/aad83fd0b8b604605b24fca6b018d623c6e74810))
- **PIGEON-4555:** fix changeing pages on update containers
  ([2e00eae](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2e00eae937fcb04ce0b01ec07635e125f35cf7bf))
- **PIGEON-4557:** use full user list to determine sole active owner status
  ([250ae0e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/250ae0e01e7e89babdd44d303e074fca303bbbbe))
- **PIGEON-4562:** fix alert title on deactive the team in teams list
  ([7c36b4e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7c36b4ee0d9315f0bf10bc84a371d63149fdf2f1))
- **PIGEON-4575:** fix change application on users list
  ([934d376](https://bitbucket.agile.bns:7999/pigeon/admin/commit/934d3763c69bfe426fbbcbc1e1ed3845d27cf572))
- **PIGEON-4577:** convert team id filter selection to number for comparison
  ([171e5fd](https://bitbucket.agile.bns:7999/pigeon/admin/commit/171e5fd93e2b33174fa4b4de9f5270e2cf78854b))
- **PIGEON-4577:** use current user instead of filter user to determine edit
  privilege
  ([788766f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/788766f0f749b3553683566e8b56422e6e4d9d6a))
- **PIGEON-4578:** remove unused endpoints - rule types + estore
  ([27d39f1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/27d39f1217fcbcf41bc39167f3de2fab126d508f))
- **PIGEON-4578:** remove unused endpoints - setup and users
  ([fad6572](https://bitbucket.agile.bns:7999/pigeon/admin/commit/fad6572f5de1b17faa0f8a037921af5906558463))
- **PIGEON-4578:** remove unused endpoints - variable Mappings
  ([36463a9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/36463a9c9419e0267b347c7dc9a5eb5ee0e3c941))
- placeholder changed application, container, pages
  ([d192cf2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d192cf289ede75e15a38305048ac999fc5910f9f))
- placeholder email create user
  ([f0e5341](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f0e5341bd14201285e57052365d5c8353ce5b6aa))
- placeholder updated for pages and containers
  ([2f032c3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2f032c31cbcac3f9a48b1c68a4599e676f47100a))
- prevent sol or storefront from being overwritten with rule version 1 instead
  of 2
  ([1b2050f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1b2050f5253b97966bf9cd93ed2df210623dffbd))
- primary button label changed for deactivating team on details page
  ([87d2ba1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/87d2ba1166546dae796688fac5518ff82949e415))
- primary label typo fixed
  ([69b4d1d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/69b4d1dbe72128622be9f19ed476cb938109e500))
- pull conflict resolved rolesListContainer
  ([1a86e13](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1a86e1317266b20d3a2c42e5c740cab264c8ab6a))
- redundant fix removed
  ([e166e66](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e166e66b3994abf0bd1197180bc95d0871c6fd13))
- redundant fix removed
  ([758f09e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/758f09eed7ca4093b63dd2f99c2fd4f58fc0256f))
- redundant queries romoved for getPotentialAssignessForCampaigns
  ([8655dfd](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8655dfd347e24ddf88e11c9e5a87a49bfa1afbaf))
- redundant state variable removed
  ([865f394](https://bitbucket.agile.bns:7999/pigeon/admin/commit/865f3947a130ae9012a318871e60b957459408ef))
- remove duplicate map to add viewer role to new owner
  ([9809f7e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9809f7e680971c62bef08c530f50a21d19fc808a))
- remove duplicate pages while mapping alert for teams details page
  ([86bfeb1](https://bitbucket.agile.bns:7999/pigeon/admin/commit/86bfeb19ecbc0f80a2cfc1a3c6f0ede361dccdcb))
- removed unused parameters for getCampaignUsers optionalGetPathSchema
  ([dea67af](https://bitbucket.agile.bns:7999/pigeon/admin/commit/dea67afcd8f876a0bc1dfaf66ac387847d7daa6b))
- removed unused params from campaign user route
  ([4a1a48d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4a1a48deb5aefa23198fc4a3745893dfde959dc0))
- reomve owner attr from team object on useteams hook
  ([0fa884c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0fa884c6d6fd27027696d75948fe6194b518b6c7))
- replaced filter with some onsubmit teams details
  ([8fd1213](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8fd12138c7d8390b849a2b37c784b4c1d9f07197))
- role filter for user page
  ([8957c92](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8957c92733b3b26d14a74ccf30cf71ec1b057e63))
- roles action update roles status
  ([dcfb1c6](https://bitbucket.agile.bns:7999/pigeon/admin/commit/dcfb1c65bce64b36637e839bf693952334362671))
- roles form inactive teams, tooltip
  ([42361a9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/42361a93713e7943296a60e99c9d41bc2db6461d))
- roles inital permission selection
  ([5eb527d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5eb527d8dd8fded1c2e6880ad453db2d130a3766))
- roles status tooltip position
  ([c14ab0c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c14ab0c140b39e9e46e5faf150300265e195000f))
- selected deactivated application logic changed
  ([dbf2de9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/dbf2de98ea9bd7cf3bcc2d632f60ccadad2aa794))
- send unique access to rule api - required for preexisting uat entries
  ([99c9e7c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/99c9e7cd3e3b4357ac3add862d668ff3fd4fc165))
- snapshot updated
  ([de622b5](https://bitbucket.agile.bns:7999/pigeon/admin/commit/de622b5c9990e604bf4e15bcc13a47f50135dc63))
- sort and paginate roles backend
  ([b193faa](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b193faab3a3128db58f8966f3368631f0908f248))
- sort and paginate roles backend
  ([4fdd517](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4fdd517a3518583ba25a68be550bbc803e56b6c5))
- sorting and pagination for user pages
  ([883a616](https://bitbucket.agile.bns:7999/pigeon/admin/commit/883a616a7c47248f9014df42ba3a9929d4baa605))
- sorting and pagination for user pages
  ([e142b84](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e142b84fc966cef7bf29479390922b8acada9104))
- spacing and tooltip headline roles details assign team
  ([a82f5c7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a82f5c7ed86e3125d338468e14d424fc7bff7fa6))
- spacing and tooltip headline roles details assign team
  ([7c01cf3](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7c01cf3598b8d7cba9abf2f9a6276477600cdbd2))
- split access into chunks to adhere to max msql insert size
  ([effd47c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/effd47c832cf96bcb41bbf28c9fc66ea82f7e46e))
- sql statement refactor for get users and roles
  ([24c0e4f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/24c0e4fa661a8f100751dee3ca452b063fe80f55))
- table accessibility for user and roles list page
  ([b02856c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/b02856cb45028e21a2a6ac8d3daf73256496f76f))
- table columns accessibility link to text button for teams users and roles
  ([f00a035](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f00a035f95d081856f5ecec179bb713a05863922))
- team owner confirmation modal unit test
  ([d580d99](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d580d99d22266f900ac6e5aa07efe91cd0919805))
- teams form confirmation modal for deactivation
  ([924e36b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/924e36b518128ec7fde4029fcdcfa56a2712b5a8))
- teams form confirmation modal for deactivation
  ([ff54c86](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ff54c86e0e30ecd754c48559d65f24eb353cdea0))
- test case added pages and containers
  ([7c9521d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7c9521d4ee2d333ab4e2207d337921a6e1081fd8))
- test case added pages and containers
  ([4fa65c5](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4fa65c5bbce2fed325959ea619a75c418ddc36fc))
- tooltip misalignment roles details
  ([2d9e5e7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2d9e5e724951f4d4051fc68ab171fc0a0b1d6838))
- tooltip prop updated
  ([ffee97c](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ffee97c2a708080f1ead432b6402611c42092230))
- unit test added for sort and paginate function
  ([f8b9dab](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f8b9dabf0e837dde73d47b9cb8c2d30ba658868e))
- update access-permission mapping on rule type
  ([4b66ac7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4b66ac7261462b5dda9fbaa94c35aec631bf501e))
- update key to get users and role for view permissions
  ([0227798](https://bitbucket.agile.bns:7999/pigeon/admin/commit/02277988fa94f314e69e2ca6bd6d35bec860a09e))
- update page number on user and role search
  ([1e38fbc](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1e38fbc3f29ec31da28194488a5bf13b26bb9fb2))
- update page number on user and role search
  ([2c9670d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2c9670dfad64e87eb6a81c33dd9af376f657aeea))
- update previous team owners when an owner is deleted
  ([9f64886](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9f64886f9e79521199c2df3b3a33b4fe31771d1f))
- update query key name - filter by team name
  ([5ad50af](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5ad50af540a3671c2a195fecbb227c612c4f4937))
- update query key name - filter by team name
  ([77d7a71](https://bitbucket.agile.bns:7999/pigeon/admin/commit/77d7a7134a7c4ede4c5c74311bb8a891bf856b00))
- update query key name - filter by team name
  ([aad0b83](https://bitbucket.agile.bns:7999/pigeon/admin/commit/aad0b83bffe83a584db423bf03ea50e31061dc4a))
- update status search sql statement for get users
  ([5c2f4b4](https://bitbucket.agile.bns:7999/pigeon/admin/commit/5c2f4b43130969d489bb77fe1193d704c091a9f8))
- update user button text
  ([88d0ae9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/88d0ae9d0c4c421855a86199c4947f1a22f20844))
- updated getAssignessSuggestions to get users from same team
  ([1faec30](https://bitbucket.agile.bns:7999/pigeon/admin/commit/1faec30dde117403691d720296034c4823096f68))
- updated snapshots
  ([12df954](https://bitbucket.agile.bns:7999/pigeon/admin/commit/12df954da819c89670723a1d7d26c7a79e77ed5d))
- user and role list views cannot fetch teams for non-admin users
  ([6649347](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6649347c266883e7e27e4ead1c22466e855bb2f1))
- user form sid placeholder
  ([aa6e98a](https://bitbucket.agile.bns:7999/pigeon/admin/commit/aa6e98ad9a44bceab5432d78fd0a915bbb4320c5))
- validation added to confirm team owner modal
  ([e3941fb](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e3941fb84a0f3f803022ca946554e6e55dfd63b1))
- validation verbiage
  ([5947931](https://bitbucket.agile.bns:7999/pigeon/admin/commit/59479318b66c432c7f7aa80ad51c1195063104ef))
- validations added to pages, containers and applications
  ([d1a856f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d1a856f6569280a5a46359aa4d3dc9025010ccc5))

## [1.4.0](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.3.74...1.4.0) (2022-08-31)

### Features

- bump pwr version
  ([4db79d7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4db79d765e4150cbbbfef6ac14249777fadd7498))
- bump pwr version
  ([734e590](https://bitbucket.agile.bns:7999/pigeon/admin/commit/734e5908ed502fc5c0dd1a39e97d7ce1bf389d77))

### Bug Fixes

- **PIGEON-4025:** remove error message on logut
  ([107674d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/107674d2cd1b81ecd55dfecf0737f96707a1dbe3))
- **PIGEON-4025:** remove error message on logut
  ([fd6d4d9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/fd6d4d9d5957998c752af704901acab1520915cd))

## [1.3.0](https://bitbucket.agile.bns:7999/pigeon/admin/compare/1.2.3...1.3.0) (2022-01-19)

### Features

- **PIGEON-3108:** refactor var mapping with redux state store and route based
  rendering
  ([2715666](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2715666b78a48aa6fbe56248f4efb28a0072d9d9))
- **PIGEON-3495:** support dark mode for preview components
  ([0332783](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0332783b0aea8f0bdf9f3f66ccdd041b9b17f087))

### Bug Fixes

- Add sonar properties file
  ([2f5ce2d](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2f5ce2db2a3da0ce53d02fb9a643adb83cf18042))
- adding ACCP config files
  ([e9cd79b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e9cd79b12bc4143983fe78b5b6090e3b8c76e0b3))
- excluding fortify scans
  ([2ba9184](https://bitbucket.agile.bns:7999/pigeon/admin/commit/2ba918459f8bb044b3d1aa17ebb196e998768a53))
- mock slack service
  ([8bfaec8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8bfaec8c4d501b2ba79ccee71be137681f647ae4))
- **PIGEON-3459:** duplicate campaign - unable to select content-type issue -
  putting back into jan 14 release
  ([c7109c7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c7109c76df0a08a1b469824ea6f9a1369ebfa51d))
- **PIGEON-3472:** upgrade knex and fix broken test cases
  ([0e61246](https://bitbucket.agile.bns:7999/pigeon/admin/commit/0e61246f71607e3b3dd841049bc13862902c845a))
- **PIGEON-3495:** add standing campaign preview to list of dark mode components
  ([3d91375](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3d913759e9c1049dd6a0ce5752448834c3cfcf0c))
- **PIGEON-3576:** include akyc ux fix from pwr
  ([bb67cbd](https://bitbucket.agile.bns:7999/pigeon/admin/commit/bb67cbd02831b814295ffaa2d5b31ec9c8c330a5))
- **PIGEON-3581:** open up csp rules to allow public site images
  ([17b8e32](https://bitbucket.agile.bns:7999/pigeon/admin/commit/17b8e32debaeb48a825110ac6cc4fb8406d7081d))
- **PIGEON-3586:** disabled dark mode toggle if not supported by component
  ([a9efb1f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a9efb1fd9c51e1a0504a5782f836944968f71333))
- **PIGEON-3638,PIGEON-3640:** filter out campaign manager from approval list
  ([177c71b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/177c71bf47d5a70c93666a7cbd71112a2d849e65))
- sonar exclusions
  ([e44fab7](https://bitbucket.agile.bns:7999/pigeon/admin/commit/e44fab79bfac83b5744a7ad740772c835eb12e5e))
- update snapshot test
  ([49315e9](https://bitbucket.agile.bns:7999/pigeon/admin/commit/49315e9746d5a78089d5c7c7873910e854bfdbf8))

## [1.2.0](https://bitbucket.agile.bns:7999/pigeon/admin/compare/v1.1.71...v1.2.0) (2021-11-11)

### Features

- **PIGEON-3304:** bump to latest pwr patch to add nova soft msg support for
  akyc
  ([8d3cf53](https://bitbucket.agile.bns:7999/pigeon/admin/commit/8d3cf53324bb2c7eef6646d758caf7efbf566b48))
- **PIGEON-3304:** bump to latest pwr patch to add nova soft msg support for
  akyc
  ([9b40504](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9b40504d28ef336b2bb4ced4956192c746f2cc2e))

### Bug Fixes

- [PIGEON-3469] Set XSS-Protection header to 1
  ([63496c2](https://bitbucket.agile.bns:7999/pigeon/admin/commit/63496c217d49daaf6e58f3e9df6fd7c6bb6380b1))
- added missing rule service to variable mapping v2 service
  ([c15df71](https://bitbucket.agile.bns:7999/pigeon/admin/commit/c15df71c020a7c46ea29b4374e4e32f8f519caa5))
- **PIGEON-3307:** reflect var mapping updates immediately
  ([45e5310](https://bitbucket.agile.bns:7999/pigeon/admin/commit/45e5310fb83819d8435099b683c1ad7fb1a9d688))
- **PIGEON-3307:** reflect var mapping updates immediately
  ([6f3c394](https://bitbucket.agile.bns:7999/pigeon/admin/commit/6f3c394112ebb6600fbaec7f375974d6e47655cc))
- **PIGEON-3312:** standardize styling of version targeting buttons
  ([635ee5e](https://bitbucket.agile.bns:7999/pigeon/admin/commit/635ee5eb54ad59a45dd87863c35c03464960b47e))
- **PIGEON-3312:** standardize styling of version targeting buttons
  ([4b12bf6](https://bitbucket.agile.bns:7999/pigeon/admin/commit/4b12bf6ad83db643580f2de5a025e90bc558db10))
- **PIGEON-3341:** make search filter box responsive
  ([3135e18](https://bitbucket.agile.bns:7999/pigeon/admin/commit/3135e18022aae22bc435ca4c6a4dd0105fe79b24))
- **PIGEON-3366:** break word web fragments content name
  ([d2353af](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d2353af4dd25db6abaa6eec64f42e388868bf8d6))
- **PIGEON-3372:** content type not available for duplicated campaign
  ([33c28ea](https://bitbucket.agile.bns:7999/pigeon/admin/commit/33c28ea84937cfe862eddd3e43d7f189bb41f6c4))
- **PIGEON-3374:** fix lodash and lodash-es vulnerabilities
  ([d6be71f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/d6be71f1b46552deb02c84ea043932e3adbb05d8))
- **PIGEON-3374:** fixing the blackduck violations/scans
  ([f782c4b](https://bitbucket.agile.bns:7999/pigeon/admin/commit/f782c4b6e533556cda278a813ecf200ae9c52e04))
- **PIGEON-3374:** update the lodash integrity
  ([ab9bb40](https://bitbucket.agile.bns:7999/pigeon/admin/commit/ab9bb40e06de7dca1758f3e362cf617b385bf90e))
- **PIGEON-3379:** override vulnerable dependencies
  ([a269b26](https://bitbucket.agile.bns:7999/pigeon/admin/commit/a269b2686af270475964e4bb8c8ac986bcfa4fde))
- **PIGEON-3409:** alert text and pageId are too close styling fix
  ([7375052](https://bitbucket.agile.bns:7999/pigeon/admin/commit/7375052f59ac242cac90602f31cdf5e310d6a195))
- pwr bump for legal footnotes fix
  ([9208d9f](https://bitbucket.agile.bns:7999/pigeon/admin/commit/9208d9f359ce4e3cdb903d963b445936b296290d))
- reverting knex version as it breaks in IST
  ([61465d8](https://bitbucket.agile.bns:7999/pigeon/admin/commit/61465d87f96df9d3ea1cb1d9d746685487fe2979))

### [1.1.18](http://bitbucket.agile.bns:7999/pigeon/admin/compare/v1.1.17...v1.1.18) (2021-04-27)

### Bug Fixes

- update `mssql` package to address blackduck issue
  ([790687d](http://bitbucket.agile.bns:7999/pigeon/admin/commit/790687df2048c6fab3c282bd2fdb3f3f26c41c99))

### [1.1.17](http://bitbucket.agile.bns:7999/pigeon/admin/compare/v1.1.0...v1.1.17) (2021-04-23)

### Bug Fixes

- [PIGEON-3044] Container not passed to Content Preview
  ([7c48d54](http://bitbucket.agile.bns:7999/pigeon/admin/commit/7c48d54578a3d4c1d8272c85f1eff62b83b70534))
- [PIGEON-3054] Fixed stuck height on fragment modal submission
  ([20ae45e](http://bitbucket.agile.bns:7999/pigeon/admin/commit/20ae45ea4cd19479b0eddcaa222083cb4f58fc9b))
- added back old canvas stylesheet to temporarily fix broken styles in variable
  mapping
  ([9236f62](http://bitbucket.agile.bns:7999/pigeon/admin/commit/9236f62cfb29283dbd6ed77f7af981fe03931eb6))
- content modal only validate pages if campaign
  ([8ca3284](http://bitbucket.agile.bns:7999/pigeon/admin/commit/8ca3284af31ee805fc4c96ad63055ef3832ebdbd))
- remove sorting functionality until canvas ticket is complete
  ([481ee6a](http://bitbucket.agile.bns:7999/pigeon/admin/commit/481ee6ad828d1125d41c70ee2f377d9fcabcd07f))
- search was not working for alerts
  ([c2551b1](http://bitbucket.agile.bns:7999/pigeon/admin/commit/c2551b1df73e4a054002339b590535c2c9f2170d))
- **pigeon-3033/pigeon-3034:** add 'no results' to search table, hold search
  term between pagination
  ([7e4ea3c](http://bitbucket.agile.bns:7999/pigeon/admin/commit/7e4ea3cae74e7fadb06f615f4e3ec1ef9ee88b8c))
