
exports.up = function(knex, Promise) {
  return Promise.all([
    knex.schema.createTable('admin_roles', function(t) {
      t.increments('id').unsigned().primary();
      t.timestamps(false, true);
      t.string('name').notNull();
    }).then(function() {
      return knex('admin_roles').insert([
        { 'name': 'Admin' },
        { 'name': 'Workflow Manager' },
        { 'name': 'Campaign Manager' },
      ]);
    }),
  ]);
};

exports.down = function(knex, Promise) {
  return knex.schema.dropTable('admin_roles');
};
