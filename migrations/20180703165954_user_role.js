
exports.up = function(knex, Promise) {
  return knex.schema.table('admin_users', function(table) {
    table.integer('role_id').unsigned().notNullable();
    table.foreign('role_id').references('id').inTable('admin_roles');
  });
};

exports.down = function(knex, Promise) {
  return knex.schema.table('admin_users', function(table) {
    table.dropForeign('role_id');
    table.dropColumn('role_id');
  });
};
