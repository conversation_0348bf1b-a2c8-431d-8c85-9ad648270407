const permissionsFromJSON = require('../src/permissions/permissions.json').permissions;

exports.up = function(knex, Promise) {
  return Promise.all([
    knex.schema.createTable('admin_permissions', function(t) {
      t.increments('id').unsigned().primary();
      t.string('name', 255);
      t.bit('admin');
      t.bit('workflow_manager');
      t.bit('campaign_manager');
      t.bit('notification_manager');
    }).then(function() {
      // strip id
      return knex('admin_permissions').insert(permissionsFromJSON.map(({ id, ...rest }) => rest));
    }),
  ]);
};

exports.down = function(knex, Promise) {
  return knex.schema.dropTable('admin_permissions');
};
