
exports.up = function(knex, Promise) {
  return knex.schema.createTable('admin_users', function(t) {
    t.increments('id').unsigned().primary();
    t.timestamps(false, true);
    t.string('name').notNull();
    t.string('email').notNull();
    t.string('sid').notNull();
    t.string('token').nullable();
  });
};

exports.down = function(knex, Promise) {
  return knex.schema.dropTable('admin_users');
};
