const { HttpError } = require('pigeon-pigeon-pack').errors;
const {
  CAMPAIGN_STATUS,
  processError,
  ruleV2toV1Adapter,
  ruleV1toV2Adaptor,
} = require('./common');

jest.mock('pigeon-pigeon-pack', () => ({
  errors: {
    HttpError: class HttpError extends Error {
      constructor(message, status = 500) {
        super(message);
        this.status = status;
        this.name = 'HttpError';
      }
    },
  },
}));

describe('v2/vignette/common', () => {
  describe('CAMPAIGN_STATUS', () => {
    it('should export correct campaign status constants', () => {
      expect(CAMPAIGN_STATUS).toEqual({
        CAMPAIGN_STATUS_SUBMITTED: 'submitted',
        CAMPAIGN_STATUS_DRAFT: 'draft',
        CAMPAIGN_STATUS_PUBLISHED: 'reviewed',
        CAMPAIGN_STATUS_REVIEWED: 'published',
      });
    });
  });

  describe('processError', () => {
    it('should return HttpError instance as is', () => {
      const httpError = new HttpError('Test error', 404);
      const result = processError(httpError);
      expect(result).toBe(httpError);
    });

    it('should wrap Error instance in HttpError', () => {
      const error = new Error('Test error');
      const result = processError(error);
      expect(result).toBeInstanceOf(HttpError);
      expect(result.message).toBe('Error: Test error');
    });

    it('should handle error with response object', () => {
      const error = {
        response: {
          data: { message: 'API error' },
          status: 400,
        },
      };
      const result = processError(error);
      expect(result).toBeInstanceOf(HttpError);
      expect(result.message).toBe(JSON.stringify({ message: 'API error' }));
      expect(result.status).toBe(400);
    });

    it('should default to 500 status when response status is missing', () => {
      const error = {
        response: {
          data: { message: 'API error' },
        },
      };
      const result = processError(error);
      expect(result.status).toBe(500);
    });

    it('should handle string error', () => {
      const error = 'String error';
      const result = processError(error);
      expect(result).toBeInstanceOf(HttpError);
      expect(result.message).toBe('String error');
    });

    it('should handle object error without response', () => {
      const error = { custom: 'error' };
      const result = processError(error);
      expect(result).toBeInstanceOf(HttpError);
      expect(result.message).toBe('[object Object]');
    });
  });

  describe('ruleV2toV1Adapter', () => {
    it('should convert rule from V2 to V1 format', () => {
      const ruleV2 = {
        id: '123',
        name: 'Test Rule',
        contents: [
          {
            page: 'page1',
            container: 'container1',
            content_space: 'space1',
            content_type: 'type1',
            content_id: 'id1',
          },
          {
            page: 'page2',
            container: 'container1',
            content_space: 'space1',
            content_type: 'type1',
            content_id: 'id1',
          },
        ],
      };

      const result = ruleV2toV1Adapter(ruleV2);

      expect(result).toEqual({
        id: '123',
        name: 'Test Rule',
        pages: [ 'page1', 'page2' ],
        container: 'container1',
        content_space: 'space1',
        content_type: 'type1',
        content_id: 'id1',
      });
      expect(result.contents).toBeUndefined();
    });

    it('should handle rule without contents property', () => {
      const ruleV2 = {
        id: '123',
        name: 'Test Rule',
      };

      const result = ruleV2toV1Adapter(ruleV2);

      expect(result).toEqual({
        id: '123',
        name: 'Test Rule',
        pages: [ undefined ],
        container: undefined,
        content_space: undefined,
        content_type: undefined,
        content_id: undefined,
      });
    });
  });

  describe('ruleV1toV2Adaptor', () => {
    it('should convert rule from V1 to V2 format', () => {
      const ruleV1 = {
        id: '123',
        name: 'Test Rule',
        pages: [ 'page1', 'page2' ],
        container: 'container1',
        content_space: 'space1',
        content_type: 'type1',
        content_id: 'id1',
      };

      const result = ruleV1toV2Adaptor(ruleV1);

      expect(result).toEqual({
        id: '123',
        name: 'Test Rule',
        contents: [
          {
            page: 'page1',
            container: 'container1',
            content_space: 'space1',
            content_type: 'type1',
            content_id: 'id1',
          },
          {
            page: 'page2',
            container: 'container1',
            content_space: 'space1',
            content_type: 'type1',
            content_id: 'id1',
          },
        ],
      });
      expect(result.pages).toBeUndefined();
      expect(result.container).toBeUndefined();
      expect(result.content_space).toBeUndefined();
      expect(result.content_type).toBeUndefined();
      expect(result.content_id).toBeUndefined();
    });

    it('should handle rule without pages property', () => {
      const ruleV1 = {
        id: '123',
        name: 'Test Rule',
        container: 'container1',
        content_space: 'space1',
        content_type: 'type1',
        content_id: 'id1',
      };

      const result = ruleV1toV2Adaptor(ruleV1);

      expect(result).toEqual({
        id: '123',
        name: 'Test Rule',
        contents: [],
      });
    });

    it('should handle empty pages array', () => {
      const ruleV1 = {
        id: '123',
        name: 'Test Rule',
        pages: [],
        container: 'container1',
        content_space: 'space1',
        content_type: 'type1',
        content_id: 'id1',
      };

      const result = ruleV1toV2Adaptor(ruleV1);

      expect(result).toEqual({
        id: '123',
        name: 'Test Rule',
        contents: [],
      });
    });

    it('should handle rule with partial V1 properties', () => {
      const ruleV1 = {
        id: '123',
        name: 'Test Rule',
        pages: [ 'page1' ],
        container: 'container1',
      };

      const result = ruleV1toV2Adaptor(ruleV1);

      expect(result).toEqual({
        id: '123',
        name: 'Test Rule',
        contents: [
          {
            page: 'page1',
            container: 'container1',
            content_space: undefined,
            content_type: undefined,
            content_id: undefined,
          },
        ],
      });
    });
  });
});
