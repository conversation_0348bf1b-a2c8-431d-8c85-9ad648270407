const { validate, schema } = require('./validation');
const R = require('ramda');
const { ERROR_HANDLER, ForbiddenError } = require('../../error');
const { canUpdateCampaign } = require('../../permissions/workflow');

const updateCampaign = ({ logger, ruleApi }) => async(req, res, next) => {
  try {
    const { ruleId } = validate(req.params, schema.update.path);
    const campaignBefore = await ruleApi.get(ruleId);
    const updatedCampaign = validate(req.body, schema.update.body);
    const allow = canUpdateCampaign(res.locals.user, campaignBefore.data, updatedCampaign);
    if (!allow) {
      return next(new ForbiddenError());
    }
    const sid = R.pathOr(null, [ 'locals', 'user', 'sid' ], res);
    updatedCampaign.updated_by = sid;
    const campaign = (await ruleApi.update(ruleId, updatedCampaign)).data;
    res.status(200).json(campaign);
  } catch (error) {
    logger.error(error);
    ERROR_HANDLER('Error while updating SOL campaign.', error, next);
  }
};

module.exports = updateCampaign;
