// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`GET /api/v2/campaign/ should match snapshot if top level values are not present 1`] = `
"<?xml version="1.0" encoding="UTF-8"?>
<ContentMetaData xsi:schemaLocation="http://xsd.bns.com/msgs/contentdelivery/v001 ContentDelivery.xsd" xmlns="http://xsd.bns.com/msgs/contentdelivery/v001" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:content="http://xsd.bns.com/types/contentdelivery/v001">
  <content:ruleName>string</content:ruleName>
  <content:startDate>2018-12-31T19:00:00</content:startDate>
  <content:expiryDate>2019-12-31T18:59:59</content:expiryDate>
</ContentMetaData>"
`;

exports[`GET /api/v2/campaign/ should return 200 with valid req 1`] = `
"<?xml version="1.0" encoding="UTF-8"?>
<ContentMetaData xsi:schemaLocation="http://xsd.bns.com/msgs/contentdelivery/v001 ContentDelivery.xsd" xmlns="http://xsd.bns.com/msgs/contentdelivery/v001" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:content="http://xsd.bns.com/types/contentdelivery/v001">
  <content:ruleId>1010101</content:ruleId>
  <content:ruleName>string</content:ruleName>
  <content:contentType>N</content:contentType>
  <content:startDate>2018-12-31T19:00:00</content:startDate>
  <content:expiryDate>2019-12-31T18:59:59</content:expiryDate>
  <content:webFragmentId>/storefront/you/are/richer/than/you/think.html</content:webFragmentId>
  <content:contentType>M</content:contentType>
  <content:containerId>offers-and-programs</content:containerId>
  <content:pageList>
    <content:pageId>accounts</content:pageId>
    <content:pageId>activities</content:pageId>
  </content:pageList>
  <content:language>en</content:language>
  <content:country>CA</content:country>
  <content:campaignId>GIC11</content:campaignId>
  <content:contentRule>
    <content:targetMode>LENIENT</content:targetMode>
    <content:ruleType>BU</content:ruleType>
    <content:ruleAttributes>
      <content:domainValue>HW:</content:domainValue>
      <content:domainValue>CASL:</content:domainValue>
    </content:ruleAttributes>
  </content:contentRule>
  <content:contentRule>
    <content:targetMode>STRICT</content:targetMode>
    <content:ruleType>PR</content:ruleType>
    <content:ruleAttributes>
      <content:domainValue>Banking:Savings:SAV:SAV:NI</content:domainValue>
      <content:domainValue>Banking:Savings:SAV:SAV:BP</content:domainValue>
    </content:ruleAttributes>
  </content:contentRule>
  <content:contentRule>
    <content:targetMode>EXCLUSIVE</content:targetMode>
    <content:ruleType>BU</content:ruleType>
    <content:ruleAttributes>
      <content:domainValue>SDBI:SL</content:domainValue>
      <content:domainValue>SMI:</content:domainValue>
    </content:ruleAttributes>
  </content:contentRule>
</ContentMetaData>"
`;

exports[`GET /api/v2/campaign/ test when products are not there 1`] = `
"<?xml version="1.0" encoding="UTF-8"?>
<ContentMetaData xsi:schemaLocation="http://xsd.bns.com/msgs/contentdelivery/v001 ContentDelivery.xsd" xmlns="http://xsd.bns.com/msgs/contentdelivery/v001" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:content="http://xsd.bns.com/types/contentdelivery/v001">
  <content:ruleId>1010101</content:ruleId>
  <content:ruleName>string</content:ruleName>
  <content:contentType>N</content:contentType>
  <content:startDate>2018-12-31T19:00:00</content:startDate>
  <content:expiryDate>2019-12-31T18:59:59</content:expiryDate>
  <content:webFragmentId>/storefront/you/are/richer/than/you/think.html</content:webFragmentId>
  <content:contentType>M</content:contentType>
  <content:containerId>offers-and-programs</content:containerId>
  <content:pageList>
    <content:pageId>accounts</content:pageId>
    <content:pageId>activities</content:pageId>
  </content:pageList>
  <content:language>en</content:language>
  <content:country>CA</content:country>
  <content:campaignId>GIC11</content:campaignId>
</ContentMetaData>"
`;
