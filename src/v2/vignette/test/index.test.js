const campaignIndex = require('../');

const serviceMock = {};
describe('/campaign route', () => {
  test('should return valid routes', () => {
    const routes = [
      '/rules/:ruleId/xml',
      '/rules/:ruleId',
      '/rules',
      '/rules',
      '/rules/:ruleId',
      '/rules/:ruleId',
    ];
    const router = campaignIndex(serviceMock);
    routes.forEach((route, index) => expect(router.stack[index].route.path).toBe(route));
  });
});
