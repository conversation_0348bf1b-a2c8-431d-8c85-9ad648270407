const UpdateCampaign = require('../updateCampaign');
const { BadRequestError, InternalServerError } = require('../../../error');
const mockData = require('./mockData');

// mock Campaign API client
const mockRuleApi = {
  update: jest.fn(),
  get: jest.fn(),
};
// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();
const mockRes = {
  status: mockStatus,
  json: mockJson,
  locals: {
    auth: { sid: 's1234567' },
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
    },
  },
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

const mockUserAssignmentService = {
  validateUsers: jest.fn(),
  assignUserToCampaign: jest.fn(),
  assignUserListToCampaign: jest.fn(),
  removeUsersFromCampaign: jest.fn(),
};

const mockServices = { logger: { error: jest.fn() }, ruleApi: mockRuleApi, userAssignmentService: mockUserAssignmentService };

describe('Campaign Rules: PATCH /rules/:ruleId', () => {
  beforeEach(() => {
    mockRuleApi.get.mockReset();
    mockRuleApi.update.mockReset();
    mockNext.mockReset();
    mockJson.mockReset();
    mockStatus.mockReset();
    mockLogger.info.mockReset();
    mockUserAssignmentService.validateUsers.mockReset();
    mockUserAssignmentService.assignUserToCampaign.mockReset();
    mockUserAssignmentService.assignUserListToCampaign.mockReset();
    mockUserAssignmentService.removeUsersFromCampaign.mockReset();

    mockStatus.mockReturnValue({ json: mockJson });
  });

  test('should not pass validation on invalid params', async() => {
    const updatePartialCampaign = UpdateCampaign(mockServices);
    const mockParams = { ruleId: '123456#ab' };
    const mockBody = { };
    await updatePartialCampaign({ params: mockParams, body: mockBody }, mockRes, mockNext);
    expect(mockRuleApi.update).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0] instanceof BadRequestError).toEqual(true);
  });

  test('should not pass validation on empty request body', async() => {
    const mockParams = { ruleId: 123 };

    const updatePartialCampaign = UpdateCampaign(mockServices);
    await updatePartialCampaign({ params: mockParams, body: {} }, mockRes, mockNext);

    expect(mockRuleApi.update).not.toBeCalled();
    expect(mockRuleApi.get).toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockStatus.mock.calls.length).toEqual(0);
  });

  test('should not pass validation on invalid body', async() => {
    const mockParams = { ruleId: 123 };
    const mockBody = { name: 123 };
    const updatePartialCampaign = UpdateCampaign(mockServices);
    await updatePartialCampaign({ params: mockParams, body: mockBody }, mockRes, mockNext);
    expect(mockRuleApi.update).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0] instanceof BadRequestError).toEqual(true);
  });

  test('should not return an error on valid request', async() => {
    const mockParams = { ruleId: 123 };
    const mockBody = mockData.body;
    const mockResponse = {
      data: { id: mockParams.ruleId, ...mockBody },
    };

    const mockCampaignBeforeResponse = {
      status: 200,
      data: mockData.campaignOld,
    };

    mockRuleApi.get.mockResolvedValueOnce(mockCampaignBeforeResponse);
    mockRuleApi.update.mockResolvedValueOnce(mockResponse);
    mockUserAssignmentService.validateUsers.mockResolvedValueOnce({ success: true });
    mockUserAssignmentService.removeUsersFromCampaign.mockResolvedValueOnce({});

    const updatePartialCampaign = UpdateCampaign(mockServices);

    await updatePartialCampaign({ params: mockParams, body: mockBody }, mockRes, mockNext);
    expect(mockRuleApi.update).toBeCalled();
    expect(mockRuleApi.update).toBeCalledTimes(1);
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('should return error when campaignAPI client unavailable', async() => {
    const mockParams = { ruleId: 123 };
    const mockBody = { name: '12345' };
    const mockResponse = {
      response: {
        status: 400,
        data: {
          code: 'HTTP_BAD_REQUEST',
          message: 'Database error',
          uuid: '04d69d4d-4c41-4373-b7f5-9b351a1f345d',
          timestamp: '2018-08-28T20:43:59.319Z',
          metadata: [ 'The duplicate key value is (sample name #38).' ],
        },
      },
    };
    const mockCampaignBefore = { data: mockData.campaignOld };

    mockRuleApi.get.mockResolvedValueOnce(mockCampaignBefore);
    mockRuleApi.update.mockRejectedValueOnce(mockResponse);
    const updatePartialCampaign = UpdateCampaign(mockServices);

    const mockResLocal = Object.assign({}, mockRes);
    mockResLocal.status = jest.fn();

    await updatePartialCampaign({ params: mockParams, body: mockBody }, mockResLocal, mockNext);

    expect(mockRuleApi.update).toBeCalled();
    expect(mockRuleApi.update).toHaveBeenCalledTimes(1);
    expect(mockStatus).toHaveBeenCalledTimes(0);
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0] instanceof InternalServerError).toBe(true);
  });

  test('should return error if not allowed to update', async() => {
    const mockParams = { ruleId: 123 };
    const mockBody = { ...mockData.body };

    const mockResponse = {
      data: { id: mockParams.ruleId, ...mockBody },
    };

    const mockCampaignBeforeResponse = {
      status: 200,
      data: mockData.campaignOld,
    };

    const localMockRes = {
      ...mockRes,
      locals: {
        ...mockRes.locals,
        user: {
          ...mockRes.locals.user,
          permissions: [ '' ],
        },
      },
    };

    mockRuleApi.get.mockResolvedValueOnce(mockCampaignBeforeResponse);
    mockRuleApi.update.mockResolvedValueOnce(mockResponse);
    mockUserAssignmentService.validateUsers.mockResolvedValueOnce({ success: true });
    mockUserAssignmentService.removeUsersFromCampaign.mockResolvedValueOnce({});

    const updatePartialCampaign = UpdateCampaign(mockServices);

    await updatePartialCampaign({ params: mockParams, body: mockBody }, localMockRes, mockNext);

    expect(mockNext).toBeCalled();
  });
});
