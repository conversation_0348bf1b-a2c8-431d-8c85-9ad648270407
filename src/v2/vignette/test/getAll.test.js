const getAllCampaigns = require('../getAll');
const mockUserService = require('__mocks__/getUsers');
const { HttpError } = require('pigeon-pigeon-pack').errors;
const mockData = require('./mockData');

const mockService = {
  getAllByAccess: jest.fn().mockReturnValue({
    data: {
      items: [],
    },
  }),
};

const jsonResp = {
  json: jest.fn(),
};

const resMock = {
  status: jest.fn().mockReturnValue(jsonResp),
  locals: {
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'campaigns_view', 'campaigns_manage' ],
      access: {
        campaigns: {
          containers: {
            sol: {
              view: [ 'offers-and-programs' ],
            },
          },
          pages: {
            sol: {
              view: [ 'accounts' ],
            },
          },
          ruleSubTypes: {
            sol: {
              view: [ 'targeted', 'mass' ],
            },
          },
        },
      },
    },
  },
};

const reqMock = {
  query: {
    type: 'vignette',
  },
};

const nextMock = jest.fn();

const mockServices = { ruleApi: mockService, userService: mockUserService };

describe('GET /api/v2/campaign-rules - get a list of campaigns', () => {
  beforeEach(() => {
    resMock.status.mockClear();
    nextMock.mockClear();
    mockService.getAllByAccess.mockClear();
  });

  test('should return 200 with valid req', async() => {
    const asyncGetAll = getAllCampaigns(mockServices);
    await asyncGetAll(reqMock, resMock, nextMock);

    expect(mockService.getAllByAccess).toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('should return no rules if user doesnt have any container/page/sub type access', async() => {
    const asyncGetAll = getAllCampaigns(mockServices);
    const customResMock = {
      status: jest.fn().mockReturnValue(jsonResp),
      locals: {
        user: {
          id: 1,
          role_id: 1,
          permissions: [ 'campaigns_view', 'campaigns_manage' ],
          access: {
            campaigns: {
              containers: {},
              pages: {},
              ruleSubTypes: {},
            },
          },
        },
      },
    };
    await asyncGetAll(reqMock, customResMock, nextMock);

    expect(mockService.getAllByAccess).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(customResMock.status).toHaveBeenCalledWith(200);
  });

  test('should return 400 with invalid request query string', async() => {
    const asyncGetAll = getAllCampaigns(mockServices);

    reqMock.query = { platform_name: 123 };

    await asyncGetAll(reqMock, resMock, nextMock);

    expect(nextMock).toHaveBeenCalled();
    expect(nextMock.mock.calls[0][0] instanceof HttpError).toBe(true);
  });

  test('should strip unkown query strings and return 200', async() => {
    const asyncGetAll = getAllCampaigns(mockServices);
    reqMock.query = {
      'unknownQueryParam': 1234,
    };

    await asyncGetAll(reqMock, resMock, nextMock);

    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(mockService.getAllByAccess).toHaveBeenCalled();
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('Should replace created by and updated by campaign information with successful request', async() => {
    const campaignData = {
      'data': {
        'total': 240,
        'offset': 0,
        'limit': 10,
        'items': [
          {
            ...mockData.campaignRuleV2,
            name: 'some different name',
            created_by: '123',
            updated_by: '123',
          },
          mockData.campaignRuleV2,
        ],
      },
    };

    mockService.getAllByAccess.mockResolvedValue(campaignData);
    mockUserService.getUser.mockResolvedValue([ { name: 'Test Name', sid: '123' } ]);

    const asyncGetAll = getAllCampaigns(mockServices);
    await asyncGetAll(reqMock, resMock, nextMock);

    expect(nextMock).not.toHaveBeenCalled();
    expect(mockService.getAllByAccess).toHaveBeenCalled();
    expect(resMock.status).toHaveBeenCalled();
    expect(jsonResp.json).toHaveBeenCalled();

    const responseData = jsonResp.json.mock.calls[0][0];

    expect(responseData.items[0].created_by === 'Test Name').toBe(true);
  });
});
