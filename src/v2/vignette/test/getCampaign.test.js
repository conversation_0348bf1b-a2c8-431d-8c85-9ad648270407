const getCampaign = require('../getCampaign');
const mockData = require('./mockData');
const { HttpError } = require('pigeon-pigeon-pack').errors;

const { MockError } = require('__mocks__/MockError');
const mockService = { get: jest.fn().mockResolvedValue({ data: {} }) };

const jsonResp = {
  json: jest.fn(),
};

const resMock = {
  status: jest.fn().mockReturnValue(jsonResp),
  locals: {
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
      access: {
        campaigns: {
          containers: {
            sol: {
              view: [ 'offers-and-programs' ],
            },
          },
          pages: {
            sol: {
              view: [ 'accounts' ],
            },
          },
          ruleSubTypes: {
            sol: {
              view: [ 'targeted', 'mass' ],
            },
          },
        },
      },
    },
  },
};

const reqMock = {
  params: {
    ruleId: 123,
  },
};

const nextMock = jest.fn();

const ruleServiceMock = {
  getAssigneesForCampaign: jest.fn().mockResolvedValue([ { full_name: 'test', sid: '123' } ]),
};

const mockServices = { ruleApi: mockService, userAssignmentService: ruleServiceMock };

describe('GET /api/v1/campaigns - get a campaign by id', () => {
  beforeEach(() => {
    nextMock.mockClear();
    jsonResp.json.mockClear();
    ruleServiceMock.getAssigneesForCampaign.mockClear();
  });

  test('should return 200 with valid req', async() => {
    mockService.get.mockResolvedValue({ data: mockData.campaignRuleV2 });
    const asyncGetCampaign = getCampaign(mockServices);
    await asyncGetCampaign(reqMock, resMock, nextMock);

    expect(mockService.get).toHaveBeenCalledTimes(1);
    expect(nextMock).not.toHaveBeenCalled();
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('should return 200 with valid req no assignees', async() => {
    ruleServiceMock.getAssigneesForCampaign.mockResolvedValue([]);
    mockService.get.mockResolvedValue({ data: mockData.campaignRuleV2 });
    const asyncGetCampaign = getCampaign(mockServices);
    await asyncGetCampaign(reqMock, resMock, nextMock);

    expect(mockService.get).toHaveBeenCalledTimes(1);
    expect(nextMock).not.toHaveBeenCalled();
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('should return BadRequest with invalid or missing url parameters', async() => {
    const asyncGetCampaign = getCampaign(mockServices);
    const badReqMock = { params: {} };

    await asyncGetCampaign(badReqMock, resMock, nextMock);

    expect(nextMock).toHaveBeenCalled();
    expect(nextMock.mock.calls[0][0] instanceof HttpError).toBe(true);
    expect(mockService.get).not.toHaveBeenCalled();
    expect(resMock.status).not.toHaveBeenCalled();
  });

  test('should return CustomError if unhandled exception occurs with service', async() => {
    const badMockService = {
      get: jest.fn().mockRejectedValueOnce(new Error()),
    };
    mockServices.ruleApi = badMockService;

    const asyncGetCampaign = getCampaign(mockServices);
    await asyncGetCampaign(reqMock, resMock, nextMock);

    expect(badMockService.get).toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
    expect(nextMock.mock.calls[0][0] instanceof HttpError).toBe(true);
  });

  test('should handle 5XX response', async() => {
    const errResponse = {
      status: 500,
      data: {},
    };

    const errMockService = {
      get: jest.fn().mockRejectedValueOnce(new MockError(500, '', errResponse)),
    };
    mockServices.ruleApi = errMockService;

    const asyncGetCampaign = getCampaign(mockServices);
    await asyncGetCampaign(reqMock, resMock, nextMock);

    expect(errMockService.get).toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
    expect(nextMock.mock.calls[0][0] instanceof HttpError).toBe(true);
  });

  test('should handle 3xx-4xx response', async() => {
    const errResponse = {
      status: 400,
      data: {},
    };

    const errMockService = {
      get: jest.fn().mockRejectedValueOnce(new MockError(400, '', errResponse)),
    };
    mockServices.ruleApi = errMockService;

    const asyncGetCampaign = getCampaign(mockServices);
    await asyncGetCampaign(reqMock, resMock, nextMock);

    expect(errMockService.get).toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
    expect(nextMock.mock.calls[0][0] instanceof HttpError).toBe(true);
  });
});
