module.exports = {
  body: {
    name: '12345',
    start_date: '2018-01-01T00:00:00Z',
    end_date: '2018-12-31T23:59:59Z',
    contents: [ {
      page: 'activities',
      container: 'my-activity',
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
    } ],
    assignees: [ 's1234567' ],
    status: 'submitted',
  },
  campaignOld: {
    assignees: [ 's1234567' ],
    id: 'ZgFK7zPnEGr9',
    name: 'dpalma-may-37-3554',
    start_date: '2019-04-29T04:00:00.000Z',
    end_date: '2019-04-30T04:00:00.000Z',
    external_ref: 'asdfc1234',
    platforms: [ 'ios', 'android' ],
    app_version: null,
    urgent: false,
    status: 'submitted',
    disabled: false,
    created_by: 's7435292',
    updated_by: 's7435292',
    created_at: '2019-06-03T19:50:51.250Z',
    updated_at: '2019-06-03T19:50:51.250Z',
    contents: [ {
      page: 'activities',
      container: 'my-activity',
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
    } ],
  },
  campaignRuleV2: {
    'id': '1010101',
    'rule_id': 'abcdefg',
    'name': 'string',
    'type': 'campaign',
    'status': 'published',
    'start_date': '2019-01-01T00:00:00.000Z',
    'end_date': '2019-12-31T23:59:59.999Z',
    'urgent': true,
    'disabled': false,
    'created_by': 's1234567',
    'updated_by': 's7654321',
    'message_type': 'N',
    'system_message': 'ADPIM_DOWNLOAD_FAILURE',
    'contents': [
      {
        'page': 'accounts',
        'container': 'offers-and-programs',
        'content_space': 'abcde12345',
        'content_type': 'targetedCampaign',
        'content_id': '24VKTicX2VqbTABpzozvhL',
        'content_disabled': false,
        'subject': 'string',
      },
      {
        'page': 'activities',
        'container': 'offers-and-programs',
        'content_space': 'abcde12345',
        'content_type': 'targetedCampaign',
        'content_id': '24VKTicX2VqbTABpzozvhL',
        'content_disabled': false,
        'subject': 'string',
      },
    ],
    'targeting': {
      'campaign_name': 'GIC11',
      'language_code': 'en',
      'country_code': 'CA',
      'platforms': [
        {
          'platform_name': 'ios',
          'platform_version': '20.3.0',
        },
      ],
      'products': [
        {
          'attribute_mode': 'or',
          'attribute_type': 'BU',
          'attribute_values': [
            'HW:',
            'CASL:',
          ],
        },
        {
          'attribute_mode': 'and',
          'attribute_type': 'PR',
          'attribute_values': [
            'Banking:Savings:SAV:SAV:NI',
            'Banking:Savings:SAV:SAV:BP',
          ],
        },
        {
          'attribute_mode': 'not',
          'attribute_type': 'BU',
          'attribute_values': [
            'SDBI:SL',
            'SMI:',
          ],
        },
      ],
      'mass': [
        {
          'attribute_mode': 'and',
          'attribute_type': 'BU',
          'attribute_values': [
            'Banking:PrepaidVisa:VPP:VPP:SC',
            'Borrowing:Visa:VIS:VCL',
          ],
        },
      ],
    },
  },
};
