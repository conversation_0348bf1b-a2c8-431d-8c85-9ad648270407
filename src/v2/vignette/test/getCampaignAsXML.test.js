const moment = require('moment-timezone');
const { HttpError } = require('pigeon-pigeon-pack').errors;

const getCampaignAsXML = require('../getCampaignAsXML');
const { xmlTimeFormat } = require('../../../constants/storefront');
const mockData = require('./mockData');
const { MockError } = require('__mocks__/MockError');

const jsonResp = {
  json: jest.fn(),
};

const resMock = {
  status: jest.fn().mockReturnValue(jsonResp),
  header: jest.fn(),
  locals: {
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
    },
  },
  send: jest.fn(),
};

const reqMock = {
  params: {
    ruleId: 123,
  },
};

const nextMock = jest.fn();

const mockServices = {
  ruleApi: {
    get: jest.fn(),
  },
  contentApi: {
    getContentDetailsVignette: jest.fn().mockResolvedValue({
      fragment_file_path: '/you/are/richer/than/you',
      fragment_file_name: 'think.html',
    }),
  },
};

describe('GET /api/v2/campaign/', () => {
  let copiedMockData;
  beforeEach(() => { copiedMockData = Object.assign({}, mockData.campaignRuleV2); });

  const getCampaignAsXMLRes = async(takeSnapshot = true) => {
    resMock.send.mockClear();
    await getCampaignAsXML(mockServices)(reqMock, resMock, nextMock);

    expect(mockServices.ruleApi.get).toHaveBeenCalledTimes(1);
    expect(resMock.status).toHaveBeenCalledWith(200);
    const resResult = resMock.send.mock.calls[0][0];

    if (takeSnapshot) {
      expect(resResult).toMatchSnapshot();
    }

    return resResult;
  };

  test('should return 200 with valid req', async() => {
    mockServices.ruleApi.get.mockResolvedValue({ data: copiedMockData });
    const res = await getCampaignAsXMLRes();
    expect(res).toContain('content:ruleId');
  });

  test('should match snapshot if top level values are not present', async() => {
    delete copiedMockData.id;
    delete copiedMockData.message_type;
    delete copiedMockData.contents;
    delete copiedMockData.targeting;
    mockServices.ruleApi.get.mockResolvedValue({ data: copiedMockData });
    const res = await getCampaignAsXMLRes();
    expect(res).not.toContain('content:ruleId');
  });

  test('test when products are not there', async() => {
    delete copiedMockData.targeting.products;
    mockServices.ruleApi.get.mockResolvedValue({ data: copiedMockData });
    const res = await getCampaignAsXMLRes();
    expect(res).not.toContain('content:contentRule');
  });

  test('start/end times are converted to EST', async() => {
    const res = await getCampaignAsXMLRes(false);
    const startTimeEST = moment.tz(copiedMockData.start_date, 'America/Toronto').format(xmlTimeFormat);
    const endTimeEST = moment.tz(copiedMockData.end_date, 'America/Toronto').format(xmlTimeFormat);
    expect(res.includes(startTimeEST)).toBe(true);
    expect(res.includes(endTimeEST)).toBe(true);
  });

  test('should handle 5XX response', async() => {
    mockServices.ruleApi.get.mockRejectedValueOnce(new MockError(500));

    await getCampaignAsXML(mockServices)(reqMock, resMock, nextMock);

    expect(nextMock).toHaveBeenCalled();
    expect(nextMock.mock.calls[0][0] instanceof HttpError).toBe(true);
  });
});
