const CreateCampaign = require('../createCampaign');
const { CustomError, BadRequestError } = require('../../../error');
const mockData = require('./mockData');

const mockRuleApi = {
  create: jest.fn(),
};

const mockRuleService = {
  validateUsers: jest.fn().mockResolvedValue({ success: true }),
  assignUserToCampaign: jest.fn(),
  assignUserListToCampaign: jest.fn(),
};

const mockUserService = {
  getUser: jest.fn().mockReturnValue([ 's000001' ]),
};
// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();

const mockRes = {
  status: mockStatus,
  json: mockJson,
  locals: {
    auth: { sid: 's1234567' },
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
    },
  },
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

const mockServices = { logger: mockLogger, userAssignmentService: mockRuleService, userService: mockUserService, ruleApi: mockRuleApi };

describe('Campaign Rules: routes > createCampaign', () => {
  beforeEach(() => {
    mockRuleApi.create.mockReset();
    mockNext.mockReset();
    mockJson.mockReset();
    mockStatus.mockReset();
    mockLogger.error.mockReset();
    mockLogger.info.mockReset();
    mockRuleService.validateUsers.mockReset();
    mockRuleService.assignUserToCampaign.mockReset();
    mockRuleService.assignUserListToCampaign.mockReset();
  });

  test('should not pass validation on empty request body', async() => {
    const createCampaign = await CreateCampaign(mockServices);
    createCampaign({ body: {} }, mockRes, mockNext);
    expect(mockRuleApi.create).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0] instanceof BadRequestError).toEqual(true);
  });

  test('should support bad request response from Campaign API client', async() => {
    const mockReq = { body: mockData.body };
    const fakeResponse = {
      response: {
        status: 400,
        data: {
          code: 'HTTP_BAD_REQUEST',
          message: 'Database error',
          uuid: '04d69d4d-4c41-4373-b7f5-9b351a1f345d',
          timestamp: '2018-08-28T20:43:59.319Z',
          metadata: [ 'The duplicate key value is (sample name #38).' ],
        },
      },
    };
    mockRuleService.validateUsers.mockResolvedValue({ success: true });
    mockRuleApi.create.mockRejectedValueOnce(fakeResponse);
    const createCampaign = CreateCampaign(mockServices);
    await createCampaign(mockReq, mockRes, mockNext);
    expect(mockRuleApi.create).toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toEqual(true);
  });

  test('should call next with Error on 500+ response status', async() => {
    const mockReq = { body: mockData.body };
    const fakeResponse = {
      response: {
        status: 500,
        data: {
          code: 'HTTP_INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
          uuid: '04d69d4d-4c41-4373-b7f5-9b351a1f345d',
          timestamp: '2018-08-28T20:43:59.319Z',
          metadata: [],
        },
      },
    };
    mockRuleService.validateUsers.mockResolvedValue({ success: true });
    mockRuleApi.create.mockRejectedValueOnce(fakeResponse);
    const createCampaign = CreateCampaign(mockServices);
    await createCampaign(mockReq, mockRes, mockNext);
    expect(mockRuleApi.create).toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls.length).toEqual(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });

  test('Should return valid response for valid campaign data - draft campaign', async() => {
    const mockReq = { body: mockData.body };

    mockRuleService.validateUsers.mockResolvedValue({ success: true });
    mockRuleService.assignUserToCampaign.mockResolvedValue({});
    mockRuleApi.create.mockResolvedValue({ data: mockData.body });
    mockStatus.mockReturnValue({ json: mockJson });

    const createCampaign = CreateCampaign(mockServices);
    await createCampaign(mockReq, mockRes, mockNext);

    expect(mockRuleApi.create).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('Should return valid response for valid campaign data - submitted campaign', async() => {
    const mockReq = { body: mockData.body };

    mockRuleService.validateUsers.mockResolvedValue({ success: true });
    mockRuleService.assignUserToCampaign.mockResolvedValue({});
    mockRuleApi.create.mockResolvedValue({ data: mockData.body });
    mockStatus.mockReturnValue({ json: mockJson });

    const createCampaign = CreateCampaign(mockServices);
    await createCampaign(mockReq, mockRes, mockNext);

    expect(mockRuleApi.create).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });
});
