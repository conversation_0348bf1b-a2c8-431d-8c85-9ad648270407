const DeleteCampaign = require('../deleteCampaign');
const { HttpError } = require('pigeon-pigeon-pack').errors;

// mock Campaign API client
const mockRuleApi = {
  delete: jest.fn().mockResolvedValue({ id: 123456 }),
};
// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();
const mockRes = {
  status: mockStatus,
  locals: {
    auth: { sid: 's1234567' },
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
    },
  },
};

const mockLogger = { error: jest.fn() };

const mockServices = { logger: mockLogger, ruleApi: mockRuleApi };

describe('Campaign Rules: DELETE /campaign-rules/:ruleId', () => {
  beforeEach(() => {
    mockRuleApi.delete.mockClear();
    mockNext.mockClear();
    mockStatus.mockClear();
    mockLogger.error.mockClear();
  });
  test('should not pass validation on invalid params', async() => {
    const deleteCampaign = DeleteCampaign(mockServices);
    await deleteCampaign({ params: { ruleId: '123456#ab' } }, mockRes, mockNext);
    expect(mockRuleApi.delete).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0] instanceof HttpError).toEqual(true);
  });
  test('should return an error on error response from campaign api client', async() => {
    const mockParams = { ruleId: 123 };
    const mockResponse = {
      response: {
        status: 400,
        data: {
          code: 'HTTP_BAD_REQUEST',
          message: 'Database error',
          uuid: '04d69d4d-4c41-4373-b7f5-9b351a1f345d',
          timestamp: '2018-08-28T20:43:59.319Z',
          metadata: [ 'The duplicate key value is (sample name #38).' ],
        },
      },
    };
    mockRuleApi.delete.mockRejectedValueOnce(mockResponse);
    const deleteCampaign = DeleteCampaign(mockServices);
    await deleteCampaign({ params: mockParams }, mockRes, mockNext);
    expect(mockRuleApi.delete).toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0] instanceof Error).toEqual(true);
    expect(mockNext.mock.calls[0][0].message).toEqual(JSON.stringify(mockResponse.response.data));
  });
  test('should return error 500 when no response field is missing from service call', async() => {
    const mockParams = { ruleId: 123 };
    mockRuleApi.delete.mockRejectedValueOnce({});
    const deleteCampaign = DeleteCampaign(mockServices);
    await deleteCampaign({ params: mockParams }, mockRes, mockNext);
    expect(mockRuleApi.delete).toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockNext).toBeCalled();
  });
  test('should return an error on unknown error response from campaign api client', async() => {
    const mockParams = { ruleId: 123 };
    const mockResponse = {
      response: {
        status: 501,
        data: {
          code: 'HTTP_NOT_IMPLEMENTED_ERROR',
          message: 'Not implemented',
          uuid: '04d69d4d-4c41-4373-b7f5-9b351a1f345d',
          timestamp: '2018-08-28T20:43:59.319Z',
          metadata: [],
        },
      },
    };
    mockRuleApi.delete.mockRejectedValueOnce(mockResponse);
    const deleteCampaign = DeleteCampaign(mockServices);
    await deleteCampaign({ params: mockParams }, mockRes, mockNext);
    expect(mockRuleApi.delete).toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0] instanceof Error).toEqual(true);
    expect(mockNext.mock.calls[0][0].message).toEqual(JSON.stringify(mockResponse.response.data));
  });
  test('should not return an error on valid request', async() => {
    const mockParams = { ruleId: 123 };
    const mockBody = {
      testkey: 'testval',
    };
    const mockResponse = {
      data: { id: mockParams.ruleId, ...mockBody },
    };
    mockRuleApi.delete.mockResolvedValueOnce(mockResponse);
    mockStatus.mockReturnValue({ json: mockJson });
    const deleteCampaign = DeleteCampaign(mockServices);
    await deleteCampaign({ params: mockParams }, mockRes, mockNext);
    expect(mockRuleApi.delete).toBeCalled();
    expect(mockStatus).toBeCalled();
    expect(mockStatus).toBeCalledWith(204);
    expect(mockNext).not.toBeCalled();
  });
});
