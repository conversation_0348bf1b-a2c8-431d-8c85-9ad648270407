const setupCampaign = require('../setupCampaign');

const mockService = {
  vignetteService: {
    getCampaignSetupInfo: jest.fn().mockImplementation(() => {
      return {
        products: [ { 'sub_category_description': 'borrowingProducts' }, { 'sub_category_description': 'others' } ],
      };
    }),
  },
};
const mockNext = jest.fn();
const mockReq = { params: { ruleId: 1234 } };
const mockResp = {
  locals: {
    user: {},
  },
  status: jest.fn(),
};
describe('test setup campaign', () => {
  test('Should call next function', async() => {
    await setupCampaign(mockService)(mockReq, mockResp, mockNext);
    expect(mockNext).toHaveBeenCalled();
  });

  test('Should call next function without otherProducts', async() => {
    await setupCampaign({
      vignetteService: {
        getCampaignSetupInfo: jest.fn().mockImplementation(() => {
          return {
            products: [ { 'sub_category_description': 'borrowingProducts' } ],
          };
        }),
      },
    })(mockReq, mockResp, mockNext);
    expect(mockNext).toHaveBeenCalled();
  });
});
