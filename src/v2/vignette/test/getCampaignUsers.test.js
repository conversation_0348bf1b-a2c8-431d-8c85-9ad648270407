const getCampaignUsers = require('../getCampaignUsers');
const permissions = require('__mocks__/permissionsMocks');
const { HttpError } = require('pigeon-pigeon-pack').errors;

const mockRuleService = {
  userAssignmentService: {
    getPotentialAssigneesForCampaign: jest.fn(),
  },
};

const mockReq = { params: { ruleId: 1234 } };
const mockResp = {
  locals: {
    user: {},
  },
  status: jest.fn(),
};
const mockNext = jest.fn();

describe('GET /api/v2/campaign-users - get campaign users', () => {
  beforeEach(() => {
    mockResp.locals.user = {};
    mockResp.status.mockClear();
  });

  test('Should throw error for invalid request', async() => {
    const invalidReq = {
      params: {},
    };

    const asyncGetCampaignUsers = getCampaignUsers(mockRuleService);
    const result = await asyncGetCampaignUsers(invalidReq, mockResp, mockNext);

    expect(result == null || result === undefined).toBe(true);
    expect(mockNext).toHaveBeenCalled();
    expect(mockNext.mock.calls[0][0] instanceof HttpError).toBe(true);
  });

  test('Should throw error when insufficient permissions are found', async() => {
    const asyncGetCampaignUsers = getCampaignUsers(mockRuleService);
    const result = await asyncGetCampaignUsers(mockReq, mockResp, mockNext);

    expect(result == null || result === undefined).toBe(true);
    expect(mockNext).toHaveBeenCalled();
    expect(mockNext.mock.calls[0][0] instanceof HttpError).toBe(true);
  });

  test('Should return 200 for valid req', async() => {
    mockRuleService.userAssignmentService.getPotentialAssigneesForCampaign.mockReturnValueOnce(
      [
        {
          full_name: 'Test User 1',
          sid: 's123456',
        },
        {
          full_name: 'Test User 2',
          sid: 's789456',
        },
      ]);

    mockResp.locals.user.permissions = permissions.admin;

    const validReq = {
      params: {
        ruleId: '123',
      },
    };

    const mockJson = {
      json: jest.fn(),
    };

    mockResp.status.mockReturnValueOnce(mockJson);

    const asyncGetCampaignUsers = getCampaignUsers(mockRuleService);
    const result = await asyncGetCampaignUsers(validReq, mockResp, mockNext);

    expect(result !== null || result !== undefined).toBe(true);
    expect(mockResp.status).toHaveBeenCalled();
    expect(mockNext).not.toHaveBeenCalled();
    expect(mockJson.json).toHaveBeenCalled();
  });

  test('Should return 500 error when 500 error response returned from rule assignment service', async() => {
    const error = new Error();
    error.response = { status: 500, data: { message: 'This is a mock eror' } };

    mockRuleService.userAssignmentService.getPotentialAssigneesForCampaign.mockImplementation(() => { throw error; });
    mockResp.locals.user.permissions = permissions.admin;

    const validReq = { params: { ruleId: 'abc123' } };

    const mockJson = { json: jest.fn() };

    mockResp.status.mockReturnValueOnce(mockJson);

    const asyncGetCampaignUsers = getCampaignUsers(mockRuleService);
    const result = await asyncGetCampaignUsers(validReq, mockResp, mockNext);

    expect(result !== null || result !== undefined).toBe(true);
    expect(mockResp.status).not.toHaveBeenCalled();
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof HttpError).toBe(true);
  });
});
