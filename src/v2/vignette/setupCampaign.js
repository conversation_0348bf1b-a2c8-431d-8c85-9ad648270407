const R = require('ramda');
const { processError } = require('./common');

const setupCampaign = ({ vignetteService }) => async(req, res, next) => {
  try {
    const campaignSetupInfo = await vignetteService.getCampaignSetupInfo();

    const returnData = {
      ...campaignSetupInfo,
      borrowingProducts: [],
      bankingProducts: [],
      investingRetail: [],
      investingWealth: [],
      otherProducts: [],
    };

    delete returnData.products;

    campaignSetupInfo.products.forEach(element => {
      const category = element.sub_category_description;
      R.has(category, returnData) ? returnData[category].push(element) : returnData.otherProducts.push(element);
    });

    if (!returnData.otherProducts.length) {
      delete returnData.otherProducts;
    }

    res.status(200).json(returnData);
  } catch (err) {
    next(processError(err));
  }
};

module.exports = setupCampaign;
