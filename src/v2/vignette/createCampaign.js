const R = require('ramda');
const { validate, schema } = require('./validation');
const { ForbiddenError, ERROR_HANDLER, CustomError, BadRequestError } = require('../../error');
const { canCreateCampaign } = require('../../permissions/workflow');

const createCampaign = ({ logger, ruleApi }) => async(req, res, next) => {
  try {
    let campaign = validate(req.body, schema.create);
    const allow = await canCreateCampaign(res.locals.user, req.body);
    if (!allow) {
      return next(new ForbiddenError());
    }
    const sid = R.pathOr(null, [ 'locals', 'user', 'sid' ], res);
    campaign.created_by = sid;
    campaign.updated_by = sid;
    const result = await ruleApi.create(campaign);
    const newCampaign = result.data;
    res.status(200).json(newCampaign);
  } catch (error) {
    logger.error(error);
    // request made and response received
    if (error.response) {
      let errorMessage;
      if (error.response.data && error.response.data.message) {
        if (error.response.data.message.includes('duplicate')) {
          errorMessage = 'Campaign with this name already exists. Use a unique name for the new campaign.';
          return next(new BadRequestError(errorMessage));
        } else {
          errorMessage = error.response.data.message;
        }
      } else {
        errorMessage = JSON.stringify(error.response.data);
      }
      return next(new CustomError(error.response.status, errorMessage));
    } else {
      ERROR_HANDLER('Error while creating SOL campaign.', error, next);
    }
  }
};

module.exports = createCampaign;
