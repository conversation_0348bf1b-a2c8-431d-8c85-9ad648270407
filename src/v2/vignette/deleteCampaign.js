const { validate, schema } = require('./validation');
const { processError } = require('./common');

const deleteCampaign = ({ logger, ruleApi }) => async(req, res, next) => {
  try {
    const { ruleId } = validate(req.params, schema.delete);
    const campaign = await ruleApi.delete(ruleId);
    res.status(204).json(campaign.data);
  } catch (error) {
    logger.error(error);
    next(processError(error));
  }
};

module.exports = deleteCampaign;
