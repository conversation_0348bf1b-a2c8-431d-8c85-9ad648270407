const express = require('express');
const getAll = require('./getAll');
const getCampaign = require('./getCampaign');
const getCampaignAsXML = require('./getCampaignAsXML');
const deleteCampaign = require('./deleteCampaign');
const createCampaign = require('./createCampaign');
const updateCampaign = require('./updateCampaign');

const {
  middleware: can,
  CAMPAIGNS_VIEW,
  CAMPAIGNS_MANAGE,
} = require('../../permissions/index');

const wrapAsync = function(fn) {
  return function(req, res, next) {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

const init = ({ logger, userService, userAssignmentService, vignetteService, ruleApi, contentApi }) => {
  const router = express.Router();

  router.get('/rules/:ruleId/xml', can([
    CAMPAIGNS_VIEW,
  ]), wrapAsync(getCampaignAsXML({ ruleApi, contentApi })));

  router.get('/rules/:ruleId', can([
    CAMPAIGNS_VIEW,
  ]), wrapAsync(getCampaign({ ruleApi, userAssignmentService })));

  router.get('/rules', can([
    CAMPAIGNS_VIEW,
  ]), wrapAsync(getAll({ ruleApi, userService })));

  router.post('/rules', can([
    CAMPAIGNS_MANAGE,
  ]), wrapAsync(createCampaign({ logger, userAssignmentService, userService, ruleApi })));

  router.patch('/rules/:ruleId', can([
    CAMPAIGNS_MANAGE,
  ]), wrapAsync(updateCampaign({ logger, ruleApi, userAssignmentService })));

  router.delete('/rules/:ruleId', can([
    CAMPAIGNS_MANAGE,
  ]), wrapAsync(deleteCampaign({ logger, ruleApi })));

  return router;
};

module.exports = init;
