const builder = require('xmlbuilder');
const moment = require('moment-timezone');
const path = require('path');

const { validate, schema } = require('./validation');
const { processError } = require('./common');
const { xmlTimeFormat } = require('../../constants/storefront');

const getCampaignAsXML = ({ ruleApi, contentApi }) => async(req, res, next) => {
  try {
    const { ruleId } = validate(req.params, schema.getOne);
    const [ result ] = await Promise.all([ ruleApi.get(ruleId) ]);
    res.status(200);
    res.header('Content-Disposition', `attachment; filename="${result.data.id}.xml"`);
    const xmlData = await xmlFromData(result.data, contentApi);
    res.send(xmlData);
  } catch (error) {
    next(processError(error));
  }
};

const xmlFromData = async(data, contentApi) => {
  const pigeonRuleToStorefrontMapping = {
    'id': 'ruleId',
    'name': 'ruleName',
    'message_type': 'contentType',
    'start_date': 'startDate',
    'end_date': 'expiryDate',
  };

  const xml = builder.create('ContentMetaData', {
    version: '1.0',
    encoding: 'UTF-8',
  })
    .att('xsi:schemaLocation', 'http://xsd.bns.com/msgs/contentdelivery/v001 ContentDelivery.xsd')
    .att('xmlns', 'http://xsd.bns.com/msgs/contentdelivery/v001')
    .att('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance')
    .att('xmlns:content', 'http://xsd.bns.com/types/contentdelivery/v001');

  for (const keyName in pigeonRuleToStorefrontMapping) {
    const ruleValue = [ 'start_date', 'end_date' ].includes(keyName)
      ? moment.tz(data[keyName], 'America/Toronto').format(xmlTimeFormat)
      : data[keyName];

    if (ruleValue) {
      xml.ele(`content:${pigeonRuleToStorefrontMapping[keyName]}`).txt(ruleValue);
    }
  }

  // check for contents
  if (data.contents && data.contents.length > 0) {
    const firstContent = data.contents[0];
    const webFragmentFileDetails = await contentApi.getContentDetailsVignette(firstContent.content_id);
    const filePath = path.join('/storefront', `${webFragmentFileDetails.fragment_file_path}/${webFragmentFileDetails.fragment_file_name}`);
    xml
      .ele('content:webFragmentId').txt(filePath).up()
      .ele('content:contentType').txt('M').up()
      .ele('content:containerId').txt(firstContent.container).up()
      .ele('content:pageList')
      .ele(data.contents.map(content => ({ 'content:pageId': content.page })));
  }

  if (data.targeting) {
    xml
      .ele('content:language').txt(data.targeting.language_code).up()
      .ele('content:country').txt(data.targeting.country_code);

    if (data.targeting.campaign_name) {
      xml.ele('content:campaignId').txt(data.targeting.campaign_name);
    }

    const { products } = data.targeting;
    if (products) {
      products.forEach(product => {
        let targetMode;

        switch (product.attribute_mode) {
          case 'or':
            targetMode = 'LENIENT';
            break;
          case 'and':
            targetMode = 'STRICT';
            break;
          case 'not':
            targetMode = 'EXCLUSIVE';
            break;
        }

        xml
          .ele('content:contentRule')
          .ele('content:targetMode').txt(targetMode).up()
          .ele('content:ruleType').txt(product.attribute_type).up()
          .ele('content:ruleAttributes')
          .ele(product.attribute_values.map((val) => ({ 'content:domainValue': val })));
      });
    }
  }

  return xml.end({ pretty: true });
};

module.exports = getCampaignAsXML;
