const { validate, schema } = require('./validation');
const { processError } = require('./common');

const getCampaignUsers = ({ userAssignmentService }) => async(req, res, next) => {
  try {
    const { ruleId } = validate(req.params, schema.getUser);
    const rule = await userAssignmentService.getPotentialAssigneesForCampaign(ruleId);
    res.status(200).json(rule);
  } catch (error) {
    next(processError(error));
  }
};

module.exports = getCampaignUsers;
