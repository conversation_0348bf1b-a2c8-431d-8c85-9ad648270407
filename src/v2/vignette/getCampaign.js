const { validate, schema } = require('./validation');
const {
  processError,
} = require('./common');
const { ForbiddenError } = require('../../error');
const { uniq } = require('lodash');

const getCampaign = ({ ruleApi }) => async(req, res, next) => {
  try {
    const { ruleId } = validate(req.params, schema.getOne);
    const [ result ] = await Promise.all([ ruleApi.get(ruleId) ]);

    // check user has access to view the page, container & sub type of the rule
    const { containers, pages, ruleSubTypes } = res.locals.user.access.campaigns;
    const app = result.data.type === 'estore' ? 'storefront' : 'sol';

    const viewAccessContainers = containers[app] ? uniq([ ...(containers[app].view || []), ...(containers[app].manage || []) ]) : [];
    const viewAccessPages = pages[app] ? uniq([ ...(pages[app].view || []), ...(pages[app].manage || []) ]) : [];
    const viewAccessSubTypes = ruleSubTypes[app] ? uniq([ ...(ruleSubTypes[app].view || []), ...(ruleSubTypes[app].manage || []) ]) : [];

    const hasContainerPageAccess = result.data.contents.some(content => {
      return viewAccessContainers.includes(content.container) && viewAccessPages.includes(content.page);
    });
    const subType = (result.data.targeting && result.data.targeting.campaign_name) ? 'targeted' : 'mass';
    const hasRuleSubTypeAccess = viewAccessSubTypes.includes(subType);

    if (!hasContainerPageAccess || !hasRuleSubTypeAccess) {
      next(new ForbiddenError());
      return;
    }

    res.status(200).json(result.data);
  } catch (error) {
    next(processError(error));
  }
};

module.exports = getCampaign;
