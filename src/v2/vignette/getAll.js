const { validate, schema } = require('./validation');
const { processError } = require('./common');
const { uniq } = require('lodash');

const getAll = ({ ruleApi, userService }) => async(req, res, next) => {
  try {
    const value = validate(req.query, schema.getAll);

    const { access } = res.locals.user;
    const { containers, pages, ruleSubTypes } = access.campaigns;
    const app = value.type === 'estore' ? 'storefront' : 'sol';

    if (!containers[app] || !pages[app] || !ruleSubTypes[app]) {
      return res.status(200).json({ items: [], total: 0 });
    }

    const accessContainers = {};
    const accessPages = {};
    const accessRuleSubTypes = {};

    accessContainers[app] = uniq([ ...(containers[app].view || []), ...(containers[app].manage || []) ]);
    accessPages[app] = uniq([ ...(pages[app].view || []), ...(pages[app].manage || []) ]);
    accessRuleSubTypes[app] = uniq([
      ...(ruleSubTypes[app].view ? ruleSubTypes[app].view.map(val => val.toUpperCase()) : []),
      ...(ruleSubTypes[app].manage ? ruleSubTypes[app].manage.map(val => val.toUpperCase()) : []),
    ]);

    const accessQuery = {
      accessContainers,
      accessPages,
      accessRuleSubTypes,
    };

    const [ result, users ] = await Promise.all([ ruleApi.getAllByAccess(value, accessQuery), userService.getUser() ]);
    const allRules = result.data;

    const sidMapping = users.reduce((o, i) => ({ ...o, [i.sid]: i.name }), {});

    allRules.items = allRules.items.map(item => {
      if (sidMapping[item.created_by]) {
        item.created_by = sidMapping[item.created_by];
      }
      if (sidMapping[item.updated_by]) {
        item.updated_by = sidMapping[item.updated_by];
      }

      return item;
    });

    res.status(200).json(allRules);
  } catch (error) {
    next(processError(error));
  }
};

module.exports = getAll;
