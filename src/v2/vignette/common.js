const { HttpError } = require('pigeon-pigeon-pack').errors;
const R = require('ramda');

const CAMPAIGN_STATUS = {
  CAMPAIGN_STATUS_SUBMITTED: 'submitted',
  CAMPAIGN_STATUS_DRAFT: 'draft',
  CAMPAIGN_STATUS_PUBLISHED: 'reviewed',
  CAMPAIGN_STATUS_REVIEWED: 'published',
};

const processError = (error) => {
  if (R.is(HttpError, error)) {
    return error;
  }

  if (R.is(Error, error)) {
    return new HttpError(error);
  }

  if (R.has('response', error)) {
    return new HttpError(JSON.stringify(error.response.data),
      R.pathOr(500, [ 'response', 'status' ], error));
  }

  return new HttpError(error);
};

const ruleV2toV1Adapter = (rule) => {
  const contents = R.propOr([ {} ], 'contents', rule);
  let ruleV1 = {
    ...rule,
    pages: contents.map((item) => item.page),
    container: contents[0].container,
    content_space: contents[0].content_space,
    content_type: contents[0].content_type,
    content_id: contents[0].content_id,
  };
  return R.omit([ 'contents' ], ruleV1);
};

const ruleV1toV2Adaptor = (rule) => {
  rule.contents = R.propOr([], 'pages', rule).map((page) => ({
    page: page,
    container: rule.container,
    content_space: rule.content_space,
    content_type: rule.content_type,
    content_id: rule.content_id,
  }));

  return R.omit([ 'pages', 'container', 'content_space', 'content_type', 'content_id' ], rule);
};

module.exports = {
  CAMPAIGN_STATUS,
  processError,
  ruleV2toV1Adapter,
  ruleV1toV2Adaptor,
};
