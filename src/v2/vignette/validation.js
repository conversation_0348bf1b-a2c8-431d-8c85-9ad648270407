const Joi = require('joi');

const ruleId = Joi.number();
const datetime = Joi.date().raw().iso();
const offset = Joi.number().min(0).default(0);
const limit = Joi.number().min(1).max(100).default(25);
const snumber = Joi.string().min(8).max(10).regex(/^s[0-9]+$/);
const assignees = Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/);

const contents = {
  page: Joi.string().min(1).max(64),
  container: Joi.string().min(1).max(64),
  content_space: Joi.string().min(1).max(40),
  content_type: Joi.string().min(1).max(40),
  content_id: Joi.string().min(1).max(40),
  content_disabled: Joi.bool(),
  subject: Joi.string().min(1).max(100),
};

const platform = {
  platform_name: Joi.string().valid('ios', 'android', 'web'),
  platform_version: Joi.string().min(1).max(20),
};

const products = {
  attribute_mode: Joi.string().valid('and', 'or', 'not'),
  attribute_type: Joi.string(),
  attribute_values: Joi.array().items(Joi.string().min(1).max(100)),
};

const targeting = {
  campaign_name: Joi.string().min(1).max(20).regex(/^[A-Z0-9]+$/).allow(null),
  language_code: Joi.string().valid('en', 'fr'),
  country_code: Joi.string().valid('CA'),
  platforms: Joi.array().items(platform),
  products: Joi.array().items(products),
};

const rule = {
  name: Joi.string().min(1).max(100),
  type: Joi.string().valid('vignette', 'estore'),
  status: Joi.string().valid('draft', 'submitted', 'reviewed', 'approved', 'published', 'terminated'),
  start_date: datetime,
  end_date: datetime,
  urgent: Joi.bool(),
  disabled: Joi.bool(),
  deleted: Joi.bool(),
  created_by: snumber,
  updated_by: snumber,
  message_type: Joi.string().valid('P', 'U', 'N', 'M'),
  system_message: Joi.string().min(1).max(20),
  contents: Joi.array().min(1).items(Joi.object().keys(contents)),
  targeting: Joi.object().keys(targeting),
  assignees: Joi.array().items(assignees).allow(null),
};

module.exports = {
  schema: {
    delete: Joi.object().keys({ ruleId: ruleId }).fork([ 'ruleId' ], (schema) => schema.required()),
    getOne: Joi.object().keys({ ruleId: ruleId }).fork([ 'ruleId' ], (schema) => schema.required()),
    getAll: Joi.object().keys({
      name: rule.name,
      type: rule.type.default('vignette'),
      page: contents.page,
      container: contents.container,
      container_disabled: Joi.bool(),
      campaign_name: targeting.campaign_name,
      platform_name: platform.platform_name,
      status: rule.status,
      disabled: rule.disabled,
      deleted: rule.deleted,
      created_by: Joi.array().items(rule.created_by),
      updated_by: Joi.array().items(rule.updated_by),
      start_date_lt: datetime,
      start_date_gt: datetime,
      end_date_lt: datetime,
      end_date_gt: datetime,
      sort: Joi.string().valid(
        'rules_api_id', '-rules_api_id',
        'name', '-name',
        'urgent', '-urgent',
        'start_date', '-start_date',
        'end_date', '-end_date',
        'created_at', '-created_at',
        'updated_at', '-updated_at',
        'rule_status', '-rule_status',
      ).default('-id'),
      offset: offset,
      limit: limit,
      assignees: rule.assignees,
      campaign_id: targeting.campaign_name,
    }),
    create: Joi.object().keys({
      ...rule,
      type: rule.type.default('vignette'),
      disabled: rule.disabled.default(false),
      status: rule.status.default('draft'),
      urgent: rule.urgent.default(false),
      pages: Joi.array().items(contents.page).min(1),
      container: contents.container,
      content_space: contents.content_space,
      content_type: contents.content_type,
      content_id: contents.content_id,
    }).fork([ 'name', 'status', 'start_date', 'end_date', 'contents' ], (schema) => schema.required()),
    update: {
      path: Joi.object().keys({ ruleId: ruleId }).fork([ 'ruleId' ], (schema) => schema.required()),
      body: Joi.object().keys({
        ...rule,
        type: rule.type,
        pages: Joi.array().items(contents.page).min(1),
        container: contents.container,
        content_space: contents.content_space,
        content_type: contents.content_type,
        content_id: contents.content_id,
      }).min(1),
    },
    getUser: Joi.object().keys({ ruleId: ruleId }).fork([ 'ruleId' ], (schema) => schema.required()),
  },
  validate: (data, schema) => {
    const { error, value } = schema.validate(data, { stripUnknown: true });
    if (error) {
      throw error;
    }
    return value;
  },
};
