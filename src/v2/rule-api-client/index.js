const commonReqParams = (config) => ({
  baseURL: config.url,
  headers: {
    'Content-Type': 'application/json; charset=utf-8',
    'Accept': 'application/json',
  },
  timeout: config.timeout,
});

const create = ({ axios }, config, body) => (
  axios.request({
    ...commonReqParams(config),
    url: '/v2/rules',
    method: 'post',
    data: body,
  })
);

const deleteCall = ({ axios }, config, id) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v2/rules/${id}`,
    method: 'delete',
  })
);

const getAll = ({ axios }, config, query) => (
  axios.request({
    ...commonReqParams(config),
    url: '/v2/rules',
    method: 'get',
    params: query,
  })
);

const getAllByAccess = ({ axios }, config, query, data) => (
  axios.request({
    ...commonReqParams(config),
    url: '/v2/rules/fetch',
    method: 'post',
    params: query,
    data,
  })
);

const get = ({ axios }, config, id) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v2/rules/${id}`,
    method: 'get',
  })
);

const update = ({ axios }, config, id, body) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v2/rules/${id}`,
    method: 'patch',
    data: body,
  })
);

module.exports = ({ axios }, config) => ({
  create: (body) => create({ axios }, config, body),
  getAll: (query) => getAll({ axios }, config, query),
  getAllByAccess: (query, data) => getAllByAccess({ axios }, config, query, data),
  update: (ruleId, body) => update({ axios }, config, ruleId, body),
  get: (ruleId) => get({ axios }, config, ruleId),
  delete: (ruleId) => deleteCall({ axios }, config, ruleId),
});
