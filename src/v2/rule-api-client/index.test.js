const ruleApi = require('./index');

const mockReq = jest.fn();
const mockAxios = { request: mockReq };
// mock config
const config = {
  url: 'http://localhost/v1',
};

describe('Rule api client', () => {
  beforeEach(() => {
    mockReq.mockReset();
  });

  test('should export methods for rule api', () => {
    const ruleApiClient = ruleApi({ axios: mockAxios }, config);
    expect(ruleApiClient).toBeDefined();
    expect(ruleApiClient).toHaveProperty('create');
    expect(ruleApiClient.create).toBeInstanceOf(Function);
    expect(ruleApiClient).toHaveProperty('get');
    expect(ruleApiClient.create).toBeInstanceOf(Function);
    expect(ruleApiClient).toHaveProperty('getAll');
    expect(ruleApiClient.create).toBeInstanceOf(Function);
    expect(ruleApiClient).toHaveProperty('update');
    expect(ruleApiClient.create).toBeInstanceOf(Function);
    expect(ruleApiClient).toHaveProperty('delete');
    expect(ruleApiClient.create).toBeInstanceOf(Function);
  });

  test('should call create endpoint', () => {
    const body = { test: 'test' };

    ruleApi({ axios: mockAxios }, config).create(body);
    expect(mockReq).toBeCalled();
    const args = mockReq.mock.calls[0][0];
    expect(args.url).toBe('/v2/rules');
    expect(args.method).toBe('post');
    expect(args.data).toEqual(body);
  });
  test('should call delete endpoint', () => {
    ruleApi({ axios: mockAxios }, config).delete(1);
    expect(mockReq).toBeCalled();
    const args = mockReq.mock.calls[0][0];
    expect(args.url).toBe('/v2/rules/1');
    expect(args.method).toBe('delete');
  });
  test('should call update endpoint', () => {
    const body = { test: 'test' };

    ruleApi({ axios: mockAxios }, config).update(1, body);
    expect(mockReq).toBeCalled();
    const args = mockReq.mock.calls[0][0];
    expect(args.url).toBe('/v2/rules/1');
    expect(args.method).toBe('patch');
    expect(args.data).toEqual(body);
  });
  test('should call get endpoint', () => {
    const body = { test: 'test' };

    ruleApi({ axios: mockAxios }, config).get(1, body);
    expect(mockReq).toBeCalled();
    const args = mockReq.mock.calls[0][0];
    expect(args.url).toBe('/v2/rules/1');
    expect(args.method).toBe('get');
  });
  test('should get-all create endpoint', () => {
    const body = { test: 'test' };

    ruleApi({ axios: mockAxios }, config).getAll(body);
    expect(mockReq).toBeCalled();
    const args = mockReq.mock.calls[0][0];
    expect(args.url).toBe('/v2/rules');
    expect(args.method).toBe('get');
    expect(args.params).toEqual(body);
  });
});
