process.env.NODE_PATH = __dirname;
require('module').Module._initPaths();

const LaunchDarkly = require('launchdarkly-node-server-sdk');
const HttpsProxyAgent = require('https-proxy-agent');
const redis = require('redis');

const axiosAuth = require('./auth');
const config = require('./constants/config');
const Server = require('./server');
const routes = require('./routes');
const authenticateMiddleware = require('./server/middleware/authenticate');
const logger = require('./services/logger')(config);
const campaign = require('./campaign');
const alert = require('./alert');
const variableMappings = require('./variable-mappings/routes');
const messageCentre = require('./message-centre-campaign');
const messageDetail = require('./message-centre-message');
const offers = require('./offers');
const createLaunchDarkly = require('./server/launch-darkly');

const serverPort = config.server.port;

process.on('unhandledRejection', err => {
  logger.error({ message: 'unhandled promise rejection', err: { message: err.message, stack: err.stack } });
});

process.on('uncaughtException', err => {
  logger.error({ message: 'uncaught exception', err: { message: err.message, stack: err.stack } });
});

const services = require('./services')(logger);
const {
  applicationService,
  containerService,
  pageService,
  permissionService,
  platformService,
  roleService,
  ruleSubTypeService,
  ruleTypeService,
  teamsService,
  userService,
  validationService,
  vignetteService,
} = services;
const variableMappingsApi = services.variableMappingsApi({ axios: axiosAuth }, config.services.ruleApi);
const variableMappingsApiV2 = services.variableMappingsApiV2({ axios: axiosAuth }, config.services.ruleApi);
const campaignApi = services.campaignApi({ axios: axiosAuth }, config.services.ruleApi);
const contentApi = services.contentApi({ axios: axiosAuth }, config.services.contentApi);
const ruleService = services.ruleService(campaignApi);
const campaignManagementApi = services.campaignManagementService({ axios: axiosAuth }, config.services.campaignManagementApi);
const offersApi = services.offersService({ axios: axiosAuth }, config.services.offersApi);
const vignette = require('./v2/vignette');
const ruleApi = require('./v2/rule-api-client')({ axios: axiosAuth }, config.services.ruleApi);

(async() => {
  console.log('**************************************************************************\n\n\n');
  console.log('src/init');
  console.log('config');
  console.log(config);
  console.log('process.env');
  console.log(process.env);
  console.log('\n\n\n**************************************************************************');
  const proxyAgent = new HttpsProxyAgent(process.env.PROXY_URL);
  const launchDarklyService = createLaunchDarkly(LaunchDarkly, config.launchDarkly.secret, config.launchDarkly.userKey, {
    proxyHost: proxyAgent.proxy.host,
    proxyPort: proxyAgent.proxy.port,
    logger,
  });

  // Initialize Launch Darkly client
  try {
    await launchDarklyService.init();
    logger.info({ message: 'Launch Darkly initialized successfully' });
  } catch (err) {
    logger.error({ message: `Could not initialize Launch Darkly: ${err.message}` });
  };

  // Initialize Redis client
  const redisClient = redis.createClient(config.redisConnectionConfig);
  redisClient
    .on('connect', () => {
      logger.info({ message: 'Connected to redis successfully' });
    })
    .on('error', err => {
      logger.error({ message: `Failed to connect to redis: ${err.message}`, code: err.code });
    });

  const marvelProductApi = services.marvelProductApi({ axios: axiosAuth }, config.services.marvelProductApi, launchDarklyService, logger);

  const server = Server(logger, config, userService, [
    [ '/.well-known/jwks.json', routes.jwks(config.services.serviceAuth) ],
    [ '/health', routes.health() ],
    [ '/api/v1/applications', routes.application(services.applicationService) ],
    [ '/api/v1/authenticate', routes.authenticate(logger) ],
    [ '/api/v1/users', routes.user(userService, config) ],
    [ '/api/v1/containers', routes.container(containerService) ],
    [ '/api/v1/pages', routes.page(pageService) ],
    [ '/api/v1/teams', routes.teams(teamsService) ],
    [ '/api/v1/rule-types', routes.ruleType(ruleTypeService) ],
    [ '/api/v1/rule-sub-types', routes.ruleSubType(ruleSubTypeService) ],
    [ '/api/v1/platforms', routes.platform(platformService) ],
    [ '/api/v1/roles', routes.role(roleService) ],
    [ '/api/v1', alert.routes(config, services.alertApi({ axios: axiosAuth }, config.services.ruleApi), userService, permissionService, contentApi) ],
    [ '/api/v1', campaign.routes(config, logger, campaignApi, marvelProductApi, userService, ruleService, vignetteService, contentApi, teamsService, applicationService) ],
    [ '/api/v1/contents', routes.content(contentApi) ],
    [ '/api/v1/variable-mappings', variableMappings.variableMappings(variableMappingsApi) ],
    [ '/api/v2/variable-mappings', variableMappings.variableMappingsV2(variableMappingsApiV2, variableMappingsApi, ruleService) ],
    [ '/api/v2/vignette', vignette({ logger, ruleApi, userService, userAssignmentService: ruleService, contentApi }) ],
    [ '/api/v2/estore', vignette({ logger, ruleApi, userService, userAssignmentService: ruleService, contentApi }) ],
    [ '/api/v1/validate', routes.validation(validationService) ],
    [ '/api/v1/message-centre', messageCentre.routes(config, logger, campaignManagementApi) ],
    [ '/api/v1/message-centre', messageDetail.routes(config, logger, campaignManagementApi) ],
    [ '/api/v1/offers', offers.routes(config, logger, offersApi, ruleService, userService) ],
  ], authenticateMiddleware(logger, userService, permissionService), launchDarklyService, redisClient);

  server.listen(serverPort, () => {
    logger.info(`Admin server started on port ${serverPort}`);
  });
})();
