const {
  getHandler,
  jsonResp,
  resMock,
  resMockResError,
  reqMock,
  nextMock,
  nextMockConsoleLog,
} = require('./index.test');
const { InternalServerError } = require('../error');

const testData = {
  name: 'test-page-name',
  description: 'test-page-description',
  pageId: 'test-page-slug',
};
const mockService = {
  getPage: jest.fn().mockReturnValue([ testData ]),
  createPage: jest.fn().mockReturnValue(testData),
  updatePage: jest.fn().mockReturnValue(testData),
  deletePage: jest.fn().mockReturnValue(testData),
};

const router = require('./page')(mockService);

describe('getPage', () => {
  it('Success', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith([ testData ]);
    expect(mockService.getPage).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMockResError, nextMock);
    expect(mockService.getPage).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('getSinglePage', () => {
  it('Success', async() => {
    await getHandler(router, '/:id', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.getPage).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/:id', 'get')(reqMock, resMockResError, nextMock);
    expect(mockService.getPage).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('createPage', () => {
  it('Success', async() => {
    await getHandler(router, '/', 'post')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.createPage).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/', 'post')(reqMock, resMockResError, nextMock);
    expect(mockService.createPage).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('updatePage', () => {
  it('Success', async() => {
    await getHandler(router, '/:id', 'patch')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.updatePage).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/:id', 'patch')(reqMock, resMockResError, nextMock);
    expect(mockService.updatePage).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('deletePage', () => {
  it('Success', async() => {
    await getHandler(router, '/:id', 'delete')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.deletePage).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/:id', 'delete')(reqMock, resMockResError, nextMock);
    expect(mockService.deletePage).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });

  it('DB transaction error', async() => {
    // simulates mssql implementation of transaction error
    const originalError = { code: 'EREQUEST', message: 'Conversion failed when converting the nvarchar value \'id\' to data type int.' };
    const svcError = { originalError, code: 'EABORT', message: 'Transaction has been aborted' };
    const mockService = { deletePage: jest.fn(() => { throw svcError; }) };
    const router = require('./page')(mockService);

    await getHandler(router, '/:id', 'delete')(reqMock, resMockResError, nextMock);
    expect(mockService.deletePage).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    const nextArg = nextMock.mock.calls[0][0];
    // assert original error is not exposed to client, ie not thrown directly at global error handler via next()
    const expectedError = new InternalServerError('Error while deleting page 1', svcError.originalError);
    expect(nextArg).toStrictEqual(expectedError);
    expect(nextArg.payload).toBe(originalError);
  });
});
