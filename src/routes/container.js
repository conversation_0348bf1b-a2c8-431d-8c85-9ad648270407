const express = require('express');
const { ERROR_HANDLER } = require('../error');
const {
  middleware: can,
  CONTAINERS_VIEW,
  CONTAINERS_VIEW_SUPER,
  CONTAINERS_MANAGE,
  CAMPAIGNS_VIEW,
  CCAU_CAMPAIGNS_VIEW,
  ALERTS_VIEW,
  TEAMS_VIEW,
} = require('../permissions');
const { validateCrossTeamRequest } = require('../permissions/utils');
const { VALIDATE, SCHEMA } = require('../constants/validate');
const { nonProdMiddleware } = require('./middleware');

module.exports = (service) => {
  const router = express.Router();

  router.get('/', can([ CONTAINERS_VIEW, CAMPAIGNS_VIEW, CCAU_CAMPAIGNS_VIEW, ALERTS_VIEW, TEAMS_VIEW ], 'OR'), async(req, res, next) => {
    try {
      const results = await service.getContainer(req.query);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while fetching containers`, err, next);
    }
  });

  router.get('/:id', can([ CONTAINERS_VIEW ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const results = await service.getContainer(req.params);
      validateCrossTeamRequest({ permissionRequired: CONTAINERS_VIEW_SUPER, teamId: results[0].team_id, res });
      res.status(200).json(results[0]);
    } catch (err) {
      ERROR_HANDLER(`Error while fetching container ${req.params.id}`, err, next);
    }
  });

  router.post('/', can([ CONTAINERS_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.body, SCHEMA.CONTAINER_CREATE);
      const results = await service.createContainer(req.body);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while creating container ${req.body.label}.`, err, next);
    }
  });

  router.patch('/:id', can([ CONTAINERS_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.CONTAINER_UPDATE);
      const results = await service.updateContainer(req.params.id, req.body);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while updating container ${req.body.label}.`, err, next);
    }
  });

  router.delete('/:id', [ nonProdMiddleware(), can([ CONTAINERS_MANAGE ]) ], async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const results = await service.deleteContainer(req.params.id);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while deleting container ${req.params.id}`, err, next);
    }
  });

  router.post('/:id/deactivate', can([ CONTAINERS_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const deactivatedContainer = await service.setContainerStatus(req.params.id, false);
      res.status(200).send(deactivatedContainer);
    } catch (err) {
      ERROR_HANDLER(`Error while deactivating container ${req.params.id}.`, err, next);
    }
  });

  router.post('/:id/activate', can([ CONTAINERS_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const deactivatedContainer = await service.setContainerStatus(req.params.id, true);
      res.status(200).send(deactivatedContainer);
    } catch (err) {
      ERROR_HANDLER(`Error while activating container ${req.params.id}.`, err, next);
    }
  });

  return router;
};
