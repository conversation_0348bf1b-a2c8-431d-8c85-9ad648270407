const {
  getHand<PERSON>,
  resMock,
  nextMock,
  jsonResp,
} = require('./index.test');

const { BadRequestError } = require('../error');
const mockService = {
  getContentLocales: jest.fn(),
  getContentTypes: jest.fn(),
  getContentTypeDetails: jest.fn(),
  getContents: jest.fn(),
  getContentDetails: jest.fn(),
  getContentDetailsVignette: jest.fn(),
  getContentsVignette: jest.fn(),
};

const router = require('./content')(mockService);

describe('Content API Routes', () => {
  beforeEach(() => {
    mockService.getContentLocales.mockClear();
    mockService.getContentTypes.mockClear();
    mockService.getContentTypeDetails.mockClear();
    mockService.getContents.mockClear();
    mockService.getContentDetails.mockClear();
  });

  test('Should throw error for invalid request when calling: /spaces/:spaceId/types', async() => {
    const invalidReqMock = {
      params: { spaceId: 1234 },
    };

    await getHandler(router, `/spaces/:spaceId/types`, 'get')(invalidReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof BadRequestError).toBe(true);
  });

  test('Should catch exception if service throws an error when calling: /spaces/:spaceId/types', async() => {
    const localReqMock = {
      params: { spaceId: 'abc123' },
    };
    mockService.getContentTypes.mockRejectedValueOnce(new Error());
    await getHandler(router, `/spaces/:spaceId/types`, 'get')(localReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(resMock.status).not.toHaveBeenCalled();
  });

  test('Should return successful response for valid request when calling: /spaces/:spaceId/types', async() => {
    const localReqMock = {
      params: { spaceId: 'abc123' },
    };
    const mockContentTypeData = {
      success: true,
    };

    mockService.getContentTypes.mockResolvedValueOnce({
      data: mockContentTypeData,
    });

    await getHandler(router, `/spaces/:spaceId/types`, 'get')(localReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('Should throw error for invalid request when calling: /spaces/:spaceId/types/:typeId', async() => {
    const localReqMock = {
      params: {
        spaceId: 'abc123',
        typeId: 123,
      },
    };

    await getHandler(router, `/spaces/:spaceId/types/:typeId`, 'get')(localReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof BadRequestError).toBe(true);
    expect(resMock.status).not.toHaveBeenCalled();
  });

  test('Should catch exception if service throws an error when calling: /spaces/:spaceId/types/:typeId', async() => {
    const localReqMock = {
      params: {
        spaceId: 'abc123',
        typeId: 'abc123',
      },
    };
    mockService.getContentTypeDetails.mockRejectedValueOnce(new Error());
    await getHandler(router, `/spaces/:spaceId/types/:typeId`, 'get')(localReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(resMock.status).not.toHaveBeenCalled();
  });

  test('Should return successful response for valid request when calling: /spaces/:spaceId/types/:typeId', async() => {
    const localReqMock = {
      params: {
        spaceId: 'abc123',
        typeId: 'abc123',
      },
    };
    const mockContentTypeData = {
      data: {
        success: true,
      },
    };

    mockService.getContentTypeDetails.mockResolvedValueOnce(mockContentTypeData);

    await getHandler(router, `/spaces/:spaceId/types/:typeId`, 'get')(localReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
    expect(jsonResp.json).toHaveBeenCalledWith(mockContentTypeData.data);
  });

  test('Should throw error for invalid request (params) when calling: /spaces/:spaceId/types/:typeId/contents', async() => {
    const invalidReqMock = {
      params: { spaceId: 1234 },
    };

    await getHandler(router, `/spaces/:spaceId/types/:typeId/contents`, 'get')(invalidReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof BadRequestError).toBe(true);
  });

  test('Should throw error for invalid request (query) when calling: /spaces/:spaceId/types/:typeId/contents', async() => {
    const invalidReqMock = {
      params: {
        spaceId: 'abc1234',
        typeId: 'abc123',
      },
      query: {
        language: 1,
      },
    };

    await getHandler(router, `/spaces/:spaceId/types/:typeId/contents`, 'get')(invalidReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof BadRequestError).toBe(true);
  });

  test('Should catch exception if service throws an error when calling: /spaces/:spaceId/types/:typeId/contents', async() => {
    const localReqMock = {
      params: {
        spaceId: 'abc1234',
        typeId: 'abc123',
      },
      query: {
        language: 'en',
      },
    };

    mockService.getContents.mockRejectedValueOnce(new Error());
    await getHandler(router, `/spaces/:spaceId/types/:typeId/contents`, 'get')(localReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(resMock.status).not.toHaveBeenCalled();
  });

  test('Should return successful response for valid request when calling: /spaces/:spaceId/types/:typeId/contnets', async() => {
    const localReqMock = {
      params: {
        spaceId: 'abc123',
        typeId: 'abc123',
      },
      query: {
        language: 'en',
      },
    };
    const mockContentTypeData = {
      data: {
        success: true,
      },
    };

    mockService.getContents.mockResolvedValueOnce(mockContentTypeData);

    await getHandler(router, `/spaces/:spaceId/types/:typeId/contents`, 'get')(localReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
    expect(jsonResp.json).toHaveBeenCalledWith(mockContentTypeData.data);
  });

  test('Should throw error for invalid request (params) when calling: /spaces/:spaceId/types/:typeId/contents/:contentId', async() => {
    const invalidReqMock = {
      params: { spaceId: 1234 },
    };

    await getHandler(router, `/spaces/:spaceId/types/:typeId/contents/:contentId`, 'get')(invalidReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof BadRequestError).toBe(true);
  });

  test('Should throw error for invalid request (query) when calling: /spaces/:spaceId/types/:typeId/contents/:contentId', async() => {
    const invalidReqMock = {
      params: {
        spaceId: 'abc1234',
        typeId: 'abc123',
      },
      query: {
        language: 1,
      },
    };

    await getHandler(router, `/spaces/:spaceId/types/:typeId/contents/:contentId`, 'get')(invalidReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof BadRequestError).toBe(true);
  });

  test('Should catch exception if service throws an error when calling: /spaces/:spaceId/types/:typeId/contents/:contentId', async() => {
    const localReqMock = {
      params: {
        spaceId: 'abc1234',
        typeId: 'abc123',
      },
      query: {
        language: 'en',
      },
    };

    mockService.getContentDetails.mockRejectedValueOnce(new Error());
    await getHandler(router, `/spaces/:spaceId/types/:typeId/contents/:contentId`, 'get')(localReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(resMock.status).not.toHaveBeenCalled();
  });

  test('Should return successful response for valid request when calling: /spaces/:spaceId/types/:typeId/contnets/:contentId', async() => {
    const localReqMock = {
      params: {
        spaceId: 'abc123',
        typeId: 'abc123',
      },
      query: {
        language: 'en',
      },
    };
    const mockContentTypeData = {
      data: {
        success: true,
      },
    };

    mockService.getContentDetails.mockResolvedValueOnce(mockContentTypeData);

    await getHandler(router, `/spaces/:spaceId/types/:typeId/contents/:contentId`, 'get')(localReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
    expect(jsonResp.json).toHaveBeenCalledWith(mockContentTypeData.data);
  });

  test('Should return successful response for valid request when calling: /vignette/:content_id', async() => {
    const mockReq = { params: { content_id: '12345' } };
    const mockData = { test: 'data' };

    mockService.getContentDetailsVignette.mockResolvedValueOnce(mockData);

    await getHandler(router, `/vignette/:content_id`, 'get')(mockReq, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
    expect(jsonResp.json).toHaveBeenCalledWith(mockData);
  });

  test('Should catch exception if service throws an error when calling: /vignette/:content_id', async() => {
    const mockReq = { params: { content_id: '12345' } };

    mockService.getContentDetailsVignette.mockRejectedValueOnce(new Error());

    await getHandler(router, `/vignette/:content_id`, 'get')(mockReq, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(resMock.status).not.toHaveBeenCalled();
  });

  test('Should throw error for invalid request (params) when calling: /vignette/:content_id', async() => {
    const mockReq = {
      params: { content_id: 'abcdefg' },
    };

    await getHandler(router, `/vignette/:content_id`, 'get')(mockReq, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof BadRequestError).toBe(true);
  });

  test('Should return successful response for valid request when calling: /vignette', async() => {
    const mockReq = { query: { } };
    const mockData = { test: 'data' };

    mockService.getContentsVignette.mockResolvedValueOnce(mockData);

    await getHandler(router, `/vignette`, 'get')(mockReq, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
    expect(jsonResp.json).toHaveBeenCalledWith(mockData);
  });

  test('Should catch exception if service throws an error when calling: /vignette', async() => {
    const mockReq = { query: { } };

    mockService.getContentsVignette.mockRejectedValueOnce(new Error());

    await getHandler(router, `/vignette`, 'get')(mockReq, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(resMock.status).not.toHaveBeenCalled();
  });

  test('Should throw error for invalid request (query) when calling: /vignette', async() => {
    const mockReq = {
      query: {
        type: 1,
      },
    };

    await getHandler(router, `/vignette`, 'get')(mockReq, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof BadRequestError).toBe(true);
  });

  test('Should catch exception if service throws an error when calling: /spaces/:spaceId/locales', async() => {
    const localReqMock = { params: { spaceId: 'abc123' } };
    mockService.getContentLocales.mockRejectedValueOnce(new Error());
    await getHandler(router, `/spaces/:spaceId/locales`, 'get')(localReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(resMock.status).not.toHaveBeenCalled();
  });

  test('Should return successful response for valid request when calling: /spaces/:spaceId/locales', async() => {
    const localReqMock = { params: { spaceId: 'abc123' } };
    const mockContentTypeData = {
      data: {
        total: 2,
        items: [
          { code: 'en-US', name: 'English (United States)', default: true },
          { code: 'fr', name: 'French' },
        ],
      },
      notifications: [],
    };
    mockService.getContentLocales.mockResolvedValueOnce({ data: mockContentTypeData });
    await getHandler(router, `/spaces/:spaceId/locales`, 'get')(localReqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
  });
});
