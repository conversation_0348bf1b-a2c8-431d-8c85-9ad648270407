const {
  getHand<PERSON>,
  jsonResp,
  resMock,
  resMockResError,
  reqMock,
  nextMock,
  nextMockConsoleLog,
} = require('./index.test');

const testData = {
  rule_type: 'test-rule-type',
};
const mockService = {
  getRuleType: jest.fn().mockReturnValue([ testData ]),
};

const router = require('./rule-type')(mockService);

describe('getRuleType', () => {
  it('Success', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith([ testData ]);
    expect(mockService.getRuleType).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMockResError, nextMock);
    expect(mockService.getRuleType).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});
