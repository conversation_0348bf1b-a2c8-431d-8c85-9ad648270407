const {
  getHand<PERSON>,
  jsonResp,
  resMock,
  resMockResError,
  reqMock,
  nextMock,
  nextMockConsoleLog,
} = require('./index.test');

const testData = {
  id: 1,
  name: 'test-application-name',
  description: 'test-application-description',
  applicationId: 'test-application-slug',
  status: true,
  rule_version: 2,
  team_id: 1,
};
const mockService = {
  getApplication: jest.fn().mockReturnValue(testData),
  getApplications: jest.fn().mockReturnValue([ testData ]),
  createApplication: jest.fn().mockReturnValue({
    admin_application_id: 1,
    admin_application_name: 'mock-name',
  }),
  updateApplication: jest.fn().mockReturnValue({
    admin_application_id: 1,
    admin_application_name: 'mock-name',
  }),
  deleteApplication: jest.fn().mockReturnValue(testData),
};

const router = require('./application')(mockService);

describe('get a list of applications', () => {
  it('Success', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith([ testData ]);
    expect(mockService.getApplications).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMockResError, nextMock);
    expect(mockService.getApplications).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('get a single application', () => {
  it('Success', async() => {
    await getHandler(router, '/:id', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.getApplication).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/:id', 'get')(reqMock, resMockResError, nextMock);
    expect(mockService.getApplication).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });

  it.todo('Non-admin attempt to get another team\'s application');
});

describe('create a new application', () => {
  it('success', async() => {
    await getHandler(router, '/', 'post')({
      ...reqMock,
      body: {
        applicationId: 'mock-app-id',
        description: 'mock-description',
        name: 'mock-name',
        platformIds: [ 1 ],
        ruleTypeIds: [ 1 ],
        status: true,
      },
    }, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith({
      admin_application_id: 1,
      admin_application_name: 'mock-name',
    });
    expect(mockService.createApplication).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it.todo('Non-admin attempt to create app for another teams');
});

describe('update an existing application', () => {
  it('success', async() => {
    const handler = getHandler(router, '/:id', 'patch');
    await handler({
      ...reqMock,
      body: {
        applicationId: 'mock-app-id',
        description: 'mock-description',
        name: 'mock-name',
        platformIds: [ 1 ],
        ruleTypeIds: [ 1 ],
        status: true,
      },
    }, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith({
      admin_application_id: 1,
      admin_application_name: 'mock-name',
    });
    expect(mockService.updateApplication).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it.todo('Non-admin attempt to update app for another teams');
});

describe('delete an existing application', () => {
  it('Success', async() => {
    await getHandler(router, '/:id', 'delete')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.deleteApplication).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/:id', 'delete')(reqMock, resMockResError, nextMock);
    expect(mockService.deleteApplication).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});
