const { NotFoundError } = require('../error');
const { getDeploymentEnvironment } = require('../utils');

/**
 * Express middleware that checks for the environment  and prevent access this route on PROD.
 *
 * @example
 * router.get('/rules', environmentMiddleware), (req, res, next) => {
 *  // protected route under
 * });
 */
const nonProdMiddleware = () => (req, res, next) => {
  const env = getDeploymentEnvironment();
  if (env === 'prd') {
    return next(new NotFoundError());
  }
  next();
};

module.exports = { nonProdMiddleware };
