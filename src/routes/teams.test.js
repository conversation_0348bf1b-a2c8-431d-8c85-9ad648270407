const {
  get<PERSON><PERSON><PERSON>,
  jsonResp,
  resMock,
  resMockResError,
  reqMock,
  nextMock,
  nextMockConsoleLog,
} = require('./index.test');

const {
  TEAMS_MANAGE,
  TEAMS_MANAGE_SUPER,
  USERS_VIEW,
  ROLES_VIEW,
} = require('../permissions');

const testData = {
  id: '99',
  name: 'test-team',
};
reqMock.body = testData;

const mockService = {
  getTeams: jest.fn().mockReturnValue([ testData ]),
  getTeam: jest.fn().mockReturnValue(testData),
  createTeam: jest.fn().mockReturnValue(testData),
  updateTeam: jest.fn().mockReturnValue(testData),
  setTeamStatusActive: jest.fn().mockReturnValue(testData),
  deleteTeam: jest.fn().mockReturnValue({}),
};

const router = require('./teams')(mockService);

describe('teams route', () => {
  afterEach(() => {
    Object.keys(mockService).forEach(svc => {
      mockService[svc].mockClear();
    });
    jsonResp.json.mockClear();
    nextMockConsoleLog.mockClear();
  });

  it('should handle happy path for all routes', async() => {
    const resMockUser = JSON.parse(JSON.stringify(resMock.locals.user));
    const testMatrix = [
      // in format of [ path, http-verb, user's permissions, backend service call, response ]
      [ '/', 'get', mockService.getTeams, [ USERS_VIEW ], [ testData ] ],
      [ '/', 'post', mockService.createTeam, [ TEAMS_MANAGE ], testData ],
      [ '/:id', 'get', mockService.getTeam, [ ROLES_VIEW ], testData ],
      [ '/:id', 'patch', mockService.updateTeam, [ TEAMS_MANAGE ], testData ],
      [ '/:id/activate', 'post', mockService.setTeamStatusActive, [ TEAMS_MANAGE ], testData ],
      [ '/:id/deactivate', 'post', mockService.setTeamStatusActive, [ TEAMS_MANAGE ], testData ],
      [ '/:id/deactivate', 'post', mockService.setTeamStatusActive, [ TEAMS_MANAGE ], testData ],
      [ '/:id', 'delete', mockService.deleteTeam, [ TEAMS_MANAGE_SUPER ], testData ],
    ];
    // for loop is preferred here over using array.forEach, as assertion errors are swallowed inside async fns
    for (let i = 0; i < testMatrix.length; i++) {
      const [ path, verb, svc, permissions, resBody ] = testMatrix[i];
      const user = { ...resMockUser, permissions };
      const res = { ...resMock, locals: { user } };
      await getHandler(router, path, verb)(reqMock, res, nextMockConsoleLog);
      expect(svc).toHaveBeenCalled();
      expect(jsonResp.json).toHaveBeenCalledWith(resBody);
      expect(nextMockConsoleLog).not.toHaveBeenCalled();
    };
  });

  it('should call next middleware when error occurs in any route', async() => {
    const testMatrix = [
      [ '/', 'get' ], // in format of [ path, http-verb ]
      [ '/', 'post' ],
      [ '/:id', 'get' ],
      [ '/:id', 'patch' ],
      [ '/:id/deactivate', 'post' ],
      [ '/:id/activate', 'post' ],
      [ '/:id', 'delete' ],
    ];
    for (let i = 0; i < testMatrix.length; i++) {
      const [ path, verb ] = testMatrix[i];
      await getHandler(router, path, verb)(reqMock, resMockResError, nextMock);
      expect(mockService.getTeams).toHaveBeenCalled();
      expect(jsonResp.json).not.toHaveBeenCalled();
      expect(nextMock).toHaveBeenCalled();
    };
  });
});
