const express = require('express');
const { ERROR_HANDLER } = require('../error');
const {
  middleware: can,
  PAGES_VIEW,
  PAGES_VIEW_SUPER,
  PAGES_MANAGE,
  CAMPAIGNS_VIEW,
  ALERTS_VIEW,
  TEAMS_VIEW,
  CCAU_CAMPAIGNS_VIEW,
} = require('../permissions');
const { validateCrossTeamRequest } = require('../permissions/utils');
const { VALIDATE, SCHEMA } = require('../constants/validate');
const { nonProdMiddleware } = require('./middleware');

module.exports = (service) => {
  const router = express.Router();

  router.get('/', can([ PAGES_VIEW, CAMPAIGNS_VIEW, CCAU_CAMPAIGNS_VIEW, ALERTS_VIEW, TEAMS_VIEW ], 'OR'), async(req, res, next) => {
    try {
      const results = await service.getPage(req.query);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while fetching pages`, err, next);
    }
  });

  router.get('/:id', can([ PAGES_VIEW ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const results = await service.getPage(req.params);
      validateCrossTeamRequest({ permissionRequired: PAGES_VIEW_SUPER, teamId: results[0].team_id, res });
      res.status(200).json(results[0]);
    } catch (err) {
      ERROR_HANDLER(`Error while fetching page ${req.params.id}`, err, next);
    }
  });

  router.post('/', can([ PAGES_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.body, SCHEMA.PAGE_CREATE);
      const results = await service.createPage(req.body);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while creating page.`, err, next);
    }
  });

  router.patch('/:id', can([ PAGES_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      VALIDATE(req.body, SCHEMA.PAGE_UPDATE);
      const results = await service.updatePage(req.params.id, req.body);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while updating page ${req.params.id}.`, err, next);
    }
  });

  router.delete('/:id', [ nonProdMiddleware(), can([ PAGES_MANAGE ]) ], async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const results = await service.deletePage(req.params.id);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while deleting page ${req.params.id}`, err, next);
    }
  });

  router.post('/:id/deactivate', can([ PAGES_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const deactivatedPage = await service.setPageStatus(req.params.id, false);
      res.status(200).send(deactivatedPage);
    } catch (err) {
      ERROR_HANDLER(`Error while deactivating page ${req.params.id}.`, err, next);
    }
  });

  router.post('/:id/activate', can([ PAGES_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const deactivatedPage = await service.setPageStatus(req.params.id, true);
      res.status(200).send(deactivatedPage);
    } catch (err) {
      ERROR_HANDLER(`Error while activating page ${req.params.id}.`, err, next);
    }
  });

  return router;
};
