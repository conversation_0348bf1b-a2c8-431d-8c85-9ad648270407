const express = require('express');
const { wamSsoAuth } = require('constants/config');

module.exports = (logger) => {
  const router = express.Router();

  router.get('/logout', (req, res) => {
    if (req.session) {
      req.session.destroy((error) => {
        if (error) {
          logger.warn({ message: 'Failed to destroy wam auth session', stack: error.stack });
        }
      });
    }
    res.clearCookie('sessionID', {
      httpOnly: true,
      secure: true,
    });
    return res.status(200).json({
      wamLogoutUrl: wamSsoAuth.logoutURL,
    });
  });

  return router;
};
