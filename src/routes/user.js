const express = require('express');
const { ERROR_HANDLER } = require('../error');
const {
  middleware: can,
  USERS_VIEW_SUPER,
  USERS_MANAGE_SUPER,
  USERS_VIEW,
  USERS_MANAGE,
  TEAMS_VIEW,
} = require('../permissions');
const { validateCrossTeamRequest } = require('../permissions/utils');
const { VALIDATE, SCHEMA } = require('../constants/validate');

module.exports = (userService, config) => {
  const router = express.Router();

  router.get('/', can([ USERS_VIEW, TEAMS_VIEW ], 'OR'), async(req, res, next) => {
    try {
      const results = req.query.sid
        ? await userService.getUser(req.query)
        : await userService.getUsers(req.query);

      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER('Error while fetching users.', err, next);
    }
  });

  router.get('/current', async(req, res, next) => {
    try {
      res.status(200).json({ data: {
        ...res.locals.user,
        auth_config: {
          wam_logout: config.wamSsoAuth.logoutURL,
        },
      } });
    } catch (err) {
      ERROR_HANDLER(`Error while fetching current user.`, err, next);
    }
  });

  router.get('/:id', can([ USERS_VIEW ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const results = await userService.getUser(req.params);
      validateCrossTeamRequest({ permissionRequired: USERS_VIEW_SUPER, teamId: results[0].team_id, res });

      res.status(200).json(results[0]);
    } catch (err) {
      ERROR_HANDLER(`Error while fetching user ${req.params.id}.`, err, next);
    }
  });

  router.post('/', can([ USERS_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.body, SCHEMA.USER_REQUIRE);
      validateCrossTeamRequest({ permissionRequired: USERS_MANAGE_SUPER, teamId: req.body.team_id, res });

      const results = await userService.createUser(req.body);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while creating user.`, err, next);
    }
  });

  router.patch('/:id', can([ USERS_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      VALIDATE(req.body, SCHEMA.USER_UPDATE);
      validateCrossTeamRequest({ permissionRequired: USERS_MANAGE_SUPER, teamId: req.body.team_id, res });

      const results = await userService.updateUser(req.params.id, req.body);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while updating user.`, err, next);
    }
  });

  router.delete('/:id', can([ USERS_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const results = await userService.deleteUser(req.params.id);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while deleting user ${req.params.id}.`, err, next);
    }
  });

  router.post('/:id/deactivate', can([ USERS_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const existingUser = await userService.getUser({ id: req.params.id });
      validateCrossTeamRequest({ permissionRequired: USERS_MANAGE_SUPER, teamId: existingUser[0].team_id, res });

      const deactivatedUser = await userService.setUserStatusActive(req.params.id, false);
      res.status(200).send(deactivatedUser);
    } catch (err) {
      ERROR_HANDLER(`Error while deactivating user ${req.params.id}.`, err, next);
    }
  });

  router.post('/:id/activate', can([ USERS_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const existingUser = await userService.getUser({ id: req.params.id });
      validateCrossTeamRequest({ permissionRequired: USERS_MANAGE_SUPER, teamId: existingUser[0].team_id, res });

      const deactivatedUser = await userService.setUserStatusActive(req.params.id);
      res.status(200).send(deactivatedUser);
    } catch (err) {
      ERROR_HANDLER(`Error while activating user ${req.params.id}.`, err, next);
    }
  });

  return router;
};
