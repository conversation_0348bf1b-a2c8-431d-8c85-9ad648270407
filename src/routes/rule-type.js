const express = require('express');
const { ERROR_HANDLER } = require('../error');

module.exports = (service) => {
  const router = express.Router();

  router.get('/', async(req, res, next) => {
    try {
      const results = await service.getRuleType(req.query);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER('Error while fetching rule types.', err, next);
    }
  });

  return router;
};
