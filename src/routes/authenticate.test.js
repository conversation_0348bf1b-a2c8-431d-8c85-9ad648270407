const {
  getHandler,
  resMock,
  reqMock,
} = require('./index.test');

const mockLogger = {
  warn: jest.fn(),
};

const router = require('./authenticate')(mockLogger);

describe('Login', () => {
  it('Logout', async() => {
    await getHandler(router, '/logout', 'get')(reqMock, resMock);
    expect(resMock.clearCookie).toHaveBeenCalled();
  });
  it('Should return error on failed to destroy session', async() => {
    const mockError = new Error('Failed to destroy session');
    const reqSessionMockDestroy = {
      isWamSsoEnabled: true,
      session: {
        destroy: jest.fn(callback => callback(mockError)),
      },
    };
    await getHandler(router, '/logout', 'get')(reqSessionMockDestroy, resMock);
    expect(mockLogger.warn).toHaveBeenCalled();
    expect(resMock.clearCookie).toHaveBeenCalled();
  });
});
