const {
  getHand<PERSON>,
  jsonResp,
  resMock,
  resMockResError,
  reqMock,
  nextMock,
  nextMockConsoleLog,
} = require('./index.test');

const testData = {
  name: 'test-name',
  description: 'test-description',
  rule_type: 'test-rule-type',
  content_type: 'test-content-type',
  pages: [],
  containerId: 'test-container-slug',
};
const mockService = {
  getContainer: jest.fn().mockReturnValue([ testData ]),
  createContainer: jest.fn().mockReturnValue(testData),
  updateContainer: jest.fn().mockReturnValue(testData),
  deleteContainer: jest.fn().mockReturnValue(testData),
};

const router = require('./container')(mockService);

describe('getContainer', () => {
  it('Success', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith([ testData ]);
    expect(mockService.getContainer).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMockResError, nextMock);
    expect(mockService.getContainer).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('getSingleContainer', () => {
  it('Success', async() => {
    await getHandler(router, '/:id', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.getContainer).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/:id', 'get')(reqMock, resMockResError, nextMock);
    expect(mockService.getContainer).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('createContainer', () => {
  it('Success', async() => {
    await getHandler(router, '/', 'post')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.createContainer).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/', 'post')(reqMock, resMockResError, nextMock);
    expect(mockService.createContainer).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('updateContainer', () => {
  it('Success', async() => {
    await getHandler(router, '/:id', 'patch')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.updateContainer).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/:id', 'patch')(reqMock, resMockResError, nextMock);
    expect(mockService.updateContainer).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('deleteContainer', () => {
  it('Success', async() => {
    await getHandler(router, '/:id', 'delete')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.deleteContainer).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/:id', 'delete')(reqMock, resMockResError, nextMock);
    expect(mockService.deleteContainer).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});
