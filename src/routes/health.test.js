const {
  getHand<PERSON>,
  jsonResp,
  resMock,
  reqMock,
} = require('./index.test');

const router = require('./health')();
const { name: appName, version: appVersion } = require('../../package.json');

describe('Health', () => {
  it('Success', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMock);
    expect(jsonResp.json).toHaveBeenCalledWith({ status: 'UP', serviceId: appName, version: appVersion });
  });
});
