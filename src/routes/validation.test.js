const {
  getH<PERSON><PERSON>,
  json<PERSON>esp,
  resMock,
  nextMock,
} = require('./index.test');

describe('Check Existence', () => {
  test('Missing required query params', async() => {
    const mockService = {
      checkExistence: jest.fn().mockReturnValue(true),
    };
    const reqMock = {
      query: {},
    };
    const router = require('./validation')(mockService);
    await getHandler(router, '/', 'get')(reqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalled();
    expect(mockService.checkExistence).not.toHaveBeenCalled();
  });

  test('Success', async() => {
    const mockService = {
      checkExistence: jest.fn().mockReturnValue(true),
    };
    const reqMock = {
      query: {
        key: 'userEmail',
        values: JSON.stringify([ 's1111111' ]),
      },
    };
    const router = require('./validation')(mockService);
    await getHand<PERSON>(router, '/', 'get')(reqMock, resMock, nextMock);
    expect(mockService.checkExistence).toHaveBeenCalled();
    expect(jsonResp.json).toHaveBeenCalledWith(true);
    expect(nextMock).not.toHaveBeenCalled();
  });

  test('Missing additional values for cross table validation', async() => {
    const mockService = {
      checkExistence: jest.fn().mockReturnValue(true),
    };
    const reqMock = {
      query: {
        key: 'roleName',
        values: JSON.stringify([ 'Admin' ]),
      },
    };
    const router = require('./validation')(mockService);
    await getHandler(router, '/', 'get')(reqMock, resMock, nextMock);
    expect(nextMock).toHaveBeenCalled();
  });
});
