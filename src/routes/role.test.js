const {
  getHandler,
  jsonResp,
  resMock,
  resMockResError,
  reqMock,
  nextMock,
  nextMockConsoleLog,
} = require('./index.test');

const testData = {
  name: 'test-name',
  permissions: [],
};
reqMock.body = testData;

const mockService = {
  getRole: jest.fn().mockReturnValue([ testData ]),
  createRole: jest.fn().mockReturnValue(testData),
  updateRole: jest.fn().mockReturnValue(testData),
  deleteRole: jest.fn().mockReturnValue(testData),
};

const router = require('./role')(mockService);

describe('getRole', () => {
  it('Success', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith([ testData ]);
    expect(mockService.getRole).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMockResError, nextMock);
    expect(mockService.getRole).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('getSingleRole', () => {
  it('Success', async() => {
    await getHandler(router, '/:id', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.getRole).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/:id', 'get')(reqMock, resMockResError, nextMock);
    expect(mockService.getRole).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('createRole', () => {
  it('Success', async() => {
    await getHandler(router, '/', 'post')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.createRole).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/', 'post')(reqMock, resMockResError, nextMock);
    expect(mockService.createRole).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('updateRole', () => {
  it('Success', async() => {
    await getHandler(router, '/:id', 'patch')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.updateRole).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/:id', 'patch')(reqMock, resMockResError, nextMock);
    expect(mockService.updateRole).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('deleteRole', () => {
  it('Success', async() => {
    await getHandler(router, '/:id', 'delete')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.deleteRole).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/:id', 'delete')(reqMock, resMockResError, nextMock);
    expect(mockService.deleteRole).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});
