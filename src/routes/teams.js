const express = require('express');
const { ERROR_HANDLER, BadRequestError } = require('../error');
const {
  middleware: can,
  PERMISSION_ADMIN,
  TEAMS_VIEW,
  TEAMS_VIEW_SUPER,
  TEAMS_MANAGE,
  USERS_VIEW,
  ROLES_VIEW,
  TEAMS_MANAGE_SUPER,
  ALERTS_VIEW,
  CAMPAIGNS_VIEW,
  CCAU_CAMPAIGNS_VIEW,
} = require('../permissions');
const { VALIDATE, SCHEMA } = require('../constants/validate');

module.exports = teamsService => {
  const router = express.Router();

  // retrieves list of teams
  // access is given to anyone with only view access to either users or roles pages
  // as these two access pages display team related info in addition to the main entity of the page
  // permissions roughly corresponds roughly to page level access than backend data access
  router.get('/', can([ TEAMS_VIEW, USERS_VIEW, ROLES_VIEW, ALERTS_VIEW, CAMPAIGNS_VIEW, CCAU_CAMPAIGNS_VIEW ], 'OR'), async(req, res, next) => {
    try {
      const results = await teamsService.getTeams(req.query);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error fetching teams`, err, next); // TODO upgrade to express-scribe logger
    }
  });

  router.get('/owners', can([ TEAMS_VIEW_SUPER ]), async(req, res, next) => {
    try {
      const results = await teamsService.getTeamOwners(req.query);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error fetching teams`, err, next);
    }
  });

  router.get('/:id', can([ TEAMS_VIEW, USERS_VIEW, ROLES_VIEW ]), async(req, res, next) => {
    try {
      const { permissions, team_id: teamId } = res.locals.user;
      const isSuperAdmin = permissions.some(permission => [ PERMISSION_ADMIN, TEAMS_VIEW_SUPER ].includes(permission));
      if (req.params.id && isNaN(req.params.id)) {
        throw new BadRequestError('This is not a valid id');
      }
      if (isSuperAdmin || teamId === Number(req.params.id)) {
        const result = await teamsService.getTeam(req.params.id);
        if (!result) {
          return res.status(404).send('Team not found');
        }
        res.status(200).json(result);
      } else {
        return res.status(403).send('Forbidden - User does not have permission to view this team');
      }
    } catch (err) {
      ERROR_HANDLER(`Error fetching team`, err, next);
    }
  });

  router.post('/', can([ TEAMS_MANAGE ]), async(req, res, next) => {
    try {
      const results = await teamsService.createTeam(req.body);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error creating team`, err, next);
    }
  });

  router.patch('/:id', can([ TEAMS_MANAGE ]), async(req, res, next) => {
    try {
      const results = await teamsService.updateTeam(req.params.id, req.body);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error updating team`, err, next);
    }
  });

  router.post('/:id/deactivate', can([ TEAMS_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const deactivatedTeam = await teamsService.setTeamStatusActive(req.params.id, false);
      res.status(200).send(deactivatedTeam);
    } catch (err) {
      ERROR_HANDLER(`Error while deactivating team ${req.params.id}`, err, next);
    }
  });

  router.post('/:id/activate', can([ TEAMS_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const deactivatedTeam = await teamsService.setTeamStatusActive(req.params.id, true, req.query);
      res.status(200).send(deactivatedTeam);
    } catch (err) {
      ERROR_HANDLER(`Error while activating team ${req.params.id}`, err, next);
    }
  });

  // Only used by integration tests
  router.delete('/:id', can([ TEAMS_MANAGE_SUPER ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const result = await teamsService.deleteTeam(req.params.id);
      res.status(200).json(result);
    } catch (err) {
      ERROR_HANDLER(`Error while deleting team`, err, next);
    }
  });

  return router;
};
