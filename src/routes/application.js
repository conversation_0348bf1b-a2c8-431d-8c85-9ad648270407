const express = require('express');
const { ERROR_HANDLER } = require('../error');
const {
  middleware: can,
  APPLICATIONS_VIEW,
  APPLICATIONS_VIEW_SUPER,
  APPLICATIONS_MANAGE,
  APPLICATIONS_MANAGE_SUPER,
  TEAMS_VIEW,
  CONTAINERS_VIEW,
  PAGES_VIEW,
  CAMPAIGNS_VIEW,
  CCAU_CAMPAIGNS_VIEW,
  ALERTS_VIEW,
} = require('../permissions');
const { validateCrossTeamRequest } = require('../permissions/utils');
const { VALIDATE, SCHEMA } = require('../constants/validate');

module.exports = (service) => {
  const router = express.Router();

  router.get('/', can([ APPLICATIONS_VIEW, TEAMS_VIEW, CONTAINERS_VIEW, PAGES_VIEW, CAMPAIGNS_VIEW, CCAU_CAMPAIGNS_VIEW, ALERTS_VIEW ], 'OR'), async(req, res, next) => {
    try {
      const results = await service.getApplications(req.query);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while fetching applications`, err, next);
    }
  });

  router.get('/:id', can([ APPLICATIONS_VIEW ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const results = await service.getApplication(req.params.id);
      validateCrossTeamRequest({ permissionRequired: APPLICATIONS_VIEW_SUPER, teamId: results.team_id, res });
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while fetching application ${req.params.id}`, err, next);
    }
  });

  router.post('/', can([ APPLICATIONS_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.body, SCHEMA.APPLICATION_CREATE);
      validateCrossTeamRequest({ permissionRequired: APPLICATIONS_MANAGE_SUPER, teamId: req.body.team_id, res });

      const result = await service.createApplication({
        ...req.body,
        status: req.body.status ? 1 : 0, // status to bit conversion
      });
      return res.status(200).json(result);
    } catch (err) {
      ERROR_HANDLER(`Error while saving a new application`, err, next);
    }
  });

  router.patch('/:id', can([ APPLICATIONS_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      VALIDATE(req.body, SCHEMA.APPLICATION_CREATE);
      validateCrossTeamRequest({ permissionRequired: APPLICATIONS_MANAGE_SUPER, teamId: req.body.team_id, res });

      const result = await service.updateApplication(req.params.id, {
        ...req.body,
        status: req.body.status ? 1 : 0, // status to bit conversion
      });
      return res.status(200).json(result);
    } catch (err) {
      ERROR_HANDLER(`Error while updating the application`, err, next);
    }
  });

  router.post('/:id/deactivate', async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const deactivatedTeam = await service.setApplicationStatus(req.params.id, false);
      res.status(200).send(deactivatedTeam);
    } catch (err) {
      ERROR_HANDLER(`Error while deactivating application ${req.params.id}`, err, next);
    }
  });

  router.post('/:id/activate', async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const deactivatedTeam = await service.setApplicationStatus(req.params.id, true, req.query);
      res.status(200).send(deactivatedTeam);
    } catch (err) {
      ERROR_HANDLER(`Error while activating application ${req.params.id}`, err, next);
    }
  });

  // Only used by integration tests
  router.delete('/:id', can([ APPLICATIONS_MANAGE_SUPER ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const result = await service.deleteApplication(req.params.id);
      res.status(200).json(result);
    } catch (err) {
      ERROR_HANDLER(`Error while deleting application ${req.params.id}`, err, next);
    }
  });

  return router;
};
