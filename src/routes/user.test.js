const {
  getHand<PERSON>,
  jsonResp,
  resMock,
  resMockResError,
  reqMock,
  nextMock,
  nextMockConsoleLog,
} = require('./index.test');

const testData = {
  name: 'test-name',
  email: '<EMAIL>',
  sid: 's000012',
  team_id: '1',
  id: '99',
  roles: [ 1, 3 ],
};
reqMock.body = testData;

const mockService = {
  getUser: jest.fn().mockReturnValue([ testData ]),
  getUsers: jest.fn().mockReturnValue([ testData ]),
  createUser: jest.fn().mockReturnValue(testData),
  updateUser: jest.fn().mockReturnValue(testData),
  deleteUser: jest.fn().mockReturnValue(testData),
};
const config = {
  wamSsoAuth: {
    logoutURL: 'wam-logout-url',
  },
};

const router = require('./user')(mockService, config);

describe('getUser', () => {
  it('Success', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith([ testData ]);
    expect(mockService.getUsers).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('Success with sid query', async() => {
    await getHandler(router, '/', 'get')({ ...reqMock,
      query: {
        sid: 's1',
      } }, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith([ testData ]);
    expect(mockService.getUser).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMockResError, nextMock);
    expect(mockService.getUsers).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('getCurrentUser', () => {
  it('Success', async() => {
    await getHandler(router, '/current', 'get')({ ...reqMock, isWamSsoEnabled: true }, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith({ data: { ...resMock.locals.user, auth_config: { wam_logout: 'wam-logout-url' } } });
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/current', 'get')(reqMock, resMockResError, nextMock);
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('getSingleUser', () => {
  it('Success', async() => {
    await getHandler(router, '/:id', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.getUser).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/:id', 'get')(reqMock, resMockResError, nextMock);
    expect(mockService.getUser).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('createUser', () => {
  it('Success', async() => {
    await getHandler(router, '/', 'post')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.createUser).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/', 'post')(reqMock, resMockResError, nextMock);
    expect(mockService.createUser).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('updateUser', () => {
  it('Success', async() => {
    await getHandler(router, '/:id', 'patch')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.updateUser).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/:id', 'patch')(reqMock, resMockResError, nextMock);
    expect(mockService.updateUser).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});

describe('deleteUser', () => {
  it('Success', async() => {
    await getHandler(router, '/:id', 'delete')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData);
    expect(mockService.deleteUser).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/:id', 'delete')(reqMock, resMockResError, nextMock);
    expect(mockService.deleteUser).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});
