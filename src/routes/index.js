const content = require('./content');

const application = require('./application');
const authenticate = require('./authenticate');
const container = require('./container');
const health = require('./health');
const page = require('./page');
const ruleType = require('./rule-type');
const role = require('./role');
const user = require('./user');
const platform = require('./platform');
const ruleSubType = require('./rule-sub-type');
const jwks = require('./jwks');
const teams = require('./teams');
const validation = require('./validation');

module.exports = {
  application,
  content,
  authenticate,
  container,
  health,
  page,
  ruleType,
  ruleSubType,
  role,
  user,
  platform,
  jwks,
  teams,
  validation,
};
