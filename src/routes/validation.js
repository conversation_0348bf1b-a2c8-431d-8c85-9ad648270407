const { Router } = require('express');
const {
  middleware: can,
  USERS_MANAGE,
} = require('../permissions');
const { ERROR_HANDLER } = require('../error');
const { VALIDATE, SCHEMA } = require('../constants/validate');
const { mapTableColumn } = require('../constants/dbMappingValidation');

// Allowed query params
/*
    Table: user, column: email, sid

    For post request -
    query: {
      key: 'role-name',
      values: ['Admin', 1]
    }
*/

module.exports = validationService => {
  const router = Router();

  router.get('/', can([ USERS_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.query, SCHEMA.UNIQUE_VALIDATION);
      const queries = mapTableColumn[req.query.key];
      const values = JSON.parse(req.query.values);
      if (queries.length !== values.length) {
        ERROR_HANDLER(`Error checking the validation - Incorrect values`, {}, next); // TODO upgrade to express-scribe logger
      }
      const result = await validationService.checkExistence(req.query.key, queries, values);
      res.status(200).json(result);
    } catch (err) {
      ERROR_HANDLER(`Error checking the validation`, err, next); // TODO upgrade to express-scribe logger
    }
  });

  return router;
};
