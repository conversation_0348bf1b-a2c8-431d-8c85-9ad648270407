const getHandler = (router, path, method) => {
  const stack = router.stack.find(i => {
    return i.route.path === path && i.route.methods[method];
  });
  return stack.route.stack[stack.route.stack.length - 1].handle;
};

const jsonResp = {
  json: jest.fn(),
};
const sendResp = {
  send: jest.fn(),
};
const resMock = {
  status: jest.fn().mockReturnValue({ ...jsonResp, ...sendResp }),
  redirect: jest.fn(),
  clearCookie: jest.fn(),
  locals: {
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
      access: {
        campaigns: {
          applications: {
            view: [ 'test-application-slug' ],
            manage: [],
          },
          containers: {
            view: [ 'test-container-slug' ],
            manage: [],
          },
          pages: {
            view: [ 'test-page-slug' ],
            manage: [],
          },
        },
        alerts: {
          applications: {
            view: [],
            manage: [],
          },
          containers: {
            view: [],
            manage: [],
          },
          pages: {
            view: [],
            manage: [],
          },
        } },
      sid: 's999999',
      team_id: 1,
    },
  },
};
const resMockForbidden = {
  ...resMock,
  locals: {
    user: {

    },
  },
};
const resMockResError = {
  ...resMock,
  status: code => { throw Error(code); },
};

const reqMock = {
  body: {},
  params: { id: 1 },
  query: {},

};
const nextMock = jest.fn();
const nextMockConsoleLog = jest.fn(console.log);

module.exports = {
  getHandler,
  jsonResp,
  resMock,
  resMockForbidden,
  resMockResError,
  reqMock,
  nextMock,
  nextMockConsoleLog,
};

const instance = require('.');
test('should create instances of required routes correctly', () => {
  expect(instance).toStrictEqual(expect.any(Object));
  expect(instance.application).toStrictEqual(expect.any(Function));
  expect(instance.content).toStrictEqual(expect.any(Function));
  expect(instance.authenticate).toStrictEqual(expect.any(Function));
  expect(instance.container).toStrictEqual(expect.any(Function));
  expect(instance.health).toStrictEqual(expect.any(Function));
  expect(instance.page).toStrictEqual(expect.any(Function));
  expect(instance.role).toStrictEqual(expect.any(Function));
  expect(instance.user).toStrictEqual(expect.any(Function));
  expect(instance.platform).toStrictEqual(expect.any(Function));
  expect(instance.ruleType).toStrictEqual(expect.any(Function));
  expect(instance.ruleSubType).toStrictEqual(expect.any(Function));
});
