const {
  getHand<PERSON>,
  jsonResp,
  resMock,
  reqMock,
  nextMockConsoleLog,
} = require('./index.test');

const mockPlatforms = [ {
  id: 1,
  slug: 'web',
  name: 'Web',
}, {
  id: 2,
  slug: 'android',
  name: 'android',
} ];

const mockService = {
  getPlatforms: jest.fn().mockReturnValue([ mockPlatforms ]),
};

const router = require('./platform')(mockService);

describe('platforms endpoints', () => {
  it('get', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith([ mockPlatforms ]);
    expect(mockService.getPlatforms).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });
});
