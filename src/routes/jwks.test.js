const {
  getH<PERSON><PERSON>,
  json<PERSON>esp,
  resMock,
  reqMock,
} = require('./index.test');

const publicKey = 'LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUUZuOW13Rm9Rd091Qi92M3VlUktXaldjcgpYTXlRMTJKa3lucDRLN25RYUVrR3M1SFQxWi9QanBiZ2l3NUZ1Y2dOZXRKY2hKZWFuZWFuODIzaFh0T2RTL2F5CnpKRU80U3dud1VCS2ROb3BMQ0d5Sk5vbVFLaHZicVFmeVBzc0F6SXVMM016OWhza0h6YnlFTHc5UTVkN1lnN0IKMG5xb0ZzQXdkcGs0RGdkcmd3SURBUUFCCi0tLS0tRU5EIFBVQkxJQyBLRVktLS0tLQo=';
const router = require('./jwks')({ publicKey });

describe('JWKS Url', () => {
  it('JWK Set', async() => {
    await getHandler(router, '/', 'get')(reqMock, resMock);
    // const result = resMock.calls[0][0];
    const result = jsonResp.json.mock.calls[0][0];
    expect(jsonResp.json).toHaveBeenCalled();
    expect(result).toHaveProperty('jwks');
    expect(result.jwks).toHaveProperty('keys');
    expect(Array.isArray(result.jwks.keys)).toBe(true);
    expect(result.jwks.keys.length).toEqual(1);
    const key = result.jwks.keys[0];
    expect(key).toHaveProperty('kty');
    expect(key).toHaveProperty('n');
    expect(key).toHaveProperty('e');
  });
});
