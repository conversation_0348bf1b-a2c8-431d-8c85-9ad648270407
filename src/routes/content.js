const express = require('express');
const path = require('path');
const util = require('util');
const { ERROR_HANDLER } = require('../error');
const { VALIDATE, SCHEMA } = require('../constants/validate');
const { contentful: contentfulConfig, space } = require('../constants/config');
const { mimeTypes } = require('../constants');

module.exports = (service) => {
  const router = express.Router();

  router.get('/spaces/:spaceId/types', async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.CONTENT);
      const contentTypes = await service.getContentTypes(req.params.spaceId);
      res.status(200).json(contentTypes.data);
    } catch (err) {
      ERROR_HANDLER('Error while fetching content types.', err, next);
    }
  });

  router.get('/spaces/:spaceId/types/:typeId', async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.CONTENT);
      const contentTypes = await service.getContentTypeDetails(req.params.spaceId, req.params.typeId);
      res.status(200).json(contentTypes.data);
    } catch (err) {
      ERROR_HANDLER(`Error while fetching ${req.params.typeId} content type.`, err, next);
    }
  });

  router.get('/spaces/:spaceId/types/:typeId/contents', async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.CONTENT);
      VALIDATE(req.query, SCHEMA.CONTENTS);
      const contentTypes = await service.getContents(req.params.spaceId, req.params.typeId, req.query);
      res.status(200).json(contentTypes.data);
    } catch (err) {
      ERROR_HANDLER(`Error while fetching ${req.params.typeId} contents.`, err, next);
    }
  });

  router.get('/spaces/:spaceId/types/:typeId/contents/:contentId', async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.CONTENT);
      VALIDATE(req.query, SCHEMA.CONTENT_DETAILS);
      const contentTypes = await service.getContentDetails(req.params.spaceId, req.params.typeId, req.params.contentId, req.query);
      res.status(200).json(contentTypes.data);
    } catch (err) {
      ERROR_HANDLER(`Error while fetching ${req.params.typeId} ${req.params.contentId}`, err, next);
    }
  });

  router.get('/spaces/:spaceId/locales', async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.CONTENT);
      const locales = await service.getContentLocales(req.params.spaceId);
      res.status(200).json(locales.data);
    } catch (err) {
      ERROR_HANDLER('Error while fetching locales.', err, next);
    }
  });

  router.get('/vignette/static', async(req, res, next) => {
    try {
      VALIDATE(req.query, SCHEMA.CONTENT_VIGNETTE_STATIC);
      const spaceToBranch = {
        'IST': 'ist',
        'UAT': 'uat',
        'PRD': 'main',
      };
      const image = await service.getStaticImageVignette(`PIGEONSOL/repos/${req.query.repo}/raw${req.query.path}?at=refs/heads/${spaceToBranch[space] || 'main'}`);
      const type = mimeTypes[path.extname(req.query.path).slice(1)] || 'text/plain';

      res.set('Content-Type', type);
      res.status(200).end(image);
    } catch (err) {
      ERROR_HANDLER(`Error while fetching ${req.params.typeId} ${req.params.contentId}`, err, next);
    }
  });

  router.get('/vignette/:content_id', async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.CONTENT_DETAILS_VIGNETTE);
      const content = await service.getContentDetailsVignette(req.params.content_id);
      res.status(200).json(content);
    } catch (err) {
      ERROR_HANDLER(`Error while fetching vignette content ${req.params.content_id}`, err, next);
    }
  });

  router.get('/vignette', async(req, res, next) => {
    try {
      VALIDATE(req.query, SCHEMA.CONTENT_VIGNETTE);
      const content = await service.getContentsVignette(req.query);
      res.status(200).json(content);
    } catch (err) {
      ERROR_HANDLER(`Error while fetching vignette contents: name=${req.query.name}, type=${req.query.type} `, err, next);
    }
  });

  router.get('/spaces/:spaceId/entries/contents/:contentId', (req, res, next) => {
    try {
      if ((/^[a-zA-Z0-9]+$/.test(req.params.spaceId)) && (/^[a-zA-Z0-9]+$/.test(req.params.contentId))) {
        res.writeHead(302, {
          Location: util.format(contentfulConfig.entryUrl, req.params.spaceId, req.params.contentId),
        }).end();
      } else {
        throw new Error('Invalid contentful space or content id');
      }
    } catch (err) {
      ERROR_HANDLER(`Error while redirecting to Contentful: spaceId=${req.params.spaceId}, contentId=${req.params.contentId} `, err, next);
    }
  });

  return router;
};
