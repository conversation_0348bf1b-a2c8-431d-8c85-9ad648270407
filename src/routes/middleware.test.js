const { nonProdMiddleware } = require('./middleware');
const { NotFoundError } = require('../error');
const { getDeploymentEnvironment } = require('../utils');

jest.mock('../utils', () => ({
  getDeploymentEnvironment: jest.fn(),
}));

describe('routes/middleware', () => {
  describe('nonProdMiddleware', () => {
    let req, res, next;

    beforeEach(() => {
      req = {};
      res = {};
      next = jest.fn();
      jest.clearAllMocks();
    });

    it('should call next() when environment is not production', () => {
      getDeploymentEnvironment.mockReturnValue('dev');

      const middleware = nonProdMiddleware();
      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith();
      expect(next).toHaveBeenCalledTimes(1);
    });

    it('should call next() when environment is staging', () => {
      getDeploymentEnvironment.mockReturnValue('stg');

      const middleware = nonProdMiddleware();
      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith();
      expect(next).toHaveBeenCalledTimes(1);
    });

    it('should call next() when environment is test', () => {
      getDeploymentEnvironment.mockReturnValue('test');

      const middleware = nonProdMiddleware();
      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith();
      expect(next).toHaveBeenCalledTimes(1);
    });

    it('should call next() with NotFoundError when environment is production', () => {
      getDeploymentEnvironment.mockReturnValue('prd');

      const middleware = nonProdMiddleware();
      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(NotFoundError));
      expect(next).toHaveBeenCalledTimes(1);
    });

    it('should call next() when environment is undefined', () => {
      getDeploymentEnvironment.mockReturnValue(undefined);

      const middleware = nonProdMiddleware();
      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith();
      expect(next).toHaveBeenCalledTimes(1);
    });

    it('should call next() when environment is empty string', () => {
      getDeploymentEnvironment.mockReturnValue('');

      const middleware = nonProdMiddleware();
      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith();
      expect(next).toHaveBeenCalledTimes(1);
    });

    it('should return a function when called', () => {
      const middleware = nonProdMiddleware();

      expect(typeof middleware).toBe('function');
      expect(middleware.length).toBe(3); // Express middleware should accept 3 parameters
    });
  });
});
