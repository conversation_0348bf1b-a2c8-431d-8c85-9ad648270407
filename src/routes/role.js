const express = require('express');
const { ERROR_HANDLER } = require('../error');
const {
  middleware: can,
  PERMISSION_ADMIN,
  ROLES_VIEW,
  ROLES_VIEW_SUPER,
  ROLES_MANAGE,
  ROLES_MANAGE_SUPER,
  USERS_VIEW,
  USERS_MANAGE_SUPER,
} = require('../permissions');
const { validateCrossTeamRequest } = require('../permissions/utils');
const { VALIDATE, SCHEMA } = require('../constants/validate');

module.exports = (service) => {
  const router = express.Router();

  router.get('/', can([ ROLES_VIEW, USERS_VIEW ], 'OR'), async(req, res, next) => {
    const permissions = res.locals.user.permissions;
    const isAdmin = permissions.includes(PERMISSION_ADMIN);
    // If user has access to manage all users, they need to be able view all roles
    const filter = (isAdmin || permissions.includes(ROLES_VIEW_SUPER) || permissions.includes(USERS_MANAGE_SUPER))
      ? {}
      : { team_id: res.locals.user.team_id };
    try {
      const results = await service.getRole({ ...req.query, ...filter });
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER('Error while fetching roles.', err, next);
    }
  });

  router.get('/:id', can([ ROLES_VIEW ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);

      const results = await service.getRole(req.params);
      validateCrossTeamRequest({ permissionRequired: ROLES_VIEW_SUPER, teamId: results[0].team_id, res });

      res.status(200).json(results[0]);
    } catch (err) {
      ERROR_HANDLER(`Error while fetching role ${req.params.id}`, err, next);
    }
  });

  router.post('/', can([ ROLES_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.body, SCHEMA.ROLE_CREATE);
      validateCrossTeamRequest({ permissionRequired: ROLES_MANAGE_SUPER, teamId: req.body.team_id, res });

      const results = await service.createRole(req.body);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while creating role.`, err, next);
    }
  });

  router.patch('/:id', can([ ROLES_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      VALIDATE(req.body, SCHEMA.ROLE_UPDATE);
      validateCrossTeamRequest({ permissionRequired: ROLES_MANAGE_SUPER, teamId: req.body.team_id, res });

      const results = await service.updateRole(req.params.id, req.body);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while updating role.`, err, next);
    }
  });

  // Only used by integration tests
  router.delete('/:id', can([ ROLES_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const results = await service.deleteRole(req.params.id);
      res.status(200).json(results);
    } catch (err) {
      ERROR_HANDLER(`Error while deleting role ${req.params.id}.`, err, next);
    }
  });

  router.post('/:id/deactivate', can([ ROLES_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const results = await service.getRole(req.params);
      validateCrossTeamRequest({ permissionRequired: ROLES_MANAGE_SUPER, teamId: results[0].team_id, res });

      const deactivatedRole = await service.setRoleStatusActive(req.params.id, false);
      res.status(200).send(deactivatedRole);
    } catch (err) {
      ERROR_HANDLER(`Error while deactivating role ${req.params.id}.`, err, next);
    }
  });

  router.post('/:id/activate', can([ ROLES_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      const results = await service.getRole(req.params);
      validateCrossTeamRequest({ permissionRequired: ROLES_MANAGE_SUPER, teamId: results[0].team_id, res });

      const deactivatedRole = await service.setRoleStatusActive(req.params.id);
      res.status(200).send(deactivatedRole);
    } catch (err) {
      ERROR_HANDLER(`Error while activating role ${req.params.id}.`, err, next);
    }
  });

  return router;
};
