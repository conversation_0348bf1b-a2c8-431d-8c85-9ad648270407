const UpdatePartialCampaign = require('./updatePartialCampaign');
const { CustomError } = require('../../error');

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

const mockCampaignManagementApi = {
  patchCampaign: jest.fn(),
};
// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn().mockReturnValue({ json: mockJson });
const mockRes = {
  status: mockStatus,
  locals: {
    validatedParams: {},
    validatedBody: {},
    auth: { sid: 's1234567' },
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
    },
  },
};
const mockParams = { campaignId: 1 };
const mockBody = {
  status: 'S',
  id: 1,
};
const mockResponse = {
  data: { id: mockParams.campaignId, ...mockBody },
};
describe('Patch /v1/message-centre/campaigns/:campaignId', () => {
  beforeEach(() => {
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
    mockLogger.info.mockClear();
    mockCampaignManagementApi.patchCampaign.mockClear();
  });
  test('Should not return an error if campaigns are returned', async() => {
    mockCampaignManagementApi.patchCampaign.mockResolvedValueOnce(mockResponse);
    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignManagementApi);

    await updatePartialCampaign({ params: mockParams, body: mockBody }, mockRes, mockNext);
    expect(mockCampaignManagementApi.patchCampaign).toBeCalled();
    expect(mockCampaignManagementApi.patchCampaign).toBeCalledTimes(1);
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('Should return an error when campaign management API is unavailable', async() => {
    const mockResponse = {
      response: {
        status: 400,
        data: {
          code: 'HTTP_BAD_REQUEST',
          message: 'Database error',
          uuid: '04d69d4d-4c41-4373-b7f5-9b351a1f345d',
          timestamp: '2018-08-28T20:43:59.319Z',
          metadata: [ 'The duplicate key value is (sample name #38).' ],
        },
      },
    };
    mockCampaignManagementApi.patchCampaign.mockRejectedValueOnce(mockResponse);
    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignManagementApi);

    await updatePartialCampaign({ params: mockParams, body: mockBody }, mockRes, mockNext);
    expect(mockStatus).toHaveBeenCalledTimes(0);
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });
});
