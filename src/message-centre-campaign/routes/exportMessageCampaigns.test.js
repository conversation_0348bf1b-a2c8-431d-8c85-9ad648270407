const request = require('supertest');
const express = require('express');
const exportMessageCampaigns = require('./exportMessageCampaigns');

// Mock the campaignManagementService with a getAllCampaigns function
const mockCampaignManagementService = {
  getAllCampaigns: jest.fn().mockResolvedValue({
    items: [
      {
        campaign_type: 'Email',
        kt_campaign_id: '123',
        campaign_channel: 'Online',
        language: 'EN',
        msg_status: 'Active',
        msg_priority: 'High',
        start_date: '2024-01-01',
        msg_gen_date: '2024-01-02',
        name: 'New Year Campaign',
      },
    ],
  }),
};

// Setup the Express server and route for testing
const app = express();
app.use(express.json());
app.use('/export-campaigns', exportMessageCampaigns(mockCampaignManagementService));

describe('exportMessageCampaigns', () => {
  it('should download a CSV file with the correct content', async() => {
    const response = await request(app)
      .get('/export-campaigns')
      .expect('Content-Type', /text\/csv/)
      .expect('Content-Disposition', /attachment; filename="campaigns-list-export.csv"/)
      .expect(200);

    // Check if the CSV string matches expected output
    expect(response.text).toContain('Type,ID,Channel,Language,Status,Priority,Start Date,End Date,Campaign Name');
    expect(response.text).toContain('Email,123,Online,EN,Active,High,2024-01-01,2024-01-02,New Year Campaign');
  });

  it('should handle errors correctly', async() => {
    // Mock the service to throw an error
    mockCampaignManagementService.getAllCampaigns.mockRejectedValueOnce(new Error('Service error'));

    const response = await request(app)
      .get('/export-campaigns')
      .expect(500);

    // Check if the error message is correct
    expect(response.body).toEqual(expect.anything()); // Update this based on how you handle errors
  });
});
