
const mockMessageCentreCampaign = require('../../__mocks__/mockMessageCentreCampaign');
const { CustomError } = require('../../error');
const {
  schemaValidationMiddleware,
  VALIDATION_TYPE,
  getMessageCentreCampaignsSchema,
  createSchema,
  DEFAULT_OPTS,
  getPathSchema,
} = require('./validation');

describe('Campaign routes validation', () => {
  const req = {};
  const res = {};
  const nextMock = jest.fn();

  beforeEach(() => {
    nextMock.mockClear();
  });

  it('schemaValidationMiddleware - should throw error if invalid param type passed', () => {
    schemaValidationMiddleware(getPathSchema, 'invalid-type')(req, res, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
    expect(nextMock.mock.calls[0][0].message).toBe(`Invalid joi validation type, must be: body, params, or query`);
  });

  it('schemaValidationMiddleware - should throw error if fails validation schema', () => {
    const localReq = {
      body: { campaign_type: '' },
    };
    schemaValidationMiddleware(createSchema, 'body')(localReq, res, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(2);
    expect(nextMock.mock.calls[0][0].message).toBe(`ValidationError: "campaign_type" must be one of [ABM, SOL, MOB, FFT, 000].`);
  });

  it('schemaValidationMiddleware - should throw error if invalid param type passed', () => {
    schemaValidationMiddleware(getPathSchema, 'invalid-type')(req, res, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
    expect(nextMock.mock.calls[0][0].message).toBe(`Invalid joi validation type, must be: body, params, or query`);
  });

  it('schemaValidationMiddleware - should pass validation - params', () => {
    const localReq = {
      params: { id: '12345' },
    };
    const localRes = {
      locals: {},
    };
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS)(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedParams).toEqual({ id: '12345' });
  });

  it('schemaValidationMiddleware - should pass validation - query', () => {
    const localReq = {
      query: {
        limit: '10',
        offset: '0',
      },
    };
    const localRes = {
      locals: {
        validatedQuery: {
          ...localReq.query,
          limit: 10,
          offset: 0,
        },
      },
    };
    schemaValidationMiddleware(getMessageCentreCampaignsSchema, VALIDATION_TYPE.QUERY)(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedQuery).toEqual(
      {
        ...localReq.query,
        // schema converts string to numbers
        limit: 10,
        offset: 0,
      },
    );
  });

  it('schemaValidationMiddleware - should pass validation - body', () => {
    const localReq = {
      body: mockMessageCentreCampaign,
    };
    const localRes = {
      locals: {
        validatedBody: { ...localReq.body },
      },
    };
    schemaValidationMiddleware(createSchema, undefined, DEFAULT_OPTS)(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedBody).toEqual(localReq.body);
  });
});
