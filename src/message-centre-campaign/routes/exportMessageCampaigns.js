const { CustomError } = require('../../error');

const SHEET_HEADERS = [
  'Type',
  'ID',
  'Channel',
  'Language',
  'Status',
  'Priority',
  'Start Date',
  'End Date',
  'Campaign Name',
];

const exportMessageCampaigns = (campaignManagementService) => {
  return async(req, res, next) => {
    try {
      const query = res.locals.validatedQuery;
      const { items } = await campaignManagementService.getAllCampaigns({
        ...query,
        offset: 0,
        limit: 0,
      });
      const csvHeadings = `${SHEET_HEADERS.toString()}\n`;

      const csvString = items.reduce((acc, campaign) => {
        acc += `${[ campaign.campaign_type,
          campaign.kt_campaign_id,
          campaign.campaign_channel,
          campaign.language,
          campaign.msg_status,
          campaign.msg_priority,
          campaign.start_date,
          campaign.msg_gen_date,
          campaign.name,
        ].toString()}\n`;
        return acc;
      }, csvHeadings);

      res.set('Content-Type', 'text/csv');
      res.attachment(`campaigns-list-export.csv`);
      res.send(csvString);
    } catch (err) {
      next(new CustomError(500, err.toString()));
    }
  };
};

module.exports = exportMessageCampaigns;
