const express = require('express');
const { wrapAsync } = require('../../utils');

const getCampaign = require('./getCampaign');
const getCampaigns = require('./getCampaigns');
const createCampaign = require('./createCampaign');
const updateCampaign = require('./updateCampaign');
const updatePartialCampaign = require('./updatePartialCampaign');
const exportMessageCampaigns = require('./exportMessageCampaigns.js');
const deleteCampaign = require('./deleteCampaign');

const {
  VALIDATION_TYPE,
  getMessageCentreCampaignsSchema,
  schemaValidationMiddleware,
  createSchema,
  getPathSchema,
  optionalGetPathSchema,
  updateSchema,
  updatePartialSchema,
  exportMessageCentreCampaignsSchema,
} = require('./validation');

const init = (config, logger, campaignManagementService) => {
  const router = express.Router();

  router.get(
    '/campaigns',
    schemaValidationMiddleware(getMessageCentreCampaignsSchema, VALIDATION_TYPE.QUERY),
    wrapAsync(getCampaigns(campaignManagementService)),
  );

  router.get(
    '/campaigns/export',
    schemaValidationMiddleware(exportMessageCentreCampaignsSchema, VALIDATION_TYPE.QUERY),
    wrapAsync(exportMessageCampaigns(campaignManagementService)),
  );

  router.get(
    '/campaigns/:id',
    schemaValidationMiddleware(optionalGetPathSchema, VALIDATION_TYPE.PARAMS),
    wrapAsync(getCampaign(logger, campaignManagementService)),
  );

  router.post(
    '/campaigns',
    schemaValidationMiddleware(createSchema, VALIDATION_TYPE.BODY),
    wrapAsync(createCampaign(logger, campaignManagementService)),
  );

  router.put(
    '/campaigns/:id',
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS),
    schemaValidationMiddleware(updateSchema, VALIDATION_TYPE.BODY),
    wrapAsync(updateCampaign(logger, campaignManagementService)),
  );

  router.patch(
    '/campaigns/:id',
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS),
    schemaValidationMiddleware(updatePartialSchema, VALIDATION_TYPE.BODY),
    wrapAsync(updatePartialCampaign(logger, campaignManagementService)),
  );

  router.delete(
    '/campaigns/:id',
    schemaValidationMiddleware(optionalGetPathSchema, VALIDATION_TYPE.PARAMS),
    wrapAsync(deleteCampaign(logger, campaignManagementService)),
  );

  return router;
};

module.exports = init;
