const { CustomError } = require('../../error');

const getCampaigns = (campaignManagementService) => {
  return async(req, res, next) => {
    try {
      const query = res.locals.validatedQuery;
      const campaigns = await campaignManagementService.getAllCampaigns(query);
      res.status(200).json(campaigns);
    } catch (err) {
      if (err.response) {
        if (err.response.status < 500) {
          res.status(err.response.status).json(err.response.data);
        } else {
          next(new CustomError(err.response.status, JSON.stringify(err.response.data)));
        }
      } else {
        next(new CustomError(500, err.toString()));
      }
    }
  };
};

module.exports = getCampaigns;
