const mockMessageCentreCampaign = require('../../__mocks__/mockMessageCentreCampaign');
const { CustomError } = require('../../error');
const GetCampaign = require('./getCampaign');

const mockCampaignManagementService = {
  getCampaign: jest.fn(),
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();

mockStatus.mockReturnValue({ json: mockJson });

describe('Campaign Rules: routes > getCampaign', () => {
  beforeEach(() => {
    mockCampaignManagementService.getCampaign.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });

  test('Should return valid response - get campaign', async() => {
    const mockReq = {
      params: {
        campaignId: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: {
          campaignId: 1,
        },
      },
    };
    mockCampaignManagementService.getCampaign.mockReturnValueOnce(mockMessageCentreCampaign);

    const getCampaign = GetCampaign(mockLogger, mockCampaignManagementService);
    await getCampaign(mockReq, mockRes, mockNext);
    expect(mockCampaignManagementService.getCampaign).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('should handle error less than 500 response status', async() => {
    const mockReq = {
      params: {
        campaignId: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: 1,
      },
    };
    const fakeResponse = {
      response: {
        status: 401,
        data: {},
      },
    };
    mockCampaignManagementService.getCampaign.mockRejectedValue(fakeResponse);
    const getCampaign = GetCampaign(mockLogger, mockCampaignManagementService);
    await getCampaign(mockReq, mockRes, mockNext);
    expect(mockCampaignManagementService.getCampaign).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(0);
    expect(mockRes.status).toHaveBeenCalledWith(401);
  });

  test('should call next with Error on 500 response status', async() => {
    const mockReq = {
      params: {
        campaignId: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: {
          campaignId: 1,
        },
      },
    };
    const fakeResponse = {
      response: {
        status: 500,
        data: {
          code: 'HTTP_INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
        },
      },
    };
    mockCampaignManagementService.getCampaign.mockRejectedValue(fakeResponse);
    const getCampaign = GetCampaign(mockLogger, mockCampaignManagementService);
    await getCampaign(mockReq, mockRes, mockNext);
    expect(mockCampaignManagementService.getCampaign).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });

  test('should handle unknown error', async() => {
    const mockReq = {
      params: {
        campaignId: 1,
      },
    };
    const res = {
      status: mockStatus,
      locals: {
        validatedParams: {
          campaignId: 1,
        },
      },
    };
    mockCampaignManagementService.getCampaign.mockRejectedValue(new Error('unknown'));
    const updateCampaign = GetCampaign(mockLogger, mockCampaignManagementService);
    await updateCampaign(mockReq, res, mockNext);
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });
});
