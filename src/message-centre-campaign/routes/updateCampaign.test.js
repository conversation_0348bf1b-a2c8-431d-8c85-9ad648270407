const mockMessageCentreCampaign = require('../../__mocks__/mockMessageCentreCampaign');
const { CustomError } = require('../../error');
const UpdateCampaign = require('./updateCampaign');

const mockCampaignManagementService = {
  putCampaign: jest.fn(),
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();
const mockRes = {
  status: mockStatus,
  locals: {
    auth: { sid: 's1234567' },
    validatedBody: {},
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
      sid: 's1234567',
    },
  },
};

mockStatus.mockReturnValue({ json: mockJson });

describe('Campaign Rules: routes > updateCampaign', () => {
  beforeEach(() => {
    mockCampaignManagementService.putCampaign.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });

  test('Should return valid response - update campaign', async() => {
    const mockBody = {
      ...mockMessageCentreCampaign,
    };
    const mockReq = {
      body: mockBody,
      locals: {
        user: {
          sid: 's1234567',
        },
      },
    };

    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: { ...mockBody, long_name: 'updated_long_name' },
        validatedParams: {
          campaignId: 1,
        },
      },
    };

    const fakeResponse = {
      status: 200,
      data: {
        ...mockMessageCentreCampaign,
        long_name: 'updated_long_name',
      },
    };

    mockCampaignManagementService.putCampaign.mockReturnValueOnce(fakeResponse);
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignManagementService);
    await updateCampaign(mockReq, res, mockNext);
    expect(mockCampaignManagementService.putCampaign).toBeCalled();
  });

  it('should handle error on less than 500 response status', async() => {
    const mockReq = {
      params: {
        campaignId: 1,
      },
      body: mockMessageCentreCampaign,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockMessageCentreCampaign,
        validatedParams: {
          campaignId: 1,
        },
      },
    };
    const fakeResponse = {
      response: {
        status: 401,
        data: {},
      },
    };
    mockCampaignManagementService.putCampaign.mockRejectedValue(fakeResponse);
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignManagementService);
    await updateCampaign(mockReq, res, mockNext);
    expect(mockNext).toHaveBeenCalledTimes(0);
    expect(res.status).toHaveBeenCalledWith(401);
  });

  it('should call next with Error on 500+ response status', async() => {
    const mockReq = {
      params: {
        id: 1,
      },
      body: mockMessageCentreCampaign,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockMessageCentreCampaign,
        validatedParams: {
          campaignId: 1,
        },
      },
    };
    const fakeResponse = {
      response: {
        status: 500,
        data: {
          code: 'HTTP_INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
        },
      },
    };

    mockCampaignManagementService.putCampaign.mockRejectedValue(fakeResponse);
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignManagementService);
    await updateCampaign(mockReq, res, mockNext);
    expect(mockCampaignManagementService.putCampaign).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });

  test('should handle unknown error', async() => {
    const mockReq = {
      params: {
        id: 1,
      },
      body: mockMessageCentreCampaign,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockMessageCentreCampaign,
        validatedParams: {
          campaignId: 1,
        },
      },
    };
    mockCampaignManagementService.putCampaign.mockRejectedValue(new Error('unknown'));
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignManagementService);
    await updateCampaign(mockReq, res, mockNext);
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });
});
