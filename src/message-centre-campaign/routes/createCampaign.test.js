const mockMessageCentreCampaign = require('../../__mocks__/mockMessageCentreCampaign');
const CreateCampaign = require('./createCampaign');

const mockCampaignManagementService = {
  createCampaign: jest.fn(),
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();
mockStatus.mockReturnValue({ json: mockJson });
const mockRes = {
  status: mockStatus,
  locals: {
    auth: { sid: 's1234567' },
    validatedBody: {},
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
      sid: 's1234567',
    },
  },
};

describe('Campaign Rules: routes > createCampaign', () => {
  beforeEach(() => {
    mockCampaignManagementService.createCampaign.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });

  test('Should return valid response - creating campaign', async() => {
    const mockBody = {
      'campaign_channel': 'ABM',
      'campaign_duration': '2',
      'campaign_id': 'TEST1',
      'campaign_long_name': 'this is a long name',
      'campaign_short_name': 'short name',
      'campaign_type': '000',
      'end_at': '2024-05-27T04:00:00.000Z',
      'language': 'English',
      'start_at': '2024-05-01T04:00:00.000Z',
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      status: 200,
      data: mockBody,
    };

    mockCampaignManagementService.createCampaign.mockReturnValueOnce(fakeResponse);

    const createCampaign = CreateCampaign(mockLogger, mockCampaignManagementService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignManagementService.createCampaign).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('should handle error on less than 400 response status', async() => {
    const mockReq = {
      body: mockMessageCentreCampaign,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockMessageCentreCampaign,
      },
    };

    const fakeResponse = {
      response: {
        status: 401,
        data: {},
      },
    };

    mockCampaignManagementService.createCampaign.mockRejectedValue(fakeResponse);

    const createCampaign = CreateCampaign(mockLogger, mockCampaignManagementService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignManagementService.createCampaign).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(0);
    expect(res.status).toHaveBeenCalledWith(401);
  });

  test('should call next with Error on 500+ response status', async() => {
    const mockBody = {
      'campaign_channel': 'ABM',
      'campaign_duration': '2',
      'campaign_id': 'TEST1',
      'campaign_long_name': 'this is a long name',
      'campaign_short_name': 'short name',
      'campaign_type': '000',
      'end_at': '2024-05-27T04:00:00.000Z',
      'language': 'English',
      'start_at': '2024-05-01T04:00:00.000Z',
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      response: {
        status: 500,
        data: {
          code: 'HTTP_INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
        },
      },
    };

    mockCampaignManagementService.createCampaign.mockRejectedValue(fakeResponse);

    const createCampaign = CreateCampaign(mockLogger, mockCampaignManagementService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignManagementService.createCampaign).toBeCalled();
    expect(mockNext).toBeCalled();
  });

  test('should handle unknown error', async() => {
    const mockReq = {
      body: mockMessageCentreCampaign,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockMessageCentreCampaign,
      },
    };

    mockCampaignManagementService.createCampaign.mockRejectedValue(new Error('unknown'));

    const createCampaign = CreateCampaign(mockLogger, mockCampaignManagementService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignManagementService.createCampaign).toBeCalled();
    expect(mockNext).toBeCalled();
  });
});
