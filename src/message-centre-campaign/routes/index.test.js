const campaignIndex = require('.');
const config = require('../../constants/config');

describe('/campaigns route', () => {
  const campaignServiceMock = { getAllCampaigns: jest.fn(), createCampaign: jest.fn() };
  test('should return valid routes', () => {
    const logger = { error: jest.fn() };
    const router = campaignIndex(config, logger, {}, {}, campaignServiceMock);
    const routes = [
      '/campaigns',
      '/campaigns/export',
      '/campaigns/:id',
    ];
    routes.forEach((route, index) => {
      expect(router.stack[index].route.path).toEqual(route);
    });
  });
});
