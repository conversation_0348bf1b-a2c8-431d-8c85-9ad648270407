const { CustomError } = require('../../error');

const createCampaign = (logger, campaignManagementService) => {
  return async(req, res, next) => {
    try {
      const value = res.locals.validatedBody;
      const sid = res.locals.user && res.locals.user.sid;

      value.updated_user_id = sid;
      value.updated_date = new Date();
      value.msg_gen_date = new Date();

      const newCampaign = await campaignManagementService.createCampaign(value);
      res.status(200).json(newCampaign);
    } catch (error) {
      if (error.response) {
        if (error.response.status < 500) {
          res.status(error.response.status).json(error.response.data);
        } else {
          next(new CustomError(error.response.status, JSON.stringify(error.response.data)));
        }
      } else {
        next(new CustomError(500, error.toString()));
      }
    }
  };
};

module.exports = createCampaign;
