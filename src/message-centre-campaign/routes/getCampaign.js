const { CustomError } = require('../../error');

const getCampaign = (logger, campaignManagementService) => {
  return async(req, res, next) => {
    const params = res.locals.validatedParams;
    try {
      const data = await campaignManagementService.getCampaign(params.id);
      return res.status(200).json(data);
    } catch (error) {
      if (error.response) {
        if (error.response.status < 500) {
          res.status(error.response.status).json(error.response.data);
        } else {
          next(new CustomError(error.response.status, JSON.stringify(error.response.data)));
        }
      } else {
        next(new CustomError(500, error.toString()));
      }
    }
  };
};

module.exports = getCampaign;
