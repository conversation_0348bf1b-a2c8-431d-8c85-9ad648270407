const { CustomError } = require('../../error');

const updateCampaign = (logger, campaignManagementService) => {
  return async(req, res, next) => {
    try {
      const params = res.locals.validatedParams;
      const value = res.locals.validatedBody;
      const sid = res.locals.user && res.locals.user.sid;

      value.updated_user_id = sid;
      value.updated_date = new Date();

      const updated = await campaignManagementService.putCampaign(
        params.id,
        value,
      );

      res.status(200).json(updated);
    } catch (error) {
      if (error.response) {
        if (error.response.status < 500) {
          res.status(error.response.status).json(error.response.data);
        } else {
          next(new CustomError(error.response.status, JSON.stringify(error.response.data)));
        }
      } else {
        next(new CustomError(500, error.toString()));
      }
    }
  };
};

module.exports = updateCampaign;
