const { CustomError } = require('../../error');

const updatePartialCampaign = (logger, campaignManagementService) => async(req, res, next) => {
  // get campaign from ruleID
  const params = res.locals.validatedParams;
  const body = res.locals.validatedBody;
  // update campaign
  try {
    const campaign = await campaignManagementService.patchCampaign(params.id, body);
    return res.status(200).json(campaign.data);
  } catch (err) {
    logger.error({ message: err.messsage, err: { code: err.code, stack: err.stack } });
    if (err.response) { // the request was made and a response was received
      return next(new CustomError(err.response.status, JSON.stringify(err.response.data)));
    } else if (err.request) { // the req  uest was made and there is no response
      return next(new CustomError(500, JSON.stringify(err.request)));
    } else {
      return next(new CustomError(500, JSON.stringify(err.message)));
    }
  }
};

module.exports = updatePartialCampaign;
