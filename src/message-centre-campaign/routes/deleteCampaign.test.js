const { CustomError } = require('../../error');
const DeleteCampaign = require('./deleteCampaign');

const mockCampaignManagementService = {
  deleteCampaign: jest.fn(),
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();

mockStatus.mockReturnValue({ json: mockJson });

describe('Campaign Rules: routes > deleteCampaign', () => {
  beforeEach(() => {
    mockCampaignManagementService.deleteCampaign.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });

  test('Should return valid response - get campaign', async() => {
    const mockReq = {
      params: {
        campaignId: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: {
          campaignId: 1,
        },
      },
    };
    mockCampaignManagementService.deleteCampaign.mockReturnValueOnce({ data: null,
      notifications: [ { message: `Campaign with id: ${mockReq.params.campaignId} has been deleted successfully` } ] });

    const deleteCampaign = DeleteCampaign(mockLogger, mockCampaignManagementService);
    await deleteCampaign(mockReq, mockRes, mockNext);
    expect(mockCampaignManagementService.deleteCampaign).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('should handle error less than 500 response status', async() => {
    const mockReq = {
      params: {
        campaignId: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: 1,
      },
    };
    const fakeResponse = {
      response: {
        status: 401,
        data: {},
      },
    };
    mockCampaignManagementService.deleteCampaign.mockRejectedValue(fakeResponse);
    const deleteCampaign = DeleteCampaign(mockLogger, mockCampaignManagementService);
    await deleteCampaign(mockReq, mockRes, mockNext);
    expect(mockCampaignManagementService.deleteCampaign).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(0);
    expect(mockRes.status).toHaveBeenCalledWith(401);
  });

  test('should call next with Error on 500 response status', async() => {
    const mockReq = {
      params: {
        campaignId: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: {
          campaignId: 1,
        },
      },
    };
    const fakeResponse = {
      response: {
        status: 500,
        data: {
          code: 'HTTP_INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
        },
      },
    };
    mockCampaignManagementService.deleteCampaign.mockRejectedValue(fakeResponse);
    const deleteCampaign = DeleteCampaign(mockLogger, mockCampaignManagementService);
    await deleteCampaign(mockReq, mockRes, mockNext);
    expect(mockCampaignManagementService.deleteCampaign).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });

  test('should handle unknown error', async() => {
    const mockReq = {
      params: {
        campaignId: 1,
      },
    };
    const res = {
      status: mockStatus,
      locals: {
        validatedParams: {
          campaignId: 1,
        },
      },
    };
    mockCampaignManagementService.deleteCampaign.mockRejectedValue(new Error('unknown'));
    const updateCampaign = DeleteCampaign(mockLogger, mockCampaignManagementService);
    await updateCampaign(mockReq, res, mockNext);
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });
});
