const Joi = require('joi');
const { CustomError, ERROR_HANDLER } = require('../../error');
const {
  CAMPAIGN_TYPES,
  CAMPAIGN_CHANNELS,
  CAMPAIGN_LANGUAGES,
  CAMPAIGN_DISPLAY_PARMS,
  MESSAGE_STATUS,
  MESSAGE_PRIORITY,
} = require('./constants');

const DEFAULT_OPTS = { stripUnknown: true };

const VALIDATION_TYPE = {
  BODY: 'body',
  PARAMS: 'params',
  QUERY: 'query',
};

const id = Joi.string().alphanum();

const schemaValidationMiddleware = (schema, type = VALIDATION_TYPE.BODY, opts = DEFAULT_OPTS) => (req, res, next) => {
  if (!Object.values(VALIDATION_TYPE).includes(type)) {
    return next(new CustomError(500, 'Invalid joi validation type, must be: body, params, or query'));
  }
  const { error, value } = schema.validate(req[type], opts);
  if (error) {
    return next(ERROR_HANDLER(`Joi validation error on req.${type}`, error, next));
  }

  switch (type) {
    case VALIDATION_TYPE.BODY:
      res.locals.validatedBody = value;
      break;
    case VALIDATION_TYPE.PARAMS:
      res.locals.validatedParams = value;
      break;
    case VALIDATION_TYPE.QUERY:
      res.locals.validatedQuery = value;
      break;
    /* istanbul ignore next */
    default:
      break;
  }
  next();
};

// get campaign by its id
const optionalGetPathSchema = Joi.object()
  .keys({ id });

const campaignSchema = {
  'campaign_type': Joi.string().valid(...Object.values(CAMPAIGN_TYPES)).required(),
  'kt_campaign_id': Joi.string().length(5).required(),
  'campaign_channel': Joi.string().valid(...Object.values(CAMPAIGN_CHANNELS)).required(),
  'language': Joi.string().valid(...Object.values(CAMPAIGN_LANGUAGES)).required(),
  'start_date': Joi.date().iso().raw(true),
  'end_date': Joi.date().iso().raw(true),
  'updated_date': Joi.date().iso().raw(true),
  'name': Joi.string().max(31).required(),
  'long_name': Joi.string().allow(null),
  'duration': Joi.number().max(99).allow(null),
  'msg_category': Joi.string().length(3),
  'msg_status': Joi.string().valid(...Object.values(MESSAGE_STATUS)),
  'msg_priority': Joi.string().length(1),
  'msg_text': Joi.string().allow(null),
  'url_params': Joi.array().items(Joi.string().valid(...Object.values(CAMPAIGN_DISPLAY_PARMS))),
  'static_url_ind': Joi.string().allow(null),
  'static_url_text': Joi.string().allow(null),
  'updated_user_id': Joi.string().allow(''),
};

const createSchema = Joi.object().keys(Object.assign({
  ...campaignSchema,
})).fork([ 'campaign_type', 'kt_campaign_id', 'campaign_channel', 'name', 'start_date', 'end_date' ], (schema) => schema.required());

const updateSchema = Joi.object()
  .keys({ ...campaignSchema })
  .fork([ 'campaign_type', 'kt_campaign_id', 'campaign_channel', 'name', 'start_date', 'end_date' ], (schema) => schema.required());

// get campaign by its id
const getPathSchema = Joi.object()
  .keys({ id })
  .fork([ 'id' ], (schema) => schema.required());

const updatePartialSchema = Joi.object()
  .keys({ msg_status: campaignSchema.msg_status, disabled: Joi.bool() })
  .fork([ 'msg_status' ], (schema) => schema.required());

const exportMessageCentreCampaignsSchema = Joi.object()
  .keys({
    search: Joi.string().min(1).max(100).allow(null, ''),
    type: Joi.string().min(1).max(100).allow(...Object.values(CAMPAIGN_TYPES), null, ''),
    channel: Joi.string().min(1).max(100).allow(...Object.values(CAMPAIGN_CHANNELS), null, ''),
    language: Joi.string().allow(...Object.values(CAMPAIGN_LANGUAGES), null, ''),
    status: Joi.string().allow(...Object.values(MESSAGE_STATUS), null, ''),
    priority: Joi.string().allow(...Object.values(MESSAGE_PRIORITY), null, ''),
    start_date: Joi.date().iso().raw(true),
    end_date: Joi.date().iso().raw(true),
    updated_date: Joi.date().iso().raw(true) });

// get list of campaigns
const getMessageCentreCampaignsSchema = exportMessageCentreCampaignsSchema.keys({
  offset: Joi.number().min(0).default(0),
  limit: Joi.number().min(1).default(50),
  end_date_gt: Joi.date().iso().raw(true),
});

// get list of messages
const getMessageCentreMessagesSchema = Joi.object()
  .keys({
    offset: Joi.number().min(0).default(0),
    limit: Joi.number().min(1).default(50),
  });

module.exports = {
  DEFAULT_OPTS,
  VALIDATION_TYPE,
  getMessageCentreCampaignsSchema,
  getPathSchema,
  optionalGetPathSchema,
  schemaValidationMiddleware,
  exportMessageCentreCampaignsSchema,
  createSchema,
  updateSchema,
  updatePartialSchema,
  getMessageCentreMessagesSchema,
};
