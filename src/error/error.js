const codes = require('http').STATUS_CODES;
const uuid = require('uuid/v4');
const xss = require('xss');

class CustomError extends Error {
  constructor(code, message = codes[code], clientMessage) {
    super(message);
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
    this.code = code;
    this.uuid = uuid();
    this.timestamp = (new Date()).toISOString();
    this.errorMessage = clientMessage;
  }
}

class BadRequestError extends CustomError {
  constructor(message) {
    super(400, message);
    // errorMessage appears to be client facing message property
    // this preserves original error message for logging purpose
    // in order to prevent exposure of internal workings
    // while still supporting useful customer facing messaging
    // TODO refactor naming convention for better readability
    this.errorMessage = message;
  }
}

class UnauthorizedError extends CustomError {
  constructor() {
    super(401, 'Unauthorized', 'You need to sign in to view this page.');
  }
}

class ForbiddenError extends CustomError {
  constructor() {
    super(403, 'Forbidden', 'You do not have sufficient permissions to perform this action.');
  }
}

class InternalServerError extends CustomError {
  constructor(message, payload, errorMessage = '') {
    super(500, message);
    this.payload = payload;
    this.errorMessage = errorMessage || message;
  }
}

class NotFoundError extends CustomError {
  constructor(message, clientMessage, payload) {
    super(404, message, clientMessage);
    this.payload = payload;
  }
}

const ERROR_HANDLER = (errorMessage, err, next) => {
  // Custom error thrown elsewhere
  if (err instanceof CustomError) {
    next(err);
    return;
  }

  // Joi error
  if (err.isJoi) {
    const messages = err.details.map(i => i.context.value ? `"${xss(i.context.value)}" is not valid for field "${i.context.label}" (${xss(i.message)})` : xss(i.message));
    next(new BadRequestError(`${err.name}: ${messages.join('; ')}.`));
    return;
  }

  // db transaction failed, unlike standard sql error, details and cause is embedded in originalError property
  if (err.originalError) {
    const handledErr = new InternalServerError(errorMessage, err.originalError);
    return next(handledErr);
  }

  // Axios error
  if (err.response && err.response.data && err.response.data.message) {
    const { message, metadata } = err.response.data;
    let formattedMsg = message;
    if (Array.isArray(metadata) && metadata.length) {
      formattedMsg = `${formattedMsg}: ${metadata.map(i => i.message).join('; ')}.`;
    } else if (!Array.isArray(metadata) && typeof metadata === 'object') {
      formattedMsg = `${formattedMsg}: ${Object.values(metadata).join('; ')}.`;
    }
    next(err.response.status === 404
      ? new NotFoundError(err.message, formattedMsg, err.response.data)
      : new InternalServerError(formattedMsg, err.response.data));
    return;
  }

  // Default error
  next(new InternalServerError(err.message, err, errorMessage));
};

module.exports = {
  CustomError,
  BadRequestError,
  UnauthorizedError,
  InternalServerError,
  ForbiddenError,
  NotFoundError,
  ERROR_HANDLER,
};
