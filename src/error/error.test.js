const {
  Custom<PERSON>rror,
  BadRequestError,
  UnauthorizedError,
  ForbiddenError,
  InternalServerError,
  NotFoundError,
  ERROR_HANDLER,
} = require('./error');

jest.mock('uuid/v4', () => jest.fn(() => 'test-uuid-123'));

describe('Error Classes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('CustomError', () => {
    it('should create a custom error with default message', () => {
      const error = new CustomError(404);

      expect(error.code).toBe(404);
      expect(error.message).toBe('Not Found');
      expect(error.uuid).toBe('test-uuid-123');
      expect(error.timestamp).toBeDefined();
      expect(error.name).toBe('CustomError');
    });

    it('should create a custom error with custom message', () => {
      const error = new CustomError(500, 'Custom error message', 'Client message');

      expect(error.code).toBe(500);
      expect(error.message).toBe('Custom error message');
      expect(error.errorMessage).toBe('Client message');
    });

    it('should have stack trace captured', () => {
      const error = new CustomError(400);
      expect(error.stack).toBeDefined();
      expect(error.stack).toContain('CustomError');
    });
  });

  describe('BadRequestError', () => {
    it('should create a bad request error with message', () => {
      const error = new BadRequestError('Invalid input');

      expect(error.code).toBe(400);
      expect(error.message).toBe('Invalid input');
      expect(error.errorMessage).toBe('Invalid input');
      expect(error.name).toBe('BadRequestError');
    });
  });

  describe('UnauthorizedError', () => {
    it('should create an unauthorized error with default messages', () => {
      const error = new UnauthorizedError();

      expect(error.code).toBe(401);
      expect(error.message).toBe('Unauthorized');
      expect(error.errorMessage).toBe('You need to sign in to view this page.');
      expect(error.name).toBe('UnauthorizedError');
    });
  });

  describe('ForbiddenError', () => {
    it('should create a forbidden error with default messages', () => {
      const error = new ForbiddenError();

      expect(error.code).toBe(403);
      expect(error.message).toBe('Forbidden');
      expect(error.errorMessage).toBe('You do not have sufficient permissions to perform this action.');
      expect(error.name).toBe('ForbiddenError');
    });
  });

  describe('InternalServerError', () => {
    it('should create an internal server error with message and payload', () => {
      const payload = { detail: 'Database connection failed' };
      const error = new InternalServerError('Server error', payload, 'Something went wrong');

      expect(error.code).toBe(500);
      expect(error.message).toBe('Server error');
      expect(error.payload).toEqual(payload);
      expect(error.errorMessage).toBe('Something went wrong');
      expect(error.name).toBe('InternalServerError');
    });

    it('should use message as errorMessage when errorMessage not provided', () => {
      const error = new InternalServerError('Server error', { detail: 'error' });

      expect(error.errorMessage).toBe('Server error');
    });
  });

  describe('NotFoundError', () => {
    it('should create a not found error with messages and payload', () => {
      const payload = { id: '123' };
      const error = new NotFoundError('Resource not found', 'Item not found', payload);

      expect(error.code).toBe(404);
      expect(error.message).toBe('Resource not found');
      expect(error.errorMessage).toBe('Item not found');
      expect(error.payload).toEqual(payload);
      expect(error.name).toBe('NotFoundError');
    });

    it('should handle NotFoundError without clientMessage', () => {
      const error = new NotFoundError('Resource not found');

      expect(error.code).toBe(404);
      expect(error.message).toBe('Resource not found');
      // errorMessage is undefined when no clientMessage is provided
      expect(error.errorMessage).toBeUndefined();
    });
  });

  describe('ERROR_HANDLER', () => {
    let nextSpy;

    beforeEach(() => {
      nextSpy = jest.fn();
    });

    it('should handle CustomError instance', () => {
      const customError = new CustomError(400, 'Bad request');
      ERROR_HANDLER('Error message', customError, nextSpy);

      expect(nextSpy).toHaveBeenCalledWith(customError);
    });

    it('should handle BadRequestError instance', () => {
      const badRequestError = new BadRequestError('Invalid data');
      ERROR_HANDLER('Error message', badRequestError, nextSpy);

      expect(nextSpy).toHaveBeenCalledWith(badRequestError);
    });

    it('should handle UnauthorizedError instance', () => {
      const unauthorizedError = new UnauthorizedError();
      ERROR_HANDLER('Error message', unauthorizedError, nextSpy);

      expect(nextSpy).toHaveBeenCalledWith(unauthorizedError);
    });

    it('should handle ForbiddenError instance', () => {
      const forbiddenError = new ForbiddenError();
      ERROR_HANDLER('Error message', forbiddenError, nextSpy);

      expect(nextSpy).toHaveBeenCalledWith(forbiddenError);
    });

    it('should handle InternalServerError instance', () => {
      const internalError = new InternalServerError('Server error');
      ERROR_HANDLER('Error message', internalError, nextSpy);

      expect(nextSpy).toHaveBeenCalledWith(internalError);
    });

    it('should handle NotFoundError instance', () => {
      const notFoundError = new NotFoundError('Not found');
      ERROR_HANDLER('Error message', notFoundError, nextSpy);

      expect(nextSpy).toHaveBeenCalledWith(notFoundError);
    });

    it('should handle Joi validation error', () => {
      const joiError = {
        isJoi: true,
        name: 'ValidationError',
        details: [
          {
            context: { value: 'test', label: 'field1' },
            message: 'is required',
          },
          {
            context: { label: 'field2' },
            message: 'must be a number',
          },
        ],
      };

      ERROR_HANDLER('Error message', joiError, nextSpy);

      expect(nextSpy).toHaveBeenCalledWith(expect.any(BadRequestError));
      const error = nextSpy.mock.calls[0][0];
      expect(error.message).toContain('ValidationError');
      expect(error.message).toContain('is not valid for field');
    });

    it('should handle database transaction error with originalError', () => {
      const dbError = {
        originalError: {
          message: 'Database connection failed',
        },
      };

      ERROR_HANDLER('Transaction failed', dbError, nextSpy);

      expect(nextSpy).toHaveBeenCalledWith(expect.any(InternalServerError));
      const error = nextSpy.mock.calls[0][0];
      expect(error.errorMessage).toBe('Transaction failed');
      expect(error.payload).toEqual(dbError.originalError);
    });

    it('should handle Axios error with 404 status', () => {
      const axiosError = {
        message: 'Request failed',
        response: {
          status: 404,
          data: {
            message: 'Resource not found',
            metadata: [ { message: 'Item does not exist' } ],
          },
        },
      };

      ERROR_HANDLER('Error message', axiosError, nextSpy);

      expect(nextSpy).toHaveBeenCalledWith(expect.any(NotFoundError));
      const error = nextSpy.mock.calls[0][0];
      expect(error.errorMessage).toContain('Resource not found');
      expect(error.errorMessage).toContain('Item does not exist');
    });

    it('should handle Axios error with non-404 status', () => {
      const axiosError = {
        message: 'Request failed',
        response: {
          status: 500,
          data: {
            message: 'Server error',
            metadata: { error1: 'Value 1', error2: 'Value 2' },
          },
        },
      };

      ERROR_HANDLER('Error message', axiosError, nextSpy);

      expect(nextSpy).toHaveBeenCalledWith(expect.any(InternalServerError));
      const error = nextSpy.mock.calls[0][0];
      expect(error.message).toContain('Server error');
      expect(error.message).toContain('Value 1');
      expect(error.message).toContain('Value 2');
    });

    it('should handle Axios error without metadata', () => {
      const axiosError = {
        message: 'Request failed',
        response: {
          status: 500,
          data: {
            message: 'Server error',
          },
        },
      };

      ERROR_HANDLER('Error message', axiosError, nextSpy);

      expect(nextSpy).toHaveBeenCalledWith(expect.any(InternalServerError));
      const error = nextSpy.mock.calls[0][0];
      expect(error.message).toBe('Server error');
    });

    it('should wrap generic Error in InternalServerError', () => {
      const genericError = new Error('Generic error');
      ERROR_HANDLER('Custom message', genericError, nextSpy);

      expect(nextSpy).toHaveBeenCalledWith(expect.any(InternalServerError));
      const error = nextSpy.mock.calls[0][0];
      expect(error.message).toBe('Generic error');
      expect(error.payload).toBe(genericError);
      expect(error.errorMessage).toBe('Custom message');
    });

    it('should handle errors without message property', () => {
      const unknownError = { someProperty: 'value' };
      ERROR_HANDLER('Error occurred', unknownError, nextSpy);

      expect(nextSpy).toHaveBeenCalledWith(expect.any(InternalServerError));
      const error = nextSpy.mock.calls[0][0];
      expect(error.payload).toBe(unknownError);
      expect(error.errorMessage).toBe('Error occurred');
    });
  });
});
