const { CustomError, BadRequestError } = require('../../error');

const updatePartialAlert = (service) => async(req, res, next) => {
  const params = res.locals.validatedParams;
  const body = res.locals.validatedBody;

  const sid = res.locals.user && res.locals.user.sid;
  body.updated_by = sid;
  try {
    const alert = await service.updateAlert(params.ruleId, body);
    res.status(200).json(alert.data);
  } catch (err) {
    if (err.response.status < 500) {
      let errorMessage = '';
      if (err.response.data && err.response.data.message) {
        errorMessage = err.response.data.message;
        if (errorMessage.includes('duplicate')) {
          errorMessage = 'Alert with this name already exists. Use a unique name for the new alert.';
          return next(new BadRequestError(errorMessage));
        }
      } else {
        errorMessage = JSON.stringify(err.response.data);
      }
      res.status(err.response.status).json(err.response.data);
      return;
    }
    next(new CustomError(err.response.status, JSON.stringify(err.response.data)));
  }
};

module.exports = updatePartialAlert;
