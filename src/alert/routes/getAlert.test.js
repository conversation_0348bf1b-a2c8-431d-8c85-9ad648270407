const getAlert = require('./getAlert');
const { CustomError } = require('../../error');

const { MockError } = require('__mocks__/MockError');
const mockService = {
  getAlert: jest.fn().mockReturnValue({
    data: {
      application: 'nova',
      container: 'alert',
      pages: [ 'login' ],
    },
  }),
};

const jsonResp = {
  json: jest.fn(),
};

const resMock = {
  status: jest.fn().mockReturnValue(jsonResp),
  locals: {
    validatedParams: { ruleId: 'asdfas123' },
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
      access: {
        alerts: {
          containers: {
            nova: {
              view: [ 'alert' ],
            },
          },
          pages: {
            nova: {
              view: [ 'login' ],
            },
          },
        },
      },
    },
  },
};

const reqMock = {
  params: {
    ruleId: 'asdfas123',
  },
};

const nextMock = jest.fn();

describe('GET /api/v1/alerts - get an alert by id', () => {
  beforeEach(() => {
    nextMock.mockClear();
  });

  test('should return 200 with valid req', async() => {
    const asyncGetAlert = getAlert(mockService);
    await asyncGetAlert(reqMock, resMock, nextMock);

    expect(mockService.getAlert).toHaveBeenCalledTimes(1);
    expect(nextMock).not.toHaveBeenCalled();
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('should return CustomError if unhandled exception occurs with service', async() => {
    const badMockService = {
      getAlert: jest.fn().mockRejectedValueOnce(new Error()),
    };

    const asyncGetAlert = getAlert(badMockService);
    await asyncGetAlert(reqMock, resMock, nextMock);

    expect(badMockService.getAlert).toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
    expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
  });

  test('should handle 5XX response', async() => {
    const errResponse = {
      status: 500,
      data: {},
    };

    const errMockService = {
      getAlert: jest.fn().mockRejectedValueOnce(new MockError(500, '', errResponse)),
    };

    const asyncGetAlert = getAlert(errMockService);
    await asyncGetAlert(reqMock, resMock, nextMock);

    expect(errMockService.getAlert).toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
    expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
  });

  test('sholud handle 3xx-4xx response', async() => {
    const errResponse = {
      status: 400,
      data: {},
    };

    const errMockService = {
      getAlert: jest.fn().mockRejectedValueOnce(new MockError(400, '', errResponse)),
    };

    const asyncGetAlert = getAlert(errMockService);
    await asyncGetAlert(reqMock, resMock, nextMock);

    expect(errMockService.getAlert).toHaveBeenCalled();
    expect(nextMock).not.toHaveBeenCalled();
    expect(resMock.status).toHaveBeenCalled();
    expect(resMock.status).toHaveBeenCalledWith(errResponse.status);
    expect(jsonResp.json).toHaveBeenCalled();
    expect(jsonResp.json).toHaveBeenCalledWith(errResponse.data);
  });
});
