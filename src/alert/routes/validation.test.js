const {
  getPathSchema,
  schemaValidationMiddleware,
} = require('./validation');
const { CustomError } = require('../../error');

describe('Alert Rules: routes > validate', () => {
  const req = {};
  const res = {};
  const nextMock = jest.fn();

  beforeEach(() => {
    nextMock.mockClear();
  });

  it('schemaValidationMiddleware - should throw error if invalid param type passed', () => {
    schemaValidationMiddleware(getPathSchema, 'invalid-type')(req, res, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
    expect(nextMock.mock.calls[0][0].message).toBe(`Invalid joi validation type, must be: body, params, or query`);
  });

  it('schemaValidationMiddleware - should throw error if fails validation schema', () => {
    const localReq = {
      params: { ruleId: 'test-id' },
    };
    schemaValidationMiddleware(getPathSchema, 'params')(localReq, res, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(2);
    expect(nextMock.mock.calls[0][0].message).toBe(`ValidationError: "test-id" is not valid for field "ruleId" ("ruleId" with value "test-id" fails to match the required pattern: /^[a-zA-Z0-9]+$/).`);
  });

  it('schemaValidationMiddleware - should pass validation - params', () => {
    const localReq = {
      params: { ruleId: 'abcd1234' },
    };
    const localRes = {
      locals: {},
    };
    schemaValidationMiddleware(getPathSchema, 'params')(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedParams).toEqual({ ruleId: 'abcd1234' });
  });

  it('schemaValidationMiddleware - should pass validation - query', () => {
    const localReq = {
      query: { search: 'sol' },
    };
    const localRes = {
      locals: {
        validatedQuery: localReq.query,
      },
    };
    schemaValidationMiddleware(getPathSchema, 'query')(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalled();
    expect(localRes.locals.validatedQuery).toEqual(localReq.query);
  });

  it('schemaValidationMiddleware - should pass validation - body', () => {
    const localReq = {
      body: {
        name: 'testalertcreate',
        start_at: '2024-03-15T04:00:00.000Z',
        end_at: '2024-03-26T04:00:00.000Z',
        platforms: [
          'ios',
          'android',
        ],
        app_version: null,
        content_space: '4szkx38resvm',
        content_type: 'alert',
        content_id: '77AcSMxeTudl9fOtBw8FTI',
        container: 'alert',
        pages: [
          'inactive-nova-page',
        ],
        status: 'submitted',
        application: 'nova',
        platforms_targeting: [
          {
            platform: 'ios',
            items: [
            ],
          },
          {
            platform: 'android',
            items: [
            ],
          },
        ],
        anonymous: true,
        authenticated: true,
        disabled: false,
      },
    };
    const localRes = {
      locals: {
        validatedBody: localReq.body,
      },
    };
    schemaValidationMiddleware(getPathSchema, undefined)(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalled();
    expect(localRes.locals.validatedBody).toEqual(localReq.body);
  });
});
