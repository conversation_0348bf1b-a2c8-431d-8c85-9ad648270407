const contentIndex = require('.');

const serviceMock = {};
describe('/contents route', () => {
  test('should return valid routes', () => {
    const router = contentIndex({ services: { passport: { redirectUrl: 'http://localhost:8090' } } }, serviceMock);
    const routes = [
      '/alert-rules',
      '/export-alert-rules',
      '/alert-rules/:ruleId',
      '/alert-rules/:ruleId',
      '/alert-rules',
      '/alert-rules/:ruleId',
      '/alert-rules/:ruleId',
    ];
    routes.forEach((route, index) => {
      expect(router.stack[index].route.path).toEqual(route);
    });
  });
});
