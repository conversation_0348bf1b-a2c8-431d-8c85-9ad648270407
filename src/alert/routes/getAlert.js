const { CustomError, ForbiddenError } = require('../../error');
const { uniq } = require('lodash');

const getAlert = function(service) {
  return async function(req, res, next) {
    const value = res.locals.validatedParams;

    try {
      const alert = await service.getAlert(value.ruleId);

      // check user has access to view the application, page, and container of the rule
      const { containers, pages } = res.locals.user.access.alerts;
      const hasContainerAccess = containers[alert.data.application] &&
        uniq([ ...(containers[alert.data.application].view || []), ...(containers[alert.data.application].manage || []) ])
          .includes(alert.data.container);

      // only require page access if the alert includes page targeting
      const hasPageAccess = alert.data.pages && alert.data.pages.length
        ? pages[alert.data.application] && uniq([ ...(pages[alert.data.application].view || []), ...(pages[alert.data.application].manage || []) ]).some(page => alert.data.pages.includes(page))
        : true;

      if (!hasContainerAccess || !hasPageAccess) {
        next(new ForbiddenError());
        return;
      }

      res.status(200).json(alert.data);
    } catch (err) {
      if (err.response) {
        if (err.response.status < 500) {
          res.status(err.response.status).json(err.response.data);
        } else {
          next(new CustomError(err.response.status, JSON.stringify(err.response.data)));
        }
      } else {
        next(new CustomError(500, err.toString()));
      }
    }
  };
};

module.exports = getAlert;
