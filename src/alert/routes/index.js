const express = require('express');
const getAll = require('./getAll');
const createAlert = require('./createAlert');
const updatePartialAlert = require('./updatePartialAlert');
const updateAlert = require('./updateAlert');
const getAlert = require('./getAlert');
const deleteAlert = require('./deleteAlert');
const exportRules = require('../../rules/exportRules');
const { wrapAsync } = require('../../utils');
const {
  middleware: can,
  ALERTS_VIEW,
  ALERTS_MANAGE,
} = require('../../permissions/index');
const {
  getPathSchema,
  getListSchema,
  createAlertSchema,
  updatePartialSchema,
  updateSchema,
  ruleExportSchema,
  VALIDATION_TYPE,
  schemaValidationMiddleware,
} = require('./validation');

const init = (config, alertService, userService, permissionService, contentApi) => {
  const router = express.Router();

  router.get('/alert-rules',
    can([ ALERTS_VIEW ]),
    schemaValidationMiddleware(getListSchema, VALIDATION_TYPE.QUERY),
    wrapAsync(getAll(alertService, userService)),
  );

  router.get('/export-alert-rules',
    can([ ALERTS_VIEW ]),
    schemaValidationMiddleware(ruleExportSchema, VALIDATION_TYPE.QUERY),
    wrapAsync(exportRules(config.redirectUrl, 'alert', alertService.getAllAlerts, contentApi, userService),
    ));

  router.get('/alert-rules/:ruleId',
    can([ ALERTS_VIEW ]),
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS),
    wrapAsync(getAlert(alertService)),
  );

  router.delete('/alert-rules/:ruleId',
    can([ ALERTS_MANAGE ]),
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS),
    wrapAsync(deleteAlert(alertService)),
  );

  router.post('/alert-rules',
    can([ ALERTS_MANAGE ]),
    schemaValidationMiddleware(createAlertSchema, VALIDATION_TYPE.BODY),
    wrapAsync(createAlert(alertService)),
  );

  router.patch('/alert-rules/:ruleId',
    can([ ALERTS_MANAGE ]),
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS),
    schemaValidationMiddleware(updatePartialSchema, VALIDATION_TYPE.BODY),
    wrapAsync(updatePartialAlert(alertService)),
  );

  router.put('/alert-rules/:ruleId',
    can([ ALERTS_MANAGE ]),
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS),
    schemaValidationMiddleware(updateSchema, VALIDATION_TYPE.BODY),
    wrapAsync(updateAlert(alertService)),
  );

  return router;
};

module.exports = init;
