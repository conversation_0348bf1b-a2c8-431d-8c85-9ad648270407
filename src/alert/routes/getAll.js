const { CustomError } = require('../../error');
const { uniq } = require('lodash');

const EXCLUDED_APPS = [ 'sol', 'storefront' ];

const getAll = function(alertService, userService) {
  return async function(req, res, next) {
    const value = res.locals.validatedQuery;

    try {
      // filter rules by pages & containers that the user has access to
      const { access } = res.locals.user;
      const { containers, pages } = access.alerts;
      const accessPages = {};
      const accessContainers = {};
      Object.keys(containers).filter(app => !EXCLUDED_APPS.includes(app)).forEach(app => {
        accessContainers[app] = uniq([ ...(containers[app].view || []), ...(containers[app].manage || []) ]);
      });
      Object.keys(pages).filter(app => !EXCLUDED_APPS.includes(app)).forEach(app => {
        accessPages[app] = uniq([ ...(pages[app].view || []), ...(pages[app].manage || []) ]);
      });

      const accessQuery = {
        accessContainers,
        accessPages,
      };

      let allAlerts = await alertService.getAllAlertsByAccess(value, accessQuery);

      if (allAlerts.data.total) {
        const offset = { ...value }.offset;
        const limit = { ...value }.limit;
        const usernameSortParams = [ 'created_by', '-created_by', 'updated_by', '-updated_by' ];
        if (value.sort && [ ...usernameSortParams ].includes(value.sort)) {
          value.limit = allAlerts.data.total;
          value.offset = 0;
          allAlerts = await alertService.getAllAlertsByAccess(value, accessQuery);
        }

        const users = await userService.getUser();
        const sidMapping = users.reduce((o, i) => ({ ...o, [i.sid]: i.name }), {});
        allAlerts.data.items.forEach(item => {
          if (sidMapping[item.created_by]) {
            item.created_by = sidMapping[item.created_by];
          }
          if (sidMapping[item.updated_by]) {
            item.updated_by = sidMapping[item.updated_by];
          }
        });

        // Sort values alphabetically if created_by or updated_by sorting is selected since sID-username mapping is required
        if (value.sort && usernameSortParams.includes(value.sort)) {
          if (value.sort === 'created_by' || value.sort === 'updated_by') {
            allAlerts.data.items.sort((a, b) => {
              const A = a[value.sort].toUpperCase();
              const B = b[value.sort].toUpperCase();
              return (A < B) ? -1 : (A > B) ? 1 : 0;
            });
            allAlerts.data.limit = limit;
            allAlerts.data.offset = offset;
            allAlerts.data.items = allAlerts.data.items.slice(offset, offset + limit);
          } else if (value.sort === '-created_by' || value.sort === '-updated_by') {
            allAlerts.data.items.sort((a, b) => {
              const A = a[value.sort.substring(1)].toUpperCase();
              const B = b[value.sort.substring(1)].toUpperCase();
              return (A > B) ? -1 : (A < B) ? 1 : 0;
            });
            allAlerts.data.limit = limit;
            allAlerts.data.offset = offset;
            allAlerts.data.items = allAlerts.data.items.slice(offset, offset + limit);
          }
        }
      }

      res.status(200).json(allAlerts.data);
    } catch (err) {
      if (err.response) {
        if (err.response.status < 500) {
          res.status(err.response.status).json(err.response.data);
        } else {
          next(new CustomError(err.response.status, JSON.stringify(err.response.data)));
        }
      } else {
        next(new CustomError(500, err.toString()));
      }
    }
  };
};

module.exports = getAll;
