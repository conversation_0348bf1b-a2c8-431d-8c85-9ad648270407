const Joi = require('joi');
const { CustomError, ERROR_HANDLER } = require('../../error');
const { customJoi } = require('../../utils/validation');
const { SCHEMA } = require('../../constants/validate');

const DEFAULT_OPTS = { stripUnknown: true };

const VALIDATION_TYPE = {
  BODY: 'body',
  PARAMS: 'params',
  QUERY: 'query',
};

const schemaValidationMiddleware = (schema, type = VALIDATION_TYPE.BODY, opts = DEFAULT_OPTS) => (req, res, next) => {
  if (!Object.values(VALIDATION_TYPE).includes(type)) {
    return next(new CustomError(500, 'Invalid joi validation type, must be: body, params, or query'));
  }
  const { error, value } = schema.validate(req[type], opts);
  if (error) {
    return next(ERROR_HANDLER(`Joi validation error on req.${type}`, error, next));
  }

  switch (type) {
    case VALIDATION_TYPE.BODY:
      res.locals.validatedBody = value;
      break;
    case VALIDATION_TYPE.PARAMS:
      res.locals.validatedParams = value;
      break;
    case VALIDATION_TYPE.QUERY:
      res.locals.validatedQuery = value;
      break;
      /* istanbul ignore next */
    default:
      break;
  }

  next();
};

// field level validators
const ruleId = Joi.string().min(1).max(20).regex(/^[a-zA-Z0-9]+$/);

const platform = Joi.string().min(1).max(20).regex(/^[a-z]+$/);

const application = SCHEMA.APPLICATION_CREATE.extract('applicationId');

const page = Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_\-.]+$/);

const status = Joi.string().min(1).max(20).regex(/^[a-z]+$/);

const scotiaUser = Joi.string().min(4).max(10).regex(/^s[0-9]+$/);

const platformTargetingItem = Joi.object()
  .keys({
    app_version: customJoi.appVersion(),
    os_version: customJoi.osVersion(),
    device_model: Joi.string().min(1).max(100),
  });

const platformTargeting = Joi.object()
  .keys({
    v: Joi.number().min(1),
    platform: Joi.string().min(1).max(20).regex(/^[a-z0-9]+$/),
    items: Joi.array().items(platformTargetingItem),
  });

const rule = {
  status,
  name: Joi.string().min(5).max(100),
  start_at: Joi.date().iso(),
  end_at: Joi.date().iso(),
  anonymous: Joi.bool(),
  authenticated: Joi.bool(),
  container: Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/).allow(null),
  pages: Joi.array().unique().items(page),
  platforms: Joi.array().items(platform),
  platforms_targeting: Joi.array().items(platformTargeting),
  disabled: Joi.bool(),
  app_version: Joi.string().min(1).max(50).allow(null),
  content_space: Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/),
  content_type: Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/),
  content_id: Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/),
  application,
};

const getPathSchema = Joi.object()
  .keys({ ruleId })
  .fork([ 'ruleId' ], (schema) => schema.required());

const getListSchema = Joi.object()
  .keys({
    platform,
    status,
    disabled: Joi.bool(),
    container: Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/),
    app_version: Joi.string().min(1),
    offset: Joi.number().min(0).default(0),
    limit: Joi.number().min(1).default(50),
    now: Joi.date().iso().raw(),
    sort: Joi.string().min(2).regex(/^[+-]?[a-z_]+$/),
    name: Joi.string().min(1).max(100),
    search: Joi.string().min(1).max(100),
    created_by: scotiaUser,
    updated_by: scotiaUser,
    application,
    start_date_lt: Joi.date().iso().raw(true),
    start_date_gt: Joi.date().iso().raw(true),
    end_date_lt: Joi.date().iso().raw(true),
    end_date_gt: Joi.date().iso().raw(true),
  });

const ruleExportSchema = (
  Joi.object()
    .keys({
      platform,
      status,
      disabled: Joi.bool(),
      container: Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/),
      app_version: Joi.string().min(1),
      offset: Joi.number().min(0).default(0),
      limit: Joi.number().min(1).default(50),
      now: Joi.date().iso().raw(),
      sort: Joi.string().min(2).regex(/^[+-]?[a-z_]+$/),
      name: Joi.string().min(1).max(100),
      search: Joi.string().min(1).max(100),
      start_date_lt: Joi.date().iso().raw(true),
      start_date_gt: Joi.date().iso().raw(true),
      end_date_lt: Joi.date().iso().raw(true),
      end_date_gt: Joi.date().iso().raw(true),
      created_by: scotiaUser,
      updated_by: scotiaUser,
      application,
      export: customJoi.stringArray().min(1).items(Joi.string().valid('name', 'date', 'content_id', 'application', 'platforms', 'created_by', 'updated_by', 'updated_at', 'status')),
    })
);

const createAlertSchema = Joi.object()
  .keys(Object.assign({ ...rule }, {
    anonymous: rule.anonymous.default(true),
    authenticated: rule.authenticated.default(true),
    platforms: rule.platforms.default([]),
    disabled: rule.disabled.default(false),
    application: rule.application.default('nova'),
  }))
  .fork([ 'name', 'start_at', 'end_at', 'container', 'content_space', 'content_type', 'content_id' ], (schema) => schema.required())
  .keys({
    pages: Joi.when('application', {
      is: 'phoenix',
      then: rule.pages.min(1).required(),
      otherwise: rule.pages,
    }),
  });

const updatePartialSchema = Joi.object()
  .keys({ ...rule })
  .keys({
    pages: Joi.when('application', {
      is: 'phoenix',
      then: rule.pages.min(1),
      otherwise: rule.pages,
    }),
  });

const updateSchema = Joi.object()
  .keys({ ...rule })
  .fork([ 'name', 'start_at', 'end_at', 'container', 'content_space', 'content_type', 'content_id' ], (schema) => schema.required())
  .keys({
    pages: Joi.when('application', {
      is: 'phoenix',
      then: rule.pages.min(1).required(),
      otherwise: rule.pages,
    }),
  });

module.exports = {
  getPathSchema,
  getListSchema,
  createAlertSchema,
  updatePartialSchema,
  updateSchema,
  ruleExportSchema,
  VALIDATION_TYPE,
  schemaValidationMiddleware,
};
