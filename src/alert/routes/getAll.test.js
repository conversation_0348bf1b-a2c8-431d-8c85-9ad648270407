const getAll = require('./getAll');
const { CustomError } = require('../../error');
const mockUserService = require('__mocks__/getUsers');

const mockAlertService = {
  getAllAlertsByAccess: jest.fn().mockReturnValue({
    data: {
      items: [],
    },
  }),
};

const jsonResp = {
  json: jest.fn(),
};

const resMock = {
  status: jest.fn().mockReturnValue(jsonResp),
  locals: {
    validatedQuery: {},
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
      access: {
        alerts: {
          containers: {
            nova: {
              view: [ 'alert' ],
            },
          },
          pages: {
            nova: {
              view: [ 'login' ],
            },
          },
          ruleSubTypes: {
            nova: {
              view: [ 'targeted' ],
            },
          },
        },
      },
    },
  },
};

const reqMock = {
  query: {},
};

const nextMock = jest.fn();

describe('GET /api/v1/alerts - get a list of alerts', () => {
  beforeEach(() => {
    mockAlertService.getAllAlertsByAccess.mockClear();
    resMock.status.mockClear();
  });

  test('should return 200 with valid req', async() => {
    const asyncGetAll = getAll(mockAlertService, mockUserService);
    await asyncGetAll(reqMock, resMock, nextMock);

    expect(mockAlertService.getAllAlertsByAccess).toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('should handle unknown error', async() => {
    const mockAlertService = {
      getAllAlertsByAccess: jest.fn().mockRejectedValue(new Error('unknown')),
    };
    const asyncGetAll = getAll(mockAlertService, mockUserService);

    try {
      await asyncGetAll(reqMock, resMock, nextMock);
    } catch (err) {
      expect(err.message).toBe('unknown');
      expect(nextMock).toHaveBeenCalledTimes(1);
      expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
    }
  });

  test('should handle 401 error', async() => {
    const mockAlertService = {
      getAllAlertsByAccess: jest.fn().mockRejectedValue({ response: { status: 401, data: {} } }),
    };
    const asyncGetAll = getAll(mockAlertService, mockUserService);

    try {
      await asyncGetAll(reqMock, resMock, nextMock);
    } catch (err) {
      expect(nextMock).toHaveBeenCalledTimes(0);
      expect(resMock.status).toHaveBeenCalledWith(401);
    }
  });

  test('should handle 500 error', async() => {
    const mockAlertService = {
      getAllAlertsByAccess: jest.fn().mockRejectedValue({ response: { status: 500, data: {} } }),
    };
    const asyncGetAll = getAll(mockAlertService, mockUserService);

    try {
      await asyncGetAll(reqMock, resMock, nextMock);
    } catch (err) {
      expect(nextMock).toHaveBeenCalledTimes(1);
      expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
    }
  });

  test('should strip unkown query strings and return 200', async() => {
    const asyncGetAll = getAll(mockAlertService, mockUserService);
    reqMock.query = {
      'unknownQueryParam': 1234,
    };

    await asyncGetAll(reqMock, resMock, nextMock);

    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(mockAlertService.getAllAlertsByAccess).toHaveBeenCalled();
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('should return list without sort and without view access', async() => {
    const campaignData = {
      'data': {
        'total': 240,
        'offset': 0,
        'limit': 10,
        'items': [
          {
            'id': '1',
            'status': 'draft',
          },
          {
            'id': '2',
            'start_at': '2020-06-12T04:00:00.000Z',
            'end_at': '2021-06-13T04:00:00.000Z',
            'status': 'published', // expired
          },
          {
            'id': '3',
            'start_at': '2025-06-12T04:00:00.000Z',
            'end_at': '2026-06-13T04:00:00.000Z',
            'status': 'published', // upcoming
          },
          {
            'id': '4',
            'start_at': '2020-06-12T04:00:00.000Z',
            'end_at': '2025-06-13T04:00:00.000Z',
            'status': 'published', // inactive
            'disabled': true,
          },
          {
            'id': '5',
            'start_at': '2020-06-12T04:00:00.000Z',
            'end_at': '2025-06-13T04:00:00.000Z',
            'status': 'published', // active
            'disabled': false,
          },
        ],
      },
    };

    const mockAlertService = {
      getAllAlertsByAccess: jest.fn().mockReturnValue(campaignData),
    };
    const reqMock = {
      query: { sort: '' },
    };
    const localResMock = {
      status: jest.fn().mockReturnValue(jsonResp),
      locals: {
        validatedQuery: {},
        user: {
          id: 1,
          role_id: 1,
          permissions: [ 'admin' ],
          access: {
            alerts: {
              containers: {
                nova: {
                  view: undefined,
                },
              },
              pages: {
                nova: {
                  view: undefined,
                },
              },
              ruleSubTypes: {
                nova: {
                  view: undefined,
                },
              },
            },
          },
        },
      },
    };

    const asyncGetAll = getAll(mockAlertService, mockUserService);
    await asyncGetAll(reqMock, localResMock, nextMock);

    expect(nextMock).not.toHaveBeenCalled();
    expect(mockAlertService.getAllAlertsByAccess).toHaveBeenCalled();
    expect(localResMock.status).toHaveBeenCalled();
    expect(jsonResp.json).toHaveBeenCalled();

    // rules to be returned are sorted alphabetically by FE status
    const responseData = jsonResp.json.mock.calls[0][0];
    expect(responseData.items[0].id).toBe('1');
    expect(responseData.items[0].status).toBe('draft'); // active
    expect(responseData.items[1].id).toBe('2');
  });

  test('Should sort rules by updated_by', async() => {
    const campaignData = {
      'data': {
        'total': 240,
        'offset': 0,
        'limit': 10,
        'items': [
          {
            'id': '1',
            'status': 'draft',
            updated_by: 'e',
          },
          {
            'id': '2',
            'start_at': '2020-06-12T04:00:00.000Z',
            'end_at': '2021-06-13T04:00:00.000Z',
            'status': 'published', // expired
            updated_by: 'a',
          },
          {
            'id': '3',
            'start_at': '2025-06-12T04:00:00.000Z',
            'end_at': '2026-06-13T04:00:00.000Z',
            'status': 'published', // upcoming
            updated_by: 'c',
          },
          {
            'id': '4',
            'start_at': '2020-06-12T04:00:00.000Z',
            'end_at': '2025-06-13T04:00:00.000Z',
            'status': 'published', // inactive
            'disabled': true,
            updated_by: 'b',
          },
          {
            'id': '5',
            'start_at': '2020-06-12T04:00:00.000Z',
            'end_at': '2025-06-13T04:00:00.000Z',
            'status': 'published', // active
            'disabled': false,
            updated_by: 'z',
          },
        ],
      },
    };

    const mockAlertService = {
      getAllAlertsByAccess: jest.fn().mockReturnValue(campaignData),
    };
    const reqMock = {
      query: { sort: 'updated_by' },
    };
    const localResMock = {
      status: jest.fn().mockReturnValue(jsonResp),
      locals: {
        ...resMock.locals,
        validatedQuery: { sort: 'updated_by', offset: 0, limit: 10 },
      },
    };

    const asyncGetAll = getAll(mockAlertService, mockUserService);
    await asyncGetAll(reqMock, localResMock, nextMock);

    expect(nextMock).not.toHaveBeenCalled();
    expect(mockAlertService.getAllAlertsByAccess).toHaveBeenCalled();
    expect(localResMock.status).toHaveBeenCalled();
    expect(jsonResp.json).toHaveBeenCalled();

    const responseData = jsonResp.json.mock.calls[0][0];
    expect(responseData.items[0].id).toBe('2');
    expect(responseData.items[0].status).toBe('published');
    expect(responseData.items[1].id).toBe('4');
    expect(responseData.items[1].status).toBe('published');
    expect(responseData.items[2].id).toBe('3');
    expect(responseData.items[2].status).toBe('published');
    expect(responseData.items[3].id).toBe('1');
    expect(responseData.items[3].status).toBe('draft');
    expect(responseData.items[4].id).toBe('5');
    expect(responseData.items[4].status).toBe('published');
  });

  test('Should sort rules by -updated_by', async() => {
    const campaignData = {
      'data': {
        'total': 240,
        'offset': 0,
        'limit': 10,
        'items': [
          {
            'id': '1',
            'status': 'draft',
            updated_by: 'e',
          },
          {
            'id': '2',
            'start_at': '2020-06-12T04:00:00.000Z',
            'end_at': '2021-06-13T04:00:00.000Z',
            'status': 'published', // expired
            updated_by: '',
          },
          {
            'id': '3',
            'start_at': '2025-06-12T04:00:00.000Z',
            'end_at': '2026-06-13T04:00:00.000Z',
            'status': 'published', // upcoming
            updated_by: 'c',
          },
          {
            'id': '4',
            'start_at': '2020-06-12T04:00:00.000Z',
            'end_at': '2025-06-13T04:00:00.000Z',
            'status': 'published', // inactive
            'disabled': true,
            updated_by: 'b',
          },
          {
            'id': '5',
            'start_at': '2020-06-12T04:00:00.000Z',
            'end_at': '2025-06-13T04:00:00.000Z',
            'status': 'published', // active
            'disabled': false,
            updated_by: 'z',
          },
        ],
      },
    };

    const mockAlertService = {
      getAllAlertsByAccess: jest.fn().mockReturnValue(campaignData),
    };
    const reqMock = {
      query: { sort: '-updated_by' },
    };
    const localResMock = {
      status: jest.fn().mockReturnValue(jsonResp),
      locals: {
        ...resMock.locals,
        validatedQuery: { sort: '-updated_by', offset: 0, limit: 10 },
      },
    };

    const asyncGetAll = getAll(mockAlertService, mockUserService);
    await asyncGetAll(reqMock, localResMock, nextMock);

    expect(nextMock).not.toHaveBeenCalled();
    expect(mockAlertService.getAllAlertsByAccess).toHaveBeenCalled();
    expect(localResMock.status).toHaveBeenCalled();
    expect(jsonResp.json).toHaveBeenCalled();

    const responseData = jsonResp.json.mock.calls[0][0];

    expect(responseData.items[0].id).toBe('5');
    expect(responseData.items[0].status).toBe('published');
    expect(responseData.items[1].id).toBe('1');
    expect(responseData.items[1].status).toBe('draft');
    expect(responseData.items[2].id).toBe('3');
    expect(responseData.items[2].status).toBe('published');
    expect(responseData.items[3].id).toBe('4');
    expect(responseData.items[3].status).toBe('published');
    expect(responseData.items[4].id).toBe('2');
    expect(responseData.items[4].status).toBe('published');
  });

  test('Should return list with sidMapping', async() => {
    const campaignData = {
      'data': {
        'total': 240,
        'offset': 0,
        'limit': 10,
        'items': [
          {
            'id': '1',
            'status': 'draft',
            updated_by: 'e',
            created_by: 's1234567',
          },
          {
            'id': '2',
            'start_at': '2020-06-12T04:00:00.000Z',
            'end_at': '2021-06-13T04:00:00.000Z',
            'status': 'published', // expired
            updated_by: 'b',
          },
          {
            'id': '3',
            'start_at': '2025-06-12T04:00:00.000Z',
            'end_at': '2026-06-13T04:00:00.000Z',
            'status': 'published', // upcoming
            updated_by: 's1234567',
          },
          {
            'id': '4',
            'start_at': '2020-06-12T04:00:00.000Z',
            'end_at': '2025-06-13T04:00:00.000Z',
            'status': 'published', // inactive
            'disabled': true,
            updated_by: 'b',
          },
          {
            'id': '5',
            'start_at': '2020-06-12T04:00:00.000Z',
            'end_at': '2025-06-13T04:00:00.000Z',
            'status': 'published', // active
            'disabled': false,
            updated_by: 'z',
          },
        ],
      },
    };

    const mockAlertService = {
      getAllAlertsByAccess: jest.fn().mockReturnValue(campaignData),
    };
    const reqMock = {
      query: { sort: 'unknown' },
    };
    const localResMock = {
      status: jest.fn().mockReturnValue(jsonResp),
      locals: {
        ...resMock.locals,
        validatedQuery: { sort: 'unknown', offset: 0, limit: 10 },
      },
    };

    const asyncGetAll = getAll(mockAlertService, mockUserService);
    await asyncGetAll(reqMock, localResMock, nextMock);

    expect(nextMock).not.toHaveBeenCalled();
    expect(mockAlertService.getAllAlertsByAccess).toHaveBeenCalled();
    expect(localResMock.status).toHaveBeenCalled();
    expect(jsonResp.json).toHaveBeenCalled();

    const responseData = jsonResp.json.mock.calls[0][0];

    expect(responseData.items[0].id).toBe('1');
    expect(responseData.items[0].status).toBe('draft');
    expect(responseData.items[1].id).toBe('2');
    expect(responseData.items[1].status).toBe('published');
    expect(responseData.items[2].id).toBe('3');
    expect(responseData.items[2].status).toBe('published');
    expect(responseData.items[3].id).toBe('4');
    expect(responseData.items[3].status).toBe('published');
    expect(responseData.items[4].id).toBe('5');
    expect(responseData.items[4].status).toBe('published');
  });

  test('should sort alerts by created_by alphabetically and apply pagination', async() => {
    const localReq = {};
    const localRes = {
      locals: {
        validatedQuery: {
          sort: 'created_by',
          offset: 0,
          limit: 2,
        },
        user: {
          access: {
            alerts: {
              containers: { nova: { view: [ 'container-test' ], manage: [] } },
              pages: { nova: { view: [ 'page-test' ], manage: [] } },
            },
          },
        },
      },
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    const localNext = jest.fn();

    const alertService = {
      getAllAlertsByAccess: jest.fn().mockResolvedValue({
        data: {
          total: 3,
          items: [
            { created_by: 'sid2', updated_by: 'sid3' },
            { created_by: 'sid1', updated_by: 'sid2' },
            { created_by: 'sid3', updated_by: 'sid1' },
          ],
        },
      }),
    };

    const userService = {
      getUser: jest.fn().mockResolvedValue([
        { sid: 'sid1', name: 'Alice' },
        { sid: 'sid2', name: 'Bob' },
        { sid: 'sid3', name: 'Charlie' },
      ]),
    };

    const handler = getAll(alertService, userService);
    await handler(localReq, localRes, localNext);

    expect(alertService.getAllAlertsByAccess).toHaveBeenCalledTimes(2); // once for total, once for full fetch
    expect(userService.getUser).toHaveBeenCalled();

    const sortedNames = localRes.json.mock.calls[0][0].items.map(i => i.created_by);
    expect(sortedNames).toEqual([ 'Alice', 'Bob' ]); // Sorted and sliced
  });
});
