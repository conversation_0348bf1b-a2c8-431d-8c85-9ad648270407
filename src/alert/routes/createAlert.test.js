const CreateAlert = require('./createAlert');
const permissionsServiceMock = require('../../__mocks__/permissionService');

// mock Alert API client
const mockAlertApiClient = {
  createAlert: jest.fn(),
};
// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();
mockStatus.mockReturnValue({ json: mockJson });
const mockRes = {
  status: mockStatus,
  locals: {
    validatedBody: {},
    auth: { sid: 's1234567' },
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
    },
  },
};

describe('Alert Rules: routes > createAlert', () => {
  beforeEach(() => {
    mockAlertApiClient.createAlert.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });

  test('should support bad request response from Alert API client', async() => {
    const mockReq = {
      body: {
        name: 'sample name',
        start_at: '2018-01-01T00:00:00Z',
        end_at: '2018-01-01T00:00:00Z',
        container: 'PRE_LOGIN',
        created_by: 's1234567',
        updated_by: 's1234567',
        content_space: '4szkx38resvm',
        content_type: 'notification',
        content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      },
    };
    const fakeResponse = {
      response: {
        status: 400,
        data: {
          code: 'HTTP_BAD_REQUEST',
          message: 'Database error',
          uuid: '04d69d4d-4c41-4373-b7f5-9b351a1f345d',
          timestamp: '2018-08-28T20:43:59.319Z',
          metadata: [ 'The duplicate key value is (sample name #38).' ],
        },
      },
    };
    mockAlertApiClient.createAlert.mockRejectedValueOnce(fakeResponse);
    const createAlert = CreateAlert(mockAlertApiClient, permissionsServiceMock);
    await createAlert(mockReq, mockRes, mockNext);
    expect(mockAlertApiClient.createAlert).toBeCalled();
    expect(mockNext).toBeCalled();
  });
  test('should call next with Error on 500+ response status', async() => {
    const mockReq = {
      body: {
        name: 'sample name',
        start_at: '2018-01-01T00:00:00Z',
        end_at: '2018-01-01T00:00:00Z',
        container: 'PRE_LOGIN',
        created_by: 's1234567',
        updated_by: 's1234567',
        content_space: '4szkx38resvm',
        content_type: 'notification',
        content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      },
    };
    const fakeResponse = {
      response: {
        status: 500,
        data: {
          code: 'HTTP_INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
          uuid: '04d69d4d-4c41-4373-b7f5-9b351a1f345d',
          timestamp: '2018-08-28T20:43:59.319Z',
          metadata: [],
        },
      },
    };
    mockAlertApiClient.createAlert.mockRejectedValueOnce(fakeResponse);
    const createAlert = CreateAlert(mockAlertApiClient, permissionsServiceMock);
    await createAlert(mockReq, mockRes, mockNext);
    expect(mockAlertApiClient.createAlert).toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls.length).toEqual(1);
    expect(mockNext.mock.calls[0][0] instanceof Error).toBe(true);
  });
});
