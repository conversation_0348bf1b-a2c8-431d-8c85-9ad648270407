const { CustomError, BadRequestError } = require('../../error');

const createAlert = (service) => async(req, res, next) => {
  const value = res.locals.validatedBody;

  const sid = res.locals.user && res.locals.user.sid;
  value.created_by = sid;
  value.updated_by = sid;
  try {
    const alert = await service.createAlert(value);
    res.status(200).json(alert.data);
  } catch (err) {
    if (err.response.status < 500) {
      let errorMessage = '';
      if (err.response.data && err.response.data.message) {
        errorMessage = err.response.data.message;
        if (errorMessage.includes('duplicate')) {
          errorMessage = 'Alert with this name already exists. Use a unique name for the new alert.';
          return next(new BadRequestError(errorMessage));
        }
      } else {
        errorMessage = JSON.stringify(err.response.data);
      }
      return next(new CustomError(err.response.status, errorMessage));
    }
    next(new Error(JSON.stringify(err.response.data)));
  }
};

module.exports = createAlert;
