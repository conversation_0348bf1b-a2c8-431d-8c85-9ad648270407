const express = require('express');
const { ERROR_HANDLER } = require('../../error');
const {
  middleware: can,
  PEGA_VARIABLE_MAPPING_VIEW,
} = require('../../permissions');
const { VALIDATE, SCHEMA } = require('../../constants/validate');

module.exports = (service) => {
  const router = express.Router();
  //  we should add KT_VARIABLE_MAPPING_VIEW one after <PERSON>'s PR ( adding or - and option )
  router.get('/sets', can([ PEGA_VARIABLE_MAPPING_VIEW ]), async(req, res, next) => {
    try {
      VALIDATE(req.query, SCHEMA.VARIABLE_MAPPINGS_SET);
      const results = await service.getVariableMappingSets(req.query);
      res.status(200).json(results.data);
    } catch (err) {
      ERROR_HANDLER('Error while fetching variable mappings sets.', err, next);
    }
  });

  //  we should add KT_VARIABLE_MAPPING_VIEW one after <PERSON>'s PR ( adding or - and option )
  router.get('/types', can([ PEGA_VARIABLE_MAPPING_VIEW ]), async(req, res, next) => {
    try {
      const results = await service.getVariableTypes();
      res.status(200).json(results.data);
    } catch (err) {
      ERROR_HANDLER('Error while fetching variable types.', err, next);
    }
  });

  return router;
};
