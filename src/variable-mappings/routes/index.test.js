const getHandler = (router, path, method) => {
  const stack = router.stack.find(i => {
    return i.route.path === path && i.route.methods[method];
  });
  return stack.route.stack[stack.route.stack.length - 1].handle;
};

const jsonResp = {
  json: jest.fn(),
};
const sendResp = {
  send: jest.fn(),
};
const resMock = {
  status: jest.fn().mockReturnValue({ ...jsonResp, ...sendResp }),
  redirect: jest.fn(),
  clearCookie: jest.fn(),
  locals: {
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
      sid: 's999999',
    },
  },
};
const resMockForbidden = {
  ...resMock,
  locals: {
    user: {

    },
  },
};
const resMockResError = {
  ...resMock,
  status: code => { throw Error(code); },
};

const reqMock = {
  body: {},
  params: { id: 1 },
  query: {},

};
const nextMock = jest.fn();
const nextMockConsoleLog = jest.fn(console.log);

module.exports = {
  getHandler,
  jsonResp,
  sendResp,
  resMock,
  resMockForbidden,
  resMockResError,
  reqMock,
  nextMock,
  nextMockConsoleLog,
};

const instance = require('.');
test('should create instances of required routes correctly', () => {
  expect(instance).toStrictEqual(expect.any(Object));
  expect(instance.variableMappings).toStrictEqual(expect.any(Function));
  expect(instance.variableMappingsV2).toStrictEqual(expect.any(Function));
});
