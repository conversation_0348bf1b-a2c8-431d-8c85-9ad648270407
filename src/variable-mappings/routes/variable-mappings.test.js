const {
  get<PERSON><PERSON><PERSON>,
  json<PERSON><PERSON><PERSON>,
  resMock,
  resMockResError,
  reqMock,
  nextMock,
  nextMockConsoleLog,
} = require('./index.test');

const testData = {
  data: {
    variable_template: 'SOLUI_EXPIRY_DATE_END',
    source: 'PEGAv2',
    variable_campaign: 'expiry_date',
    variable_type: 'date',
  },
};

const testVariableMappingSet = {
  data: {
    data:
    {
      variable_set_id: 7,
      created_at: '2021-01-15T02:25:28.291Z',
      created_by: 's7646571',
      status: 'draft',
      updated_at: null,
      updated_by: null,
      variables: [
        {
          variable_template: 'SOLUI_CUST_FULL_NAME_END',
          variable_campaign: 'cust_full_name',
          variable_type: 'text',
        },
      ],
    },

  },
};
reqMock.body = {
  status: 'draft',
  description: 'test',
  variables: [
    {
      variable_template: 'SOLUI_CUST_FULL_NAME_END',
      variable_campaign: 'cust_full_name',
      variable_type: 'text',
    },
  ],
};

const mockService = {
  getVariableMappingSet: jest.fn().mockReturnValue(testVariableMappingSet),
  getVariableMappingSets: jest.fn().mockReturnValue(testData),
  updateVariableSet: jest.fn().mockReturnValue([ testData ]),
  getVariableTypes: jest.fn().mockReturnValue(testData),
};

const router = require('./variable-mappings')(mockService);

describe('variableMappings', () => {
  it('Success', async() => {
    await getHandler(router, '/sets', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData.data);
    expect(mockService.getVariableMappingSets).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/sets', 'get')(reqMock, resMockResError, nextMock);
    expect(mockService.getVariableMappingSets).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });

  it('Success', async() => {
    await getHandler(router, '/types', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(jsonResp.json).toHaveBeenCalledWith(testData.data);
    expect(mockService.getVariableTypes).toHaveBeenCalled();
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('ResError', async() => {
    await getHandler(router, '/types', 'get')(reqMock, resMockResError, nextMock);
    expect(mockService.getVariableTypes).toHaveBeenCalled();
    expect(jsonResp.json).not.toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});
