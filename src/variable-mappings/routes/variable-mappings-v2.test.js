const {
  getHand<PERSON>,
  sendResp,
  resMock,
  reqMock,
  nextMockConsoleLog,
  resMockForbidden,
} = require('./index.test');
const { BadRequestError, ForbiddenError, InternalServerError } = require('../../error');

const approvers = [
  { full_name: 'A Nova viewer', sid: 's2' },
  { full_name: 'B Pigeon Admin', sid: 's1' },
  { full_name: 'C Nova owner', sid: 's3' },
];

const ruleService = {
  getApproversForVariableMapping: jest.fn().mockReturnValue([ approvers ]),
};

const variableMappingsApiV2 = {
  createVariableSet: jest.fn().mockReturnValue([]),
  updateVariableSet: jest.fn().mockReturnValue([]),
  patchVariableSet: jest.fn().mockReturnValue([]),
};

const variableMappingsApiV1 = {
  getVariableMappingSet: jest.fn().mockReturnValue({ data: { data: { status: 'draft' } } }),
};

const router = require('./variable-mappings-v2')(variableMappingsApiV2, variableMappingsApiV1, ruleService);

describe('variableMappingsV2 /approvers', () => {
  it('GET /approvers', async() => {
    await getHandler(router, '/approvers', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(sendResp.send).toHaveBeenCalledWith([ approvers ]);
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('GET /approvers error case', async() => {
    const router = require('./variable-mappings-v2')(undefined, undefined, undefined);
    await getHandler(router, '/approvers', 'get')(reqMock, resMock, nextMockConsoleLog);
    expect(nextMockConsoleLog).toHaveBeenCalledWith(new InternalServerError("Cannot read properties of undefined (reading 'getApproversForVariableMapping')"));
  });
});

describe('variableMappingV2 /sets', () => {
  const reqMockBody = {
    ...reqMock,
    body: {
      created_at: '2023-09-15T17:30:47.230Z',
      created_by: 's1838652',
      status: 'active',
      updated_at: '2023-09-15T17:30:53.413Z',
      updated_by: 's1838652',
      approver_sid: 's1838652',
      variables: [
        {
          variable_campaign: 'approved_credit_limit',
          variable_template: 'SOLUI_APPROVED_CREDIT_LIMIT_END',
          variable_type: 'currency',
          variable_type_label: 'Currency',
        },
      ],
    },
  };

  // POST
  it('POST /sets no permission', async() => {
    await getHandler(router, '/sets', 'post')(reqMockBody, resMockForbidden, nextMockConsoleLog);
    expect(nextMockConsoleLog).toHaveBeenCalledWith(new ForbiddenError('You do not have sufficient permissions to perform this action.'));
  });

  it('POST /sets', async() => {
    await getHandler(router, '/sets', 'post')(reqMockBody, resMock, nextMockConsoleLog);
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('POST /sets error case', async() => {
    await getHandler(router, '/sets', 'post')(reqMock, resMock, nextMockConsoleLog);
    expect(nextMockConsoleLog).toHaveBeenCalledWith(new BadRequestError('ValidationError: "variables" is required.'));
  });

  // PUT
  it('PUT /sets/:id no permission', async() => {
    await getHandler(router, '/sets/:id', 'put')(reqMockBody, resMockForbidden, nextMockConsoleLog);
    expect(nextMockConsoleLog).toHaveBeenCalledWith(new ForbiddenError('You do not have sufficient permissions to perform this action.'));
  });

  it('PUT /sets/:id', async() => {
    await getHandler(router, '/sets/:id', 'put')(reqMockBody, resMock, nextMockConsoleLog);
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('PUT /sets/:id', async() => {
    await getHandler(router, '/sets/:id', 'put')(reqMockBody, resMock, nextMockConsoleLog);
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('PUT /sets/:id error case', async() => {
    await getHandler(router, '/sets/:id', 'put')(reqMock, resMock, nextMockConsoleLog);
    expect(nextMockConsoleLog).toHaveBeenCalledWith(new BadRequestError('ValidationError: "variables" is required.'));
  });

  // PATCH
  it('PATCH /sets/:id no permission', async() => {
    await getHandler(router, '/sets/:id', 'patch')(reqMockBody, resMock, nextMockConsoleLog);
    expect(nextMockConsoleLog).toHaveBeenCalledWith(new ForbiddenError('You do not have sufficient permissions to perform this action.'));
  });

  it('PATCH /sets/:id no permission', async() => {
    const reqMockBody = {
      ...reqMock,
      body: {
        created_at: '2023-09-15T17:30:47.230Z',
        created_by: 's1838652',
        status: 'pending',
        updated_at: '2023-09-15T17:30:53.413Z',
        updated_by: 's1838652',
        approver_sid: 's1838652',
        variables: [
          {
            variable_campaign: 'approved_credit_limit',
            variable_template: 'SOLUI_APPROVED_CREDIT_LIMIT_END',
            variable_type: 'currency',
            variable_type_label: 'Currency',
          },
        ],
      },
    };
    await getHandler(router, '/sets/:id', 'patch')(reqMockBody, resMock, nextMockConsoleLog);
    expect(nextMockConsoleLog).not.toHaveBeenCalled();
  });

  it('PATCH /sets/:id error case', async() => {
    await getHandler(router, '/sets/:id', 'patch')(reqMock, resMock, nextMockConsoleLog);
    expect(nextMockConsoleLog).toHaveBeenCalledWith(new BadRequestError('ValidationError: "status" is required.'));
  });
});
