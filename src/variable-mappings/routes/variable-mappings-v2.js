const express = require('express');
const { ERROR_HANDLER, ForbiddenError } = require('../../error');
const {
  middleware: can,
  PEGA_VARIABLE_MAPPING_MANAGE,
} = require('../../permissions');
const { VALIDATE, SCHEMA } = require('../../constants/validate');
const { canUpdateVariableMappings, canCreateVariableMappingDraft, canUpdateVariableMappingDraft } = require('../../permissions/workflow');

module.exports = (service, serviceV1, ruleService) => {
  const router = express.Router();

  router.post('/sets', can([ PEGA_VARIABLE_MAPPING_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.body, SCHEMA.CREATE_AND_UPDATE_VARIABLE_MAPPING_SET_V2);
      const allow = canCreateVariableMappingDraft(res.locals.user);
      if (!allow) {
        return next(new ForbiddenError());
      }
      const paylod = {
        ...req.body,
        created_by_sid: res.locals.user.sid,
      };
      const { data } = await service.createVariableSet(paylod);
      res.status(200).json(data);
    } catch (err) {
      ERROR_HANDLER('Error while creating variable mappings set.', err, next);
    }
  });

  router.put('/sets/:id', can([ PEGA_VARIABLE_MAPPING_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      VALIDATE(req.body, SCHEMA.CREATE_AND_UPDATE_VARIABLE_MAPPING_SET_V2);
      const allow = canUpdateVariableMappingDraft(res.locals.user);
      if (!allow) {
        return next(new ForbiddenError());
      }
      const paylod = {
        ...req.body,
        updated_by_sid: res.locals.user.sid,
      };
      const { data } = await service.updateVariableSet(req.params.id, paylod);
      res.status(200).json(data);
    } catch (err) {
      ERROR_HANDLER('Error while updating variable mappings set.', err, next);
    }
  });

  router.patch('/sets/:id', can([ PEGA_VARIABLE_MAPPING_MANAGE ]), async(req, res, next) => {
    try {
      VALIDATE(req.params, SCHEMA.ID);
      VALIDATE(req.body, SCHEMA.PATCH_VARIABLE_MAPPING_SET_V2);
      const { data: originalVariableMappingSet } = (await serviceV1.getVariableMappingSet(req.params.id)).data;
      const allow = canUpdateVariableMappings(res.locals.user, originalVariableMappingSet, req.body);
      if (!allow) {
        return next(new ForbiddenError());
      }
      const paylod = {
        ...req.body,
        updated_by_sid: res.locals.user.sid,
      };
      const { data } = await service.patchVariableSet(req.params.id, paylod);
      res.status(200).send(data);
    } catch (err) {
      ERROR_HANDLER('Error while updating variable mappings set.', err, next);
    }
  });

  router.get('/approvers', can([ PEGA_VARIABLE_MAPPING_MANAGE ]), async(req, res, next) => {
    try {
      const teamId = res.locals.user.team_id; // limit results to team mates except for pigeon members
      const data = await ruleService.getApproversForVariableMapping(teamId > 1 && teamId);
      res.status(200).send(data);
    } catch (err) {
      ERROR_HANDLER('Error while updating variable mappings set.', err, next);
    }
  });

  return router;
};
