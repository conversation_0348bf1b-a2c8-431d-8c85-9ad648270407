const { createServiceAuthClient } = require('pigeon-cerberus');
const config = require('../constants/config');
const axios = require('axios');

// create an access token requester
const serviceAuthClient = createServiceAuthClient({
  passportAccessTokenURI: `${config.services.passportAPI.uri}/token`,
  privateKey: config.services.passportAPI.privateKey,
  privateKeyAlgorithm: config.services.passportAPI.algorithm,
  clientId: config.services.passportAPI.clientId,
  scope: config.services.passportAPI.scope,
  expiresIn: config.services.passportAPI.expiresIn,
  notBefore: config.services.passportAPI.notBefore,
});

// create axios with Authorization header set
const axiosAuth = axios.create();

axiosAuth.interceptors.request.use(async(config) => {
  const token = await serviceAuthClient();
  config.headers['Authorization'] = `Bearer ${token}`;

  // Remove data property for GET requests to prevent body transmission
  // This change is added as part of Axios upgrade from v0.21.4 to v1.9.0
  if (config.method && config.method.toLowerCase() === 'get' && config.data !== undefined) {
    delete config.data;
    delete config.headers['Content-Length'];
  }

  return config;
}, (err) => Promise.reject(err));

module.exports = axiosAuth;
