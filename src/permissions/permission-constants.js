// TODO: Depreacte old permissions list & refactor anywhere they are used
const permissionsV1 = {
  PERMISSION_ADMIN: 'admin',
};

// New permissions
const permissions = {
  CAMPAIGNS_VIEW: 'campaigns_view',
  CAMPAIGNS_MANAGE: 'campaigns_manage',
  CAMPAIGNS_REVIEW: 'campaigns_review',
  CAMPAIGNS_APPROVE: 'campaigns_approve',

  CCAU_CAMPAIGNS_VIEW: 'ccau_campaigns_view',
  CCAU_CAMPAIGNS_MANAGE: 'ccau_campaigns_manage',
  CCAU_CAMPAIGNS_REVIEW: 'ccau_campaigns_review',
  CCAU_CAMPAIGNS_APPROVE: 'ccau_campaigns_approve',

  ALERTS_VIEW: 'alerts_view',
  ALER<PERSON>_MANAGE: 'alerts_manage',
  ALERTS_APPROVE: 'alerts_approve',

  // Placement
  APPLICATIONS_VIEW: 'applications_view',
  APPLICATIONS_MANAGE: 'applications_manage',
  APPLICATIONS_VIEW_SUPER: 'applications_view_super',
  APPLICATIONS_MANAGE_SUPER: 'applications_manage_super',

  PAGES_VIEW: 'pages_view',
  PAGES_MANAGE: 'pages_manage',
  PAGES_VIEW_SUPER: 'pages_view_super',
  PAGES_MANAGE_SUPER: 'pages_manage_super',

  CONTAINERS_VIEW: 'containers_view',
  CONTAINERS_MANAGE: 'containers_manage',
  CONTAINERS_VIEW_SUPER: 'containers_view_super',
  CONTAINERS_MANAGE_SUPER: 'containers_manage_super',

  // Access Control
  TEAMS_VIEW: 'teams_view',
  TEAMS_VIEW_SUPER: 'teams_view_super',
  TEAMS_MANAGE_SUPER: 'teams_manage_super',
  TEAMS_MANAGE: 'teams_manage',

  USERS_VIEW: 'users_view',
  USERS_MANAGE: 'users_manage',
  USERS_VIEW_SUPER: 'users_view_super',
  USERS_MANAGE_SUPER: 'users_manage_super',

  ROLES_VIEW: 'roles_view',
  ROLES_MANAGE: 'roles_manage',
  ROLES_VIEW_SUPER: 'roles_view_super',
  ROLES_MANAGE_SUPER: 'roles_manage_super',

  // Variable Mapping
  PEGA_VARIABLE_MAPPING_VIEW: 'pega_variable_mapping_view',
  PEGA_VARIABLE_MAPPING_MANAGE: 'pega_variable_mapping_manage',
  PEGA_VARIABLE_MAPPING_REVIEW: 'pega_variable_mapping_review',
  PEGA_VARIABLE_MAPPING_APPROVE: 'pega_variable_mapping_approve',

  KT_VARIABLE_MAPPING_VIEW: 'kt_variable_mapping_view',
  KT_VARIABLE_MAPPING_MANAGE: 'kt_variable_mapping_manage',
  KT_VARIABLE_MAPPING_REVIEW: 'kt_variable_mapping_review',
  KT_VARIABLE_MAPPING_APPROVE: 'kt_variable_mapping_approve',

  // Message Centre
  MESSAGE_CENTRE_CAMPAIGNS_VIEW: 'message_centre_campaigns_view',
  MESSAGE_CENTRE_CAMPAIGNS_MANAGE: 'message_centre_campaigns_manage',

  MESSAGE_CENTRE_MESSAGES_VIEW: 'message_centre_messages_view',
  MESSAGE_CENTRE_MESSAGES_MANAGE: 'message_centre_messages_manage',

  // Offers Management
  OFFERS_VIEW: 'offers_view',
  OFFERS_MANAGE: 'offers_manage',
  OFFERS_REVIEW: 'offers_review',
  OFFERS_APPROVE: 'offers_approve',
};

// The following permissions are not configurable on the FE and should be assigned to a team only if they own an application
const APPLICATION_OWNER_PERMISSION_LIST = [
  permissions.APPLICATIONS_VIEW,
  permissions.APPLICATIONS_MANAGE,
  permissions.PAGES_VIEW,
  permissions.PAGES_MANAGE,
  permissions.CONTAINERS_VIEW,
  permissions.CONTAINERS_MANAGE,
];

const APPLICATION_VIEWER_PERMISSION_LIST = [
  permissions.APPLICATIONS_VIEW,
  permissions.PAGES_VIEW,
  permissions.CONTAINERS_VIEW,
];

const CAMPAIGN_ALERT_PERMISSIONS = [
  permissions.CAMPAIGNS_VIEW,
  permissions.CAMPAIGNS_MANAGE,
  permissions.CAMPAIGNS_REVIEW,
  permissions.CAMPAIGNS_APPROVE,
  permissions.CCAU_CAMPAIGNS_VIEW,
  permissions.CCAU_CAMPAIGNS_MANAGE,
  permissions.CCAU_CAMPAIGNS_REVIEW,
  permissions.CCAU_CAMPAIGNS_APPROVE,
  permissions.ALERTS_VIEW,
  permissions.ALERTS_MANAGE,
  permissions.ALERTS_APPROVE,
];

const REVIEW_APPROVE_PERMISSIONS = [
  permissions.CAMPAIGNS_REVIEW,
  permissions.CAMPAIGNS_APPROVE,
  permissions.CCAU_CAMPAIGNS_REVIEW,
  permissions.CCAU_CAMPAIGNS_APPROVE,
  permissions.ALERTS_APPROVE,
  permissions.PEGA_VARIABLE_MAPPING_REVIEW,
  permissions.PEGA_VARIABLE_MAPPING_APPROVE,
  permissions.KT_VARIABLE_MAPPING_REVIEW,
  permissions.KT_VARIABLE_MAPPING_APPROVE,
  permissions.OFFERS_REVIEW,
  permissions.OFFERS_APPROVE,
];

const VIEW_PERMISSION_LIST = [
  permissions.CAMPAIGNS_VIEW,
  permissions.CCAU_CAMPAIGNS_VIEW,
  permissions.ALERTS_VIEW,
  permissions.APPLICATIONS_VIEW,
  permissions.APPLICATIONS_VIEW_SUPER,
  permissions.PAGES_VIEW,
  permissions.PAGES_VIEW_SUPER,
  permissions.CONTAINERS_VIEW,
  permissions.CONTAINERS_VIEW_SUPER,
  permissions.TEAMS_VIEW,
  permissions.TEAMS_VIEW_SUPER,
  permissions.USERS_VIEW,
  permissions.USERS_VIEW_SUPER,
  permissions.ROLES_VIEW,
  permissions.ROLES_VIEW_SUPER,
  permissions.PEGA_VARIABLE_MAPPING_VIEW,
  permissions.KT_VARIABLE_MAPPING_VIEW,
];

// TODO: Add KT permissions
const FULL_PERMISSION_LIST = [
  permissions.CAMPAIGNS_VIEW,
  permissions.CAMPAIGNS_MANAGE,
  permissions.CAMPAIGNS_REVIEW,
  permissions.CAMPAIGNS_APPROVE,
  permissions.CCAU_CAMPAIGNS_VIEW,
  permissions.CCAU_CAMPAIGNS_MANAGE,
  permissions.CCAU_CAMPAIGNS_REVIEW,
  permissions.CCAU_CAMPAIGNS_APPROVE,
  permissions.ALERTS_VIEW,
  permissions.ALERTS_MANAGE,
  permissions.ALERTS_APPROVE,
  permissions.APPLICATIONS_VIEW,
  permissions.APPLICATIONS_MANAGE,
  permissions.APPLICATIONS_VIEW_SUPER,
  permissions.APPLICATIONS_MANAGE_SUPER,
  permissions.PAGES_VIEW,
  permissions.PAGES_MANAGE,
  permissions.PAGES_VIEW_SUPER,
  permissions.PAGES_MANAGE_SUPER,
  permissions.CONTAINERS_VIEW,
  permissions.CONTAINERS_MANAGE,
  permissions.CONTAINERS_VIEW_SUPER,
  permissions.CONTAINERS_MANAGE_SUPER,
  permissions.TEAMS_VIEW,
  permissions.TEAMS_MANAGE,
  permissions.TEAMS_VIEW_SUPER,
  permissions.TEAMS_MANAGE_SUPER,
  permissions.USERS_VIEW,
  permissions.USERS_MANAGE,
  permissions.USERS_VIEW_SUPER,
  permissions.USERS_MANAGE_SUPER,
  permissions.ROLES_VIEW,
  permissions.ROLES_MANAGE,
  permissions.ROLES_VIEW_SUPER,
  permissions.ROLES_MANAGE_SUPER,
  permissions.PEGA_VARIABLE_MAPPING_VIEW,
  permissions.PEGA_VARIABLE_MAPPING_MANAGE,
  permissions.PEGA_VARIABLE_MAPPING_REVIEW,
  permissions.PEGA_VARIABLE_MAPPING_APPROVE,
  permissions.MESSAGE_CENTRE_CAMPAIGNS_VIEW,
  permissions.MESSAGE_CENTRE_CAMPAIGNS_MANAGE,
  permissions.MESSAGE_CENTRE_MESSAGES_VIEW,
  permissions.MESSAGE_CENTRE_MESSAGES_MANAGE,
  permissions.OFFERS_VIEW,
  permissions.OFFERS_MANAGE,
  permissions.OFFERS_REVIEW,
  permissions.OFFERS_APPROVE,
];

const STATIC_PERMISSION_SECTIONS = {
  placement: [
    permissions.APPLICATIONS_VIEW,
    permissions.APPLICATIONS_MANAGE,
    permissions.APPLICATIONS_VIEW_SUPER,
    permissions.APPLICATIONS_MANAGE_SUPER,
    permissions.PAGES_VIEW,
    permissions.PAGES_MANAGE,
    permissions.PAGES_VIEW_SUPER,
    permissions.PAGES_MANAGE_SUPER,
    permissions.CONTAINERS_VIEW,
    permissions.CONTAINERS_MANAGE,
    permissions.CONTAINERS_VIEW_SUPER,
    permissions.CONTAINERS_MANAGE_SUPER,
  ],
  accessControl: [
    permissions.TEAMS_VIEW,
    permissions.TEAMS_MANAGE,
    permissions.TEAMS_MANAGE_SUPER,
    permissions.TEAMS_MANAGE_SUPER,
    permissions.USERS_VIEW,
    permissions.USERS_MANAGE,
    permissions.USERS_VIEW_SUPER,
    permissions.USERS_MANAGE_SUPER,
    permissions.ROLES_VIEW,
    permissions.ROLES_MANAGE,
    permissions.ROLES_VIEW_SUPER,
    permissions.ROLES_MANAGE_SUPER,
  ],
  variableMapping: [
    permissions.PEGA_VARIABLE_MAPPING_VIEW,
    permissions.PEGA_VARIABLE_MAPPING_MANAGE,
  ],
};

const FLAGS = {
  SKIP_ACCESS: 'skipAccess',
  SKIP_ALL_EXCEPT_TEAM_OWNERS: 'skipAllExceptTeamOwners',
  SKIP_ALL: 'skipAll',
  INCLUDE_FUNCTIONALITIES: 'includeFunctionalities',
};

const permissionSelectionsMappings = {
  campaigns: {
    name: 'Campaigns',
    actions: {
      view: permissions.CAMPAIGNS_VIEW,
      manage: permissions.CAMPAIGNS_MANAGE,
      review: permissions.CAMPAIGNS_REVIEW,
      approve: permissions.CAMPAIGNS_APPROVE,
    },
  },
  ccau_campaigns: {
    name: 'CCAU Campaigns',
    actions: {
      view: permissions.CCAU_CAMPAIGNS_VIEW,
      manage: permissions.CCAU_CAMPAIGNS_MANAGE,
      review: permissions.CCAU_CAMPAIGNS_REVIEW,
      approve: permissions.CCAU_CAMPAIGNS_APPROVE,
    },
  },
  alerts: {
    name: 'Alerts',
    actions: {
      view: permissions.ALERTS_VIEW,
      manage: permissions.ALERTS_MANAGE,
      approve: permissions.ALERTS_APPROVE,
    },
  },
  applications: {
    name: 'Applications (own application)',
    actions: {
      view: permissions.APPLICATIONS_VIEW,
      manage: permissions.APPLICATIONS_MANAGE,
    },
  },
  applications_super: {
    name: 'Applications (all applications)',
    actions: {
      view: permissions.APPLICATIONS_VIEW_SUPER,
      manage: permissions.APPLICATIONS_MANAGE_SUPER,
    },
  },
  pages: {
    name: 'Pages (own application)',
    actions: {
      view: permissions.PAGES_VIEW,
      manage: permissions.PAGES_MANAGE,
    },
  },
  pages_super: {
    name: 'Pages (all applications)',
    actions: {
      view: permissions.PAGES_VIEW_SUPER,
      manage: permissions.PAGES_MANAGE_SUPER,
    },
  },
  containers: {
    name: 'Containers (own application)',
    actions: {
      view: permissions.CONTAINERS_VIEW,
      manage: permissions.CONTAINERS_MANAGE,
    },
  },
  containers_super: {
    name: 'Containers (all applications)',
    actions: {
      view: permissions.CONTAINERS_VIEW_SUPER,
      manage: permissions.CONTAINERS_MANAGE_SUPER,
    },
  },
  teams: {
    name: 'Teams (own team)',
    actions: {
      view: permissions.TEAMS_VIEW,
      manage: permissions.TEAMS_MANAGE,
    },
  },
  teams_super: {
    name: 'Teams (all teams)',
    actions: {
      view: permissions.TEAMS_VIEW_SUPER,
      manage: permissions.TEAMS_MANAGE_SUPER,
    },
  },
  users: {
    name: 'Users (own team)',
    actions: {
      view: permissions.USERS_VIEW,
      manage: permissions.USERS_MANAGE,
    },
  },
  users_super: {
    name: 'Users (all teams)',
    actions: {
      view: permissions.USERS_VIEW_SUPER,
      manage: permissions.USERS_MANAGE_SUPER,
    },
  },
  roles: {
    name: 'Roles (own team)',
    actions: {
      view: permissions.ROLES_VIEW,
      manage: permissions.ROLES_MANAGE,
    },
  },
  roles_super: {
    name: 'Roles (all teams)',
    actions: {
      view: permissions.ROLES_VIEW_SUPER,
      manage: permissions.ROLES_MANAGE_SUPER,
    },
  },
  pega_variable_mapping: {
    name: 'Pega variable mapping',
    actions: {
      view: permissions.PEGA_VARIABLE_MAPPING_VIEW,
      manage: permissions.PEGA_VARIABLE_MAPPING_MANAGE,
      review: permissions.PEGA_VARIABLE_MAPPING_REVIEW,
      approve: permissions.PEGA_VARIABLE_MAPPING_APPROVE,
    },
  },
  message_centre_campaigns: {
    name: 'Message Centre (Campaigns)',
    actions: {
      view: permissions.MESSAGE_CENTRE_CAMPAIGNS_VIEW,
      manage: permissions.MESSAGE_CENTRE_CAMPAIGNS_MANAGE,
    },
  },
  message_centre_messages: {
    name: 'Message Centre (Messages)',
    actions: {
      view: permissions.MESSAGE_CENTRE_MESSAGES_VIEW,
      manage: permissions.MESSAGE_CENTRE_MESSAGES_MANAGE,
    },
  },
  offers: {
    name: 'Offers',
    actions: {
      view: permissions.OFFERS_VIEW,
      manage: permissions.OFFERS_MANAGE,
      review: permissions.OFFERS_REVIEW,
      approve: permissions.OFFERS_APPROVE,
    },
  },
};

module.exports = {
  FULL_PERMISSION_LIST,
  CAMPAIGN_ALERT_PERMISSIONS,
  APPLICATION_OWNER_PERMISSION_LIST,
  APPLICATION_VIEWER_PERMISSION_LIST,
  VIEW_PERMISSION_LIST,
  REVIEW_APPROVE_PERMISSIONS,
  STATIC_PERMISSION_SECTIONS,
  FLAGS,
  permissionSelectionsMappings,
  permissionsV1,
  permissions,
};
