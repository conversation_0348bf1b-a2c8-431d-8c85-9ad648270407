const middleware = require('./middleware');
const { permissionsV1, permissions } = require('./permission-constants');

// to be removed soon
// ex "resolvePermission" to keep backward compatability with older "solution"
const resolvePermission = ({ permissions }, permissionName) => (
  permissions && (permissions.includes(permissionName) || permissions.includes(permissionsV1.PERMISSION_ADMIN))
);

module.exports = {
  middleware,
  resolvePermission,
  ...permissionsV1,
  ...permissions,
};
