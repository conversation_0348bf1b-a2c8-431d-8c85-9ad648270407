const {
  validateCrossTeamRequest,
  mapAccessToRulePermissions,
} = require('./utils');

const { ForbiddenError } = require('../error');

jest.mock('../error');

describe('permissions utils', () => {
  it('validateCrossTeamRequest to throw ', () => {
    const res = {
      locals: {
        user: {
          team_id: 1,
          permissions: [ 'campaigns_view' ],
        },
      },
    };
    expect(() => {
      validateCrossTeamRequest({ permissionRequired: 'users_view_super', teamId: 2, res });
    }).toThrow(ForbiddenError);
  });

  it('validateCrossTeamRequest to not throw', () => {
    const res = {
      locals: {
        user: {
          team_id: 1,
          permissions: [ 'campaigns_view' ],
        },
      },
    };

    expect(() => {
      validateCrossTeamRequest({ permissionRequired: 'admin', teamId: 2, res });
    }).toThrow('');
  });

  it('mapAccessToRulePermissions should return fullPermissions, viewPermissions', () => {
    const access = {
      containers: undefined,
      pages: undefined,
      ruleSubTypes: undefined,
    };
    const mappedAccess = mapAccessToRulePermissions(access, [
      {
        admin_rule_type_name: 'alert',
        admin_rule_type_id: 1,
      },
    ]);

    expect(mappedAccess).toEqual(
      { fullPermissions: [], viewPermissions: [] },
    );
  });
});
