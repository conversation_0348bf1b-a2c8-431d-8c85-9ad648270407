const { ForbiddenError } = require('../error');
const { PERMISSION_ADMIN } = require('../permissions');
const { permissionSelectionsMappings } = require('../permissions/permission-constants');

/**
 * Access and placement endpoints require protection against cross team data access and modifications.
 * If request is for object belonging to a different team from current user's team,
 * admin or super permission is required. If current user does not have sufficient permissions,
 * forbidden error is thrown, and request is expected to be denied.
 *
 * @param {string} permissionRequired - permission required to access or modify
 * @param {number} teamId - id of team intended to access or modify
 * @param {object} res - response object containing current user data
 */
const validateCrossTeamRequest = ({ permissionRequired, teamId, res }) => {
  const permissions = res.locals.user.permissions;
  const currentTeamId = res.locals.user.team_id;
  const isAdmin = permissions.includes(PERMISSION_ADMIN);
  if (!isAdmin && !permissions.includes(permissionRequired) && Number(teamId) !== currentTeamId) {
    throw new ForbiddenError();
  }
};

/**
 * Map team's campaign/alert access to campaign/alert permissions
 *
 * @param {object} access - permission required to access or modify
 * @param {object} ruleTypes - id of team intended to access or modify
 */
const mapAccessToRulePermissions = (access, ruleTypes) => {
  let fullPermissions = [];
  let viewPermissions = [];

  if (!access) {
    return {
      fullPermissions,
      viewPermissions,
    };
  }

  const campaignRuleTypeIds = ruleTypes.filter(r => r.admin_rule_type_name.toLowerCase() !== 'alert' && r.admin_rule_type_name.toLowerCase() !== 'ccau_campaign').map(r => r.admin_rule_type_id);
  const ccauCampaignRuleType = ruleTypes.find(r => r.admin_rule_type_name.toLowerCase() === 'ccau_campaign');
  const alertRuleTypeId = ruleTypes.find(r => r.admin_rule_type_name.toLowerCase() === 'alert').admin_rule_type_id;

  const accesssMap = {
    campaigns: [
      ...(access.containers || []).filter(c => campaignRuleTypeIds.includes(c.rule_type_id)),
      ...(access.pages || []).filter(p => campaignRuleTypeIds.includes(p.rule_type_id)),
      ...(access.ruleSubTypes || []).filter(r => campaignRuleTypeIds.includes(r.rule_type_id)),
    ],
    alerts: [
      ...(access.containers || []).filter(c => c.rule_type_id === alertRuleTypeId),
      ...(access.pages || []).filter(p => p.rule_type_id === alertRuleTypeId),
      ...(access.ruleSubTypes || []).filter(r => r.rule_type_id === alertRuleTypeId),
    ],
    ccau_campaigns: [
      ...(access.containers || []).filter(c => ccauCampaignRuleType ? ccauCampaignRuleType.admin_rule_type_id : undefined === c.rule_type_id),
      ...(access.pages || []).filter(p => ccauCampaignRuleType ? ccauCampaignRuleType.admin_rule_type_id : undefined === p.rule_type_id),
    ],
  };

  Object.keys(accesssMap).forEach(key => {
    if (accesssMap[key].some(i => i.access === 'manage')) {
      fullPermissions = [
        ...fullPermissions,
        permissionSelectionsMappings[key].actions.view,
        permissionSelectionsMappings[key].actions.manage,
        permissionSelectionsMappings[key].actions.review,
        permissionSelectionsMappings[key].actions.approve,
      ].filter(p => p);
      viewPermissions = [ ...viewPermissions, permissionSelectionsMappings[key].actions.view ];
    } else if (accesssMap[key].some(i => i.access === 'view')) {
      fullPermissions = [ ...fullPermissions, permissionSelectionsMappings[key].actions.view ];
      viewPermissions = [ ...viewPermissions, permissionSelectionsMappings[key].actions.view ];
    }
  });

  return {
    fullPermissions,
    viewPermissions,
  };
};

module.exports = {
  validateCrossTeamRequest,
  mapAccessToRulePermissions,
};
