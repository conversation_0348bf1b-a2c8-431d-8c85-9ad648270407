const {
  draftToSubmitted,
  submittedToDraft,
  submittedToReviewed,
  noSpecial<PERSON><PERSON><PERSON>hanged,
  reviewedToDraft,
  reviewedToPublished,
  disableCampaign,
  enableCampaign,
  terminateCampaign,
  createdWithValidStatus,
  activeVariableMappingSetDraft,
  draftVariableMappingSetPending,
  editVariableMappingSetDraft,
  pendingVariableMappingSetActive,
  draftVariableMappingSetDeleted,
  pendingVariableMappingSetDraft,
  offerCreatedWithValidStatus,
  inactivateOffer,
  activateOffer,
} = require('./transactions');

const {
  resolvePermission,
  PERMISSION_ADMIN,
  CAMPAIGNS_MANAGE,
  CAMPAIGNS_REVIEW,
  CAMPAIGNS_APPROVE,
  PEGA_VARIABLE_MAPPING_MANAGE,
  PEGA_VARIABLE_MAPPING_APPROVE,
  CCAU_CAMPAIGNS_MANAGE,
  CCAU_CAMPAIGNS_APPROVE,
  CCAU_CAMPAIGNS_RE<PERSON>E<PERSON>,
  OFFERS_MANA<PERSON>,
  OFFERS_APPROVE,
  OFFERS_REVIEW,
} = require('../index');

const canUpdateCampaign = (user, campaignBefore, body) => {
  const conditions = [
    draftToSubmitted(campaignBefore, body) && (resolvePermission(user, CAMPAIGNS_MANAGE) || resolvePermission(user, CCAU_CAMPAIGNS_MANAGE)),
    submittedToDraft(campaignBefore, body) && (resolvePermission(user, CAMPAIGNS_REVIEW) || resolvePermission(user, CCAU_CAMPAIGNS_REVIEW)),
    submittedToReviewed(campaignBefore, body) && (resolvePermission(user, CAMPAIGNS_REVIEW) || resolvePermission(user, CCAU_CAMPAIGNS_REVIEW)),
    reviewedToDraft(campaignBefore, body) && (resolvePermission(user, CAMPAIGNS_APPROVE) || resolvePermission(user, CCAU_CAMPAIGNS_APPROVE)),
    reviewedToPublished(campaignBefore, body) && (resolvePermission(user, CAMPAIGNS_APPROVE) || resolvePermission(user, CCAU_CAMPAIGNS_APPROVE)),
    disableCampaign(campaignBefore, body) && (resolvePermission(user, CAMPAIGNS_MANAGE) || resolvePermission(user, CCAU_CAMPAIGNS_MANAGE)),
    enableCampaign(campaignBefore, body) && (resolvePermission(user, CAMPAIGNS_MANAGE) || resolvePermission(user, CCAU_CAMPAIGNS_MANAGE)),
    terminateCampaign(campaignBefore, body) && (resolvePermission(user, CAMPAIGNS_MANAGE) || resolvePermission(user, CCAU_CAMPAIGNS_MANAGE)),
    noSpecialFieldChanged(campaignBefore, body) && (resolvePermission(user, CAMPAIGNS_MANAGE) || resolvePermission(user, CCAU_CAMPAIGNS_MANAGE)),
  ];
  return conditions.filter(condition => condition === true).length === 1;
};

const canUpdateOffer = (user, offerBefore, reqBody) => {
  const conditions = [
    draftToSubmitted(offerBefore, reqBody) && resolvePermission(user, OFFERS_MANAGE),
    submittedToDraft(offerBefore, reqBody) && resolvePermission(user, OFFERS_REVIEW),
    submittedToReviewed(offerBefore, reqBody) && resolvePermission(user, OFFERS_REVIEW),
    reviewedToDraft(offerBefore, reqBody) && resolvePermission(user, OFFERS_APPROVE),
    reviewedToPublished(offerBefore, reqBody) && resolvePermission(user, OFFERS_APPROVE),
    inactivateOffer(offerBefore) && resolvePermission(user, OFFERS_MANAGE),
    activateOffer(offerBefore) && resolvePermission(user, OFFERS_MANAGE),
  ];
  return conditions.filter(condition => condition === true).length === 1;
};

const canCreateCampaign = (user, body) => {
  const conditions = [
    createdWithValidStatus(body) && (resolvePermission(user, CAMPAIGNS_MANAGE) || resolvePermission(user, CCAU_CAMPAIGNS_MANAGE)),
  ];
  return conditions.filter(condition => condition === true).length === 1;
};

const canCreateOffer = (user, reqBody) => {
  const conditions = [
    offerCreatedWithValidStatus(reqBody) && resolvePermission(user, OFFERS_MANAGE),
  ];
  return conditions.filter(condition => condition === true).length === 1;
};

const userIsApprover = (originalVariableMappingSet, user) =>
  originalVariableMappingSet.approver_sid === user.sid;

const canUpdateVariableMappings = (user, originalVariableMappingSet, body) => {
  const conditions = [
    draftVariableMappingSetPending(originalVariableMappingSet, body) && resolvePermission(user, PEGA_VARIABLE_MAPPING_MANAGE),
    draftVariableMappingSetDeleted(originalVariableMappingSet, body) && resolvePermission(user, PEGA_VARIABLE_MAPPING_MANAGE),
    activeVariableMappingSetDraft(originalVariableMappingSet, body) && resolvePermission(user, PEGA_VARIABLE_MAPPING_MANAGE),
    editVariableMappingSetDraft(originalVariableMappingSet, body) && resolvePermission(user, PEGA_VARIABLE_MAPPING_MANAGE),
    pendingVariableMappingSetDraft(originalVariableMappingSet, body, user) && resolvePermission(user, PEGA_VARIABLE_MAPPING_APPROVE),
    (pendingVariableMappingSetActive(originalVariableMappingSet, body) && (userIsApprover(originalVariableMappingSet, user) || resolvePermission(user, PERMISSION_ADMIN))) && resolvePermission(user, PEGA_VARIABLE_MAPPING_APPROVE),
  ];
  return conditions.filter(condition => condition === true).length >= 1;
};

const canCreateVariableMappingDraft = (user) => {
  return resolvePermission(user, PEGA_VARIABLE_MAPPING_MANAGE);
};

const canUpdateVariableMappingDraft = (user) => {
  return resolvePermission(user, PEGA_VARIABLE_MAPPING_MANAGE);
};

module.exports = {
  canUpdateCampaign,
  canCreateCampaign,
  canUpdateVariableMappings,
  canCreateVariableMappingDraft,
  canUpdateVariableMappingDraft,
  canCreateOffer,
  canUpdateOffer,
};
