const { canUpdateCampaign, canUpdateVariableMappings } = require('./index');
const permissionsServiceMock = require('__mocks__/permissionService');

const createMockCampaign = (overwrites) => ({
  container: 'my-activity',
  content_id: '437BeQbV0seae2QkusuOCa',
  content_space: '4szkx38resvm',
  content_type: 'targetedCampaignTemplate1',
  created_at: '2018-12-20T20:38:11.047Z',
  created_by: 's7646571',
  disabled: false,
  end_at: '2019-01-24T05:00:00.000Z',
  external_ref: null,
  id: 'D49VEXWng9dX',
  name: 'testing20thdec',
  pages: [ 'my-activity' ],
  platforms: [ 'ios' ],
  start_at: '2018-12-22T05:00:00.000Z',
  status: 'draft',
  updated_at: '2018-12-20T20:47:29.337Z',
  updated_by: 's7646571',
  urgent: false,
  ...overwrites,
});

const createVariableMappingSet = (overwrites) => ({
  updated_by_sid: 's7646571',
  status: 'draft',
  description: 'test',
  variables: [
    {
      variable_template: 'SOLUI_CUST_FULL_NAME_END',
      variable_campaign: 'cust_full_name',
      variable_type: 'text',
    },
  ],
  ...overwrites,
});

const access = {
  campaigns: {
    applications: {},
    containers: {},
    pages: {},
  },
  alerts: {
    applications: {},
    containers: {},
    pages: {},
  },
};

const mockWorkflowManagerUser = { sid: 's123456', permissions: permissionsServiceMock.getPermissionsForUser(2), access };
const mockCampaignManagerUser = { sid: 's998877', permissions: permissionsServiceMock.getPermissionsForUser(3), access };
const mockAdminUser = { sid: 's998879', permissions: permissionsServiceMock.getPermissionsForUser(1), access };

describe('correct campaign permissions', () => {
  it('Campaign patch: workflow manager / status draft to submitted / positive', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      status: 'draft',
    });
    const campaignAfterUpdate = createMockCampaign({
      status: 'submitted',
    });
    const allow = canUpdateCampaign(mockWorkflowManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Campaign patch: workflow manager / status submitted to draft / positive', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      status: 'submitted',
    });
    const campaignAfterUpdate = createMockCampaign({
      status: 'draft',
    });
    const allow = canUpdateCampaign(mockWorkflowManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Campaign patch: workflow manager / status submitted to reviewed / positive', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      status: 'submitted',
    });
    const campaignAfterUpdate = createMockCampaign({
      status: 'reviewed',
    });
    const allow = canUpdateCampaign(mockWorkflowManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Campaign patch: workflow manager / update campaign details / positive', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      name: 'oldName',
    });
    const campaignAfterUpdate = createMockCampaign({
      name: 'newName',
    });
    const allow = canUpdateCampaign(mockWorkflowManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Campaign patch: workflow manager / update campaign details and put in invalid status / negative', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      name: 'oldName',
    });
    const campaignAfterUpdate = createMockCampaign({
      name: 'newName',
      status: 'invalid status',
    });
    const allow = canUpdateCampaign(mockWorkflowManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(false);
  });

  it('Campaign patch: campaign manager / status reviewed to draft / positive', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      status: 'reviewed',
    });
    const campaignAfterUpdate = createMockCampaign({
      status: 'draft',
    });
    const allow = canUpdateCampaign(mockCampaignManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Campaign patch: workflow manager / status reviewed to draft / negative', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      status: 'reviewed',
    });
    const campaignAfterUpdate = createMockCampaign({
      status: 'draft',
    });
    const allow = canUpdateCampaign(mockWorkflowManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(false);
  });

  it('Campaign patch: campaign manager / update campaign details / positive', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      name: 'oldName',
    });
    const campaignAfterUpdate = createMockCampaign({
      name: 'newName',
    });
    const allow = canUpdateCampaign(mockCampaignManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Campaign patch: campaign manager / status reviewed to published / positive', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      status: 'reviewed',
    });
    const campaignAfterUpdate = createMockCampaign({
      status: 'published',
    });
    const allow = canUpdateCampaign(mockCampaignManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Campaign patch: workflow manager / status reviewed to published / negative', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      status: 'reviewed',
    });
    const campaignAfterUpdate = createMockCampaign({
      status: 'published',
    });
    const allow = canUpdateCampaign(mockWorkflowManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(false);
  });

  it('Campaign patch: workflow manager / update campaign details / positive', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      name: 'oldName',
    });
    const campaignAfterUpdate = createMockCampaign({
      name: 'newName',
    });
    const allow = canUpdateCampaign(mockWorkflowManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Campaign patch: campaign manager / disable campaign / positive', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      status: 'published',
    });
    const campaignAfterUpdate = createMockCampaign({
      disabled: true,
    });
    const allow = canUpdateCampaign(mockCampaignManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Campaign patch: campaign manager / disable campaign from invalid status / negative', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      status: 'draft',
    });
    const campaignAfterUpdate = createMockCampaign({
      disabled: true,
    });
    const allow = canUpdateCampaign(mockCampaignManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(false);
  });

  it('Campaign patch: workflow manager / do multiple allowed actions at the same time / positive', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      status: 'submitted',
      name: 'some name 1',
    });
    const campaignAfterUpdate = createMockCampaign({
      status: 'draft',
      name: 'some name 2',
    });
    const allow = canUpdateCampaign(mockWorkflowManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Campaign patch: workflow manager / object with no change / positive', async() => {
    const campaignBeforeUpdate = createMockCampaign({
      status: 'draft',
      name: 'name test',
    });
    const campaignAfterUpdate = createMockCampaign({
      status: 'draft',
      name: 'name test',
    });
    const allow = canUpdateCampaign(mockWorkflowManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Variable Mapping Patch: workflow manager / update variable set from active to draft', async() => {
    const campaignBeforeUpdate = createVariableMappingSet({
      status: 'active',
    });
    const campaignAfterUpdate = createVariableMappingSet({
      status: 'draft',
    });
    const allow = canUpdateVariableMappings(mockCampaignManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Variable Mapping Patch: workflow manager / update variable set from draft to pending', async() => {
    const campaignBeforeUpdate = createVariableMappingSet({
      status: 'draft',
    });
    const campaignAfterUpdate = createVariableMappingSet({
      status: 'pending',
    });
    const allow = canUpdateVariableMappings(mockCampaignManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Variable Mapping Patch: workflow manager / update variable set from pending to draft', async() => {
    const campaignBeforeUpdate = createVariableMappingSet({
      status: 'pending',
    });
    const campaignAfterUpdate = createVariableMappingSet({
      status: 'draft',
    });
    const allow = canUpdateVariableMappings(mockWorkflowManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Variable Mapping Patch: workflow manager / update variable set from pending to active / negative', async() => {
    const campaignBeforeUpdate = createVariableMappingSet({
      status: 'pending',
    });
    const campaignAfterUpdate = createVariableMappingSet({
      status: 'active',
    });
    const allow = canUpdateVariableMappings(mockCampaignManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(false);
  });

  it('Variable Mapping Patch: workflow manager / update variable set from pending to active / positive', async() => {
    const campaignBeforeUpdate = createVariableMappingSet({
      status: 'pending',
      approver_sid: mockWorkflowManagerUser.sid,
    });
    const campaignAfterUpdate = createVariableMappingSet({
      status: 'active',
    });
    const allow = canUpdateVariableMappings(mockWorkflowManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });

  it('Variable Mapping Patch: workflow manager / update variable set from pending to active when not approver / negative', async() => {
    const campaignBeforeUpdate = createVariableMappingSet({
      status: 'pending',
      approver_sid: 's1111111',
    });
    const campaignAfterUpdate = createVariableMappingSet({
      status: 'active',
    });
    const allow = canUpdateVariableMappings(mockWorkflowManagerUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(false);
  });

  it('Variable Mapping Patch: admin / update variable set from pending to active when not approver / positive', async() => {
    const campaignBeforeUpdate = createVariableMappingSet({
      status: 'pending',
      approver_sid: 's1111111',
    });
    const campaignAfterUpdate = createVariableMappingSet({
      status: 'active',
    });
    const allow = canUpdateVariableMappings(mockAdminUser, campaignBeforeUpdate, campaignAfterUpdate);
    expect(allow).toBe(true);
  });
});
