const specialProperties = [
  'status',
  'disabled',
];

const isUndefined = (value) => typeof value === 'undefined';

const draftToSubmitted = (oldCampaign, requestBody) => {
  if (!oldCampaign.status || !requestBody.status) {
    return false;
  } else {
    const statusBefore = oldCampaign.status.toLowerCase();
    const statusAfter = requestBody.status.toLowerCase();
    return statusBefore === 'draft' && statusAfter === 'submitted';
  }
};

const submittedToDraft = (oldCampaign, requestBody) => {
  if (!oldCampaign.status || !requestBody.status) {
    return false;
  } else {
    const statusBefore = oldCampaign.status.toLowerCase();
    const statusAfter = requestBody.status.toLowerCase();
    return statusBefore === 'submitted' && statusAfter === 'draft';
  }
};

const submittedToReviewed = (oldCampaign, requestBody) => {
  if (!oldCampaign.status || !requestBody.status) {
    return false;
  } else {
    const statusBefore = oldCampaign.status.toLowerCase();
    const statusAfter = requestBody.status.toLowerCase();
    return statusBefore === 'submitted' && statusAfter === 'reviewed';
  }
};

const reviewedToDraft = (oldCampaign, requestBody) => {
  if (!oldCampaign.status || !requestBody.status) {
    return false;
  } else {
    const statusBefore = oldCampaign.status.toLowerCase();
    const statusAfter = requestBody.status.toLowerCase();
    return statusBefore === 'reviewed' && statusAfter === 'draft';
  }
};

const reviewedToPublished = (oldCampaign, requestBody) => {
  if (!oldCampaign.status || !requestBody.status) {
    return false;
  } else {
    const statusBefore = oldCampaign.status.toLowerCase();
    const statusAfter = requestBody.status.toLowerCase();
    return statusBefore === 'reviewed' && statusAfter === 'published';
  }
};

const disableCampaign = (oldCampaign, requestBody) => {
  if (!oldCampaign.status || isUndefined(requestBody.disabled)) {
    return false;
  } else {
    const statusBefore = oldCampaign.status.toLowerCase();
    return statusBefore === 'published' && requestBody.disabled === true;
  }
};

const enableCampaign = (oldCampaign, requestBody) => {
  if (!oldCampaign.status || isUndefined(requestBody.disabled)) {
    return false;
  } else {
    const statusBefore = oldCampaign.status.toLowerCase();
    return statusBefore === 'published' && requestBody.disabled === false;
  }
};

const terminateCampaign = (oldCampaign, requestBody) => {
  if (!oldCampaign.status || !requestBody.status) {
    return false;
  } else {
    const statusBefore = oldCampaign.status.toLowerCase();
    const statusAfter = requestBody.status.toLowerCase();
    return statusBefore === 'published' && statusAfter === 'terminated';
  }
};

const noSpecialFieldChanged = (oldCampaign, newCampaign) => {
  if (Object.keys(newCampaign).length === 0) {
    return true;
  }
  return !Object.keys(oldCampaign).reduce((updatedSpecialProperties, key) => {
    if (!isUndefined(oldCampaign[key]) && !isUndefined(newCampaign[key])) {
      if (JSON.stringify(oldCampaign[key]) !== JSON.stringify(newCampaign[key])) {
        if (specialProperties.includes(key)) {
          updatedSpecialProperties = true;
        }
      }
    }
    return updatedSpecialProperties;
  }, false);
};

const createdWithValidStatus = (body) => {
  const { status, disabled } = body;
  return !disabled && (status && (status === 'draft' || status === 'submitted'));
};

const activeVariableMappingSetDraft = (originalVariableMappingSet, body) => originalVariableMappingSet.status === 'active' && body.status === 'draft';

// i don't think this is needed
const editVariableMappingSetDraft = (originalVariableMappingSet, body) => originalVariableMappingSet.status === 'draft' && body.status === 'draft';

const draftVariableMappingSetPending = (originalVariableMappingSet, body) => originalVariableMappingSet.status === 'draft' && body.status === 'pending';

const pendingVariableMappingSetActive = (originalVariableMappingSet, body) =>
  originalVariableMappingSet.status === 'pending' &&
  body.status === 'active';

const draftVariableMappingSetDeleted = (originalVariableMappingSet, body) =>
  originalVariableMappingSet.status === 'draft' &&
  body.status === 'deleted';

const pendingVariableMappingSetDraft = (originalVariableMappingSet, body) =>
  originalVariableMappingSet.status === 'pending' &&
  body.status === 'draft';

// Offer transactions
const offerCreatedWithValidStatus = (reqBody) => {
  const offerStatus = reqBody.offer_status;
  return offerStatus && (offerStatus === 'draft' || offerStatus === 'submitted');
};

const inactivateOffer = (oldOffer) => {
  const statusBefore = oldOffer.offer_status;
  if (!statusBefore) return false;
  return statusBefore === 'ACTIVE';
};

const activateOffer = (oldOffer) => {
  const statusBefore = oldOffer.offer_status;
  if (!statusBefore) return false;
  return statusBefore === 'INACTIVE';
};

module.exports = {
  draftToSubmitted,
  submittedToDraft,
  submittedToReviewed,
  reviewedToDraft,
  reviewedToPublished,
  noSpecialFieldChanged,
  disableCampaign,
  enableCampaign,
  terminateCampaign,
  createdWithValidStatus,
  activeVariableMappingSetDraft,
  draftVariableMappingSetPending,
  editVariableMappingSetDraft,
  pendingVariableMappingSetActive,
  draftVariableMappingSetDeleted,
  pendingVariableMappingSetDraft,
  offerCreatedWithValidStatus,
  inactivateOffer,
  activateOffer,
};
