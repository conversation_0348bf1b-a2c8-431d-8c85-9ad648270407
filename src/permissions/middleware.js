const { permissionsV1 } = require('./permission-constants');
const { ForbiddenError } = require('../error');

/**
 * Express middleware that checks for user permissions. _super version of a permission also allows access.
 *
 * @param {string[]|string} requiredPermissions - A single string, or array of permissions string to check for.
 *        (Multiple permissions are checked with operator supplied to second param)
 * @param {string} [operator=AND] - optional operator used to check requested required permissions,
 *        defaults to AND, can also be OR, considers all else as AND
 * @example
 * router.get('/rules', can([ 'view_rules' ], 'OR'), (req, res, next) => {
 *  // protected route
 * });
 */
const permissionMiddleware = (requiredPermissions, operator = 'AND') => (req, res, next) => {
  let permissions;
  try {
    permissions = res.locals.user.permissions;
  } catch (err) {
    return next(new ForbiddenError());
  }
  if (!permissions || !requiredPermissions) {
    return next(new ForbiddenError());
  }
  if (typeof requiredPermissions === 'string') {
    requiredPermissions = [ requiredPermissions ];
  }
  if (Array.isArray(requiredPermissions)) {
    const isAdmin = permissions.includes(permissionsV1.PERMISSION_ADMIN);
    const hasPermissions = requiredPermissions.map(requiredPermission => {
      const superEquivalent = `${requiredPermission}_super`;
      return permissions.includes(requiredPermission) || permissions.includes(superEquivalent);
    });
    const allowed = operator.toUpperCase() === 'OR' ? hasPermissions.includes(true) : !(hasPermissions.includes(false));
    if (!allowed && !isAdmin) {
      return next(new ForbiddenError());
    } else {
      return next();
    }
  } else {
    return next(new ForbiddenError());
  }
};

module.exports = permissionMiddleware;
