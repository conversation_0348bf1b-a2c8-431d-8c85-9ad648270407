const { middleware, ...permissions } = require('./index');
const { ForbiddenError } = require('../error/error');

const createResWithPermissions = (permissions = []) => {
  return {
    locals: {
      user: {
        permissions,
      },
    },
  };
};

describe('Permissions middleware', () => {
  it('Should not allow access if res.locals.permissions is falsy', () => {
    const handler = middleware();
    const nextMock = jest.fn();
    handler(null, null, nextMock);
    expect(nextMock.mock.calls[0][0] instanceof ForbiddenError).toBe(true);
  });

  it('Should deny access if no permissions passed to permission middleware', () => {
    const handler = middleware();
    const nextMock = jest.fn();
    handler(null, createResWithPermissions(), nextMock);
    expect(nextMock.mock.calls[0][0] instanceof ForbiddenError).toBe(true);
  });

  it('Should allow access if empty array of permissions passed', () => {
    const handler = middleware([]);
    const nextMock = jest.fn();
    handler(null, createResWithPermissions(), nextMock);
    expect(nextMock.mock.calls[0][0]).toBe(undefined);
  });

  it('Should allow to view campaigns if user has allow campaigns permission', () => {
    const handler = middleware([ permissions.CAMPAIGNS_VIEW ]);
    const nextMock = jest.fn();
    handler(null, createResWithPermissions([ 'campaigns_view' ]), nextMock);
    expect(nextMock.mock.calls[0][0]).toBe(undefined);
  });

  it(`Should allow to view campaigns if user has allow campaigns permission and it's passed as a string`, () => {
    const handler = middleware(permissions.CAMPAIGNS_VIEW);
    const nextMock = jest.fn();
    handler(null, createResWithPermissions([ 'campaigns_view' ]), nextMock);
    expect(nextMock.mock.calls[0][0]).toBe(undefined);
  });

  it(`Should deny access if a variable of an invaild type was passed to the middleware`, () => {
    const handler = middleware(1);
    const nextMock = jest.fn();
    handler(null, createResWithPermissions(1), nextMock);
    expect(nextMock.mock.calls[0][0] instanceof ForbiddenError).toBe(true);
  });

  it('Should now allow to view campaigns if user has allow campaigns permission', () => {
    const handler = middleware([ permissions.PERMISSION_CAMPAIGNS_VIEW ]);
    const nextMock = jest.fn();
    handler(null, createResWithPermissions([]), nextMock);
    expect(nextMock.mock.calls[0][0] instanceof ForbiddenError).toBe(true);
  });

  it('Should now allow to view campaigns and alerts if user only has view campaigns ', () => {
    const handler = middleware([ permissions.PERMISSION_CAMPAIGNS_VIEW ]);
    const nextMock = jest.fn();
    handler(null, createResWithPermissions([]), nextMock);
    expect(nextMock.mock.calls[0][0] instanceof ForbiddenError).toBe(true);
  });

  it('should allow access if equivalent super permission exists', () => {
    const handler = middleware([ permissions.ROLES_VIEW ]);
    const nextMock = jest.fn();
    handler(null, createResWithPermissions([ permissions.ROLES_VIEW_SUPER ]), nextMock);
    expect(nextMock.mock.calls[0][0]).toBe(undefined);
  });

  it('should allow access for partial permissions match when OR operator is applied', () => {
    const handler = middleware([ permissions.ROLES_VIEW, permissions.USERS_VIEW ], 'OR');
    const nextMock = jest.fn();
    handler(null, createResWithPermissions([ permissions.USERS_VIEW ]), nextMock);
    expect(nextMock.mock.calls[0][0]).toBe(undefined);
  });
});
