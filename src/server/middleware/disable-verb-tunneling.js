/**
 * Middleware to prevent HTTP method overrides. Required to prevent "Often Misused: HTTP Method Override" webinspect error.
 * @param {*} logger
 * @returns
 */
const disableVerbTunneling = (logger) => (req, res, next) => {
  const methodParam = req.params._method;
  const xhmHeader = req.get('x-http-method');
  const xhmoHeader = req.get('x-http-method-override');
  const xmoHeader = req.get('x-method-override');

  if (methodParam !== undefined || xhmHeader !== undefined || xhmoHeader !== undefined || xmoHeader !== undefined) {
    logger.error({ message: 'Bad actor is attempting to use HTTP Method Tunneling.' });
    return res.status(405).json({ error: 'Not allowed' });
  }

  next();
};

module.exports = disableVerbTunneling;
