const { omit } = require('lodash');
const { isIP, inRange } = require('range_check');

/**
 * Middleware to add LD rate limiting config & true client IP to the req object
 * True client IP set to first IP in x-forwarded-for header
 * If the resultant IP is invalid or the IP of an CDP load balancer/instance, client rate limiting will be disabled
 * @param {*} logger
 * @returns
 */
const rateLimitMiddleware = (launchDarklyService, logger, rateLimitConfigs) => async(req, res, next) => {
  try {
    req.ldRateLimitConfig = await launchDarklyService.getValue('admin.config.rateLimit', omit(rateLimitConfigs, [ 'cdpTrustedIp' ]));
  } catch (err) {
    req.ldRateLimitConfig = omit(rateLimitConfigs, [ 'cdpTrustedIp' ]);
    logger.error({ message: 'Unable to call Launch Darkly for rate limiting middleware', err });
  }

  let trueClientIp = '';
  const xForwardedFor = req.header('x-forwarded-for');
  if (xForwardedFor) {
    try {
      const firstIP = xForwardedFor.split(',').map(ip => ip.trim())[0];
      /* istanbul ignore else */
      if (firstIP && isIP(firstIP) && !inRange(firstIP, rateLimitConfigs.cdpTrustedIp)) {
        trueClientIp = firstIP;
      }
    } catch (err) {
      logger.error({ err, message: `Error while parsing client ip from ${xForwardedFor}` });
    }
  }
  if (!trueClientIp) {
    logger.warn({ message: `Unable to determine client ip from ${xForwardedFor}` });
  }
  req.trueClientIp = trueClientIp;

  next();
};

module.exports = rateLimitMiddleware;
