const whitelistPath = [
  '/health',
  '/api/v1/authenticate/logout',
  '/.well-known/jwks.json',
];

const authenticateMiddleware = (logger, userService, permissionService) => {
  return async(req, res, next) => {
    if (!whitelistPath.includes(req.path)) {
      const identifier = req.session.wam.sub;
      if (!(identifier && identifier.length > 0)) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      try {
        res.locals.user = await userService.getUserBySid(identifier);

        if (!res.locals.user) {
          return res.status(401).json({ error: 'Unauthorized' });
        }

        delete res.locals.user.token;
        delete res.locals.user.token_expires;
        delete res.locals.user.token_expire_date;

        const promises = [
          permissionService.getPermissionsForUser(res.locals.user.id),
          permissionService.getAccessForUser(res.locals.user.id),
        ];
        const [ permissions, access ] = await Promise.all(promises);
        res.locals.user.permissions = permissions;
        res.locals.user.access = access;
      } catch (err) {
        logger.error({ message: err.messsage, err: { code: err.code, stack: err.stack } });
        next(err);
      }
    }
    next();
  };
};

module.exports = authenticateMiddleware;
