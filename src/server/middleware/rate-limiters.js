const rateLimit = require('express-rate-limit');

/**
 * Rate limiter middlewares:
    * Limit the number of requests allowed per instance ip & the number of requests allowed by the client ip
 * Rate limit params:
    * windowMs - Time frame for which requests are checked/remembered. Hard coded in config.
    * max - The maximum number of connections to allow during the window before rate limiting the client. Pulled from LD on every request, falls back to value in manifest if LD cannot be reached
 * @param {*} rateLimitConfigs - rate limit configs defined in manifest files
 * @param {*} logger
 * @returns rate limiter middleware
 */

const totalRateLimiter = (rateLimitConfigs, logger) => rateLimit({
  windowMs: rateLimitConfigs.window,
  max: (req, res) => req.ldRateLimitConfig.overall,
  standardHeaders: true,
  legacyHeaders: false,
  requestPropertyName: 'totalRateLimit',
  handler: (req, res, next, options) => {
    logger.warn({ message: `Total rate limit hit for instance ${req.ip}` });
    return res.status(options.statusCode).send(options.message);
  },
});

const clientRateLimiter = (rateLimitConfigs, logger) => rateLimit({
  windowMs: rateLimitConfigs.window,
  max: (req, res) => {
    if (req.trueClientIp) {
      return req.ldRateLimitConfig.client;
    }
    return 0; // if unable to determine client ip, client rate limiting is disabled
  },
  standardHeaders: true,
  legacyHeaders: false,
  requestPropertyName: 'clientRateLimit',
  keyGenerator: (req, res) => req.trueClientIp,
  handler: (req, res, next, options) => {
    logger.warn({ message: `Client rate limit hit for ip ${req.trueClientIp}` });
    return res.status(options.statusCode).send(options.message);
  },
});

module.exports = {
  totalRateLimiter,
  clientRateLimiter,
};
