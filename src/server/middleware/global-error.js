const { BadRequestError } = require('../../error');
const globalErrorMiddleware = logger => (err, req, res, next) => {
  if (err.payload) {
    if (err.payload.request) {
      delete err.payload.request;
    }
    logger.error({ message: err.message, payload: err.payload.toString(), stack: err.stack });
  }

  // bad request
  // mostly thrown by body-parser's out-of-box error handler
  // http://expressjs.com/en/resources/middleware/body-parser.html#errors
  if (parseInt(Number(err.statusCode) / 100) === 4) {
    const badRequestError = new BadRequestError(err.message);
    const loggableErr = { ...err, url: req.originalUrl };
    delete loggableErr.body; // too verbose, can open up logging in the future as needed
    logger.warn({
      message: 'Rejected bad request',
      error: { ...badRequestError, metadata: [ loggableErr ] },
    });
    return res
      .status(err.statusCode)
      .json({ data: {}, notifications: [ badRequestError ] });
  }

  return res.status(isNaN(err.code) ? 500 : err.code).json({
    code: err.code,
    message: err.errorMessage,
    uuid: err.uuid,
    timestamp: err.timestamp,
    metadata: err.metadata,
  });
};

module.exports = globalErrorMiddleware;
