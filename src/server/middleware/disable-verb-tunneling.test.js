const disableVerbTunneling = require('./disable-verb-tunneling');

describe('Verb Tunneling Attempt Handler', () => {
  const next = jest.fn();
  const json = jest.fn();
  const status = jest.fn().mockReturnValue({ json });
  const res = { status, json };
  const req = { params: {}, get: jest.fn() };
  const logger = {
    error: jest.fn(),
  };

  beforeEach(() => {
    status.mockClear();
    json.mockClear();
    next.mockClear();
    req.get.mockClear();
    logger.error.mockClear();
  });

  test('should call next if verb tunnelling is not attempted', () => {
    disableVerbTunneling(logger)(req, res, next);

    expect(next).toHaveBeenCalled();
  });

  test('should return 405 if _method present', () => {
    req.params._method = 'PATCH';
    disableVerbTunneling(logger)(req, res, next);

    expect(status).toHaveBeenCalledWith(405);
    expect(json).toHaveBeenCalledWith({ error: 'Not allowed' });
    expect(logger.error).toHaveBeenCalledWith({ message: 'Bad actor is attempting to use HTTP Method Tunneling.' });
  });

  test('should return 405 if override header x-http-method present', () => {
    req.get.mockImplementationOnce(val => val === 'x-http-method');
    disableVerbTunneling(logger)(req, res, next);

    expect(status).toHaveBeenCalledWith(405);
    expect(json).toHaveBeenCalledWith({ error: 'Not allowed' });
    expect(logger.error).toHaveBeenCalledWith({ message: 'Bad actor is attempting to use HTTP Method Tunneling.' });
  });

  test('should return 405 if override header x-http-method-override present', () => {
    req.get.mockImplementationOnce(val => val === 'x-http-method-override');
    disableVerbTunneling(logger)(req, res, next);

    expect(status).toHaveBeenCalledWith(405);
    expect(json).toHaveBeenCalledWith({ error: 'Not allowed' });
    expect(logger.error).toHaveBeenCalledWith({ message: 'Bad actor is attempting to use HTTP Method Tunneling.' });
  });

  test('should return 405 if override header x-method-override present', () => {
    req.get.mockImplementationOnce(val => val === 'x-method-override');
    disableVerbTunneling(logger)(req, res, next);

    expect(status).toHaveBeenCalledWith(405);
    expect(json).toHaveBeenCalledWith({ error: 'Not allowed' });
    expect(logger.error).toHaveBeenCalledWith({ message: 'Bad actor is attempting to use HTTP Method Tunneling.' });
  });
});
