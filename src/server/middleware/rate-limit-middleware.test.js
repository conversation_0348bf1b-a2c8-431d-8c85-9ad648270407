const rateLimitMiddleware = require('./rate-limit-middleware');

describe('Rate Limit Middleware', () => {
  const next = jest.fn();
  const json = jest.fn();
  const status = jest.fn().mockReturnValue({ json });
  const res = { status, json };
  const logger = {
    error: jest.fn(),
    warn: jest.fn(),
  };
  const launchDarklyService = {
    getValue: () => {
      return {
        client: 5,
        overall: 50,
      };
    },
  };
  const rateLimitConfigs = {
    client: 10,
    overall: 100,
    cdpTrustedIp: '***********/23',
  };

  beforeEach(() => {
    status.mockClear();
    json.mockClear();
    next.mockClear();
  });

  test('should set the client ip as the first x-forwarded-for header', async() => {
    const req = {
      header: () => '*************, *************',
    };
    await rateLimitMiddleware(launchDarklyService, logger, rateLimitConfigs)(req, res, next);

    expect(req.trueClientIp).toBe('*************');
    expect(req.ldRateLimitConfig).toEqual({
      client: 5,
      overall: 50,
    });
    expect(next).toHaveBeenCalled();
  });

  test('should not set the client ip if request does not have the x-forwarded-for header', async() => {
    const req = {
      header: () => {},
    };
    await rateLimitMiddleware(launchDarklyService, logger, rateLimitConfigs)(req, res, next);

    expect(req.trueClientIp).toBe('');
    expect(req.ldRateLimitConfig).toEqual({
      client: 5,
      overall: 50,
    });
    expect(next).toHaveBeenCalled();
  });
});
