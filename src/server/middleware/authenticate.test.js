const validateRequest = require('./authenticate');

const mockPermissionService = {
  getPermissionsForUser: jest.fn().mockResolvedValue([ 'admin' ]),
  getAccessForUser: jest.fn().mockResolvedValue({ campaigns: {}, alerts: {} }),
};

const mockLogger = {
  error: jest.fn(),
  info: jest.fn(),
};

// TODO: RE-WRITE
describe('authenticate middleware tests', () => {
  test('request made to a whitelisted path', () => {
    const mockReq = {
      path: '/health',
    };
    const mockNext = jest.fn();
    const mwInstance = validateRequest({});
    mwInstance(mockReq, {}, mockNext);
    expect(mockNext).toBeCalled();
  });

  test('request made without sid in session', () => {
    const mockReq = {
      session: {
        wam: {
          sub: null,
        },
      },
    };
    const mockNext = jest.fn();
    const jsonFn = jest.fn();
    const mockRes = {
      locals: {
        user: {
          id: 1,
        },
      },
      status: () => {
        return {
          json: jsonFn,
        };
      },
    };
    const mwInstance = validateRequest({});
    mwInstance(mockReq, mockRes, mockNext);
    expect(jsonFn).toBeCalledWith({ error: 'Unauthorized' });
  });

  test('should return Unauthorized when user does not exist', async() => {
    const mockReq = {
      session: {
        wam: {
          sub: 's123456',
        },
      },
    };
    const nextMock = jest.fn();
    const mockUserService = {
      getUserBySid: jest.fn().mockResolvedValue(false),
    };
    const jsonFn = jest.fn();
    const mockRes = {
      locals: {
        user: undefined,
      },
      status: () => {
        return {
          json: jsonFn,
        };
      },
    };
    const mwInstance = validateRequest(mockLogger, mockUserService, mockPermissionService);
    await mwInstance(mockReq, mockRes, nextMock);
    expect(mockUserService.getUserBySid).toHaveBeenCalled();
    expect(jsonFn).toBeCalledWith({ error: 'Unauthorized' });
  });

  test('request made without sid in session and valid user', async() => {
    const mockReq = {
      session: {
        wam: {
          sub: 's123456',
        },
      },
    };
    const nextMock = jest.fn();
    const mockUserService = {
      getUserBySid: jest.fn().mockResolvedValue({ id: 1, sid: 's123456' }),
    };
    const jsonFn = jest.fn();
    const mockRes = {
      locals: {
        user: {
          id: 1,
        },
      },
      status: () => {
        return {
          json: jsonFn,
        };
      },
    };
    const mwInstance = validateRequest(mockLogger, mockUserService, mockPermissionService);
    await mwInstance(mockReq, mockRes, nextMock);
    expect(mockUserService.getUserBySid).toHaveBeenCalled();
    expect(mockPermissionService.getAccessForUser).toHaveBeenCalled();
    expect(mockPermissionService.getPermissionsForUser).toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
  });
});
