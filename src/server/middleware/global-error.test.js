const globalError = require('./global-error');

const loggerMock = {
  error: jest.fn(),
  warn: jest.fn(),
};

const jsonMock = jest.fn();

const mockRes = {
  status: () => ({ json: jsonMock }),
};

// TODO: RE-WRITE
describe('Error logger', () => {
  test('error logger type case', () => {
    expect(typeof globalError).toBe('function');
  });

  const globalErrorInstance = globalError(loggerMock);

  test('create instance of global error', () => {
    expect(typeof globalErrorInstance).toBe('function');
  });

  test('execute instance of logger', () => {
    globalErrorInstance({ payload: 'error' }, {}, mockRes, jest.fn());
    expect(loggerMock.error).toBeCalled();
  });

  test('execute logger with headerSent response equals true', () => {
    const nextMock = jest.fn();
    const errMock = {
      payload: 'errorValue',
    };
    globalErrorInstance(errMock, {}, {
      ...mockRes,
      headerSent: true,
    }, nextMock);
    expect(loggerMock.error).toBeCalled();
  });

  test('execute logger with error code number', () => {
    const nextMock = jest.fn();
    const errMock = {
      code: 1,
      message: 'error string',
      uuid: 1,
      timestamp: '2009-10-20 00:00:00.000',
      stack: 'error stack',
    };
    globalErrorInstance(errMock, {}, mockRes, nextMock);
  });

  test('execute logger with error code string', () => {
    const nextMock = jest.fn();
    const errMock = {
      code: 'error100',
      name: 'knex error',
      message: 'error string',
      uuid: 1,
      timestamp: '2009-10-20 00:00:00.000',
      stack: 'error stack',
    };
    globalErrorInstance(errMock, {}, mockRes, nextMock);
    expect(jsonMock).toBeCalled();
  });

  test('execute logger without error code', () => {
    const nextMock = jest.fn();
    const errMock = {
      message: 'error string',
      uuid: 1,
      timestamp: '2009-10-20 00:00:00.000',
      stack: 'error stack',
      response: {
        data: 'error description',
      },
    };
    globalErrorInstance(errMock, {}, mockRes, nextMock);
    expect(jsonMock).toBeCalled();
  });

  test('should handle 4xx bad request error', () => {
    const nextMock = jest.fn();
    const errMock = {
      statusCode: 400,
      message: 'Bad request error',
      body: 'request body',
    };
    const reqMock = {
      originalUrl: '/test/url',
    };

    globalErrorInstance(errMock, reqMock, mockRes, nextMock);

    expect(loggerMock.warn).toHaveBeenCalled();
    expect(jsonMock).toHaveBeenCalledWith(expect.objectContaining({
      data: {},
      notifications: expect.arrayContaining([
        expect.objectContaining({
          message: 'Bad request error',
        }),
      ]),
    }));
  });

  test('should handle 401 unauthorized error', () => {
    const nextMock = jest.fn();
    const errMock = {
      statusCode: 401,
      message: 'Unauthorized',
    };
    const reqMock = {
      originalUrl: '/api/secure',
    };

    globalErrorInstance(errMock, reqMock, mockRes, nextMock);

    expect(loggerMock.warn).toHaveBeenCalled();
  });

  test('should handle 422 unprocessable entity error', () => {
    const nextMock = jest.fn();
    const errMock = {
      statusCode: 422,
      message: 'Unprocessable entity',
    };
    const reqMock = {
      originalUrl: '/api/resource',
    };

    globalErrorInstance(errMock, reqMock, mockRes, nextMock);

    expect(loggerMock.warn).toHaveBeenCalled();
  });

  test('should delete payload.request if it exists', () => {
    const nextMock = jest.fn();
    const errMock = {
      code: 500,
      message: 'error with request',
      payload: {
        request: 'sensitive request data',
        otherData: 'should remain',
      },
    };

    globalErrorInstance(errMock, {}, mockRes, nextMock);

    expect(errMock.payload.request).toBeUndefined();
    expect(errMock.payload.otherData).toBe('should remain');
    expect(loggerMock.error).toHaveBeenCalled();
  });

  test('should handle error without payload', () => {
    const nextMock = jest.fn();
    const errMock = {
      code: 500,
      message: 'error without payload',
    };

    globalErrorInstance(errMock, {}, mockRes, nextMock);

    expect(jsonMock).toHaveBeenCalled();
  });
});
