const createLaunchDarkly = require('./index');

const LaunchDarkly = {
  init: jest.fn().mockReturnValue({
    waitForInitialization: jest.fn().mockResolvedValue(true),
    variation: jest.fn().mockResolvedValue(true),
    close: jest.fn(),
  }),
};

const mockSDK = 'sdk-mock-secret';
const mockUser = 'mock-user';
const mockConfig = {
  proxyHost: 'webproxy.xyz',
  proxyPort: '8080',
  logger: {},
};

describe('Launch Darkly', () => {
  let launchDarklyService;
  test('returns launch darkly service with init, getValue, and close functions', async() => {
    launchDarklyService = createLaunchDarkly(LaunchDarkly, mockSDK, mockUser, mockConfig);
    expect(launchDarklyService).toHaveProperty('init');
    expect(launchDarklyService).toHaveProperty('getValue');
    expect(launchDarklyService).toHaveProperty('close');
  });

  test('calls Launch Darkly SDK init function when LD service init function is called', async() => {
    (async() => {
      launchDarklyService = createLaunchDarkly(LaunchDarkly, mockSDK, mockUser, mockConfig);
      await launchDarklyService.init();
    })();
    expect(LaunchDarkly.init).toHaveBeenCalled();
  });

  test('calls Launch Darkly SDK variation function when getValue function is called', () => {
    (async() => {
      launchDarklyService = createLaunchDarkly(LaunchDarkly, mockSDK, mockUser, mockConfig);
      await launchDarklyService.init();
    })();
    launchDarklyService.getValue('test', false);
    expect(LaunchDarkly.init().variation).toHaveBeenCalled();
  });

  test('calls Launch Darkly SDK variation function when getValue function is called when no default value is passed', () => {
    (async() => {
      launchDarklyService = createLaunchDarkly(LaunchDarkly, mockSDK, mockUser, mockConfig);
      await launchDarklyService.init();
    })();
    launchDarklyService.getValue('test');
    expect(LaunchDarkly.init().variation).toHaveBeenCalled();
  });

  test('calls Launch Darkly SDK close function when LD service close function is called', () => {
    (async() => {
      launchDarklyService = createLaunchDarkly(LaunchDarkly, mockSDK, mockUser, mockConfig);
      await launchDarklyService.init();
    })();
    launchDarklyService.close();
    expect(LaunchDarkly.init().close).toHaveBeenCalled();
  });

  test('throws an error when secret is missing', () => {
    (async() => {
      try {
        launchDarklyService = createLaunchDarkly(LaunchDarkly, undefined, mockUser, mockConfig);
        await launchDarklyService.init();
      } catch (err) {
        expect(err.message).toBe('Missing Launch Darkly CDP secret SDK key');
      }
    })();
  });
});
