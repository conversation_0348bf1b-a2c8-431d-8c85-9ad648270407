const server = require('./index');
const config = require('constants/config');

jest.mock('const-express-hermes/dist/standalone', () => ({
  default: jest.fn(),
}));

const loggerMock = {
  error: jest.fn(),
  info: jest.fn(),
};
const userServiceMock = {};
const routes = [ [ '/api/v1', jest.fn() ] ];
const authenticateMiddlewareMock = jest.fn();
const launchDarklyMock = {
  client: {},
  overall: {},
};

const redisClientMock = {};

describe('server setting up', () => {
  test('create a middleware object', () => {
    expect(typeof server).toBe('function');
    const serverInstance = server(
      loggerMock,
      config,
      userServiceMock,
      routes,
      authenticateMiddlewareMock,
      launchDarklyMock,
      redisClientMock,
    );
    expect(typeof serverInstance).toBe('function');
  });
});
