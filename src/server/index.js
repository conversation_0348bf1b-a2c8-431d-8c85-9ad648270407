const express = require('express');
const helmet = require('helmet');
const { omit } = require('lodash');
const globalErrorMiddleware = require('./middleware/global-error');
const disableVerbTunneling = require('./middleware/disable-verb-tunneling');
const rateLimitMiddleware = require('./middleware/rate-limit-middleware');
const { totalRateLimiter, clientRateLimiter } = require('./middleware/rate-limiters');
const compress = require('compression');
const cookieParser = require('cookie-parser');
const { loggerMiddleware, reqStartTimeMiddleware } = require('nrlw-express-scribe');
const hermes = require('const-express-hermes/dist/standalone').default;
const session = require('express-session');
const csurf = require('csurf');
const RedisStore = require('connect-redis')(session);

module.exports = (logger, config, userService, routes, authenticateMiddleware, launchDarklyService, redisClient) => {
  const app = express();
  app.use(helmet({ contentSecurityPolicy: config.contentSecurityPolicy }));
  app.use((req, res, next) => {
    res.setHeader('x-xss-protection', 1);
    res.set({
      'Cache-Control': [ 'no-store', 'no-cache', 'no-transform', 'must-revalidate', 'max-age=604800' ],
    });
    next();
  });

  app.use(reqStartTimeMiddleware());
  app.use(loggerMiddleware({
    logger,
    level: (_, res) => (res.statusCode >= 400 && res.statusCode !== 422 ? 'warn' : 'info'),
    maskedReqHeaders: [],
    maskedResHeaders: [],
    ignoreLogging: (req, _) => config.logging.ignoredLogRoutes.includes(req.originalUrl),
    obfuscatorConfig: [
      { path: '/api/v1/users/*', response: data => data ? '<hidden>' : data },
      { path: '/api/v1/campaign-users*', response: data => data ? '<hidden>' : data },
    ],
  }));
  app.use(cookieParser(config.wamSsoAuth.clientSecret));

  // Initialize session management
  app.use(
    session({
      name: 'sessionID',
      store: new RedisStore({ client: redisClient }),
      proxy: true,
      cookie: {
        secure: config.nodeEnv === 'production',
        maxAge: config.wamSsoAuth.localSessionTTL,
        domain: config.wamSsoAuth.validReturnDomain,
      },
      secret: config.wamSsoAuth.clientSecret,
      resave: false,
      saveUninitialized: false,
      unset: 'destroy',
    }),
  );

  // Initialize WAM authentication
  const cookieConfigs = { httpOnly: true, secure: true, domain: config.wamSsoAuth.validReturnDomain };
  const setCsrfToken = (req, res, next) => {
    res.cookie('XSRF-TOKEN', req.csrfToken(), {
      secure: process.env.NODE_ENV !== 'development',
    });
    next();
  };
  const wamSSOMiddlewares = [
    csurf({ cookie: cookieConfigs }),
    setCsrfToken,
    // Wrap the Hermes middleware to intercept its response
    (req, res, next) => {
      const originalSend = res.send;
      const originalEnd = res.end;

      // Intercept all response methods
      const handleResponse = () => {
        if (res.statusCode === 302 && req.get('sec-fetch-mode') === 'cors') {
          const response = {
            needsRefresh: true,
            message: 'Session expired, please refresh the page',
          };
          res.statusCode = 401;
          return res.json(response);
        }
      };

      // Override response methods
      res.send = function(...params) {
        handleResponse();
        res.send = originalSend;
        return res.send(...params);
      };

      res.end = function(...params) {
        handleResponse();
        res.end = originalEnd;
        return res.end(...params);
      };

      // Call the actual Hermes middleware
      return hermes({
        clientID: config.wamSsoAuth.clientID,
        clientSecret: config.wamSsoAuth.clientSecret,
        redirectURI: config.wamSsoAuth.redirectURI,
        authorizeURI: config.wamSsoAuth.authorizeURI,
        tokenURI: config.wamSsoAuth.tokenURI,
        tokenInfoURI: config.wamSsoAuth.tokenInfoURI,
        jwksURI: config.wamSsoAuth.jwksURI,
        privateKey: config.wamSsoAuth.clientSecret,
        csurfCookie: cookieConfigs,
        logger,
      })(req, res, next);
    },
  ];
  const applyWamSsoMiddleware = (middleware) => async(req, res, next) => {
    if (config.wamSsoAuth.unsecureRoutes.some((route) => route === req.path)) {
      return next();
    }
    return middleware(req, res, next);
  };
  wamSSOMiddlewares.forEach((middleware) => app.use(applyWamSsoMiddleware(middleware)));

  app.use(compress());
  app.use(express.json({ limit: 200000 })); // 200kb - default request body size 100kb
  app.use(express.urlencoded({ limit: 200000, extended: true }));

  app.use('/api',
    rateLimitMiddleware(launchDarklyService, logger, omit(config.rateLimiting, [ 'window' ])),
    totalRateLimiter(config.rateLimiting, logger),
    clientRateLimiter(config.rateLimiting, logger),
  );

  app.get('/', async(req, res, next) => {
    const { query } = req;
    if (!query.error) {
      try {
        await userService.validateUser(req.session.wam.sub);
        return next();
      } catch (err) {
        logger.error({ message: err.message, err });
        if ([ 'USER_DEACTIVATED', 'USER_NONEXIST' ].includes(err.message)) {
          return res.redirect(302, `/?error=${err.message}&wam_logout=${config.wamSsoAuth.logoutURL}`);
        } else {
          return res.redirect(302, '/?error=AUTH_ERROR');
        }
      }
    } else {
      return next();
    }
  });

  if (process.env.NODE_ENV === 'development') {
    const { createProxyMiddleware } = require('http-proxy-middleware');
    app.use(/^\/(?!(api|health|.well-known)).*/, createProxyMiddleware({ target: process.env.FRONTEND_URL }));
  } else {
    app.use('/assets', express.static(config.static.path));
    app.use(/^\/preview/, (req, res) => res.sendFile(config.static.preview));
    app.use(/^\/(?!(api|health|assets|preview|.well-known)).*/, (req, res) => res.sendFile(config.static.index));
  }

  app.use(authenticateMiddleware);
  app.use(disableVerbTunneling(logger));

  // set routes
  routes.forEach(route => {
    app.use(route[0], route[1]);
  });

  // adding global error as last middleware intentionally
  // will be called for every request
  app.use(globalErrorMiddleware(logger));

  return app;
};
