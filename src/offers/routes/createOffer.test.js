const mockOffer = require('../../__mocks__/mockOffer');
const CreateOffer = require('./createOffer');

const mockOffersService = {
  createOffer: jest.fn(),
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();
mockStatus.mockReturnValue({ json: mockJson });
const mockRes = {
  status: mockStatus,
  locals: {
    auth: { sid: 's1234567' },
    validatedBody: {},
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
      sid: 's1234567',
    },
  },
};

describe('Offer: routes > createOffer', () => {
  beforeEach(() => {
    mockOffersService.createOffer.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });

  test('Should return valid response - creating offer', async() => {
    const mockBody = {
      ...mockOffer,
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      status: 200,
      data: mockBody,
    };

    mockOffersService.createOffer.mockReturnValueOnce(fakeResponse);

    const createOffer = CreateOffer(mockLogger, mockOffersService);
    await createOffer(mockReq, res, mockNext);
    expect(mockOffersService.createOffer).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('should handle error on less than 400 response status', async() => {
    const mockReq = {
      body: mockOffer,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockOffer,
      },
    };

    const fakeResponse = {
      response: {
        status: 401,
        data: {},
      },
    };

    mockOffersService.createOffer.mockRejectedValue(fakeResponse);

    const createOffer = CreateOffer(mockLogger, mockOffersService);
    await createOffer(mockReq, res, mockNext);
    expect(mockOffersService.createOffer).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(0);
    expect(res.status).toHaveBeenCalledWith(401);
  });

  test('should call next with Error on 500+ response status', async() => {
    const mockBody = {
      ...mockOffer,
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      response: {
        status: 500,
        data: {
          code: 'HTTP_INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
        },
      },
    };

    mockOffersService.createOffer.mockRejectedValue(fakeResponse);

    const createOffer = CreateOffer(mockLogger, mockOffersService);
    await createOffer(mockReq, res, mockNext);
    expect(mockOffersService.createOffer).toBeCalled();
    expect(mockNext).toBeCalled();
  });

  test('should handle unknown error', async() => {
    const mockReq = {
      body: mockOffer,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockOffer,
      },
    };

    mockOffersService.createOffer.mockRejectedValue(new Error('unknown'));

    const createOffer = CreateOffer(mockLogger, mockOffersService);
    await createOffer(mockReq, res, mockNext);
    expect(mockOffersService.createOffer).toBeCalled();
    expect(mockNext).toBeCalled();
  });
});
