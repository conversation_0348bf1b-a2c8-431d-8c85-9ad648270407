const { CustomError } = require('../../error');

const updateOffer = (logger, offersService) => {
  return async(req, res, next) => {
    try {
      const params = res.locals.validatedParams;
      const value = res.locals.validatedBody;
      const sid = res.locals.user && res.locals.user.sid;

      const updatedOffer = await offersService.updateOffer(params.id, { value, sid });
      res.status(200).json({ updatedOffer });
    } catch (error) {
      if (error.response) {
        if (error.response.status < 500) {
          res.status(error.response.status).json(error.response.data);
        } else {
          next(new CustomError(error.response.status, JSON.stringify(error.response.data)));
        }
      } else {
        next(new CustomError(500, error.toString()));
      }
    }
  };
};

module.exports = updateOffer;
