const Joi = require('joi');
const { CustomError, ERROR_HANDLER } = require('../../error');
const { ACTION, OFFERS_STATUS } = require('./constants');

const DEFAULT_OPTS = { stripUnknown: true };

const VALIDATION_TYPE = {
  BODY: 'body',
  PARAMS: 'params',
  QUERY: 'query',
};

const id = Joi.string().regex(/^(OFF|BEN|PER)-\d{1,10}$/i);

const scotiaUser = Joi.string().min(4).max(10).regex(/^s[0-9]+$/);

const schemaValidationMiddleware = (schema, type = VALIDATION_TYPE.BODY, opts = DEFAULT_OPTS) => (req, res, next) => {
  if (!Object.values(VALIDATION_TYPE).includes(type)) {
    return next(new CustomError(500, 'Invalid joi validation type, must be: body, params, or query'));
  }
  const { error, value } = schema.validate(req[type], opts);
  if (error) {
    return next(ERROR_HANDLER(`Joi validation error on req.${type}`, error, next));
  }

  switch (type) {
    case VALIDATION_TYPE.BODY:
      res.locals.validatedBody = value;
      break;
    case VALIDATION_TYPE.PARAMS:
      res.locals.validatedParams = value;
      break;
    case VALIDATION_TYPE.QUERY:
      res.locals.validatedQuery = value;
      break;
    /* istanbul ignore next */
    default:
      break;
  }
  next();
};

// get offers by its id
const optionalGetPathSchema = Joi.object()
  .keys({ id });

const productItem = Joi.object()
  .keys({
    ownership_type: Joi.string().valid('B', 'R').required(),
    product_code: Joi.string().min(1).max(3).regex(/^[A-Z0-9]+$/),
    product_sub_code: Joi.string().min(1).max(3).regex(/^[A-Z0-9]+$/),
  });

const targetingByProduct = Joi.object()
  .keys({
    any_of: Joi.array().items(productItem),
    none_of: Joi.array().items(productItem),
    all_of: Joi.array().items(productItem),
  });

const offersSchema = {
  offer_id: Joi.string().regex(/^(OFF|BEN|PER)-\d{1,10}$/i),
  title: Joi.string().max(94).optional(),
  category: Joi.string().valid(...[ 'BENEFITS', 'OFFERS', 'PERKS' ]).optional(),
  priority: Joi.number().max(50).optional(),
  start_date: Joi.date().iso().raw(true).optional(),
  expiry_date: Joi.date().optional().iso().raw(true),
  target_language: Joi.array().items(Joi.string().valid(...[ 'ENGLISH', 'FRENCH' ])).optional(),
  products: targetingByProduct.optional(),
  product_relationship: Joi.string().valid(...[ 'PRIMARY', 'EVERYONE' ]),
  contentful_id: Joi.string().required(),
  approvers: Joi.array().optional(),
  reviewers: Joi.array().optional(),
  contentful_space_id: Joi.string().required(),
  contentful_type: Joi.string().required(),
  offer_status: Joi.string().valid(...Object.values(OFFERS_STATUS)),
  location: Joi.object({
    country: Joi.string().valid('CA', 'US').required(),
    provinces_states: Joi.array().items(
      Joi.string().pattern(/^[A-Z]{2,3}$/),
    ).required(),
  }),
};

const createSchema = Joi.object().keys(Object.assign({
  ...offersSchema,
})).fork([ 'title', 'category', 'priority', 'start_date', 'product_relationship' ], (schema) => schema.required());

const updateSchema = Joi.object()
  .keys({ ...offersSchema })
  .fork([ 'title', 'category', 'priority', 'start_date', 'product_relationship', 'location' ], (schema) => schema.required());

const updateOfferLocationSchema =  Joi.object({
    location: Joi.object({
      country: Joi.string(),
      provinces_states: Joi.array()
        .items(Joi.string()),

    }).required(),
    approvers: Joi.array()
    .items(Joi.string().alphanum().min(1)),
    key:Joi.string().optional()
  }).required()
 


const updateOfferStatusSchema = Joi.object()
  .keys({ action: Joi.string().valid(...[ 'PAUSE', 'RESUME' ]) })
  .fork([ 'action' ], (schema) => schema.required());

const approveOfferSchema = Joi.object({
  action: Joi.string()
    .valid(ACTION.APPROVE, ACTION.REJECT, ACTION.PUBLISH)
    .required(),
  approvers: Joi.when('action', {
    is: ACTION.APPROVE,
    then: Joi.array().items(scotiaUser).min(1).required(),
    otherwise: Joi.array().optional(),
  }),
});

const getPathSchema = Joi.object()
  .keys({ id })
  .fork([ 'id' ], (schema) => schema.required());

const getOffersListSchema = Joi.object()
  .keys({
    offset: Joi.number().min(0).default(0),
    limit: Joi.number().min(1).default(50),
    category: Joi.string().valid(...[ 'BENEFITS', 'OFFERS', 'PERKS' ]),
    status: Joi.string().valid(...Object.values(OFFERS_STATUS)),
    offer_id: id,
    priority: Joi.number().max(50),
    filter: Joi.string().valid(...[ 'PUBLISHED', 'PENDING' ]),
    sort: Joi.string().valid(),
    search: Joi.string().valid(),
    start_date: Joi.date().iso().raw(true),
    end_date: Joi.date().iso().raw(true),
  });

const getOfferAssigneesSchema = Joi.object()
  .keys({
    offer_status: Joi.string()
      .valid(...[ ...Object.values(OFFERS_STATUS) ]).optional(),
  });

module.exports = {
  DEFAULT_OPTS,
  VALIDATION_TYPE,
  optionalGetPathSchema,
  createSchema,
  updateSchema,
  updateOfferLocationSchema,
  getPathSchema,
  getOffersListSchema,
  updateOfferStatusSchema,
  schemaValidationMiddleware,
  approveOfferSchema,
  getOfferAssigneesSchema,
};
