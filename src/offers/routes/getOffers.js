const { filter, whereEq, sortBy, path, prop, reverse, includes, toLower } = require('ramda');
const { CustomError } = require('../../error');

const getOffers = (offersService) => {
  return async(req, res, next) => {
    try {
      const query = res.locals.validatedQuery;
      const sid = res.locals.user && res.locals.user.sid;
      let allOffers = await offersService.getAllOffers(query, sid);
      let filteredOffers = allOffers.data;

      if (query.search) {
        filteredOffers = filter(offer => includes(toLower(query.search), toLower(offer.offer_id)) || includes(toLower(query.search), toLower(offer.title)), filteredOffers);
      }
      const filters = {
        ...(query.category ? { category: query.category } : {}),
        ...(query.priority ? { priority: query.priority } : {}),
        ...(query.offer_id ? { offer_id: query.offer_id } : {}),
        ...(query.status ? { offer_status: query.status } : {}),
      };

      filteredOffers = filter(whereEq(filters), filteredOffers);

      // apply date filters
      filteredOffers = filter(offer => {
        let include = true;

        if (query.start_date && query.end_date) {
          include = include && new Date(offer.start_date) >= new Date(query.start_date) && new Date(offer.expiry_date) <= new Date(query.end_date);
        } else if (query.start_date) {
          include = include && new Date(offer.start_date) >= new Date(query.start_date);
        } else if (query.end_date) {
          include = include && new Date(offer.expiry_date) <= new Date(query.end_date);
        }
        return include;
      }, filteredOffers);

      const defaultSortBy = 'last_updated_timestamp';
      let actualSortBy = defaultSortBy;
      let sortByOrder = -1;
      if (query.sort) {
        if (query.sort.startsWith('-')) {
          actualSortBy = query.sort.split('-')[1];
          sortByOrder = -1; // Descending
        } else {
          actualSortBy = query.sort;
          sortByOrder = 1; // Ascending
        }
      }

      const sorter = sortBy(offer => {
        if (actualSortBy === 'last_updated_timestamp') {
          const lastUpdatedTimestamp = path([ 'change_history', 'last_updated_timestamp' ], offer);
          return lastUpdatedTimestamp || path([ 'change_history', 'created_timestamp' ], offer);
        } else {
          return prop(actualSortBy === 'status' ? 'offer_status' : actualSortBy, offer);
        }
      });

      filteredOffers = sortByOrder === 1 ? sorter(filteredOffers) : reverse(sorter(filteredOffers));

      const offset = { ...query }.offset || 0;
      const limit = { ...query }.limit || 10;

      allOffers.limit = limit;
      allOffers.offset = offset;
      allOffers.total = filteredOffers.length;
      allOffers.data = filteredOffers.map(offer => {
        return offer;
      }).slice(offset, offset + limit);
      res.status(200).json(allOffers);
    } catch (err) {
      if (err.response) {
        if (err.response.status < 500) {
          res.status(err.response.status).json(err.response.data);
        } else {
          next(new CustomError(err.response.status, JSON.stringify(err.response.data)));
        }
      } else {
        next(new CustomError(500, err.toString()));
      }
    }
  };
};

module.exports = getOffers;
