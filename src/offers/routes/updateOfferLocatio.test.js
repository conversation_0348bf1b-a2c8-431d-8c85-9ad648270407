const mockOffer = require('../../__mocks__/mockOffer');
const { CustomError } = require('../../error');
const updateOfferLocation = require('./updateOfferLocation');

const mockOffersService = {
  updateOfferLocation: jest.fn(),
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();
const mockRes = {
  status: mockStatus,
  locals: {
    auth: { sid: 's1234567' },
    validatedBody: {},
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
      sid: 's1234567',
    },
  },
};

mockStatus.mockReturnValue({ json: mockJson });

describe('Offers: routes > updateOfferLocation', () => {
  beforeEach(() => {
    mockOffersService.updateOfferLocation.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });

  test('Should return valid response - update offer', async() => {
    const mockBody = {
      'location': {
        'country': 'CA',
        'provinces_states': [
          'AB', 'ON',
        ],
      },
      'approvers': [ 's1234567' ],
    };
    const mockReq = {
      body: mockBody,
      locals: {
        user: {
          sid: 's1234567',
        },
      },
    };

    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: { ...mockBody, long_name: 'updated_long_name' },
        validatedParams: {
          id: 1,
        },
      },
    };

    const fakeResponse = {
      status: 200,
      data: {
        ...mockOffer,
        long_name: 'updated_long_name',
      },
    };

    mockOffersService.updateOfferLocation.mockReturnValueOnce(fakeResponse);
    const updateOffer = updateOfferLocation(mockLogger, mockOffersService);
    await updateOffer(mockReq, res, mockNext);
    expect(mockOffersService.updateOfferLocation).toBeCalled();
  });

  it('should handle error on less than 500 response status', async() => {
    const mockReq = {
      params: {
        id: 1,
      },
      body: mockOffer,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockOffer,
        validatedParams: {
          id: 1,
        },
      },
    };
    const fakeResponse = {
      response: {
        status: 401,
        data: {},
      },
    };
    mockOffersService.updateOfferLocation.mockRejectedValue(fakeResponse);
    const updateOffer = updateOfferLocation(mockLogger, mockOffersService);
    await updateOffer(mockReq, res, mockNext);
    expect(mockNext).toHaveBeenCalledTimes(0);
    expect(res.status).toHaveBeenCalledWith(401);
  });

  it('should call next with Error on 500+ response status', async() => {
    const mockReq = {
      params: {
        id: 1,
      },
      body: mockOffer,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockOffer,
        validatedParams: {
          id: 1,
        },
      },
    };
    const fakeResponse = {
      response: {
        status: 500,
        data: {
          code: 'HTTP_INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
        },
      },
    };

    mockOffersService.updateOfferLocation.mockRejectedValue(fakeResponse);
    const updateOffer = updateOfferLocation(mockLogger, mockOffersService);
    await updateOffer(mockReq, res, mockNext);
    expect(mockOffersService.updateOfferLocation).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });

  test('should handle unknown error', async() => {
    const mockReq = {
      params: {
        id: 1,
      },
      body: mockOffer,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockOffer,
        validatedParams: {
          id: 1,
        },
      },
    };
    mockOffersService.updateOfferLocation.mockRejectedValue(new Error('unknown'));
    const updateOffer = updateOfferLocation(mockLogger, mockOffersService);
    await updateOffer(mockReq, res, mockNext);
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });
});
