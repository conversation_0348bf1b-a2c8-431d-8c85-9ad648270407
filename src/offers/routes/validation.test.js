const { omit } = require('ramda');
const mockOffer = require('../../__mocks__/mockOffer');
const { CustomError } = require('../../error');

const {
  schemaValidationMiddleware,
  VALIDATION_TYPE,
  updateSchema,
  DEFAULT_OPTS,
  getPathSchema,
  optionalGetPathSchema,
  getOffersListSchema,
  approveOfferSchema,
  updateOfferStatusSchema,
} = require('./validation');

describe('Offer routes validation', () => {
  const req = {};
  const res = {};
  const nextMock = jest.fn();
  const testScotiaID = 's1234567';

  beforeEach(() => {
    nextMock.mockClear();
  });

  it('schemaValidationMiddleware - should throw error if invalid param type passed', () => {
    schemaValidationMiddleware(getPathSchema, 'invalid-type')(req, res, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
    expect(nextMock.mock.calls[0][0].message).toBe(`Invalid joi validation type, must be: body, params, or query`);
  });

  it('schemaValidationMiddleware - should throw error if fails validation schema', () => {
    const localReq = {
      body: { title: '' },
    };
    schemaValidationMiddleware(updateSchema, 'body')(localReq, res, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(2);
    expect(nextMock.mock.calls[0][0].message).toBe(`ValidationError: "title" is not allowed to be empty.`);
  });

  it('schemaValidationMiddleware - should pass validation - optional params', () => {
    const localReq = {
      params: { id: 'OFF-1' },
    };
    const localRes = {
      locals: {},
    };
    schemaValidationMiddleware(optionalGetPathSchema, 'params')(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedParams).toEqual({ id: 'OFF-1' });
  });

  it('schemaValidationMiddleware - should throw error if invalid param type passed', () => {
    schemaValidationMiddleware(getPathSchema, 'invalid-type')(req, res, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
    expect(nextMock.mock.calls[0][0].message).toBe(`Invalid joi validation type, must be: body, params, or query`);
  });

  it('schemaValidationMiddleware - should pass validation - params', () => {
    const localReq = {
      params: { id: 'OFF-1' },
    };
    const localRes = {
      locals: {},
    };
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS)(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedParams).toEqual({ id: 'OFF-1' });
  });

  it('schemaValidationMiddleware - should pass validation - body', () => {
    const localReq = {
      body: omit([ 'change_history' ], mockOffer),
    };
    const localRes = {
      locals: {
        validatedBody: { ...localReq.body },
      },
    };
    schemaValidationMiddleware(updateSchema, undefined, DEFAULT_OPTS)(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedBody).toEqual(localReq.body);
  });

  it('schemaValidationMiddleware - should pass validation - query', () => {
    const localReq = {
      query: {
        limit: '10',
        offset: '0',
      },
    };
    const localRes = {
      locals: {
        validatedQuery: {
          ...localReq.query,
          limit: 10,
          offset: 0,
        },
      },
    };
    schemaValidationMiddleware(getOffersListSchema, VALIDATION_TYPE.QUERY)(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedQuery).toEqual(
      {
        ...localReq.query,
        limit: 10,
        offset: 0,
      },
    );
  });

  it('schemaValidationMiddleware - should pass validation - update status body', () => {
    const localReq = {
      body: {
        action: 'PAUSE',
      },
    };
    const localRes = {
      locals: {
        validatedBody: { ...localReq.body },
      },
    };
    schemaValidationMiddleware(updateOfferStatusSchema, undefined, DEFAULT_OPTS)(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedBody).toEqual(localReq.body);
  });

  it('schemaValidationMiddleware - approverOfferSchema - should pass validation - APPROVE action', () => {
    const payload = {
      action: 'APPROVE',
      approvers: [ testScotiaID ],
    };
    const localReq = {
      body: payload,
    };

    const localRes = {
      locals: {
        validatedBody: {
          ...localReq.body,
        },
      },
    };

    schemaValidationMiddleware(approveOfferSchema, DEFAULT_OPTS)(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedBody).toEqual(localReq.body);
  });
});
