const { CustomError } = require('../../error');

const getOffer = (logger, offersService) => {
  return async(req, res, next) => {
    const params = res.locals.validatedParams;
    const sid = res.locals.user && res.locals.user.sid;

    try {
      const data = await offersService.getOffer({ offerId: params.id, sid });
      return res.status(200).json(data);
    } catch (error) {
      if (error.response) {
        if (error.response.status < 500) {
          res.status(error.response.status).json(error.response.data);
        } else {
          next(new CustomError(error.response.status, JSON.stringify(error.response.data)));
        }
      } else {
        next(new CustomError(500, error.toString()));
      }
    }
  };
};

module.exports = getOffer;
