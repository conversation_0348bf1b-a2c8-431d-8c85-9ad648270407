const offerIndex = require('.');
const config = require('../../constants/config');

describe('/offers route', () => {
  const offersSerivceMock = {
    getOffer: jest.fn(),
    createOffer: jest.fn(),
    updateOffer: jest.fn(),
  };
  test('should return valid routes', () => {
    const logger = { error: jest.fn() };
    const router = offerIndex(config, logger, {}, {}, offersSerivceMock);
    const routes = [
      '/',
      '/export',
      '/assignees',
      '/:id',
    ];
    routes.forEach((route, index) => {
      expect(router.stack[index].route.path).toEqual(route);
    });
  });
});
