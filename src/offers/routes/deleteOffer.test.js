const { CustomError } = require('../../error');
const deleteOffer = require('./deleteOffer');

const mockOfferManagementService = {
  deleteOffer: jest.fn(),
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();

mockStatus.mockReturnValue({ json: mockJson });

describe('Offer Rules: routes > deleteOffer', () => {
  beforeEach(() => {
    mockOfferManagementService.deleteOffer.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });

  test('Should return valid response - delete offer', async() => {
    const mockReq = {
      params: {
        offerId: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: {
          offerId: 1,
        },
      },
    };
    mockOfferManagementService.deleteOffer.mockReturnValueOnce({ data: null,
      notifications: [ { message: `Offer with id: ${mockReq.params.offerId} has been deleted successfully` } ] });

    const deleteOfferReq = deleteOffer(mockLogger, mockOfferManagementService);
    await deleteOfferReq(mockReq, mockRes, mockNext);
    expect(mockOfferManagementService.deleteOffer).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('should handle error less than 500 response status', async() => {
    const mockReq = {
      params: {
        offerId: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: 1,
      },
    };
    const fakeResponse = {
      response: {
        status: 401,
        data: {},
      },
    };
    mockOfferManagementService.deleteOffer.mockRejectedValue(fakeResponse);
    const deleteOfferReq = deleteOffer(mockLogger, mockOfferManagementService);
    await deleteOfferReq(mockReq, mockRes, mockNext);
    expect(mockOfferManagementService.deleteOffer).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(0);
    expect(mockRes.status).toHaveBeenCalledWith(401);
  });

  test('should call next with Error on 500 response status', async() => {
    const mockReq = {
      params: {
        offerId: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: {
          offerId: 1,
        },
      },
    };
    const fakeResponse = {
      response: {
        status: 500,
        data: {
          code: 'HTTP_INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
        },
      },
    };
    mockOfferManagementService.deleteOffer.mockRejectedValue(fakeResponse);
    const deleteOfferReq = deleteOffer(mockLogger, mockOfferManagementService);
    await deleteOfferReq(mockReq, mockRes, mockNext);
    expect(mockOfferManagementService.deleteOffer).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });

  test('should handle unknown error', async() => {
    const mockReq = {
      params: {
        offerId: 1,
      },
    };
    const res = {
      status: mockStatus,
      locals: {
        validatedParams: {
          offerId: 1,
        },
      },
    };
    mockOfferManagementService.deleteOffer.mockRejectedValue(new Error('unknown'));
    const updateOffer = deleteOffer(mockLogger, mockOfferManagementService);
    await updateOffer(mockReq, res, mockNext);
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });
});
