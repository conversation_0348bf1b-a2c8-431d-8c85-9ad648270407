const { CustomError } = require('../../error');

const approveOffer = (logger, offersService) => {
  return async(req, res, next) => {
    let params;
    try {
      params = res.locals.validatedParams;
      const value = res.locals.validatedBody;
      const sid = res.locals.user && res.locals.user.sid;

      const respStatus = await offersService.approveOffer(params.id, { value, sid });
      if (respStatus && respStatus === 204) return res.sendStatus(respStatus);
    } catch (error) {
      if (error.response && error.response.status && error.response.data) {
        logger.error({ message: `Error when approving an offer - ${params.id}: ${JSON.stringify(error.response.data)} ` });
        if (error.response.status < 500) {
          return res.status(error.response.status).json(error.response.data);
        } else {
          next(new CustomError(error.response.status, JSON.stringify(error.response.data)));
        }
      } else {
        next(new CustomError(500, error.toString()));
      }
    }
  };
};

module.exports = approveOffer;
