const mockOffer = require('../../__mocks__/mockOffer');
const { CustomError } = require('../../error');
const GetOffer = require('./getOffer');

const mockOffersService = {
  getOffer: jest.fn(),
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();

mockStatus.mockReturnValue({ json: mockJson });

describe('Offers: routes > getOffer', () => {
  beforeEach(() => {
    mockOffersService.getOffer.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });

  test('Should return valid response - get offer', async() => {
    const mockReq = {
      params: {
        id: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: {
          id: 1,
        },
      },
    };
    mockOffersService.getOffer.mockReturnValueOnce(mockOffer);

    const getOffer = GetOffer(mockLogger, mockOffersService);
    await getOffer(mockReq, mockRes, mockNext);
    expect(mockOffersService.getOffer).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('should handle error less than 500 response status', async() => {
    const mockReq = {
      params: {
        id: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: 1,
      },
    };
    const fakeResponse = {
      response: {
        status: 401,
        data: {},
      },
    };
    mockOffersService.getOffer.mockRejectedValue(fakeResponse);
    const getOffer = GetOffer(mockLogger, mockOffersService);
    await getOffer(mockReq, mockRes, mockNext);
    expect(mockOffersService.getOffer).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(0);
    expect(mockRes.status).toHaveBeenCalledWith(401);
  });

  test('should call next with Error on 500 response status', async() => {
    const mockReq = {
      params: {
        id: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: {
          id: 1,
        },
      },
    };
    const fakeResponse = {
      response: {
        status: 500,
        data: {
          code: 'HTTP_INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
        },
      },
    };
    mockOffersService.getOffer.mockRejectedValue(fakeResponse);
    const getOffer = GetOffer(mockLogger, mockOffersService);
    await getOffer(mockReq, mockRes, mockNext);
    expect(mockOffersService.getOffer).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });

  test('should handle unknown error', async() => {
    const mockReq = {
      params: {
        id: 1,
      },
    };
    const res = {
      status: mockStatus,
      locals: {
        validatedParams: {
          id: 1,
        },
      },
    };
    mockOffersService.getOffer.mockRejectedValue(new Error('unknown'));
    const getOffer = GetOffer(mockLogger, mockOffersService);
    await getOffer(mockReq, res, mockNext);
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });
});
