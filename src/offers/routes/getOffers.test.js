const getOffers = require('./getOffers');
const { CustomError } = require('../../error');
const mockOffer = require('../../__mocks__/mockOffer');

const mockOffersService = {
  getAllOffers: jest.fn(),
};

const mockJson = jest.fn();

const resMock = {
  status: jest.fn().mockReturnValue({ json: mockJson }),
  locals: {
    validatedQuery: {},
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
      access: {
        campaigns: {
          containers: {
            nova: {
              view: [ 'offers-and-programs' ],
            },
          },
          pages: {
            nova: {
              view: [ 'accounts' ],
            },
          },
          ruleSubTypes: {
            nova: {
              view: [ 'targeted' ],
            },
          },
        },
      },
    },
  },
};
const reqMock = {
  query: {
    validatedQuery: {},
  },
};
const nextMock = jest.fn();

describe('GET /v1/offers', () => {
  beforeEach(() => {
    mockOffersService.getAllOffers.mockClear();
    resMock.status.mockClear();
    mockJson.mockClear();
  });

  test('should return 200 with valid req', async() => {
    const asyncGetAll = getOffers(mockOffersService);
    mockOffersService.getAllOffers.mockResolvedValueOnce({ data: [] });
    await asyncGetAll(reqMock, resMock, nextMock);
    expect(mockOffersService.getAllOffers).toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('should return 200 with offers data', async() => {
    const asyncGetAll = getOffers(mockOffersService);
    const mockOffers = [ mockOffer ];
    mockOffersService.getAllOffers.mockResolvedValueOnce({ data: mockOffers });
    await asyncGetAll(reqMock, resMock, nextMock);

    expect(mockOffersService.getAllOffers).toHaveBeenCalled();
    expect(mockJson).toHaveBeenCalledWith({
      data: mockOffers,
      limit: 10,
      offset: 0,
      total: 1,
    });
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('should return 200 with offers data with filters', async() => {
    const asyncGetAll = getOffers(mockOffersService);
    const mockOffers = [ mockOffer ];
    mockOffersService.getAllOffers.mockResolvedValueOnce({ data: mockOffers });
    resMock.locals.validatedQuery = {
      category: 'BENEFITS',
      search: 'title',
      start_date: '2025-02-25T04:00:00.000Z',
      end_date: '2025-04-21T05:00:00.000Z',
    };
    await asyncGetAll(reqMock, resMock, nextMock);

    expect(mockOffersService.getAllOffers).toHaveBeenCalled();
    expect(mockJson).toHaveBeenCalledWith({
      data: mockOffers,
      limit: 10,
      offset: 0,
      total: 1,
    });
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('should return 200 with offers data with filters - start date', async() => {
    const asyncGetAll = getOffers(mockOffersService);
    const mockOffers = [ mockOffer ];
    mockOffersService.getAllOffers.mockResolvedValueOnce({ data: mockOffers });
    resMock.locals.validatedQuery = {
      category: 'BENEFITS',
      search: 'title',
      start_date: '2025-02-25T04:00:00.000Z',
      // end_date: '2025-04-21T05:00:00.000Z',
    };
    await asyncGetAll(reqMock, resMock, nextMock);

    expect(mockOffersService.getAllOffers).toHaveBeenCalled();
    expect(mockJson).toHaveBeenCalledWith({
      data: mockOffers,
      limit: 10,
      offset: 0,
      total: 1,
    });
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('should return 200 with offers data with filters - end date', async() => {
    const asyncGetAll = getOffers(mockOffersService);
    const mockOffers = [ mockOffer ];
    mockOffersService.getAllOffers.mockResolvedValueOnce({ data: mockOffers });
    resMock.locals.validatedQuery = {
      category: 'BENEFITS',
      search: 'title',
      // start_date: '2025-02-25T04:00:00.000Z',
      end_date: '2025-04-21T05:00:00.000Z',
    };
    await asyncGetAll(reqMock, resMock, nextMock);

    expect(mockOffersService.getAllOffers).toHaveBeenCalled();
    expect(mockJson).toHaveBeenCalledWith({
      data: mockOffers,
      limit: 10,
      offset: 0,
      total: 1,
    });
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('should handle unknown error', async() => {
    const mockOffersService = {
      getAllOffers: jest.fn().mockRejectedValue(new Error('unknown')),
    };
    const asyncGetAll = getOffers(mockOffersService);

    try {
      await asyncGetAll(reqMock, resMock, nextMock);
    } catch (err) {
      expect(err.message).toBe('unknown');
      expect(nextMock).toHaveBeenCalledTimes(1);
      expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
    }
  });

  test('should handle 401 error', async() => {
    const mockOffersService = {
      getAllOffers: jest.fn().mockRejectedValue({ response: { status: 401, data: {} } }),
    };
    const asyncGetAll = getOffers(mockOffersService);

    try {
      await asyncGetAll(reqMock, resMock, nextMock);
    } catch (err) {
      expect(nextMock).toHaveBeenCalledTimes(0);
      expect(resMock.status).toHaveBeenCalledWith(401);
    }
  });

  test('should handle 500 error', async() => {
    const mockOffersService = {
      getAllOffers: jest.fn().mockRejectedValue({ response: { status: 500, data: {} } }),
    };
    const asyncGetAll = getOffers(mockOffersService);

    try {
      await asyncGetAll(reqMock, resMock, nextMock);
    } catch (err) {
      expect(nextMock).toHaveBeenCalledTimes(1);
      expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
    }
  });
});
