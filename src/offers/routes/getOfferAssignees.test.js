const { CustomError } = require('../../error');
const GetOfferAssignees = require('./getOfferAssignees');

// Mock the CustomError class
jest.mock('../../error', () => ({
  CustomError: jest.fn().mockImplementation((statusCode, message) => ({
    statusCode,
    message,
  })),
}));

const mockService = {
  getPotentialAssigneesForOfferCampaign: jest.fn(),
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

// Mock next
const mockNext = jest.fn();
// Mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();
mockStatus.mockReturnValue({ json: mockJson });
const mockRes = {
  status: mockStatus,
  locals: {
    validatedQuery: { offer_status: 'DRAFT' },
    user: {
      team_id: 'team123',
    },
  },
};

describe('Offer: routes > getOfferAssignees', () => {
  beforeEach(() => {
    mockService.getPotentialAssigneesForOfferCampaign.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
    CustomError.mockClear();
    console.log = jest.fn();
  });

  test('should return potential assignees successfully', async() => {
    const mockReq = {};
    const res = { ...mockRes };

    const mockAssignees = [
      { sid: 'user1', full_name: 'John Doe' },
      { sid: 'user2', full_name: 'Jane Smith' },
    ];

    mockService.getPotentialAssigneesForOfferCampaign.mockResolvedValueOnce(mockAssignees);

    const getOfferAssignees = GetOfferAssignees(mockLogger, mockService);
    await getOfferAssignees(mockReq, res, mockNext);

    expect(mockService.getPotentialAssigneesForOfferCampaign).toHaveBeenCalledWith(
      'DRAFT',
      'team123',
    );
    expect(mockStatus).toHaveBeenCalledWith(200);
    expect(mockJson).toHaveBeenCalledWith({ data: mockAssignees });
    expect(mockNext).not.toHaveBeenCalled();
    expect(CustomError).not.toHaveBeenCalled();
  });

  test('should handle error on less than 500 response status', async() => {
    const mockReq = {};
    const res = { ...mockRes };

    const fakeResponse = {
      response: {
        status: 400,
        data: {
          errorMessage: 'Invalid offer status',
        },
      },
    };

    mockService.getPotentialAssigneesForOfferCampaign.mockRejectedValue(fakeResponse);

    const getOfferAssignees = GetOfferAssignees(mockLogger, mockService);
    await getOfferAssignees(mockReq, res, mockNext);

    expect(mockService.getPotentialAssigneesForOfferCampaign).toHaveBeenCalled();
    expect(mockLogger.error).toHaveBeenCalled();
    expect(mockStatus).toHaveBeenCalledWith(400);
    expect(mockJson).toHaveBeenCalledWith({ errorMessage: 'Invalid offer status' });
    expect(mockNext).not.toHaveBeenCalled();
    expect(CustomError).not.toHaveBeenCalled();
  });

  test('should call next with CustomError on 500 response status', async() => {
    const mockReq = {};
    const res = { ...mockRes };

    const fakeResponse = {
      response: {
        status: 500,
        data: {
          errorMessage: 'Internal server error',
        },
      },
    };

    mockService.getPotentialAssigneesForOfferCampaign.mockRejectedValue(fakeResponse);

    const getOfferAssignees = GetOfferAssignees(mockLogger, mockService);
    await getOfferAssignees(mockReq, res, mockNext);

    expect(mockService.getPotentialAssigneesForOfferCampaign).toHaveBeenCalled();
    expect(mockLogger.error).toHaveBeenCalled();
    expect(CustomError).toHaveBeenCalledWith(
      500,
      JSON.stringify({ errorMessage: 'Internal server error' }),
    );
    expect(mockNext).toHaveBeenCalledWith(
      expect.objectContaining({
        statusCode: 500,
        message: JSON.stringify({ errorMessage: 'Internal server error' }),
      }),
    );
  });

  test('should handle unknown error with CustomError', async() => {
    const mockReq = {};
    const res = { ...mockRes };

    const error = new Error('Unknown error occurred');
    mockService.getPotentialAssigneesForOfferCampaign.mockRejectedValue(error);

    const getOfferAssignees = GetOfferAssignees(mockLogger, mockService);
    await getOfferAssignees(mockReq, res, mockNext);

    expect(mockService.getPotentialAssigneesForOfferCampaign).toHaveBeenCalled();
    expect(CustomError).toHaveBeenCalledWith(
      500,
      'Error: Unknown error occurred',
    );
    expect(mockNext).toHaveBeenCalledWith(
      expect.objectContaining({
        statusCode: 500,
        message: 'Error: Unknown error occurred',
      }),
    );
  });
});
