const { CustomError } = require('../../error');

// Getting offer assignees based on offer_status
const getOfferAssignees = function(logger, assigneeService) {
  return async function(req, res, next) {
    const validQuery = res.locals.validatedQuery;
    try {
      const potentialAssignees = await assigneeService.getPotentialAssigneesForOfferCampaign(validQuery.offer_status, res.locals.user.team_id);
      res.status(200).json({ data: potentialAssignees });
    } catch (err) {
      if (err.response) {
        logger.error({ message: `Error when getting assignee for an offer: ${JSON.stringify(err.response.data)} ` });
        if (err.response.status < 500) {
          res.status(err.response.status).json(err.response.data);
        } else {
          next(new CustomError(err.response.status, JSON.stringify(err.response.data)));
        }
      } else {
        next(new CustomError(500, err.toString()));
      }
    }
  };
};

module.exports = getOfferAssignees;
