const express = require('express');
const { wrapAsync } = require('../../utils');
const createOffer = require('./createOffer');
const getOffer = require('./getOffer');
const updateOffer = require('./updateOffer');
const updateOfferLocation = require('./updateOfferLocation');
const getOffers = require('./getOffers');
const approveOffer = require('./approveOffer');
const getOfferAssignees = require('./getOfferAssignees');
const {
  schemaValidationMiddleware,
  optionalGetPathSchema,
  VALIDATION_TYPE,
  createSchema,
  updateSchema,
  updateOfferLocationSchema,
  getPathSchema,
  getOffersListSchema,
  updateOfferStatusSchema,
  approveOfferSchema,
  getOfferAssigneesSchema,
} = require('./validation');
const deleteOffer = require('./deleteOffer');
const exportOffers = require('./exportOffers');

const init = (config, logger, offersService, ruleService, userService) => {
  const router = express.Router();

  router.get(
    '/',
    schemaValidationMiddleware(getOffersListSchema, VALIDATION_TYPE.QUERY),
    wrapAsync(getOffers(offersService)),
  );

  router.get(
    '/export',
    schemaValidationMiddleware(getOffersListSchema, VALIDATION_TYPE.QUERY),
    wrapAsync(exportOffers(offersService, userService)),
  );

  router.get(
    '/assignees',
    schemaValidationMiddleware(getOfferAssigneesSchema, VALIDATION_TYPE.QUERY),
    wrapAsync(getOfferAssignees(logger, ruleService)),
  );

  router.get(
    '/:id',
    schemaValidationMiddleware(optionalGetPathSchema, VALIDATION_TYPE.PARAMS),
    wrapAsync(getOffer(logger, offersService)),
  );

  router.post(
    '/',
    schemaValidationMiddleware(createSchema, VALIDATION_TYPE.BODY),
    wrapAsync(createOffer(logger, offersService)),
  );

  router.put(
    '/:id',
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS),
    schemaValidationMiddleware(updateSchema, VALIDATION_TYPE.BODY),
    wrapAsync(updateOffer(logger, offersService)),
  );

  router.put(
    '/updateOfferLocation/:id',
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS),
    schemaValidationMiddleware(updateOfferLocationSchema, VALIDATION_TYPE.BODY),
    wrapAsync(updateOfferLocation(logger, offersService)),
  );

  router.put(
    '/:id/status',
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS),
    schemaValidationMiddleware(updateOfferStatusSchema, VALIDATION_TYPE.BODY),
    wrapAsync(updateOffer(logger, offersService)),
  );

  router.delete(
    '/:id',
    schemaValidationMiddleware(optionalGetPathSchema, VALIDATION_TYPE.PARAMS),
    wrapAsync(deleteOffer(logger, offersService)),
  );

  router.patch(
    '/:id',
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS),
    schemaValidationMiddleware(approveOfferSchema, VALIDATION_TYPE.BODY),
    wrapAsync(approveOffer(logger, offersService)),
  );

  return router;
};

module.exports = init;
