const { CustomError } = require('../../error');
const ApproveOffer = require('./approveOffer');

const mockOffersService = {
  approveOffer: jest.fn(),
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

// Mock the CustomError class
jest.mock('../../error', () => ({
  CustomError: jest.fn().mockImplementation((statusCode, message) => ({
    statusCode,
    message,
  })),
}));

// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();
mockStatus.mockReturnValue({ json: mockJson });
const mockRes = {
  status: mockStatus,
  locals: {
    validatedParams: { id: 'OFF-1' },
    validatedBody: {
      action: 'APPROVE',
      approvers: [ 's1234567', 's6719128' ],
    },
    user: {
      sid: 's1234567',
    },
  },
};

describe('Offer: routes > approveOffer', () => {
  beforeEach(() => {
    mockOffersService.approveOffer.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
    CustomError.mockClear();
  });

  test('Should return 204 status when approving offer successfully', async() => {
    const mockReq = {};
    const mockSendStatus = jest.fn();
    const res = {
      ...mockRes,
      sendStatus: mockSendStatus,
    };

    const mockResponse = 204;

    mockOffersService.approveOffer.mockResolvedValueOnce(mockResponse);

    const approveOffer = ApproveOffer(mockLogger, mockOffersService);
    await approveOffer(mockReq, res, mockNext);

    expect(mockOffersService.approveOffer).toHaveBeenCalledWith('OFF-1', { value: {
      action: 'APPROVE',
      approvers: [ 's1234567', 's6719128' ],
    },
    sid: 's1234567' });
    expect(mockSendStatus).toHaveBeenCalledWith(204);
    expect(mockNext).not.toHaveBeenCalled();
    expect(CustomError).not.toHaveBeenCalled();
  });

  test('should handle error on less than 500 response status', async() => {
    const mockReq = {};
    const res = { ...mockRes };

    const mockResponse = {
      response: {
        status: 400,
        data: {
          notifications: [ {
            code: 400,
            metadata: {
              details: 'Bad Request',
            },
          } ],
        },
      },
    };

    mockOffersService.approveOffer.mockRejectedValue(mockResponse);

    const approveOffer = ApproveOffer(mockLogger, mockOffersService);
    await approveOffer(mockReq, res, mockNext);

    expect(mockOffersService.approveOffer).toHaveBeenCalled();
    expect(mockLogger.error).toHaveBeenCalled();
    expect(mockStatus).toHaveBeenCalledWith(400);
    expect(mockJson).toHaveBeenCalledWith(mockResponse.response.data);
    expect(mockNext).not.toHaveBeenCalled();
  });

  test('should call next with Error on 500 response status', async() => {
    const mockReq = {};
    const res = { ...mockRes };

    const mockResponse = {
      response: {
        status: 500,
        data: {
          errorMessage: 'Internal server error',
        },
      },
    };

    mockOffersService.approveOffer.mockRejectedValue(mockResponse);

    const approveOffer = ApproveOffer(mockLogger, mockOffersService);
    await approveOffer(mockReq, res, mockNext);

    expect(mockOffersService.approveOffer).toHaveBeenCalled();
    expect(CustomError).toHaveBeenCalledWith(
      500,
      JSON.stringify({ errorMessage: 'Internal server error' }),
    );
  });

  test('should handle unknown error', async() => {
    const mockReq = {};
    const res = { ...mockRes };

    const error = new Error('Unknown error occurred');
    mockOffersService.approveOffer.mockRejectedValue(error);

    const approveOffer = ApproveOffer(mockLogger, mockOffersService);
    await approveOffer(mockReq, res, mockNext);

    expect(mockOffersService.approveOffer).toHaveBeenCalled();
    expect(CustomError).toHaveBeenCalledWith(
      500,
      'Error: Unknown error occurred',
    );
  });
});
