const { CustomError } = require('../../error');

const createOffer = (logger, offersService) => {
  return async(req, res, next) => {
    try {
      const value = res.locals.validatedBody;
      const sid = res.locals.user && res.locals.user.sid;

      const newOffer = await offersService.createOffer({ value, sid });
      return res.status(200).json(newOffer);
    } catch (error) {
      if (error.response) {
        if (error.response.status < 500) {
          res.status(error.response.status).json(error.response.data);
        } else {
          next(new CustomError(error.response.status, JSON.stringify(error.response.data)));
        }
      } else {
        next(new CustomError(500, error.toString()));
      }
    }
  };
};

module.exports = createOffer;
