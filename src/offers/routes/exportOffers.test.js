const request = require('supertest');
const express = require('express');
const exportOffers = require('./exportOffers');

// Mock the mockOffersService with a getAllOffers function
const mockOffersService = {
  getAllOffers: jest.fn().mockResolvedValue({
    data: [
      {
        offer_id: 'OFF-123',
        title: 'test offer',
        start_date: '2024-01-01',
        end_date: '2024-01-01',
        category: 'OFFERS',
        offer_status: 'ACTIVE',
        approvers: {
          approvers: [ 's1234567' ],
        },
        priority: '12',
      },
      {
        offer_id: 'BEN-123',
        title: 'test offer',
        start_date: '2024-01-01',
        end_date: '2024-01-01',
        category: 'BENEFITS',
        offer_status: 'ACTIVE',
        priority: '12',
      },
    ],
  }),
};

const mockUserService = {
  getUsersFromSIDs: jest.fn(),
};

// Setup the Express server and route for testing
const app = express();
app.use(express.json());
app.use((req, res, next) => {
  res.locals = {
    validatedQuery: {},
  };

  res.locals.validatedQuery = { ...req.query };
  next();
});
app.use('/export', exportOffers(mockOffersService, mockUserService));

describe('exportOffers', () => {
  it('should download a CSV file with the correct content', async() => {
    const response = await request(app)
      .get('/export')
      .expect('Content-Type', /text\/csv/)
      .expect('Content-Disposition', /attachment; filename="offers-list-export.csv"/)
      .expect(200);

    // Check if the CSV string matches expected output
    expect(response.text).toContain('ID,Title,Start Date,End Date,Category,Approver,Status,Priority');
    expect(response.text).toContain('OFF-123,test offer,2024-01-01,2024-01-01,OFFERS,s1234567,ACTIVE,12');
    expect(response.text).toContain('BEN-123,test offer,2024-01-01,2024-01-01,BENEFITS,NA,ACTIVE,12');
  });

  it('should download a CSV file with the correct content with filters', async() => {
    const response = await request(app)
      .get('/export')
      .query({ category: 'BENEFITS', search: 'test' })
      .expect('Content-Type', /text\/csv/)
      .expect('Content-Disposition', /attachment; filename="offers-list-export.csv"/)
      .expect(200);

    // Check if the CSV string matches expected output
    expect(response.text).toContain('ID,Title,Start Date,End Date,Category,Approver,Status,Priority');
    expect(response.text).toContain('BEN-123,test offer,2024-01-01,2024-01-01,BENEFITS,NA,ACTIVE,12');
  });

  it('should handle errors correctly', async() => {
    // Mock the service to throw an error
    mockOffersService.getAllOffers.mockRejectedValueOnce(new Error('Service error'));

    const response = await request(app)
      .get('/export')
      .expect(500);

    // Check if the error message is correct
    expect(response.body).toEqual(expect.anything()); // Update this based on how you handle errors
  });
});
