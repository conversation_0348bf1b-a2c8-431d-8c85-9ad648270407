const { filter, whereEq, includes, toLower, uniq, flatten, map, pathOr } = require('ramda');
const { CustomError } = require('../../error');

const SHEET_HEADERS = [
  'ID',
  'Title',
  'Start Date',
  'End Date',
  'Category',
  'Approver',
  'Status',
  'Priority',
];

const exportOffers = (offersService, userService) => {
  return async(req, res, next) => {
    try {
      const query = res.locals.validatedQuery;
      const sid = res.locals.user && res.locals.user.sid;
      let allOffers = await offersService.getAllOffers({
        ...query,
        offset: 0,
        limit: 0,
      },
      sid,
      );
      let filteredOffers = allOffers.data;

      if (query.search) {
        filteredOffers = filter(offer => includes(toLower(query.search), toLower(offer.offer_id)) || includes(toLower(query.search), toLower(offer.title)), filteredOffers);
      }
      const filters = {
        ...(query.category ? { category: query.category } : {}),
        ...(query.priority ? { priority: query.priority } : {}),
        ...(query.offer_id ? { offer_id: query.offer_id } : {}),
        ...(query.status ? { offer_status: query.status } : {}),
      };

      filteredOffers = filter(whereEq(filters), filteredOffers);

      const uniqueUserIdList = uniq(flatten(map(pathOr([], [ 'approvers', 'approvers' ]), filteredOffers)));
      const uniqueUsers = await userService.getUsersFromSIDs(uniqueUserIdList);

      const uniqueUsersMap = (uniqueUsers || []).reduce((acc, val) => {
        acc[val.sid] = val.name;
        return acc;
      }, {});

      const csvHeadings = `${SHEET_HEADERS.toString()}\n`;

      const csvString = filteredOffers.reduce((acc, offer) => {
        let approverNames = [];
        if (offer.approvers && offer.approvers.approvers.length) {
          approverNames = offer.approvers.approvers.map(approver => uniqueUsersMap[approver] || approver);
        }
        acc += `${[ offer.offer_id,
          offer.title,
          offer.start_date,
          offer.end_date,
          offer.category,
          approverNames.length ? approverNames.join(',') : 'NA',
          offer.offer_status,
          offer.priority,
        ].toString()}\n`;
        return acc;
      }, csvHeadings);

      res.set('Content-Type', 'text/csv');
      res.attachment(`offers-list-export.csv`);
      res.send(csvString);
    } catch (err) {
      next(new CustomError(500, err.toString()));
    }
  };
};

module.exports = exportOffers;
