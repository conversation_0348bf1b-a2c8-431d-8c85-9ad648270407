const exportRules = require('./exportRules');

const mockRule = id => ({
  external_ref: id,
  name: `Name ${id}`,
  content_space: '4szkx38resvm',
  content_id: `CONTENT_ID_${id}`,
  start_at: 'May 17, 2021',
  end_at: 'May 22, 2021',
  status: 'published',
  application: 'Nova',
  pages: [ 'page', 'page1' ],
  container: 'container',
  platforms: [ 'ios', 'android' ],
});

const rules = Array.from({ length: 5 }, (val, index) => mockRule(index));

describe('exportRules', () => {
  const getAllRules = jest.fn().mockResolvedValue({
    data: {
      items: rules,
    },
  });
  const contentApi = {
    getContentsByIds: jest.fn().mockResolvedValue({
      data: {
        items: [
          { id: 'CONTENT_ID_0', content: { name: 'First Content' } },
          { id: 'CONTENT_ID_1', content: { name: 'Second Content' } },
          { id: 'CONTENT_ID_2', content: { name: 'Third Content' } },
          { id: 'CONTENT_ID_3', content: { name: 'Fourth Content' } },
          { id: 'CONTENT_ID_4', content: { name: 'Fifth Content' } },
        ],
      },
    }),
  };

  const mockRes = {
    send: jest.fn(),
    set: jest.fn(),
    attachment: jest.fn(),
    locals: {
      validatedQuery: {},
    },
  };
  const campaignParams = [ 'http://localhost:8090', 'campaign', getAllRules, contentApi ];

  beforeEach(() => {
    getAllRules.mockClear();
    contentApi.getContentsByIds.mockClear();
    mockRes.send.mockClear();
    mockRes.set.mockClear();
    mockRes.attachment.mockClear();
  });

  test('exporting the fields that require no transformation', async() => {
    const req = {
      query: {
        export: 'external_ref,name,platforms,application,pages,container',
      },
    };
    const res = {
      ...mockRes,
      locals: {
        validatedQuery: {
          export: [ 'external_ref', 'name', 'platforms', 'application', 'pages', 'container' ],
        },
      },
    };

    const rulesFunc = exportRules(...campaignParams);
    await rulesFunc(req, res, jest.fn());
    expect(res.send.mock.calls[0][0]).toContain('Campaign ID');
    expect(res.send.mock.calls[0][0]).toContain('Campaign Name');
    expect(res.send.mock.calls[0][0]).toContain('Platforms');
    expect(res.send.mock.calls[0][0]).toContain('Application');
    expect(res.send.mock.calls[0][0]).toContain('Pages');
    expect(res.send.mock.calls[0][0]).toContain('Container');
    expect(res.send.mock.calls[0][0]).toMatchSnapshot();
  });

  test('exporting the status field', async() => {
    const req = {
      query: {
        export: 'name,status',
      },
    };
    const res = {
      ...mockRes,
      locals: {
        validatedQuery: {
          export: [ 'name', 'status' ],
        },
      },
    };

    const rulesFunc = exportRules(...campaignParams);
    await rulesFunc(req, res, jest.fn());
    expect(res.send.mock.calls[0][0]).toContain('Status');
    expect(res.send.mock.calls[0][0]).toContain('expired');
    expect(res.send.mock.calls[0][0]).toMatchSnapshot();
  });

  test('exporting the date field', async() => {
    const req = {
      query: {
        export: 'name,date',
      },
    };
    const res = {
      ...mockRes,
      locals: {
        validatedQuery: {
          export: [ 'name', 'date' ],
        },
      },
    };

    const rulesFunc = exportRules(...campaignParams);
    await rulesFunc(req, res, jest.fn());
    expect(res.send.mock.calls[0][0]).toContain('Start Date');
    expect(res.send.mock.calls[0][0]).toContain('May 17, 2021');
    expect(res.send.mock.calls[0][0]).toContain('End Date');
    expect(res.send.mock.calls[0][0]).toContain('May 22, 2021');
    expect(res.send.mock.calls[0][0]).toMatchSnapshot();
  });

  test('exporting content_id resolves to content name', async() => {
    const req = {
      query: {
        export: 'name,content_id',
      },
    };
    const res = {
      ...mockRes,
      locals: {
        validatedQuery: {
          export: [ 'name', 'content_id' ],
        },
      },
    };

    const rulesFunc = exportRules(...campaignParams);
    await rulesFunc(req, res, jest.fn());
    expect(res.send.mock.calls[0][0]).toContain('Content Preview Name');
    expect(res.send.mock.calls[0][0]).toContain('First Content');
    expect(res.send.mock.calls[0][0]).toMatchSnapshot();
  });
});
