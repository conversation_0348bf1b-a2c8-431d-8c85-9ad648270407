const { capitalize, dateFormat } = require('../utils');

const contentfulSpaces = [ '4szkx38resvm', 'gk5ecj6dpckc', 'aqy9l22vkp1s' ];

const exportRules = (baseURL, ruleType, getAllRules, contentApi, userService) => async(req, res, next) => {
  const value = res.locals.validatedQuery;

  const csvHeadingMap = {
    'external_ref': `${capitalize(ruleType)} ID`,
    'name': `${capitalize(ruleType)} Name`,
    'content_id': 'Content Preview Name',
    'start_at': 'Start Date',
    'end_at': 'End Date',
    'status': 'Status',
    'platforms': 'Platforms',
    'application': 'Application',
    'pages': 'Pages',
    'container': 'Container',
    'created_by': 'Created By',
    'updated_by': 'Last Updated By',
    'updated_at': 'Last Updated At',
    'url': `${capitalize(ruleType)} URL`,
  };

  const { export: exportKeys, ...query } = value;
  const dateIndex = exportKeys.indexOf('date');
  if (dateIndex !== -1) {
    exportKeys.splice(dateIndex, 1, 'start_at', 'end_at');
  }

  const allRules = await getAllRules(query);

  const reduceObjectToKeyset = (obj, keyset = []) => {
    const output = {};
    keyset.forEach(key => {
      output[key] = Array.isArray(obj[key]) ? obj[key].join(', ') : obj[key];
      if (key === 'status' && obj[key] === 'published') {
        if (new Date(obj.end_at) < Date.now()) {
          output[key] = 'expired';
        } else if (new Date(obj.start_at) > Date.now() && !obj.disabled) {
          output[key] = 'upcoming';
        } else if (obj.disabled) {
          output[key] = 'inactive';
        } else {
          output[key] = 'active';
        }
      }
      if (![ 'content_id', 'created_by', 'updated_by' ].includes(key)) {
        output[key] = `"${output[key]}"`;
      }
    });
    return output;
  };

  const reducedRules = allRules.data.items.map(item => {
    // format the dates in a friendlier format if they are included in the export
    if (dateIndex !== -1) {
      item.start_at = dateFormat(item.start_at);
      item.end_at = dateFormat(item.end_at);
    };
    const output = reduceObjectToKeyset(item, exportKeys);
    output.content_space = item.content_space;
    // include a link to the rule in admin
    output['url'] = `=HYPERLINK("${baseURL}/${ruleType}s/${item.id}")`;
    return output;
  });

  const exportContentName = exportKeys.includes('content_id');
  if (exportContentName) {
    // reduce the rules to a set of ids to call content api with
    const allContents = await Promise.all(contentfulSpaces.map(async contentfulSpace => {
      const uniqueContentIds = new Set();
      reducedRules.filter(rule => rule.content_space === contentfulSpace).forEach(rule => {
        uniqueContentIds.add(rule.content_id);
      });
      const uniqueContentIdList = Array.from(uniqueContentIds);

      if (uniqueContentIdList.length) {
        // contentful default limit is 100 entries
        let allContentItems = [];
        const iterations = Math.floor(uniqueContentIds.size / 100);
        for (let i = 0; i <= iterations; i++) {
          const contentIdsSubset = uniqueContentIdList.slice(i * 100, i * 100 + 100).join(',');
          const contents = await contentApi.getContentsByIds(contentfulSpace, { content_ids: contentIdsSubset });
          allContentItems.push(...contents.data.items);
        }
        return { [contentfulSpace]: allContentItems };
      }
    }));

    const contentMap = new Map();
    allContents.forEach(content => {
      if (content) {
        const items = Object.values(content)[0];
        const contentfulSpace = Object.keys(content)[0];
        items.forEach(item => {
          contentMap.set(`${contentfulSpace}:${item.id}`, `"${item.content.name}"`);
        });
      }
    });

    reducedRules.forEach(rule => {
      rule.content_id = contentMap.get(`${rule.content_space}:${rule.content_id}`);
    });
  }

  const exportUserField = exportKeys.includes('created_by') || exportKeys.includes('updated_by');
  if (exportUserField) {
    // reduce the rules to a set of sIDs to fetch from db
    const uniqueUserIds = new Set();
    reducedRules.forEach(rule => {
      if (exportKeys.includes('created_by')) {
        uniqueUserIds.add(rule.created_by);
      }
      if (exportKeys.includes('updated_by') && rule.updated_by !== rule.created_by) {
        uniqueUserIds.add(rule.updated_by);
      }
    });
    const uniqueUserIdList = Array.from(uniqueUserIds);
    const uniqueUsers = await userService.getUsersFromSIDs(uniqueUserIdList);

    const uniqueUsersMap = (uniqueUsers || []).reduce((acc, val) => {
      acc[val.sid] = val.name;
      return acc;
    }, {});

    reducedRules.forEach(rule => {
      if (exportKeys.includes('created_by')) {
        rule.created_by = uniqueUsersMap[rule.created_by];
      }
      if (exportKeys.includes('updated_by')) {
        rule.updated_by = uniqueUsersMap[rule.updated_by];
      }
    });
  }

  const allExportKeys = [ ...exportKeys, 'url' ];
  const csvHeadings = `${allExportKeys.map(exportKey => csvHeadingMap[exportKey]).join(',')}\n`;
  const csvString = reducedRules.reduce((acc, val) => {
    acc += `${allExportKeys.map(exportKey => val[exportKey]).join(',')}\n`;
    return acc;
  }, csvHeadings);

  res.set('Content-Type', 'text/csv');
  res.attachment(`${ruleType}-list-export.csv`);
  res.send(csvString);
};

module.exports = exportRules;
