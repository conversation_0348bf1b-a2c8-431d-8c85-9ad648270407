// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`exportRules exporting content_id resolves to content name 1`] = `
"Campaign Name,Content Preview Name,Campaign URL
"Name 0","First Content",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"Name 1","Second Content",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"Name 2","Third Content",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"Name 3","Fourth Content",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"Name 4","Fifth Content",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"
`;

exports[`exportRules exporting the date field 1`] = `
"Campaign Name,Start Date,End Date,Campaign URL
"Name 0","May 17, 2021 12:00 AM","May 22, 2021 12:00 AM",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"Name 1","May 17, 2021 12:00 AM","May 22, 2021 12:00 AM",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"Name 2","May 17, 2021 12:00 AM","May 22, 2021 12:00 AM",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"Name 3","May 17, 2021 12:00 AM","May 22, 2021 12:00 AM",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"Name 4","May 17, 2021 12:00 AM","May 22, 2021 12:00 AM",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"
`;

exports[`exportRules exporting the fields that require no transformation 1`] = `
"Campaign ID,Campaign Name,Platforms,Application,Pages,Container,Campaign URL
"0","Name 0","ios, android","Nova","page, page1","container",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"1","Name 1","ios, android","Nova","page, page1","container",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"2","Name 2","ios, android","Nova","page, page1","container",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"3","Name 3","ios, android","Nova","page, page1","container",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"4","Name 4","ios, android","Nova","page, page1","container",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"
`;

exports[`exportRules exporting the status field 1`] = `
"Campaign Name,Status,Campaign URL
"Name 0","expired",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"Name 1","expired",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"Name 2","expired",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"Name 3","expired",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"Name 4","expired",=HYPERLINK("http://localhost:8090/campaigns/undefined")
"
`;
