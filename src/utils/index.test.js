const { getCampaignDetailsUrl, mapPropToKey } = require('./');

describe('utils', () => {
  test('Get campaign details url', () => {
    const response = getCampaignDetailsUrl('1234');
    expect(response).toBe('http://localhost:8090/campaigns/1234');
  });

  test('map props to key', () => {
    const data = [
      {
        name: 'test',
        id: 'testing',
        description: 'This is a test',
      },
      {
        name: 'test2',
        id: 'testing2',
        description: 'another test',
      },
    ];
    const response = mapPropToKey(data, 'id');
    expect(Object.keys(response).length).toBe(2);
  });
});
