const { createAlertSchema } = require('../alert/routes/validation');

describe('Custom Joi Extensions', () => {
  describe('appVersion', () => {
    test('should fail on invalid app version', () => {
      const payload = {
        name: 'sample name',
        start_at: '2018-01-01T00:00:00Z',
        end_at: '2018-01-01T00:00:00Z',
        container: 'alert',
        platforms_targeting: [ {
          v: 1,
          platform: 'ios',
          items: [ { app_version: 'invalid' } ],
        } ],
      };
      const { error } = createAlertSchema.validate(payload);
      expect(error.message).toStrictEqual('Invalid app version format');
    });

    test('should fail on invalid app range', () => {
      const payload = {
        name: 'sample name',
        start_at: '2018-01-01T00:00:00Z',
        end_at: '2018-01-01T00:00:00Z',
        container: 'alert',
        platforms_targeting: [ {
          v: 1,
          platform: 'ios',
          items: [ { app_version: 'invalid - invalid' } ],
        } ],
      };
      const { error } = createAlertSchema.validate(payload);
      expect(error.message).toStrictEqual('Invalid app range format');
    });

    test('should fail if app max is lower than app min', () => {
      const payload = {
        name: 'sample name',
        start_at: '2018-01-01T00:00:00Z',
        end_at: '2018-01-01T00:00:00Z',
        container: 'alert',
        platforms_targeting: [ {
          v: 1,
          platform: 'ios',
          items: [ { app_version: '2.2.2 - 1.1.1' } ],
        } ],
      };
      const { error } = createAlertSchema.validate(payload);
      expect(error.message).toStrictEqual('Max app version must be greater than min');
    });
  });

  describe('osVersion', () => {
    test('should fail on invalid os version', () => {
      const payload = {
        name: 'sample name',
        start_at: '2018-01-01T00:00:00Z',
        end_at: '2018-01-01T00:00:00Z',
        container: 'alert',
        platforms_targeting: [ {
          v: 1,
          platform: 'ios',
          items: [ { os_version: 'invalid' } ],
        } ],
      };
      const { error } = createAlertSchema.validate(payload);
      expect(error.message).toStrictEqual('Invalid os version format');
    });

    test('should fail on invalid os range', () => {
      const payload = {
        name: 'sample name',
        start_at: '2018-01-01T00:00:00Z',
        end_at: '2018-01-01T00:00:00Z',
        container: 'alert',
        platforms_targeting: [ {
          v: 1,
          platform: 'ios',
          items: [ { os_version: 'invalid - invalid' } ],
        } ],
      };
      const { error } = createAlertSchema.validate(payload);
      expect(error.message).toStrictEqual('Invalid os range format');
    });

    test('should fail if os max is lower than os min', () => {
      const payload = {
        name: 'sample name',
        start_at: '2018-01-01T00:00:00Z',
        end_at: '2018-01-01T00:00:00Z',
        container: 'alert',
        platforms_targeting: [ {
          v: 1,
          platform: 'ios',
          items: [ { os_version: '2 - 1.1' } ],
        } ],
      };
      const { error } = createAlertSchema.validate(payload);
      expect(error.message).toStrictEqual('Max os version must be greater than min');
    });
  });
});
