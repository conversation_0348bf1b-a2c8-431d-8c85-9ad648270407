const { TABLE } = require('../constants/db');

const TRANSFORM_RESULT = (data, mapping, unique) => Object.values(data.reduce((o, i) => {
  if (!o[i.id]) {
    Object.keys(mapping).forEach(k => {
      i[k] = [];
    });
    o[i.id] = i;
  }
  Object.entries(mapping).forEach(([ k, v ]) => {
    if (i[v]) {
      if (unique) {
        if (o[i.id][k].indexOf(i[v]) === -1) {
          o[i.id][k].push(i[v]);
        }
      } else {
        o[i.id][k].push(i[v]);
      }
    }
    delete o[i.id][v];
  });
  return o;
}, {}));

const TRANSFORM_INPUT = (data, mapping) => {
  const result = {};
  Object.entries(mapping).forEach(([ k, v ]) => {
    if (data.hasOwnProperty(k)) {
      result[v] = data[k];
    }
  });
  return result;
};

const TRUNCATE_DATABASE = async(dbInstance) => {
  const promises = [];
  for (let key in TABLE) {
    promises.push(dbInstance(TABLE[key]).truncate());
  }
  return Promise.all(promises);
};

const SORT_AND_PAGINATE = (results, query) => {
  try {
    const { sort } = query;
    // Sorting
    let sortedItems = results;
    if (sort) {
      const sortKey = sort.replace('-', '');
      const type = typeof results[0][sortKey];
      // Sanitze results
      const nullValues = (type !== 'boolean') ? results.filter(r => !r[sortKey]) : [];
      const santizedResults = nullValues.length ? results.filter(r => r[sortKey]) : results;
      if ([ 'boolean', 'number' ].includes(type)) {
        if (sort.search(sortKey) === 0) {
          sortedItems = santizedResults.sort((a, b) => {
            if (a[sortKey] < b[sortKey]) {
              return -1;
            }
            if (a[sortKey] > b[sortKey]) {
              return 1;
            }
            return 0;
          });
        } else if (sort.search(sortKey) > 0) {
          sortedItems = santizedResults.sort((a, b) => {
            if (a[sortKey] < b[sortKey]) {
              return 1;
            }
            if (a[sortKey] > b[sortKey]) {
              return -1;
            }
            return 0;
          });
        }
      } else if ([ 'string' ].includes(type)) {
        if (sort.search(sortKey) === 0) {
          sortedItems = santizedResults.sort((a, b) => a[sortKey].localeCompare(b[sortKey]));
        } else if (sort.search(sortKey) > 0) {
          sortedItems = santizedResults.sort((a, b) => b[sortKey].localeCompare(a[sortKey]));
        }
      }
    }
    // Pagination
    const pageNumber = query.pageNumber || 1;
    const limit = query.limit;
    const lowerLimit = (pageNumber - 1) * limit;
    const upperLimit = (pageNumber * limit);
    let data = {
      items: limit ? sortedItems.slice(lowerLimit, upperLimit) : sortedItems,
      offset: (pageNumber - 1) * limit,
      limit: parseInt(limit),
      total: results.length,
    };
    return data;
  } catch (error) {
    return {
      items: results,
      offset: 0,
      limit: results.length,
      total: results.length,
    };
  };
};

module.exports = {
  TRANSFORM_RESULT,
  TRANSFORM_INPUT,
  TRUNCATE_DATABASE,
  SORT_AND_PAGINATE,
};
