const moment = require('moment');

const wrapAsync = fn => (req, res, next) => Promise.resolve(fn(req, res, next)).catch(next);

// should return one of '', 'ist', 'uat', 'nft', 'prd'
const getDeploymentEnvironment = () => {
  const spaceName = process.env.VCAP_APPLICATION && JSON.parse(process.env.VCAP_APPLICATION).space_name;
  return (spaceName || '').toLowerCase();
};

const adminEnvironmentUrl = () => {
  const environment = getDeploymentEnvironment();
  const environmentUrl = {
    'ist': 'https://pigeon-admin-ist.apps.cloud.bns',
    'uat': 'https://pigeon-admin-uat.apps.cloud.bns',
    'prd': 'https://pigeon-admin.apps.cloud.bns',
  };

  return environmentUrl[environment] || 'http://localhost:8090';
};

const getCampaignDetailsUrl = (campaignId) => `${adminEnvironmentUrl()}/campaigns/${campaignId}`;

const addVersionToPlatformTargetingData = (data, version = 1) => {
  const copiedData = { ...data };
  if (copiedData.platforms_targeting) {
    copiedData.platforms_targeting.forEach(platform => { platform.v = version; });
  }
  return copiedData;
};

const capitalize = string => `${string.charAt(0).toUpperCase()}${string.slice(1)}`;

const dateFormat = dateString => moment(dateString).format('lll');

const mapPropToKey = (data, prop) => data.reduce((o, i) => ({ ...o, [i[prop]]: i }), {});

module.exports = {
  capitalize,
  dateFormat,
  wrapAsync,
  mapPropToKey,
  getCampaignDetailsUrl,
  getDeploymentEnvironment,
  addVersionToPlatformTargetingData,
};
