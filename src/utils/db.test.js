const uuid = require('uuid');
const { SORT_AND_PAGINATE } = require('./db');

const mapObjectPropertyToArray = (data, property) => Object.values(data || {}).map(i => i[property]);

describe('Sort and paginate function test', () => {
  // array of objects to be sorted and paginated
  const dataLength = 20;
  let tmp = 0;
  const dataList = [];
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  const limit = 10;
  const compare = {
    str: [],
    bool: [],
    num: [],
  };
  while (tmp < dataLength) {
    const data = {
      str: characters.charAt(Math.floor(Math.random() * characters.length)) + uuid.v4().split('-')[1],
      bool: Math.random() > 0.5 && true,
      num: Math.ceil(Math.random() * (dataLength + 1)),
    };
    dataList.push(data);
    compare.str.push(data.str);
    compare.bool.push(data.bool);
    compare.num.push(data.num);
    tmp++;
  };
  const tester = (key) => {
    const sortList = SORT_AND_PAGINATE(dataList, { sort: key, limit });
    const mappedArray = mapObjectPropertyToArray(sortList.items, key.replace('-', ''));
    return { list: sortList, mapped: mappedArray };
  };
  it('Key not found', () => {
    expect(tester('xyz').list.items).toStrictEqual(dataList.slice(0, limit));
  });
  it('String sort test', () => {
    expect(tester('str').mapped).toStrictEqual(compare.str.sort((a, b) => a.localeCompare(b)).slice(0, limit));
    expect(tester('-str').mapped).toStrictEqual(compare.str.sort((a, b) => b.localeCompare(a)).slice(0, limit));
  });
  it('Number sort test', () => {
    expect(tester('num').mapped).toStrictEqual(compare.num.sort((a, b) => a - b).slice(0, limit));
    expect(tester('-num').mapped).toStrictEqual(compare.num.sort((a, b) => b - a).slice(0, limit));
  });
  it('Boolean sort test', () => {
    expect(tester('bool').mapped).toStrictEqual(compare.bool.sort().slice(0, limit));
    expect(tester('-bool').mapped).toStrictEqual(compare.bool.reverse().slice(0, limit));
  });
  it('Check pagination', () => {
    expect(tester('str').list.items.length).toStrictEqual(limit);
  });
});
