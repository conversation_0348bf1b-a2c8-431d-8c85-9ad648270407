const Joi = require('joi');
const semverGt = require('semver/functions/gt');
const semverCoerce = require('semver/functions/coerce');
const semverValid = require('semver/functions/valid');

// Custom validation to support a comma-separated string
const customJoi = Joi
  .extend((joi) => {
    return {
      type: 'stringArray',
      base: joi.array(),
      coerce: (value, helpers) => {
        return { value: value && value.length > 0 && value.split ? value.split(',') : value };
      }, // NOSONAR
    };
  })
  .extend((joi) => {
    return {
      type: 'osVersion',
      base: joi.array(),
      prepare: (value, helpers) => {
        const preparedValue = value.split(' - ');
        if (preparedValue.length === 1) {
          // Not in range format, drop the operators
          preparedValue[0] = preparedValue[0].replace(/(^>=)|(^<=)|(^>)|(^<)/g, '');
        }
        return { value: preparedValue };
      },
      validate: (value, helpers) => {
        let isValid = value.every(val => /^(0|[1-9]\d*)(\.(0|[1-9]\d*)){0,2}$/.test(val));
        let errMessage = null;
        if (!isValid) {
          errMessage = value.length > 1 ? 'rangeFormat' : 'versionFormat';
        } else if (value.length > 1 && !semverGt(semverCoerce(value[1]), semverCoerce(value[0]))) {
          isValid = false;
          errMessage = 'range';
        }
        return isValid ? { value: helpers.original } : { errors: helpers.error(errMessage) };
      },
      messages: {
        versionFormat: 'Invalid os version format',
        rangeFormat: 'Invalid os range format',
        range: 'Max os version must be greater than min',
      },
    };
  })
  .extend((joi) => {
    return {
      type: 'appVersion',
      base: joi.array(),
      prepare: (value, helpers) => {
        const preparedValue = value.split(' - ');
        if (preparedValue.length === 1) {
          // Not in range format, drop the operators
          preparedValue[0] = preparedValue[0].replace(/(^>=)|(^<=)|(^>)|(^<)/g, '');
        }
        return { value: preparedValue };
      },
      validate: (value, helpers) => {
        let isValid = value.every(val => semverValid(val));
        let errMessage = null;
        if (!isValid) {
          errMessage = value.length > 1 ? 'rangeFormat' : 'versionFormat';
        } else if (value.length > 1 && !semverGt(value[1], value[0])) {
          isValid = false;
          errMessage = 'range';
        }
        return isValid ? { value: helpers.original } : { errors: helpers.error(errMessage) };
      },
      messages: {
        versionFormat: 'Invalid app version format',
        rangeFormat: 'Invalid app range format',
        range: 'Max app version must be greater than min',
      },
    };
  });

module.exports = { customJoi };
