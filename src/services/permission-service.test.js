const knex = require('knex');
const mockKnex = require('mock-knex');
const PermissionService = require('./permission-service');

const mockDb = knex({ client: 'mssql', debug: false });
const tracker = mockKnex.getTracker();

describe('Permission service', () => {
  beforeAll(() => {
    mockKnex.mock(mockDb);
  });
  afterAll(() => {
    mockKnex.unmock(mockDb);
  });
  beforeEach(() => {
    tracker.install();
  });
  afterEach(() => {
    tracker.uninstall();
  });

  it('getPermissionsForUser', async() => {
    const dbResponse = [ { // before .pluck() operation
      admin_user_id: 2,
      admin_role_id: [ 1, 1, 1 ],
      created_by: [ 'sa', 'sa', 'sa', 'sa' ],
      created_ts: [ '2021-10-04T21:44:18.216Z', '2021-10-04T21:44:18.100Z', '2021-10-04T21:44:18.240Z', '2021-10-04T21:44:18.140Z' ],
      updated_by: null,
      updated_ts: null,
      admin_role_name: 'Admin',
      admin_permission_id: [ 1, 1 ],
      admin_permission_name: 'campaigns_view',
      admin_permission_description: 'campaigns_view',
    } ];
    tracker.on('query', (query, step) => {
      if (step === 1) { // select operation - returns users permissions
        query.response(dbResponse);
      }
    });
    const response = await PermissionService(mockDb).getPermissionsForUser(1);
    expect(response).toStrictEqual([ 'campaigns_view' ]);
  });

  // TODOD
  // it('getAccessForUser', async() => {
  // });
});
