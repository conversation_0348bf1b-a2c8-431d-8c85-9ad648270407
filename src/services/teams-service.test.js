const mockKnex = require('mock-knex');

const TeamsService = require('./teams-service');
const {
  FULL_PERMISSION_LIST,
} = require('../permissions/permission-constants');
const { mockDbClient } = require('../db/index.test');
const { BadRequestError } = require('../error');
const { COL_NAME } = require('../constants/db');

const dbInst = require('knex')({ client: 'mssql' });
const dbClient = mockDbClient(dbInst);
const tracker = mockKnex.getTracker();
const baseTeamFields = {
  id: 1,
  name: 'Pigeon Team',
  description: 'Team added at db implementation',
  active: true,
};

const mockRuleSubTypeAccess = {
  application_id: 3,
  rule_type_id: 2,
  rule_sub_type_id: 1,
  access_id: 2,
  team_id: baseTeamFields.id,
};

const mockPageAccess = {
  application_id: 3,
  rule_type_id: 2,
  page_id: 1,
  access_id: 2,
  team_id: baseTeamFields.id,
};

const mockContainerAccess = {
  application_id: 3,
  rule_type_id: 2,
  container_id: 1,
  access_id: 2,
  team_id: baseTeamFields.id,
};

const userService = {
  createUser: jest.fn(() => ({ id: 1, name: 'testNewOwner1' })),
  createUsers: jest.fn(() => []),
};

const getTeam = (query, step, startStep) => {
  if (step === startStep) { // select operation - returns team info
    query.response([
      {
        ...baseTeamFields,
        admin_role_id: 3,
        admin_permission_name: 'teams_view',
      },
      {
        ...baseTeamFields,
        admin_role_id: 3,
        admin_permission_name: 'teams_manage',
      },
    ]);
  } else if (step === (startStep + 1)) { // select operation - returns owner role ids (all teams)
    query.response([
      { id: 3 },
      { id: 4 },
    ]);
  } else if (step === (startStep + 2)) { // _getTeamAccessByTeamId - get access levels
    query.response([
      { admin_access_id: 1, admin_access_name: 'view' },
      { admin_access_id: 2, admin_access_name: 'manage' },
    ]);
  } else if (step === (startStep + 3)) { // _getTeamAccessByTeamId - get container access
    query.response([ mockContainerAccess ]);
  } else if (step === (startStep + 4)) { // _getTeamAccessByTeamId - get page access
    query.response([ mockPageAccess ]);
  } else if (step === (startStep + 5)) { // _getTeamAccessByTeamId - get rule sub type access
    query.response([ mockRuleSubTypeAccess ]);
  }
};

const getTeamResponse = [
  {
    ...baseTeamFields,
    permissions: [ 'teams_view', 'teams_manage' ],
    ownerRoleId: 3,
    accessApplicationIds: [ 3 ],
    access: {
      containers: [ { ...mockContainerAccess, access_id: undefined, access: 'manage' } ],
      ruleSubTypes: [ { ...mockRuleSubTypeAccess, access_id: undefined, access: 'manage' } ],
      pages: [ { ...mockPageAccess, access_id: undefined, access: 'manage' } ],
    },
  },
];

describe('Teams service', () => {
  beforeAll(() => {
    mockKnex.mock(dbInst);
  });
  afterAll(() => {
    mockKnex.unmock(dbInst);
  });
  beforeEach(() => {
    tracker.install();
  });
  afterEach(() => {
    tracker.uninstall();
  });

  it('getTeams - no flag', async() => {
    tracker.on('query', (query, step) => {
      getTeam(query, step, 1);
    });
    const response = await TeamsService({ dbClient }).getTeams({ limit: 30, pageNumber: 1 });
    const expected = { items: getTeamResponse, total: 1, limit: 30, offset: 0 };
    expect(response).toEqual(expected);
  });

  it('getTeams - skipAll', async() => {
    tracker.on('query', (query, step) => {
      getTeam(query, step, 1);
    });
    const item = { ...getTeamResponse[0], admin_permission_name: 'teams_view' };
    delete item.access;
    delete item.permissions;
    delete item.ownerRoleId;
    delete item.accessApplicationIds;
    const response = await TeamsService({ dbClient }).getTeams({ flag: 'skipAll', limit: 30, pageNumber: 1 });
    const expected = { items: [ item ], total: 1, limit: 30, offset: 0 };
    expect(response).toEqual(expected);
  });

  it('getTeams - includeFunctionalities', async() => {
    tracker.on('query', (query, step) => {
      getTeam(query, step, 1);
    });
    const item = { ...getTeamResponse[0], functionalities: [ 2 ] };
    delete item.access;
    const response = await TeamsService({ dbClient }).getTeams({ flag: 'includeFunctionalities', limit: 30, pageNumber: 1 });
    const expected = { items: [ item ], total: 1, limit: 30, offset: 0 };
    expect(response).toEqual(expected);
  });

  it('getTeam - team not found', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // select operation - get team by id
        query.response([]);
      }
    });
    const response = await TeamsService({ dbClient }).getTeam(2);
    expect(response).toEqual(null);
  });

  it('createTeam', async() => {
    const newTeam = {
      ...baseTeamFields,
      access: {
        containers: [
          { teamId: 1, rule_type_id: 2, application_id: 3, container_id: 1, access: 'manage' },
        ],
        pages: [
          { teamId: 1, rule_type_id: 2, application_id: 3, page_id: 1, access: 'manage' },
        ],
        ruleSubTypes: [
          { teamId: 1, rule_type_id: 2, application_id: 3, rule_sub_type_id: 1, access: 'manage' },
        ],
      },
      owners: [ { id: 1, name: 'First last', email: '<EMAIL>', sid: 's1234567' } ],
      permissions: [ 'teams_view', 'teams_manage' ],
    };
    tracker.on('query', (query, step) => {
      step < 19 && query.response([ // create team transaction
        [ { id: 1 } ], // _getUserIds
        [ // get rule types
          { admin_rule_type_id: 1, admin_rule_type_name: 'alert' },
          { admin_rule_type_id: 2, admin_rule_type_name: 'campaign' },
        ],
        [ // get access levels
          { access_id: 1, access_name: 'view' },
          { access_id: 2, access_name: 'manage' },
        ],
        [], // search for duplicate team name
        [ { [COL_NAME.team.id]: 1 } ], // create new team, returns new team id
        undefined, undefined, undefined, // _insertAccess - insert pages, containers, rule sub types
        undefined, // _insertTeamsPermissions - insert 2 permissions
        [ 3 ], // insert operation - create team owner role, returns viewer role id
        undefined, // _insertRolesPermissions - insert 2 permissions to owner role
        1, // delete operation - delete user's roles from previous team
        undefined, // insert operation - link owner role to users
        [ 4 ], // insert operation - create viewer role, returns viewer role id
        undefined, // insert operation - link viewer role to users
        undefined, // _insertRolesPermissions - insert 2 permissions to viewer role
        undefined, // commit transaction
      ][step - 2]);
      step >= 19 && getTeam(query, step, 19); // return newly created team
    });
    const response = await TeamsService({ dbClient, userService }).createTeam(newTeam);
    expect(response).toStrictEqual(getTeamResponse[0]);
  });

  it('updateTeam - success', async() => {
    const updatedTeam = {
      ...baseTeamFields,
      access: {
        containers: [ // removing team access to app-3 placements, and adding app-1
          { teamId: 1, rule_type_id: 2, application_id: 9, container_id: 1, access: 'manage' },
        ],
        pages: [
          { teamId: 1, rule_type_id: 2, application_id: 9, page_id: 1, access: 'manage' },
        ],
        ruleSubTypes: [
          { teamId: 1, rule_type_id: 1, application_id: 9, rule_sub_type_id: 1, access: 'manage' },
        ],
      },
      owners: [ { id: 2, name: 'New owner', email: '<EMAIL>', sid: 's1111111' } ],
      permissions: FULL_PERMISSION_LIST,
    };
    tracker.on('query', (query, step) => {
      step < 35 && query.response([ // update team queries
        [ // getTeam - select operation - returns team info
          { ...baseTeamFields, admin_role_id: 3, admin_permission_name: 'teams_view' },
          { ...baseTeamFields, admin_role_id: 3, admin_permission_name: 'teams_manage' },
        ], [ // resolvedFetchPromises - get rule types
          { admin_rule_type_id: 1, admin_rule_type_name: 'alert' },
          { admin_rule_type_id: 2, admin_rule_type_name: 'campaign' },
        ], [ // resolvedFetchPromises - get access levels
          { admin_access_id: 1, admin_access_name: 'view' },
          { admin_access_id: 2, admin_access_name: 'manage' },
        ], [ { name: 'Team owner 1', id: 3 } ], // select operation - get id of owner role
        [ { name: 'Viewer 1', id: 4 } ], // select operation - get id of viewer role
        [ { id: 1, name: 'Pigeon Team' } ], // check for duplicate team name
        [ { id: 3 }, { id: 4 } ], // select operation - returns owner role ids (all teams)
        [ // _getTeamAccessByTeamId - get access levels
          { admin_access_id: 1, admin_access_name: 'view' },
          { admin_access_id: 2, admin_access_name: 'manage' },
        ], [ mockContainerAccess ], // _getTeamAccessByTeamId - get container access
        [ mockPageAccess ], // _getTeamAccessByTeamId - get page access
        [ mockRuleSubTypeAccess ], // _getTeamAccessByTeamId - get rule sub type access
        undefined, // update operation - update team name & description
        1, 1, 1, // _deleteAccess - delete team's pages, container & rule sub type access
        ...Array(3).fill(undefined), // _insertAccess - insert pages, containers, rule sub types
        [ // delete operation - delete roles_permissions the team no longer has, return teamRolesWithDeletedPermissions
          { [COL_NAME.roles_permissions.role_id]: 3 },
          { [COL_NAME.roles_permissions.role_id]: 4 },
        ],
        // delete operation - delete teams_permissions the team no longer has
        // _insertTeamsPermissions
        // _insertRolesPermissions - owner
        // _insertRolesPermissions - viewer
        ...Array(4).fill(undefined),
        [ { admin_role_id: 4 } ], // select operation - get team roles that still have permissions, return teamRoles
        [ { admin_role_id: undefined } ], // select operation - get team roles that still have permissions, return teamRoles
        [], // select operation - get placeholder permissions, return placeholderPermission
        // insert operation - insert teams_permissions placeholder permission
        // insert operation - insert roles_permissions placeholder permission for role 3
        undefined, undefined,
        [ // update owner list - select - get user and role details for new owners list
          { id: 1, role_id: 3 }, // team currently has a sole owner
          { id: 2, role_id: 4 }, // new owner currently belongs to a different team
        ], [ { admin_role_id: 4 } ], // update owner list - select - get invalid role id's of added owners (from previous team)
        1, // update owner list - delete - remove added owner's previous team roles
        // update owner list - insert - add viewer role to added owners
        // update owner list - insert - add team owner role to added owners
        undefined, undefined,
        1, // update owner list - delete - remove added owner role from removed owners
      ][step - 1]);
      step >= 35 && getTeam(query, step, 35); // get updated team
    });

    const response = await TeamsService({ dbClient, userService }).updateTeam(1, updatedTeam);
    expect(response).toStrictEqual(getTeamResponse[0]);
  });

  it('updateTeam - reject if attempting to remove permissions from Pigeon', async() => {
    const updatedTeam = {
      ...baseTeamFields,
      access: {
        containers: [
          { teamId: 1, rule_type_id: 2, application_id: 3, container_id: 1, access: 'manage' },
        ],
        pages: [
          { teamId: 1, rule_type_id: 2, application_id: 3, page_id: 1, access: 'manage' },
        ],
        ruleSubTypes: [
          { teamId: 1, rule_type_id: 2, application_id: 3, rule_sub_type_id: 1, access: 'manage' },
        ],
      },
      owners: [ { email: '<EMAIL>', sid: 's1111111', name: 'New owner' } ],
      permissions: [ 'teams_view', 'teams_manage', 'ccau_campaigns_view', 'ccau_campaigns_manage', 'ccau_campaigns_review', 'ccau_campaigns_approve' ],
    };
    tracker.on('query', (query, step) => {
      step < 10 && query.response([ // update team queries
        [ // getTeam - select operation - returns team info
          { ...baseTeamFields, admin_role_id: 3, admin_permission_name: 'teams_view' },
          { ...baseTeamFields, admin_role_id: 3, admin_permission_name: 'teams_manage' },
        ], [ // resolvedFetchPromises - get rule types
          { admin_rule_type_id: 1, admin_rule_type_name: 'alert' },
          { admin_rule_type_id: 2, admin_rule_type_name: 'campaign' },
        ], [ // resolvedFetchPromises - get access levels
          { admin_access_id: 1, admin_access_name: 'view' },
          { admin_access_id: 2, admin_access_name: 'manage' },
        ], [ { name: 'Team owner 1', id: 3 } ], // resolvedFetchPromises - get id of owner role
        [ { name: 'Viewer 1', id: 4 } ], // resolvedFetchPromises - get id of viewer role
        [], // check for duplicate team name
        [ { id: 3 }, { id: 4 } ], // getTeam - select operation - returns owner role ids (all teams)
        [ // getTeam - select operation - returns team owners
          { id: 1, name: 'First last', email: '<EMAIL>', sid: 's1234567' },
        ], [ // getTeam - _getTeamAccessByTeamId - get container access
          { application_id: 3, rule_type_id: 2, container_id: 1, access_id: 2 },
        ], [ // getTeam - _getTeamAccessByTeamId - get page access
          { application_id: 3, rule_type_id: 2, page_id: 1, access_id: 2 },
        ], [ // getTeam -  _getTeamAccessByTeamId - get rule sub type access
          { application_id: 3, rule_type_id: 2, rule_sub_type_id: 1, access_id: 2 },
        ],
      ][step - 1]);
      step >= 10 && getTeam(query, step, 10); // get updated team
    });

    try {
      await TeamsService({ dbClient }).updateTeam(1, updatedTeam);
    } catch (err) {
      expect(err).toStrictEqual(new Error(`Permissions cannot be removed from the Pigeon team - alerts_view`));
    }
  });

  it('updateTeam - reject if attempting to deactivate Pigeon', async() => {
    const updatedTeam = {
      ...baseTeamFields,
      access: {
        containers: [
          { teamId: 1, rule_type_id: 2, application_id: 3, container_id: 1, access: 'manage' },
        ],
        pages: [
          { teamId: 1, rule_type_id: 2, application_id: 3, page_id: 1, access: 'manage' },
        ],
        ruleSubTypes: [
          { teamId: 1, rule_type_id: 2, application_id: 3, rule_sub_type_id: 1, access: 'manage' },
        ],
      },
      owners: [ { email: '<EMAIL>', sid: 's1111111', name: 'New owner' } ],
      permissions: FULL_PERMISSION_LIST,
      active: false,
    };
    tracker.on('query', (query, step) => {
      query.response([ // update team queries
        [ // getTeam - select operation - returns team info
          { ...baseTeamFields, admin_role_id: 3, admin_permission_name: 'teams_view' },
          { ...baseTeamFields, admin_role_id: 3, admin_permission_name: 'teams_manage' },
        ], [ // resolvedFetchPromises - get rule types
          { admin_rule_type_id: 1, admin_rule_type_name: 'alert' },
          { admin_rule_type_id: 2, admin_rule_type_name: 'campaign' },
        ], [ // resolvedFetchPromises - get access levels
          { admin_access_id: 1, admin_access_name: 'view' },
          { admin_access_id: 2, admin_access_name: 'manage' },
        ], [ { name: 'Team owner 1', id: 3 } ], // resolvedFetchPromises - get id of owner role
        [ { name: 'Viewer 1', id: 4 } ], // resolvedFetchPromises - get id of viewer role
        [], // check for duplicate team name
        [ { id: 3 }, { id: 4 } ], // getTeam - select operation - returns owner role ids (all teams)
        [ // getTeam - select operation - returns team owners
          { id: 1, name: 'First last', email: '<EMAIL>', sid: 's1234567' },
        ], [ // getTeam - _getTeamAccessByTeamId - get access levels
          { admin_access_id: 1, admin_access_name: 'view' },
          { admin_access_id: 2, admin_access_name: 'manage' },
        ], [ // getTeam - _getTeamAccessByTeamId - get rule types
          { admin_rule_type_id: 1, admin_rule_type_name: 'alert' },
          { admin_rule_type_id: 2, admin_rule_type_name: 'campaign' },
        ], [ // getTeam - _getTeamAccessByTeamId - get container access
          { application_id: 3, rule_type_id: 2, container_id: 1, access_id: 2 },
        ], [ // getTeam - _getTeamAccessByTeamId - get page access
          { application_id: 3, rule_type_id: 2, page_id: 1, access_id: 2 },
        ], [ // getTeam - _getTeamAccessByTeamId - get rule sub type access
          { application_id: 3, rule_type_id: 2, rule_sub_type_id: 1, access_id: 2 },
        ],
      ][step - 1]);
    });

    try {
      await TeamsService({ dbClient }).updateTeam(1, updatedTeam);
    } catch (err) {
      expect(err).toStrictEqual(new Error(`Cannot deactivate Pigeon team`));
    }
  });

  it('updateTaem - reject if duplicate name', async() => {
    const updatedTeam = {
      ...baseTeamFields,
      name: 'Duplicate team name',
      permissions: FULL_PERMISSION_LIST,
    };
    tracker.on('query', (query, step) => {
      step < 35 && query.response([ // update team queries
        [ // getTeam - select operation - returns team info
          { ...baseTeamFields, admin_role_id: 3, admin_permission_name: 'teams_view' },
          { ...baseTeamFields, admin_role_id: 3, admin_permission_name: 'teams_manage' },
        ], [ // resolvedFetchPromises - get rule types
          { admin_rule_type_id: 1, admin_rule_type_name: 'alert' },
          { admin_rule_type_id: 2, admin_rule_type_name: 'campaign' },
        ], [ // resolvedFetchPromises - get access levels
          { admin_access_id: 1, admin_access_name: 'view' },
          { admin_access_id: 2, admin_access_name: 'manage' },
        ], [ { name: 'Team owner 1', id: 3 } ], // select operation - get id of owner role
        [ { name: 'Viewer 1', id: 4 } ], // select operation - get id of viewer role
        [ { id: 2, name: 'Duplicate team name' } ], // check for duplicate team name
        [ { id: 3 }, { id: 4 } ], // select operation - returns owner role ids (all teams)
        [ // _getTeamAccessByTeamId - get access levels
          { admin_access_id: 1, admin_access_name: 'view' },
          { admin_access_id: 2, admin_access_name: 'manage' },
        ], [ mockContainerAccess ], // _getTeamAccessByTeamId - get container access
        [ mockPageAccess ], // _getTeamAccessByTeamId - get page access
        [ mockRuleSubTypeAccess ], // _getTeamAccessByTeamId - get rule sub type access
      ][step - 1]);
    });
    try {
      await TeamsService({ dbClient, userService }).updateTeam(1, updatedTeam);
      throw new Error('Expected error was not thrown'); // Explicit failure
    } catch (e) {
      return expect(e).toEqual(new BadRequestError('Team name already exists'));
    }
  });

  it('setTeamStatus - deactivate', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // update operation - set team's active status
        query.response(undefined);
      } else if (step === 2) { // select operation - get team's user id's
        query.response([ { user_id: 1 }, { user_id: 2 } ]);
      } else if (step === 3) { // update operation - update team's users' status
        query.response(undefined);
      } else if (step === 4) { // select operation - get team's role id's
        query.response([ { admin_role_id: 1 }, { admin_role_id: 2 } ]);
      } else if (step === 5) { // update operation - update team's roles' status
        query.response(undefined);
      } else if (step === 6) { // update operation - assign applications to pigeon
        query.response(undefined);
      } else if (step === 7) { // delete operation - remove team's role permissions
        query.response(undefined);
      } else if (step === 8) { // delete operation - remove team's permissions
        query.response(undefined);
      } else {
        getTeam(query, step, 9);
      }
    });

    const response = await TeamsService({ dbClient }).setTeamStatusActive(2, false);
    expect(response).toStrictEqual(getTeamResponse[0]);
  });

  it('setTeamStatus - deactivate - reject if trying to deactivate Pigeon', async() => {
    try {
      await TeamsService({ dbClient }).setTeamStatusActive(1, false);
    } catch (err) {
      expect(err).toStrictEqual(new Error(`Cannot deactivate Pigeon team`));
    }
  });

  it('setTeamStatus - activate', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // update operation - set team's active status
        query.response(undefined);
      } else if (step === 2) { // select operation - get team's user id's
        query.response([ { user_id: 1 }, { user_id: 2 } ]);
      } else if (step === 3) { // update operation - update team's users' status
        query.response(undefined);
      } else if (step === 4) { // select operation - get team's role id's
        query.response([ { admin_role_id: 1 }, { admin_role_id: 2 } ]);
      } else if (step === 5) { // update operation - update team's roles' status
        query.response(undefined);
      } else {
        getTeam(query, step, 6);
      }
    });

    const response = await TeamsService({ dbClient }).setTeamStatusActive(1, true, { activateChildren: 'true' });
    expect(response).toStrictEqual(getTeamResponse[0]);
  });

  it('deleteTeam - success', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // get team's users
        query.response([ { admin_user_id: 1 }, { admin_user_id: 2 } ]);
      } else if (step === 2) { // get team's roles
        query.response([ { admin_role_id: 2 }, { admin_role_id: 3 } ]);
      } else if (step === 3) { // get Pigeon viewer role
        query.response([ { id: 1 } ]);
      } else if (step === 4) { // delete operations - delete team users roles
        query.response(undefined);
      } else if (step === 5) { // insert operation - add Pigeon viewer role to users
        query.response(undefined);
      } else if ([ 6, 7, 8, 9, 10, 11, 12 ].includes(step)) { // delete operations - roles_permissions, teams_permissions, access_containers, access_pages, access_rule_sub_types, role, team
        query.response(undefined);
      }
    });

    const response = await TeamsService({ dbClient }).deleteTeam(2);
    expect(response).toStrictEqual({});
  });

  it('deleteTeam - reject if trying to delete Pigeon', async() => {
    try {
      await TeamsService({ dbClient }).deleteTeam(1);
    } catch (err) {
      expect(err).toStrictEqual(new Error(`Cannot delete Pigeon team`));
    }
  });
  it('deleteTeam - success when  teamUserIds and teamRoleIds is empty', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // get team's users
        query.response([ ]);
      } else if (step === 2) { // get team's roles
        query.response([ ]);
      } else if (step === 3) { // get Pigeon viewer role
        query.response([ { id: 1 } ]);
      } else if (step === 4) { // delete operations - delete team users roles
        query.response(undefined);
      } else if (step === 5) { // insert operation - add Pigeon viewer role to users
        query.response(undefined);
      } else if ([ 6, 7, 8, 9, 10, 11, 12 ].includes(step)) { // delete operations - roles_permissions, teams_permissions, access_containers, access_pages, access_rule_sub_types, role, team
        query.response(undefined);
      }
    });

    const response = await TeamsService({ dbClient }).deleteTeam(2);
    expect(response).toStrictEqual({});
  });

  it('should update successful when access does not change', async() => {
    const sameContainerAccess = { team_id: 1, rule_type_id: 1, application_id: 1, container_id: 1, access: 'manage', access_id: 2 };
    const samePageAccess = { team_id: 1, rule_type_id: 2, application_id: 9, page_id: 1, access: 'manage', access_id: 2 };
    const sameRuleSubType = { team_id: 1, rule_type_id: 2, application_id: 9, rule_sub_type_id: 1, access: 'manage', access_id: 2 };

    const getTeamResponseLocal = [
      {
        ...baseTeamFields,
        permissions: [ 'teams_view', 'teams_manage' ],
        ownerRoleId: 3,
        accessApplicationIds: [ 1, 9 ],
        access: {
          containers: [ { ...sameContainerAccess, access_id: undefined, access: 'manage' } ],
          ruleSubTypes: [ { ...sameRuleSubType, access_id: undefined, access: 'manage' } ],
          pages: [ { ...samePageAccess, access_id: undefined, access: 'manage' } ],
        },
      },
    ];

    const updatedTeam = {
      ...baseTeamFields,
      access: {
        containers: [ // removing team access to app-3 placements, and adding app-1
          { ...sameContainerAccess },
        ],
        pages: [
          { ...samePageAccess },
        ],
        ruleSubTypes: [
          { ...sameRuleSubType },
        ],
      },
      owners: [ { id: 2, name: 'New owner', email: '<EMAIL>', sid: 's1111111' } ],
      permissions: FULL_PERMISSION_LIST,
    };
    tracker.on('query', (query, step) => {
      query.response([ // update team queries
        [ // getTeam - select operation - returns team info
          { ...baseTeamFields, admin_role_id: 3, admin_permission_name: 'teams_view', access: updatedTeam.access },
          { ...baseTeamFields, name: 'asdf', admin_role_id: 3, admin_permission_name: 'teams_manage' },
        ], [ // resolvedFetchPromises - get rule types
          { admin_rule_type_id: 1, admin_rule_type_name: 'alert' },
          { admin_rule_type_id: 2, admin_rule_type_name: 'campaign' },
        ], [ // resolvedFetchPromises - get access levels
          { admin_access_id: 1, admin_access_name: 'view' },
          { admin_access_id: 2, admin_access_name: 'manage' },
        ], [ { name: 'Team owner 1', id: 3 } ], // select operation - get id of owner role
        [ { name: 'Viewer 1', id: 4 } ], // select operation - get id of viewer role
        [ { id: 1, name: 'Pigeon Team' } ], // check for duplicate team name
        [ { id: 3 }, { id: 4 } ], // select operation - returns owner role ids (all teams)
        [ // _getTeamAccessByTeamId - get access levels
          { admin_access_id: 1, admin_access_name: 'view' },
          { admin_access_id: 2, admin_access_name: 'manage' },
        ], [ { ...sameContainerAccess } ], // _getTeamAccessByTeamId - get container access
        [ { ...samePageAccess } ], // _getTeamAccessByTeamId - get page access
        [ { ...sameRuleSubType } ], // _getTeamAccessByTeamId - get rule sub type access
        undefined, // update operation - update team name & description
        [ ], // rolesWithDeletedPermissions
        ...Array(4).fill(undefined), // Delete permissions, _insertTeamsPermissions, _insertRolesPermissions ,_insertRolesPermissions
        [ // ownersInDbSql
          { [COL_NAME.roles_permissions.role_id]: 3 },
          { [COL_NAME.roles_permissions.role_id]: 4 },
        ],
      ][step - 1]);
      step >= 19 && getTeamLocal(query, step, 19); // get updated team
    });

    const getTeamLocal = (query, step, startStep) => {
      if (step === startStep) { // select operation - returns team info
        query.response([
          {
            ...baseTeamFields,
            admin_role_id: 3,
            admin_permission_name: 'teams_view',
          },
          {
            ...baseTeamFields,
            admin_role_id: 3,
            admin_permission_name: 'teams_manage',
          },
        ]);
      } else if (step === (startStep + 1)) { // select operation - returns owner role ids (all teams)
        query.response([
          { id: 3 },
          { id: 4 },
        ]);
      } else if (step === (startStep + 2)) { // _getTeamAccessByTeamId - get access levels
        query.response([
          { admin_access_id: 1, admin_access_name: 'view' },
          { admin_access_id: 2, admin_access_name: 'manage' },
        ]);
      } else if (step === (startStep + 3)) { // _getTeamAccessByTeamId - get container access
        query.response([ sameContainerAccess ]);
      } else if (step === (startStep + 4)) { // _getTeamAccessByTeamId - get page access
        query.response([ samePageAccess ]);
      } else if (step === (startStep + 5)) { // _getTeamAccessByTeamId - get rule sub type access
        query.response([ sameRuleSubType ]);
      }
    };

    const response = await TeamsService({ dbClient, userService }).updateTeam(1, updatedTeam);
    expect(response).toStrictEqual(getTeamResponseLocal[0]);
  });
});
