const knex = require('knex');
const mockKnex = require('mock-knex');

const ContainerService = require('./container-service');
const { COL_NAME } = require('../constants/db');

const mockDb = knex({ client: 'mssql', debug: false });
const tracker = mockKnex.getTracker();
const baseContainer = {
  name: 'test-name',
  containerId: 'test-id',
  application: 1,
  rule_type: 'test-rule-type',
  content_type: 'test-content-type',
  status: true,
  description: 'test-description',
};

describe('Container service', () => {
  beforeAll(() => {
    mockKnex.mock(mockDb);
  });
  afterAll(() => {
    mockKnex.unmock(mockDb);
  });
  beforeEach(() => {
    tracker.install();
  });
  afterEach(() => {
    tracker.uninstall();
  });

  it('createContainer', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // _getRuleTypeId
        query.response([ 1 ]);
      } else if (step === 2) { // insert operation - returns container id
        query.response([ { [COL_NAME.container.id]: 1 } ]);
      } else if (step === 3) { // _insertContainersPages
        query.response(undefined);
      } else if (step === 4) { // _addContainerAccessToPigeon - get access levels
        query.response([ { admin_access_id: 1 }, { admin_access_id: 2 } ]);
      } else if (step === 5) { // _addContainerAccessToPigeon - insert container access
        query.response(undefined);
      } else if (step === 6) { // getContainer
        query.response([ { ...baseContainer, id: 1, rule_type_id: 1, page_id: 1 } ]);
      }
    });
    const response = await ContainerService(mockDb).createContainer({ ...baseContainer, pages: [ 1 ] });
    expect(response).toStrictEqual({ ...baseContainer, pages: [ 1 ], id: 1 });
  });

  it('updateContainer', async() => {
    tracker.on('query', (query, step) => {
      switch (step) {
        case 1: query.response([ { ...baseContainer, id: 1, rule_type_id: 1, page_id: 1 } ]); return; // getContainer - old
        case 2: query.response([ { admin_rule_type_id: 1 } ]); return; // // update operation, returns success
        case 3: query.response(1); return; // // update container access, returns success
        case 4: query.response(1); return; // // _deleteContainersPages, returns success
        case 5: query.response(undefined); return; // // _insertContainersPages, returns success
        case 6: query.response([ { admin_access_id: 1 }, { admin_access_id: 2 } ]); return; // // _updateContainerAccessToPigeon, returns access levels
        case 7: query.response(1); return; // // _updateContainerAccessToPigeon, returns success
        case 8: query.response(1); return; // // _updateContainerAccessToPigeon, returns success
        case 9: query.response([]); return; // // _updateContainerAccessToPigeon, returns success
        case 10: query.response(1); return; // // _updateContainerAccessToPigeon, returns success

        case 11: query.response([ { ...baseContainer, id: 1, rule_type_id: 1, page_id: 1 } ]); return; // getContainer - new
        default:;
      }
    });
    const response = await ContainerService(mockDb).updateContainer(1, { ...baseContainer, pages: [ 1 ] });
    expect(response).toMatchObject({ ...baseContainer, id: 1, pages: [ 1 ] });
  });

  it('updateContainer (no return)', async() => {
    tracker.on('query', (query, step) => {
      switch (step) {
        case 1: query.response([ { ...baseContainer, id: 1, rule_type_id: 1, page_id: 1 } ]); return; // getContainer - old
        case 2: query.response([ { admin_rule_type_id: 1 } ]); return;// _getRuleTypeId
        case 3: query.response(1); return; // // update operation, returns success
        case 4: query.response(1); return; // // _deleteContainersPages, returns success
        case 5: query.response(undefined); return; // // _insertContainersPages, returns success
        case 6: query.response([ { admin_access_id: 1 }, { admin_access_id: 2 } ]); return; // // _updateContainerAccessToPigeon, returns access levels
        case 7: query.response(1); return; // // _updateContainerAccessToPigeon, returns success
        case 8: query.response(1); return; // // _updateContainerAccessToPigeon, returns success
        case 9: query.response([]); return; // // _updateContainerAccessToPigeon, returns success
        case 10: query.response(1); return; // // _updateContainerAccessToPigeon, returns success
        default :;
      }
    });
    const response = await ContainerService(mockDb).updateContainer(1, { ...baseContainer, pages: [ 1 ] }, { noReturn: true });
    expect(response).toBeUndefined();
  });

  it('deleteContainer', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // getContainer
        query.response([ { ...baseContainer, id: 1, rule_type_id: 1, page_id: 1 } ]);
      } else if ([ 2, 3, 4 ].includes(step)) { // delete operations _deleteContainersPages, container access, container
        query.response(1);
      }
    });
    const response = await ContainerService(mockDb).deleteContainer(1);
    expect(response).toStrictEqual({ ...baseContainer, id: 1, pages: [ 1 ] });
  });
});
