const knex = require('knex');
const mockKnex = require('mock-knex');
const validationService = require('./validation-service');
const { mapTableColumn } = require('../constants/dbMappingValidation');

const mockDb = knex({ client: 'mssql', debug: false });
const tracker = mockKnex.getTracker();

describe('Validation Service', () => {
  beforeAll(() => {
    mockKnex.mock(mockDb);
  });
  afterAll(() => {
    mockKnex.unmock(mockDb);
  });
  beforeEach(() => {
    tracker.install();
  });
  afterEach(() => {
    tracker.uninstall();
  });

  it('Validation check - Result found', async() => {
    tracker.on('query', (query) => {
      query.response([ { id: 1 } ]);
    });
    const key = 'userEmail';
    const queries = mapTableColumn[key];
    const values = JSON.stringify([ 's1111111' ]);
    const response = await validationService(mockDb).checkExistence(key, queries, values);
    expect(response).toStrictEqual(true);
  });

  it('Validation check - roleName', async() => {
    tracker.on('query', (query) => {
      query.response([ { id: 1 } ]);
    });
    const key = 'roleName';
    const queries = mapTableColumn[key];
    const values = JSON.stringify([ 's1111111' ]);
    const response = await validationService(mockDb).checkExistence(key, queries, values);
    expect(response).toStrictEqual(true);
  });

  it('Validation check - No result found', async() => {
    tracker.on('query', (query) => {
      query.response([]);
    });
    const key = 'userEmail';
    const queries = mapTableColumn[key];
    const values = JSON.stringify([ '<EMAIL>' ]);
    const response = await validationService(mockDb).checkExistence(key, queries, values);
    expect(response).toStrictEqual(false);
  });
});
