const { getRequestHeaders } = require('./common');

const commonReqParams = (config) => ({
  baseURL: config.url,
  headers: getRequestHeaders(),
  timeout: config.timeout,
});

const createVariableSet = ({ axios }, config, data) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v2/variable-mappings/sets`,
    method: 'post',
    data,
  })
);

const patchVariableSet = ({ axios }, config, id, data) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v2/variable-mappings/sets/${id}`,
    method: 'patch',
    data,
  })
);

const updateVariableSet = ({ axios }, config, id, data) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v2/variable-mappings/sets/${id}`,
    method: 'put',
    data,
  })
);

const init = ({ axios }, config) => {
  return {
    createVariableSet: (data) => createVariableSet({ axios }, config, data),
    updateVariableSet: (id, data) => updateVariableSet({ axios }, config, id, data),
    patchVariableSet: (id, data) => patchVariableSet({ axios }, config, id, data),
  };
};

module.exports = init;
