const VariableMappingsApiClientV2 = require('.');

// mock axios
const mockRequest = jest.fn();
const axios = {
  request: mockRequest,
};
// mock config
const config = {
  url: 'http://localhost/v1',
};

describe('Variable Mappings API client V2', () => {
  test('should have createVariableSet method', () => {
    const client = VariableMappingsApiClientV2({ axios }, config);
    expect(client).toHaveProperty('createVariableSet');
    expect(typeof client.createVariableSet).toEqual('function');
  });

  test('should have patchVariableSet method', () => {
    const client = VariableMappingsApiClientV2({ axios }, config);
    expect(client).toHaveProperty('patchVariableSet');
    expect(typeof client.patchVariableSet).toEqual('function');
  });

  test('should have updateVariableSet method', () => {
    const client = VariableMappingsApiClientV2({ axios }, config);
    expect(client).toHaveProperty('updateVariableSet');
    expect(typeof client.updateVariableSet).toEqual('function');
  });

  test('should successfully call createVariableSet', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };
    const mockData = {};
    mockRequest.mockReturnValueOnce(fakeResponse);
    const client = VariableMappingsApiClientV2({ axios }, config);
    const result = await client.createVariableSet(mockData);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual('/v2/variable-mappings/sets');
    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call patchVariableSet', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };
    const mockId = 434142;
    const mockData = {};
    mockRequest.mockReturnValueOnce(fakeResponse);
    const client = VariableMappingsApiClientV2({ axios }, config);
    const result = await client.patchVariableSet(mockId, mockData);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v2/variable-mappings/sets/${mockId}`);
    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call updateVariableSet', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };
    const mockId = 434142;
    const mockData = {};
    mockRequest.mockReturnValueOnce(fakeResponse);
    const client = VariableMappingsApiClientV2({ axios }, config);
    const result = await client.updateVariableSet(mockId, mockData);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(result).toEqual(fakeResponse);
  });
});
