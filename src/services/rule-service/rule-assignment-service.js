/* istanbul ignore next */
const { CustomError } = require('../../error');

const {
  resolvePermission,
  CAMPAIGNS_REVIEW,
  CAMPAIGNS_APPROVE,
  CCAU_CAMPAIGNS_REVIEW,
  CCAU_CAMPAIGNS_APPROVE,
  OFFERS_APPROVE,
  OFFERS_REVIEW,
} = require('../../permissions');

const { OFFERS_STATUS } = require('../../offers/routes/constants');

let _dbService;
let _logger;
let _permissionService;
let _userService;
let _campaignService;

async function assignUserListToCampaign(users, campaignId) {
  // detect duplicate assignees
  const duplicates = _findDuplicateAssignee(users);

  if (duplicates.length > 0) {
    throw new CustomError(
      500,
      'Unable to assign users. Duplicate assignee found',
      'Unable to assign users. Duplicate assignee found',
    );
  }

  try {
    const result = await _findUsers(users);

    /* isstanbul ignore else */
    if (result === undefined || result[0] === undefined) {
      throw new CustomError(500, `Unable to find users: ${users}`, 'Unable to find users');
    }

    const sidMap = result.map(function(item) {
      return item.sid;
    });

    const diff = users.filter(item => { return sidMap.indexOf(item) < 0; });
    if (diff.length > 0) {
      throw new CustomError(500, `Unable to find user: ${diff}`, 'Unable to find user');
    }

    const insertValues = result.map(function(item) {
      return {
        assigned_admin_user_id: item.admin_user_id,
        campaign_rule_id: campaignId,
      };
    });

    const insertQuery = _dbService('admin_user_campaign_rules')
      .returning([ 'assigned_admin_user_id', 'campaign_rule_id' ])
      .insert(insertValues)
      .on('query-error', (error, obj) => _handleQueryError(error, obj));

    insertValues.length && await insertQuery;
    const assignmentResult = await getAssigneesForCampaign(campaignId);
    return assignmentResult;
  } catch (e) {
    _logger.error(e);
    throw e;
  }
}

async function assignUserToCampaign(userSid, campaignId) {
  const result = await _findUser(userSid);

  if (result === undefined || result.length <= 0) {
    // throw exceptions - unable to find user
    throw new CustomError(500, `Unable to find user with sid: ${userSid} for campaign:${campaignId}`, `Unable to find user with sid: ${userSid} for campaign:${campaignId}`);
  } else {
    const insertQuery = _dbService('admin_user_campaign_rules')
      .returning([ 'assigned_admin_user_id', 'campaign_rule_id' ])
      .insert({ assigned_admin_user_id: result.id, campaign_rule_id: campaignId })
      .on('query-error', (error, obj) => _handleQueryError(error, obj));

    await insertQuery;
    const assignmentResult = await getAssigneesForCampaign(campaignId);
    return assignmentResult;
  }
}

async function removeUsersFromCampaign(campaignId) {
  const result = await _dbService('admin_user_campaign_rules')
    .where('campaign_rule_id', campaignId)
    .del()
    .on('query-error', (error, obj) => _handleQueryError(error, obj));

  return result;
}

async function validateUsers(users, permission) {
  const invalidUsers = [];
  for (let i = 0; i < users.length; i++) {
    // make sure the user exists and is active
    const userResult = await _userService.getUser({ sid: users[i] });
    if (userResult === undefined || userResult.length === 0) {
      invalidUsers.push({
        'sid': users[i],
        'message': 'User not found',
      });
    } else {
      const user = userResult[0];
      // ensure that the user has sufficient permissions
      // TODO: update error handling - what happens if this request / lookup fails?
      const permissions = await _permissionService.getPermissionsForUser(user.id);
      if (permissions) {
        let userPermissionAvailable;
        if (Array.isArray(permission)) {
          const permissionResult = [];
          let j;
          for (j = 0; j < permission.length; j++) {
            permissionResult.push(resolvePermission({ permissions }, permission[j]));
          }
          userPermissionAvailable = !permissionResult.some(function(item) {
            return item === false;
          });
        } else {
          userPermissionAvailable = resolvePermission({ permissions }, permission);
        }

        if (!userPermissionAvailable) {
          invalidUsers.push({
            'sid': users[i],
            'message': 'Insufficent permissions',
          });
        }
      } else {
        invalidUsers.push({
          'sid': users[i],
          'message': 'Insufficent permissions',
        });
      }
    }
  }

  if (invalidUsers.length > 0) {
    return {
      success: false,
      error: {
        message: 'Invalid users',
        details: invalidUsers,
      },
    };
  } else {
    return { success: true };
  }
}

/**
 * Get approvers for variable mapping
 *
 * @param {*} teamId - optional team id for filtering results to a specific team
 * @returns list of approvers
 */
async function getApproversForVariableMapping(teamId) {
  const sql = _dbService('admin_user')
    .select('full_name', 'sid')
    .innerJoin('admin_users_roles', 'admin_user.admin_user_id', 'admin_users_roles.admin_user_id')
    .innerJoin('admin_roles_permissions', 'admin_users_roles.admin_role_id', 'admin_roles_permissions.admin_role_id')
    .where('admin_roles_permissions.admin_permission_name', 'pega_variable_mapping_approve')
    .andWhere('admin_user.active_indicator', 1);
  teamId && sql.andWhere('admin_roles_permissions.admin_team_id', teamId);
  sql
    .groupBy('full_name', 'sid')
    .orderBy('admin_user.full_name')
    .on('query-error', (error, obj) => _handleQueryError(error, obj));

  const result = await sql;
  return result.length === 0 ? [] : result;
}

async function getPotentialAssigneesForCampaign(campaignId, teamId) {
  let requiredPermissions;
  let result;

  if (!campaignId) {
    requiredPermissions = [ CAMPAIGNS_REVIEW, CCAU_CAMPAIGNS_REVIEW ];
  } else {
  // get campaign details for campaign id
    const campaignDetails = await _campaignService.getCampaign(campaignId);
    const campaignData = campaignDetails.data;

    if (campaignData.status === 'published' || campaignData.status === 'reviewed') {
      return {}; // no assignees can be attached to this campaign
    }

    if (campaignData.status === 'draft') {
      requiredPermissions = [ CAMPAIGNS_REVIEW, CCAU_CAMPAIGNS_REVIEW ];
    } else if (campaignData.status === 'submitted') {
      requiredPermissions = [ CAMPAIGNS_APPROVE, CCAU_CAMPAIGNS_APPROVE ];
    }
  }

  // In addition to the required permission level, the assignee would also need access to the container/page/rule sub type
  result = await _getPotentialAssigneesForCampaign(requiredPermissions, teamId);
  return result;
}

// New service for Windmill offer management
async function getPotentialAssigneesForOfferCampaign(offerStatus, userTeamId) {
  try {
    let requiredPermissions;
    let result;

    if (!offerStatus) {
      requiredPermissions = [ CAMPAIGNS_REVIEW, CCAU_CAMPAIGNS_REVIEW ];
    } else {
      // Cannot assign users to this campaign
      if (offerStatus === OFFERS_STATUS.INACTIVE || offerStatus === OFFERS_STATUS.ACTIVE || offerStatus === OFFERS_STATUS.REVIEWED || offerStatus === OFFERS_STATUS.EXPIRED) return {};

      if (offerStatus === OFFERS_STATUS.DRAFT) {
        requiredPermissions = [ OFFERS_REVIEW ];
      } else if (offerStatus === OFFERS_STATUS.SUBMITTED) {
        requiredPermissions = [ OFFERS_APPROVE ];
      }
    }
    // In addition to the required permission level, the assignee would also need access to the container/page/rule sub type
    result = await _getPotentialAssigneesForCampaign(requiredPermissions, userTeamId);
    return result;
  } catch (err) {
    _logger.error(err);
    throw err;
  }
}

async function getAssigneesForCampaign(campaignId) {
  const result = await _dbService('admin_user_campaign_rules')
    .select('admin_user.full_name', 'admin_user.sid')
    .innerJoin('admin_user', 'admin_user_campaign_rules.assigned_admin_user_id', '=', 'admin_user.admin_user_id')
    .where('admin_user_campaign_rules.campaign_rule_id', '=', campaignId)
    .on('query-error', (error, obj) => _handleQueryError(error, obj));

  if (result.length === 0) {
    return [];
  } else {
    return result;
  }
}

// private functions
async function _getPotentialAssigneesForCampaign(requiredPermissions, teamId) {
  const result = await _dbService('admin_user')
    .select('full_name', 'sid')
    .innerJoin('admin_users_roles', 'admin_user.admin_user_id', 'admin_users_roles.admin_user_id')
    .innerJoin('admin_roles_permissions', 'admin_users_roles.admin_role_id', 'admin_roles_permissions.admin_role_id')
    .whereIn('admin_permission_name', requiredPermissions)// list required permissions
    .andWhere('admin_user.active_indicator', 1)
    .groupBy('full_name', 'sid')
    .where('admin_roles_permissions.admin_team_id', teamId)
    .on('query-error', (error, obj) => _handleQueryError(error, obj));

  if (result.length === 0) {
    return [];
  } else {
    return result;
  }
}

async function _findUser(userSid) {
  const result = (await _userService.getUser({ sid: userSid }))[0];
  return result;
}

async function _findUsers(userSidList) {
  // TODO: update this function to use methods from _userService
  return _dbService('admin_user')
    .select([ 'admin_user_id', { user_name: 'full_name' }, 'sid' ])
    .whereIn('sid', userSidList)
    .andWhere('active_indicator', 1)
    .on('query-error', (error, obj) => _handleQueryError(error, obj));
}

function _handleQueryError(error, obj) {
  _logger.error(error);
}

function _findDuplicateAssignee(assigneeList) {
  const duplicateMap = {};
  const duplicates = [];

  assigneeList.forEach(item => {
    if (!duplicateMap[item]) {
      duplicateMap[item] = 0;
    }

    duplicateMap[item] += 1;
  });

  for (const prop in duplicateMap) {
    if (duplicateMap[prop] >= 2) {
      duplicates.push(prop);
    }
  }

  return duplicates;
}

const init = function({ dbService, logger, userService, permissionService, campaignService }) {
  _dbService = dbService;
  _logger = logger;
  _permissionService = permissionService;
  _userService = userService;
  _campaignService = campaignService;

  return {
    // publicly accessible functions here
    assignUserToCampaign,
    assignUserListToCampaign,
    removeUsersFromCampaign,
    validateUsers,
    getPotentialAssigneesForCampaign,
    getAssigneesForCampaign,
    getApproversForVariableMapping,
    getPotentialAssigneesForOfferCampaign,
  };
};

module.exports = init;
