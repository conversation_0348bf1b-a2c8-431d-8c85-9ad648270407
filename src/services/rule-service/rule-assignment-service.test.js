const knex = require('knex');
const mockKnex = require('mock-knex');
const tracker = require('mock-knex').getTracker();
const R = require('ramda');

const { CustomError } = require('../../error');
const { TABLE, COL_NAME } = require('../../constants/db');
const { TRUNCATE_DATABASE, TRANSFORM_INPUT } = require('../../utils/db');
const RuleAssignmentService = require('./rule-assignment-service');
const {
  PERMISSION_CAMPAIGNS_STATUS_DRAFT_SUBMITTED,
} = require('../../permissions');

describe('rule-assignment-service', () => {
  const dbService = knex({ client: 'mssql', debug: false });
  const logger = { error: jest.fn() };

  beforeAll((done) => {
    mockKnex.mock(dbService);
    done();
  });

  afterAll((done) => {
    mockKnex.unmock(dbService);
    done();
  });

  beforeEach((done) => {
    tracker.install();
    done();
  });

  afterEach((done) => {
    tracker.uninstall();
    done();
  });

  it('assignUserToCampaign - should throw error when user not found on assignment', (done) => {
    mockUserService.getUser.mockReturnValueOnce([]);
    RuleAssignmentService({ userService: mockUserService }).assignUserToCampaign('abc123', 'abc123')
      .catch(e => {
        expect(e.message).toBe('Unable to find user with sid: abc123 for campaign:abc123');
        done();
      });
  });

  it('assignUserToCampaign - Should return assignment result for valid user campaign assignment', async() => {
    mockUserService.getUser.mockReturnValueOnce([ R.pick([ 'id', 'sid' ], mockUserWithPermissions) ]);
    const expected = { assigned_admin_user_id: mockUserWithPermissions.id, campaign_rule_id: 'XYZ123' };
    tracker.on('query', (query, step) => {
      step === 1 &&
        query.sql === 'insert into [admin_user_campaign_rules] ([assigned_admin_user_id], [campaign_rule_id]) output inserted.[assigned_admin_user_id], inserted.[campaign_rule_id] values (@p0, @p1)' &&
        query.response();
      step === 2 &&
        query.sql === 'select [admin_user].[full_name], [admin_user].[sid] from [admin_user_campaign_rules] inner join [admin_user] on [admin_user_campaign_rules].[assigned_admin_user_id] = [admin_user].[admin_user_id] where [admin_user_campaign_rules].[campaign_rule_id] = @p0' &&
        query.response(expected);
    });
    const svc = RuleAssignmentService({ dbService, userService: mockUserService });
    const assignmentResult = await svc.assignUserToCampaign(mockUserWithPermissions.id, 'XYZ123');
    expect(assignmentResult).toBe(expected);
  });

  it('assignUserListToCampaign - should throw error for duplicate assignee', (done) => {
    const assignees = [ 's123456', 's123456' ];
    RuleAssignmentService({}).assignUserListToCampaign(assignees, null)
      .catch(e => {
        expect(e.code === 500);
        expect(e.message).toBe('Unable to assign users. Duplicate assignee found');
        done();
      });
  });

  test('assignUserListToCampaign - should throw error when user not found', (done) => {
    tracker.on('query', (query, step) => {
      step === 1 &&
        query.sql === 'select [admin_user_id], [full_name] as [user_name], [sid] from [admin_user] where [sid] in (@p0, @p1) and [active_indicator] = @p2' &&
        query.response([]);
    });
    RuleAssignmentService({ dbService, logger })
      .assignUserListToCampaign([ 's789456', 's123456' ], null)
      .catch(e => {
        expect(e instanceof CustomError).toBe(true);
        expect(e.code === 500).toBe(true);
        expect(e.message).toBe('Unable to find users: s789456,s123456');
        done();
      });
  });

  test('getApproversForVariableMapping', async() => {
    const expected = [
      { full_name: 'A Nova viewer', sid: 's2' },
      { full_name: 'B Pigeon Admin', sid: 's1' },
      { full_name: 'C Nova owner', sid: 's3' },
    ];
    tracker.on('query', (query, step) => query.response(expected));
    const svc = RuleAssignmentService({ dbService });
    const approvers = await svc.getApproversForVariableMapping();
    expect(approvers.length).toBe(3);
    expect(approvers[0]).toEqual(expected[0]);
    expect(approvers[1]).toEqual(expected[1]);
    expect(approvers[2]).toEqual(expected[2]);
  });

  test('getApproversForVariableMapping - non-pigeon team member', async() => {
    tracker.on('query', (query, step) => {
      const teamIdQueryExists = query.sql.includes('[admin_roles_permissions].[admin_team_id] = ');
      teamIdQueryExists && query.response([
        { full_name: 'A Nova viewer', sid: 's2' },
        { full_name: 'C Nova owner', sid: 's3' },
      ]);
      !teamIdQueryExists && query.response([
        { full_name: 'B Pigeon Admin', sid: 's1' },
        { full_name: 'A Nova viewer', sid: 's2' },
        { full_name: 'C Nova owner', sid: 's3' },
      ]);
    });
    const svc = RuleAssignmentService({ dbService });
    const approvers = await svc.getApproversForVariableMapping(1);
    expect(approvers.length).toBe(2);
    expect(approvers[0]).toEqual({ full_name: 'A Nova viewer', sid: 's2' });
    expect(approvers[1]).toEqual({ full_name: 'C Nova owner', sid: 's3' });
  });

  test('should return reviewers when no offer status is provided', async() => {
    const mockTeamId = 'team123';
    tracker.on('query', (query) => {
      query.response([ { full_name: 'Test User', sid: 's123456' } ]);
    });
    const svc = RuleAssignmentService({ dbService, logger });
    const result = await svc.getPotentialAssigneesForOfferCampaign(null, mockTeamId);
    expect(Array.isArray(result)).toBe(true);
    expect(result.length).toBe(1);
  });

  test('should handle alternative branch in validateUsers function', async() => {
    const mockUserService = {
      getUser: jest.fn().mockResolvedValue(undefined),
    };
    const svc = RuleAssignmentService({ dbService, logger, userService: mockUserService });
    const invalidUsers = [ 'invalidUser1', 'invalidUser2' ];

    const result = await svc.validateUsers(invalidUsers);

    expect(result).toEqual({
      success: false,
      error: {
        message: 'Invalid users',
        details: [
          { message: 'User not found', sid: 'invalidUser1' },
          { message: 'User not found', sid: 'invalidUser2' },
        ],
      },
    });

    expect(mockUserService.getUser).toHaveBeenCalledTimes(invalidUsers.length);
  });

  // Test for validateUsers with active users but insufficient permissions
  test('should validate users with insufficient permissions', async() => {
    const mockUserService = {
      getUser: jest.fn().mockResolvedValue([ { id: 1, sid: 'user1' } ]),
    };

    const mockPermissionService = {
      getPermissionsForUser: jest.fn().mockResolvedValue([]),
    };

    const svc = RuleAssignmentService({
      dbService,
      logger,
      userService: mockUserService,
      permissionService: mockPermissionService,
    });

    const result = await svc.validateUsers([ 'user1' ], 'some_permission');

    expect(result).toEqual({
      success: false,
      error: {
        message: 'Invalid users',
        details: [ { sid: 'user1', message: 'Insufficent permissions' } ],
      },
    });
  });

  test('should return empty object for INACTIVE offer status', async() => {
    const mockTeamId = 'team123';
    const svc = RuleAssignmentService({ dbService, logger });
    const result = await svc.getPotentialAssigneesForOfferCampaign('INACTIVE', mockTeamId);
    expect(result).toEqual({});
  });

  test('should return empty object for ACTIVE offer status', async() => {
    const mockTeamId = 'team123';
    const svc = RuleAssignmentService({ dbService, logger });
    const result = await svc.getPotentialAssigneesForOfferCampaign('ACTIVE', mockTeamId);
    expect(result).toEqual({});
  });

  test('should return empty object for REVIEWED offer status', async() => {
    const mockTeamId = 'team123';
    const svc = RuleAssignmentService({ dbService, logger });
    const result = await svc.getPotentialAssigneesForOfferCampaign('REVIEWED', mockTeamId);
    expect(result).toEqual({});
  });

  test('should return empty object for EXPIRED offer status', async() => {
    const mockTeamId = 'team123';
    const svc = RuleAssignmentService({ dbService, logger });
    const result = await svc.getPotentialAssigneesForOfferCampaign('EXPIRED', mockTeamId);
    expect(result).toEqual({});
  });

  // Test for empty result from database
  test('should handle empty result from database', async() => {
    const mockTeamId = 'team123';

    // Mock the database query response with empty array
    tracker.on('query', (query) => {
      query.response([]);
    });

    const svc = RuleAssignmentService({ dbService, logger });
    const result = await svc.getPotentialAssigneesForOfferCampaign('DRAFT', mockTeamId);
    expect(Array.isArray(result)).toBe(true);
    expect(result.length).toBe(0);
  });

  test('should handle duplicate assignees', async() => {
    const duplicateAssignees = [ 'user1', 'user1', 'user2' ];

    const svc = RuleAssignmentService({ dbService, logger });

    await expect(svc.assignUserListToCampaign(duplicateAssignees, 'campaignId')).rejects.toThrow(
      'Unable to assign users. Duplicate assignee found',
    );
  });

  // Test for DRAFT offer status
  test('should return reviewers for DRAFT offer status', async() => {
    const mockTeamId = 'team123';

    // Mock the database query response
    tracker.on('query', (query) => {
      expect(query.bindings).toContain('offers_review');
      expect(query.bindings).toContain(mockTeamId);
      query.response([ { full_name: 'Test User', sid: 's123456' } ]);
    });

    const svc = RuleAssignmentService({
      dbService,
      logger,
    });

    const result = await svc.getPotentialAssigneesForOfferCampaign('DRAFT', mockTeamId);

    expect(Array.isArray(result)).toBe(true);
    expect(result.length).toBe(1);
    expect(result[0]).toEqual({ full_name: 'Test User', sid: 's123456' });
  });

  // Test for SUBMITTED offer status
  test('should return approvers for SUBMITTED offer status', async() => {
    const mockTeamId = 'team123';

    // Install and configure tracker before the test
    tracker.install();

    // Mock the database query response with a step parameter
    tracker.on('query', (query, step) => {
      // Verify query properties
      expect(query.bindings).toContain('offers_approve');
      expect(query.bindings).toContain(mockTeamId);

      // Respond with mock data
      query.response([ { full_name: 'Test User', sid: 's123456' } ]);
    });

    const svc = RuleAssignmentService({
      dbService,
      logger,
    });

    const result = await svc.getPotentialAssigneesForOfferCampaign('SUBMITTED', mockTeamId);

    // Uninstall tracker after the test
    tracker.uninstall();

    expect(Array.isArray(result)).toBe(true);
    expect(result.length).toBe(1);
    expect(result[0]).toEqual({ full_name: 'Test User', sid: 's123456' });
  });

  // Test error handling
  test('should handle errors in getPotentialAssigneesForOfferCampaign', async() => {
    const mockTeamId = 'team123';

    // Install and configure tracker before the test
    tracker.install();

    // Mock the database query to throw an error
    tracker.on('query', () => {
      throw new Error('Database error');
    });

    const svc = RuleAssignmentService({
      dbService,
      logger,
    });

    await expect(svc.getPotentialAssigneesForOfferCampaign('DRAFT', mockTeamId)).rejects.toThrow(
      'Database error',
    );

    // Uninstall tracker after the test
    tracker.uninstall();

    expect(logger.error).toHaveBeenCalled();
  });
});

const mockDbService = {};

const mockLogger = {
  error: jest.fn(),
};

const mockUserService = {
  getUser: jest.fn(),
};

const mockPermissionService = {
  getPermissionsForUser: jest.fn(),
};

const mockCampaignService = {
  getCampaign: jest.fn(),
};

const mockUserWithPermissions = {
  id: '1',
  name: 'test user - draft',
  email: '<EMAIL>',
  sid: 's123456',
  token: 'abc123451234',
  active: 1,
  token_expire_date: 1559056346742,
  token_expires: 0,
};

const mockUserNoPermissions = {
  id: '2',
  name: 'test user - no draft permissions',
  email: '<EMAIL>',
  sid: 's1234567',
  token: 'abc1234512341',
  active: 1,
  token_expire_date: 1559056346742,
  token_expires: 0,
};

const permissionList = [ 'campaigns_view',
  'campaigns_create',
  'campaigns_update_details',
  'campaigns_delete',
  'campaigns_update_status_draft_submitted',
  'campaigns_update_status_submitted_reviewed',
  'campaigns_update_status_reviewed_draft',
  'campaigns_update_status_reviewed_published',
  'campaigns_update_status_submitted_draft',
  'campaigns_update_disable',
  'alerts_view',
  'alerts_create',
  'alerts_update',
  'alerts_delete',
  'alerts_update_status_draft_submitted',
  'alerts_update_status_submitted_draft',
  'alerts_update_status_submitted_published',
  'alerts_update_disable',
  'users_view',
  'users_update',
  'users_create',
  'users_delete',
  'roles_update',
  'roles_create',
  'roles_view',
  'roles_delete',
  'admin' ];

module.exports = mockDb => {
  const _mockDb = mockDb;

  describe('Rule Assignment Service Tests', () => {
    beforeAll(async() => {
      await TRUNCATE_DATABASE(mockDb);
      // create initial data for DB --
      const rolesPermissionsInsert = [];
      for (let i = 1; i < 28; i++) {
        rolesPermissionsInsert.push(TRANSFORM_INPUT({
          role_id: 1,
          permission_name: permissionList[i],
        }, COL_NAME.roles_permissions));
      }

      const permissionInserts = [];
      let permissionListIndex;

      for (permissionListIndex = 0; permissionListIndex < permissionList.length; permissionListIndex++) {
        permissionInserts.push(TRANSFORM_INPUT({
          name: permissionList[permissionListIndex],
          description: permissionList[permissionListIndex],
        }, COL_NAME.permission));
      }

      const campaignAssignmentInserts = [
        TRANSFORM_INPUT({
          admin_user_id: '1',
          campaign_rule_id: 'ACB1234',
        }, COL_NAME.user_campaign_rules),

        TRANSFORM_INPUT({
          admin_user_id: '1',
          campaign_rule_id: 'ASDF123123',
        }, COL_NAME.user_campaign_rules),

        TRANSFORM_INPUT({
          admin_user_id: '1',
          campaign_rule_id: 'DEF1234',
        }, COL_NAME.user_campaign_rules),

        TRANSFORM_INPUT({
          admin_user_id: '2',
          campaign_rule_id: 'DEF1234',
        }, COL_NAME.user_campaign_rules) ];

      return Promise.all([ mockDb(TABLE.user)
        .insert(TRANSFORM_INPUT(mockUserWithPermissions,
          COL_NAME.user)),

      mockDb(TABLE.user)
        .insert(TRANSFORM_INPUT(mockUserNoPermissions,
          COL_NAME.user)),

      mockDb(TABLE.users_roles)
        .insert(TRANSFORM_INPUT({
          user_id: '1',
          role_id: '1',
        }, COL_NAME.users_roles)),

      mockDb(TABLE.users_roles)
        .insert(TRANSFORM_INPUT({
          user_id: '2',
          role_id: '2',
        }, COL_NAME.users_roles)),

      mockDb(TABLE.roles_permissions)
        .insert(rolesPermissionsInsert),

      mockDb(TABLE.permission)
        .insert(permissionInserts),

      mockDb(TABLE.user_campaign_rules)
        .insert(campaignAssignmentInserts),
      ]);
    });

    beforeEach(() => {
      mockUserService.getUser.mockClear();
      mockCampaignService.getCampaign.mockClear();
      mockPermissionService.getPermissionsForUser.mockClear();
    });

    test('should return assignment results for valid system users assigned to a campaign', async() => {
      const client = RuleAssignmentService({ dbService: mockDb,
        logger: mockLogger,
        userService: mockUserService,
        permissionService: mockPermissionService,
        campaignService: mockCampaignService });

      const usersToAssign = [ mockUserWithPermissions.sid, mockUserNoPermissions.sid ];
      const assignmentResult = await client.assignUserListToCampaign(usersToAssign, 'XYZ1234');

      expect(Array.isArray(assignmentResult)).toBe(true);
      expect(assignmentResult.length === usersToAssign.length).toBe(true);

      const assignmentVerificationResult = await mockDb('admin_user_campaign_rules')
        .select('assigned_admin_user_id')
        .where('campaign_rule_id', 'XYZ1234');

      expect(Array.isArray(assignmentVerificationResult)).toBe(true);
      expect(assignmentVerificationResult.length === 2).toBe(true);
      // expect(assignmentVerificationResult[0]).toHaveProperty('assigned_admin_user_id');
    });

    test('should return assignees eligible to create new campaigns', async() => {
      const client = RuleAssignmentService({
        dbService: _mockDb,
        logger: mockLogger,
      });

      const result = await client.getPotentialAssigneesForCampaign();
      const expected = [ {
        full_name: mockUserWithPermissions.name,
        sid: mockUserWithPermissions.sid,
      } ];

      expect(result).toEqual(expected);
    });

    test('should return empty array when fetching assignees for published campaign', async() => {
      mockCampaignService.getCampaign.mockReturnValueOnce({
        data: {
          status: 'published',
        },
      });

      const client = RuleAssignmentService({ dbService: mockDbService,
        logger: mockLogger,
        userService: mockUserService,
        permissionService: mockPermissionService,
        campaignService: mockCampaignService });

      const result = await client.getPotentialAssigneesForCampaign('abc123');

      expect(result instanceof Object).toBe(true);
      expect(Object.keys(result).length === 0).toBe(true);
    });

    test('should return valid assignee for draft campaign', async() => {
      mockCampaignService.getCampaign.mockReturnValueOnce({
        data: {
          status: 'draft',
        },
      });

      const client = RuleAssignmentService({ dbService: _mockDb,
        logger: mockLogger,
        userService: mockUserService,
        permissionService: mockPermissionService,
        campaignService: mockCampaignService });

      const result = await client.getPotentialAssigneesForCampaign('abc123');
      expect(Array.isArray(result)).toBe(true);
      expect(result.length > 0).toBe(true);
    });

    test('should return valid assignee for submitted campaign', async() => {
      mockCampaignService.getCampaign.mockReturnValueOnce({
        data: {
          status: 'submitted',
        },
      });

      const client = RuleAssignmentService({ dbService: _mockDb,
        logger: mockLogger,
        userService: mockUserService,
        permissionService: mockPermissionService,
        campaignService: mockCampaignService });
      const result = await client.getPotentialAssigneesForCampaign('abc123');

      expect(Array.isArray(result)).toBe(true);
      expect(result.length === 1).toBe(true);
      const userWithPermissions = result.find(function(element) {
        if (element.sid === mockUserWithPermissions.sid) {
          return element;
        }
      });
      expect(userWithPermissions !== undefined).toBe(true);
    });

    test('should return valid assignee for reviewed campaign', async() => {
      mockCampaignService.getCampaign.mockReturnValueOnce({
        data: {
          status: 'reviewed',
        },
      });

      const client = RuleAssignmentService({ dbService: _mockDb,
        logger: mockLogger,
        userService: mockUserService,
        permissionService: mockPermissionService,
        campaignService: mockCampaignService });
      const result = await client.getPotentialAssigneesForCampaign('abc123');

      expect(Array.isArray(result)).toBe(false);
    });

    test('Should fail user validation for missing user', async() => {
      mockUserService.getUser.mockReturnValueOnce(undefined);

      const client = RuleAssignmentService({ dbService: _mockDb,
        logger: mockLogger,
        userService: mockUserService,
        permissionService: mockPermissionService,
        campaignService: mockCampaignService });

      const result = await client.validateUsers([ 'abc123' ]);

      const failResponse = {
        success: false,
        error: {
          message: 'Invalid users',
          details: [ { sid: 'abc123', message: 'User not found' } ],
        },
      };
      expect(result).toEqual(failResponse);
    });

    test('Should fail user validation for invalid permissions', async() => {
      mockUserService.getUser.mockReturnValueOnce([ { id: 'abc123' } ]);
      mockPermissionService.getPermissionsForUser.mockReturnValueOnce([]);

      const client = RuleAssignmentService({ dbService: _mockDb,
        logger: mockLogger,
        userService: mockUserService,
        permissionService: mockPermissionService,
        campaignService: mockCampaignService });

      const result = await client.validateUsers([ 'abc123' ]);

      const failResponse = {
        success: false,
        error: {
          message: 'Invalid users',
          details: [ { sid: 'abc123', message: 'Insufficent permissions' } ],
        },
      };
      expect(result).toEqual(failResponse);
    });

    test('Should succeed user validation with valid user and permissions', async() => {
      mockUserService.getUser.mockReturnValueOnce([ { id: 'abc:123' } ]);
      mockPermissionService.getPermissionsForUser.mockReturnValueOnce({ permissions: permissionList });

      const client = RuleAssignmentService({ dbService: _mockDb,
        logger: mockLogger,
        userService: mockUserService,
        permissionService: mockPermissionService,
        campaignService: mockCampaignService });

      const result = await client.validateUsers({ permissions: [ 'abc123' ] }, PERMISSION_CAMPAIGNS_STATUS_DRAFT_SUBMITTED);

      const successResponse = {
        success: true,
      };
      expect(result).toEqual(successResponse);
    });

    test('Should return records for valid campaign rule assignemnts', async() => {
      const client = RuleAssignmentService({ dbService: _mockDb,
        logger: mockLogger,
        userService: mockUserService,
        permissionService: mockPermissionService,
        campaignService: mockCampaignService });

      const result = await client.getAssigneesForCampaign('ACB1234');
      expect(Array.isArray(result)).toBe(true);
      expect(result.length > 0).toBe(true);
    });

    test('Should return result when successfully deleting campaign assignments', async() => {
      const client = RuleAssignmentService({ dbService: _mockDb,
        logger: mockLogger,
        userService: mockUserService,
        permissionService: mockPermissionService,
        campaignService: mockCampaignService });

      const result = await client.removeUsersFromCampaign('DEF1234');
      expect(result === 2).toBe(true);
      const unassignedCampaign = await client.getAssigneesForCampaign('DEF1234');
      expect(Array.isArray(unassignedCampaign)).toBe(true);
      expect(unassignedCampaign.length === 0).toBe(true);
    });
  });
};
