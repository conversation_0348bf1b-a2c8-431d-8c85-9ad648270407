const RuleAssignmentService = require('.');

const mockDbService = {};
const mockLogger = {};
const mockUserService = {};
const mockPermissionService = {};
const mockCampaignService = {};

describe('Rule Assignment Service', () => {
  test('should have assignUserToCampaign method ', () => {
    const client = RuleAssignmentService({ dbService: mockDbService,
      logger: mockLogger,
      userService: mockUserService,
      permissionService: mockPermissionService,
      campaignService: mockCampaignService });

    expect(client).toHaveProperty('assignUserToCampaign');
    expect(typeof client.assignUserToCampaign).toEqual('function');
  });

  test('should have assignUserListToCampaign method ', () => {
    const client = RuleAssignmentService({ dbService: mockDbService,
      logger: mockLogger,
      userService: mockUserService,
      permissionService: mockPermissionService,
      campaignService: mockCampaignService });

    expect(client).toHaveProperty('assignUserListToCampaign');
    expect(typeof client.assignUserToCampaign).toEqual('function');
  });

  test('should have removeUsersFromCampaign method ', () => {
    const client = RuleAssignmentService({ dbService: mockDbService,
      logger: mockLogger,
      userService: mockUserService,
      permissionService: mockPermissionService,
      campaignService: mockCampaignService });

    expect(client).toHaveProperty('removeUsersFromCampaign');
    expect(typeof client.assignUserToCampaign).toEqual('function');
  });

  test('should have validateUsers method ', () => {
    const client = RuleAssignmentService({ dbService: mockDbService,
      logger: mockLogger,
      userService: mockUserService,
      permissionService: mockPermissionService,
      campaignService: mockCampaignService });

    expect(client).toHaveProperty('validateUsers');
    expect(typeof client.assignUserToCampaign).toEqual('function');
  });

  test('should have getPotentialAssigneesForCampaign method ', () => {
    const client = RuleAssignmentService({ dbService: mockDbService,
      logger: mockLogger,
      userService: mockUserService,
      permissionService: mockPermissionService,
      campaignService: mockCampaignService });

    expect(client).toHaveProperty('getPotentialAssigneesForCampaign');
    expect(typeof client.assignUserToCampaign).toEqual('function');
  });
});
