const { COLUMN, COL_NAME, JOIN, SELECT, TABLE } = require('../constants/db');
const { TRANSFORM_RESULT, TRANSFORM_INPUT, SORT_AND_PAGINATE } = require('../utils/db');
const { flatten, omit } = require('lodash');

const pageService = ({ query: db, trx }) => {
  const getPage = async(query = {}) => {
    const pageIdsWithContainerId = query.container_id && (await db(TABLE.page)
      .select(COLUMN.page.id)
      .leftJoin(...JOIN.page_pages_containers)
      .where(COLUMN.containers_pages.container_id, query.container_id))
      .map(r => r.admin_page_id);

    const result = await db(TABLE.page)
      .select(
        COLUMN.page,
        SELECT.containers_pages.container_id,
        SELECT.application.applicationId,
        SELECT.application.team_id,
      )
      .leftJoin(...JOIN.page_pages_containers)
      .leftJoin(...JOIN.page_application)
      .where(TRANSFORM_INPUT(query, COLUMN.page))
      .where(builder => {
        if (query.container_id) {
          builder.whereIn(COLUMN.page.id, pageIdsWithContainerId);
        }
        if (query.application_id) {
          builder.where(COLUMN.page.application, query.application_id);
        }
        if (query.team_id) {
          builder.where(COLUMN.application.team_id, query.team_id);
        }
        if (query.search) {
          builder.where(innerBuilder => {
            innerBuilder.where(COL_NAME.page.name, 'LIKE', `%${query.search}%`)
              .orWhere(COL_NAME.page.pageId, 'LIKE', `%${query.search}%`);
          });
        }
      });

    const transformedResult = TRANSFORM_RESULT(result, { containers: 'container_id' });
    if (query.id) {
      return transformedResult;
    }
    return SORT_AND_PAGINATE(transformedResult, query);
  };

  const _addPageAccessToPigeon = async(pageId, applicationId, dbClient) => {
    const ruleTypeIdsRes = await dbClient(TABLE.applications_rule_types)
      .select(COLUMN.applications_rule_types.rule_type_id)
      .where(COLUMN.applications_rule_types.application_id, applicationId);
    const ruleTypeIds = ruleTypeIdsRes.map(r => r.admin_rule_type_id);

    const accessLevelsRes = await dbClient(TABLE.access_levels).select(COLUMN.access_levels.access_id);
    const accessLevels = accessLevelsRes.map(a => a.admin_access_id);

    ruleTypeIds.length && accessLevels.length && await dbClient(TABLE.access_pages).insert(
      flatten(
        ruleTypeIds.map((ruleTypeId) => (
          accessLevels.map((accessLevelId) => ({
            [COL_NAME.access_pages.team_id]: 1, // Pigeon
            [COL_NAME.access_pages.rule_type_id]: ruleTypeId,
            [COL_NAME.access_pages.application_id]: applicationId,
            [COL_NAME.access_pages.page_id]: pageId,
            [COL_NAME.access_pages.active]: 1,
            [COL_NAME.access_pages.access_id]: accessLevelId,
          }))
        )),
      ),
    );
  };

  const _switchTeam = async(id, oldPage, newPage, dbClient) => {
    // update access to page from old app to new app for all teams with access
    const sqlAccess = dbClient(TABLE.access_pages)
      .update({
        [COL_NAME.access_pages.application_id]: newPage.application,
      }).where({
        [COL_NAME.access_pages.application_id]: oldPage.application,
        [COL_NAME.access_pages.page_id]: id,
      });
    await sqlAccess;

    // break links with associated containers on the old app
    // teams with access to old app will have access to new app
    // but orphaned without associated container to be able to publish rules into
    const sqlBreakLinks = dbClient(TABLE.containers_pages)
      .delete()
      .where(builder => {
        builder.whereIn(COLUMN.containers_pages.container_id, oldPage.containers);
        builder.where(COLUMN.containers_pages.page_id, id);
      });
    await sqlBreakLinks;
  };

  const createPage = async(query) => {
    const id = await trx(async t => _createPage(t, query));
    return (await getPage({ id }))[0];
  };

  const _createPage = async(dbClient, query) => {
    const { [COL_NAME.page.id]: id } = (await dbClient(TABLE.page)
      .insert({ ...TRANSFORM_INPUT(query, COL_NAME.page) })
      .returning(COL_NAME.page.id))[0];
    await _addPageAccessToPigeon(id, query.application, dbClient);
    return id;
  };

  const updatePage = async(id, query, option = {}) => {
    await trx(async t => _updatePage(t, id, query));
    if (option.noReturn) {
      return;
    }
    return (await getPage({ id }))[0];
  };

  const _updatePage = async(dbClient, id, query) => {
    const oldPage = (await getPage({ id }))[0];
    await dbClient(TABLE.page)
      .update({ ...TRANSFORM_INPUT(omit(query, [ 'pageId' ]), omit(COL_NAME.page, [ 'pageId' ])) })
      .where(COLUMN.page.id, id);
    (query.application && query.application !== oldPage.application) && await _switchTeam(id, oldPage, query, dbClient);
  };

  const setPageStatus = async(id, active = true) => {
    await db(TABLE.page)
      .update({ [COL_NAME.page.status]: active })
      .where(COL_NAME.page.id, id);
    return (await getPage({ id }))[0];
  };

  const _deletePage = async(dbClient, id) => {
    const result = (await getPage({ id }))[0];
    await dbClient(TABLE.containers_pages)
      .where(COL_NAME.containers_pages.page_id, id)
      .delete();

    await dbClient(TABLE.access_pages)
      .delete()
      .where(COLUMN.access_pages.page_id, id);

    await dbClient(TABLE.page)
      .where(COLUMN.page.id, id)
      .delete();
    return result;
  };

  // run all queries involved with delete page service inside of a transaction
  const deletePage = async(id) => trx(async t => _deletePage(t, id));

  return {
    getPage,
    createPage,
    updatePage,
    deletePage,
    setPageStatus,
  };
};

module.exports = pageService;
