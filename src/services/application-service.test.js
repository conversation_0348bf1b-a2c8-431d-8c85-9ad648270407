const knex = require('knex');
const mockKnex = require('mock-knex');

const ApplicationService = require('./application-service');
const { COL_NAME } = require('../constants/db');

const mockDb = knex({ client: 'mssql', debug: false });
const tracker = mockKnex.getTracker();
const testApp = {
  applicationId: 'test-id',
  description: 'test-description',
  contentful_space: 'test-space',
  name: 'test-name',
  team_id: 1,
};

describe('Application service', () => {
  beforeAll(() => {
    mockKnex.mock(mockDb);
  });
  afterAll(() => {
    mockKnex.unmock(mockDb);
  });
  beforeEach(() => {
    tracker.install();
  });
  afterEach(() => {
    tracker.uninstall();
  });

  it('getApplications', async() => {
    const dbResponse = [ {
      ...testApp,
      id: 1,
      status: true,
      rule_version: 1,
      admin_platform_name: 'Web',
      admin_rule_type_name: 'campaign',
      admin_platform_id: 1,
      admin_rule_type_id: 2,
      admin_rule_sub_type_id: 3,
      admin_rule_sub_type_name: 'MASS',
    } ];
    tracker.on('query', (query, step) => {
      if (step === 1) { // select operation - returns apps
        query.response(dbResponse);
      }
    });
    const response = await ApplicationService(mockDb).getApplications({ limit: 30, pageNumber: 1 });
    const items = [ {
      ...testApp,
      id: 1,
      status: true,
      rule_version: 1,
      platforms: [ 'Web' ],
      platformIds: [ 1 ],
      ruleTypes: [ 'campaign' ],
      ruleTypeIds: [ 2 ],
      ruleSubTypeIds: [ 3 ],
      ruleSubTypes: [ 'MASS' ],
      team: [],
      wealth_lobs: [
        { code: 'SDBI', description: 'Scotia iTRADE' },
        { code: 'SMI', description: 'ScotiaMcLeod' },
        { code: 'CASL', description: 'Private Investment Counsel' },
        { code: 'TRST', description: 'Scotiatrust' },
        { code: 'SPCGIIA', description: 'International Investment Advisory' },
        { code: 'REGL', description: '1832 Asset Management U.S. Inc.' },
      ],
    } ];
    expect(response).toEqual({ items, total: 1, limit: 30, offset: 0 });
  });

  it('createApplication', async() => {
    const newApp = {
      ...testApp,
      platformIds: [ 1 ], // web
      ruleTypeIds: [ 2 ], // campaign
      ruleSubTypeIds: [ 3 ], // targeted, mass msg
      status: 1,
    };
    const dbResponse = [ {
      ...testApp,
      id: 1,
      status: true,
      rule_version: 1,
      admin_platform_name: 'Web',
      admin_rule_type_name: 'campaign',
      admin_rule_sub_type_name: 'mass',
      admin_platform_id: 1,
      admin_rule_type_id: 2,
      admin_rule_sub_type_id: 3,
    } ];
    tracker.on('query', (query, step) => {
      if (step === 1) { // insert operation - returns new app id
        query.response([ { [COL_NAME.application.id]: 1 } ]);
      } else if (step === 2) { // _reconcileTeamPermissions - get current permissions
        query.response([ ]);
      } else if (step === 3) { // _reconcileTeamPermissions - add missing permissions to team
        query.response(undefined);
      } else if (step === 4) { // _reconcileTeamPermissions - get team owner role
        query.response([ { id: 1 } ]);
      } else if (step === 5) { // _reconcileTeamPermissions - add missing permissions to team owner role (_insertRolesPermissions)
        query.response(undefined);
      } else if (step === 6) { // _reconcileTeamPermissions - get team viewer role
        query.response([ { id: 1 } ]);
      } else if (step === 7) { // _reconcileTeamPermissions - add missing permissions to team viewer role (_insertRolesPermissions)
        query.response(undefined);
      } else if (step === 8 || step === 9 || step === 10) { // insert operation - platforms, rule types, rule subtypes
        query.response(undefined);
      } else if (step === 11) { // _addRuleSubTypeAccessToPigeon - get access levels
        query.response([ { admin_access_id: 1 } ]);
      } else if (step === 12) { // _addRuleSubTypeAccessToPigeon - insert rule sub type access
        query.response(undefined);
      } else if (step === 13) { // getApplication - returns app
        query.response(dbResponse);
      }
    });

    const response = await ApplicationService(mockDb).createApplication(newApp);
    expect(response).toStrictEqual(dbResponse[0]);
  });

  it('updateApplication - removing rule type (campaign) throws error if it has associated containers', async() => {
    const updatedApp = {
      ...testApp,
      platformIds: [ 2, 3 ], // ios, android
      ruleTypeIds: [ 1 ], // alert
      ruleSubTypeIds: [ 1, 2 ], // targeted, mass campaign
      status: 1,
    };
    tracker.on('query', (query, step) => {
      if (step === 1) { // select operation - returns application's rule types & rule sub types
        query.response([
          { admin_rule_type_id: 1, admin_rule_sub_type_id: 1 },
          { admin_rule_type_id: 1, admin_rule_sub_type_id: 2 },
          { admin_rule_type_id: 2, admin_rule_sub_type_id: 1 },
          { admin_rule_type_id: 2, admin_rule_sub_type_id: 2 },
        ]);
      } else if (step === 2) { // select operation - returns found containers
        query.response([
          { admin_container_name: 'my-activity', admin_container_id: 2 },
          { admin_container_name: 'offers-and-programs', admin_container_id: 3 },
        ]);
      } else if (step === 3) { // select operation - returns rule type names
        query.response([ { admin_rule_type_name: 'campaign' } ]);
      }
    });

    try {
      await ApplicationService(mockDb).updateApplication(1, updatedApp);
    } catch (err) {
      const errMsg = 'You\'re trying to remove campaign rule type(s) from an application, but these rule type(s) are used in containers that rely on this application and rule type(s) combination. \n\nmy-activity (id 2)\noffers-and-programs (id 3). \n\nRemove the campaign rule type(s) from these containers in order to proceed with removing the rule type(s) from this application.';
      expect(err.message).toStrictEqual(errMsg);
    }
  });

  it('updateApplication - removing rule type (campaign) returns success if it has no associated containers', async() => {
    const updatedApp = {
      ...testApp,
      platformIds: [ 2, 3 ], // ios, android
      ruleTypeIds: [ 2 ], // campaign
      ruleSubTypeIds: [ 1, 2, 3 ], // targeted, mass campaign, message
      status: 1,
    };
    tracker.on('query', (query, step) => {
      if (step === 1) { // select operation - returns application's rule types & rule sub types
        query.response([
          { admin_rule_type_id: 1, admin_rule_sub_type_id: 1, team_id: 2, status: true },
          { admin_rule_type_id: 1, admin_rule_sub_type_id: 2, team_id: 2, status: true },
          { admin_rule_type_id: 2, admin_rule_sub_type_id: 1, team_id: 2, status: true },
          { admin_rule_type_id: 2, admin_rule_sub_type_id: 2, team_id: 2, status: true },
        ]);
      } else if (step === 2) { // select operation - returns found containers
        query.response([]);
      } else if ([ 3, 4, 5, 6 ].includes(step)) {
        // _deleteRuleTypes - deletes rule type's access containers
        // _deleteRuleTypes - deletes rule type's access pages
        // _deleteRuleTypes - deletes rule type's access rule sub types
        // _deleteRuleTypes - deletes rule type
        query.response(1);
      } else if (step === 7) { // _updateApplicationBasicFields update operation - updates application, returns success
        query.response(1);
      } else if (step === 8) { // _reconcileTeamPermissions - get current permissions
        query.response([ ]);
      } else if (step === 9) { // _reconcileTeamPermissions - add missing permissions to team
        query.response(undefined);
      } else if (step === 10) { // _reconcileTeamPermissions - get team owner role
        query.response([ { id: 1 } ]);
      } else if (step === 11) { // _reconcileTeamPermissions - add missing permissions to team owner role (_insertRolesPermissions)
        query.response(undefined);
      } else if (step === 12) { // _reconcileTeamPermissions - get team viewer role
        query.response([ { id: 1 } ]);
      } else if (step === 13) { // _reconcileTeamPermissions - add missing permissions to team viewer role (_insertRolesPermissions)
        query.response(undefined);
      } else if (step === 14) { // _reconcileTeamPermissions - get previous team's other applications
        query.response([]);
      } else if (step === 15) { // _reconcileTeamPermissions - delete previous team's roles' permissions
        query.response(1);
      } else if (step === 16) { // _reconcileTeamPermissions - delete previous team's permissions
        query.response(1);
      } else if (step === 17) { // _updateRuleSubTypes - insert new rule types
        query.response(1);
      } else if (step === 18) { // _updatePlatforms - delete platforms, return success
        query.response(1);
      } else if (step === 19) { // _updatePlatforms - insert platforms
        query.response(undefined);
      } else if (step === 20) { // _removeRuleSubTypeAccessFromPigeon
        query.response(undefined);
      } else if (step === 21) { // _addRuleSubTypeAccessToPigeon - get access levels
        query.response([ { admin_access_id: 1 }, { admin_access_id: 2 } ]);
      } else if (step === 22) { // _addRuleSubTypeAccessToPigeon - insert rule sub types
        query.response(undefined);
      } else if (step === 23) { // getApplication select operation - returns app
        query.response([ {
          ...testApp,
          id: 1,
          status: true,
          rule_version: 1,
          admin_platform_id: 2,
          admin_platform_name: 'iOS',
          admin_rule_type_id: 2,
          admin_rule_type_name: 'campaign',
          admin_rule_sub_type_id: 1,
          admin_rule_sub_type_name: 'targeted',
        }, {
          ...testApp,
          id: 1,
          status: true,
          rule_version: 1,
          admin_platform_id: 2,
          admin_platform_name: 'iOS',
          admin_rule_type_id: 2,
          admin_rule_type_name: 'campaign',
          admin_rule_sub_type_id: 2,
          admin_rule_sub_type_name: 'mass',
        }, {
          ...testApp,
          id: 1,
          status: true,
          rule_version: 1,
          admin_platform_id: 3,
          admin_platform_name: 'Android',
          admin_rule_type_id: 2,
          admin_rule_type_name: 'campaign',
          admin_rule_sub_type_id: 1,
          admin_rule_sub_type_name: 'targeted',
        }, {
          ...testApp,
          id: 1,
          status: true,
          rule_version: 1,
          admin_platform_id: 3,
          admin_platform_name: 'Android',
          admin_rule_type_id: 2,
          admin_rule_type_name: 'campaign',
          admin_rule_sub_type_id: 2,
          admin_rule_sub_type_name: 'mass',
        } ]);
      }
    });

    const response = await ApplicationService(mockDb).updateApplication(1, updatedApp);
    expect(response).toStrictEqual({
      ...testApp,
      id: 1,
      status: true,
      rule_version: 1,
      platforms: [ 'iOS', 'Android' ],
      platformIds: [ 2, 3 ],
      ruleTypes: [ 'campaign' ],
      ruleTypeIds: [ 2 ],
      ruleSubTypes: [ 'targeted', 'mass' ],
      ruleSubTypeIds: [ 1, 2 ],
      wealth_lobs: [
        { code: 'SDBI', description: 'Scotia iTRADE' },
        { code: 'SMI', description: 'ScotiaMcLeod' },
        { code: 'CASL', description: 'Private Investment Counsel' },
        { code: 'TRST', description: 'Scotiatrust' },
        { code: 'SPCGIIA', description: 'International Investment Advisory' },
        { code: 'REGL', description: '1832 Asset Management U.S. Inc.' },
      ],
    });
  });

  it('updateApplication - update rule sub types (add targeted, remove mass msg) returns success', async() => {
    const updatedApp = {
      ...testApp,
      platformIds: [ 2, 3 ], // ios, android
      ruleTypeIds: [ 2 ], // campaign
      ruleSubTypeIds: [ 1, 2 ], // targeted, mass campaign
      status: 1,
    };
    tracker.on('query', (query, step) => {
      if (step === 1) { // select operation - returns application's rule types & rule sub types
        query.response([
          { admin_rule_type_id: 2, admin_rule_sub_type_id: 2, team_id: 1, status: true },
          { admin_rule_type_id: 2, admin_rule_sub_type_id: 3, team_id: 1, status: true },
        ]);
      } else if (step === 2) { // updateApplicationBasicFields update operation - updates application, returns success
        query.response(1);
      } else if (step === 3) { // _updateRuleSubTypes delete operation - deletes rule subtype's access, returns success
        query.response(1);
      } else if (step === 4) { // _updateRuleSubTypes delete operation - deletes rule subtype, returns success
        query.response(1);
      } else if (step === 5) { // _updateRuleSubTypes insert operation - add rule subtypes
        query.response(undefined);
      } else if (step === 6) { // _updatePlatforms - delete platforms, return success
        query.response(1);
      } else if (step === 7) { // _updatePlatforms - insert platforms
        query.response(undefined);
      } else if (step === 8) { // _removeRuleSubTypeAccessFromPigeon
        query.response(undefined);
      } else if (step === 9) { // _addRuleSubTypeAccessToPigeon - get access levels
        query.response([ { admin_access_id: 1 }, { admin_access_id: 2 } ]);
      } else if (step === 10) { // _addRuleSubTypeAccessToPigeon - insert rule sub types
        query.response(undefined);
      } else if (step === 11) { // getApplication select operation - returns app
        query.response([ {
          ...testApp,
          id: 1,
          status: true,
          rule_version: 1,
          admin_platform_id: 2,
          admin_platform_name: 'iOS',
          admin_rule_type_id: 2,
          admin_rule_type_name: 'campaign',
          admin_rule_sub_type_id: 1,
          admin_rule_sub_type_name: 'targeted',
        }, {
          ...testApp,
          id: 1,
          status: true,
          rule_version: 1,
          admin_platform_id: 2,
          admin_platform_name: 'iOS',
          admin_rule_type_id: 2,
          admin_rule_type_name: 'campaign',
          admin_rule_sub_type_id: 2,
          admin_rule_sub_type_name: 'mass',
        }, {
          ...testApp,
          id: 1,
          status: true,
          rule_version: 1,
          admin_platform_id: 3,
          admin_platform_name: 'Android',
          admin_rule_type_id: 2,
          admin_rule_type_name: 'campaign',
          admin_rule_sub_type_id: 1,
          admin_rule_sub_type_name: 'targeted',
        }, {
          ...testApp,
          id: 1,
          status: true,
          rule_version: 1,
          admin_platform_id: 3,
          admin_platform_name: 'Android',
          admin_rule_type_id: 2,
          admin_rule_type_name: 'campaign',
          admin_rule_sub_type_id: 2,
          admin_rule_sub_type_name: 'mass',
        } ]);
      }
    });

    const response = await ApplicationService(mockDb).updateApplication(1, updatedApp);
    expect(response).toStrictEqual({
      ...testApp,
      id: 1,
      status: true,
      rule_version: 1,
      platforms: [ 'iOS', 'Android' ],
      platformIds: [ 2, 3 ],
      ruleTypes: [ 'campaign' ],
      ruleTypeIds: [ 2 ],
      ruleSubTypes: [ 'targeted', 'mass' ],
      ruleSubTypeIds: [ 1, 2 ],
      wealth_lobs: [
        { code: 'SDBI', description: 'Scotia iTRADE' },
        { code: 'SMI', description: 'ScotiaMcLeod' },
        { code: 'CASL', description: 'Private Investment Counsel' },
        { code: 'TRST', description: 'Scotiatrust' },
        { code: 'SPCGIIA', description: 'International Investment Advisory' },
        { code: 'REGL', description: '1832 Asset Management U.S. Inc.' },
      ],
    });
  });

  it('updateApplication - update status returns success', async() => {
    const updatedApp = {
      ...testApp,
      platformIds: [ 2, 3 ], // ios, android
      ruleTypeIds: [ 1, 2 ], // alerts, campaigns
      ruleSubTypeIds: [ 1, 2, 3 ], // targeted, mass campaign, message
      status: 0,
    };
    tracker.on('query', (query, step) => {
      if (step === 1) { // select operation - returns application's rule types & rule sub types
        query.response([
          { admin_rule_type_id: 2, admin_rule_sub_type_id: 1, team_id: 1, status: true },
          { admin_rule_type_id: 2, admin_rule_sub_type_id: 2, team_id: 1, status: true },
        ]);
      } else if ([ 2, 3, 4 ].includes(step)) {
        // _updateApplicationBasicFields update operation - updates application, returns success
        // _updateRuleSubTypes - insert new rule sub type
        // _updatePlatforms - delete platforms, return success
        query.response(1);
      } else if ([ 5, 6, 7, 8, 9 ].includes(step)) {
        // _updatePlatforms - insert platforms
        // _updateContainerPageStatus update operation - updates container status
        // _updateContainerPageStatus update operation - updates page status
        // add new rule type to application - insert into applications_rule_types
        // _removeRuleSubTypeAccessFromPigeon
        query.response(undefined);
      } else if (step === 10) { // _addRuleSubTypeAccessToPigeon - get access levels
        query.response([ { admin_access_id: 1 }, { admin_access_id: 2 } ]);
      } else if (step === 11) { // _addRuleSubTypeAccessToPigeon - insert rule sub types
        query.response(undefined);
      } else if (step === 12) { // getApplication select operation - returns app
        query.response([ {
          ...testApp,
          id: 1,
          status: false,
          rule_version: 1,
          admin_platform_id: 2,
          admin_platform_name: 'iOS',
          admin_rule_type_id: 2,
          admin_rule_type_name: 'campaign',
          admin_rule_sub_type_id: 1,
          admin_rule_sub_type_name: 'targeted',
        }, {
          ...testApp,
          id: 1,
          status: false,
          rule_version: 1,
          admin_platform_id: 2,
          admin_platform_name: 'iOS',
          admin_rule_type_id: 2,
          admin_rule_type_name: 'campaign',
          admin_rule_sub_type_id: 2,
          admin_rule_sub_type_name: 'mass',
        }, {
          ...testApp,
          id: 1,
          status: false,
          rule_version: 1,
          admin_platform_id: 3,
          admin_platform_name: 'Android',
          admin_rule_type_id: 2,
          admin_rule_type_name: 'campaign',
          admin_rule_sub_type_id: 1,
          admin_rule_sub_type_name: 'targeted',
        }, {
          ...testApp,
          id: 1,
          status: false,
          rule_version: 1,
          admin_platform_id: 3,
          admin_platform_name: 'Android',
          admin_rule_type_id: 2,
          admin_rule_type_name: 'campaign',
          admin_rule_sub_type_id: 2,
          admin_rule_sub_type_name: 'mass',
        } ]);
      }
    });

    const response = await ApplicationService(mockDb).updateApplication(1, updatedApp);
    expect(response).toStrictEqual({
      ...testApp,
      id: 1,
      status: false,
      rule_version: 1,
      platforms: [ 'iOS', 'Android' ],
      platformIds: [ 2, 3 ],
      ruleTypes: [ 'campaign' ],
      ruleTypeIds: [ 2 ],
      ruleSubTypes: [ 'targeted', 'mass' ],
      ruleSubTypeIds: [ 1, 2 ],
      wealth_lobs: [
        { code: 'SDBI', description: 'Scotia iTRADE' },
        { code: 'SMI', description: 'ScotiaMcLeod' },
        { code: 'CASL', description: 'Private Investment Counsel' },
        { code: 'TRST', description: 'Scotiatrust' },
        { code: 'SPCGIIA', description: 'International Investment Advisory' },
        { code: 'REGL', description: '1832 Asset Management U.S. Inc.' },
      ],
    });
  });

  it('setApplicationStatus', async() => {
    const dbResponse = [ {
      ...testApp,
      id: 1,
      status: true,
      rule_version: 1,
      admin_platform_name: 'Web',
      admin_rule_type_name: 'campaign',
      admin_platform_id: 1,
      admin_rule_type_id: 2,
      admin_rule_sub_type_id: 3,
      admin_rule_sub_type_name: 'message',
    } ];
    tracker.on('query', (query, step) => {
      if (step === 1 || step === 2) { // _updateContainerPageStatus update operation - updates container, page status
        query.response(undefined);
      } else if (step === 3) { // update operation - updates application status
        query.response(undefined);
      } else if (step === 4) { // getApplication - returns app
        query.response(dbResponse);
      }
    });

    const response = await ApplicationService(mockDb).setApplicationStatus(1, true, { activateChildren: 'true' });
    expect(response).toStrictEqual({
      ...testApp,
      id: 1,
      status: true,
      rule_version: 1,
      platforms: [ 'Web' ],
      platformIds: [ 1 ],
      ruleTypes: [ 'campaign' ],
      ruleTypeIds: [ 2 ],
      ruleSubTypes: [ 'message' ],
      ruleSubTypeIds: [ 3 ],
      wealth_lobs: [
        { code: 'SDBI', description: 'Scotia iTRADE' },
        { code: 'SMI', description: 'ScotiaMcLeod' },
        { code: 'CASL', description: 'Private Investment Counsel' },
        { code: 'TRST', description: 'Scotiatrust' },
        { code: 'SPCGIIA', description: 'International Investment Advisory' },
        { code: 'REGL', description: '1832 Asset Management U.S. Inc.' },
      ],
    });
  });

  it('deleteApplication', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // select operation - returns app
        query.response([ {
          ...testApp,
          id: 1,
          status: true,
          rule_version: 1,
          admin_platform_id: 1,
          admin_platform_name: 'Web',
          admin_rule_type_id: 2,
          admin_rule_type_name: 'campaign',
          admin_rule_sub_type_id: 3,
          admin_rule_sub_type_name: 'message',
        } ]);
      } else if (step === 2) { // delete operation - delete application/platform link returns success
        query.response([ 1 ]);
      } else if (step === 3) { // select operation - returns container ids with app id
        query.response([ { admin_container_id: 1 }, { admin_container_id: 2 } ]);
      } else if (step === 4) { // delete operation - delete container/page link, returns success
        query.response([ 1 ]);
      } else if (step === 5) { // delete operation - delete container access, returns success
        query.response([ 1 ]);
      } else if (step === 6) { // delete operation - delete containers, returns success
        query.response([ 1 ]);
      } else if (step === 7) { // select operation - returns page ids with app id
        query.response([ { admin_page_id: 1 }, { admin_page_id: 2 } ]);
      } else if (step === 8) { // delete operation - delete page access, return success
        query.response([ 1 ]);
      } else if (step === 9) { // delete operation - delete pages, return success
        query.response([ 1 ]);
      } else if (step === 10) { // delete operation - delete rule sub type access, return success
        query.response([ 1 ]);
      } else if (step === 11) { // delete operation - delete rule sub type, return success
        query.response([ 1 ]);
      } else if (step === 12) { // delete operation - delete rule type, return success
        query.response([ 1 ]);
      } else if (step === 13) { // delete operation - delete application, return success
        query.response([ 1 ]);
      }
    });

    const response = await ApplicationService(mockDb).deleteApplication(1);
    expect(response).toStrictEqual({
      ...testApp,
      id: 1,
      status: true,
      rule_version: 1,
      platforms: [ 'Web' ],
      platformIds: [ 1 ],
      ruleTypes: [ 'campaign' ],
      ruleTypeIds: [ 2 ],
      ruleSubTypes: [ 'message' ],
      ruleSubTypeIds: [ 3 ],
      wealth_lobs: [
        { code: 'SDBI', description: 'Scotia iTRADE' },
        { code: 'SMI', description: 'ScotiaMcLeod' },
        { code: 'CASL', description: 'Private Investment Counsel' },
        { code: 'TRST', description: 'Scotiatrust' },
        { code: 'SPCGIIA', description: 'International Investment Advisory' },
        { code: 'REGL', description: '1832 Asset Management U.S. Inc.' },
      ],
    });
  });

  describe('Wealth LOB functionality', () => {
    it('should return default LOB configuration for unknown application', async() => {
      const dbResponse = [ {
        ...testApp,
        id: 1,
        status: true,
        rule_version: 1,
        admin_platform_name: 'Web',
        admin_rule_type_name: 'campaign',
        admin_platform_id: 1,
        admin_rule_type_id: 2,
        admin_rule_sub_type_id: 3,
        admin_rule_sub_type_name: 'MASS',
      } ];

      tracker.on('query', (query) => {
        query.response(dbResponse);
      });

      const response = await ApplicationService(mockDb).getApplication(1);

      expect(response.wealth_lobs).toEqual([
        { code: 'SDBI', description: 'Scotia iTRADE' },
        { code: 'SMI', description: 'ScotiaMcLeod' },
        { code: 'CASL', description: 'Private Investment Counsel' },
        { code: 'TRST', description: 'Scotiatrust' },
        { code: 'SPCGIIA', description: 'International Investment Advisory' },
        { code: 'REGL', description: '1832 Asset Management U.S. Inc.' },
      ]);
    });

    it('should return starburst LOB configuration for starburst application', async() => {
      const dbResponse = [ {
        ...testApp,
        applicationId: 'starburst',
        id: 1,
        status: true,
        rule_version: 1,
        admin_platform_name: 'Web',
        admin_rule_type_name: 'campaign',
        admin_platform_id: 1,
        admin_rule_type_id: 2,
        admin_rule_sub_type_id: 3,
        admin_rule_sub_type_name: 'MASS',
      } ];

      tracker.on('query', (query) => {
        query.response(dbResponse);
      });

      const response = await ApplicationService(mockDb).getApplication(1);

      expect(response.wealth_lobs).toEqual([
        { code: 'SDBI', description: 'Scotia iTRADE' },
      ]);
    });

    it('should return atlantis LOB configuration for atlantis application', async() => {
      const dbResponse = [ {
        ...testApp,
        applicationId: 'atlantis',
        id: 1,
        status: true,
        rule_version: 1,
        admin_platform_name: 'Web',
        admin_rule_type_name: 'campaign',
        admin_platform_id: 1,
        admin_rule_type_id: 2,
        admin_rule_sub_type_id: 3,
        admin_rule_sub_type_name: 'MASS',
      } ];

      tracker.on('query', (query) => {
        query.response(dbResponse);
      });

      const response = await ApplicationService(mockDb).getApplication(1);

      expect(response.wealth_lobs).toEqual([
        { code: 'SMI', description: 'ScotiaMcLeod' },
        { code: 'CASL', description: 'Private Investment Counsel' },
        { code: 'TRST', description: 'Scotiatrust' },
        { code: 'SPCGIIA', description: 'International Investment Advisory' },
        { code: 'REGL', description: '1832 Asset Management U.S. Inc.' },
      ]);
    });

    it('should include wealth_lobs in getApplications response', async() => {
      const dbResponse = [ {
        ...testApp,
        applicationId: 'starburst',
        id: 1,
        status: true,
        rule_version: 1,
        admin_platform_name: 'Web',
        admin_rule_type_name: 'campaign',
        admin_platform_id: 1,
        admin_rule_type_id: 2,
        admin_rule_sub_type_id: 3,
        admin_rule_sub_type_name: 'MASS',
      } ];

      tracker.on('query', (query) => {
        query.response(dbResponse);
      });

      const response = await ApplicationService(mockDb).getApplications({ limit: 30, pageNumber: 1 });

      expect(response.items[0].wealth_lobs).toEqual([
        { code: 'SDBI', description: 'Scotia iTRADE' },
      ]);
    });

    it('should return correct LOB mappings with proper codes and descriptions', async() => {
      const dbResponse = [ {
        ...testApp,
        id: 1,
        status: true,
        rule_version: 1,
        admin_platform_name: 'Web',
        admin_rule_type_name: 'campaign',
        admin_platform_id: 1,
        admin_rule_type_id: 2,
        admin_rule_sub_type_id: 3,
        admin_rule_sub_type_name: 'MASS',
      } ];

      tracker.on('query', (query) => {
        query.response(dbResponse);
      });

      const response = await ApplicationService(mockDb).getApplication(1);
      const lobCodes = response.wealth_lobs.map(lob => lob.code);
      const lobDescriptions = response.wealth_lobs.map(lob => lob.description);

      // Verify all expected LOB codes are present
      expect(lobCodes).toContain('SDBI');
      expect(lobCodes).toContain('SMI');
      expect(lobCodes).toContain('CASL');
      expect(lobCodes).toContain('TRST');
      expect(lobCodes).toContain('SPCGIIA');
      expect(lobCodes).toContain('REGL');

      // Verify corresponding descriptions
      expect(lobDescriptions).toContain('Scotia iTRADE');
      expect(lobDescriptions).toContain('ScotiaMcLeod');
      expect(lobDescriptions).toContain('Private Investment Counsel');
      expect(lobDescriptions).toContain('Scotiatrust');
      expect(lobDescriptions).toContain('International Investment Advisory');
      expect(lobDescriptions).toContain('1832 Asset Management U.S. Inc.');
    });

    it('should maintain consistent LOB structure across different methods', async() => {
      const dbResponse = [ {
        ...testApp,
        applicationId: 'atlantis',
        id: 1,
        status: true,
        rule_version: 1,
        admin_platform_name: 'Web',
        admin_rule_type_name: 'campaign',
        admin_platform_id: 1,
        admin_rule_type_id: 2,
        admin_rule_sub_type_id: 3,
        admin_rule_sub_type_name: 'MASS',
      } ];

      tracker.on('query', (query) => {
        query.response(dbResponse);
      });

      const getApplicationResponse = await ApplicationService(mockDb).getApplication(1);
      const getApplicationsResponse = await ApplicationService(mockDb).getApplications({ limit: 30, pageNumber: 1 });

      // Both methods should return the same LOB structure for atlantis
      expect(getApplicationResponse.wealth_lobs).toEqual(getApplicationsResponse.items[0].wealth_lobs);
    });
  });
});
