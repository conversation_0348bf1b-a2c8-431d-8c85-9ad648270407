const {
  TABLE,
  COL_NAME,
  COLUMN,
  SELECT,
  JOIN,
} = require('../constants/db');
const { TRANSFORM_RESULT, TRANSFORM_INPUT, SORT_AND_PAGINATE } = require('../utils/db');
const { flatten, difference, omit } = require('lodash');

const containerService = db => {
  const _getRuleTypeId = async(ruleType) => {
    return (await db(TABLE.rule_type)
      .where(COLUMN.rule_type.rule_type, ruleType)
      .pluck(COLUMN.rule_type.id))[0];
  };

  const _insertContainersPages = async(containerId, pages) => {
    pages.length && await db(TABLE.containers_pages)
      .insert(pages.map(i => ({
        [COL_NAME.containers_pages.container_id]: containerId,
        [COL_NAME.containers_pages.page_id]: i,
      })));
  };

  const _deleteContainersPages = async(containerId) => {
    return db(TABLE.containers_pages)
      .delete()
      .where(COLUMN.containers_pages.container_id, containerId);
  };

  const getContainer = async(query = {}) => {
    // get all the container ids that have specified page
    const containerIdsWithPageId = query.page_id && (await db(TABLE.container)
      .select(COLUMN.container.id)
      .leftJoin(...JOIN.container_containers_pages)
      .where(COLUMN.containers_pages.page_id, query.page_id))
      .map(r => r.admin_container_id);

    const result = await db(TABLE.container)
      .select(
        COLUMN.container,
        SELECT.containers_pages.page_id,
        SELECT.rule_type.rule_type,
        SELECT.application.applicationId,
        SELECT.application.team_id,
      )
      .leftJoin(...JOIN.container_containers_pages)
      .leftJoin(...JOIN.container_rule_type)
      .leftJoin(...JOIN.container_application)
      .where(TRANSFORM_INPUT(query, COLUMN.container))
      .where(builder => {
        if (query.page_id) {
          builder.whereIn(COLUMN.container.id, containerIdsWithPageId);
        }
        if (query.application_id) {
          builder.where(COLUMN.container.application, query.application_id);
        }
        if (query.team_id) {
          builder.where(COLUMN.application.team_id, query.team_id);
        }
        if (query.search) {
          builder.where(innerBuilder => {
            innerBuilder.where(COL_NAME.container.name, 'LIKE', `%${query.search}%`)
              .orWhere(COL_NAME.container.containerId, 'LIKE', `%${query.search}%`);
          });
        }
      });

    result.forEach(r => delete r.rule_type_id);
    const transformedResult = TRANSFORM_RESULT(result, { pages: 'page_id' });
    if (query.id) {
      return transformedResult;
    }
    return SORT_AND_PAGINATE(transformedResult, query);
  };

  const _addContainerAccessToPigeon = async(containerId, ruleTypeId, applicationId) => {
    const accessLevelsRes = await db(TABLE.access_levels).select(COLUMN.access_levels.access_id);
    const accessLevels = accessLevelsRes.map(a => a.admin_access_id);

    accessLevels.length && await db(TABLE.access_containers).insert(
      accessLevels.map((accessLevelId) => ({
        [COL_NAME.access_containers.team_id]: 1, // Pigeon
        [COL_NAME.access_containers.rule_type_id]: ruleTypeId,
        [COL_NAME.access_containers.application_id]: applicationId,
        [COL_NAME.access_containers.container_id]: containerId,
        [COL_NAME.access_containers.active]: 1,
        [COL_NAME.access_containers.access_id]: accessLevelId,
      })),
    );
  };

  const _updateContainerAccessToPigeon = async(
    containerId,
    ruleTypeId,
    applicationId,
    pages,
  ) => {
    const accessLevelsRes = await db(TABLE.access_levels).select(COLUMN.access_levels.access_id);
    const accessLevels = accessLevelsRes.map((a) => a.admin_access_id);

    await db(TABLE.access_containers)
      .delete()
      .where({
        [COL_NAME.access_containers.team_id]: 1, // Pigeon
        [COL_NAME.access_containers.application_id]: applicationId,
        [COL_NAME.access_containers.container_id]: containerId,
      });

    accessLevels.length && await db(TABLE.access_containers).insert(
      accessLevels.map((accessLevelId) => ({
        [COL_NAME.access_containers.team_id]: 1, // Pigeon
        [COL_NAME.access_containers.rule_type_id]: ruleTypeId,
        [COL_NAME.access_containers.application_id]: applicationId,
        [COL_NAME.access_containers.container_id]: containerId,
        [COL_NAME.access_containers.active]: 1,
        [COL_NAME.access_containers.access_id]: accessLevelId,
      })),
    );
    // tech-debt https://jira.agile.bns/browse/PIGEON-4573
    const alreadyExistPages = await db(TABLE.access_pages)
      .select(COL_NAME.access_pages.page_id)
      .where({
        [COL_NAME.access_pages.team_id]: 1,
        [COL_NAME.access_pages.rule_type_id]: ruleTypeId,
        [COL_NAME.access_pages.application_id]: applicationId,
        [COL_NAME.access_pages.active]: 1,
      });

    const pagesToInsert = difference(
      pages,
      alreadyExistPages.map((i) => i[COL_NAME.access_pages.page_id]),
    );

    pagesToInsert.length && accessLevels.length && await db(TABLE.access_pages).insert(
      flatten(
        pagesToInsert.map((pageId) =>
          accessLevels.map((accessLevelId) => ({
            [COL_NAME.access_pages.team_id]: 1, // Pigeon
            [COL_NAME.access_pages.rule_type_id]: ruleTypeId,
            [COL_NAME.access_pages.application_id]: applicationId,
            [COL_NAME.access_pages.page_id]: pageId,
            [COL_NAME.access_pages.active]: 1,
            [COL_NAME.access_pages.access_id]: accessLevelId,
          })),
        ),
      ),
    );
  };

  const createContainer = async(query) => {
    const { pages, rule_type: ruleType, ...containerQuery } = query;
    const ruleTypeId = await _getRuleTypeId(ruleType);
    const { [COL_NAME.container.id]: id } = (await db(TABLE.container)
      .insert({
        [COL_NAME.container.rule_type_id]: ruleTypeId,
        ...TRANSFORM_INPUT(containerQuery, COL_NAME.container),
      })
      .returning(COL_NAME.container.id))[0];
    if (pages) {
      await _insertContainersPages(id, pages);
    }
    await _addContainerAccessToPigeon(id, ruleTypeId, query.application);

    return (await getContainer({ id }))[0];
  };

  const _updateContainerAccess = async(id, oldContainer, newContainer) => {
    if (newContainer.application === oldContainer.application &&
        newContainer.rule_type === oldContainer.rule_type) {
      return;
    }
    const sql = db(TABLE.access_containers)
      .update({
        [COL_NAME.access_containers.rule_type_id]: await _getRuleTypeId(newContainer.rule_type),
        [COL_NAME.access_containers.application_id]: newContainer.application,
      }).where({
        [COL_NAME.access_containers.rule_type_id]: await _getRuleTypeId(oldContainer.rule_type),
        [COL_NAME.access_containers.application_id]: oldContainer.application,
        [COL_NAME.access_containers.container_id]: id,
      });
    await sql;
  };

  const updateContainer = async(id, query, option = {}) => {
    const { pages, rule_type: ruleType, ...containerQuery } = query;
    const oldContainer = (await getContainer({ id }))[0];
    const ruleTypeId = await _getRuleTypeId(ruleType);

    await db(TABLE.container)
      .update({
        [COL_NAME.container.rule_type_id]: ruleTypeId,
        ...TRANSFORM_INPUT(omit(containerQuery, [ 'containerId' ]), omit(COL_NAME.container, [ 'containerId' ])),
      })
      .where(COLUMN.container.id, id);
    await _updateContainerAccess(id, oldContainer, query);
    if (pages) {
      await _deleteContainersPages(id);
      await _insertContainersPages(id, pages);
    }

    await _updateContainerAccessToPigeon(id,
      ruleTypeId,
      query.application,
      pages);

    if (option.noReturn) {
      return;
    }
    return (await getContainer({ id }))[0];
  };

  const setContainerStatus = async(id, active = true) => {
    await db(TABLE.container)
      .update({ [COL_NAME.container.status]: active })
      .where(COL_NAME.container.id, id);
    return (await getContainer({ id }))[0];
  };

  const deleteContainer = async(id) => {
    const result = (await getContainer({ id }))[0];
    await _deleteContainersPages(id);

    await db(TABLE.access_containers)
      .delete()
      .where(COLUMN.access_containers.container_id, id);

    await db(TABLE.container)
      .delete()
      .where(COLUMN.container.id, id);
    return result;
  };

  return {
    getContainer,
    createContainer,
    updateContainer,
    deleteContainer,
    setContainerStatus,
  };
};

module.exports = containerService;
