const qs = require('qs');
const { getRequestHeaders } = require('./common');

const commonReqParams = (config) => ({
  baseURL: config.url,
  headers: getRequestHeaders(),
  timeout: config.timeout,
});

const getVariableMappingSet = ({ axios }, config, id) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/variable-mappings/sets/${id}`,
    method: 'get',
  })
);

const getVariableMappingSets = ({ axios }, config, query) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/variable-mappings/sets${qs.stringify(query, { addQueryPrefix: true })}`,
    method: 'get',
  })
);

const getVariableTypes = ({ axios }, config) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/variable-mappings/types`,
    method: 'get',
  })
);

const updateVariableSet = ({ axios }, config, id, data) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/variable-mappings/sets/${id}`,
    method: 'patch',
    data,
  })
);

const init = ({ axios }, config) => {
  return {
    getVariableMappingSets: (status) => getVariableMappingSets({ axios }, config, status),
    getVariableMappingSet: (id) => getVariableMappingSet({ axios }, config, id),
    getVariableTypes: () => getVariableTypes({ axios }, config),
    updateVariableSet: (id, data) => updateVariableSet({ axios }, config, id, data),
  };
};

module.exports = init;
