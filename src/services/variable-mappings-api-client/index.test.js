const qs = require('qs');
const VariableMappingsApiClient = require('.');

// mock axios
const mockRequest = jest.fn();
const axios = {
  request: mockRequest,
};
// mock config
const config = {
  url: 'http://localhost/v1',
};

describe('Rules API client', () => {
  test('should have getVariableMappingSet method', () => {
    const client = VariableMappingsApiClient({ axios }, config);
    expect(client).toHaveProperty('getVariableMappingSet');
    expect(typeof client.getVariableMappingSet).toEqual('function');
  });

  test('should have getVariableMappingSets method', () => {
    const client = VariableMappingsApiClient({ axios }, config);
    expect(client).toHaveProperty('getVariableMappingSets');
    expect(typeof client.getVariableMappingSets).toEqual('function');
  });

  test('should have getVariableTypes method', () => {
    const client = VariableMappingsApiClient({ axios }, config);
    expect(client).toHaveProperty('getVariableTypes');
    expect(typeof client.updateVariableSet).toEqual('function');
  });

  test('should have updateVariableSet method', () => {
    const client = VariableMappingsApiClient({ axios }, config);
    expect(client).toHaveProperty('updateVariableSet');
    expect(typeof client.updateVariableSet).toEqual('function');
  });

  test('should successfully call getVariableMappingSet', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };
    mockRequest.mockReturnValueOnce(fakeResponse);
    const client = VariableMappingsApiClient({ axios }, config);
    const result = await client.getVariableMappingSet(1);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/variable-mappings/sets/1`);
    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call getVariableMappingSets', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };
    const mockQuery = {
      'item1': 'value1',
      'item2': 'value2',
      'item3': 'value3',
    };
    mockRequest.mockReturnValueOnce(fakeResponse);
    const client = VariableMappingsApiClient({ axios }, config);
    const result = await client.getVariableMappingSets(mockQuery);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/variable-mappings/sets?${qs.stringify(mockQuery)}`);
    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call getVariableTypes', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };
    mockRequest.mockReturnValueOnce(fakeResponse);
    const client = VariableMappingsApiClient({ axios }, config);
    const result = await client.getVariableTypes();
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/variable-mappings/types`);
    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call updateVariableSet', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };
    const mockId = 123;
    const mockData = {};
    mockRequest.mockReturnValueOnce(fakeResponse);
    const client = VariableMappingsApiClient({ axios }, config);
    const result = await client.updateVariableSet(mockId, mockData);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/variable-mappings/sets/${mockId}`);
    expect(result).toEqual(fakeResponse);
  });
});
