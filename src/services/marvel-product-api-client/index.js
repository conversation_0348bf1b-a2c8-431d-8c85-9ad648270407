const { getRequestHeaders } = require('./common');

const commonReqParams = (config, isProductAtlasUrlEnabled) => ({
  baseURL: isProductAtlasUrlEnabled ? config.atlasUrl : config.url,
  headers: getRequestHeaders(),
  timeout: config.timeout,
});

const ldFlagProduct = 'pigeon.downstreams.product-atlas';

const getProducts = async({ axios }, config, launchDarklyService, logger) => {
  let isProductAtlasUrlEnabled = false;
  try {
    isProductAtlasUrlEnabled = await launchDarklyService.getValue(ldFlagProduct, false);
  } catch (err) {
    logger.error({
      message: 'Error calling Launch Darkly',
      details: { err, flag: ldFlagProduct },
    });
  }

  return axios.request({
    ...commonReqParams(config, isProductAtlasUrlEnabled),
    url: `/v2/product`,
    method: 'get',
  });
};

const init = ({ axios }, config, launchDarklyService, logger) => {
  return {
    getProducts: () => getProducts({ axios }, config, launchDarklyService, logger),
  };
};

module.exports = init;
