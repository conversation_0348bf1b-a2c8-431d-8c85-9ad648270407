const MarvelProductClient = require('.');

// mock axios
const mockRequest = jest.fn();
const axios = { request: mockRequest };
const config = { url: 'http://localhost/v2', atlasUrl: 'http://atlas/v2' };

const mockGetValue = jest.fn();
const launchDarklyService = { getValue: mockGetValue };

const mockLogger = { error: jest.fn() };

describe('Marvel Product API client', () => {
  test('should have getProducts method', () => {
    const client = MarvelProductClient({ axios }, config, launchDarklyService, mockLogger);
    expect(client).toHaveProperty('getProducts');
    expect(typeof client.getProducts).toEqual('function');
  });

  test('should successfully call getProducts', async() => {
    const mockResponse = {
      status: 200,
      data: {},
    };
    mockRequest.mockReturnValueOnce(mockResponse);
    mockGetValue.mockResolvedValue(false);
    const client = MarvelProductClient({ axios }, config, launchDarklyService, mockLogger);
    const result = await client.getProducts();
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v2/product`);
    expect(mockRequest.mock.calls[0][0].baseURL).toEqual(config.url);
    expect(mockGetValue).toHaveBeenCalledWith('pigeon.downstreams.product-atlas', false);
    expect(mockLogger.error).not.toHaveBeenCalled();
    expect(result).toEqual(mockResponse);
  });
});
