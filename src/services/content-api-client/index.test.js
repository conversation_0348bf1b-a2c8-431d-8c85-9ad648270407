const ContentApiCilent = require('.');

let mockResponseData = {};

const mockRequest = jest.fn().mockImplementation(() => {
  return new Promise((resolve, reject) => {
    resolve({
      data: mockResponseData,
    });
  });
});

const axios = {
  request: mockRequest,
};

const config = {
  url: 'http://localhost/v1',
};

describe('Content API client', () => {
  test('should have getContentTypes method', () => {
    const client = ContentApiCilent({ axios }, config);
    expect(client).toHaveProperty('getContentTypes');
    expect(typeof client.getContentTypes).toEqual('function');
  });

  test('should have getContentTypeDetails method', () => {
    const client = ContentApiCilent({ axios }, config);
    expect(client).toHaveProperty('getContentTypeDetails');
    expect(typeof client.getContentTypes).toEqual('function');
  });

  test('should have getContents method', () => {
    const client = ContentApiCilent({ axios }, config);
    expect(client).toHaveProperty('getContents');
    expect(typeof client.getContentTypes).toEqual('function');
  });

  test('should have getContentDetails method', () => {
    const client = ContentApiCilent({ axios }, config);
    expect(client).toHaveProperty('getContentDetails');
    expect(typeof client.getContentTypes).toEqual('function');
  });

  test('should have getContentsVignette method', () => {
    const client = ContentApiCilent({ axios }, config);
    expect(client).toHaveProperty('getContentsVignette');
    expect(typeof client.getContentTypes).toEqual('function');
  });

  test('should have getContentDetailsVignette method', () => {
    const client = ContentApiCilent({ axios }, config);
    expect(client).toHaveProperty('getContentDetailsVignette');
    expect(typeof client.getContentDetailsVignette).toEqual('function');
  });

  test('should successfully call getContentTypes', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };

    mockResponseData = fakeResponse;
    const client = ContentApiCilent({ axios }, config);
    const spaceId = '123';

    const result = await client.getContentTypes(spaceId);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/contents/spaces/${spaceId}/types`);
    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call getContentTypeDetails', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };

    mockResponseData = fakeResponse;
    const client = ContentApiCilent({ axios }, config);
    const spaceId = '123';
    const typeId = '123';

    const result = await client.getContentTypeDetails(spaceId, typeId);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/contents/spaces/${spaceId}/types/${typeId}`);
    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call getContents', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };

    mockResponseData = fakeResponse;
    const client = ContentApiCilent({ axios }, config);
    const spaceId = '123';
    const typeId = '123';
    const query = {
      'item1': 'value1',
      'item2': 'value2',
    };

    const result = await client.getContents(spaceId, typeId, query);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);

    const requestConfig = mockRequest.mock.calls[0][0];
    // Assert URL contains no query string
    expect(requestConfig.url).toEqual(`/contents/spaces/${spaceId}/types/${typeId}/contents`);
    // Assert params property contains the query object
    expect(requestConfig.params).toEqual(query);

    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call getContentDetails', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };

    mockResponseData = fakeResponse;
    const client = ContentApiCilent({ axios }, config);
    const spaceId = '123';
    const typeId = '123';
    const contentId = '123';
    const query = {
      'item1': 'value1',
      'item2': 'value2',
    };

    const result = await client.getContentDetails(spaceId, typeId, contentId, query);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);

    const requestConfig = mockRequest.mock.calls[0][0];
    // Assert URL contains no query string
    expect(requestConfig.url).toEqual(`/contents/spaces/${spaceId}/types/${typeId}/contents/${contentId}`);
    // Assert params property contains the query object
    expect(requestConfig.params).toEqual(query);
    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call getContents', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };

    mockResponseData = fakeResponse;
    const client = ContentApiCilent({ axios }, config);
    const spaceId = '123';
    const typeId = '123';
    const query = {
      'item1': 'value1',
      'item2': 'value2',
    };

    const result = await client.getContents(spaceId, typeId, query);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);

    const requestConfig = mockRequest.mock.calls[0][0];

    expect(requestConfig.url).toEqual(`/contents/spaces/${spaceId}/types/${typeId}/contents`);
    // Assert params property contains the query object
    expect(requestConfig.params).toEqual(query);
    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call getContentsVignette', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };

    mockResponseData = fakeResponse;
    const client = ContentApiCilent({ axios }, config);
    const query = {
      'item1': 'value1',
      'item2': 'value2',
    };

    const result = await client.getContentsVignette(query);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);

    const requestConfig = mockRequest.mock.calls[0][0];
    // Assert URL contains no query string
    expect(requestConfig.url).toEqual(`/vignette/contents`);
    // Assert params property contains the query object
    expect(requestConfig.params).toEqual(query);
    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call getContentDetailsVignette', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };

    mockResponseData = fakeResponse;
    const client = ContentApiCilent({ axios }, config);
    const contentId = '123';

    const result = await client.getContentDetailsVignette(contentId);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/vignette/contents/${contentId}`);
    expect(result).toEqual(fakeResponse);
  });
});
