const { getRequestHeaders } = require('./common');

const commonReqParams = (config) => ({
  baseURL: config.url,
  headers: getRequestHeaders(),
  method: 'get',
  data: null,
  timeout: config.timeout,
});

const getContentDetailsVignette = async({ axios }, config, contentId) => {
  const res = await axios.request({
    ...commonReqParams(config),
    url: `/vignette/contents/${contentId}`,
  });
  return res.data;
};

const getContentDetails = async({ axios }, config, spaceId, typeId, contentId, query) => {
  const requestConfig = {
    ...commonReqParams(config),
    url: `/contents/spaces/${spaceId}/types/${typeId}/contents/${contentId}`,
    // Let axios handle params serialization, new change as part of Axios version upgrade from v0.21.4 to v1.9.0
    params: query,
  };

  const res = await axios.request(requestConfig);
  return res.data;
};

const getContentTypeDetails = async({ axios }, config, spaceId, typeId) => {
  const res = await axios.request({
    ...commonReqParams(config),
    url: `/contents/spaces/${spaceId}/types/${typeId}`,
  });
  return res.data;
};

const getContentTypes = async({ axios }, config, spaceId) => {
  const res = await axios.request({
    ...commonReqParams(config),
    url: `/contents/spaces/${spaceId}/types`,
  });
  return res.data;
};

const getContentsVignette = async({ axios }, config, query) => {
  const requestConfig = {
    ...commonReqParams(config),
    url: `/vignette/contents`,
    // Let axios handle params serialization, new change as part of Axios version upgrade from v0.21.4 to v1.9.0
    params: query,
  };

  const res = await axios.request(requestConfig);
  return res.data;
};

const getStaticImageVignette = async({ axios }, config, query) => {
  const res = await axios.request({
    ...commonReqParams(config),
    url: `/vignette/file/${query}`,
    responseType: 'arraybuffer',
  });
  return Buffer.from(res.data, 'binary');
};

const getContents = async({ axios }, config, spaceId, typeId, query) => {
  const requestConfig = {
    ...commonReqParams(config),
    url: `/contents/spaces/${spaceId}/types/${typeId}/contents`,
    // Let axios handle params serialization, new change as part of Axios version upgrade from v0.21.4 to v1.9.0
    params: query,
  };

  const res = await axios.request(requestConfig);
  return res.data;
};

const getContentsByIds = async({ axios }, config, spaceId, query) => {
  const requestConfig = {
    ...commonReqParams(config),
    url: `/contents/spaces/${spaceId}/contents`,
    // Let axios handle params serialization, new change as part of Axios version upgrade from v0.21.4 to v1.9.0
    params: query,
  };

  const res = await axios.request(requestConfig);
  return res.data;
};

const getContentLocales = async({ axios }, config, spaceId) => {
  const res = await axios.request({
    ...commonReqParams(config),
    url: `/contents/spaces/${spaceId}/locales`,
  });
  return res.data;
};

const init = ({ axios }, config) => {
  return {
    getContentTypes: (spaceId) => getContentTypes({ axios }, config, spaceId),
    getContentTypeDetails: (spaceId, typeId) => getContentTypeDetails({ axios }, config, spaceId, typeId),
    getContents: (spaceId, typeId, query) => getContents({ axios }, config, spaceId, typeId, query),
    getContentsByIds: (spaceId, query) => getContentsByIds({ axios }, config, spaceId, query),
    getContentDetails: (spaceId, typeId, contentId, query) => getContentDetails({ axios }, config, spaceId, typeId, contentId, query),
    getContentLocales: (spaceId) => getContentLocales({ axios }, config, spaceId),
    getContentsVignette: (query) => getContentsVignette({ axios }, config, query),
    getStaticImageVignette: (query) => getStaticImageVignette({ axios }, config, query),
    getContentDetailsVignette: (contentId) => getContentDetailsVignette({ axios }, config, contentId),
  };
};

module.exports = init;
