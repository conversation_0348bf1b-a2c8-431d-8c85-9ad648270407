const knex = require('knex');
const mockKnex = require('mock-knex');

const RoleService = require('./role-service');
const { COL_NAME } = require('../constants/db');

const mockDb = knex({ client: 'mssql', debug: false });
const tracker = mockKnex.getTracker();

const baseRole = {
  name: 'test-name',
  status: true,
  team_id: 1,
};

// const testQuery = { ...testQueryWithoutPermissions, permissions: [ 'admin' ] };

describe('Role service', () => {
  beforeAll(() => {
    mockKnex.mock(mockDb);
  });
  afterAll(() => {
    mockKnex.unmock(mockDb);
  });
  beforeEach(() => {
    tracker.install();
  });
  afterEach(() => {
    tracker.uninstall();
  });

  it('createRole', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // insert operation - returns role id
        query.response([ { [COL_NAME.role.id]: 1 } ]);
      } else if (step === 2) { // get teamsPermissions
        query.response([ { permission_name: 'campaigns_view' }, { permission_name: 'alerts_view' } ]);
      } else if (step === 3) { // _insertRolesPermissionsRole
        query.response(undefined);
      } else if (step === 4) { // getRole
        query.response([ { ...baseRole, id: 1, permission_name: 'campaigns_view' }, { ...baseRole, id: 1, permission_name: 'alerts_view' } ]);
      }
    });
    const newRole = {
      ...baseRole,
      permissions: [ 'campaigns_view', 'alerts_view' ],
    };
    const response = await RoleService(mockDb).createRole(newRole);
    expect(response).toStrictEqual({ ...newRole, id: 1 });
  });

  it('getRole with team sort ascending', async() => {
    tracker.on('query', (query) => {
      expect(query.sql).toContain('admin_team_name');
      query.response([ { ...baseRole, id: 1, permission_name: 'campaigns_view', admin_team_name: 'Team A' } ]);
    });

    const query = { id: 1, sort: 'team' };
    const response = await RoleService(mockDb).getRole(query);
    expect(response).toBeDefined();
  });

  it('getRole with team sort descending', async() => {
    tracker.on('query', (query) => {
      expect(query.sql).toContain('admin_team_name');
      query.response([ { ...baseRole, id: 1, permission_name: 'campaigns_view', admin_team_name: 'Team Z' } ]);
    });

    const query = { id: 1, sort: '-team' };
    const response = await RoleService(mockDb).getRole(query);
    expect(response).toBeDefined();
  });

  it('updateRole', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // update operation - returns success
        query.response(1);
      } else if (step === 2) { // _deleteRolesPermissions - returns success
        query.response(1);
      } else if (step === 3) { // get teamsPermissions
        query.response([ { permission_name: 'campaigns_view' }, { permission_name: 'alerts_view' } ]);
      } else if (step === 4) { // _insertRolesPermissions
        query.response(undefined);
      } else if (step === 5) { // getRole
        query.response([ { ...baseRole, id: 1, permission_name: 'campaigns_view' }, { ...baseRole, id: 1, permission_name: 'alerts_view' } ]);
      }
    });
    const updatedRole = {
      ...baseRole,
      id: 1,
      permissions: [ 'campaigns_view', 'alerts_view' ],
    };
    const response = await RoleService(mockDb).updateRole(1, updatedRole);
    expect(response).toStrictEqual(updatedRole);
  });

  it('updateRole (no return)', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // update operation - returns success
        query.response(1);
      } else if (step === 2) { // _deleteRolesPermissions - returns success
        query.response(1);
      } else if (step === 3) { // get teamsPermissions
        query.response([ { permission_name: 'campaigns_view' }, { permission_name: 'alerts_view' } ]);
      } else if (step === 4) { // _insertRolesPermissions
        query.response(undefined);
      }
    });
    const updatedRole = {
      ...baseRole,
      id: 1,
      permissions: [ 'campaigns_view', 'alerts_view' ],
    };
    const response = await RoleService(mockDb).updateRole(1, updatedRole, { noReturn: true });
    expect(response).toBeUndefined();
  });

  it('setRoleStatusActive', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // update opertaion - set role status
        query.response(1);
      } else if (step === 2) { // getRole
        query.response([ { ...baseRole, id: 1, permission_name: 'campaigns_view' }, { ...baseRole, id: 1, permission_name: 'alerts_view' } ]);
      }
    });
    const updatedRole = {
      ...baseRole,
      id: 1,
      permissions: [ 'campaigns_view', 'alerts_view' ],
    };
    const response = await RoleService(mockDb).setRoleStatusActive(1, true);
    expect(response).toStrictEqual([ updatedRole ]);
  });

  it('deleteRole', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // getRole
        query.response([ { ...baseRole, id: 1, permission_name: 'campaigns_view' }, { ...baseRole, id: 1, permission_name: 'alerts_view' } ]);
      } else if ([ 2, 3, 4 ].includes(step)) { // delete operations - role/permission ref, user/role ref role
        query.response(1);
      }
    });
    const deletedRole = {
      ...baseRole,
      id: 1,
      permissions: [ 'campaigns_view', 'alerts_view' ],
    };
    const response = await RoleService(mockDb).deleteRole(1);
    expect(response).toStrictEqual(deletedRole);
  });
});
