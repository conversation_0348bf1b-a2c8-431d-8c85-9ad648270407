const { TABLE, COLUMN, JO<PERSON> } = require('../constants/db');
const { mapJoinTables } = require('../constants/dbMappingValidation');

const validationService = db => {
  const checkExistence = async(key, queries, values) => {
    const sqlStatement = db(TABLE[queries[0].table])
      .select(COLUMN[queries[0].table].id, COLUMN[queries[0].table][queries[0].column])
      .where(COLUMN[queries[0].table][queries[0].column], values[0]);

    if (queries.length > 1) {
      const joinTable = mapJoinTables[key];
      for (let i = 1; i < queries.length; i++) {
        if (joinTable[i - 1].join) {
          sqlStatement.leftJoin(...JOIN[joinTable[i - 1].joinKeyword]);
        }
        sqlStatement.select(COLUMN[queries[i].table][queries[i].column]);
        sqlStatement.where(COLUMN[queries[i].table][queries[i].column], values[i]);
      }
      sqlStatement.limit(1);
    }
    const result = await sqlStatement;
    return !!result.length;
  };
  return {
    checkExistence,
  };
};

module.exports = validationService;
