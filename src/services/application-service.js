const { COLUMN, TABLE, COL_NAME, JOIN, SELECT } = require('../constants/db');
const { TRANSFORM_INPUT, TRANSFORM_RESULT, SORT_AND_PAGINATE } = require('../utils/db');
const { APPLICATION_OWNER_PERMISSION_LIST, APPLICATION_VIEWER_PERMISSION_LIST } = require('../permissions/permission-constants');
const { difference, flattenDeep } = require('lodash');

const applicationService = (db) => {
  const getApplicationByName = async(applicationName) => {
    let result = await db(TABLE.application)
      .select(
        COLUMN.application,
        COLUMN.rule_type.id,
        COLUMN.rule_type.rule_type,
      )
      .leftJoin(...JOIN.applications_rule_types)
      .leftJoin(...JOIN.applications_rule_types_rule_type)
      .where(COLUMN.application.applicationId, applicationName);

    const transformedResult = TRANSFORM_RESULT(result, {
      ruleTypes: COL_NAME.rule_type.rule_type,
      ruleTypeIds: COL_NAME.rule_type.id,
    }, true)[0];
    return transformedResult;
  };

  const getApplication = async(id) => {
    let result = await db(TABLE.application)
      .select(
        COLUMN.application,
        COLUMN.platform.id,
        COLUMN.platform.name,
        COLUMN.rule_type.id,
        COLUMN.rule_type.rule_type,
        COLUMN.rule_sub_type.id,
        COLUMN.rule_sub_type.name,
      )
      .leftJoin(...JOIN.applications_platforms)
      .leftJoin(...JOIN.applications_platforms_platform)
      .leftJoin(...JOIN.applications_rule_types)
      .leftJoin(...JOIN.applications_rule_types_rule_type)
      .leftJoin(...JOIN.applications_rule_sub_types)
      .leftJoin(...JOIN.applications_rule_sub_types_rule_sub_type)
      .where(COLUMN.application.id, id);

    const transformedResult = TRANSFORM_RESULT(result, {
      platforms: COL_NAME.platform.name,
      platformIds: COL_NAME.platform.id,
      ruleTypes: COL_NAME.rule_type.rule_type,
      ruleTypeIds: COL_NAME.rule_type.id,
      ruleSubTypes: COL_NAME.rule_sub_type.name,
      ruleSubTypeIds: COL_NAME.rule_sub_type.id,
    }, true)[0];

    const getWealthLOBs = (applicationId) => {
      const lobConfigs = {
        'starburst': [
          { code: 'SDBI', description: 'Scotia iTRADE' },
        ],
        'atlantis': [
          { code: 'SMI', description: 'ScotiaMcLeod' },
          { code: 'CASL', description: 'Private Investment Counsel' },
          { code: 'TRST', description: 'Scotiatrust' },
          { code: 'SPCGIIA', description: 'International Investment Advisory' },
          { code: 'REGL', description: '1832 Asset Management U.S. Inc.' },
        ],
      };

      return lobConfigs[applicationId] || [
        { code: 'SDBI', description: 'Scotia iTRADE' },
        { code: 'SMI', description: 'ScotiaMcLeod' },
        { code: 'CASL', description: 'Private Investment Counsel' },
        { code: 'TRST', description: 'Scotiatrust' },
        { code: 'SPCGIIA', description: 'International Investment Advisory' },
        { code: 'REGL', description: '1832 Asset Management U.S. Inc.' },
      ];
    };

    transformedResult.wealth_lobs = getWealthLOBs(transformedResult.applicationId);
    return transformedResult;
  };

  const getApplications = async({ flag, ...query }) => {
    if (flag === 'baseFields') {
      const result = await db(TABLE.application)
        .select(
          SELECT.application.name,
          SELECT.application.id,
          SELECT.application.team_id,
          SELECT.application.status,
        )
        .where(TRANSFORM_INPUT(query, COLUMN.application));
      return SORT_AND_PAGINATE(result, query);
    } else {
      const result = await db(TABLE.application)
        .select(
          COLUMN.application,
          COLUMN.platform.id,
          COLUMN.platform.name,
          COLUMN.rule_type.rule_type,
          COLUMN.rule_type.id,
          COLUMN.rule_sub_type.id,
          COLUMN.rule_sub_type.name,
          COLUMN.team.name,
        )
        .leftJoin(...JOIN.applications_platforms)
        .leftJoin(...JOIN.applications_platforms_platform)
        .leftJoin(...JOIN.applications_rule_types)
        .leftJoin(...JOIN.applications_rule_types_rule_type)
        .leftJoin(...JOIN.applications_rule_sub_types)
        .leftJoin(...JOIN.applications_rule_sub_types_rule_sub_type)
        .leftJoin(...JOIN.applications_teams)
        .where(TRANSFORM_INPUT(query, COLUMN.application))
        .where(builder => {
          if (query.search) {
            builder.where(innerBuilder => {
              innerBuilder.where(COL_NAME.application.name, 'LIKE', `%${query.search}%`)
                .orWhere(COL_NAME.application.applicationId, 'LIKE', `%${query.search}%`);
            });
          }
        });

      let transformedResult = TRANSFORM_RESULT(result, {
        platforms: COL_NAME.platform.name,
        platformIds: COL_NAME.platform.id,
        ruleTypes: COL_NAME.rule_type.rule_type,
        ruleTypeIds: COL_NAME.rule_type.id,
        ruleSubTypes: COL_NAME.rule_sub_type.name,
        ruleSubTypeIds: COL_NAME.rule_sub_type.id,
        team: COL_NAME.team.name,
      }, true);

      const getWealthLOBs = (applicationId) => {
        const lobConfigs = {
          'starburst': [
            { code: 'SDBI', description: 'Scotia iTRADE' },
          ],
          'atlantis': [
            { code: 'SMI', description: 'ScotiaMcLeod' },
            { code: 'CASL', description: 'Private Investment Counsel' },
            { code: 'TRST', description: 'Scotiatrust' },
            { code: 'SPCGIIA', description: 'International Investment Advisory' },
            { code: 'REGL', description: '1832 Asset Management U.S. Inc.' },
          ],
        };

        return lobConfigs[applicationId] || [
          { code: 'SDBI', description: 'Scotia iTRADE' },
          { code: 'SMI', description: 'ScotiaMcLeod' },
          { code: 'CASL', description: 'Private Investment Counsel' },
          { code: 'TRST', description: 'Scotiatrust' },
          { code: 'SPCGIIA', description: 'International Investment Advisory' },
          { code: 'REGL', description: '1832 Asset Management U.S. Inc.' },
        ];
      };

      // Add wealth_lobs to each application
      transformedResult = transformedResult.map(app => ({
        ...app,
        wealth_lobs: getWealthLOBs(app.applicationId),
      }));

      if (query.platform_id) {
        transformedResult = transformedResult.filter(app => app.platformIds.includes(parseInt(query.platform_id)));
      }
      if (query.rule_type_id) {
        transformedResult = transformedResult.filter(app => app.ruleTypeIds.includes(parseInt(query.rule_type_id)));
      }
      return SORT_AND_PAGINATE(transformedResult, query);
    }
  };

  const _updateApplicationBasicFields = async(appId, data) => {
    return db(TABLE.application)
      .update({
        [COL_NAME.application.name]: data.name,
        [COL_NAME.application.rule_version]: data.rule_version,
        [COL_NAME.application.description]: data.description,
        [COL_NAME.application.status]: data.status,
        [COL_NAME.application.contentful_space]: data.contentful_space,
        [COL_NAME.application.team_id]: data.team_id,
      })
      .where(COL_NAME.application.id, appId);
  };

  const _deleteRuleTypes = async(appId, removedRuleTypes) => {
    // delete application's rule type access (FK constraints)
    const deleteAccessContainers = removedRuleTypes.map(id => {
      return db(TABLE.access_containers)
        .delete()
        .where({
          [COLUMN.access_containers.application_id]: appId,
          [COLUMN.access_containers.rule_type_id]: id,
        });
    });
    await Promise.all(deleteAccessContainers);

    const deleteAccessPages = removedRuleTypes.map(id => {
      return db(TABLE.access_pages)
        .delete()
        .where({
          [COLUMN.access_pages.application_id]: appId,
          [COLUMN.access_pages.rule_type_id]: id,
        });
    });
    await Promise.all(deleteAccessPages);

    const deleteAccessRuleSubTypes = removedRuleTypes.map(id => {
      return db(TABLE.access_rule_sub_types)
        .delete()
        .where({
          [COLUMN.access_rule_sub_types.application_id]: appId,
          [COLUMN.access_rule_sub_types.rule_type_id]: id,
        });
    });
    await Promise.all(deleteAccessRuleSubTypes);

    const deleteRuleTypes = removedRuleTypes.map(id => {
      return db(TABLE.applications_rule_types)
        .delete()
        .where({
          [COLUMN.applications_rule_types.application_id]: appId,
          [COLUMN.applications_rule_types.rule_type_id]: id,
        });
    });
    await Promise.all(deleteRuleTypes);
  };

  const _updateRuleSubTypes = async(appId, reqRuleSubTypes, targetAppRuleSubTypes) => {
    const addedRuleSubTypes = difference(reqRuleSubTypes, targetAppRuleSubTypes);
    const removedRuleSubTypes = difference(targetAppRuleSubTypes, reqRuleSubTypes);
    if (removedRuleSubTypes.length) {
      // delete application's rule sub type access (FK constraints)
      const deleteAccessRuleSubTypes = removedRuleSubTypes.map(id => {
        return db(TABLE.access_rule_sub_types)
          .delete()
          .where({
            [COLUMN.access_rule_sub_types.application_id]: appId,
            [COLUMN.access_rule_sub_types.rule_sub_type_id]: id,
          });
      });
      await Promise.all(deleteAccessRuleSubTypes);

      // delete application's rule sub types
      const deleteRuleSubTypes = removedRuleSubTypes.map(id => {
        return db(TABLE.applications_rule_sub_types)
          .delete()
          .where({
            [COLUMN.applications_rule_sub_types.application_id]: appId,
            [COLUMN.applications_rule_sub_types.rule_sub_type_id]: id,
          });
      });
      await Promise.all(deleteRuleSubTypes);
    }

    if (addedRuleSubTypes.length) {
      // insert application's new rule sub types
      await db(TABLE.applications_rule_sub_types).insert(
        addedRuleSubTypes.map(id => ({
          [COL_NAME.applications_rule_sub_types.rule_sub_type_id]: id,
          [COL_NAME.applications_rule_sub_types.application_id]: appId,
        })),
      );
    }
  };

  const _updatePlatforms = async(appId, platformIds) => {
    const platformEntries = platformIds.map(platformId => ({
      [COL_NAME.applications_platforms.platform_id]: platformId,
      [COL_NAME.applications_platforms.application_id]: appId,
    }));
    // flush platforms
    await db(TABLE.applications_platforms)
      .delete()
      .where(COL_NAME.applications_platforms.application_id, appId);
    platformEntries.length && await db(TABLE.applications_platforms).insert(platformEntries);
  };

  const _updateContainerPageStatus = async(appId, status) => {
    await db(TABLE.container)
      .update(COL_NAME.container.status, status)
      .where(COLUMN.container.application, appId);
    return db(TABLE.page)
      .update(COL_NAME.page.status, status)
      .where(COLUMN.page.application, appId);
  };

  const _insertRolesPermissions = async(permissions, roleId, teamId) => {
    permissions.length && await db(TABLE.roles_permissions).insert(
      permissions.map((permission) => ({
        [COL_NAME.roles_permissions.role_id]: roleId,
        [COL_NAME.roles_permissions.permission_id]: 0,
        [COL_NAME.roles_permissions.permission_name]: permission,
        [COL_NAME.roles_permissions.team_id]: teamId,
      })),
    );
  };

  /**
   * This function responsible for reconciling the team permissions + owner/viewer roles
   * here is the steps :-
   * 1-  check if the teamId !== previousTeamId because if there is no change of the team id ,we don't need to run these changes
   * 2-  get the current permission this team has ( new one ) where these permissions in this array APPLICATION_OWNER_PERMISSION_LIST
   *     > for example if a team has (pages_view & pages_manage) we should retrieve these permissions
   * 3-  get the missing owner permissions
   *  >  same example we should get (applications_view , applications_manage , containers_view , containers_manage)
   * 4-  get the missing viewer permissions
   *     > same example we should get (applications_view  , containers_view )
   * 5-  insert into teams permissions table the missing owner permissions ( all permissions not only the viewer ones)
   * 6-  get the owner role for this team
   * 7-  insert the owner permissions to this user
   * 8-  get the viewer role for this team
   * 9-  insert the owner permissions to this user
   * 10- if previous team doesn't own any other applications - need to delete the team's and its roles 'own application' permissions
   * @param {string} teamId - the team id associate to the application
   * @param {string} previousTeamId - the previous team id associate to the application
   */
  const _reconcileTeamPermissions = async(teamId, previousTeamId = null) => {
    if (Number(teamId) !== Number(previousTeamId)) {
      // If an application is assigned to a team, that team gets the view/manage permissions for applications/containers/pages
      let currentPermissions = await db(TABLE.teams_permissions)
        .select(COLUMN.teams_permissions.permission_name)
        .where(COLUMN.teams_permissions.team_id, teamId)
        .whereIn(COLUMN.teams_permissions.permission_name, APPLICATION_OWNER_PERMISSION_LIST);

      if (currentPermissions.length) {
        currentPermissions = TRANSFORM_RESULT(currentPermissions, {
          permissions: COL_NAME.teams_permissions.permission_name,
        }, true)[0].permissions;
      }

      const missingOwnerPermissions = difference(APPLICATION_OWNER_PERMISSION_LIST, currentPermissions);
      const missingViewerPermissions = difference(APPLICATION_VIEWER_PERMISSION_LIST, currentPermissions);

      // Add missing permissions to the new team
      missingOwnerPermissions.length && await db(TABLE.teams_permissions).insert(
        missingOwnerPermissions.map((i) => ({
          [COL_NAME.teams_permissions.team_id]: teamId,
          [COL_NAME.teams_permissions.permission_id]: 0,
          [COL_NAME.teams_permissions.permission_name]: i,
        })),
      );

      // Add missing permissions to the new team's owner role
      const ownerRoles = await db(TABLE.role)
        .select(SELECT.role.id)
        .where(COLUMN.role.name, `Team owner ${teamId}`);
      const ownerRoleId = ownerRoles.map((r) => r.id)[0];
      await _insertRolesPermissions(missingOwnerPermissions, ownerRoleId, teamId);

      // Add missing permissions to the new team's owner role
      const viewerRoles = await db(TABLE.role)
        .select(SELECT.role.id)
        .where(COLUMN.role.name, `Viewer ${teamId}`);
      const viewerRoleId = viewerRoles.map((r) => r.id)[0];
      await _insertRolesPermissions(missingViewerPermissions, viewerRoleId, teamId);

      // If previous team doesn't own any other applications - need to delete the team's and its roles 'own application' permissions
      if (previousTeamId) {
        const previousTeamsOtherApplicationsRes = await db(TABLE.application)
          .select(SELECT.application.id)
          .where(COLUMN.application.team_id, previousTeamId);
        const previousTeamsOtherApplications = previousTeamsOtherApplicationsRes.map(({ id }) => id);
        if (!previousTeamsOtherApplications.length) {
          await db(TABLE.roles_permissions)
            .delete()
            .whereIn(COLUMN.roles_permissions.permission_name, APPLICATION_OWNER_PERMISSION_LIST)
            .where(COLUMN.roles_permissions.team_id, previousTeamId);
          await db(TABLE.teams_permissions)
            .delete()
            .whereIn(COLUMN.teams_permissions.permission_name, APPLICATION_OWNER_PERMISSION_LIST)
            .where(COLUMN.teams_permissions.team_id, previousTeamId);
        }
      }
    }
  };

  const _addRuleSubTypeAccessToPigeon = async(applicationId, ruleTypeIds, ruleSubTypeIds) => {
    const accessLevelsRes = await db(TABLE.access_levels).select(COLUMN.access_levels.access_id);
    const accessLevels = accessLevelsRes.map(a => a.admin_access_id);
    const query = flattenDeep(
      ruleTypeIds.map((ruleTypeId) => (
        ruleSubTypeIds.map((ruleSubTypeId) => (
          accessLevels.map((accessLevelId) => ({
            [COL_NAME.access_rule_sub_types.team_id]: 1, // 1 - Pigeon
            [COL_NAME.access_rule_sub_types.rule_type_id]: ruleTypeId, // 2 - Campaign, 7 - CCAU
            [COL_NAME.access_rule_sub_types.application_id]: applicationId,
            [COL_NAME.access_rule_sub_types.rule_sub_type_id]: ruleSubTypeId,
            [COL_NAME.access_rule_sub_types.active]: 1,
            [COL_NAME.access_rule_sub_types.access_id]: accessLevelId, // 1 - View, 2 - Manage
          }))
        ))
      )),
    );
    ruleTypeIds &&
      ruleSubTypeIds.length &&
      accessLevels.length &&
      await db(TABLE.access_rule_sub_types).insert(query);
  };

  const _removeRuleSubTypeAccessFromPigeon = async(appId) => {
    return db(TABLE.access_rule_sub_types)
      .delete()
      .where({
        [COL_NAME.access_rule_sub_types.team_id]: 1, // 1 - Pigeon
        [COL_NAME.access_rule_sub_types.application_id]: appId,
      });
  };

  const createApplication = async(data) => {
    const { [COL_NAME.application.id]: newAppId } = (await db(TABLE.application)
      .insert({
        [COL_NAME.application.applicationId]: data.applicationId,
        [COL_NAME.application.name]: data.name,
        [COL_NAME.application.rule_version]: 1,
        [COL_NAME.application.description]: data.description,
        [COL_NAME.application.status]: data.status,
        [COL_NAME.application.contentful_space]: data.contentful_space,
        [COL_NAME.application.team_id]: data.team_id,
      })
      .returning([ COL_NAME.application.id ]))[0];

    await _reconcileTeamPermissions(data.team_id);

    // now insert into relationship tables
    const platformEntries = data.platformIds.map(id => ({
      [COL_NAME.applications_platforms.platform_id]: id,
      [COL_NAME.applications_platforms.application_id]: newAppId,
    }));
    const ruleTypeEntries = data.ruleTypeIds.map(id => ({
      [COL_NAME.applications_rule_types.rule_type_id]: id,
      [COL_NAME.applications_rule_types.application_id]: newAppId,
    }));
    let ruleSubTypeEntries = [];
    if (data.ruleSubTypeIds.length) {
      ruleSubTypeEntries = data.ruleSubTypeIds.map(id => ({
        [COL_NAME.applications_rule_sub_types.rule_sub_type_id]: id,
        [COL_NAME.applications_rule_sub_types.application_id]: newAppId,
      }));
    }

    await Promise.all([
      db(TABLE.applications_platforms).insert(platformEntries),
      db(TABLE.applications_rule_types).insert(ruleTypeEntries),
      ruleSubTypeEntries.length && db(TABLE.applications_rule_sub_types).insert(ruleSubTypeEntries),
    ]);

    if (data.ruleSubTypeIds) {
      await _addRuleSubTypeAccessToPigeon(newAppId, data.ruleTypeIds, data.ruleSubTypeIds);
    }
    return getApplication(newAppId);
  };

  const updateApplication = async(appId, data) => {
    // get target application that's about to be updated
    let targetApp = await db(TABLE.application)
      .select([
        COLUMN.rule_type.id,
        COLUMN.rule_sub_type.id,
        SELECT.application.team_id,
        SELECT.application.status,
        SELECT.application.rule_version,
      ])
      .leftJoin(...JOIN.applications_rule_types)
      .leftJoin(...JOIN.applications_rule_types_rule_type)
      .leftJoin(...JOIN.applications_rule_sub_types)
      .leftJoin(...JOIN.applications_rule_sub_types_rule_sub_type)
      .where(COLUMN.application.id, appId);
    targetApp = TRANSFORM_RESULT(targetApp, {
      ruleTypes: COL_NAME.rule_type.id,
      ruleSubTypes: COL_NAME.rule_sub_type.id,
    }, true)[0];

    // check if any of rule types ids were removed
    let removedRuleTypes = [];
    // find rule types that are in the targetApp but not in the request data
    for (let i = 0; i < targetApp.ruleTypes.length; i++) {
      if (data.ruleTypeIds.indexOf(targetApp.ruleTypes[i]) === -1) {
        removedRuleTypes.push(targetApp.ruleTypes[i]);
      }
    }

    if (removedRuleTypes.length > 0) {
      // get the names of containers that are associated with missing app_id + rule_type_id combo
      let containers = [];
      for (let i = 0; i < removedRuleTypes.length; i++) {
        const foundContainers = await db(TABLE.container)
          .select([
            COLUMN.container.name,
            COLUMN.container.id,
          ])
          .where({
            [COLUMN.container.application]: appId,
            [COLUMN.container.rule_type_id]: removedRuleTypes[i],
          });
        if (foundContainers) {
          containers = containers.concat(foundContainers);
        }
      }
      if (containers.length > 0) {
        // get actual rule types names instead of ids for a more descriptive message
        let ruleTypeNames = await db(TABLE.rule_type)
          .select(COL_NAME.rule_type.rule_type)
          .whereIn(COL_NAME.rule_type.id, removedRuleTypes);
        // transform from the database format
        ruleTypeNames = ruleTypeNames.map(({ admin_rule_type_name }) => admin_rule_type_name); // eslint-disable-line
        const strContainerList = containers.reduce((accum, container) => {
          return `${accum}\n${container.admin_container_name} (id ${container.admin_container_id})`;
        }, '\n');
        // TODO: add more descriptive error, get rule types names, not just ids
        throw new Error(`You're trying to remove ${ruleTypeNames.join(', ')} rule type(s) from an application, but these rule type(s) are used in containers that rely on this application and rule type(s) combination. ${strContainerList}. \n\nRemove the ${ruleTypeNames.join(', ')} rule type(s) from these containers in order to proceed with removing the rule type(s) from this application.`);
      } else {
        // should be ok to delete rule types
        if (removedRuleTypes.length) {
          await _deleteRuleTypes(appId, removedRuleTypes);
        }
      }
    }

    await _updateApplicationBasicFields(appId, { ...data, rule_version: targetApp.rule_version });

    await _reconcileTeamPermissions(data.team_id, targetApp.team_id);

    await _updateRuleSubTypes(appId, data.ruleSubTypeIds, targetApp.ruleSubTypes);

    await _updatePlatforms(appId, data.platformIds);

    // if app is being deactivated, containers and pages must be deactivated, if it is being reactivated,
    // user has choice whether or not to enable pages/applications
    const newStatus = !!data.status;
    if (!newStatus && newStatus !== targetApp.status) {
      await _updateContainerPageStatus(appId, newStatus);
    }

    // get new rule type ids that are in the request body, but don't belong to the application yet
    const addedRuleTypes = difference(data.ruleTypeIds, targetApp.ruleTypes);
    if (addedRuleTypes.length) {
      // insert application's new rule types
      addedRuleTypes.length && await db(TABLE.applications_rule_types).insert(
        addedRuleTypes.map(id => ({
          [COLUMN.applications_rule_types.rule_type_id]: id,
          [COLUMN.applications_rule_types.application_id]: appId,
        })),
      );
    }

    await _removeRuleSubTypeAccessFromPigeon(appId);
    await _addRuleSubTypeAccessToPigeon(appId, data.ruleTypeIds, data.ruleSubTypeIds);

    return getApplication(appId);
  };

  const setApplicationStatus = async(id, active = true, query = {}) => {
    if (!active || query.activateChildren === 'true') {
      await _updateContainerPageStatus(id, active);
    }
    await db(TABLE.application)
      .update(COL_NAME.application.status, active)
      .where(COL_NAME.application.id, id);
    return getApplication(id);
  };

  // Deletes all linked containers & pages along with the application - only used in integration test
  const deleteApplication = async(id) => {
    const result = (await getApplication(id));

    await db(TABLE.applications_platforms)
      .delete()
      .where(COLUMN.applications_platforms.application_id, id);

    const containerIdsWithAppIdRes = await db(TABLE.container)
      .select(COLUMN.container.id)
      .where(COLUMN.container.application, id);
    const containerIdsWithAppId = containerIdsWithAppIdRes.map(r => r.admin_container_id);
    if (containerIdsWithAppId && containerIdsWithAppId.length) {
      await db(TABLE.containers_pages)
        .delete()
        .whereIn(COLUMN.containers_pages.container_id, containerIdsWithAppId);
      await db(TABLE.access_containers)
        .delete()
        .whereIn(COLUMN.access_containers.container_id, containerIdsWithAppId);
      await db(TABLE.container)
        .delete()
        .whereIn(COLUMN.container.id, containerIdsWithAppId);
    }

    const pageIdsWithAppIdRes = await db(TABLE.page)
      .select(COLUMN.page.id)
      .where(COLUMN.page.application, id);
    const pageIdsWithAppId = pageIdsWithAppIdRes.map(r => r.admin_page_id);
    if (pageIdsWithAppId && pageIdsWithAppId.length) {
      await db(TABLE.access_pages)
        .delete()
        .whereIn(COLUMN.access_pages.page_id, pageIdsWithAppId);
      await db(TABLE.page)
        .delete()
        .whereIn(COLUMN.page.id, pageIdsWithAppId);
    }

    await db(TABLE.access_rule_sub_types)
      .delete()
      .where(COLUMN.access_rule_sub_types.application_id, id);
    await db(TABLE.applications_rule_sub_types)
      .delete()
      .where(COLUMN.applications_rule_sub_types.application_id, id);
    await db(TABLE.applications_rule_types)
      .delete()
      .where(COLUMN.applications_rule_types.application_id, id);

    await db(TABLE.application)
      .delete()
      .where(COLUMN.application.id, id);

    return result;
  };

  return {
    getApplication,
    getApplicationByName,
    getApplications,
    deleteApplication,
    createApplication,
    updateApplication,
    setApplicationStatus,
  };
};

module.exports = applicationService;
