
const knex = require('knex');
const mockKnex = require('mock-knex');
const RuleTypeService = require('./rule-type-service');

const mockDb = knex({ client: 'mssql', debug: false });
const tracker = mockKnex.getTracker();

const allRuleTypes = [
  {
    'id': 1,
    'rule_type': 'alert',
    'slug': 'alert',
  },
  {
    'id': 2,
    'rule_type': 'campaign',
    'slug': 'campaign',
  },
  {
    'id': 3,
    'rule_type': 'vignette',
    'slug': 'vignette',
  },
  {
    'id': 4,
    'rule_type': 'vignette-broadcast',
    'slug': 'vignette-broadcast',
  },
  {
    'id': 5,
    'rule_type': 'vignette-priority',
    'slug': 'vignette-priority',
  },
  {
    'id': 7,
    'rule_type': 'estore',
    'slug': 'estore',
  },
];

describe('RuleType service', () => {
  beforeAll(() => {
    mockKnex.mock(mockDb);
  });
  afterAll(() => {
    mockKnex.unmock(mockDb);
  });
  beforeEach(() => {
    tracker.install();
  });
  afterEach(() => {
    tracker.uninstall();
  });

  it('get RuleType', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // insert operation - returns id
        query.response(allRuleTypes);
      }
    });
    const response = await RuleTypeService(mockDb).getRuleType();
    expect(response).toMatchObject(allRuleTypes);
  });
});
