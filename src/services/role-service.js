const { SELECT, TABLE, COLUMN, COL_NAME, JO<PERSON> } = require('../constants/db');
const { TRANSFORM_RESULT, TRANSFORM_INPUT, SORT_AND_PAGINATE } = require('../utils/db');

const permissionService = db => {
  const _insertRolesPermissions = async(roleId, teamId, permissions, teamPermissions) => {
    permissions.length && await db(TABLE.roles_permissions)
      .insert(permissions.map(permission => ({
        [COL_NAME.roles_permissions.role_id]: roleId,
        [COL_NAME.roles_permissions.permission_id]: teamPermissions.find(p => p.permission_name === permission).permission_id, // TODO: should not be required by db schema
        [COL_NAME.roles_permissions.permission_name]: permission,
        [COL_NAME.roles_permissions.team_id]: teamId,
      })));
  };

  const _deleteRolesPermissions = async(roleId) => {
    return db(TABLE.roles_permissions)
      .delete()
      .where(COLUMN.roles_permissions.role_id, roleId);
  };

  const getRole = async(query) => {
    const sqlStatement = db(TABLE.role)
      .select(
        COLUMN.role,
        SELECT.roles_permissions.permission_name,
        SELECT.roles_permissions.team_id,
      )
      .leftJoin(...JOIN.role_roles_permissions)
      .where(TRANSFORM_INPUT(query, COLUMN.role))
      .andWhere(TRANSFORM_INPUT(query.team_id ? { team_id: Number(query.team_id) } : {}, COLUMN.roles_permissions));

    if (query.sort && query.sort.replace('-', '') === 'team') {
      query.sort = query.sort === 'team' ? 'admin_team_name' : '-admin_team_name';
      sqlStatement.select(COLUMN.team.name);
      sqlStatement.leftJoin(...JOIN.roles_permissions_team);
    }
    if (query.search) {
      sqlStatement.where(COL_NAME.role.name, 'LIKE', `%${query.search}%`);
    }

    const result = await sqlStatement;

    let transformedResult = TRANSFORM_RESULT(result, { permissions: 'permission_name' });
    transformedResult = transformedResult.map(r => {
      r.permissions = r.permissions.filter(p => p !== 'placeholder');
      return r;
    });

    if (query.id) {
      return transformedResult;
    }
    return SORT_AND_PAGINATE(transformedResult, query);
  };

  const createRole = async(query) => {
    const { permissions, team_id: teamId, ...roleQuery } = query;
    const roleId = (await db(TABLE.role)
      .insert(TRANSFORM_INPUT(roleQuery, COL_NAME.role))
      .returning(COL_NAME.role.id))[0][COL_NAME.role.id];
    if (permissions) {
      const teamPermissions = await db(TABLE.teams_permissions)
        .select(COLUMN.teams_permissions)
        .where(TRANSFORM_INPUT({ team_id: teamId }, COLUMN.teams_permissions));
      await _insertRolesPermissions(roleId, teamId, permissions, teamPermissions);
    }
    return (await getRole({ id: roleId }))[0];
  };

  const updateRole = async(id, query, option = {}) => {
    const { permissions, team_id: teamId, ...roleQuery } = query;
    await db(TABLE.role)
      .update(TRANSFORM_INPUT(roleQuery, COL_NAME.role))
      .where(COLUMN.role.id, id);
    if (permissions) {
      await _deleteRolesPermissions(id);
      const teamPermissions = await db(TABLE.teams_permissions)
        .select(COLUMN.teams_permissions)
        .where(TRANSFORM_INPUT({ team_id: teamId }, COLUMN.teams_permissions));
      await _insertRolesPermissions(id, teamId, permissions, teamPermissions);
    }
    if (option.noReturn) {
      return;
    }
    return (await getRole({ id }))[0];
  };

  // Only used by integration tests
  const deleteRole = async(id) => {
    const result = (await getRole({ id }))[0];
    await _deleteRolesPermissions(id);

    await db(TABLE.users_roles)
      .delete()
      .where(COLUMN.users_roles.role_id, id);

    await db(TABLE.role)
      .delete()
      .where(COLUMN.role.id, id);
    return result;
  };

  const setRoleStatusActive = async(id, active = true) => {
    // set role active status
    await db(TABLE.role)
      .update({ [COL_NAME.role.status]: active })
      .where(COL_NAME.role.id, id);

    return getRole({ id });
  };

  return {
    getRole,
    createRole,
    updateRole,
    deleteRole,
    setRoleStatusActive,
  };
};

module.exports = permissionService;
