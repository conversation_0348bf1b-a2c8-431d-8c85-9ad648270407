const mockKnex = require('mock-knex');

const PageService = require('./page-service');
const { mockDbClient } = require('../db/index.test');

const dbInst = require('knex')({ client: 'mssql' });
const mockDb = mockDbClient(dbInst);
const tracker = mockKnex.getTracker();

const basePage = {
  application: 1,
  name: 'test-name',
  pageId: 'test-id',
  status: true,
  description: 'test-description',
};

describe('Page service', () => {
  beforeAll(() => {
    mockKnex.mock(dbInst);
  });
  afterAll(() => {
    mockKnex.unmock(dbInst);
  });
  beforeEach(() => {
    tracker.install();
  });
  afterEach(() => {
    tracker.uninstall();
  });

  it('getPage', async() => {
    tracker.on('query', (query, step) => {
      query.response([
        [ { admin_page_id: 1 }, { admin_page_id: 2 } ], // get pageIdsWithContainerId
        [ { ...basePage, id: 1, name: 'test1', container_id: 1, team_id: 1 } ], // get page matching search query
      ][step - 1]);
    });
    // change application field
    const response = await PageService(mockDb).getPage(
      { container_id: 1, search: 'test1', application_id: 1, team_id: 1, limit: 1, offset: 0 },
    );
    expect(response).toStrictEqual({
      items: [ { ...basePage, id: 1, name: 'test1', team_id: 1, containers: [ 1 ] } ],
      limit: 1,
      offset: 0,
      total: 1,
    });
  });

  it('createPage', async() => {
    tracker.on('query', (query, step) => {
      query.response([
        [ { admin_page_id: 1 } ], // insert operation - returns page id
        [ { admin_rule_type_id: 1 }, { admin_rule_type_id: 2 } ], // _addPageAccessToPigeon - get rule types (alert, campaign)
        [ { admin_access_id: 1 }, { admin_access_id: 2 } ], // _addPageAccessToPigeon - get access levels
        undefined, // _addPageAccessToPigeon - insert page access
        undefined, // commit
        [ { ...basePage, id: 1, container_id: 1 } ], // getPage - result
      ][step - 2]);
    });
    const response = await PageService(mockDb).createPage(basePage);
    expect(response).toStrictEqual({ ...basePage, id: 1, containers: [ 1 ] });
  });

  it('updatePage', async() => {
    tracker.on('query', (query, step) => {
      query.response([
        [ { ...basePage, id: 1, container_id: 1 } ], // getPage - old page
        // update operation - returns success
        // update operation - page access
        // update operation - break linkage to containers from previous app
        1, 1, 1,
        undefined, // commit
        [ { ...basePage, id: 1, container_id: 1, application: 2 } ], // getPage - new page
      ][step - 2]);
    });
    // change application field
    const response = await PageService(mockDb).updatePage(1, { ...basePage, application: 2 });
    expect(response).toStrictEqual({ ...basePage, id: 1, containers: [ 1 ], application: 2 });
  });

  it('updatePage (no return)', async() => {
    tracker.on('query', (query, step) => {
      query.response([
        [ { ...basePage, id: 1, container_id: null } ], // getPage - old page
        1, // update operation - returns success
      ][step - 2]);
    });
    const response = await PageService(mockDb).updatePage(1, basePage, { noReturn: true });
    expect(response).toBeUndefined();
  });

  it('set page status', async() => {
    tracker.on('query', (query, step) => {
      query.response([
        undefined, // set status
        [ { ...basePage, status: false } ], // get page
      ][step - 1]);
    });
    // change application field
    const response = await PageService(mockDb).setPageStatus(1, false);
    expect(response).toStrictEqual({ ...basePage, containers: [], status: false });
  });

  it('deletePage', async() => {
    tracker.on('query', (query, step) => {
      query.response([
        [ { ...basePage, id: 1, container_id: null } ], // getPage - result
        1, 1, 1, // delete - containers_pages, access_pages, page
      ][step - 2]);
    });
    const response = await PageService(mockDb).deletePage(1);
    expect(response).toStrictEqual({ ...basePage, id: 1, containers: [] });
  });
});
