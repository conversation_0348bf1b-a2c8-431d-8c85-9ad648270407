
const knex = require('knex');
const mockKnex = require('mock-knex');

const UserService = require('./user-service');
const { COL_NAME } = require('../constants/db');
const { BadRequestError } = require('../error');

const mockDb = knex({ client: 'mssql', debug: false });
const tracker = mockKnex.getTracker();
const baseUser = {
  name: 'test-name',
  email: '<EMAIL>',
  sid: 's1234567',
};

describe('User service', () => {
  beforeAll(() => {
    mockKnex.mock(mockDb);
  });
  afterAll(() => {
    mockKnex.unmock(mockDb);
  });
  beforeEach(() => {
    tracker.install();
  });
  afterEach(() => {
    tracker.uninstall();
  });

  it('createUser', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // insert operation - returns user id
        query.response([ { [COL_NAME.user.id]: 1 } ]);
      } else if (step === 2) { // _insertUsersRoles
        query.response(undefined);
      } else if (step === 3) { // getUser
        query.response([ { ...baseUser, id: 1, active: true, role_id: 1 } ]);
      }
    });
    const response = await UserService(mockDb).createUser({ ...baseUser, roles: [ '1' ] });
    expect(response).toStrictEqual({ ...baseUser, id: 1, active: true, roles: [ 1 ] });
  });

  it('createUsers', async() => {
    const userList = [ 1, 2, 3 ].map(uid => ({
      name: `${uid}${baseUser.name}`,
      sid: `${uid}${baseUser.sid}`,
      email: `${uid}${baseUser.name}`,
    }));
    const expected = userList.map(u => ({
      sid: u.sid,
      email: u.email,
      admin_user_id: Number(u.sid.charAt(0)),
    }));
    tracker.on('query', (query, step) => {
      if (step === 1) { // insert operation - returns basic user info
        query.response(userList.map(u => ({
          admin_user_id: Number(u.sid.charAt(0)),
          sid: u.sid,
          email: u.email,
        })));
      }
    });
    const response = await UserService(mockDb).createUsers(userList);
    expect(response).toStrictEqual(expected);
  });

  it('createUsers no query', async() => {
    const response = await UserService(mockDb).createUsers([]);
    expect(response).toStrictEqual([]);
  });

  it('updateUser', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // update operation
        query.response(undefined);
      } else if (step === 2) { // _deleteUsersRoles
        query.response(undefined);
      } else if (step === 3) { // _insertUsersRoles
        query.response(undefined);
      } else if (step === 4) { // getUser
        query.response([ { ...baseUser, id: 1, active: true, role_id: 1 } ]);
      }
    });
    const response = await UserService(mockDb).updateUser(1, { ...baseUser, roles: [ '1' ] });
    expect(response).toStrictEqual({ ...baseUser, id: 1, active: true, roles: [ 1 ] });
  });

  it('updateUser (no return)', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // update operation
        query.response(undefined);
      } else if (step === 2) { // _deleteUsersRoles
        query.response(undefined);
      } else if (step === 3) { // _insertUsersRoles
        query.response(undefined);
      }
    });
    const response = await UserService(mockDb).updateUser(1, { ...baseUser, roles: [ '1' ] }, { noReturn: true });
    expect(response).toBeUndefined();
  });

  it('deleteUser', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // getUser
        query.response([ { ...baseUser, id: 1, active: true, role_id: 1 } ]);
      } else if (step === 2) { // _deleteUsersRoles
        query.response(undefined);
      } if (step === 3) { // delete operation
        query.response(undefined);
      }
    });
    const response = await UserService(mockDb).deleteUser(1);
    expect(response).toStrictEqual({ ...baseUser, id: 1, active: true, roles: [ 1 ] });
  });

  it('getUsersFromSIDs', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // select operation
        query.response([ { ...baseUser, id: 1, active: true, role_name: 'Admin' } ]);
      }
    });
    const response = await UserService(mockDb).getUsersFromSIDs([ baseUser.sid ]);
    expect(response).toStrictEqual([ { ...baseUser, id: 1, active: true, roles: [ 'Admin' ] } ]);
  });

  test('should return user for valid sid', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // get User
        query.response([ { ...baseUser, id: 1, active: true } ]);
      }
    });
    const userResult = {
      ...baseUser, id: 1, active: true, roles: [],
    };
    const result = await UserService(mockDb).getUserBySid('s123456');
    expect(result).toStrictEqual(userResult);
  });

  test('should return undefined for no sid', async() => {
    const result = await UserService(mockDb).validateUser();
    expect(result).toBe(undefined);
  });

  test('should throw user does not exist', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // get User
        query.response([]);
      }
    });

    let error;
    try {
      await UserService(mockDb).validateUser('s123456');
    } catch (err) {
      error = err;
    }
    expect(error instanceof BadRequestError).toBe(true);
    expect(error.code).toBe(400);
    expect(error.message).toBe('USER_NONEXIST');
  });

  test('should throw user deactivated for inactive user', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // get User
        query.response([ { ...baseUser, id: 1, active: false } ]);
      }
    });
    let error;
    try {
      await UserService(mockDb).validateUser('s123456');
    } catch (err) {
      error = err;
    }
    expect(error instanceof BadRequestError).toBe(true);
    expect(error.code).toBe(400);
    expect(error.message).toBe('USER_DEACTIVATED');
  });

  test('should return user for validateUser', async() => {
    tracker.on('query', (query, step) => {
      if (step === 1) { // get User
        query.response([ { ...baseUser, id: 1, active: true } ]);
      }
    });
    const userResult = {
      ...baseUser, id: 1, active: true, roles: [],
    };
    const result = await UserService(mockDb).validateUser('s123456');
    expect(result).toStrictEqual(userResult);
  });
});
