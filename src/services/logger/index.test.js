const logger = require('./index');
const { createLogger } = require('nrlw-express-scribe');

jest.mock('nrlw-express-scribe', () => ({
  createLogger: jest.fn(),
}));

describe('Logger Configuration', () => {
  let config = {
    epm: 'BFB6',
    env: 'development',
    logging: {
      name: 'admin',
      obfuscate: false,
      colorize: false,
      prettyPrint: false,
      ignoreSiem: true,
      ignoredLogRoutes: [],
    },
  };
  it('creates logger instances development', () => {
    logger(config);

    expect(createLogger).toHaveBeenCalledWith({
      level: 'debug',
      programId: 'EPMBFB6-admin',
      obfuscate: false,
      colorize: false,
      prettyPrint: false,
      ignoreSiem: true,
    });
  });

  it('creates logger instances production', () => {
    const prodConfig = {
      ...config,
      env: 'production',
    };

    logger(prodConfig);

    expect(createLogger).toHaveBeenCalledWith({
      level: 'info',
      programId: 'EPMBFB6-admin',
      obfuscate: false,
      colorize: false,
      prettyPrint: false,
      ignoreSiem: true,
    });
  });
});
