const knex = require('knex');
const mockKnex = require('mock-knex');
const platformService = require('./platform-service');

const mockDb = knex({ client: 'mssql', debug: false });
const tracker = mockKnex.getTracker();

describe('Platform Service', () => {
  beforeAll(() => {
    mockKnex.mock(mockDb);
  });

  afterAll(() => {
    mockKnex.unmock(mockDb);
  });

  beforeEach(() => {
    tracker.install();
  });

  afterEach(() => {
    tracker.uninstall();
  });

  describe('getPlatforms', () => {
    it('should return all platforms', async() => {
      const mockPlatforms = [
        { id: 1, name: 'Platform 1', description: 'First platform' },
        { id: 2, name: 'Platform 2', description: 'Second platform' },
      ];

      tracker.on('query', (query) => {
        expect(query.sql).toContain('platform');
        query.response(mockPlatforms);
      });

      const service = platformService(mockDb);
      const result = await service.getPlatforms();

      expect(result).toEqual(mockPlatforms);
    });

    it('should return empty array when no platforms exist', async() => {
      tracker.on('query', (query) => {
        query.response([]);
      });

      const service = platformService(mockDb);
      const result = await service.getPlatforms();

      expect(result).toEqual([]);
    });
  });
});
