const mockOffer = require('../../__mocks__/mockOffer');
const { randomBytes } = require('crypto');

const getMockOffer = async(id) => {
  return Promise.resolve({ ...mockOffer, offer_id: id, offer_status: 'SUBMITTED' }).then(data => data);
};

const getMockOffers = async(query) => {
  const responseLength = 50;
  const { limit, offset } = query;
  const myPromise = new Promise((resolve, reject) => {
    setTimeout(() => {
      const data = {
        total: responseLength,
        limit: limit,
        offset: offset,
        items: Array.from({ length: responseLength }, (_, i) => ({
          ...mockOffer,
          offer_id: `OFF-${i + 1}`,
          // offer_status: [ 'DRAFT', 'SUBMITTED', 'REVIEWED', 'ACTIVE', 'EXPIRED', 'INACTIVE' ][Math.floor(Math.random() * 6)],
          offer_status: 'SUBMITTED',
        })).slice(offset, offset + limit),
      };
      resolve(data);
    }, 300);
  });
  return myPromise.then((data) => {
    return data;
  });
};

const getRequestHeaders = (scotiaId) => ({
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'x-scotia-id': scotiaId, // scotiaId
  'x-country-code': 'CA',
  'x-b3-traceid': randomBytes(16).toString('hex'),
  'x-b3-spanid': randomBytes(16).toString('hex'),
  'x-channel-id': 'Online',
  'x-originating-appl-code': 'BFB6',
});

module.exports = {
  getMockOffer,
  getMockOffers,
  getRequestHeaders,
};
