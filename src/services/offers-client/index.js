const qs = require('qs');
const {
  getRequestHeaders,
} = require('./common');

const commonReqParams = (config, sid) => ({
  baseURL: config.url,
  headers: {
    ...getRequestHeaders(sid),
  },
  timeout: config.timeout,
});

const getAllOffers = async({ axios }, config, query, sid) => {
  const res = await axios.request({
    ...commonReqParams(config, sid),
    url: `/v1/offers${qs.stringify(query, { addQueryPrefix: true })}`,
    method: 'get',
  });
  return res.data;
};

const getOffer = async({ axios }, config, data) => {
  const { offerId, sid } = data;
  const res = await axios.request({
    ...commonReqParams(config, sid),
    url: `/v1/offers/${offerId}`,
    method: 'get',
  });
  return res.data;
};

const createOffer = async({ axios }, config, data) => {
  const { value, sid } = data;
  const res = await axios.request({
    ...commonReqParams(config, sid),
    url: `/v1/offers`,
    method: 'post',
    data: value,
  });
  return res.data;
};

const updateOffer = async({ axios }, config, id, data) => {
  const { value, sid } = data;
  const res = await axios.request({
    ...commonReqParams(config, sid),
    url: `/v1/offers/${id}`,
    method: 'put',
    data: { [ value.action ? 'update_offer_status' : 'update_offer' ]: value },
  });
  return res.data;
};

const updateOfferLocation = async({ axios }, config, id, data) => {
  const { value, sid } = data;
  const res = await axios.request({
    ...commonReqParams(config, sid),
    url: `/v1/offers/${id}`,
    method: 'put',
    data: { update_offer_provinces: value },
  });
  return res.data;
};


const deleteOffer = async({ axios }, config, id, sid) => {
  const { data } = await axios.request({
    ...commonReqParams(config, sid),
    url: `/v1/offers/${id}`,
    method: 'delete',
  });
  return data;
};

const approveOffer = async({ axios }, config, id, data) => {
  const { value, sid } = data;
  const res = await axios.request({
    ...commonReqParams(config, sid),
    url: `/v1/offers/${id}`,
    method: 'patch',
    data: value,
  });
  return res.status;
};

const init = ({ axios }, config) => {
  return {
    getOffer: (id) => getOffer({ axios }, config, id),
    getAllOffers: (query, sid) => getAllOffers({ axios }, config, query, sid),
    createOffer: (data) => createOffer({ axios }, config, data),
    updateOfferLocation: (id, data) => updateOfferLocation({ axios }, config, id, data),
    updateOffer: (id, data) => updateOffer({ axios }, config, id, data),
    deleteOffer: (id, sid) => deleteOffer({ axios }, config, id, sid),
    approveOffer: (id, data) => approveOffer({ axios }, config, id, data),
  };
};

module.exports = init;
