const OffersApiClient = require('.');
const qs = require('qs');

// mock axios
const mockRequest = jest.fn();
const axios = {
  request: mockRequest,
};
// mock config
const config = { url: 'http://localhost/v1', timeout: 5000 };

describe('Offers API client', () => {
  test('should expose core components', () => {
    const client = OffersApiClient({ axios }, config);
    expect(client).toEqual({
      getOffer: expect.any(Function),
      getAllOffers: expect.any(Function),
      createOffer: expect.any(Function),
      updateOffer: expect.any(Function),
      deleteOffer: expect.any(Function),
      approveOffer: expect.any(Function),
      updateOfferLocation: expect.any(Function),
    });
  });

  describe('Service methods implementation', () => {
    const testSid = 's1234567';
    const testOfferId = 'OFF-999';
    const testData = { title: 'Test Offer' };

    test('getAllOffers constructs proper query URL', async() => {
      const queryParams = { limit: 25, offset: 50 };
      mockRequest.mockResolvedValue({ status: 200, data: {} });

      const client = OffersApiClient({ axios }, config);
      await client.getAllOffers(queryParams, testSid);

      const expectedUrl = `/v1/offers${qs.stringify(queryParams, { addQueryPrefix: true })}`;
      expect(mockRequest).toHaveBeenCalledWith({
        method: 'get',
        url: expectedUrl,
        baseURL: config.url,
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'x-scotia-id': testSid,
          'x-country-code': 'CA',
          'x-channel-id': 'Online',
          'x-originating-appl-code': 'BFB6',
        }),
        timeout: config.timeout,
      });

      // Verify trace IDs are present and formatted correctly
      const actualHeaders = mockRequest.mock.calls[0][0].headers;
      expect(actualHeaders).toHaveProperty('x-b3-traceid');
      expect(actualHeaders['x-b3-traceid']).toMatch(/^[0-9a-f]{32}$/);
      expect(actualHeaders).toHaveProperty('x-b3-spanid');
      expect(actualHeaders['x-b3-spanid']).toMatch(/^[0-9a-f]{32}$/);
    });

    test('createOffer sends POST with payload', async() => {
      mockRequest.mockResolvedValue({ status: 201, data: {} });

      const client = OffersApiClient({ axios }, config);
      await client.createOffer({ value: testData, sid: testSid });

      expect(mockRequest).toHaveBeenCalledWith(expect.objectContaining({
        method: 'post',
        url: '/v1/offers',
        data: testData,
      }));
    });

    test('getOffer uses correct endpoint structure', async() => {
      mockRequest.mockResolvedValue({ status: 200, data: {} });

      const client = OffersApiClient({ axios }, config);
      await client.getOffer({ offerId: testOfferId, sid: testSid });

      expect(mockRequest).toHaveBeenCalledWith(expect.objectContaining({
        method: 'get',
        url: `/v1/offers/${testOfferId}`,
      }));
    });

    test('should have updateOffer method', () => {
      const client = OffersApiClient({ axios }, config);
      expect(client).toHaveProperty('updateOffer');
      expect(typeof client.updateOffer).toEqual('function');
    });

    test('approveOffer sends PATCH with ID', async() => {
      mockRequest.mockResolvedValue({ status: 204, data: {} });

      const client = OffersApiClient({ axios }, config);
      await client.approveOffer(testOfferId, { value: testData, sid: testSid });

      expect(mockRequest).toHaveBeenCalledWith(expect.objectContaining({
        method: 'patch',
        url: `/v1/offers/${testOfferId}`,
        data: testData,
      }));
    });

    test('should successfully call deleteOffer', async() => {
      const mockResponse = {
        status: 200,
        data: {},
      };
      // const mockOfferId = 'OFR-123';
      mockRequest.mockReturnValueOnce(mockResponse);
      const client = OffersApiClient({ axios }, config);
      // const result = await client.deleteOffer(mockOfferId);
      // expect(mockRequest).toBeCalled();
      // expect(mockRequest.mock.calls.length).toEqual(1);
      // expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/offers/${mockOfferId}`);
      // expect(result).toEqual(mockResponse);
      expect(client).toHaveProperty('deleteOffer');
      expect(typeof client.deleteOffer).toEqual('function');
    });
  });
});
