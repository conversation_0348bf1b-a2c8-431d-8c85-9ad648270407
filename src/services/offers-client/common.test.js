const { getMockOffer, getMockOffers, getRequestHeaders } = require('./common');
const { randomBytes } = require('crypto');
const mockOffer = require('../../__mocks__/mockOffer');

jest.mock('crypto');

describe('Mock Offer API', () => {
  // Test data constants
  const TEST_SCOTIA_ID = 's1234567';
  const DEFAULT_LIMIT = 10;
  const DEFAULT_OFFSET = 0;

  describe('getMockOffer', () => {
    test('should return offer with specified ID and SUBMITTED status', async() => {
      const testId = 'OFF-999';
      const result = await getMockOffer(testId);

      expect(result.offer_id).toBe(testId);
      expect(result.offer_status).toBe('SUBMITTED');
    });

    test('should not mutate original mock data', async() => {
      const originalId = mockOffer.offer_id;
      const result = await getMockOffer('NEW-ID');

      expect(result.offer_id).not.toBe(originalId);
      expect(mockOffer.offer_id).toBe(originalId);
    });
  });

  describe('getMockOffers', () => {
    beforeAll(() => {
      jest.useFakeTimers();
    });

    afterAll(() => {
      jest.useRealTimers();
    });

    test('should return paginated results with correct metadata', async() => {
      const query = { limit: DEFAULT_LIMIT, offset: DEFAULT_OFFSET };
      const promise = getMockOffers(query);

      jest.advanceTimersByTime(300);
      const result = await promise;

      expect(result.total).toBe(50);
      expect(result.limit).toBe(query.limit);
      expect(result.offset).toBe(query.offset);
      expect(result.items.length).toBe(query.limit);
    });

    test('should handle offset beyond dataset length', async() => {
      const promise = getMockOffers({ limit: 10, offset: 45 });
      jest.advanceTimersByTime(300);
      const result = await promise;

      expect(result.items.length).toBe(5);
      expect(result.items[0].offer_id).toBe('OFF-46');
    });

    test('should maintain consistent SUBMITTED status', async() => {
      const promise = getMockOffers({ limit: 25, offset: DEFAULT_OFFSET });
      jest.advanceTimersByTime(300);
      const result = await promise;

      result.items.forEach(item => {
        expect(item.offer_status).toBe('SUBMITTED');
      });
    });
  });

  describe('getRequestHeaders', () => {
    const mockRandomBytes = (size) => Buffer.alloc(size, 'a');

    test('should generate valid headers structure', () => {
      randomBytes.mockImplementation(mockRandomBytes);

      const headers = getRequestHeaders(TEST_SCOTIA_ID);

      expect(headers).toEqual(expect.objectContaining({
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-scotia-id': TEST_SCOTIA_ID,
        'x-country-code': 'CA',
        'x-channel-id': 'Online',
        'x-originating-appl-code': 'BFB6',
      }));
    });

    test('should generate valid trace identifiers', () => {
      randomBytes.mockImplementation((size) => Buffer.alloc(size, 'b'));

      const headers = getRequestHeaders(TEST_SCOTIA_ID);

      expect(headers['x-b3-traceid']).toHaveLength(32);
      expect(headers['x-b3-spanid']).toHaveLength(32);
      expect(headers['x-b3-traceid']).toMatch(/^[0-9a-f]{32}$/);
      expect(headers['x-b3-spanid']).toMatch(/^[0-9a-f]{32}$/);
    });
  });
});
