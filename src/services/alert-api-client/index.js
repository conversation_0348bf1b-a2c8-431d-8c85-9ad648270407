const qs = require('qs');
const { getRequestHeaders } = require('./common');
const { addVersionToPlatformTargetingData } = require('../../utils');

const commonReqParams = (config) => ({
  baseURL: config.url,
  headers: getRequestHeaders(),
  timeout: config.timeout,
});

const createAlert = ({ axios }, config, body) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/alert-rules`,
    method: 'post',
    data: addVersionToPlatformTargetingData(body),
  })
);

const deleteAlert = ({ axios }, config, ruleId) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/alert-rules/${ruleId}`,
    method: 'delete',
  })
);

const getAlert = ({ axios }, config, ruleId) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/alert-rules/${ruleId}`,
    method: 'get',
  })
);

const getAllAlerts = ({ axios }, config, query) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/alert-rules${qs.stringify(query, { addQueryPrefix: true })}`,
    method: 'get',
  })
);

const getAllAlertsByAccess = ({ axios }, config, query, data) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/alert-rules/fetch${qs.stringify(query, { addQueryPrefix: true })}`,
    method: 'post',
    data,
  })
);

const updateAlert = ({ axios }, config, ruleId, body) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/alert-rules/${ruleId}`,
    method: 'patch',
    data: addVersionToPlatformTargetingData(body),
  })
);

const init = ({ axios }, config) => {
  return {
    createAlert: (body) => createAlert({ axios }, config, body),
    getAllAlerts: (query) => getAllAlerts({ axios }, config, query),
    getAllAlertsByAccess: (query, data) => getAllAlertsByAccess({ axios }, config, query, data),
    updateAlert: (ruleId, body) => updateAlert({ axios }, config, ruleId, body),
    getAlert: (ruleId) => getAlert({ axios }, config, ruleId),
    deleteAlert: (ruleId) => deleteAlert({ axios }, config, ruleId),
  };
};

module.exports = init;
