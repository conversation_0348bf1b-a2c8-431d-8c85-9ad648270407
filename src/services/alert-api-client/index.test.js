const qs = require('qs');
const AlertApiClient = require('.');

// mock axios
const mockRequest = jest.fn();
const axios = {
  request: mockRequest,
};
// mock config
const config = {
  url: 'http://localhost/v1',
};

describe('Alert API client', () => {
  test('should have createAlert method', () => {
    const client = AlertApiClient({ axios }, config);
    expect(client).toHaveProperty('createAlert');
    expect(typeof client.createAlert).toEqual('function');
  });
  test('should have updateAlert method', () => {
    const client = AlertApiClient({ axios }, config);
    expect(client).toHaveProperty('updateAlert');
    expect(typeof client.createAlert).toEqual('function');
  });
  test('should have getAlert method', () => {
    const client = AlertApiClient({ axios }, config);
    expect(client).toHaveProperty('getAlert');
    expect(typeof client.createAlert).toEqual('function');
  });
  test('should have getAllAlerts method', () => {
    const client = AlertApiClient({ axios }, config);
    expect(client).toHaveProperty('getAllAlerts');
    expect(typeof client.createAlert).toEqual('function');
  });
  test('should have getAllAlerts method', () => {
    const client = AlertApiClient({ axios }, config);
    expect(client).toHaveProperty('getAllAlerts');
    expect(typeof client.createAlert).toEqual('function');
  });

  test('should successfully call createAlert', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };
    const mockBody = {};
    mockRequest.mockReturnValueOnce(fakeResponse);
    const client = AlertApiClient({ axios }, config);
    const result = await client.createAlert(mockBody);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/alert-rules`);
    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call deleteAlert', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };
    const mockRuleId = '123456ab';
    mockRequest.mockReturnValueOnce(fakeResponse);
    const client = AlertApiClient({ axios }, config);
    const result = await client.deleteAlert(mockRuleId);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/alert-rules/${mockRuleId}`);
    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call getAlert', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };
    const mockRuleId = '123456ab';
    mockRequest.mockReturnValueOnce(fakeResponse);
    const client = AlertApiClient({ axios }, config);
    const result = await client.getAlert(mockRuleId);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/alert-rules/${mockRuleId}`);
    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call getAllAlerts', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };
    const mockQuery = {
      'item1': 'value1',
      'item2': 'value2',
      'item3': 'value3',
    };
    mockRequest.mockReturnValueOnce(fakeResponse);
    const client = AlertApiClient({ axios }, config);
    const result = await client.getAllAlerts(mockQuery);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/alert-rules?${qs.stringify(mockQuery)}`);
    expect(result).toEqual(fakeResponse);
  });

  test('should successfully call updateAlert', async() => {
    const fakeResponse = {
      status: 200,
      data: {},
    };
    const mockRuleId = '1234';
    const mockBody = {};
    mockRequest.mockReturnValueOnce(fakeResponse);
    const client = AlertApiClient({ axios }, config);
    const result = await client.updateAlert(mockRuleId, mockBody);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/alert-rules/${mockRuleId}`);
    expect(result).toEqual(fakeResponse);
  });
});
