const { TABLE, COL_NAME, COLUMN, SELECT, JOIN } = require('../constants/db');
const { BadRequestError } = require('../error');
const { TRANSFORM_RESULT, TRANSFORM_INPUT, SORT_AND_PAGINATE } = require('../utils/db');

const userService = db => {
  const userColumns = [
    SELECT.user.id,
    SELECT.user.name,
    SELECT.user.email,
    SELECT.user.sid,
    SELECT.user.active,
  ];

  const _insertUsersRoles = async(userId, roles, dbClient = db) => {
    roles.length && await dbClient(TABLE.users_roles)
      .insert(roles.map(i => ({
        [COL_NAME.users_roles.user_id]: userId,
        [COL_NAME.users_roles.role_id]: i,
      })));
  };
  const _deleteUsersRoles = async(userId) => {
    return db(TABLE.users_roles)
      .delete()
      .where(COLUMN.users_roles.user_id, userId);
  };

  const getUser = async(query = {}, options = {}, dbClient = db) => {
    const result = await dbClient(TABLE.user)
      .select([
        ...userColumns,
        SELECT.users_roles.role_id,
        ...(options.returnToken ? [ SELECT.user.token, SELECT.user.token_expire_date, SELECT.user.token_expires ] : []),
        SELECT.roles_permissions.team_id,
      ])
      .where(TRANSFORM_INPUT(query, COLUMN.user))
      .leftJoin(...JOIN.user_users_roles)
      .leftJoin(...JOIN.users_roles_roles_permissions);

    return TRANSFORM_RESULT(result, { roles: 'role_id' }, true);
  };

  const getUsers = async(query = {}, options = {}) => {
    const sqlStatement = db(TABLE.user)
      .select([
        ...userColumns,
        SELECT.users_roles.role_id,
        ...(options.returnToken ? [ SELECT.user.token, SELECT.user.token_expire_date, SELECT.user.token_expires ] : []),
        SELECT.roles_permissions.team_id,
      ])
      .leftJoin(...JOIN.user_users_roles)
      .leftJoin(...JOIN.users_roles_roles_permissions)
      .where(TRANSFORM_INPUT(query, COLUMN.user))
      .where(TRANSFORM_INPUT(query.team_id ? { team_id: parseInt(query.team_id) } : {}, COLUMN.roles_permissions));

    if (query.search) {
      sqlStatement.andWhere(builder => {
        builder.where(COL_NAME.user.name, 'LIKE', `%${query.search}%`)
          .orWhere(COL_NAME.user.sid, 'LIKE', `%${query.search}%`);
      });
    }

    if (query.sort && query.sort.replace('-', '') === 'team') {
      query.sort = query.sort === 'team' ? 'admin_team_name' : '-admin_team_name';
      sqlStatement.select(COLUMN.team.name);
      sqlStatement.leftJoin(...JOIN.roles_permissions_team);
    }

    const result = await sqlStatement;
    let transformedResult = TRANSFORM_RESULT(result, { roles: 'role_id' }, true);
    if (query.role_id) {
      transformedResult = transformedResult.filter(user => user.roles.includes(parseInt(query.role_id)));
    }
    return SORT_AND_PAGINATE(transformedResult, query);
  };

  const getUsersFromSIDs = async(sidUserArray, query) => {
    const result = await db(TABLE.user)
      .select([
        ...userColumns,
        `${COLUMN.role.name} as role_name`,
        SELECT.roles_permissions.team_id,
      ])
      .whereIn('sid', sidUserArray)
      .leftJoin(...JOIN.user_users_roles)
      .leftJoin(...JOIN.users_roles_role)
      .leftJoin(...JOIN.users_roles_roles_permissions);
    return TRANSFORM_RESULT(result, { roles: 'role_name' }, true);
  };

  const createUser = async(query, dbClient = db) => {
    const { roles, ...userQuery } = query;
    const id = (await dbClient(TABLE.user)
      .insert(TRANSFORM_INPUT(userQuery, COL_NAME.user))
      .returning(COL_NAME.user.id))[0][COL_NAME.user.id];
    /* istanbul ignore else */
    if (roles) {
      await _insertUsersRoles(id, roles, dbClient);
    }
    return (await getUser({ id }, {}, dbClient))[0];
  };

  /**
   * Bulk create users. Used on create/edit teams page where
   * new users can be bulk created by adding them as new team owners.
   * This function does not handle roles for newly created users.
   * Caller is expected to setup roles for users created based on
   * user info returned.
   *
   * @param {*} userQueries
   * @returns
   */
  const createUsers = async(userQueries) => {
    if (!userQueries || !userQueries.length) { return []; }
    const dbInserts = userQueries.map(q => TRANSFORM_INPUT(q, COL_NAME.user));
    const usersCreated = dbInserts.length && await db(TABLE.user)
      .insert(dbInserts)
      .returning([ COL_NAME.user.id, COL_NAME.user.sid, COL_NAME.user.email ]);
    return usersCreated;
  };

  const updateUser = async(id, query, option = {}) => {
    // TODO tw - do not allow viewer role to be modified from frontend
    // and do not allow owner role to be removed if user is the only owner
    const { roles, ...userQuery } = query;
    await db(TABLE.user)
      .update(TRANSFORM_INPUT(userQuery, COL_NAME.user))
      .where(COLUMN.user.id, id);
    /* istanbul ignore else */
    if (roles) {
      await _deleteUsersRoles(id);
      await _insertUsersRoles(id, roles);
    }
    if (option.noReturn) {
      return;
    }
    return (await getUser({ id }))[0];
  };

  const deleteUser = async(id) => {
    const result = (await getUser({ id }))[0];
    await _deleteUsersRoles(id);
    await db(TABLE.user)
      .delete()
      .where(COLUMN.user.id, id);

    return result;
  };

  const setUserStatusActive = async(id, active = true) => {
    // set user active status
    await db(TABLE.user)
      .update(TRANSFORM_INPUT({ active }, COL_NAME.user))
      .where(COLUMN.user.id, id);

    return getUser({ id });
  };

  const validateUser = async(sid) => {
    if (!sid) {
      return undefined;
    }
    const user = (await getUser({ sid }))[0];
    if (!user) {
      throw new BadRequestError('USER_NONEXIST');
    }
    if (!user.active) {
      throw new BadRequestError('USER_DEACTIVATED');
    }
    return user;
  };

  const getUserBySid = async(sid) => {
    if (sid) {
      const user = (await getUser({ sid }))[0];
      if (user && user.active) {
        return user;
      }
    }
  };

  return {
    getUser,
    getUsers,
    getUsersFromSIDs,
    createUser,
    createUsers,
    updateUser,
    deleteUser,
    setUserStatusActive,
    validateUser,
    getUserBySid,
  };
};

module.exports = userService;
