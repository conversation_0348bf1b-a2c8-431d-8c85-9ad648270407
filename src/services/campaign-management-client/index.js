const qs = require('qs');
const {
  getRequestHeaders,
} = require('./common');

const commonReqParams = (config) => ({
  baseURL: config.url,
  headers: getRequestHeaders(),
  timeout: config.timeout,
});

const getAllCampaigns = async({ axios }, config, query) => {
  // This can be added once the campaign management API is ready
  const { data } = await axios.request({
    ...commonReqParams(config),
    url: `/v1/campaigns${qs.stringify(query, { addQueryPrefix: true })}`,
    method: 'get',
  });
  return data;
};

const getCampaign = async({ axios }, config, id) => {
  const { data } = await axios.request({
    ...commonReqParams(config),
    url: `/v1/campaigns/${id}`,
    method: 'get',
  });
  return data;
};

const createCampaign = async({ axios }, config, data) => {
  const res = await axios.request({
    ...commonReqParams(config),
    url: `/v1/campaigns`,
    method: 'post',
    data,
  });
  return res.data;
};

const patchCampaign = async({ axios }, config, campaignId, body) => {
  const res = await axios.request({
    ...commonReqParams(config),
    url: `/v1/campaigns/${campaignId}`,
    method: 'patch',
    data: body,
  });
  return res.data;
};

const deleteCampaign = async({ axios }, config, id) => {
  const { data } = await axios.request({
    ...commonReqParams(config),
    url: `/v1/campaigns/${id}`,
    method: 'delete',
  });
  return data;
};

const putCampaign = async({ axios }, config, campaignId, body) => {
  const res = await axios.request({
    ...commonReqParams(config),
    url: `/v1/campaigns/${campaignId}`,
    method: 'put',
    data: body,
  });
  return res.data;
};

const getMessageDetail = async({ axios }, config, id, query) => {
  const { data } = await axios.request({
    ...commonReqParams(config),
    url: `/v1/offers/${id}${qs.stringify(query, { addQueryPrefix: true })}`,
    method: 'get',
  });
  return data;
};

const updateMessageDetail = async({ axios }, config, id, body) => {
  const { data } = await axios.request({
    ...commonReqParams(config),
    url: `/v1/offers/${id}`,
    method: 'patch',
    data: body,
  });
  return data;
};

const getAllMessages = async({ axios }, config, query) => {
  const { data } = await axios.request({
    ...commonReqParams(config),
    url: `/v1/offers${qs.stringify(query, { addQueryPrefix: true })}`,
    method: 'get',
  });
  return data;
};

const init = ({ axios }, config) => {
  return {
    getAllCampaigns: (query) => getAllCampaigns({ axios }, config, query),
    getCampaign: (id) => getCampaign({ axios }, config, id),
    createCampaign: (data) => createCampaign({ axios }, config, data),
    putCampaign: (campaignId, body) => putCampaign({ axios }, config, campaignId, body),
    patchCampaign: (campaignId, body) => patchCampaign({ axios }, config, campaignId, body),
    deleteCampaign: (id) => deleteCampaign({ axios }, config, id),
    getMessageDetail: (id, query) => getMessageDetail({ axios }, config, id, query),
    updateMessageDetail: (id, body) => updateMessageDetail({ axios }, config, id, body),
    getAllMessages: (query) => getAllMessages({ axios }, config, query),
  };
};

module.exports = init;
