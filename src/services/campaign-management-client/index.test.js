const CampaignManagementApiClient = require('.');

// mock axios
const mockRequest = jest.fn();
const axios = {
  request: mockRequest,
};
// mock config
const config = {
  url: 'http://localhost/v1',
};

describe('Campaigns Management API client', () => {
  test('should have getAllCampaigns method', () => {
    const client = CampaignManagementApiClient({ axios }, config);
    expect(client).toHaveProperty('getAllCampaigns');
    expect(typeof client.getAllCampaigns).toEqual('function');
  });

  test('should have createCampaign method', () => {
    const client = CampaignManagementApiClient({ axios }, config);
    expect(client).toHaveProperty('createCampaign');
    expect(typeof client.createCampaign).toEqual('function');
  });

  test('should have getAllCampaigns method', () => {
    const client = CampaignManagementApiClient({ axios }, config);
    expect(client).toHaveProperty('patchCampaign');
    expect(typeof client.patchCampaign).toEqual('function');
  });

  test('should successfully call getAllCampaigns', async() => {
    const mockResponse = {
      status: 200,
      data: {},
    };
    const mockQuery = {};
    mockRequest.mockReturnValueOnce(mockResponse);
    const client = CampaignManagementApiClient({ axios }, config);
    await client.getAllCampaigns(mockQuery);
  });

  test('should successfully call patch campaign', async() => {
    const mockResponse = {
      status: 200,
      data: {},
    };
    mockRequest.mockReturnValueOnce(mockResponse);
    const client = CampaignManagementApiClient({ axios }, config);
    await client.patchCampaign(1, { status: 'N', id: 1 });
  });

  test('should successfully call to campaign management api to get campaign', async() => {
    const mockResponse = {
      status: 200,
      data: {},
    };
    const mockRuleId = 'PAD22';
    mockRequest.mockReturnValueOnce(mockResponse);
    const client = CampaignManagementApiClient({ axios }, config);
    const result = await client.getCampaign(mockRuleId);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/campaigns/${mockRuleId}`);
    expect(result).toEqual(mockResponse.data);
  });

  test('should successfully call to campaign management api to create campaign', async() => {
    const mockResponse = {
      status: 200,
      data: {},
    };
    const mockRuleData = {};
    mockRequest.mockReturnValueOnce(mockResponse);
    const client = CampaignManagementApiClient({ axios }, config);
    const result = await client.createCampaign(mockRuleData);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/campaigns`);
    expect(result).toEqual(mockResponse.data);
  });
});
