const mockMessageCentreCampaign = require('../../__mocks__/mockMessageCentreCampaign');
const mockMessageDetail = require('../../__mocks__/mockMessageDetail');

const getMockMessages = async(query) => {
  const responseLength = 50;
  const { limit, offset } = query;
  const myPromise = new Promise((resolve, reject) => {
    setTimeout(() => {
      const data = {
        total: responseLength,
        limit: limit,
        offset: offset,
        items: Array.from({ length: responseLength }, (_, i) => ({
          ...mockMessageDetail,
          id: i + 1,
          // Campaign information
          name: `Momentum No Fee PA ${i + 1}`,
          msg_status: [ 'N', 'S', 'D' ][Math.floor(Math.random() * 3)],
        })).slice(offset, offset + limit),
      };
      resolve(data);
    }, 300);
  });
  return myPromise.then((data) => {
    return data;
  });
};

const getMockCampaign = async(id) => {
  return Promise.resolve({ ...mockMessageCentreCampaign, campaignId: id }).then((data) => data);
};

const getMockMessageDetail = async(id) => {
  return Promise.resolve({ ...mockMessageDetail, messageDetailId: id }).then((data) => data);
};

const getRequestHeaders = () => ({
  'Content-Type': 'application/json',
  Accept: 'application/json',
});

module.exports = {
  getRequestHeaders,
  getMockCampaign,
  getMockMessageDetail,
  getMockMessages,
};
