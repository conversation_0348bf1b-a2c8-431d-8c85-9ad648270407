const campaignApi = require('./campaign-api-client');
const alertApi = require('./alert-api-client');
const contentApi = require('./content-api-client');
const marvelProductApi = require('./marvel-product-api-client');
const variableMappingsApi = require('./variable-mappings-api-client');
const variableMappingsApiV2 = require('./variable-mappings-api-client-v2');

const applicationService = require('./application-service');
const containerService = require('./container-service');
const pageService = require('./page-service');
const permissionServiceConstructor = require('./permission-service');
const ruleTypeService = require('./rule-type-service');
const ruleSubTypeService = require('./rule-sub-type-service');
const roleService = require('./role-service');
const ruleAssignmentService = require('./rule-service');
const userServiceConstructor = require('./user-service');
const vignetteService = require('./vignette-service');
const platformService = require('./platform-service');
const teamsService = require('./teams-service');
const validationService = require('./validation-service');
const campaignManagementService = require('./campaign-management-client');
const offersService = require('./offers-client');
const { db: createDbClient } = require('../db');

const serviceInit = (logger, dbOpts) => {
  const dbClient = createDbClient(dbOpts);
  const db = dbClient.query; // for backwards compatibility while trx db is retrofitted
  const permissionService = permissionServiceConstructor(db, logger);
  const userService = userServiceConstructor(db, logger);

  return {
    campaignApi,
    alertApi,
    contentApi,
    applicationService: applicationService(db),
    marvelProductApi,
    variableMappingsApi,
    variableMappingsApiV2,

    permissionService,
    userService,
    containerService: containerService(db, logger),
    pageService: pageService(dbClient),
    ruleTypeService: ruleTypeService(db, logger),
    ruleSubTypeService: ruleSubTypeService(db, logger),
    roleService: roleService(db, logger),
    ruleService: (campaignService) => ruleAssignmentService({ dbService: db, logger, userService, permissionService, campaignService }),
    vignetteService: vignetteService(db, logger),
    platformService: platformService(db),
    teamsService: teamsService({ dbClient, userService }),
    validationService: validationService(db),
    campaignManagementService,
    offersService,
  };
};

module.exports = serviceInit;
