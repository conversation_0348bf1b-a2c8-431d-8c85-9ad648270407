const knex = require('knex');
const mockKnex = require('mock-knex');
const ruleSubTypeService = require('./rule-sub-type-service');

const mockDb = knex({ client: 'mssql', debug: false });
const tracker = mockKnex.getTracker();

describe('Rule Sub Type Service', () => {
  beforeAll(() => {
    mockKnex.mock(mockDb);
  });

  afterAll(() => {
    mockKnex.unmock(mockDb);
  });

  beforeEach(() => {
    tracker.install();
  });

  afterEach(() => {
    tracker.uninstall();
  });

  describe('getRuleSubTypes', () => {
    it('should return all rule sub types with correct field mapping', async() => {
      const mockRuleSubTypes = [
        {
          admin_rule_sub_type_id: 1,
          admin_rule_sub_type_description: 'Description 1',
          admin_rule_sub_type_name: 'Type 1',
          admin_rule_type_id: 10,
        },
        {
          admin_rule_sub_type_id: 2,
          admin_rule_sub_type_description: 'Description 2',
          admin_rule_sub_type_name: 'Type 2',
          admin_rule_type_id: 20,
        },
      ];

      tracker.on('query', (query) => {
        expect(query.sql).toContain('rule_sub_type');
        query.response(mockRuleSubTypes);
      });

      const service = ruleSubTypeService(mockDb);
      const result = await service.getRuleSubTypes();

      expect(result).toEqual(mockRuleSubTypes);
    });

    it('should return empty array when no rule sub types exist', async() => {
      tracker.on('query', (query) => {
        query.response([]);
      });

      const service = ruleSubTypeService(mockDb);
      const result = await service.getRuleSubTypes();

      expect(result).toEqual([]);
    });
  });
});
