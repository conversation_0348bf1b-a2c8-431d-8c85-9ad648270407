const getRedisConnectionConfig = require('./index');

describe('getRedisConnectionConfig', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
    delete process.env.SECRET_REDIS_AUTH_STRING;
    delete process.env.REDIS_HOST;
    delete process.env.REDIS_URL;
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  it('should use SECRET_REDIS_AUTH_STRING when available', () => {
    process.env.SECRET_REDIS_AUTH_STRING = 'secret-password';
    process.env.REDIS_HOST = 'redis.example.com';

    const redisConnectionConfig = getRedisConnectionConfig();

    expect(redisConnectionConfig.port).toBe(6378);
    expect(redisConnectionConfig.host).toBe('redis.example.com');
    expect(redisConnectionConfig.password).toBe('secret-password');
    expect(redisConnectionConfig.tls).toEqual({
      servername: 'redis.example.com',
    });
    expect(redisConnectionConfig.url).toBeUndefined();
  });

  it('should use REDIS_URL when available and no SECRET_REDIS_AUTH_STRING', () => {
    process.env.REDIS_URL = 'redis://custom-redis:6379';

    const redisConnectionConfig = getRedisConnectionConfig();

    expect(redisConnectionConfig.url).toBe('redis://custom-redis:6379');
    expect(redisConnectionConfig.port).toBeUndefined();
    expect(redisConnectionConfig.host).toBeUndefined();
    expect(redisConnectionConfig.password).toBeUndefined();
    expect(redisConnectionConfig.tls).toBeUndefined();
  });

  it('should use default localhost URL when no environment variables are set', () => {
    const redisConnectionConfig = getRedisConnectionConfig();

    expect(redisConnectionConfig.url).toBe('redis://localhost:6379');
    expect(redisConnectionConfig.port).toBeUndefined();
    expect(redisConnectionConfig.host).toBeUndefined();
    expect(redisConnectionConfig.password).toBeUndefined();
    expect(redisConnectionConfig.tls).toBeUndefined();
  });

  it('should prioritize SECRET_REDIS_AUTH_STRING over REDIS_URL', () => {
    process.env.SECRET_REDIS_AUTH_STRING = 'secret-password';
    process.env.REDIS_HOST = 'redis.example.com';
    process.env.REDIS_URL = 'redis://should-not-use:6379';

    const redisConnectionConfig = getRedisConnectionConfig();

    expect(redisConnectionConfig.port).toBe(6378);
    expect(redisConnectionConfig.host).toBe('redis.example.com');
    expect(redisConnectionConfig.password).toBe('secret-password');
    expect(redisConnectionConfig.url).toBeUndefined();
  });
});
