const getRedisConnectionConfig = () => {
  const redisConnectionConfig = {};

  if (process.env.SECRET_REDIS_AUTH_STRING) {
    const redisPassword = process.env.SECRET_REDIS_AUTH_STRING;
    redisConnectionConfig.port = 6378;
    redisConnectionConfig.host = process.env.REDIS_HOST;
    redisConnectionConfig.password = redisPassword;
    redisConnectionConfig.tls = {
      servername: process.env.REDIS_HOST,
    };
  } else if (process.env.REDIS_URL) {
    redisConnectionConfig.url = process.env.REDIS_URL;
  } else {
    redisConnectionConfig.url = 'redis://localhost:6379';
  }

  return redisConnectionConfig;
};

module.exports = getRedisConnectionConfig;
