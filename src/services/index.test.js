const mockLogger = undefined;
const dbOpts = { client: 'mssql', debug: false };
const services = require('.')(mockLogger, dbOpts);

describe('Services', () => {
  test('should return valid api', () => {
    expect(services).toHaveProperty('campaignApi');
    expect(typeof services.campaignApi).toBe('function');

    expect(services).toHaveProperty('alertApi');
    expect(typeof services.alertApi).toBe('function');

    expect(services).toHaveProperty('contentApi');
    expect(typeof services.contentApi).toBe('function');

    expect(services).toHaveProperty('variableMappingsApi');
    expect(typeof services.variableMappingsApi).toBe('function');

    expect(services).toHaveProperty('variableMappingsApiV2');
    expect(typeof services.variableMappingsApiV2).toBe('function');
  });
});
