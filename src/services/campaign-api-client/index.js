const qs = require('qs');
const { getRequestHeaders } = require('./common');
const { addVersionToPlatformTargetingData } = require('../../utils');

const commonReqParams = (config) => ({
  baseURL: config.url,
  headers: getRequestHeaders(),
  timeout: config.timeout,
});

const createCampaign = ({ axios }, config, ruleData) => (
  axios.request({
    ...commonReqParams(config),
    url: '/v1/campaign-rules',
    method: 'post',
    data: addVersionToPlatformTargetingData(ruleData),
  })
);

const deleteCampaign = ({ axios }, config, ruleId) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/campaign-rules/${ruleId}`,
    method: 'delete',
  })
);

const getAllCampaigns = ({ axios }, config, query) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/campaign-rules${qs.stringify(query, { addQueryPrefix: true })}`,
    method: 'get',
  })
);

const getAllCampaignsByAccess = ({ axios }, config, query, data) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/campaign-rules/fetch${qs.stringify(query, { addQueryPrefix: true })}`,
    method: 'post',
    data,
  })
);

const getCampaign = ({ axios }, config, ruleId) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/campaign-rules/${ruleId}`,
    method: 'get',
  })
);

const patchCampaign = ({ axios }, config, ruleId, body) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/campaign-rules/${ruleId}`,
    method: 'patch',
    data: body,
  })
);

const updateCampaign = ({ axios }, config, ruleId, ruleData) => (
  axios.request({
    ...commonReqParams(config),
    url: `/v1/campaign-rules/${ruleId}`,
    method: 'patch',
    data: addVersionToPlatformTargetingData(ruleData),
  })
);

const init = ({ axios }, config) => {
  return {
    createCampaign: (ruleData) => createCampaign({ axios }, config, ruleData),
    getAllCampaigns: (query) => getAllCampaigns({ axios }, config, query),
    getAllCampaignsByAccess: (query, data) => getAllCampaignsByAccess({ axios }, config, query, data),
    getCampaign: (ruleId) => getCampaign({ axios }, config, ruleId),
    deleteCampaign: (ruleId) => deleteCampaign({ axios }, config, ruleId),
    updateCampaign: (ruleId, body) => updateCampaign({ axios }, config, ruleId, body),
    patchCampaign: (ruleId, body) => patchCampaign({ axios }, config, ruleId, body),
  };
};

module.exports = init;
