const qs = require('qs');
const CampaignApiClient = require('.');

// mock axios
const mockRequest = jest.fn();
const axios = {
  request: mockRequest,
};
// mock config
const config = {
  url: 'http://localhost/v1',
};

describe('Campaign API client', () => {
  test('should have createCampaign method', () => {
    const client = CampaignApiClient({ axios }, config);
    expect(client).toHaveProperty('createCampaign');
    expect(typeof client.createCampaign).toEqual('function');
  });

  test('should have updateCampaign method', () => {
    const client = CampaignApiClient({ axios }, config);
    expect(client).toHaveProperty('updateCampaign');
    expect(typeof client.updateCampaign).toEqual('function');
  });

  test('should have getCampaign method', () => {
    const client = CampaignApiClient({ axios }, config);
    expect(client).toHaveProperty('getCampaign');
    expect(typeof client.getCampaign).toEqual('function');
  });

  test('should have getAllCampaigns method', () => {
    const client = CampaignApiClient({ axios }, config);
    expect(client).toHaveProperty('getAllCampaigns');
    expect(typeof client.getAllCampaigns).toEqual('function');
  });

  test('should have patchCampaign method', () => {
    const client = CampaignApiClient({ axios }, config);
    expect(client).toHaveProperty('patchCampaign');
    expect(typeof client.patchCampaign).toEqual('function');
  });

  test('should successfully call createCampaign', async() => {
    const mockResponse = {
      status: 200,
      data: {},
    };
    const mockRuleData = {};
    mockRequest.mockReturnValueOnce(mockResponse);
    const client = CampaignApiClient({ axios }, config);
    const result = await client.createCampaign(mockRuleData);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/campaign-rules`);
    expect(result).toEqual(mockResponse);
  });

  test('should successfully call deleteCampaign', async() => {
    const mockResponse = {
      status: 200,
      data: {},
    };
    const mockRuleId = '123456ab';
    mockRequest.mockReturnValueOnce(mockResponse);
    const client = CampaignApiClient({ axios }, config);
    const result = await client.deleteCampaign(mockRuleId);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/campaign-rules/${mockRuleId}`);
    expect(result).toEqual(mockResponse);
  });

  test('should successfully call getAllCampaigns', async() => {
    const mockResponse = {
      status: 200,
      data: {},
    };
    const mockQuery = {
      'item1': 'value1',
      'item2': 'value2',
      'item3': 'value3',
    };
    mockRequest.mockReturnValueOnce(mockResponse);
    const client = CampaignApiClient({ axios }, config);
    const result = await client.getAllCampaigns(mockQuery);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/campaign-rules?${qs.stringify(mockQuery)}`);
    expect(result).toEqual(mockResponse);
  });

  test('should successfully call getCampaign', async() => {
    const mockResponse = {
      status: 200,
      data: {},
    };
    const mockRuleId = '123456ab';
    mockRequest.mockReturnValueOnce(mockResponse);
    const client = CampaignApiClient({ axios }, config);
    const result = await client.getCampaign(mockRuleId);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/campaign-rules/${mockRuleId}`);
    expect(result).toEqual(mockResponse);
  });

  test('should successfully call patchCampaign', async() => {
    const mockResponse = {
      status: 200,
      data: {},
    };
    const mockRuleId = '123456ab';
    const mockBody = {};
    mockRequest.mockReturnValueOnce(mockResponse);
    const client = CampaignApiClient({ axios }, config);
    const result = await client.patchCampaign(mockRuleId, mockBody);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/campaign-rules/${mockRuleId}`);
    expect(result).toEqual(mockResponse);
  });

  test('should successfully call updateCampaign', async() => {
    const mockResponse = {
      status: 200,
      data: {},
    };
    const mockRuleId = '123456ab';
    const mockBody = {};
    mockRequest.mockReturnValueOnce(mockResponse);
    const client = CampaignApiClient({ axios }, config);
    const result = await client.updateCampaign(mockRuleId, mockBody);
    expect(mockRequest).toBeCalled();
    expect(mockRequest.mock.calls.length).toEqual(1);
    expect(mockRequest.mock.calls[0][0].url).toEqual(`/v1/campaign-rules/${mockRuleId}`);
    expect(result).toEqual(mockResponse);
  });
});
