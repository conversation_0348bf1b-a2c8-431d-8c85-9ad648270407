const { TABLE } = require('../constants/db');

const ruleSubTypeService = db => {
  const getRuleSubTypes = () => {
    return db(TABLE.rule_sub_type).select(
      'admin_rule_sub_type_id AS id',
      'admin_rule_sub_type_description AS description',
      'admin_rule_sub_type_name AS type',
      'admin_rule_type_id AS ruleTypeId',
    );
  };

  return {
    getRuleSubTypes,
  };
};

module.exports = ruleSubTypeService;
