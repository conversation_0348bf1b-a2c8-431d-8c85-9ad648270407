const { TABLE, COLUMN, JOIN, SELECT } = require('../constants/db');
const { TRANSFORM_RESULT } = require('../utils/db');
const { mapValues, groupBy } = require('lodash');

const permissionService = db => {
  const getPermissionsForUser = async(userId) => (
    db(TABLE.users_roles)
      .where(COLUMN.users_roles.user_id, userId)
      .join(...JOIN.users_roles_role)
      .join(...JOIN.role_roles_permissions)
      .where(COLUMN.role.status, 1)
      .pluck(COLUMN.roles_permissions.permission_name)
      .distinct()
  );

  const _getTeamAccessByTeamId = async(teamId) => {
    const getContainers = async() => {
      return db(TABLE.access_containers)
        .select(
          SELECT.application.applicationId,
          SELECT.container.containerId,
          'admin_rule_type_slug AS rule_type',
          SELECT.access_levels.access_name,
        )
        .leftJoin(...JOIN.access_containers_application)
        .leftJoin(...JOIN.access_containers_container)
        .leftJoin(...JOIN.access_containers_rule_type)
        .leftJoin(...JOIN.access_containers_access_levels)
        .where(COLUMN.access_containers.team_id, teamId);
    };

    const getPages = async() => {
      return db(TABLE.access_pages)
        .select(
          SELECT.application.applicationId,
          SELECT.page.pageId,
          'admin_rule_type_slug AS rule_type',
          SELECT.access_levels.access_name,
        )
        .leftJoin(...JOIN.access_pages_application)
        .leftJoin(...JOIN.access_pages_page)
        .leftJoin(...JOIN.access_pages_rule_type)
        .leftJoin(...JOIN.access_pages_access_levels)
        .where(COLUMN.access_pages.team_id, teamId);
    };

    const getRuleSubTypes = async() => {
      return db(TABLE.access_rule_sub_types) // Rule sub types only exist for campaigns
        .select(
          SELECT.application.applicationId,
          'admin_rule_sub_type_name AS rule_sub_type',
          SELECT.access_levels.access_name,
        )
        .leftJoin(...JOIN.access_rule_sub_types_application)
        .leftJoin(...JOIN.access_rule_sub_types_rule_sub_type)
        .leftJoin(...JOIN.access_rule_sub_types_access_levels)
        .where(COLUMN.access_rule_sub_types.team_id, teamId);
    };

    const [ containers, pages, ruleSubTypes ] = await Promise.all([
      getContainers(),
      getPages(),
      getRuleSubTypes(),
    ]);

    return {
      campaigns: {
        containers: mapValues(
          groupBy(containers.filter(c => c.rule_type !== 'alert'), 'applicationId'),
          list => mapValues(
            groupBy(list, 'access_name'),
            listInner => listInner.map(({ containerId }) => containerId)),
        ),
        pages: mapValues(
          groupBy(pages.filter(p => p.rule_type !== 'alert'), 'applicationId'),
          list => mapValues(
            groupBy(list, 'access_name'),
            listInner => listInner.map(({ pageId }) => pageId)),
        ),
        ruleSubTypes: mapValues(
          groupBy(ruleSubTypes, 'applicationId'),
          list => mapValues(
            groupBy(list, 'access_name'),
            // eslint-disable-next-line camelcase
            listInner => listInner.map(({ rule_sub_type }) => rule_sub_type)),
        ),
      },
      alerts: {
        containers: mapValues(
          groupBy(containers.filter(c => c.rule_type === 'alert'), 'applicationId'),
          list => mapValues(
            groupBy(list, 'access_name'),
            listInner => listInner.map(({ containerId }) => containerId)),
        ),
        pages: mapValues(
          groupBy(pages.filter(p => p.rule_type === 'alert'), 'applicationId'),
          list => mapValues(
            groupBy(list, 'access_name'),
            listInner => listInner.map(({ pageId }) => pageId)),
        ),
      },
      ccau_campaign: {
        containers: mapValues(
          groupBy(containers.filter(c => c.rule_type === 'ccau_campaign'), 'applicationId'),
          list => mapValues(
            groupBy(list, 'access_name'),
            listInner => listInner.map(({ containerId }) => containerId)),
        ),
        pages: mapValues(
          groupBy(pages.filter(p => p.rule_type === 'ccau_campaign'), 'applicationId'),
          list => mapValues(
            groupBy(list, 'access_name'),
            listInner => listInner.map(({ pageId }) => pageId)),
        ),
      },
    };
  };

  const getAccessForUser = async(userId) => {
    // Need to retrieve all the application, container, and page id's the user's team has access to
    const result = await db(TABLE.users_roles)
      .select(SELECT.roles_permissions.team_id)
      .leftJoin(...JOIN.users_roles_roles_permissions)
      .where(COLUMN.users_roles.user_id, userId);
    const teamId = TRANSFORM_RESULT(result, { teamId: 'team_id' }, true)[0].teamId[0];
    return _getTeamAccessByTeamId(teamId);
  };

  return {
    getPermissionsForUser,
    getAccessForUser,
  };
};

module.exports = permissionService;
