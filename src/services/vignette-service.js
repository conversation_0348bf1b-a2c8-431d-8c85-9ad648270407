const { COLUMN, SELECT, TABLE, JOIN } = require('../constants/db');

let _db;
let _logger;

const vignetteService = (db, logger) => {
  _logger = logger;
  _db = db;

  const getCampaignSetupInfo = async() => {
    const result = await Promise.all([
      _db(TABLE.business_line)
        .select(COLUMN.business_line)
        .on('query-error', (error, obj) => _handleQueryError(error)),

      _db(TABLE.registration_status)
        .select(COLUMN.registration_status)
        .on('query-error', (error, obj) => _handleQueryError(error)),

      _db(TABLE.device)
        .select(COLUMN.device)
        .on('query-error', (error, obj) => _handleQueryError(error)),

      _db(TABLE.province)
        .select(COLUMN.province)
        .on('query-error', (error, obj) => _handleQueryError(error)),

      _db(TABLE.product)
        .select(COLUMN.product, SELECT.product_sub_category.sub_category_description)
        .innerJoin(...JOIN.product_sub_product)
        .on('query-error', (error, obj) => _handleQueryError(error)),
    ]);

    return {
      businessLines: result[0],
      registrationStatus: result[1],
      device: result[2],
      geography: result[3],
      products: result[4],
    };
  };

  function _handleQueryError(error, obj) {
    _logger.error(error);
  }

  return {
    getCampaignSetupInfo,
  };
};

module.exports = vignetteService;
