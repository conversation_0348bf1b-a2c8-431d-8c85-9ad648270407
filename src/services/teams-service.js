const { TABLE, COL_NAME, COLUMN, SELECT, JOIN } = require('../constants/db');
const { TRANSFORM_RESULT, TRANSFORM_INPUT, SORT_AND_PAGINATE } = require('../utils/db');
const {
  APPLICATION_OWNER_PERMISSION_LIST,
  VIEW_PERMISSION_LIST,
  FULL_PERMISSION_LIST,
  STATIC_PERMISSION_SECTIONS,
  FLAGS,
  permissions: allPermissions,
} = require('../permissions/permission-constants');
const { mapAccessToRulePermissions } = require('../permissions/utils');
const { concat, difference, differenceWith, flattenDeep, isEmpty, mapValues, uniq, findKey } = require('lodash');
const { BadRequestError } = require('../error');

const teamsService = ({ dbClient: { query: db, trx }, userService }) => {
  /**
   * Returns access level table by id - { 1: 'alert', 2: 'campaign' }
   * @param {*} accessLevels
   * @returns {}
   */
  const _getAccessLevelIdMap = (accessLevels) => {
    return accessLevels.reduce((all, a) => {
      return {
        ...all,
        [a[COL_NAME.access_levels.access_id]]: a[COL_NAME.access_levels.access_name],
      };
    }, {});
  };

  /**
   * Returns access level table by name - { manage: 2, view: 1 }
   * @param {*} accessLevels
   * @returns {}
   */
  const _getAccessLevelNameMap = (accessLevels) => {
    return accessLevels.reduce((all, a) => {
      return {
        ...all,
        [a[COL_NAME.access_levels.access_name]]: a[COL_NAME.access_levels.access_id],
      };
    }, {});
  };

  const _deleteAccess = async(teamId, access = {}, accessLevelNameMap = {}) => {
    if (isEmpty(access)) {
      await db(TABLE.access_containers)
        .delete()
        .where(COLUMN.access_containers.team_id, teamId);
      await db(TABLE.access_pages)
        .delete()
        .where(COLUMN.access_pages.team_id, teamId);
      await db(TABLE.access_rule_sub_types)
        .delete()
        .where(COLUMN.access_rule_sub_types.team_id, teamId);
    } else {
      if (access.containers) {
        const promises = access.containers.map(i => db(TABLE.access_containers).delete().where({
          [COL_NAME.access_containers.team_id]: teamId,
          [COL_NAME.access_containers.rule_type_id]: i.rule_type_id,
          [COL_NAME.access_containers.application_id]: i.application_id,
          [COL_NAME.access_containers.container_id]: i.container_id,
          [COL_NAME.access_containers.active]: 1,
          [COL_NAME.access_containers.access_id]: accessLevelNameMap[i.access],
        }));
        await Promise.all(promises);
      }
      if (access.pages) {
        const promises = access.pages.map(i => db(TABLE.access_pages).delete().where({
          [COL_NAME.access_pages.team_id]: teamId,
          [COL_NAME.access_pages.rule_type_id]: i.rule_type_id,
          [COL_NAME.access_pages.application_id]: i.application_id,
          [COL_NAME.access_pages.page_id]: i.page_id,
          [COL_NAME.access_pages.active]: 1,
          [COL_NAME.access_pages.access_id]: accessLevelNameMap[i.access],
        }));
        await Promise.all(promises);
      }
      if (access.ruleSubTypes) {
        const promises = access.ruleSubTypes.map(i => db(TABLE.access_rule_sub_types).delete().where({
          [COL_NAME.access_rule_sub_types.team_id]: teamId,
          [COL_NAME.access_rule_sub_types.rule_type_id]: i.rule_type_id,
          [COL_NAME.access_rule_sub_types.application_id]: i.application_id,
          [COL_NAME.access_rule_sub_types.rule_sub_type_id]: i.rule_sub_type_id,
          [COL_NAME.access_rule_sub_types.active]: 1,
          [COL_NAME.access_rule_sub_types.access_id]: accessLevelNameMap[i.access],
        }));
        await Promise.all(promises);
      }
    }
  };

  // mssql limit: numberOfFields * chunkSize < 2100
  // numberOfFields = 6 (team_id, rule_type_id, application_id, etc.), therefore chunkSize = 350
  const chunkSize = 330;
  const _insertAccess = async(teamId, access, accessLevelNameMap, dbClient = db) => {
    if (access) {
      if (access.containers) {
        const chunkedArray = [];
        for (let i = 0; i < access.containers.length; i += chunkSize) {
          chunkedArray.push(access.containers.slice(i, i + chunkSize));
        }
        const promises = [];
        chunkedArray.forEach(chunk => {
          promises.push(dbClient(TABLE.access_containers).insert(
            chunk.map((i) => ({
              [COL_NAME.access_containers.team_id]: teamId,
              [COL_NAME.access_containers.rule_type_id]: i.rule_type_id,
              [COL_NAME.access_containers.application_id]: i.application_id,
              [COL_NAME.access_containers.container_id]: i.container_id,
              [COL_NAME.access_containers.active]: 1,
              [COL_NAME.access_containers.access_id]: accessLevelNameMap[i.access],
            })),
          ));
        });
        await Promise.all(promises);
      }
      if (access.pages) {
        const chunkedArray = [];
        for (let i = 0; i < access.pages.length; i += chunkSize) {
          chunkedArray.push(access.pages.slice(i, i + chunkSize));
        }
        const promises = [];
        chunkedArray.forEach(chunk => {
          promises.push(dbClient(TABLE.access_pages).insert(
            chunk.map((i) => ({
              [COL_NAME.access_pages.team_id]: teamId,
              [COL_NAME.access_pages.rule_type_id]: i.rule_type_id,
              [COL_NAME.access_pages.application_id]: i.application_id,
              [COL_NAME.access_pages.page_id]: i.page_id,
              [COL_NAME.access_pages.active]: 1,
              [COL_NAME.access_pages.access_id]: accessLevelNameMap[i.access],
            })),
          ));
        });
        await Promise.all(promises);
      }
      if (access.ruleSubTypes && access.ruleSubTypes.length) {
        await dbClient(TABLE.access_rule_sub_types).insert(
          access.ruleSubTypes.map((i) => ({
            [COL_NAME.access_rule_sub_types.team_id]: teamId,
            [COL_NAME.access_rule_sub_types.rule_type_id]: i.rule_type_id,
            [COL_NAME.access_rule_sub_types.application_id]: i.application_id,
            [COL_NAME.access_rule_sub_types.rule_sub_type_id]: i.rule_sub_type_id,
            [COL_NAME.access_rule_sub_types.active]: 1,
            [COL_NAME.access_rule_sub_types.access_id]: accessLevelNameMap[i.access],
          })),
        );
      }
    }
  };

  // Since teams access table only stores manage, need to map these to review and approve permissions where applicable
  const _getReviewManagePermissions = (permissionList) => {
    const reviewApprovePermissions = [];
    if (permissionList.includes(allPermissions.PEGA_VARIABLE_MAPPING_MANAGE)) {
      reviewApprovePermissions.push(allPermissions.PEGA_VARIABLE_MAPPING_REVIEW);
      reviewApprovePermissions.push(allPermissions.PEGA_VARIABLE_MAPPING_APPROVE);
    }
    // if (permissionList.includes(allPermissions.KT_VARIABLE_MAPPING_MANAGE)) {
    //   reviewApprovePermissions.push(allPermissions.KT_VARIABLE_MAPPING_REVIEW);
    //   reviewApprovePermissions.push(allPermissions.KT_VARIABLE_MAPPING_APPROVE);
    // }
    return reviewApprovePermissions;
  };

  const _insertTeamsPermissions = async(permissions, teamId, dbClient = db) => {
    permissions.length && await dbClient(TABLE.teams_permissions).insert(
      permissions.map((i) => ({
        [COL_NAME.teams_permissions.team_id]: teamId,
        [COL_NAME.teams_permissions.permission_id]: 0, // TODO: should not be required by db schema
        [COL_NAME.teams_permissions.permission_name]: i,
      })),
    );
  };

  const _insertRolesPermissions = async(permissions, roleId, teamId, dbClient = db) => {
    permissions.length && await dbClient(TABLE.roles_permissions).insert(
      permissions.map((permission) => ({
        [COL_NAME.roles_permissions.role_id]: roleId,
        [COL_NAME.roles_permissions.permission_id]: 0, // TODO: should not be required by db schema
        [COL_NAME.roles_permissions.permission_name]: permission,
        [COL_NAME.roles_permissions.team_id]: teamId,
      })),
    );
  };

  const _addPlaceholderPermission = async(id, teamRolesWithDeletedPermissions) => {
    const teamRoles = await Promise.all(
      teamRolesWithDeletedPermissions.map(async roleId => {
        let roles = await db(TABLE.roles_permissions)
          .select(COLUMN.roles_permissions.role_id)
          .where(COLUMN.roles_permissions.role_id, roleId);
        roles = roles.map(r => r.admin_role_id)[0];
        return roles;
      }),
    );

    const rolesWithNoPermissions = difference(teamRolesWithDeletedPermissions, teamRoles);

    if (rolesWithNoPermissions.length) {
      const placeholderPermission = await db(TABLE.teams_permissions)
        .select(SELECT.teams_permissions.permission_name)
        .where(COLUMN.teams_permissions.team_id, id)
        .where(COLUMN.teams_permissions.permission_name, 'placeholder');
      if (!placeholderPermission || !placeholderPermission.length) {
        await db(TABLE.teams_permissions).insert({
          [COL_NAME.teams_permissions.team_id]: id,
          [COL_NAME.teams_permissions.permission_id]: 0,
          [COL_NAME.teams_permissions.permission_name]: 'placeholder',
        });
      }
      rolesWithNoPermissions.length && await db(TABLE.roles_permissions).insert(
        rolesWithNoPermissions.map((roleId) => ({
          [COL_NAME.roles_permissions.role_id]: roleId,
          [COL_NAME.roles_permissions.permission_id]: 0,
          [COL_NAME.roles_permissions.permission_name]: 'placeholder',
          [COL_NAME.roles_permissions.team_id]: id,
        })),
      );
    }
  };

  const _getUserIds = async(dbClient = db, users) => {
    let userIds = [];
    for (const user of users) {
      // check if the user exists first
      const foundUser = (
        await dbClient(TABLE.user)
          .select(SELECT.user.id)
          .where(TRANSFORM_INPUT(user, COLUMN.user))
      )[0];
      if (foundUser && foundUser.id) {
        userIds.push(foundUser.id);
      } else {
        // TODO allow db client to be passed in so transaction steps failed after this step can be rolled back
        const createdUser = await userService.createUser(user, dbClient);
        userIds.push(createdUser.id);
      }
    }
    return userIds;
  };

  const _getTeamAccessByTeamIds = async(teamIds, dbClient = db) => {
    if (!teamIds || !teamIds.length) {
      return [];
    }

    const accessQueries = [
      dbClient(TABLE.access_levels)
        .select(
          COLUMN.access_levels.access_id,
          COLUMN.access_levels.access_name,
        ),
      dbClient(TABLE.access_containers)
        .select(
          SELECT.access_containers.team_id,
          SELECT.access_containers.application_id,
          SELECT.access_containers.rule_type_id,
          SELECT.access_containers.container_id,
          SELECT.access_containers.access_id,
        ).whereIn(
          COLUMN.access_containers.team_id, teamIds,
        ),
      dbClient(TABLE.access_pages)
        .select(
          SELECT.access_pages.team_id,
          SELECT.access_pages.application_id,
          SELECT.access_pages.rule_type_id,
          SELECT.access_pages.page_id,
          SELECT.access_pages.access_id,
        ).whereIn(
          COLUMN.access_pages.team_id, teamIds,
        ),
      dbClient(TABLE.access_rule_sub_types)
        .select(
          SELECT.access_rule_sub_types.team_id,
          SELECT.access_rule_sub_types.application_id,
          SELECT.access_rule_sub_types.rule_type_id,
          SELECT.access_rule_sub_types.rule_sub_type_id,
          SELECT.access_rule_sub_types.access_id,
        ).whereIn(
          COLUMN.access_rule_sub_types.team_id, teamIds,
        ),
    ];

    const [
      accessLevels,
      accessContainers,
      accessPages,
      accessRuleSubTypes,
    ] = await Promise.all(accessQueries);

    const accessLevelIdMap = _getAccessLevelIdMap(accessLevels);

    const containers = accessContainers.map((c) => ({
      ...c,
      access: accessLevelIdMap[c.access_id],
      access_id: undefined,
    }));
    const pages = accessPages.map((c) => ({
      ...c,
      access: accessLevelIdMap[c.access_id],
      access_id: undefined,
    }));
    const ruleSubTypes = accessRuleSubTypes.map((c) => ({
      ...c,
      access: accessLevelIdMap[c.access_id],
      access_id: undefined,
    }));

    const result = teamIds.map(teamId => ({
      teamId,
      containers: containers.filter(c => c.team_id === teamId),
      pages: pages.filter(p => p.team_id === teamId),
      ruleSubTypes: ruleSubTypes.filter(rs => rs.team_id === teamId),
    }));

    return result;
  };

  const getTeam = async(id) => {
    const foundTeam = await getTeams({ id });
    return foundTeam && foundTeam.items && foundTeam.items.length > 0 ? foundTeam.items[0] : null;
  };

  const getTeamOwners = async(query = {}) => {
    if (query.application_id) {
      const allTeamIds = (await db(TABLE.team)
        .select(COLUMN.team.id))
        .map(t => t.admin_team_id);
      const accesses = await _getTeamAccessByTeamIds(allTeamIds);

      let filteredTeamIds = await Promise.all(
        allTeamIds.map(async(teamId) => {
          const teamAccess = accesses.find(a => a.teamId === teamId) || {};
          const accessApplicationIds = uniq(
            concat(teamAccess.containers, teamAccess.pages, teamAccess.ruleSubTypes).map(
              (e) => e.application_id,
            ),
          );
          if (!accessApplicationIds.includes(parseInt(query.application_id))) {
            return null;
          }
          return teamId;
        }),
      );
      filteredTeamIds = filteredTeamIds.filter(t => !!t);

      const teamOwners = await db(TABLE.team)
        .select(SELECT.user.id)
        .leftJoin(...JOIN.team_roles_permissions)
        .leftJoin(...JOIN.roles_permissions_role)
        .leftJoin(...JOIN.role_users_roles)
        .leftJoin(...JOIN.users_roles_user)
        .whereIn(COLUMN.team.id, filteredTeamIds)
        .andWhere(COLUMN.role.name, 'like', '%Team owner%');
      return uniq(teamOwners.map(o => o.id));
    }

    const users = await db(TABLE.role)
      .select(SELECT.user.id)
      .leftJoin(...JOIN.role_users_roles)
      .leftJoin(...JOIN.users_roles_user)
      .where(COLUMN.role.name, 'like', '%Team owner%');
    return uniq(users.map(o => o.id));
  };

  /**
   * @param {string} flag - flag to limit unecessary db operations based on FE page calling the service, only one flag should be used per request
   +------------------------------+----------+------------+-------------------------+---------+------------------------+
   |                              | No flag  | skipAccess | skipAllExceptTeamOwners | skipAll | includeFunctionalities |
   +==============================+==========+============+=========================+=========+========================+
   | access                       |    y     |      n     |             n           |    n    |           n            |
   +------------------------------+----------+------------+-------------------------+---------+------------------------+
   | accessApplicationIds         |    y     |      n     |             n           |    n    |           y            |
   +------------------------------+----------+------------+-------------------------+---------+------------------------+
   | functionalities (rule types) |    n     |      n     |             n           |    n    |           y            |
   +------------------------------+----------+------------+-------------------------+---------+------------------------+
   | ownerRoldId                  |    y     |      y     |             y           |    n    |           y            |
   +------------------------------+----------+------------+-------------------------+---------+------------------------+
   | permissions                  |    y     |      y     |             n           |    n    |           y            |
   +------------------------------+----------+------------+-------------------------+---------+------------------------+
   * @param {*} query - search/filter queries
   * @returns teams
  */
  const getTeams = async({ dbClient = db, flag, ...query }) => {
    let sqlStatement;

    // get basic team info
    if (flag === FLAGS.SKIP_ALL) {
      sqlStatement = dbClient(TABLE.team)
        .select(SELECT.team.id, SELECT.team.name, SELECT.team.active)
        .where(TRANSFORM_INPUT(query, COLUMN.team));
    } else {
      sqlStatement = dbClient(TABLE.team)
        .select(
          COLUMN.team,
          COLUMN.roles_permissions.role_id,
          COLUMN.teams_permissions.permission_name,
        )
        .leftJoin(...JOIN.team_roles_permissions)
        .leftJoin(...JOIN.team_teams_permissions)
        .where(TRANSFORM_INPUT(query, COLUMN.team));
    }
    if (query.owner_id) {
      sqlStatement
        .leftJoin(...JOIN.roles_permissions_users_roles)
        .where(COL_NAME.users_roles.user_id, query.owner_id);
    }
    if (query.search) {
      sqlStatement.where(COL_NAME.team.name, 'LIKE', `%${query.search}%`);
    }
    const result = await sqlStatement;
    if (!result || result.length === 0) {
      return [];
    }

    let basicTeamsInfo = TRANSFORM_RESULT(result, {
      roleIds: COL_NAME.roles_permissions.role_id,
      ...(![ FLAGS.SKIP_ALL, FLAGS.SKIP_ALL_EXCEPT_TEAM_OWNERS ].includes(flag) && { permissions: COL_NAME.teams_permissions.permission_name }),
    }, true);

    let functionalityId;
    if (query.functionality_id) {
      if (Object.keys(STATIC_PERMISSION_SECTIONS).includes(query.functionality_id)) {
        basicTeamsInfo = basicTeamsInfo.filter(team => {
          const teamStaticPermissions = uniq(team.permissions.map((permission) =>
            findKey(STATIC_PERMISSION_SECTIONS, (o) => o.find((p) => p === permission)),
          ).filter(p => !!p));
          return teamStaticPermissions.includes(query.functionality_id);
        });
      } else {
        const ruleTypes = await dbClient(TABLE.rule_type).select(COLUMN.rule_type.rule_type, COLUMN.rule_type.id);
        const ruleType = ruleTypes.find(rt => rt.admin_rule_type_name === query.functionality_id);
        functionalityId = ruleType && ruleType.admin_rule_type_id;
      }
    }

    // get owner role ids
    let ownerRoleIds = [];
    if (flag !== FLAGS.SKIP_ALL) {
      const ownerRoleIdsRes = await dbClient(TABLE.role)
        .select(SELECT.role.id)
        .where(COLUMN.role.name, 'like', '%Team owner%');
      ownerRoleIds = ownerRoleIdsRes.map((r) => r.id);
    }
    const skipAccessQueries = [ FLAGS.SKIP_ALL, FLAGS.SKIP_ACCESS, FLAGS.SKIP_ALL_EXCEPT_TEAM_OWNERS ].includes(flag);
    const accesses = skipAccessQueries
      ? []
      : await _getTeamAccessByTeamIds(basicTeamsInfo.map(team => team.id), dbClient);

    const teams = await Promise.all(
      basicTeamsInfo.map(async(team) => {
        team.ownerRoleId = team.roleIds.find(id => ownerRoleIds.includes(id));
        if (flag === FLAGS.SKIP_ALL_EXCEPT_TEAM_OWNERS) {
          delete team.admin_permission_name;
        }
        if (skipAccessQueries) {
          delete team.roleIds;
          return team;
        }

        const teamAccess = accesses.find(a => a.teamId === team.id) || {};
        delete teamAccess.teamId;
        team.access = teamAccess;

        if (flag === FLAGS.INCLUDE_FUNCTIONALITIES) {
          team.functionalities = _mapAccessToFunctionalities(teamAccess);
          if (query.functionality_id && functionalityId && !team.functionalities.includes(functionalityId)) {
            return null;
          }
          delete team.access;
        }

        team.accessApplicationIds = uniq(
          concat(teamAccess.containers, teamAccess.pages, teamAccess.ruleSubTypes).map(
            (e) => e.application_id,
          ),
        );
        if (query.application_id && !team.accessApplicationIds.includes(parseInt(query.application_id))) {
          return null;
        }

        delete team.roleIds;
        return team;
      }),
    );
    const data = SORT_AND_PAGINATE(teams.filter(t => !!t), query);
    return data;
  };

  const createTeam = async(id) => {
    const newTeamId = await trx(async t => _createTeam(t, id));
    return getTeam(newTeamId);
  };

  const _createTeam = async(dbTrx, payload) => {
    const { owners, access, permissions: rawPermission, ...team } = payload;
    const permissions = rawPermission.filter(p => p !== allPermissions.TEAMS_MANAGE_SUPER);
    const ownerIds = await _getUserIds(dbTrx, owners); // get owner user ids, create if new
    const ruleTypes = await dbTrx(TABLE.rule_type).select(COLUMN.rule_type.rule_type, COLUMN.rule_type.id);
    const accessLevels = await dbTrx(TABLE.access_levels).select(COLUMN.access_levels.access_id, COLUMN.access_levels.access_name);
    const duplicateTeamNames = await dbTrx(TABLE.team).select(COLUMN.team.name).where(COLUMN.team.name, team.name);
    if (duplicateTeamNames.length) throw new BadRequestError('Team name already exists');
    const newTeamId = (await dbTrx(TABLE.team)
      .insert({
        [COL_NAME.team.name]: team.name,
        [COL_NAME.team.description]: team.description,
        [COL_NAME.team.active]: team.active,
      })
      .returning([ COL_NAME.team.id ]))[0][COL_NAME.team.id];

    // Add team's access
    const accessLevelNameMap = _getAccessLevelNameMap(accessLevels);
    await _insertAccess(newTeamId, access, accessLevelNameMap, dbTrx);

    // Add team's permissions
    const rulePermissions = mapAccessToRulePermissions(access, ruleTypes);
    const reviewApprovePermissions = _getReviewManagePermissions(permissions);
    const fullPermissions = [ ...permissions, ...rulePermissions.fullPermissions, ...reviewApprovePermissions ];
    const viewerPermissions = permissions.filter((p) => VIEW_PERMISSION_LIST.includes(p));
    const viewPermissions = [ ...rulePermissions.viewPermissions, ...viewerPermissions ];
    await _insertTeamsPermissions(fullPermissions, newTeamId, dbTrx);

    // Create a new team owner role
    const ownerRoleId = (
      await dbTrx(TABLE.role)
        .insert(TRANSFORM_INPUT({ name: `Team owner ${newTeamId}` }, COL_NAME.role))
        .returning(COL_NAME.role.id)
    )[0][COL_NAME.role.id];
    // Map permission records to owner role
    await _insertRolesPermissions(fullPermissions, ownerRoleId, newTeamId, dbTrx);
    // Since owner is being reassigned from another team, delete all their previous team roles
    await dbTrx(TABLE.users_roles)
      .delete()
      .whereIn(COLUMN.users_roles.user_id, ownerIds);
    // Map owner role to users
    ownerIds.length && await dbTrx(TABLE.users_roles).insert(
      ownerIds.map((id) => ({
        [COL_NAME.users_roles.user_id]: id,
        [COL_NAME.users_roles.role_id]: ownerRoleId,
      })),
    );

    // Create a new viewer role for the team
    const viewerRoleId = (
      await dbTrx(TABLE.role)
        .insert(TRANSFORM_INPUT({ name: `Viewer ${newTeamId}` }, COL_NAME.role))
        .returning(COL_NAME.role.id)
    )[0][COL_NAME.role.id];
    // Map viewer role to users
    ownerIds.length && await dbTrx(TABLE.users_roles).insert(
      ownerIds.map((id) => ({
        [COL_NAME.users_roles.user_id]: id,
        [COL_NAME.users_roles.role_id]: viewerRoleId,
      })),
    );
    // Map permission records to viewer role
    await _insertRolesPermissions(viewPermissions, viewerRoleId, newTeamId, dbTrx);

    return newTeamId;
  };

  const updateTeam = async(id, payload) => {
    const { owners: ownersFromReq, access, permissions: rawPermission, active, ...team } = payload;
    const permissions = parseInt(id) === 1
      ? rawPermission
      : rawPermission.filter(p => p !== allPermissions.TEAMS_MANAGE_SUPER);
    const resolvedFetchPromises = await Promise.all([
      getTeam(id),
      db(TABLE.rule_type).select(COLUMN.rule_type.rule_type, COLUMN.rule_type.id),
      db(TABLE.access_levels).select(COLUMN.access_levels.access_id, COLUMN.access_levels.access_name),
      db(TABLE.role).select(SELECT.role.id).where(COLUMN.role.name, `Team owner ${id}`),
      db(TABLE.role).select(SELECT.role.id).where(COLUMN.role.name, `Viewer ${id}`),
      db(TABLE.team).select(COLUMN.team.name).where(COLUMN.team.name, team.name),
    ]);

    const currentTeam = resolvedFetchPromises[0];
    const ruleTypes = resolvedFetchPromises[1];
    const accessLevels = resolvedFetchPromises[2];
    const ownerRoleId = resolvedFetchPromises[3].map((r) => r.id)[0];
    const viewerRoleId = resolvedFetchPromises[4].map((r) => r.id)[0];
    const rulePermissions = mapAccessToRulePermissions(access, ruleTypes);
    const reviewApprovePermissions = _getReviewManagePermissions(permissions);
    const fullPermissions = [ ...permissions, ...rulePermissions.fullPermissions, ...reviewApprovePermissions ];
    const duplicateTeamNames = resolvedFetchPromises[5];

    if (team.name !== currentTeam.name && duplicateTeamNames.length) {
      throw new BadRequestError('Team name already exists');
    }

    // Protect Pigeon from invalid operations (fail safe)
    if (parseInt(id) === 1) {
      // KT permissions excluded from first release
      const removedPermissions = difference(FULL_PERMISSION_LIST, fullPermissions);
      if (removedPermissions.length) {
        throw new Error(`Permissions cannot be removed from the Pigeon team - ${removedPermissions[0]}`);
      } else if (!active) {
        throw new Error(`Cannot deactivate Pigeon team`);
      }
    }

    // Update team's name and description
    await db(TABLE.team)
      .update({
        [COL_NAME.team.name]: team.name,
        [COL_NAME.team.description]: team.description,
      })
      .where(COL_NAME.team.id, id);

    // Reset team's access
    const currentAccess = currentTeam.access;
    const accessToDelete = {
      containers: differenceWith(currentAccess.containers, access.containers, (a, b) => a.application_id === b.application_id && a.rule_type_id === b.rule_type_id && a.container_id === b.container_id && a.access === b.access),
      pages: differenceWith(currentAccess.pages, access.pages, (a, b) => a.application_id === b.application_id && a.rule_type_id === b.rule_type_id && a.page_id === b.page_id && a.access === b.access),
      ruleSubTypes: differenceWith(currentAccess.ruleSubTypes, access.ruleSubTypes, (a, b) => a.application_id === b.application_id && a.rule_type_id === b.rule_type_id && a.rule_sub_type_id === b.rule_sub_type_id && a.access === b.access),
    };
    const accessToAdd = {
      containers: differenceWith(access.containers, currentAccess.containers, (a, b) => a.application_id === b.application_id && a.rule_type_id === b.rule_type_id && a.container_id === b.container_id && a.access === b.access),
      pages: differenceWith(access.pages, currentAccess.pages, (a, b) => a.application_id === b.application_id && a.rule_type_id === b.rule_type_id && a.page_id === b.page_id && a.access === b.access),
      ruleSubTypes: differenceWith(access.ruleSubTypes, currentAccess.ruleSubTypes, (a, b) => a.application_id === b.application_id && a.rule_type_id === b.rule_type_id && a.rule_sub_type_id === b.rule_sub_type_id && a.access === b.access),
    };
    const accessLevelNameMap = _getAccessLevelNameMap(accessLevels);

    await _deleteAccess(id, accessToDelete, accessLevelNameMap);
    await _insertAccess(id, accessToAdd, accessLevelNameMap);

    // Delete permissions off all team's roles that the team no longer has
    const rolesWithDeletedPermissions = await db(TABLE.roles_permissions)
      .delete()
      .whereNotIn(COLUMN.roles_permissions.permission_name, fullPermissions)
      .where(COLUMN.roles_permissions.team_id, id)
      .returning(COL_NAME.roles_permissions.role_id);
    const teamRolesWithDeletedPermissions = uniq(rolesWithDeletedPermissions.map(r => r[COL_NAME.roles_permissions.role_id]));

    // Delete permissions off team that it no longer has
    await db(TABLE.teams_permissions)
      .delete()
      .whereNotIn(COLUMN.teams_permissions.permission_name, fullPermissions)
      .andWhere(COLUMN.teams_permissions.team_id, id);

    // Add any net new permissions to the team
    const newPermissions = difference(fullPermissions, currentTeam.permissions);
    await _insertTeamsPermissions(newPermissions, id);

    // Add any net new permissions to base roles
    await _insertRolesPermissions(newPermissions, ownerRoleId, id);
    await _insertRolesPermissions(newPermissions.filter(p => p.includes('_view')), viewerRoleId, id);

    // If a team is updated to have fewer permissions and as a result a role no longer has any permissions
    // - need assign a placeholder permission so role doesn't lose link to team
    await _addPlaceholderPermission(id, teamRolesWithDeletedPermissions);

    // update owner list - if any new owner doesn't exists, create them in the db
    const ownersInDbSql = await db(TABLE.user)
      .select(SELECT.user.id, SELECT.users_roles.role_id, SELECT.user.sid, SELECT.user.email)
      .leftJoin(...JOIN.user_users_roles)
      .where(COL_NAME.users_roles.role_id, ownerRoleId) // current owners
      .orWhere(builder => { // new owners
        ownersFromReq.forEach(o => builder.orWhere(TRANSFORM_INPUT(o, COLUMN.user)));
      });
    const ownersInDB = TRANSFORM_RESULT(ownersInDbSql, { roles: 'role_id' });

    const ownersFromReqLabelled = ownersFromReq.reduce((acc, uReq) => {
      const uDB = ownersInDB.find(uFound => {
        return uReq && uFound && (
          (uReq.id && uReq.id === uFound.id) || // user id provided, and exists in DB
          // user id not provided, but rest of info provided can still uniquely identify user in DB
          (!uReq.id && uReq.sid === uFound.sid && uReq.email === uFound.email)
        );
      });
      const isPreviousOwner = uDB && uDB.roles.includes(ownerRoleId);

      // attach labels
      uDB && acc.ownersInDB.push(uDB); // new owner exists in DB
      isPreviousOwner && acc.previousOwners.push(uDB); // existing owner
      !isPreviousOwner && uDB && acc.newOwnersInDB.push(uDB); // new owner require team change
      !uDB && acc.toCreate.push(uReq); // new owner need to created in DB

      return acc;
    }, { ownersInDB: [], previousOwners: [], newOwnersInDB: [], toCreate: [] });

    const oldOwnerIds = ownersInDB.filter(u => u.roles.includes(ownerRoleId)).map(u => u.id);
    const newOwnerIdsInDb = ownersFromReqLabelled.newOwnersInDB.map(u => u.id);
    const newOwnersToCreate = ownersFromReqLabelled.toCreate;
    const newOwnersCreated = await userService.createUsers(newOwnersToCreate);
    const newOwnerIdsCreated = newOwnersCreated.map(u => u.admin_user_id);
    const diffOwnerIds = differenceWith([ ...newOwnerIdsCreated, ...newOwnerIdsInDb ], oldOwnerIds);

    // update owner list - update roles for new owners added
    if (diffOwnerIds.length) {
      await Promise.all(
        diffOwnerIds.map(async(addedOwnerId) => {
          // Check if owner is from another team
          const previousTeamRoleIdsSql = await db(TABLE.roles_permissions)
            .select(COLUMN.roles_permissions.role_id)
            .leftJoin(...JOIN.roles_permissions_users_roles)
            .where(COLUMN.users_roles.user_id, addedOwnerId)
            .whereNot(COLUMN.roles_permissions.team_id, id);

          if (previousTeamRoleIdsSql.length) {
            const previousTeamRoleIds = TRANSFORM_RESULT(previousTeamRoleIdsSql, {
              roleIds: COL_NAME.users_roles.role_id,
            }, true)[0].roleIds;

            // Delete new owner's pervious team roles
            await db(TABLE.users_roles)
              .delete()
              .where(COLUMN.users_roles.user_id, addedOwnerId)
              .whereIn(COLUMN.users_roles.role_id, previousTeamRoleIds);

            // Add viewer role to new owner (new owner is from different team)
            await db(TABLE.users_roles).insert({
              [COL_NAME.users_roles.user_id]: addedOwnerId,
              [COL_NAME.users_roles.role_id]: viewerRoleId,
            });
          }
        }),
      );

      // update owner list - Add owner role to new owners (new owner is from same team or different team)
      diffOwnerIds.length && await db(TABLE.users_roles).insert(
        diffOwnerIds.map((addedOwnersId) => ({
          [COL_NAME.users_roles.user_id]: addedOwnersId,
          [COL_NAME.users_roles.role_id]: ownerRoleId,
        })),
      );

      // update owner list - Map viewer role to newly created users
      newOwnerIdsCreated.length && await db(TABLE.users_roles).insert(
        newOwnerIdsCreated.map((newOwnerId) => ({
          [COL_NAME.users_roles.user_id]: newOwnerId,
          [COL_NAME.users_roles.role_id]: viewerRoleId,
        })),
      );
    }

    // for users removed from owners list, remove their owner roles
    const newOwnerIds = ownersInDB.filter(u => ownersFromReq.some(o => o.id === u.id)).map(u => u.id);
    const removedOwners = differenceWith(oldOwnerIds, newOwnerIds, (a, b) => a === b);

    if (removedOwners.length) {
      await db(TABLE.users_roles)
        .delete()
        .whereIn(COLUMN.users_roles.user_id, removedOwners)
        .andWhere(COLUMN.users_roles.role_id, ownerRoleId);
    }

    if (active !== currentTeam.active) {
      setTeamStatusActive(id, active);
    }

    return getTeam(id);
  };

  const setTeamStatusActive = async(id, active = true, query = {}) => {
    // Protect Pigeon from invalid operations (fail safe)
    if (parseInt(id) === 1 && !active) {
      throw new Error(`Cannot deactivate Pigeon team`);
    }

    // set team active status
    await db(TABLE.team)
      .update(COLUMN.team.active, active)
      .where(COLUMN.team.id, id);

    // if team is deactivated, or if reactivated with reactivating children
    if (!active || query.activateChildren === 'true') {
      // get all users of the team
      let teamUserIds = await db(TABLE.team)
        .select(COLUMN.users_roles.user_id)
        .leftJoin(...JOIN.team_roles_permissions)
        .leftJoin(...JOIN.roles_permissions_users_roles)
        .where(COLUMN.team.id, id);
      teamUserIds = TRANSFORM_RESULT(
        teamUserIds,
        { userIds: COL_NAME.users_roles.user_id },
        true,
      )[0].userIds;

      // update all users' active status
      await db(TABLE.user)
        .update(COLUMN.user.active, active)
        .whereIn(COL_NAME.user.id, teamUserIds);

      // get all roles of the team
      const roleIds = await db(TABLE.role)
        .select(COLUMN.role.id)
        .leftJoin(...JOIN.role_roles_permissions)
        .leftJoin(...JOIN.roles_permissions_team)
        .where(COLUMN.team.id, id);

      // update all roles' active status
      await db(TABLE.role)
        .update(COL_NAME.role.status, active)
        .whereIn(COL_NAME.role.id, roleIds.map((m) => m.admin_role_id));
    } else {
      // team is reactivated without reactivating children - must still reactivate team owner/viewer roles & team owners
      const ownerRoleId = (
        await db(TABLE.role)
          .select(SELECT.role.id)
          .where(COLUMN.role.name, `Team owner ${id}`)
      ).map((r) => r.id)[0];
      const viewerRoleId = (
        await db(TABLE.role)
          .select(SELECT.role.id)
          .where(COLUMN.role.name, `Viewer ${id}`)
      ).map((r) => r.id)[0];
      await db(TABLE.role)
        .update(COL_NAME.role.status, active)
        .whereIn(COL_NAME.role.id, [ ownerRoleId, viewerRoleId ]);

      await db(TABLE.user)
        .leftJoin(...JOIN.user_users_roles)
        .update(COLUMN.user.active, 1)
        .where(COLUMN.users_roles.role_id, ownerRoleId);
    }

    // If team has been deactivated, reassign any of the applications it owns to Pigeon
    if (!active) {
      await db(TABLE.application)
        .update(COL_NAME.application.team_id, 1)
        .where(COL_NAME.application.team_id, id);

      // Delete the 'own application' permissions off team's roles
      await db(TABLE.roles_permissions)
        .delete()
        .whereIn(COLUMN.roles_permissions.permission_name, APPLICATION_OWNER_PERMISSION_LIST)
        .where(COLUMN.roles_permissions.team_id, id);

      // Delete the 'own application' permissions off team
      await db(TABLE.teams_permissions)
        .delete()
        .whereIn(COLUMN.teams_permissions.permission_name, APPLICATION_OWNER_PERMISSION_LIST)
        .where(COLUMN.teams_permissions.team_id, id);
    }

    return getTeam(id);
  };

  // Deletes all linked roles along with the team, reassigns users to Pigeon - only used in integration test
  const deleteTeam = async(id) => {
    // Protect Pigeon from invalid operations (fail safe)
    if (parseInt(id) === 1) {
      throw new Error(`Cannot delete Pigeon team`);
    }

    let userResult = await db(TABLE.user)
      .select(COLUMN.user.id)
      .leftJoin(...JOIN.user_users_roles)
      .leftJoin(...JOIN.users_roles_roles_permissions)
      .where(COLUMN.roles_permissions.team_id, id);
    userResult = TRANSFORM_RESULT(userResult, {
      userIds: COL_NAME.user.id,
    }, true);
    const teamUserIds = userResult && userResult.length ? userResult[0].userIds : [];

    let roleResult = await db(TABLE.role)
      .select(COLUMN.role.id)
      .leftJoin(...JOIN.role_users_roles)
      .leftJoin(...JOIN.users_roles_roles_permissions)
      .where(COLUMN.roles_permissions.team_id, id);
    roleResult = TRANSFORM_RESULT(roleResult, {
      roleIds: COL_NAME.role.id,
    }, true);
    const teamRoleIds = roleResult && roleResult.length ? roleResult[0].roleIds : [];

    const pigeonViewerRoleId = (
      await db(TABLE.role)
        .select(SELECT.role.id)
        .where(COLUMN.role.name, `Viewer 1`)
    ).map((r) => r.id)[0];

    // Remove team roles from all team users
    await db(TABLE.users_roles)
      .delete()
      .whereIn(COLUMN.users_roles.role_id, teamRoleIds);

    // Reassign team users as Pigeon viewers
    teamUserIds.length && await db(TABLE.users_roles).insert(
      teamUserIds.map((userId) => ({
        [COL_NAME.users_roles.user_id]: userId,
        [COL_NAME.users_roles.role_id]: pigeonViewerRoleId,
      })),
    );

    // Delete permissions off all team's roles
    await db(TABLE.roles_permissions)
      .delete()
      .where(COLUMN.roles_permissions.team_id, id);

    // Delete permissions off team
    await db(TABLE.teams_permissions)
      .delete()
      .where(COLUMN.teams_permissions.team_id, id);

    await _deleteAccess(id);

    // Delete team's roles
    await db(TABLE.role)
      .delete()
      .whereIn(COLUMN.role.id, teamRoleIds);

    // Delete team
    await db(TABLE.team)
      .delete()
      .where(COLUMN.team.id, id);

    return {};
  };

  /**
   * map team access matrix to list of functionalities
   * @param {*} access - team access matrix
   */
  const _mapAccessToFunctionalities = access => {
    const ruleTypesMapped = mapValues(access, acc => acc.map(a => a.rule_type_id));
    const flattened = flattenDeep(Object.values(ruleTypesMapped));
    const result = uniq(flattened);
    return result;
  };

  return {
    getTeams,
    getTeamOwners,
    createTeam,
    updateTeam,
    getTeam,
    setTeamStatusActive,
    deleteTeam,
  };
};

module.exports = teamsService;
