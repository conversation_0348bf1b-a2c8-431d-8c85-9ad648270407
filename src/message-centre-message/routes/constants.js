const MESSAGE_RESPONSE = {
  P: 'P',
  N: 'N',
  D: 'D',
  E: 'E',
  F: 'F',
  Y: 'Y',
  C: 'C',
  V: 'V',
  A: 'A',
};

const CAMPAIGN_TYPES = {
  ABM: 'ABM',
  SOL: 'SOL',
  MOB: 'MOB',
  FFT: 'FFT',
  DEF: '000',
};

const CAMPAIGN_CHANNELS = {
  ABM: 'ABM',
  SOL: 'SOL',
  MOB: 'MOB',
  BRN: 'BRN',
  SBD: 'SBD',
};

const CAMPAIGN_LANGUAGES = {
  E: 'E',
  F: 'F',
  ENGLISH: 'English',
  FRENCH: 'French',
};

const CAMPAIGN_DISPLAY_PARMS = {
  NAME: 'NAME',
  ACCT: 'ACCT',
  LIMIT: 'LIMIT',
  PRODUCT: 'PRODUCT',
  OTHER: 'OTHER',
};

const MESSAGE_STATUS = {
  N: 'N',
  S: 'S',
  D: 'D',
};

const MESSAGE_PRIORITY = {
  U: 'U', // Urgent
  N: 'N', // Normal
  D: 'D', // Urgent, intercept not dismissible
};

const MESSAGE_SEARCH_FIELDS = {
  cssNumber: 'cssNumber', // ScotiaCard Number
  cidNumber: 'cidNumber', // Customer ID
  messageId: 'messageId', // Message ID
};

module.exports = {
  MESSAGE_RESPONSE,
  CAMPAIGN_TYPES,
  CAMPAIGN_CHANNELS,
  CAMPAIGN_LANGUAGES,
  CAMPAIGN_DISPLAY_PARMS,
  MESSAGE_STATUS,
  MESSAGE_PRIORITY,
  MESSAGE_SEARCH_FIELDS,
};
