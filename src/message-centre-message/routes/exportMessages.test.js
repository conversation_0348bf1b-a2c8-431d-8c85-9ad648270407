const request = require('supertest');
const express = require('express');
const exportMessageMessages = require('./exportMessages');

// Mock the campaignManagementService with a getAllCampaigns function
const mockCampaignManagementService = {
  getAllCampaigns: jest.fn().mockResolvedValue({
    items: [
      {
        campaign_type: 'Email',
        kt_campaign_id: '123',
        campaign_channel: 'Online',
        language: 'EN',
        msg_status: 'Active',
        msg_priority: 'High',
        start_date: '2024-01-01',
        msg_gen_date: '2024-01-02',
        name: 'New Year Campaign',
      },
    ],
  }),
  getAllMessages: jest.fn().mockResolvedValue({
    items: [
      {
        campaign_type: 'Email',
        campaign_id: '123',
        channel: 'Online',
        language: 'EN',
        msg_status: 'Active',
        msg_priority: 'High',
        start_date: '2024-01-01',
        updated_date: '2024-01-02',
        subject_line: 'New Year Campaign',
      },
    ],
  }),
};

// Setup the Express server and route for testing
const app = express();
app.use(express.json());
app.use('/export-messages', exportMessageMessages(mockCampaignManagementService));

describe('exportMessages', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset to default successful response
    mockCampaignManagementService.getAllMessages.mockResolvedValue({
      items: [
        {
          campaign_type: 'Email',
          campaign_id: '123',
          channel: 'Online',
          language: 'EN',
          msg_status: 'Active',
          msg_priority: 'High',
          start_date: '2024-01-01',
          updated_date: '2024-01-02',
          subject_line: 'New Year Campaign',
        },
      ],
    });
  });

  it('should download a CSV file with the correct content', async() => {
    const response = await request(app)
      .get('/export-messages')
      .expect('Content-Type', /text\/csv/)
      .expect('Content-Disposition', /attachment; filename="messages-list-export.csv"/)
      .expect(200);

    // Check if the CSV string matches expected output
    expect(response.text).toContain('Type,ID,Channel,Language,Status,Priority,Start Date,Last Updated Date,Subject');
    expect(response.text).toContain('Email,123,Online,EN,Active,High,2024-01-01,2024-01-02,New Year Campaign');
  });

  it('should handle multiple messages in CSV export', async() => {
    mockCampaignManagementService.getAllMessages.mockResolvedValueOnce({
      items: [
        {
          campaign_type: 'Email',
          campaign_id: '123',
          channel: 'Online',
          language: 'EN',
          msg_status: 'Active',
          msg_priority: 'High',
          start_date: '2024-01-01',
          updated_date: '2024-01-02',
          subject_line: 'New Year Campaign',
        },
        {
          campaign_type: 'SMS',
          campaign_id: '456',
          channel: 'Mobile',
          language: 'FR',
          msg_status: 'Draft',
          msg_priority: 'Low',
          start_date: '2024-02-01',
          updated_date: '2024-02-02',
          subject_line: 'Valentine Special',
        },
      ],
    });

    const response = await request(app)
      .get('/export-messages')
      .expect(200);

    expect(response.text).toContain('Email,123,Online,EN,Active,High,2024-01-01,2024-01-02,New Year Campaign');
    expect(response.text).toContain('SMS,456,Mobile,FR,Draft,Low,2024-02-01,2024-02-02,Valentine Special');
  });

  it('should handle empty messages list', async() => {
    mockCampaignManagementService.getAllMessages.mockResolvedValueOnce({
      items: [],
    });

    const response = await request(app)
      .get('/export-messages')
      .expect(200);

    // Should only contain headers
    expect(response.text).toBe('Type,ID,Channel,Language,Status,Priority,Start Date,Last Updated Date,Subject\n');
  });

  it('should pass query parameters to service with offset and limit reset', async() => {
    const app2 = express();
    app2.use(express.json());
    app2.use('/export-messages', (req, res, next) => {
      res.locals.validatedQuery = {
        someFilter: 'value',
        offset: 100,
        limit: 50,
      };
      next();
    }, exportMessageMessages(mockCampaignManagementService));

    await request(app2)
      .get('/export-messages')
      .expect(200);

    expect(mockCampaignManagementService.getAllMessages).toHaveBeenCalledWith({
      someFilter: 'value',
      offset: 0,
      limit: 0,
    });
  });

  it('should handle errors correctly', async() => {
    // Mock the service to throw an error
    mockCampaignManagementService.getAllMessages.mockRejectedValueOnce(new Error('Service error'));

    const response = await request(app)
      .get('/export-messages')
      .expect(500);

    // Check if the error message is correct
    expect(response.body).toEqual(expect.anything()); // Update this based on how you handle errors
  });

  it('should handle service errors with custom message', async() => {
    const errorMessage = 'Database connection failed';
    mockCampaignManagementService.getAllMessages.mockRejectedValueOnce(new Error(errorMessage));

    await request(app)
      .get('/export-messages')
      .expect(500);

    expect(mockCampaignManagementService.getAllMessages).toHaveBeenCalled();
  });
});
