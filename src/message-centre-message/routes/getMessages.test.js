const getAll = require('./getMessages');
const { CustomError } = require('../../error');

const mockCampaignManagementService = {
  getAllMessages: jest.fn().mockReturnValue({
    data: {
      items: [],
    },
  }),
};

const jsonResp = {
  json: jest.fn(),
};

const resMock = {
  status: jest.fn().mockReturnValue(jsonResp),
  locals: {
    validatedQuery: {},
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
      access: {
        campaigns: {
          containers: {
            nova: {
              view: [ 'offers-and-programs' ],
            },
          },
          pages: {
            nova: {
              view: [ 'accounts' ],
            },
          },
          ruleSubTypes: {
            nova: {
              view: [ 'targeted' ],
            },
          },
        },
      },
    },
  },
};
const reqMock = {
  query: {
    validatedQuery: {},
  },
};
const nextMock = jest.fn();

describe('GET /v1/message-centre/campaigns', () => {
  beforeEach(() => {
    mockCampaignManagementService.getAllMessages.mockClear();
    resMock.status.mockClear();
  });

  test('should return 200 with valid req', async() => {
    const asyncGetAll = getAll(mockCampaignManagementService);
    await asyncGetAll(reqMock, resMock, nextMock);

    expect(mockCampaignManagementService.getAllMessages).toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalledTimes(0);
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('should handle unknown error', async() => {
    const mockCampaignManagementService = {
      getAllMessages: jest.fn().mockRejectedValue(new Error('unknown')),
    };
    const asyncGetAll = getAll(mockCampaignManagementService);

    try {
      await asyncGetAll(reqMock, resMock, nextMock);
    } catch (err) {
      expect(err.message).toBe('unknown');
      expect(nextMock).toHaveBeenCalledTimes(1);
      expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
    }
  });

  test('should handle 401 error', async() => {
    const mockCampaignManagementService = {
      getAllMessages: jest.fn().mockRejectedValue({ response: { status: 401, data: {} } }),
    };
    const asyncGetAll = getAll(mockCampaignManagementService);

    try {
      await asyncGetAll(reqMock, resMock, nextMock);
    } catch (err) {
      expect(nextMock).toHaveBeenCalledTimes(0);
      expect(resMock.status).toHaveBeenCalledWith(401);
    }
  });

  test('should handle 500 error', async() => {
    const mockCampaignManagementService = {
      getAllMessages: jest.fn().mockRejectedValue({ response: { status: 500, data: {} } }),
    };
    const asyncGetAll = getAll(mockCampaignManagementService);

    try {
      await asyncGetAll(reqMock, resMock, nextMock);
    } catch (err) {
      expect(nextMock).toHaveBeenCalledTimes(1);
      expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
    }
  });
});
