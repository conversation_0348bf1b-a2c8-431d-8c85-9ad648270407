const mockMessageDetail = require('../../__mocks__/mockMessageDetail');
const { CustomError } = require('../../error');
const GetMessageDetail = require('./getMessageDetail');

const mockCampaignManagementService = {
  getMessageDetail: jest.fn(),
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();

mockStatus.mockReturnValue({ json: mockJson });

describe('Campaign Rules: routes > getCampaign', () => {
  beforeEach(() => {
    mockCampaignManagementService.getMessageDetail.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });

  test('Should return valid response - get campaign', async() => {
    const mockReq = {
      params: {
        messageDetailId: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: {
          messageDetailId: 1,
        },
      },
    };
    mockCampaignManagementService.getMessageDetail.mockReturnValueOnce(mockMessageDetail);

    const getCampaign = GetMessageDetail(mockLogger, mockCampaignManagementService);
    await getCampaign(mockReq, mockRes, mockNext);
    expect(mockCampaignManagementService.getMessageDetail).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('should handle error less than 500 response status', async() => {
    const mockReq = {
      params: {
        messageDetailId: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: 1,
      },
    };
    const fakeResponse = {
      response: {
        status: 401,
        data: {},
      },
    };
    mockCampaignManagementService.getMessageDetail.mockRejectedValue(fakeResponse);
    const getCampaign = GetMessageDetail(mockLogger, mockCampaignManagementService);
    await getCampaign(mockReq, mockRes, mockNext);
    expect(mockCampaignManagementService.getMessageDetail).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(0);
    expect(mockRes.status).toHaveBeenCalledWith(401);
  });

  test('should call next with Error on 500 response status', async() => {
    const mockReq = {
      params: {
        messageDetailId: 1,
      },
    };
    const mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: {
          messageDetailId: 1,
        },
      },
    };
    const fakeResponse = {
      response: {
        status: 500,
        data: {
          code: 'HTTP_INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
        },
      },
    };
    mockCampaignManagementService.getMessageDetail.mockRejectedValue(fakeResponse);
    const getCampaign = GetMessageDetail(mockLogger, mockCampaignManagementService);
    await getCampaign(mockReq, mockRes, mockNext);
    expect(mockCampaignManagementService.getMessageDetail).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });

  test('should handle unknown error', async() => {
    const mockReq = {
      params: {
        messageDetailId: 1,
      },
    };
    const res = {
      status: mockStatus,
      locals: {
        validatedParams: {
          messageDetailId: 1,
        },
      },
    };
    mockCampaignManagementService.getMessageDetail.mockRejectedValue(new Error('unknown'));
    const updateCampaign = GetMessageDetail(mockLogger, mockCampaignManagementService);
    await updateCampaign(mockReq, res, mockNext);
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });
});
