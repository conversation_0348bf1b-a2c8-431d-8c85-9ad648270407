const mockUpdateMessageDetail = require('../../__mocks__/mockUpdateMessageDetail');
const { CustomError } = require('../../error');
const UpdateMessageDetail = require('./updateMessageDetail');

const mockCampaignManagementService = {
  updateMessageDetail: jest.fn(),
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();
const mockRes = {
  status: mockStatus,
  locals: {
    auth: { sid: 's1234567' },
    validatedBody: {},
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
      sid: 's1234567',
    },
  },
};

mockStatus.mockReturnValue({ json: mockJson });

describe('Message Detail: routes > updateMessageDetail', () => {
  beforeEach(() => {
    mockCampaignManagementService.updateMessageDetail.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });

  test('Should return valid response - update campaign', async() => {
    const mockBody = {
      ...mockUpdateMessageDetail,
    };
    const mockReq = {
      body: mockBody,
      locals: {
        user: {
          sid: 's1234567',
        },
      },
    };

    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: { ...mockBody, msg_priority: 'X' },
        validatedParams: {
          messageDetailId: 1,
        },
      },
    };

    const fakeResponse = {
      status: 200,
      data: {
        ...mockUpdateMessageDetail,
        msg_priority: 'X',
      },
    };

    mockCampaignManagementService.updateMessageDetail.mockReturnValueOnce(fakeResponse);
    const updateMessageDetail = UpdateMessageDetail(mockLogger, mockCampaignManagementService);
    await updateMessageDetail(mockReq, res, mockNext);
    expect(mockCampaignManagementService.updateMessageDetail).toBeCalled();
  });

  it('should handle error on less than 500 response status', async() => {
    const mockReq = {
      params: {
        messageDetailId: 1,
      },
      body: mockUpdateMessageDetail,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockUpdateMessageDetail,
        validatedParams: {
          messageDetailId: 1,
        },
      },
    };
    const fakeResponse = {
      response: {
        status: 401,
        data: {},
      },
    };
    mockCampaignManagementService.updateMessageDetail.mockRejectedValue(fakeResponse);
    const updateMessageDetail = UpdateMessageDetail(mockLogger, mockCampaignManagementService);
    await updateMessageDetail(mockReq, res, mockNext);
    expect(mockNext).toHaveBeenCalledTimes(0);
    expect(res.status).toHaveBeenCalledWith(401);
  });

  it('should call next with Error on 500+ response status', async() => {
    const mockReq = {
      params: {
        id: 1,
      },
      body: mockUpdateMessageDetail,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockUpdateMessageDetail,
        validatedParams: {
          messageDetailId: 1,
        },
      },
    };
    const fakeResponse = {
      response: {
        status: 500,
        data: {
          code: 'HTTP_INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
        },
      },
    };

    mockCampaignManagementService.updateMessageDetail.mockRejectedValue(fakeResponse);
    const updateMessageDetail = UpdateMessageDetail(mockLogger, mockCampaignManagementService);
    await updateMessageDetail(mockReq, res, mockNext);
    expect(mockCampaignManagementService.updateMessageDetail).toBeCalled();
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });

  test('should handle unknown error', async() => {
    const mockReq = {
      params: {
        id: 1,
      },
      body: mockUpdateMessageDetail,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockUpdateMessageDetail,
        validatedParams: {
          messageDetailId: 1,
        },
      },
    };
    mockCampaignManagementService.updateMessageDetail.mockRejectedValue(new Error('unknown'));
    const updateMessageDetail = UpdateMessageDetail(mockLogger, mockCampaignManagementService);
    await updateMessageDetail(mockReq, res, mockNext);
    expect(mockNext).toHaveBeenCalledTimes(1);
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });
});
