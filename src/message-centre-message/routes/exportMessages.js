const { CustomError } = require('../../error');

const SHEET_HEADERS = [
  'Type',
  'ID',
  'Channel',
  'Language',
  'Status',
  'Priority',
  'Start Date',
  'Last Updated Date',
  'Subject',
];

const exportMessages = (campaignManagementService) => {
  return async(req, res, next) => {
    try {
      const query = res.locals.validatedQuery;
      const { items } = await campaignManagementService.getAllMessages({
        ...query,
        offset: 0,
        limit: 0,
      });
      const csvHeadings = `${SHEET_HEADERS.toString()}\n`;

      const csvString = items.reduce((acc, campaign) => {
        acc += `${[ campaign.campaign_type,
          campaign.campaign_id,
          campaign.channel,
          campaign.language,
          campaign.msg_status,
          campaign.msg_priority,
          campaign.start_date,
          campaign.updated_date,
          campaign.subject_line,
        ].toString()}\n`;
        return acc;
      }, csvHeadings);

      res.set('Content-Type', 'text/csv');
      res.attachment(`messages-list-export.csv`);
      res.send(csvString);
    } catch (err) {
      next(new CustomError(500, err.toString()));
    }
  };
};

module.exports = exportMessages;
