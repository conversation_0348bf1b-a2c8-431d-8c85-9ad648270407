const mockUpdateMessageDetail = require('../../__mocks__/mockUpdateMessageDetail');
const { CustomError } = require('../../error');
const {
  schemaValidationMiddleware,
  VALIDATION_TYPE,
  updateSchema,
  DEFAULT_OPTS,
  getPathSchema,
  optionalGetPathSchema,
} = require('./validation');

describe('Campaign routes validation', () => {
  const req = {};
  const res = {};
  const nextMock = jest.fn();

  beforeEach(() => {
    nextMock.mockClear();
  });

  it('schemaValidationMiddleware - should throw error if invalid param type passed', () => {
    schemaValidationMiddleware(getPathSchema, 'invalid-type')(req, res, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
    expect(nextMock.mock.calls[0][0].message).toBe(`Invalid joi validation type, must be: body, params, or query`);
  });

  it('schemaValidationMiddleware - should throw error if fails validation schema', () => {
    const localReq = {
      body: { msg_priority: '' },
    };
    schemaValidationMiddleware(updateSchema, 'body')(localReq, res, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(2);
    expect(nextMock.mock.calls[0][0].message).toBe(`ValidationError: "msg_priority" is not allowed to be empty.`);
  });

  it('schemaValidationMiddleware - should pass validation - optional params', () => {
    const localReq = {
      params: { messageDetailId: '12345' },
    };
    const localRes = {
      locals: {},
    };
    schemaValidationMiddleware(optionalGetPathSchema, 'params')(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedParams).toEqual({ messageDetailId: '12345' });
  });

  it('schemaValidationMiddleware - should throw error if invalid param type passed', () => {
    schemaValidationMiddleware(getPathSchema, 'invalid-type')(req, res, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
    expect(nextMock.mock.calls[0][0].message).toBe(`Invalid joi validation type, must be: body, params, or query`);
  });

  it('schemaValidationMiddleware - should pass validation - params', () => {
    const localReq = {
      params: { messageDetailId: '12345' },
    };
    const localRes = {
      locals: {},
    };
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS)(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedParams).toEqual({ messageDetailId: '12345' });
  });

  it('schemaValidationMiddleware - should pass validation - body', () => {
    const localReq = {
      body: mockUpdateMessageDetail,
    };
    const localRes = {
      locals: {
        validatedBody: { ...localReq.body },
      },
    };
    schemaValidationMiddleware(updateSchema, undefined, DEFAULT_OPTS)(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedBody).toEqual(localReq.body);
  });
});
