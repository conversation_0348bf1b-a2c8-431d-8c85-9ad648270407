const Joi = require('joi');
const { CustomError, ERROR_HANDLER } = require('../../error');
const {
  CAMPAIGN_TYPES,
  CAMPAIGN_CHANNELS,
  CAMPAIGN_LANGUAGES,
  MESSAGE_RESPONSE,
  MESSAGE_STATUS,
  MESSAGE_PRIORITY,
  MESSAGE_SEARCH_FIELDS,
} = require('./constants');

const DEFAULT_OPTS = { stripUnknown: true };

const VALIDATION_TYPE = {
  BODY: 'body',
  PARAMS: 'params',
  QUERY: 'query',
};

const messageDetailId = Joi.string().alphanum();

const language = Joi.string().valid('E', 'F').required();
const channel = Joi.string().max(4).required();

const schemaValidationMiddleware = (schema, type = VALIDATION_TYPE.BODY, opts = DEFAULT_OPTS) => (req, res, next) => {
  if (!Object.values(VALIDATION_TYPE).includes(type)) {
    return next(new CustomError(500, 'Invalid joi validation type, must be: body, params, or query'));
  }
  const { error, value } = schema.validate(req[type], opts);
  if (error) {
    return next(ERROR_HANDLER(`Joi validation error on req.${type}`, error, next));
  }

  switch (type) {
    case VALIDATION_TYPE.BODY:
      res.locals.validatedBody = value;
      break;
    case VALIDATION_TYPE.PARAMS:
      res.locals.validatedParams = value;
      break;
    case VALIDATION_TYPE.QUERY:
      res.locals.validatedQuery = value;
      break;
    /* istanbul ignore next */
    default:
      break;
  }
  next();
};

// get campaign by its id
const getPathSchema = Joi.object()
  .keys({ messageDetailId })
  .fork([ 'messageDetailId' ], (schema) => schema.required());

const optionalGetPathSchema = Joi.object()
  .keys({ messageDetailId });

const messageDetailSchema = {
  'msg_priority': Joi.string().length(1),
  'msg_status': Joi.string().length(1),
  'msg_response': Joi.string().valid(...Object.values(MESSAGE_RESPONSE), null),
  'start_date': Joi.date().iso().raw(true),
  'end_date': Joi.date().iso().raw(true),
  'updated_date': Joi.date().iso().raw(true),
  'channel_display_sol': Joi.boolean().optional(),
  'channel_display_abm': Joi.boolean().optional(),
  'channel_display_csr': Joi.boolean().optional(),
  'message_display_spot': Joi.boolean().optional(),
  'message_display_pal': Joi.boolean().optional(),
  'message_display_mob': Joi.boolean().optional(),
  language,
  channel,
};

const updateSchema = Joi.object()
  .keys({ ...messageDetailSchema })
  .fork([ 'msg_priority', 'msg_status', 'msg_response', 'start_date', 'end_date' ], (schema) => schema.required());
const exportMessageCentreMessagesSchema = Joi.object()
  .keys({
    type: Joi.string().min(1).max(100).allow(...Object.values(CAMPAIGN_TYPES), null, ''),
    channel: Joi.string().min(1).max(100).allow(...Object.values(CAMPAIGN_CHANNELS), null, ''),
    language: Joi.string().allow(...Object.values(CAMPAIGN_LANGUAGES), null, ''),
    status: Joi.string().allow(...Object.values(MESSAGE_STATUS), null, ''),
    priority: Joi.string().allow(...Object.values(MESSAGE_PRIORITY), null, ''),
    start_date: Joi.date().iso().raw(true),
    end_date: Joi.date().iso().raw(true),
    updated_date_gt: Joi.date().iso().raw(true),
    updated_date_lt: Joi.date().iso().raw(true),
  });
// get list of messages
const getMessageCentreMessagesSchema = exportMessageCentreMessagesSchema.keys({
  searchQuery: Joi.string().min(1).max(100).allow(null, ''),
  searchField: Joi.string().valid(...Object.values(MESSAGE_SEARCH_FIELDS)),
  offset: Joi.number().min(0).default(0),
  limit: Joi.number().min(1).default(50),
});

const getMessageCentreMessageSchema = Joi.object().keys({
  language,
  channel,
});

module.exports = {
  DEFAULT_OPTS,
  VALIDATION_TYPE,
  schemaValidationMiddleware,
  updateSchema,
  optionalGetPathSchema,
  getPathSchema,
  getMessageCentreMessagesSchema,
  getMessageCentreMessageSchema,
  exportMessageCentreMessagesSchema,
};
