const messageIndex = require('.');
const config = require('../../constants/config');

describe('/messages route', () => {
  const campaignServiceMock = {
    getAllMessages: jest.fn(),
    getMessageDetail: jest.fn(),
    updateMessageDetail: jest.fn(),
  };
  test('should return valid routes', () => {
    const logger = { error: jest.fn() };
    const router = messageIndex(config, logger, {}, {}, campaignServiceMock);
    const routes = [
      '/messages',
      '/messages/export',
      '/messages/:messageDetailId',
    ];
    routes.forEach((route, index) => {
      expect(router.stack[index].route.path).toEqual(route);
    });
  });
});
