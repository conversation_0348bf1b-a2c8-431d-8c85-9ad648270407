const { CustomError } = require('../../error');

const getMessageDetail = (logger, campaignManagementService) => {
  return async(req, res, next) => {
    const params = res.locals.validatedParams;
    const query = res.locals.validatedQuery;
    try {
      const data = await campaignManagementService.getMessageDetail(params.messageDetailId, query);
      return res.status(200).json(data);
    } catch (error) {
      if (error.response) {
        if (error.response.status < 500) {
          res.status(error.response.status).json(error.response.data);
        } else {
          next(new CustomError(error.response.status, JSON.stringify(error.response.data)));
        }
      } else {
        next(new CustomError(500, error.toString()));
      }
    }
  };
};

module.exports = getMessageDetail;
