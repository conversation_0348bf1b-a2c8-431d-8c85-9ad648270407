const express = require('express');
const { wrapAsync } = require('../../utils');

const getMessageDetail = require('./getMessageDetail');
const updateMessageDetail = require('./updateMessageDetail');
const getMessages = require('./getMessages');
const exportMessages = require('./exportMessages');

const {
  VALIDATION_TYPE,
  schemaValidationMiddleware,
  updateSchema,
  optionalGetPathSchema,
  getMessageCentreMessagesSchema,
  getMessageCentreMessageSchema,
  exportMessageCentreMessagesSchema,
} = require('./validation');

const init = (config, logger, campaignManagementService) => {
  const router = express.Router();

  router.get('/messages',
    schemaValidationMiddleware(getMessageCentreMessagesSchema, VALIDATION_TYPE.QUERY),
    wrapAsync(getMessages(campaignManagementService)),
  );

  router.get(
    '/messages/export',
    schemaValidationMiddleware(exportMessageCentreMessagesSchema, VALIDATION_TYPE.QUERY),
    wrapAsync(exportMessages(campaignManagementService)),
  );

  router.get('/messages/:messageDetailId',
    schemaValidationMiddleware(optionalGetPathSchema, VALIDATION_TYPE.PARAMS),
    schemaValidationMiddleware(getMessageCentreMessageSchema, VALIDATION_TYPE.QUERY),
    wrapAsync(getMessageDetail(logger, campaignManagementService)),
  );

  router.put('/messages/:messageDetailId',
    schemaValidationMiddleware(optionalGetPathSchema, VALIDATION_TYPE.PARAMS),
    schemaValidationMiddleware(updateSchema, VALIDATION_TYPE.BODY),
    wrapAsync(updateMessageDetail(logger, campaignManagementService)),
  );

  return router;
};

module.exports = init;
