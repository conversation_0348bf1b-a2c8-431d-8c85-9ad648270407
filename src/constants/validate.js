const Joi = require('joi');

// reusable field level validators
const datetime = Joi.date().iso().raw();
const applicationId = Joi.number().min(1); // pigeon internal system oriented numerical id
const applicationIdString = Joi.string().min(1).max(20).regex(/^[a-z0-9_-]+$/); // client system oriented string id

const VALIDATE = (input, schema) => {
  const { error } = schema.validate(input, { stripUnknown: true });
  if (error) {
    throw error;
  }
};

const SCHEMA = {
  ID: Joi.object()
    .keys({
      id: Joi.number().min(1),
    })
    .fork([ 'id' ], (schema) => schema.required()),
  CONTAINER_CREATE: Joi.object()
    .keys({
      name: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/),
      containerId: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/),
      application: applicationId,
      rule_type: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/),
      pages: Joi.array().items(Joi.number()),
      status: Joi.boolean(),
      description: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/).allow('').optional(),
      campaign_limit:  Joi.alternatives().try(Joi.number().integer(), Joi.valid(null)),
      content_type: Joi.string().optional(),
    }),
  CONTAINER_UPDATE: Joi.object()
    .keys({
      name: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/).optional(),
      containerId: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/),
      application: applicationId.optional(),
      rule_type: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/).optional(),
      pages: Joi.array().items(Joi.number()).optional(),
      status: Joi.boolean().optional(),
      description: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/).allow('').optional(),
      campaign_limit:  Joi.alternatives().try(Joi.number().integer(), Joi.valid(null)),
      content_type: Joi.string().optional(),
    }),
  PERMISSION_CREATE: Joi.object()
    .keys({
      description: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/),
      name: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/),
    }),
  PERMISSION_UPDATE: Joi.object()
    .keys({
      description: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/).optional(),
      name: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/).optional(),
    }),
  ROLE_CREATE: Joi.object()
    .keys({
      name: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/),
      permissions: Joi.array().items(Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/)),
    }),
  ROLE_UPDATE: Joi.object()
    .keys({
      name: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/).optional(),
      permissions: Joi.array().items(Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/)).optional(),
    }),
  USER_CREATE: Joi.object()
    .keys({
      name: Joi.string().min(1).max(200).regex(/^[a-zA-Z\d\s:_-]+$/),
      email: Joi.string().email({ minDomainSegments: 2 }),
      sid: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/),
      roles: Joi.array().items(Joi.number()),
      team_id: Joi.number(),
      active: Joi.boolean(),
    }),
  USER_UPDATE: Joi.object()
    .keys({
      name: Joi.string().min(1).max(200).optional().regex(/^[a-zA-Z\d\s:_-]+$/),
      email: Joi.string().email({ minDomainSegments: 2 }).optional(),
      sid: Joi.string().min(1).optional().regex(/^[a-zA-Z\d\s:_-]+$/),
      roles: Joi.array().items(Joi.number()),
      active: Joi.boolean().optional(),
      team_id: Joi.number(),
    }),
  PAGE_CREATE: Joi.object()
    .keys({
      applicationId,
      name: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/),
      pageId: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_\-.]+$/),
      status: Joi.boolean(),
      description: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/).allow('').optional(),
    }),
  PAGE_UPDATE: Joi.object()
    .keys({
      applicationId: applicationId.optional(),
      name: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/).optional(),
      pageId: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_\-.]+$/).optional(),
      status: Joi.boolean().optional(),
      description: Joi.string().min(1).regex(/^[a-zA-Z\d\s:_-]+$/).allow('').optional(),
    }),
  CONTENT: Joi.object()
    .keys({
      spaceId: Joi.string().min(1).max(20).regex(/^[a-zA-Z0-9_-]+$/),
      typeId: Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/),
      contentId: Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/),
    }),
  CONTENTS: Joi.object()
    .keys({
      language: Joi.string().min(1).max(20).regex(/^[a-zA-Z-]+$/),
      offset: Joi.number().min(0),
      limit: Joi.number().min(1),
    }),
  CONTENT_DETAILS: Joi.object()
    .keys({
      language: Joi.string().min(1).max(20).regex(/^[a-zA-Z-]+$/),
      select: Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/),
    }),
  CONTENT_VIGNETTE: Joi.object()
    .keys({
      name: Joi.string().min(1).max(200),
      type: Joi.string().default('webfragment'),
      sort: Joi.string().min(2).default('-updated_at').regex(/^[-]?[a-z_]+$/),
      updated_at_start: datetime,
      updated_at_end: datetime,
      offset: Joi.number().min(0),
      limit: Joi.number().min(1),
    }),
  CONTENT_DETAILS_VIGNETTE: Joi.object()
    .keys({
      content_id: Joi.number().min(1),
    }),
  CONTENT_VIGNETTE_STATIC: Joi.object()
    .keys({
      path: Joi.string().required(),
      repo: Joi.string().valid('dm-authenticated', 'dm-storefront'),
    }),
  APPLICATION_CREATE: Joi.object()
    .keys({
      applicationId: applicationIdString,
      description: Joi.string().allow('').optional(),
      name: Joi.string().required(),
      platformIds: Joi.array().items(Joi.number()).optional(),
      ruleTypeIds: Joi.array().items(Joi.number()).optional(),
      ruleSubTypeIds: Joi.array().items(Joi.number()).optional(),
      status: Joi.bool().required(),
      team_id: Joi.number(),
    }),
  VARIABLE_MAPPINGS_SET: Joi.object().keys({
    status: Joi.string().optional(),
  }),
  CREATE_AND_UPDATE_VARIABLE_MAPPING_SET_V2: Joi.object().keys({
    approver_sid: Joi.string().min(4).max(10).regex(/^s[0-9]+$/),
    description: Joi.string().regex(/[a-zA-Z0-9]*$/).max(350).optional(),
    variables: Joi.array().items(Joi.object().keys({
      variable_template: Joi.string().regex(/[a-z_]*$/).max(60),
      variable_campaign: Joi.string().regex(/[a-z_]*$/).max(50),
      variable_type: Joi.string().valid('text', 'currency', 'date', 'account-number-mask', 'number', 'account', 'gic-special-term', 'gic-special-rate', 'locale-rate'),
    }).fork([ 'variable_template', 'variable_campaign', 'variable_type' ], (schema) => schema.required())).min(1),
  }).fork([ 'variables' ], (schema) => schema.required()),
  PATCH_VARIABLE_MAPPING_SET_V2: Joi.object().keys({
    approver_sid: Joi.string().min(4).max(10).regex(/^s[0-9]+$/),
    description: Joi.string().regex(/[a-zA-Z0-9]*$/).max(350).optional(),
    status: Joi.string().valid('active', 'pending', 'draft', 'deleted'),
  }).fork([ 'status' ], (schema) => schema.required()),
  UPDATE_VARIABLE_MAPPING_SET: Joi.object().keys({
    description: Joi.string().regex(/[a-zA-Z0-9]*$/).max(350).optional(),
    status: Joi.string().valid('active', 'pending', 'draft', 'deleted'),
    variables: Joi.array().items(Joi.object().keys({
      variable_template: Joi.string().regex(/[a-z_]*$/).max(60),
      variable_campaign: Joi.string().regex(/[a-z_]*$/).max(50),
      variable_type: Joi.string().valid('text', 'currency', 'date', 'account-number-mask', 'number', 'account', 'gic-special-term', 'gic-special-rate', 'locale-rate'),
    }).fork([ 'variable_template', 'variable_campaign', 'variable_type' ], (schema) => schema.required())).min(1),
  }).fork([ 'variables', 'status' ], (schema) => schema.required()),
  UNIQUE_VALIDATION: Joi.object().keys({
    key: Joi.string().valid('userEmail', 'userSid', 'roleName', 'applicationName').required(),
    values: Joi.string().required(),
  }),
};

SCHEMA.USER_REQUIRE = SCHEMA.USER_CREATE.fork([ 'name', 'email', 'sid', 'roles', 'team_id' ], (schema) => schema.required());

module.exports = {
  VALIDATE,
  SCHEMA,
};
