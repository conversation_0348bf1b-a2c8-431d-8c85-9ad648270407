let config;

describe('config object', () => {
  beforeEach(() => {
    process.env.FAILOVER_GROUP = 'pigeon-failover';
  });

  afterEach(() => {
    process.env.VCAP_SERVICES = null;
  });

  test('should return a valid config object when "azure-sqldb-failover-group" env var is present', async() => {
    const mockVcapServices = `{
      "azure-sqldb-failover-group": [
        {
          "name": "${process.env.FAILOVER_GROUP}",
          "credentials": {
            "sqldbName": "mock_database_name",
            "username": "mock user name",
            "port": 8081,
            "sqlServerFullyQualifiedDomainName": "mock://mock_server"
          }
        }
      ],
      "azure-rediscache": [
        {
          "label": "azure-rediscache",
          "provider": null,
          "plan": "premiump1",
          "name": "pigeon-cache",
          "instance_guid": "f0746c04-1d9d-4aaf-a33d-16e48da22006",
          "instance_name": "pigeon-cache",
          "binding_guid": "fbe839ea-b897-4c09-893a-7b916cfbe67d",
          "binding_name": null,
          "credentials": {
            "name": "name",
            "hostname": "name.redis.cache.windows.net",
            "port": 6379,
            "sslPort": 6380,
            "primaryKey": "primaryKey",
            "secondaryKey": "secondaryKey",
            "redisUrl": "rediss://name:<EMAIL>:6380"
          }
        }
      ]
    }`;
    const mockDbResult = {
      database: 'mock_database_name',
      user: 'mock user name',
      port: 8081,
      server: 'mock://mock_server',
      timezone: 'utc',
      options: {
        encrypt: true,
        trustServerCertificate: true,
      },
    };
    process.env.VCAP_SERVICES = mockVcapServices;
    config = require('./config');
    expect(typeof config).toBe('object');
    expect(config.db).toEqual(mockDbResult);
  });

  test('should return a valid config object when "azure-sqldb" env var is present', () => {
    const mockVcapServices = `{
      "azure-sqldb": [
        {
          "name": "${process.env.FAILOVER_GROUP}",
          "credentials": {
            "sqldbName": "mock_database_name",
            "username": "mock user name",
            "port": 8081,
            "sqlServerFullyQualifiedDomainName": "mock://mock_server"
          }
        }
      ],
      "azure-rediscache": [
        {
          "label": "azure-rediscache",
          "provider": null,
          "plan": "premiump1",
          "name": "pigeon-cache",
          "instance_guid": "f0746c04-1d9d-4aaf-a33d-16e48da22006",
          "instance_name": "pigeon-cache",
          "binding_guid": "fbe839ea-b897-4c09-893a-7b916cfbe67d",
          "binding_name": null,
          "credentials": {
            "name": "name",
            "hostname": "name.redis.cache.windows.net",
            "port": 6379,
            "sslPort": 6380,
            "primaryKey": "primaryKey",
            "secondaryKey": "secondaryKey",
            "redisUrl": "rediss://name:<EMAIL>:6380"
          }
        }
      ]
    }`;
    const mockDbResult = {
      database: 'mock_database_name',
      user: 'mock user name',
      port: 8081,
      server: 'mock://mock_server',
      timezone: 'utc',
      options: {
        encrypt: true,
        trustServerCertificate: true,
      },
    };
    process.env.VCAP_SERVICES = mockVcapServices;
    config.init();
    expect(typeof config).toBe('object');
    expect(config.db).toEqual(mockDbResult);
  });

  test('should throw an error when envs var are not present', () => {
    const mockVcapServices = '{}';
    process.env.VCAP_SERVICES = mockVcapServices;
    expect(config.init).toThrow();
  });
});
