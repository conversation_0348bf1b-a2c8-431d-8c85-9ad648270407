const { SELECT, COLUMN, COL_NAME } = require('./db');

describe('Database constants', () => {
  test('COLUMN constant', () => {
    expect(COLUMN).toMatchSnapshot();
    expect(Object.keys(COL_NAME)).toEqual(Object.keys(COLUMN));
    expect(COLUMN.application.id).toEqual('ref_admin_application.admin_application_id');
  });

  test('SELECT constant', () => {
    expect(SELECT).toMatchSnapshot();
    expect(Object.keys(COL_NAME)).toEqual(Object.keys(SELECT));
    expect(SELECT.application.id).toEqual('ref_admin_application.admin_application_id AS id');
  });
});
