const helmet = require('helmet');
const R = require('ramda');
require('dotenv').config();
const path = require('path');

const getRedisConnectionConfig = require('../services/redis');
const productBookSeed = require('./productBook/seed.json');
const productBookOverride = require('./productBook/overrides.json');

const getEnvString = R.curry((env, defaults) => R.pathOr(defaults, [ env ], process.env));
const getEnvNumber = R.pipe(getEnvString, Number);
const toBool = (x) => !!Number(x);
const toJSON = (v, defs) => {
  try {
    return JSON.parse(v);
  } catch (err) {
    return defs;
  }
};

const init = () => {
  const s2sPublicKey = process.env.AUTH_S2S_PUBLIC_KEY;
  if (!s2sPublicKey) {
    throw new Error('service to service authentication public key not found');
  }

  const defaultDirectives = helmet.contentSecurityPolicy.getDefaultDirectives();
  // delete default policies from helmet 4 that are not yet supported in all modern browsers
  delete defaultDirectives['script-src-attr'];
  const defaultCsp = [ "'self'", 'https://*.scotiabank.com' ];

  let finalConfig = {
    init: init,
    clsNamespace: 'pigeon_admin_cls_namespace',
    epm: process.env.EPM || 'BFB6',
    nodeEnv: process.env.NODE_ENV || 'production',
    server: {
      port: process.env.SERVER_PORT || 8090,
    },
    redirectUrl: process.env.ADMIN_REDIRECT_URL,
    services: {
      serviceAuth: {
        publicKey: s2sPublicKey,
      },
      ruleApi: {
        url: process.env.RULE_API_URL,
        timeout: 60000,
      },
      contentApi: {
        url: process.env.CONTENT_API_URL,
        timeout: 60000,
      },
      marvelProductApi: {
        url: process.env.MARVEL_PRODUCT_API_URL,
        atlasUrl: process.env.MARVEL_PRODUCT_API_ATLAS_URL,
        timeout: 60000,
      },
      passportAPI: {
        uri: process.env.PASSPORT_API_URI,
        ttl: Number(process.env.PASSPORT_API_S2S_TTL) || 60,
        scope: process.env.PASSPORT_API_S2S_SCOPE,
        privateKey: process.env.CDP_SECRET_S2S_PRIVATE_KEY ? Buffer.from(process.env.CDP_SECRET_S2S_PRIVATE_KEY, 'base64').toString() : '',
        algorithm: process.env.PASSPORT_API_S2S_ALGORITHM,
        clientId: process.env.PASSPORT_API_S2S_CLIENTID,
        expiresIn: process.env.AUTH_S2S_CLAIM_EXPIRESIN,
        notBefore: process.env.AUTH_S2S_CLAIM_NOTBEFORE,
      },
      campaignManagementApi: {
        url: process.env.CAMPAIGN_MANAGEMENT_API_URL,
        timeout: 60000,
      },
      offersApi: {
        url: process.env.OFFERS_MANAGEMENT_API_URL,
        timeout: 60000,
      },
    },
    contentful: {
      entryUrl: process.env.CONTENTFUL_ENTRY_URL,
    },
    contentSecurityPolicy: {
      useDefaults: false,
      directives: {
        ...defaultDirectives,
        'img-src': [ ...defaultCsp, ...(toJSON(process.env['CSP_IMAGE_SRC']) || []) ],
        'frame-src': [ ...defaultCsp, ...(toJSON(process.env['CSP_FRAME_SRC']) || []) ],
        'script-src': [ ...defaultCsp, "'unsafe-eval'", "'unsafe-inline'", ...(toJSON(process.env['CSP_SCRIPT_SRC']) || []) ],
        'connect-src': [ ...defaultCsp, ...(toJSON(process.env['CSP_CONNECT_SRC']) || []) ],
        // reportUri: [ '/report' ], // for local debugging
      },
    },
    bitbucket: {
      staticUrl: process.env.BITBUCKET_STATIC_URL,
    },
    db: {
      database: process.env.KNEX_DB_NAME,
      user: process.env.KNEX_DB_USER,
      password: process.env.KNEX_DB_PWD,
      port: parseInt(process.env.KNEX_DB_PORT),
      server: process.env.KNEX_DB_SERVER,
      timezone: 'utc',
    },
    static: {
      path: process.env.STATIC_PATH || path.join(__dirname, '../..', 'front-end/build'),
      index: process.env.STATIC_INDEX || path.join(__dirname, '../..', 'front-end/build', 'index.html'),
      preview: process.env.STATIC_INDEX || path.join(__dirname, '../..', 'front-end/build', 'preview.html'),
    },
    logging: {
      name: process.env.LOG_NAME || 'admin',
      obfuscate: toBool(process.env.LOG_OBFUSCATE),
      colorize: toBool(process.env.LOG_COLORIZE),
      prettyPrint: toBool(process.env.LOG_PRETTY_PRINT),
      ignoreSiem: !toBool(process.env.LOG_SIEM),
      ignoredLogRoutes: toJSON(process.env.IGNORED_ROUTES_FOR_LOGGING, []),
    },
    launchDarkly: {
      secret: process.env.CDP_SECRET_LAUNCH_DARKLY_SDK_KEY
        ? Buffer.from(process.env.CDP_SECRET_LAUNCH_DARKLY_SDK_KEY, 'base64').toString()
        : undefined,
      userKey: process.env.LAUNCH_DARKLY_USER_ID,
    },
    productBook: {
      refreshIntervalMillis: 24 * 60 * 60 * 1000,
      seedData: productBookSeed,
      overrideData: productBookOverride,
    },
    rateLimiting: {
      window: 60000,
      client: getEnvNumber('RATE_LIMIT_CLIENT_MAX', 30),
      overall: getEnvNumber('RATE_LIMIT_CLIENT_MAX', 300),
      cdpTrustedIp: process.env.RATE_LIMIT_CDP_TRUSTED_IP,
    },
    timeout: 60000,
    wamSsoAuth: {
      clientID: process.env.WAM_CLIENT_ID,
      clientSecret: process.env.CDP_SECRET_WAM_CLIENT_SECRET, // private key
      redirectURI: process.env.WAM_REDIRECT_URL,
      authorizeURI: process.env.WAM_AUTHORIZE_URL,
      tokenURI: process.env.WAM_TOKEN_URL,
      tokenInfoURI: process.env.WAM_TOKEN_INFO_URL,
      jwksURI: process.env.WAM_JWKS_URL,
      validReturnDomain: process.env.VALID_RETURN_DOMAIN,
      logoutURL: encodeURIComponent(process.env.WAM_POST_LOGOUT_REDIRECT_URL),
      localSessionTTL: process.env.WAM_LOCAL_SESSION_TTL * 1000, // in milliseconds
      unsecureRoutes: [ '/health', '/.well-known/jwks.json' ],
    },
    redisConnectionConfig: getRedisConnectionConfig(),
  };

  try {
    if (process.env.VCAP_SERVICES) {
      const vcap = JSON.parse(process.env.VCAP_SERVICES);
      let azureSqlKey;

      if (Object.prototype.hasOwnProperty.call(vcap, 'azure-sqldb-failover-group')) {
        azureSqlKey = 'azure-sqldb-failover-group';
      } else if (Object.prototype.hasOwnProperty.call(vcap, 'azure-sqldb')) {
        azureSqlKey = 'azure-sqldb';
      } else {
        throw new Error('AzureSQL credentials not found');
      }

      const filteredConfig = vcap[azureSqlKey].filter(item => item.name === process.env.FAILOVER_GROUP);
      const creds = filteredConfig[0].credentials;

      const vcapDB = {
        database: creds.sqldbName,
        user: creds.username,
        password: creds.password,
        port: Number(creds.port),
        server: creds.sqlServerFullyQualifiedDomainName,
        timezone: 'utc',
        options: {
          encrypt: true,
          trustServerCertificate: true,
        },
      };

      finalConfig.db = vcapDB;
      finalConfig.space = vcap.space_name;
    }

    return finalConfig;
  } catch (err) {
    throw new Error(`invalid VCAP_SERVICES: ${err.toString()}`);
  }
};

module.exports = init();
