const TABLE = {
  application: 'ref_admin_application',
  container: 'ref_admin_container',
  containers_pages: 'admin_containers_pages',
  page: 'ref_admin_page',
  permission: 'ref_admin_permission',
  role: 'ref_admin_role',
  roles_permissions: 'admin_roles_permissions',
  rule_type: 'ref_admin_rule_type',
  rule_sub_type: 'ref_admin_rule_sub_type',
  user: 'admin_user',
  users_roles: 'admin_users_roles',
  user_campaign_rules: 'admin_user_campaign_rules',
  business_line: 'ref_business_line',
  device: 'ref_device',
  registration_status: 'ref_registration_status',
  province: 'ref_province',
  product: 'ref_product',
  product_sub_category: 'ref_product_sub_category',
  applications_platforms: 'admin_applications_platforms',
  platform: 'ref_admin_platform',
  applications_rule_types: 'admin_applications_rule_types',
  applications_rule_sub_types: 'admin_applications_rule_sub_types',
  team: 'ref_admin_team',
  teams_permissions: 'admin_teams_permissions',
  access_containers: 'admin_access_containers',
  access_pages: 'admin_access_pages',
  access_rule_sub_types: 'admin_access_rule_sub_types',
  access_levels: 'ref_admin_access',
};

const COL_NAME = {
  application: {
    id: 'admin_application_id',
    name: 'admin_application_name',
    description: 'admin_application_description',
    applicationId: 'admin_application_slug',
    status: 'admin_application_status',
    rule_version: 'admin_application_rule_version',
    contentful_space: 'admin_application_contentful_space',
    team_id: 'admin_team_id',
  },
  container: {
    id: 'admin_container_id',
    name: 'admin_container_name',
    containerId: 'admin_container_slug',
    application: 'admin_application_id',
    rule_type_id: 'admin_rule_type_id',
    status: 'admin_container_status',
    description: 'admin_container_description',
    campaign_limit: 'admin_container_limit',
    content_type: 'content_type',
  },
  containers_pages: {
    container_id: 'admin_container_id',
    page_id: 'admin_page_id',
  },
  page: {
    id: 'admin_page_id',
    application: 'admin_application_id',
    name: 'admin_page_name',
    pageId: 'admin_page_slug',
    status: 'admin_page_status',
    description: 'admin_page_description',
  },
  permission: { // No longer used
    id: 'admin_permission_id',
    name: 'admin_permission_name',
    description: 'admin_permission_description',
  },
  role: {
    id: 'admin_role_id',
    name: 'admin_role_name',
    status: 'active',
  },
  roles_permissions: {
    role_id: 'admin_role_id',
    team_id: 'admin_team_id',
    permission_id: 'admin_permission_id', // No longer used - hard coded to 0
    permission_name: 'admin_permission_name',
  },
  rule_type: {
    id: 'admin_rule_type_id',
    rule_type: 'admin_rule_type_name',
    slug: 'admin_rule_type_slug',
  },
  rule_sub_type: {
    id: 'admin_rule_sub_type_id',
    name: 'admin_rule_sub_type_name',
    description: 'admin_rule_sub_type_description',
  },
  user: {
    id: 'admin_user_id',
    name: 'full_name',
    email: 'email',
    sid: 'sid',
    token: 'token',
    active: 'active_indicator',
    token_expire_date: 'token_expire_date',
    token_expires: 'token_expire_indicator',
  },
  users_roles: {
    user_id: 'admin_user_id',
    role_id: 'admin_role_id',
  },
  user_campaign_rules: {
    id: 'admin_user_campaign_rules_id',
    admin_user_id: 'assigned_admin_user_id',
    campaign_rule_id: 'campaign_rule_id',
  },
  business_line: {
    id: 'business_line_id',
    description: 'business_line_description',
    code: 'business_line_type',
  },
  device: {
    id: 'device_id',
    description: 'device_description',
    code: 'device_type',
  },
  registration_status: {
    id: 'registration_status_id',
    description: 'registration_status_description',
    code: 'registration_status_type',
  },
  province: {
    id: 'province_id',
    description: 'province_description',
    code: 'province_code',
  },
  product: {
    id: 'product_id',
    sub_category_id: 'product_sub_category_id',
    description: 'product_description',
    code: 'product_type',
  },
  product_sub_category: {
    id: 'product_sub_category_id',
    sub_category_description: 'product_sub_category_description',
  },
  applications_platforms: {
    platform_id: 'admin_platform_id',
    application_id: 'admin_application_id',
  },
  platform: {
    id: 'admin_platform_id',
    slug: 'admin_platform_slug',
    name: 'admin_platform_name',
  },
  applications_rule_types: {
    rule_type_id: 'admin_rule_type_id',
    application_id: 'admin_application_id',
  },
  applications_rule_sub_types: {
    application_id: 'admin_application_id',
    rule_sub_type_id: 'admin_rule_sub_type_id',
  },
  team: {
    id: 'admin_team_id',
    name: 'admin_team_name',
    description: 'admin_team_description',
    active: 'admin_team_active',
  },
  teams_permissions: {
    team_id: 'admin_team_id',
    permission_id: 'admin_permission_id', // Not used - hard coded to 0
    permission_name: 'admin_permission_name',
  },
  access_containers: {
    team_id: 'admin_team_id',
    rule_type_id: 'admin_rule_type_id',
    application_id: 'admin_application_id',
    container_id: 'admin_container_id',
    access_id: 'admin_access_id',
    active: 'active', // Not used
  },
  access_pages: {
    team_id: 'admin_team_id',
    rule_type_id: 'admin_rule_type_id',
    application_id: 'admin_application_id',
    page_id: 'admin_page_id',
    access_id: 'admin_access_id',
    active: 'active', // Not used
  },
  access_rule_sub_types: {
    team_id: 'admin_team_id',
    rule_type_id: 'admin_rule_type_id',
    application_id: 'admin_application_id',
    rule_sub_type_id: 'admin_rule_sub_type_id',
    access_id: 'admin_access_id',
    active: 'active', // Not used
  },
  access_levels: {
    access_id: 'admin_access_id',
    access_name: 'admin_access_name',
    access_level: 'admin_access_level', // Not used
  },
};

const createQueryForColName = (aliasKey = false) => (
  Object.entries(COL_NAME).reduce((acc, [ key, val ]) => {
    const temp = Object.entries(val).reduce((a, [ k, v ]) => {
      a[k] = `${TABLE[key]}.${v}`;
      if (aliasKey) {
        a[k] = `${a[k]} AS ${k}`;
      }

      return a;
    }, {});

    acc[key] = temp;
    return acc;
  }, {})
);

const COLUMN = createQueryForColName();
const SELECT = createQueryForColName(true);

const JOIN = {
  container_containers_pages: [ TABLE.containers_pages, COLUMN.container.id, COLUMN.containers_pages.container_id ],
  page_pages_containers: [ TABLE.containers_pages, COLUMN.page.id, COLUMN.containers_pages.page_id ],
  page_application: [ TABLE.application, COLUMN.page.application, COLUMN.application.id ],
  container_rule_type: [ TABLE.rule_type, COLUMN.container.rule_type_id, COLUMN.rule_type.id ],
  container_application: [ TABLE.application, COLUMN.container.application, COLUMN.application.id ],
  user_users_roles: [ TABLE.users_roles, COLUMN.user.id, COLUMN.users_roles.user_id ],
  users_roles_user: [ TABLE.user, COLUMN.user.id, COLUMN.users_roles.user_id ],
  role_users_roles: [ TABLE.users_roles, COLUMN.role.id, COLUMN.users_roles.role_id ],
  users_roles_role: [ TABLE.role, COLUMN.users_roles.role_id, COLUMN.role.id ],
  users_roles_roles_permissions: [ TABLE.roles_permissions, COLUMN.users_roles.role_id, COLUMN.roles_permissions.role_id ],
  product_sub_product: [ TABLE.product_sub_category, COLUMN.product.sub_category_id, COLUMN.product_sub_category.id ],
  applications_platforms: [ TABLE.applications_platforms, COLUMN.application.id, COLUMN.applications_platforms.application_id ],
  applications_platforms_platform: [ TABLE.platform, COLUMN.platform.id, COLUMN.applications_platforms.platform_id ],
  applications_teams: [ TABLE.team, COLUMN.team.id, COLUMN.application.team_id ],
  applications_rule_types: [ TABLE.applications_rule_types, COLUMN.application.id, COLUMN.applications_rule_types.application_id ],
  applications_rule_types_rule_type: [ TABLE.rule_type, COLUMN.rule_type.id, COLUMN.applications_rule_types.rule_type_id ],
  applications_rule_sub_types: [ TABLE.applications_rule_sub_types, COLUMN.application.id, COLUMN.applications_rule_sub_types.application_id ],
  applications_rule_sub_types_rule_sub_type: [ TABLE.rule_sub_type, COLUMN.rule_sub_type.id, COLUMN.applications_rule_sub_types.rule_sub_type_id ],
  role_roles_permissions: [ TABLE.roles_permissions, COLUMN.role.id, COLUMN.roles_permissions.role_id ],
  roles_permissions_role: [ TABLE.role, COLUMN.role.id, COLUMN.roles_permissions.role_id ],
  roles_permissions_users_roles: [ TABLE.users_roles, COLUMN.users_roles.role_id, COLUMN.roles_permissions.role_id ],
  roles_permissions_team: [ TABLE.team, COLUMN.roles_permissions.team_id, COLUMN.team.id ],
  team_access_pages: [ TABLE.access_pages, COLUMN.team.id, COLUMN.access_pages.team_id ],
  team_access_rule_sub_types: [ TABLE.access_rule_sub_types, COLUMN.team.id, COLUMN.access_rule_sub_types.team_id ],
  team_roles_permissions: [ TABLE.roles_permissions, COLUMN.team.id, COLUMN.roles_permissions.team_id ],
  team_teams_permissions: [ TABLE.teams_permissions, COLUMN.team.id, COLUMN.teams_permissions.team_id ],
  team_application: [ TABLE.application, COLUMN.team.id, COLUMN.application.team_id ],
  access_containers_application: [ TABLE.application, COLUMN.access_containers.application_id, COLUMN.application.id ],
  access_containers_container: [ TABLE.container, COLUMN.access_containers.container_id, COLUMN.container.id ],
  access_containers_rule_type: [ TABLE.rule_type, COLUMN.access_containers.rule_type_id, COLUMN.rule_type.id ],
  access_containers_access_levels: [ TABLE.access_levels, COLUMN.access_containers.access_id, COLUMN.access_levels.access_id ],
  access_pages_application: [ TABLE.application, COLUMN.access_pages.application_id, COLUMN.application.id ],
  access_pages_page: [ TABLE.page, COLUMN.access_pages.page_id, COLUMN.page.id ],
  access_pages_rule_type: [ TABLE.rule_type, COLUMN.access_pages.rule_type_id, COLUMN.rule_type.id ],
  access_pages_access_levels: [ TABLE.access_levels, COLUMN.access_pages.access_id, COLUMN.access_levels.access_id ],
  access_rule_sub_types_application: [ TABLE.application, COLUMN.access_rule_sub_types.application_id, COLUMN.application.id ],
  access_rule_sub_types_rule_sub_type: [ TABLE.rule_sub_type, COLUMN.access_rule_sub_types.rule_sub_type_id, COLUMN.rule_sub_type.id ],
  access_rule_sub_types_rule_type: [ TABLE.rule_type, COLUMN.access_rule_sub_types.rule_type_id, COLUMN.rule_type.id ],
  access_rule_sub_types_access_levels: [ TABLE.access_levels, COLUMN.access_rule_sub_types.access_id, COLUMN.access_levels.access_id ],
};

module.exports = {
  TABLE,
  COL_NAME,
  COLUMN,
  SELECT,
  JOIN,
};
