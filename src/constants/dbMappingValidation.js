const mapTableColumn = {
  userEmail: [ {
    table: 'user',
    column: 'email',
  } ],
  userSid: [ {
    table: 'user',
    column: 'sid',
  } ],
  roleName: [
    {
      table: 'role',
      column: 'name',
    },
    {
      table: 'roles_permissions',
      column: 'team_id',
    },
  ],
  applicationName: [
    {
      table: 'application',
      column: 'name',
    },
  ],
};

const mapJoinTables = {
  roleName: [ {
    joinKeyword: 'role_roles_permissions',
    join: true,
  } ],
};

module.exports = {
  mapTableColumn,
  mapJoinTables,
};
