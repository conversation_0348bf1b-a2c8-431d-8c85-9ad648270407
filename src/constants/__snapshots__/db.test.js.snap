// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Database constants COLUMN constant 1`] = `
{
  "access_containers": {
    "access_id": "admin_access_containers.admin_access_id",
    "active": "admin_access_containers.active",
    "application_id": "admin_access_containers.admin_application_id",
    "container_id": "admin_access_containers.admin_container_id",
    "rule_type_id": "admin_access_containers.admin_rule_type_id",
    "team_id": "admin_access_containers.admin_team_id",
  },
  "access_levels": {
    "access_id": "ref_admin_access.admin_access_id",
    "access_level": "ref_admin_access.admin_access_level",
    "access_name": "ref_admin_access.admin_access_name",
  },
  "access_pages": {
    "access_id": "admin_access_pages.admin_access_id",
    "active": "admin_access_pages.active",
    "application_id": "admin_access_pages.admin_application_id",
    "page_id": "admin_access_pages.admin_page_id",
    "rule_type_id": "admin_access_pages.admin_rule_type_id",
    "team_id": "admin_access_pages.admin_team_id",
  },
  "access_rule_sub_types": {
    "access_id": "admin_access_rule_sub_types.admin_access_id",
    "active": "admin_access_rule_sub_types.active",
    "application_id": "admin_access_rule_sub_types.admin_application_id",
    "rule_sub_type_id": "admin_access_rule_sub_types.admin_rule_sub_type_id",
    "rule_type_id": "admin_access_rule_sub_types.admin_rule_type_id",
    "team_id": "admin_access_rule_sub_types.admin_team_id",
  },
  "application": {
    "applicationId": "ref_admin_application.admin_application_slug",
    "contentful_space": "ref_admin_application.admin_application_contentful_space",
    "description": "ref_admin_application.admin_application_description",
    "id": "ref_admin_application.admin_application_id",
    "name": "ref_admin_application.admin_application_name",
    "rule_version": "ref_admin_application.admin_application_rule_version",
    "status": "ref_admin_application.admin_application_status",
    "team_id": "ref_admin_application.admin_team_id",
  },
  "applications_platforms": {
    "application_id": "admin_applications_platforms.admin_application_id",
    "platform_id": "admin_applications_platforms.admin_platform_id",
  },
  "applications_rule_sub_types": {
    "application_id": "admin_applications_rule_sub_types.admin_application_id",
    "rule_sub_type_id": "admin_applications_rule_sub_types.admin_rule_sub_type_id",
  },
  "applications_rule_types": {
    "application_id": "admin_applications_rule_types.admin_application_id",
    "rule_type_id": "admin_applications_rule_types.admin_rule_type_id",
  },
  "business_line": {
    "code": "ref_business_line.business_line_type",
    "description": "ref_business_line.business_line_description",
    "id": "ref_business_line.business_line_id",
  },
  "container": {
    "application": "ref_admin_container.admin_application_id",
    "campaign_limit": "ref_admin_container.admin_container_limit",
    "containerId": "ref_admin_container.admin_container_slug",
    "content_type": "ref_admin_container.content_type",
    "description": "ref_admin_container.admin_container_description",
    "id": "ref_admin_container.admin_container_id",
    "name": "ref_admin_container.admin_container_name",
    "rule_type_id": "ref_admin_container.admin_rule_type_id",
    "status": "ref_admin_container.admin_container_status",
  },
  "containers_pages": {
    "container_id": "admin_containers_pages.admin_container_id",
    "page_id": "admin_containers_pages.admin_page_id",
  },
  "device": {
    "code": "ref_device.device_type",
    "description": "ref_device.device_description",
    "id": "ref_device.device_id",
  },
  "page": {
    "application": "ref_admin_page.admin_application_id",
    "description": "ref_admin_page.admin_page_description",
    "id": "ref_admin_page.admin_page_id",
    "name": "ref_admin_page.admin_page_name",
    "pageId": "ref_admin_page.admin_page_slug",
    "status": "ref_admin_page.admin_page_status",
  },
  "permission": {
    "description": "ref_admin_permission.admin_permission_description",
    "id": "ref_admin_permission.admin_permission_id",
    "name": "ref_admin_permission.admin_permission_name",
  },
  "platform": {
    "id": "ref_admin_platform.admin_platform_id",
    "name": "ref_admin_platform.admin_platform_name",
    "slug": "ref_admin_platform.admin_platform_slug",
  },
  "product": {
    "code": "ref_product.product_type",
    "description": "ref_product.product_description",
    "id": "ref_product.product_id",
    "sub_category_id": "ref_product.product_sub_category_id",
  },
  "product_sub_category": {
    "id": "ref_product_sub_category.product_sub_category_id",
    "sub_category_description": "ref_product_sub_category.product_sub_category_description",
  },
  "province": {
    "code": "ref_province.province_code",
    "description": "ref_province.province_description",
    "id": "ref_province.province_id",
  },
  "registration_status": {
    "code": "ref_registration_status.registration_status_type",
    "description": "ref_registration_status.registration_status_description",
    "id": "ref_registration_status.registration_status_id",
  },
  "role": {
    "id": "ref_admin_role.admin_role_id",
    "name": "ref_admin_role.admin_role_name",
    "status": "ref_admin_role.active",
  },
  "roles_permissions": {
    "permission_id": "admin_roles_permissions.admin_permission_id",
    "permission_name": "admin_roles_permissions.admin_permission_name",
    "role_id": "admin_roles_permissions.admin_role_id",
    "team_id": "admin_roles_permissions.admin_team_id",
  },
  "rule_sub_type": {
    "description": "ref_admin_rule_sub_type.admin_rule_sub_type_description",
    "id": "ref_admin_rule_sub_type.admin_rule_sub_type_id",
    "name": "ref_admin_rule_sub_type.admin_rule_sub_type_name",
  },
  "rule_type": {
    "id": "ref_admin_rule_type.admin_rule_type_id",
    "rule_type": "ref_admin_rule_type.admin_rule_type_name",
    "slug": "ref_admin_rule_type.admin_rule_type_slug",
  },
  "team": {
    "active": "ref_admin_team.admin_team_active",
    "description": "ref_admin_team.admin_team_description",
    "id": "ref_admin_team.admin_team_id",
    "name": "ref_admin_team.admin_team_name",
  },
  "teams_permissions": {
    "permission_id": "admin_teams_permissions.admin_permission_id",
    "permission_name": "admin_teams_permissions.admin_permission_name",
    "team_id": "admin_teams_permissions.admin_team_id",
  },
  "user": {
    "active": "admin_user.active_indicator",
    "email": "admin_user.email",
    "id": "admin_user.admin_user_id",
    "name": "admin_user.full_name",
    "sid": "admin_user.sid",
    "token": "admin_user.token",
    "token_expire_date": "admin_user.token_expire_date",
    "token_expires": "admin_user.token_expire_indicator",
  },
  "user_campaign_rules": {
    "admin_user_id": "admin_user_campaign_rules.assigned_admin_user_id",
    "campaign_rule_id": "admin_user_campaign_rules.campaign_rule_id",
    "id": "admin_user_campaign_rules.admin_user_campaign_rules_id",
  },
  "users_roles": {
    "role_id": "admin_users_roles.admin_role_id",
    "user_id": "admin_users_roles.admin_user_id",
  },
}
`;

exports[`Database constants SELECT constant 1`] = `
{
  "access_containers": {
    "access_id": "admin_access_containers.admin_access_id AS access_id",
    "active": "admin_access_containers.active AS active",
    "application_id": "admin_access_containers.admin_application_id AS application_id",
    "container_id": "admin_access_containers.admin_container_id AS container_id",
    "rule_type_id": "admin_access_containers.admin_rule_type_id AS rule_type_id",
    "team_id": "admin_access_containers.admin_team_id AS team_id",
  },
  "access_levels": {
    "access_id": "ref_admin_access.admin_access_id AS access_id",
    "access_level": "ref_admin_access.admin_access_level AS access_level",
    "access_name": "ref_admin_access.admin_access_name AS access_name",
  },
  "access_pages": {
    "access_id": "admin_access_pages.admin_access_id AS access_id",
    "active": "admin_access_pages.active AS active",
    "application_id": "admin_access_pages.admin_application_id AS application_id",
    "page_id": "admin_access_pages.admin_page_id AS page_id",
    "rule_type_id": "admin_access_pages.admin_rule_type_id AS rule_type_id",
    "team_id": "admin_access_pages.admin_team_id AS team_id",
  },
  "access_rule_sub_types": {
    "access_id": "admin_access_rule_sub_types.admin_access_id AS access_id",
    "active": "admin_access_rule_sub_types.active AS active",
    "application_id": "admin_access_rule_sub_types.admin_application_id AS application_id",
    "rule_sub_type_id": "admin_access_rule_sub_types.admin_rule_sub_type_id AS rule_sub_type_id",
    "rule_type_id": "admin_access_rule_sub_types.admin_rule_type_id AS rule_type_id",
    "team_id": "admin_access_rule_sub_types.admin_team_id AS team_id",
  },
  "application": {
    "applicationId": "ref_admin_application.admin_application_slug AS applicationId",
    "contentful_space": "ref_admin_application.admin_application_contentful_space AS contentful_space",
    "description": "ref_admin_application.admin_application_description AS description",
    "id": "ref_admin_application.admin_application_id AS id",
    "name": "ref_admin_application.admin_application_name AS name",
    "rule_version": "ref_admin_application.admin_application_rule_version AS rule_version",
    "status": "ref_admin_application.admin_application_status AS status",
    "team_id": "ref_admin_application.admin_team_id AS team_id",
  },
  "applications_platforms": {
    "application_id": "admin_applications_platforms.admin_application_id AS application_id",
    "platform_id": "admin_applications_platforms.admin_platform_id AS platform_id",
  },
  "applications_rule_sub_types": {
    "application_id": "admin_applications_rule_sub_types.admin_application_id AS application_id",
    "rule_sub_type_id": "admin_applications_rule_sub_types.admin_rule_sub_type_id AS rule_sub_type_id",
  },
  "applications_rule_types": {
    "application_id": "admin_applications_rule_types.admin_application_id AS application_id",
    "rule_type_id": "admin_applications_rule_types.admin_rule_type_id AS rule_type_id",
  },
  "business_line": {
    "code": "ref_business_line.business_line_type AS code",
    "description": "ref_business_line.business_line_description AS description",
    "id": "ref_business_line.business_line_id AS id",
  },
  "container": {
    "application": "ref_admin_container.admin_application_id AS application",
    "campaign_limit": "ref_admin_container.admin_container_limit AS campaign_limit",
    "containerId": "ref_admin_container.admin_container_slug AS containerId",
    "content_type": "ref_admin_container.content_type AS content_type",
    "description": "ref_admin_container.admin_container_description AS description",
    "id": "ref_admin_container.admin_container_id AS id",
    "name": "ref_admin_container.admin_container_name AS name",
    "rule_type_id": "ref_admin_container.admin_rule_type_id AS rule_type_id",
    "status": "ref_admin_container.admin_container_status AS status",
  },
  "containers_pages": {
    "container_id": "admin_containers_pages.admin_container_id AS container_id",
    "page_id": "admin_containers_pages.admin_page_id AS page_id",
  },
  "device": {
    "code": "ref_device.device_type AS code",
    "description": "ref_device.device_description AS description",
    "id": "ref_device.device_id AS id",
  },
  "page": {
    "application": "ref_admin_page.admin_application_id AS application",
    "description": "ref_admin_page.admin_page_description AS description",
    "id": "ref_admin_page.admin_page_id AS id",
    "name": "ref_admin_page.admin_page_name AS name",
    "pageId": "ref_admin_page.admin_page_slug AS pageId",
    "status": "ref_admin_page.admin_page_status AS status",
  },
  "permission": {
    "description": "ref_admin_permission.admin_permission_description AS description",
    "id": "ref_admin_permission.admin_permission_id AS id",
    "name": "ref_admin_permission.admin_permission_name AS name",
  },
  "platform": {
    "id": "ref_admin_platform.admin_platform_id AS id",
    "name": "ref_admin_platform.admin_platform_name AS name",
    "slug": "ref_admin_platform.admin_platform_slug AS slug",
  },
  "product": {
    "code": "ref_product.product_type AS code",
    "description": "ref_product.product_description AS description",
    "id": "ref_product.product_id AS id",
    "sub_category_id": "ref_product.product_sub_category_id AS sub_category_id",
  },
  "product_sub_category": {
    "id": "ref_product_sub_category.product_sub_category_id AS id",
    "sub_category_description": "ref_product_sub_category.product_sub_category_description AS sub_category_description",
  },
  "province": {
    "code": "ref_province.province_code AS code",
    "description": "ref_province.province_description AS description",
    "id": "ref_province.province_id AS id",
  },
  "registration_status": {
    "code": "ref_registration_status.registration_status_type AS code",
    "description": "ref_registration_status.registration_status_description AS description",
    "id": "ref_registration_status.registration_status_id AS id",
  },
  "role": {
    "id": "ref_admin_role.admin_role_id AS id",
    "name": "ref_admin_role.admin_role_name AS name",
    "status": "ref_admin_role.active AS status",
  },
  "roles_permissions": {
    "permission_id": "admin_roles_permissions.admin_permission_id AS permission_id",
    "permission_name": "admin_roles_permissions.admin_permission_name AS permission_name",
    "role_id": "admin_roles_permissions.admin_role_id AS role_id",
    "team_id": "admin_roles_permissions.admin_team_id AS team_id",
  },
  "rule_sub_type": {
    "description": "ref_admin_rule_sub_type.admin_rule_sub_type_description AS description",
    "id": "ref_admin_rule_sub_type.admin_rule_sub_type_id AS id",
    "name": "ref_admin_rule_sub_type.admin_rule_sub_type_name AS name",
  },
  "rule_type": {
    "id": "ref_admin_rule_type.admin_rule_type_id AS id",
    "rule_type": "ref_admin_rule_type.admin_rule_type_name AS rule_type",
    "slug": "ref_admin_rule_type.admin_rule_type_slug AS slug",
  },
  "team": {
    "active": "ref_admin_team.admin_team_active AS active",
    "description": "ref_admin_team.admin_team_description AS description",
    "id": "ref_admin_team.admin_team_id AS id",
    "name": "ref_admin_team.admin_team_name AS name",
  },
  "teams_permissions": {
    "permission_id": "admin_teams_permissions.admin_permission_id AS permission_id",
    "permission_name": "admin_teams_permissions.admin_permission_name AS permission_name",
    "team_id": "admin_teams_permissions.admin_team_id AS team_id",
  },
  "user": {
    "active": "admin_user.active_indicator AS active",
    "email": "admin_user.email AS email",
    "id": "admin_user.admin_user_id AS id",
    "name": "admin_user.full_name AS name",
    "sid": "admin_user.sid AS sid",
    "token": "admin_user.token AS token",
    "token_expire_date": "admin_user.token_expire_date AS token_expire_date",
    "token_expires": "admin_user.token_expire_indicator AS token_expires",
  },
  "user_campaign_rules": {
    "admin_user_id": "admin_user_campaign_rules.assigned_admin_user_id AS admin_user_id",
    "campaign_rule_id": "admin_user_campaign_rules.campaign_rule_id AS campaign_rule_id",
    "id": "admin_user_campaign_rules.admin_user_campaign_rules_id AS id",
  },
  "users_roles": {
    "role_id": "admin_users_roles.admin_role_id AS role_id",
    "user_id": "admin_users_roles.admin_user_id AS user_id",
  },
}
`;
