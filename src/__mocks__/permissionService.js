const mockPermissions = require('./permissionsMocks');

const permissionServiceMock = {
  getPermissionsForUser: userId => {
    const userIdToRole = {
      1: 'admin',
      2: 'workflow_manager',
      3: 'campaign_manager',
      4: 'notification_manager',
    };
    return mockPermissions[userIdToRole[userId]];
  },
  setUserRoles: userId => {
    return {};
  },
  getRoles: () => {
    return [];
  },
};

module.exports = permissionServiceMock;
