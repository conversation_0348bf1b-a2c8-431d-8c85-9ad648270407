// const getUsers = require('../../../_OLD/user/routes/getUsers');
// const permissionsServiceMock = require('__test__/permissionService');

const mockService = {
  getUser: jest.fn().mockReturnValue([ { sid: 's1234567', name: 'Test Name' } ]),
};

// const jsonResp = {
//   json: jest.fn(),
// };
//
// const resMock = {
//   status: jest.fn().mockReturnValue(jsonResp),
//   locals: {
//     user: {
//       id: 1,
//       role_id: 1,
//     },
//   },
// };
//
// const reqMock = {
//   body: {},
// };
//
// const nextMock = jest.fn();
//
// describe('POST /api/v1/users - get users', () => {
//   test('should return 200 with valid request', async() => {
//     const asyncGetUsers = getUsers(mockService, permissionsServiceMock);
//     await asyncGetUsers(reqMock, resMock, nextMock);
//     expect(mockService.getUsers).toHaveBeenCalled();
//     expect(nextMock).not.toHaveBeenCalled();
//     expect(resMock.status).toHaveBeenCalled();
//   });
// });

module.exports = mockService;
