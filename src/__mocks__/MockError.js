const codes = require('http').STATUS_CODES;

class MockError extends Error {
  constructor(code, message = codes[code], response, request) {
    super(message);
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
    this.code = code;
    this.timestamp = (new Date()).toISOString();
    this.response = response;
    this.request = request;
  }
}

module.exports = {
  MockError,
};
