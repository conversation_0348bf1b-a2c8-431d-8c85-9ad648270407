const mockMessageDetails = [
  {
    id: 1,
    name: 'Momentum No Fee PA 1',
    long_name: '',
    campaign_event_type: 'ABM',
    campaign_id: 'PAC79',
    channel: 'ABM',
    start_date: '2024-06-17T19:47:44.129Z',
    end_date: *************,
    duration: 1,
    language: 'E',
    msg_category: 'ALT',
    msg_priority: 'N',
    msg_status: 'N',
    subject_line: "You've been pro-approved for Scotiabank No-Fee Momentum Credit Card.",
    url_params: '',
    static_url_ind: '',
    static_url_text: '',
    updated_user_id: 'VA032',
    update_user_date: '2024-06-17T19:47:44.129Z',
    msg_gen_date: '2024-06-17T19:47:44.129Z',
  },
  {
    id: 2,
    name: 'Momentum No Fee PA 2',
    long_name: '',
    campaign_event_type: 'ABM',
    campaign_id: 'PAC79',
    channel: 'ABM',
    start_date: '2024-06-17T19:47:44.129Z',
    end_date: *************,
    duration: 1,
    language: 'E',
    msg_category: 'ALT',
    msg_priority: 'N',
    msg_status: 'S',
    subject_line: "You've been pro-approved for Scotiabank No-Fee Momentum Credit Card.",
    url_params: '',
    static_url_ind: '',
    static_url_text: '',
    updated_user_id: 'VA032',
    update_user_date: '2024-06-17T19:47:44.129Z',
    msg_gen_date: '2024-06-17T19:47:44.129Z',
  },
  {
    id: 3,
    name: 'Momentum No Fee PA 3',
    long_name: '',
    campaign_event_type: 'ABM',
    campaign_id: 'PAC79',
    channel: 'ABM',
    start_date: '2024-06-17T19:47:44.129Z',
    end_date: *************,
    duration: 1,
    language: 'E',
    msg_category: 'ALT',
    msg_priority: 'N',
    msg_status: 'D',
    subject_line: "You've been pro-approved for Scotiabank No-Fee Momentum Credit Card.",
    url_params: '',
    static_url_ind: '',
    static_url_text: '',
    updated_user_id: 'VA032',
    update_user_date: '2024-06-17T19:47:44.129Z',
    msg_gen_date: '2024-06-17T19:47:44.129Z',
  },
  {
    id: 4,
    name: 'Momentum No Fee PA 4',
    long_name: '',
    campaign_event_type: 'ABM',
    campaign_id: 'PAC79',
    channel: 'ABM',
    start_date: '2024-06-17T19:47:44.129Z',
    end_date: *************,
    duration: 1,
    language: 'E',
    msg_category: 'ALT',
    msg_priority: 'N',
    msg_status: 'S',
    subject_line: "You've been pro-approved for Scotiabank No-Fee Momentum Credit Card.",
    url_params: '',
    static_url_ind: '',
    static_url_text: '',
    updated_user_id: 'VA032',
    update_user_date: '2024-06-17T19:47:44.129Z',
    msg_gen_date: '2024-06-17T19:47:44.129Z',
  },
  {
    id: 5,
    name: 'Momentum No Fee PA 5',
    long_name: '',
    campaign_event_type: 'ABM',
    campaign_id: 'PAC79',
    channel: 'ABM',
    start_date: '2024-06-17T19:47:44.129Z',
    end_date: *************,
    duration: 1,
    language: 'E',
    msg_category: 'ALT',
    msg_priority: 'N',
    msg_status: 'S',
    subject_line: "You've been pro-approved for Scotiabank No-Fee Momentum Credit Card.",
    url_params: '',
    static_url_ind: '',
    static_url_text: '',
    updated_user_id: 'VA032',
    update_user_date: '2024-06-17T19:47:44.129Z',
    msg_gen_date: '2024-06-17T19:47:44.129Z',
  },
  {
    id: 6,
    name: 'Momentum No Fee PA 6',
    long_name: '',
    campaign_event_type: 'ABM',
    campaign_id: 'PAC79',
    channel: 'ABM',
    start_date: '2024-06-17T19:47:44.129Z',
    end_date: *************,
    duration: 1,
    language: 'E',
    msg_category: 'ALT',
    msg_priority: 'N',
    msg_status: 'S',
    subject_line: "You've been pro-approved for Scotiabank No-Fee Momentum Credit Card.",
    url_params: '',
    static_url_ind: '',
    static_url_text: '',
    updated_user_id: 'VA032',
    update_user_date: '2024-06-17T19:47:44.129Z',
    msg_gen_date: '2024-06-17T19:47:44.129Z',
  },
  {
    id: 7,
    name: 'Momentum No Fee PA 7',
    long_name: '',
    campaign_event_type: 'ABM',
    campaign_id: 'PAC79',
    channel: 'ABM',
    start_date: '2024-06-17T19:47:44.129Z',
    end_date: *************,
    duration: 1,
    language: 'E',
    msg_category: 'ALT',
    msg_priority: 'N',
    msg_status: 'D',
    subject_line: "You've been pro-approved for Scotiabank No-Fee Momentum Credit Card.",
    url_params: '',
    static_url_ind: '',
    static_url_text: '',
    updated_user_id: 'VA032',
    update_user_date: '2024-06-17T19:47:44.129Z',
    msg_gen_date: '2024-06-17T19:47:44.129Z',
  },
  {
    id: 8,
    name: 'Momentum No Fee PA 8',
    long_name: '',
    campaign_event_type: 'ABM',
    campaign_id: 'PAC79',
    channel: 'ABM',
    start_date: '2024-06-17T19:47:44.129Z',
    end_date: *************,
    duration: 1,
    language: 'E',
    msg_category: 'ALT',
    msg_priority: 'N',
    msg_status: 'D',
    subject_line: "You've been pro-approved for Scotiabank No-Fee Momentum Credit Card.",
    url_params: '',
    static_url_ind: '',
    static_url_text: '',
    updated_user_id: 'VA032',
    update_user_date: '2024-06-17T19:47:44.129Z',
    msg_gen_date: '2024-06-17T19:47:44.129Z',
  },
  {
    id: 9,
    name: 'Momentum No Fee PA 9',
    long_name: '',
    campaign_event_type: 'ABM',
    campaign_id: 'PAC79',
    channel: 'ABM',
    start_date: '2024-06-17T19:47:44.129Z',
    end_date: *************,
    duration: 1,
    language: 'E',
    msg_category: 'ALT',
    msg_priority: 'N',
    msg_status: 'N',
    subject_line: "You've been pro-approved for Scotiabank No-Fee Momentum Credit Card.",
    url_params: '',
    static_url_ind: '',
    static_url_text: '',
    updated_user_id: 'VA032',
    update_user_date: '2024-06-17T19:47:44.129Z',
    msg_gen_date: '2024-06-17T19:47:44.129Z',
  },
  {
    id: 10,
    name: 'Momentum No Fee PA 10',
    long_name: '',
    campaign_event_type: 'ABM',
    campaign_id: 'PAC79',
    channel: 'ABM',
    start_date: '2024-06-17T19:47:44.129Z',
    end_date: *************,
    duration: 1,
    language: 'E',
    msg_category: 'ALT',
    msg_priority: 'N',
    msg_status: 'N',
    subject_line: "You've been pro-approved for Scotiabank No-Fee Momentum Credit Card.",
    url_params: '',
    static_url_ind: '',
    static_url_text: '',
    updated_user_id: 'VA032',
    update_user_date: '2024-06-17T19:47:44.129Z',
    msg_gen_date: '2024-06-17T19:47:44.129Z',
  },
];

module.exports = mockMessageDetails;
