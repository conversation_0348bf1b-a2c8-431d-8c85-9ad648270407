const mockOffer = {
  'offer_id': 'OFF-1',
  'title': 'Offer title22',
  'category': 'BENEFITS',
  'priority': 1,
  'start_date': '2025-02-25T04:00:00.000Z',
  'expiry_date': '2025-04-20T05:00:00.000Z',
  'offer_status': 'DRAFT',
  'products': {
    'any_of': [
      {
        'ownership_type': 'R',
        'product_code': 'DDA',
        'product_sub_code': 'CA',
      },
      {
        'ownership_type': 'R',
        'product_code': 'DDA',
        'product_sub_code': 'SC',
      },
    ],
  },
  'product_relationship': 'PRIMARY',
  'location': {
    'country': 'CA',
    'provinces_states': [
      'AB', 'ON',
    ],
  },
  'target_language': [
    'ENGLISH',
    'FRENCH',
  ],
  'contentful_id': '5kK974iIBvudbjWdMPwKIl',
  'contentful_space_id': 'iy4ddvu7jd2s',
  'contentful_type': 'cmOffer',
  'change_history': {
    'created_by': 's6295903',
    'created_timestamp': '2018-10-17T00:00:00Z',
    'last_updated_by': 's6295903',
    'last_updated_timestamp': '2018-10-17T00:00:00Z',
    'last_action': 'STATUS_UPDATE',
  },
  'approvers': [ 's1234567' ],
  'reviewers': [ 's1234567' ],
};

module.exports = mockOffer;
