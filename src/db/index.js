const knex = require('knex');
const { db: dbConfig } = require('../constants/config');

const db = opts => {
  const dbInst = knex(opts || {
    client: 'mssql',
    connection: {
      database: dbConfig.database,
      user: dbConfig.user,
      password: dbConfig.password,
      port: dbConfig.port,
      server: dbConfig.server,
      timezone: 'utc',
      options: dbConfig.options || {},
    },
    pool: { min: 0, max: 7 },
  });

  /**
   * Creates a query object on schema 'admin' inside of a given transaction.
   *
   * @param {*} transaction - transaction
   * @returns
   */
  const _trxQuery = transaction => table => {
    return transaction(table).withSchema('admin');
  };

  /**
   * Creates a transaction, and accepts a parameter of type function
   * which is executed inside of the new transaction.
   *
   * @example
   * const completedTransaction = await trx(async table => {
   *  await table(TABLE.t1).select(COLUMN.t1.id, 2).where(COLUMN.t1.id, 1)
   *  await table(TABLE.t2).select(COLUMN.t2.id, 2).where(COLUMN.t2.id, 1)
   * })
   *
   * @param {*} handler - a function which is executed inside of the transaction
   * @returns
   */
  const trx = async handler => dbInst.transaction(async transaction => handler(_trxQuery(transaction)));

  /**
   *  Creates a query object on schema 'admin'
   *
   * @param {*} tableName
   * @returns
   */
  const query = tableName => dbInst(tableName).withSchema('admin');

  return {
    dbInst,
    query,
    trx,
  };
};

module.exports = {
  db,
};
