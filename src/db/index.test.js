const mockKnex = require('mock-knex');
const { db: newDbClient } = require('.');

const db = newDbClient({ client: 'mssql' });
const tracker = mockKnex.getTracker();
describe('db', () => {
  beforeAll(() => {
    mockKnex.mock(db.dbInst);
  });
  afterAll(() => {
    mockKnex.unmock(db.dbInst);
  });
  beforeEach(() => {
    tracker.install();
  });
  afterEach(() => {
    tracker.uninstall();
  });

  it('should initialize db with default config when no opts are passed', () => {
    const { db: defaultDb } = require('.');
    const instance = defaultDb(); // no opts
    expect(instance).toHaveProperty('dbInst');
    expect(instance).toHaveProperty('query');
    expect(instance).toHaveProperty('trx');
  });

  it('query should use admin schema', async() => {
    tracker.on('query', (query) => {
      expect(query.sql.toLowerCase()).toContain('from [admin].[table1]');
      query.response([]);
    });

    await db.query('table1').select('*');
  });

  it('query', async() => {
    const expected = { a: 1, b: 2 };
    tracker.on('query', (query, step) => {
      step === 1 && query.response(expected);
    });
    const result = await db.query('table1');
    expect(result).toBe(expected);
  });

  it('transaction', async() => {
    const dbTrx = mockDbClient(db.dbInst);
    const expected = { a: 1, b: 2 };
    const commit = jest.fn();
    tracker.on('query', (query, step) => {
      if (query.sql.includes('begin') || query.sql.includes('commit')) {
        query.response([]);
        query.sql.includes('commit') && commit();
        return;
      }
      step === 2 && query.response(expected);
    });
    const result = await dbTrx.trx(db => db('table1'));
    expect(result).toBe(expected);
    expect(commit).toHaveBeenCalled();
  });

  it('transaction - rollback on error', async() => {
    const dbTrx = mockDbClient(db.dbInst);
    const expected = new Error('sql error');
    const commit = jest.fn();
    const rollback = jest.fn();
    tracker.on('query', (query, step) => {
      if (query.sql.includes('begin') || query.sql.includes('commit')) {
        query.response([]);
        query.sql.includes('commit') && commit();
        return;
      }
      step === 2 && query.reject(expected);
      rollback();
    });

    try {
      const result = await dbTrx.trx(db => db('table1'));
      expect(result).toBeFalsy();
    } catch (e) {
      expect(e).toBe(expected);
    }
    expect(commit).not.toHaveBeenCalled();
    expect(rollback).toHaveBeenCalled();
  });
});

// simulate transaction as mock-knex does not support transaction on mocked db instance
const mockDbClient = dbInst => {
  const trx = async handler => {
    await dbInst.raw('begin;');
    const result = await handler(dbInst);
    await dbInst.raw('commit;');
    return result;
  };
  return {
    query: dbInst,
    trx,
  };
};

module.exports = {
  mockDbClient,
};
