const { CustomError, ForbiddenError, BadRequestError } = require('../../error');
const { canUpdateCampaign } = require('../../permissions/workflow');
const {
  submittedToDraft,
  reviewedToDraft,
  reviewedToPublished,
} = require('../../permissions/workflow/transactions');

const CampaignConstants = require('./constants');

const {
  CAMPAIGNS_APPROVE,
  CAMPAIGNS_MANAGE,
  CAMPAIGNS_REVIEW,
  CCAU_CAMPAIGNS_APPROVE,
  CCAU_CAMPAIGNS_MANAGE,
  CCAU_CAMPAIGNS_REVIEW,
} = require('../../permissions');

const permissionStatusMap = {
  'draft': [ CAMPAIGNS_MANAGE ],
  'submitted': [ CAMPAIGNS_REVIEW ],
  'reviewed': [ CAMPAIGNS_APPROVE ],
  'published': [ CAMPAIGNS_MANAGE ],
};

const permissionStatusMapCCAU = {
  'draft': [ CCAU_CAMPAIGNS_MANAGE ],
  'submitted': [ CCAU_CAMPAIGNS_REVIEW ],
  'reviewed': [ CCAU_CAMPAIGNS_APPROVE ],
  'published': [ CCAU_CAMPAIGNS_MANAGE ],
};

const updatePartialCampaign = (logger, service, ruleService) => async(req, res, next) => {
  const params = res.locals.validatedParams;
  const body = res.locals.validatedBody;

  const campaignBefore = (await service.getCampaign(params.ruleId)).data;
  const allow = canUpdateCampaign(res.locals.user, campaignBefore, body);
  if (!allow) {
    return next(new ForbiddenError());
  }
  const sid = res.locals.user && res.locals.user.sid;
  body.updated_by = sid;

  try {
    // verify user assignee list for campaign
    try {
      if (
        (submittedToDraft(campaignBefore, body) || reviewedToDraft(campaignBefore, body)) &&
        (body.assignees && (!body.assignees.includes(campaignBefore.created_by) || !body.assignees.includes(campaignBefore.updated_by)))) {
        return next(
          new BadRequestError(
            `Cannot assign users while campaign is draft status`,
          ),
        );
      } else if (reviewedToPublished(campaignBefore, body) && body.assignees) {
        // no assignees should be included in the request
        return next(new BadRequestError('Cannot assign users to campaign while publishing.'));
      } else if ((body.status === campaignBefore.status) &&
        (body.status === CampaignConstants.CAMPAIGN_STATUS_DRAFT) &&
        (body.assignees !== undefined && body.assignees.length > 0) &&
        (!body.assignees.includes(campaignBefore.created_by))) {
        return next(new BadRequestError(`Cannot assign users while campaign is draft status.`));
      } else {
        if (body.status && body.assignees && body.assignees.length > 0) {
          let requiredPermissions;

          requiredPermissions = permissionStatusMap[body.status];

          if (campaignBefore.type === CampaignConstants.CCAU_CAMPAIGN_RULE_TYPE) {
            requiredPermissions = permissionStatusMapCCAU[body.status];
          }
          const result = await ruleService.validateUsers(body.assignees, requiredPermissions);
          if (result.success === false) {
            return next(new BadRequestError(`Invalid users detected: \n\n${JSON.stringify(result.error)}`));
          }
        }
      }
    } catch (err) {
      logger.error({ message: err.messsage, err: { code: err.code, stack: err.stack } });
      next(new CustomError(500, 'Error validating users for campaign assignment.'));
      return;
    }

    // update campaign
    const campaign = await service.patchCampaign(params.ruleId, body);

    // possibly save this result for error recovery
    let assignmentResult = [];
    // this is a rejection
    if (body.status === CampaignConstants.CAMPAIGN_STATUS_DRAFT) {
      await ruleService.removeUsersFromCampaign(campaignBefore.id);
      assignmentResult = await ruleService.assignUserToCampaign(campaignBefore.created_by, campaignBefore.id);
    // this is approve for publishing
    } else if (body.assignees) {
      await ruleService.removeUsersFromCampaign(campaignBefore.id);
      assignmentResult = await ruleService.assignUserListToCampaign(body.assignees, campaignBefore.id);
    }

    return res.status(200).json(Object.assign({ assignees: assignmentResult }, campaign.data));
  } catch (err) {
    logger.error({ message: err.messsage, err: { code: err.code, stack: err.stack } });

    if (err.response) { // the request was made and a response was received
      return next(new CustomError(err.response.status, JSON.stringify(err.response.data)));
    } else if (err.request) { // the req  uest was made and there is no response
      return next(new CustomError(500, JSON.stringify(err.request)));
    } else {
      return next(new CustomError(500, JSON.stringify(err.message)));
    }
  }
};

module.exports = updatePartialCampaign;
