const { CustomError } = require('../../error');

const deleteCampaign = function(service) {
  return async function(req, res, next) {
    const value = res.locals.validatedParams;

    try {
      const rule = await service.deleteCampaign(value.ruleId);
      res.status(200).json(rule.data);
    } catch (err) {
      if (err.response) {
        if (err.response.status < 500) {
          res.status(err.response.status).json(err.response.data);
        } else {
          next(new CustomError(err.response.status, JSON.stringify(err.response.data)));
        }
      } else {
        next(new CustomError(500, err.toString()));
      }
    }
  };
};

module.exports = deleteCampaign;
