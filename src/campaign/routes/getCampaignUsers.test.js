const getCampaignUsers = require('./getCampaignUsers');
const permissions = require('__mocks__/permissionsMocks');

const mockRuleService = {
  getPotentialAssigneesForCampaign: jest.fn(),
};

const mockReq = {};
const mockResp = {
  locals: {
    validatedParams: {},
    user: {},
  },
  status: jest.fn(),
};
const mockNext = jest.fn();

describe('GET /api/v1/campaign-users - get campaign users', () => {
  beforeEach(() => {
    mockResp.locals.user = {};
    mockResp.status.mockClear();
  });

  test('Should throw error when insufficient permissions are found', async() => {
    const asyncGetCampaignUsers = getCampaignUsers(mockRuleService);
    const result = await asyncGetCampaignUsers(mockReq, mockResp, mockNext);

    expect(result == null || result === undefined).toBe(true);
    expect(mockNext).toHaveBeenCalledTimes(1);
  });

  test('Should return 200 for valid req', async() => {
    mockRuleService.getPotentialAssigneesForCampaign.mockReturnValueOnce(
      [
        {
          full_name: 'Test User 1',
          sid: 's123456',
        },
        {
          full_name: 'Test User 2',
          sid: 's789456',
        },
      ]);

    mockResp.locals.user.permissions = permissions.admin;

    const validReq = {
      params: {
        ruleId: 'abc123',
      },
    };

    const mockJson = {
      json: jest.fn(),
    };

    mockResp.status.mockReturnValueOnce(mockJson);

    const asyncGetCampaignUsers = getCampaignUsers(mockRuleService);
    const result = await asyncGetCampaignUsers(validReq, mockResp, mockNext);

    expect(result !== null || result !== undefined).toBe(true);
    expect(mockResp.status).toHaveBeenCalledTimes(1);
    expect(mockNext).toHaveBeenCalledTimes(0);
    expect(mockJson.json).toHaveBeenCalledTimes(1);
  });

  test('Should return 500 error when 500 error response returned from rule assignment service', async() => {
    const error = new Error();
    error.response = {
      status: 500,
      data: {
        message: 'This is a mock eror',
      },
    };

    mockRuleService.getPotentialAssigneesForCampaign.mockImplementation(() => {
      throw error;
    });

    mockResp.locals.user.permissions = permissions.admin;

    const validReq = {
      params: {
        ruleId: 'abc123',
      },
    };

    const mockJson = {
      json: jest.fn(),
    };

    mockResp.status.mockReturnValueOnce(mockJson);

    const asyncGetCampaignUsers = getCampaignUsers(mockRuleService);
    const result = await asyncGetCampaignUsers(validReq, mockResp, mockNext);

    expect(result !== null || result !== undefined).toBe(true);
    expect(mockResp.status).toHaveBeenCalledTimes(0);
    expect(mockNext).toHaveBeenCalledTimes(1);
  });

  test('Should return 422 error when 422 error response returned from rule assignment service', async() => {
    const error = new Error();
    error.response = {
      status: 422,
      data: {
        message: 'This is a mock 422 error',
      },
    };

    mockRuleService.getPotentialAssigneesForCampaign.mockImplementation(() => {
      throw error;
    });

    mockResp.locals.user.permissions = permissions.admin;

    const validReq = {
      params: {
        ruleId: 'abc123',
      },
    };

    const mockJson = {
      json: jest.fn(),
    };

    mockResp.status.mockReturnValueOnce(mockJson);

    const asyncGetCampaignUsers = getCampaignUsers(mockRuleService);
    const result = await asyncGetCampaignUsers(validReq, mockResp, mockNext);

    expect(result !== null || result !== undefined).toBe(true);
    expect(mockResp.status).toHaveBeenCalledTimes(1);
  });
});
