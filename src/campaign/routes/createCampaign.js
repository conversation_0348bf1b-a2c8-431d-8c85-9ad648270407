const { BadRequestError, ForbiddenError, CustomError } = require('../../error');
const { canCreateCampaign } = require('../../permissions/workflow');
const CampaignStatus = require('./constants');
const {
  CAMPAIGNS_MANAGE, CCAU_CAMPAIGNS_MANAGE,
} = require('../../permissions');

const createCampaign = function(logger, service, ruleService, userService, applicationService) {
  return async function(req, res, next) {
    const value = res.locals.validatedBody;

    const allow = await canCreateCampaign(res.locals.user, req.body);
    if (!allow) {
      return next(new ForbiddenError());
    }

    const application = await applicationService.getApplicationByName(value.application);
    if (!application) {
      return next(new BadRequestError('Invalid application'));
    }
    if (!application.ruleTypes.includes(value.type)) {
      return next(new BadRequestError('Invalid combination of application & rule type'));
    }
    // if campaign is in draft, no assignee are allowed. item must be assigned to campaign rule author
    if (req.body.assignees && req.body.status === CampaignStatus.CAMPAIGN_STATUS_DRAFT) {
      next(new BadRequestError('Cannot assign user to a campaign rule in draft status.'));
      return;
    } else if (!req.body.assignees && (req.body.status === CampaignStatus.CAMPAIGN_STATUS_REVIEWED || req.body.status === CampaignStatus.CAMPAIGN_STATUS_SUBMITTED)) {
      next(new BadRequestError('Campaign must include assignees.'));
      return;
    }
    const sid = res.locals.user && res.locals.user.sid;
    value.created_by = sid;
    value.updated_by = sid;

    try {
      if (value.assignees && (value.status === CampaignStatus.CAMPAIGN_STATUS_SUBMITTED)) {
        let permission = value.type === CampaignStatus.CCAU_CAMPAIGN_RULE_TYPE ? CCAU_CAMPAIGNS_MANAGE : CAMPAIGNS_MANAGE;
        const result = await ruleService.validateUsers(value.assignees, permission);

        if (!result.success) {
          return next(new BadRequestError(`Invalid users detected: \n\n${JSON.stringify(result.error)}`));
        }

        const newCampaign = await service.createCampaign(value);
        const campaignId = newCampaign.data.id;
        const assigneeResult = await ruleService.assignUserListToCampaign(value.assignees, campaignId);
        const campaignData = Object.assign({ assignees: assigneeResult }, newCampaign.data);

        return res.status(200).json(campaignData);
      } else if (!req.body.assignees && (value.status === CampaignStatus.CAMPAIGN_STATUS_DRAFT)) {
        const newCampaign = await service.createCampaign(value);

        const campaignId = newCampaign.data.id;

        await ruleService.assignUserToCampaign(newCampaign.data.created_by, campaignId);

        const userDetails = await userService.getUser({ sid: newCampaign.data.created_by });

        const campaignData = Object.assign({
          assignees: userDetails.map(({ sid: sidVal, name }) => ({
            sid: sidVal,
            full_name: name,
          })),
        }, newCampaign.data);

        return res.status(200).json(campaignData);
      }
    } catch (error) {
      logger.error({ message: error.messsage, err: { code: error.code, stack: error.stack } });
      if (error.response) { // the request was made and a response was received
        let errorMessage;
        if (error.response.data && error.response.data.message) {
          if (error.response.data.message.includes('duplicate')) {
            errorMessage = 'Campaign with this name already exists. Use a unique name for the new campaign.';
            return next(new BadRequestError(errorMessage));
          } else {
            errorMessage = error.response.data.message;
          }
        } else {
          errorMessage = JSON.stringify(error.response.data);
        }
        return next(new CustomError(error.response.status, errorMessage));
      } else if (error.request) { // the request was made and there is no response
        return next(new CustomError(500, JSON.stringify(error.request)));
      } else {
        return next(new CustomError(500, JSON.stringify(error.message)));
      }
    }
  };
};

module.exports = createCampaign;
