const { CustomError } = require('../../error');

const getCampaignUsers = function(service) {
  return async function(req, res, next) {
    const value = res.locals.validatedParams;

    try {
      const rule = await service.getPotentialAssigneesForCampaign(value.ruleId, res.locals.user.team_id);
      res.status(200).json({ data: rule });
    } catch (err) {
      if (err.response) {
        if (err.response.status < 500) {
          res.status(err.response.status).json(err.response.data);
        } else {
          next(new CustomError(err.response.status, JSON.stringify(err.response.data)));
        }
      } else {
        next(new CustomError(500, err.toString()));
      }
    }
  };
};

module.exports = getCampaignUsers;
