const express = require('express');
const getAll = require('./getAll');
const getCampaign = require('./getCampaign');
const deleteCampaign = require('./deleteCampaign');
const createCampaign = require('./createCampaign');
const updatePartialCampaign = require('./updatePartialCampaign');
const updateCampaign = require('./updateCampaign');
const getCampaignUsers = require('./getCampaignUsers');
const setupCampaign = require('./setupCampaign');
const exportRules = require('../../rules/exportRules');
const { getProductBook } = require('./getProductBook');
const { wrapAsync } = require('../../utils');
const {
  middleware: can,
  CAMPAIGNS_VIEW,
  CAMPAIGNS_MANAGE,
  CCAU_CAMPAIGNS_VIEW,
  CCAU_CAMPAIGNS_MANAGE,
} = require('../../permissions/index');
const {
  createSchema,
  getListSchema,
  getPathSchema,
  optionalGetPathSchema,
  updatePartialSchema,
  updateSchema,
  ruleExportSchema,
  VALIDATION_TYPE,
  schemaValidationMiddleware,
} = require('./validation');

const init = (
  config, logger,
  campaignService, marvelProductApi, userService, ruleService, vignetteService, contentApi, teamsService, applicationService,
) => {
  const router = express.Router();

  router.get('/campaign-users/:ruleId',
    can([ CAMPAIGNS_VIEW, CCAU_CAMPAIGNS_VIEW ], 'OR'),
    schemaValidationMiddleware(optionalGetPathSchema, VALIDATION_TYPE.PARAMS),
    wrapAsync(getCampaignUsers(ruleService)),
  );

  router.get('/campaign-users',
    can([ CAMPAIGNS_VIEW, CCAU_CAMPAIGNS_VIEW ], 'OR'),
    schemaValidationMiddleware(optionalGetPathSchema, VALIDATION_TYPE.PARAMS),
    wrapAsync(getCampaignUsers(ruleService)),
  );

  router.get('/campaign-rules',
    can([ CAMPAIGNS_VIEW, CCAU_CAMPAIGNS_VIEW ], 'OR'),
    schemaValidationMiddleware(getListSchema, VALIDATION_TYPE.QUERY),
    wrapAsync(getAll(campaignService, userService, teamsService)),
  );

  router.get('/export-campaign-rules',
    can([ CAMPAIGNS_VIEW, CCAU_CAMPAIGNS_VIEW ], 'OR'),
    schemaValidationMiddleware(ruleExportSchema, VALIDATION_TYPE.QUERY),
    wrapAsync(exportRules(config.redirectUrl, 'campaign', campaignService.getAllCampaigns, contentApi)),
  );

  router.get('/campaign-rules/:ruleId',
    can([ CAMPAIGNS_VIEW, CCAU_CAMPAIGNS_VIEW ], 'OR'),
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS),
    wrapAsync(getCampaign(campaignService, ruleService)),
  );

  router.get('/campaign-setup',
    wrapAsync(setupCampaign(vignetteService)),
  );

  router.get('/product-book', wrapAsync(getProductBook(config.productBook, logger, marvelProductApi).getProducts));

  router.delete('/campaign-rules/:ruleId',
    can([ CAMPAIGNS_MANAGE, CCAU_CAMPAIGNS_MANAGE ], 'OR'),
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS),
    wrapAsync(deleteCampaign(campaignService)),
  );

  router.post('/campaign-rules',
    can([ CAMPAIGNS_MANAGE, CCAU_CAMPAIGNS_MANAGE ], 'OR'),
    schemaValidationMiddleware(createSchema, VALIDATION_TYPE.BODY),
    wrapAsync(createCampaign(logger, campaignService, ruleService, userService, applicationService)),
  );

  router.patch('/campaign-rules/:ruleId',
    can([ CAMPAIGNS_MANAGE, CCAU_CAMPAIGNS_MANAGE ], 'OR'),
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS),
    schemaValidationMiddleware(updatePartialSchema, VALIDATION_TYPE.BODY),
    wrapAsync(updatePartialCampaign(logger, campaignService, ruleService)),
  );

  router.put('/campaign-rules/:ruleId',
    can([ CAMPAIGNS_MANAGE, CCAU_CAMPAIGNS_MANAGE ], 'OR'),
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS),
    schemaValidationMiddleware(updateSchema, VALIDATION_TYPE.BODY),
    wrapAsync(updateCampaign(logger, campaignService, ruleService, applicationService)),
  );

  return router;
};

module.exports = init;
