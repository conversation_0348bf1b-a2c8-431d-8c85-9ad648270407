const R = require('ramda');

let productBook = [];

// compare same field between product and override, ignore case
const matchProp = (prop, product, expected) => R.equals(
  (product[`${prop}`] || '').toLowerCase(),
  (expected[`${prop}`] || '').toLowerCase(),
);

const matchProps = (product, criteria, strict) => [ 'code', 'sub_code', 'ownership', 'category' ]
  .every(prop => (!criteria[`${prop}`] && !strict) || matchProp(prop, product, criteria));

const matchIdOrProps = (product, criteria, strict) => criteria.id
  ? matchProp('id', product, criteria) // match by id directly if provided
  : matchProps(product, criteria, strict); // otherwise match on all other fields

const addProduct = (logger, productBook, product) => {
  const duplicateFound = productBook.some(existingProduct => matchProps(existingProduct, product, true));
  if (duplicateFound) {
    logger.debug({ message: 'product book - dropped duplicate product', product });
    return productBook;
  }
  return [ ...productBook, product ];
};

const applyOverrides = (logger, products, overrides) => {
  const { deletes, additions, updates } = overrides.reduce((bins, override) => {
    if (override.action === 'delete') {
      bins.deletes.push(override);
    } else if (products.some(p => matchIdOrProps(p, override))) {
      bins.updates.push(override);
    } else {
      bins.additions.push(override);
    }
    return bins;
  }, { deletes: [], additions: [], updates: [] });

  // deletes
  const productsAfterDeletes = deletes.reduce((remainingProducts, override) => {
    // filter out matches by product id or combo of all other applicable props
    return remainingProducts.filter(product => !matchIdOrProps(product, override));
  }, products);

  // updates - only description is supported
  const productsAfterUpdates = updates.reduce((remainingProducts, override) => {
    const existingProduct = remainingProducts.find(p => matchIdOrProps(p, override, true));
    if (!existingProduct || !override.description) {
      logger.warn('product book - failed to apply override due to id mismatch or missing description override');
      return remainingProducts;
    }
    existingProduct.description = override.description;
    return remainingProducts;
  }, productsAfterDeletes);

  // additions
  const productsAfterAdditions = additions.reduce((remainingProducts, override) => {
    if (remainingProducts.some(p => p.id === override.id)) {
      logger.warn('product book - dropped override item with duplicate id ' + override.id);
      return remainingProducts;
    }
    // pick truthy fields, ie fields provided in override file
    const newItem = R.pick([ 'category', 'code', 'description', 'id', 'ownership', 'sub_code' ], override);
    // validate combo of all non-id fields do not result in duplication
    if (remainingProducts.some(p => R.equals(p, newItem))) {
      logger.warn('product book - dropped invalid new addition with id: ' + newItem.id);
    } else {
      return addProduct(logger, remainingProducts, newItem);
    }
    return remainingProducts;
  }, productsAfterUpdates);

  productsAfterAdditions.sort((a, b) => {
    if (a.id > b.id) return 1;
    if (a.id < b.id) return -1;
    return 0;
  });

  return productsAfterAdditions;
};

const mapToSpecs = (logger, products) => {
  const mapped = products.reduce((newProductBook, p) => {
    const categoryRaw = p.properties.find(prop => prop.type === 'CORNERSTONE_ID') || {};
    const category = !!categoryRaw.value && categoryRaw.value.toLowerCase();
    const defaultProductType = p.product_types.find(prop => prop.product_domain === 'default') || {};
    const code = defaultProductType.product_code && defaultProductType.product_code.trim();
    const subCode = defaultProductType.sub_product_code && defaultProductType.sub_product_code.trim();
    const descriptionLocalized = p.descriptions.find(desc => desc.locale === 'en_CA') || {};
    const description = descriptionLocalized.value;
    const { product_id: id, ownership } = p;
    if ((!code && !subCode) || !description || !category || !ownership) {
      return newProductBook; // drop misconfigured product
    }
    const newItem = { category, code, description, id, ownership, sub_code: subCode };
    Object.keys(newItem).forEach(k => !newItem[`${k}`] && delete newItem[`${k}`]); // pick truthy fields
    const newBook = addProduct(logger, newProductBook, newItem);
    return newBook;
  }, []);
  const filteredFalsy = mapped.filter(Boolean);
  return filteredFalsy;
};

const getProductBook = (config, logger, marvelProductApi) => {
  const { seedData, overrideData } = config;
  try {
    const mappedProducts = mapToSpecs(logger, seedData.data.products);
    productBook = applyOverrides(logger, mappedProducts, overrideData);
    logger.info('product book - successfully initialized product book cache');
  } catch (err) {
    logger.error({
      message: 'product book - service init failed',
      err: { msg: err.message, stack: err.stack },
    });
    return {};
  }

  // start refresh job
  const refreshJob = setInterval(async() => {
    try {
      const { data } = await marvelProductApi.getProducts();
      const mapped = mapToSpecs(logger, data.data.products);
      productBook = applyOverrides(logger, mapped, overrideData);
      logger.info('product book - successfully updated producted book');
    } catch (err) {
      logger.error({
        message: 'product book - failed to update product book',
        err: { msg: err.message, stack: err.stack },
      });
    }
  }, config.refreshIntervalMillis);

  return {
    getProducts: (req, res) => res.send(productBook),
    refreshJob,
  };
};
module.exports = {
  getProductBook,
  applyOverrides,
  mapToSpecs,
};
