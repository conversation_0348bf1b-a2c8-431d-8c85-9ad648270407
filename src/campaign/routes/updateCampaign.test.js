const UpdateCampaign = require('./updateCampaign');
const { BadRequestError, CustomError, ForbiddenError } = require('../../error');

// mock Campaign API client
const mockCampaignApiClient = {
  updateCampaign: jest.fn().mockResolvedValue({ id: '123456ab' }),
  getCampaign: jest.fn().mockResolvedValue({
    data: {
      name: 'test campaign',
    },
  }),
};

const mockNext = jest.fn();
const mockJson = jest.fn();
const mockStatus = jest.fn();

mockStatus.mockReturnValue({ json: mockJson });
let mockRes = {
  status: mockStatus,
  locals: {
    validatedParams: {},
    validatedBody: {},
    auth: { sid: 's1234567' },
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
    },
  },
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

const mockRuleService = {
  assignUserToCampaign: jest.fn(),
  validateUsers: jest.fn(),
  removeUsersFromCampaign: jest.fn(),
  assignUserListToCampaign: jest.fn(),
};
const mockApplicationsService = {
  getApplicationByName: jest.fn().mockReturnValue({ ruleTypes: [ 'campaign' ] }),
};

describe('Campaign Rules: PUT /campaign-rules/:ruleId', () => {
  beforeEach(() => {
    mockCampaignApiClient.updateCampaign.mockClear();
    mockCampaignApiClient.updateCampaign.mockReset();
    mockCampaignApiClient.getCampaign.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
    mockRuleService.validateUsers.mockClear();
    mockRuleService.removeUsersFromCampaign.mockClear();
    mockRuleService.assignUserToCampaign.mockClear();
    mockRuleService.assignUserListToCampaign.mockClear();

    mockRes = {
      status: mockStatus,
      locals: {
        validatedParams: {},
        validatedBody: {},
        auth: { sid: 's1234567' },
        user: {
          id: 1,
          role_id: 1,
          permissions: [ 'admin' ],
        },
      },
    };
  });

  test('Should return 200 when updating campaign from submitted to draft', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockCampaignBefore = {
      'assignees': [
        {
          'full_name': 'Donovan Palma',
          'sid': 's7435292',
        },
        {
          'full_name': 'test user',
          'sid': 's1234565',
        },
      ],
      'id': 'ZgFK7zPnEGr9',
      'name': 'dpalma-may-37-3554',
      'start_at': '2019-04-29T04:00:00.000Z',
      'end_at': '2019-04-30T04:00:00.000Z',
      'content_space': '4szkx38resvm',
      'content_type': 'standingCampaign',
      'content_id': '0qjYVZSfhTiDAKpHwRU43',
      'container': 'my-activity',
      'pages': [
        'activities',
      ],
      'external_ref': 'asdfc1234',
      'platforms': [
        'ios',
        'android',
      ],
      'app_version': null,
      'urgent': false,
      'status': 'submitted',
      'disabled': false,
      'created_by': 's789456123',
      'updated_by': 's789456123',
      'created_at': '2019-06-03T19:50:51.250Z',
      'updated_at': '2019-06-14T18:52:27.570Z',
    };
    const mockBody = {
      'name': 'dpalma-may-37-3554',
      'application': 'nova',
      'start_at': '2019-04-29T04:00:00.000Z',
      'end_at': '2019-04-30T04:00:00.000Z',
      'platforms': [
        'ios',
        'android',
      ],
      'app_version': null,
      'content_space': '4szkx38resvm',
      'content_type': 'standingCampaign',
      'content_id': '0qjYVZSfhTiDAKpHwRU43',
      'container': 'my-activity',
      'status': 'draft',
      'external_ref': 'asdfc1234',
      'pages': [
        'activities',
      ],
      'urgent': false,
      'type': 'campaign',
    };
    const mockUpdatedCampaignResponse = {
      data: {
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'draft',
        'disabled': false,
        'created_by': 's789456123',
        'updated_by': 's789456123',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-14T18:52:27.570Z',
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockCampaignBefore });
    mockCampaignApiClient.updateCampaign.mockResolvedValueOnce(mockUpdatedCampaignResponse);
    mockRuleService.validateUsers.mockReturnValueOnce({ success: true });
    mockRuleService.removeUsersFromCampaign.mockResolvedValueOnce({});
    mockRuleService.assignUserToCampaign.mockResolvedValueOnce([ { full_name: 'Test User', sid: mockCampaignBefore.updated_by } ]);

    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    await updateCampaign({}, { ...mockRes, locals: { ...mockRes.locals, validatedParams: mockParams, validatedBody: mockBody } }, mockNext);

    expect(mockCampaignApiClient.updateCampaign).toBeCalled();
    expect(mockStatus).toBeCalled();
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
    expect(mockJson.mock.calls[0][0].assignees !== undefined).toBe(true);
    expect(mockJson.mock.calls[0][0].assignees.some(function(item) {
      return item.sid === mockCampaignBefore.updated_by;
    }));
  });

  test('Should return 200 when updating campaign while in draft mode', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockCampaignBefore = {
      'assignees': [
        {
          'full_name': 'Donovan Palma',
          'sid': 's7435292',
        },
        {
          'full_name': 'test user',
          'sid': 's1234565',
        },
      ],
      'id': 'ZgFK7zPnEGr9',
      'name': 'dpalma-may-37-3554',
      'start_at': '2019-04-29T04:00:00.000Z',
      'end_at': '2019-04-30T04:00:00.000Z',
      'content_space': '4szkx38resvm',
      'content_type': 'standingCampaign',
      'content_id': '0qjYVZSfhTiDAKpHwRU43',
      'container': 'my-activity',
      'pages': [
        'activities',
      ],
      'external_ref': 'asdfc1234',
      'platforms': [
        'ios',
        'android',
      ],
      'app_version': null,
      'urgent': false,
      'status': 'draft',
      'disabled': false,
      'created_by': 's789456123',
      'updated_by': 's789456123',
      'created_at': '2019-06-03T19:50:51.250Z',
      'updated_at': '2019-06-14T18:52:27.570Z',
    };
    const mockBody = {
      'name': 'dpalma-may-37-3554',
      'application': 'nova',
      'start_at': '2019-04-29T04:00:00.000Z',
      'end_at': '2019-04-30T04:00:00.000Z',
      'platforms': [
        'ios',
        'android',
      ],
      'app_version': null,
      'content_space': '4szkx38resvm',
      'content_type': 'standingCampaign',
      'content_id': '0qjYVZSfhTiDAKpHwRU43',
      'container': 'my-activity',
      'status': 'draft',
      'external_ref': 'asdfc1234',
      'pages': [
        'activities',
      ],
      'urgent': false,
      'type': 'campaign',
    };
    const mockUpdatedCampaignResponse = {
      data: {
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'draft',
        'disabled': false,
        'created_by': 's789456123',
        'updated_by': 's789456123',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-14T18:52:27.570Z',
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockCampaignBefore });
    mockCampaignApiClient.updateCampaign.mockResolvedValueOnce(mockUpdatedCampaignResponse);
    mockRuleService.validateUsers.mockReturnValueOnce({ success: true });
    mockRuleService.removeUsersFromCampaign.mockResolvedValueOnce({});
    mockRuleService.assignUserToCampaign.mockResolvedValueOnce([ { full_name: 'Test User', sid: mockCampaignBefore.updated_by } ]);

    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    await updateCampaign({}, { ...mockRes, locals: { ...mockRes.locals, validatedParams: mockParams, validatedBody: mockBody } }, mockNext);

    expect(mockCampaignApiClient.updateCampaign).toBeCalled();
    expect(mockStatus).toBeCalled();
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
    expect(mockJson.mock.calls[0][0].assignees !== undefined).toBe(true);
    expect(mockJson.mock.calls[0][0].assignees.some(function(item) {
      return item.sid === mockCampaignBefore.updated_by;
    }));
  });

  test('should call next with CustomError when service response with error', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: '12345',
      application: 'nova',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-12-31T23:59:59Z',
      container: 'PRE_LOGIN',
      content_space: '4szkx38resvm',
      content_type: 'notification',
      content_id: '6zGhwB6kz6ii6IQ2Yg86wy',
      created_by: 's999999',
      updated_by: 's888888',
      pages: [ 'my-activity' ],
      assignees: [ 's123456', 's1234567' ],
      status: 'draft',
    };
    const mockResponse = {
      response: {
        status: 400,
        data: {
          code: 'HTTP_BAD_REQUEST',
          message: 'Database error',
          uuid: '04d69d4d-4c41-4373-b7f5-9b351a1f345d',
          timestamp: '2018-08-28T20:43:59.319Z',
          metadata: [ 'The duplicate key value is (sample name #38).' ],
        },
      },
    };
    mockCampaignApiClient.updateCampaign.mockRejectedValueOnce(mockResponse);
    mockRuleService.validateUsers.mockReturnValueOnce({ success: true });

    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);
    await updateCampaign({}, { ...mockRes, locals: { ...mockRes.locals, validatedParams: mockParams, validatedBody: mockBody } }, mockNext);
    // expect(mockCampaignApiClient.updateCampaign).toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(CustomError);
  });

  test('should call next with Forbidden error if insufficent permissions detected', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: '12345',
      application: 'nova',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-12-31T23:59:59Z',
      container: 'PRE_LOGIN',
      content_space: '4szkx38resvm',
      content_type: 'notification',
      content_id: '6zGhwB6kz6ii6IQ2Yg86wy',
      created_by: 's999999',
      updated_by: 's888888',
      pages: [ 'my-activity' ],
      status: 'draft',
      assignees: [ 's123456', 's1234567' ],
    };
    const mockResponse = {};

    // remove all permissions
    mockRes.locals.user.permissions = [];

    mockCampaignApiClient.updateCampaign.mockRejectedValueOnce(mockResponse);
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);
    await updateCampaign({}, { ...mockRes, locals: { ...mockRes.locals, validatedParams: mockParams, validatedBody: mockBody } }, mockNext);
    expect(mockCampaignApiClient.updateCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(ForbiddenError);
  });

  test('should return BadRequest error for updating campaign to draft with assignees', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockResponse = {};
    const mockBody = {
      'name': 'dpalma-may-37-3554',
      'start_at': '2019-04-29T04:00:00.000Z',
      'end_at': '2019-04-30T04:00:00.000Z',
      'platforms': [
        'ios',
        'android',
      ],
      'app_version': null,
      'content_space': '4szkx38resvm',
      'content_type': 'standingCampaign',
      'content_id': '0qjYVZSfhTiDAKpHwRU43',
      'container': 'my-activity',
      'status': 'draft',
      'external_ref': 'asdfc1234',
      'pages': [
        'activities',
      ],
      'urgent': false,
      'assignees': [ 's123456', 's1234567' ],
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: mockBody,
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    mockCampaignApiClient.updateCampaign.mockRejectedValueOnce(mockResponse);

    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);
    await updateCampaign({}, { ...mockRes, locals: { ...mockRes.locals, validatedParams: mockParams, validatedBody: mockBody } }, mockNext);

    expect(mockCampaignApiClient.updateCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
  });

  test('should return BadRequest for moving campaign forward to submitted status without assignees', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockResponse = {};
    const mockBody = {
      name: 'assignment-test',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'draft',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      assignees: [],
      type: 'campaign',
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: { status: 'submitted', application: 'nova', type: 'campaign' },
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    mockCampaignApiClient.updateCampaign.mockRejectedValueOnce(mockResponse);

    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    await updateCampaign({}, mockRes, mockNext);

    expect(mockCampaignApiClient.updateCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext.mock.calls[0][0].message).toBe('Cannot move campaign to submitted status without assigning to a user');
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
  });

  test('should return BadRequest for moving campaign forward to submitted status without assignees when assignees is empty array', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockResponse = {};
    const mockBody = {
      name: 'assignment-test',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'draft',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      assignees: [],
      type: 'campaign',
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: { status: 'submitted', application: 'nova', type: 'campaign', assignees: [] },
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    mockCampaignApiClient.updateCampaign.mockRejectedValueOnce(mockResponse);

    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    await updateCampaign({}, mockRes, mockNext);

    expect(mockCampaignApiClient.updateCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
    expect(mockNext.mock.calls[0][0].message).toBe('Cannot move campaign to submitted status without assigning to a user');
  });

  it('should return BadRequest if application is not found', async() => {
    const mockBody = {
      name: 'assignment-test',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'draft',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      assignees: [],
      type: 'campaign',
    };

    const mockApplicationsServiceRejectValue = {
      getApplicationByName: jest.fn().mockReturnValueOnce(undefined),
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsServiceRejectValue);

    await updateCampaign({}, mockRes, mockNext);

    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
    expect(mockNext.mock.calls[0][0].message).toBe('Invalid application');
  });

  it('should return BadRequest if cannot assign user while campaign is transition to draft by created_by', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: 'assignment-test',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'submitted',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      type: 'campaign',
      created_by: 's234',
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: { status: 'draft', application: 'nova', type: 'campaign', assignees: [ 's987' ] },
        user: {
          sid: 's987',
          permissions: [
            'campaigns_review',
          ],
        },
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    await updateCampaign({}, mockRes, mockNext);

    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
    expect(mockNext.mock.calls[0][0].message).toBe('Cannot assign users while campaign is in draft status');
  });

  it('should return BadRequest if cannot assign user while campaign is transition to draft by updated_by', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: 'assignment-test',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'submitted',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      type: 'campaign',
      created_by: 's999999',
      updated_by: 's0000000',
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: { status: 'draft', application: 'nova', type: 'campaign', assignees: [ 's987', 's999999' ] },
        user: {
          sid: 's987',
          permissions: [
            'campaigns_review',
          ],
        },
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    await updateCampaign({}, mockRes, mockNext);

    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
    expect(mockNext.mock.calls[0][0].message).toBe('Cannot assign users while campaign is in draft status');
  });

  it('should return BadRequest Cannot assign users to campaign while publishing.', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: 'assignment-test',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'reviewed',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      type: 'campaign',
      created_by: 's999999',
      updated_by: 's0000000',
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: { status: 'published', application: 'nova', type: 'campaign', assignees: [ 's987', 's999999' ] },
        user: {
          sid: 's987',
          permissions: [
            'admin',
          ],
        },
      },
    };

    // canUpdateCampaign.jest.fn().mockReturnValue(true);

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    await updateCampaign({}, mockRes, mockNext);

    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
    expect(mockNext.mock.calls[0][0].message).toBe('Cannot assign users to campaign while publishing.');
  });

  it('should return BadRequest Cannot assign users to campaign while publishing.', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: 'assignment-test',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'reviewed',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      type: 'campaign',
      created_by: 's999999',
      updated_by: 's0000000',
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: { status: 'published', application: 'nova', type: 'campaign', assignees: [ 's987', 's999999' ] },
        user: {
          sid: 's987',
          permissions: [
            'admin',
          ],
        },
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    await updateCampaign({}, mockRes, mockNext);

    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
    expect(mockNext.mock.calls[0][0].message).toBe('Cannot assign users to campaign while publishing.');
  });

  it('should return Cannot assign users while campaign is draft status.', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: 'assignment-test',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'draft',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      type: 'campaign',
      created_by: 's888888',
      updated_by: 's0000000',
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: { status: 'draft', application: 'nova', type: 'campaign', assignees: [ 's987', 's999999' ] },
        user: {
          sid: 's987',
          permissions: [
            'admin',
          ],
        },
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    await updateCampaign({}, mockRes, mockNext);

    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
    expect(mockNext.mock.calls[0][0].message).toBe('Cannot assign users while campaign is draft status.');
  });

  it('should return Invalid users detected when enabling campaign and unable to find user', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: 'assignment-test',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'published',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      type: 'campaign',
      created_by: 's888888',
      updated_by: 's0000000',
      disabled: true,
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: {
          name: 'assignment-test2',
          application: 'nova',
          type: 'campaign',
          assignees: [ 's987', 's999999' ],
          disabled: false,
        },
        user: {
          sid: 's987',
          permissions: [
            'admin',
          ],
        },
      },
    };

    const mockRuleServiceLocal = {
      validateUsers: jest.fn().mockReturnValue({ success: false }),
    };

    mockRuleServiceLocal.validateUsers.mockReturnValue({ success: false });
    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleServiceLocal, mockApplicationsService);

    await updateCampaign({}, mockRes, mockNext);

    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
    expect(mockNext.mock.calls[0][0].message).toContain('Invalid users detected');
  });

  it('should return Invalid users detected when enabling campaign and unable to find user CCAU', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: 'assignment-test',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'published',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      type: 'ccau_campaign',
      created_by: 's888888',
      updated_by: 's0000000',
      disabled: true,
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: {
          name: 'assignment-test2',
          application: 'nova',
          type: 'campaign',
          assignees: [ 's987', 's999999' ],
          disabled: false,
        },
        user: {
          sid: 's987',
          permissions: [
            'admin',
          ],
        },
      },
    };

    const mockRuleServiceLocal = {
      validateUsers: jest.fn().mockReturnValue({ success: false }),
    };

    mockRuleServiceLocal.validateUsers.mockReturnValue({ success: false });
    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleServiceLocal, mockApplicationsService);

    await updateCampaign({}, mockRes, mockNext);

    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
    expect(mockNext.mock.calls[0][0].message).toContain('Invalid users detected');
  });

  it('should return Error validating users for campaign assignment if validateUsers has failed', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: 'assignment-test',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'published',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      type: 'ccau_campaign',
      created_by: 's888888',
      updated_by: 's0000000',
      disabled: true,
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: {
          name: 'assignment-test2',
          application: 'nova',
          type: 'campaign',
          assignees: [ 's987', 's999999' ],
          disabled: false,
        },
        user: {
          sid: 's987',
          permissions: [
            'admin',
          ],
        },
      },
    };

    const mockRuleServiceLocal = {
      validateUsers: jest.fn().mockRejectedValueOnce({ }),
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleServiceLocal, mockApplicationsService);

    await updateCampaign({}, mockRes, mockNext);

    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(CustomError);
    expect(mockNext.mock.calls[0][0].message).toContain('Error validating users for campaign assignment');
  });

  // TODO: investigate the asynchoronous issue
  it.skip('should reassign users to campaign', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: 'reassign_users',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'draft',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      assignees: [ 's9871', 's1200' ],
      type: 'campaign',
      created_by: 's9871',
      updated_by: 's1200',
      disabled: true,
      id: '123456ab',
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: {
          name: 'reassign_users',
          status: 'submitted',
          application: 'nova',
          type: 'campaign',
          assignees: [ 's9871', 's1200' ],
          disabled: false,
        },
        user: {
          sid: 's987',
          permissions: [
            'admin',
          ],
        },
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    mockRuleService.validateUsers.mockReturnValueOnce({ success: true });
    mockCampaignApiClient.updateCampaign.mockResolvedValueOnce({ data: { ...mockBody, status: 'draft' } });

    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    await updateCampaign({}, mockRes, mockNext);

    // Add after updateCampaign call:
    console.log('validateUsers called:', mockRuleService.validateUsers.mock.calls.length);
    console.log('updateCampaign called:', mockCampaignApiClient.updateCampaign.mock.calls.length);
    console.log('assignUserListToCampaign called:', mockRuleService.assignUserListToCampaign.mock.calls.length);

    // Verification
    expect(mockRuleService.assignUserListToCampaign).toHaveBeenCalledWith(
      mockParams.ruleId,
      [ 's9871', 's1200' ],
    );
  });

  it('should return error if duplicate campaign', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: 'duplicate',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'published',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      type: 'campaign',
      created_by: 's888888',
      updated_by: 's0000000',
      disabled: true,
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: {
          application: 'nova',
          type: 'campaign',
          assignees: [ 's9871', 's1200' ],
          disabled: false,
        },
        user: {
          sid: 's987',
          permissions: [
            'admin',
          ],
        },
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    mockRuleService.validateUsers.mockReturnValueOnce({ success: true });

    const errorMessage = 'Campaign with this name already exists. Use a unique name for the new campaign.';

    mockCampaignApiClient.updateCampaign.mockRejectedValueOnce(
      {
        response: {
          data: {
            message: errorMessage,
          },
        },
      },
    );

    try {
      await updateCampaign({}, mockRes, mockNext);
    } catch (error) {
      if (error.response && error.response.data) {
        throw new BadRequestError(errorMessage);
      }
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
      expect(mockNext.mock.calls[0][0].message).toBe(errorMessage);
    }
  });

  it('should return error if Internal Server Error', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: 'internal_server_error',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'published',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      type: 'campaign',
      created_by: 's888888',
      updated_by: 's0000000',
      disabled: true,
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: {
          application: 'nova',
          type: 'campaign',
          assignees: [ 's9871', 's1200' ],
          disabled: false,
        },
        user: {
          sid: 's987',
          permissions: [
            'admin',
          ],
        },
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    mockRuleService.validateUsers.mockReturnValueOnce({ success: true });

    mockCampaignApiClient.updateCampaign.mockRejectedValueOnce(
      {
        response: {
          data: {
            message: 'Internal Server Error',
          },
        },
      },
    );

    await updateCampaign({}, mockRes, mockNext);

    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(CustomError);
    expect(mockNext.mock.calls[0][0].message).toBe('Internal Server Error');
  });

  it('should return error if failed with data object', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: 'internal_server_error',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'published',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      type: 'campaign',
      created_by: 's888888',
      updated_by: 's0000000',
      disabled: true,
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: {
          application: 'nova',
          type: 'campaign',
          assignees: [ 's9871', 's1200' ],
          disabled: false,
        },
        user: {
          sid: 's987',
          permissions: [
            'admin',
          ],
        },
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    mockRuleService.validateUsers.mockReturnValueOnce({ success: true });

    mockCampaignApiClient.updateCampaign.mockRejectedValueOnce(// eslint-disable-next-line prefer-promise-reject-errors
      {
        response: {
          data: {
            error: 'failed to update',
          },
        },
      },
    );

    try {
      await updateCampaign({}, mockRes, mockNext);
    } catch (error) {
      if (error.response && error.response.data) {
        throw new CustomError(JSON.stringify(error.response.data));
      }
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(CustomError);
      expect(mockNext.mock.calls[0][0].message).toBe(JSON.stringify({
        error: 'failed to update',
      }));
    }
  });

  it('should return error if request fails', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: 'request_failed',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'published',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      type: 'campaign',
      created_by: 's888888',
      updated_by: 's0000000',
      disabled: true,
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: {
          application: 'nova',
          type: 'campaign',
          assignees: [ 's9871', 's1200' ],
          disabled: false,
        },
        user: {
          sid: 's987',
          permissions: [
            'admin',
          ],
        },
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    mockRuleService.validateUsers.mockReturnValueOnce({ success: true });

    mockCampaignApiClient.updateCampaign.mockRejectedValueOnce(// eslint-disable-next-line prefer-promise-reject-errors
      {
        response: {
          status: 500,
          data: { message: 'request failed' },
        },
      },
    );

    try {
      await updateCampaign({}, mockRes, mockNext);
    } catch (error) {
      if (error.response && error.response.data) {
        throw new CustomError(error.response.data);
      }
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(CustomError);
      // eslint-disable-next-line no-useless-escape
      expect(mockNext.mock.calls[0][0].message).toBe(JSON.stringify({
        message: 'request failed',
      }));
    }
  });

  it('should return error if failed with custom message', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: 'custom_message',
      start_at: '2020-08-19T00:00:00.000Z',
      end_at: '2020-08-29T00:00:00.000Z',
      platforms: [
        'ios',
        'android',
      ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'standingCampaign',
      content_id: '0qjYVZSfhTiDAKpHwRU43',
      container: 'my-activity',
      status: 'published',
      external_ref: 'asdfc1234',
      pages: [
        'activities',
      ],
      urgent: false,
      type: 'campaign',
      created_by: 's888888',
      updated_by: 's0000000',
      disabled: true,
    };
    mockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: {
          application: 'nova',
          type: 'campaign',
          assignees: [ 's9871', 's1200' ],
          disabled: false,
        },
        user: {
          sid: 's987',
          permissions: [
            'admin',
          ],
        },
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockBody });
    const updateCampaign = UpdateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockApplicationsService);

    mockRuleService.validateUsers.mockReturnValueOnce({ success: true });

    mockCampaignApiClient.updateCampaign.mockRejectedValue({
      message: 'request failed',
    });

    await updateCampaign({}, mockRes, mockNext);

    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(CustomError);
    // eslint-disable-next-line no-useless-escape
    expect(mockNext.mock.calls[0][0].message).toBe(JSON.stringify('request failed'));
  });
});
