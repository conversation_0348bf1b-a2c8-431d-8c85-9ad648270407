const Route = require('./setupCampaign');
const { CustomError } = require('../../error');

const mockService = {
  getCampaignSetupInfo: jest.fn().mockResolvedValue({
    businessLines: [
      { id: 5, description: 'Scotia iTRADE', code: 'SDBI:SL' },
    ],
    products: [
      { id: 1, sub_category_id: 1, description: 'DDA DDA', code: 'Banking:DDA:DDA:DDA:', sub_category_description: 'Banking' },
    ],
    device: [
      { id: 2, description: 'Android Phone', code: 'Android' },
    ],
    borrowingProducts: {},
  }),
};

describe('Setup campaign endpoint', () => {
  it('should initialize endpoint correctly', () => {
    const handler = Route(mockService);
    expect(typeof handler).toStrictEqual('function');
  });
  it('should call the endpoint handler correctly', async() => {
    const handler = Route(mockService);
    const mockRes = {
      status: jest.fn().mockReturnValue({
        json: jest.fn(),
      }),
    };
    const mockNext = jest.fn();
    await handler(null, mockRes, mockNext);
    expect(mockService.getCampaignSetupInfo).toHaveBeenCalled();
    expect(mockRes.status).toHaveBeenCalledWith(200);
  });

  it('should return error if products not found', async() => {
    const mockServiceError = {
      getCampaignSetupInfo: jest.fn().mockRejectedValue({
        response: {
          status: 404,
          data: { message: 'Not found' },
        },
      }),
    };

    const handler = Route(mockServiceError);
    const mockRes = {
      status: jest.fn().mockReturnValue({
        json: jest.fn(),
      }),
    };
    const mockNext = jest.fn();
    await handler(null, mockRes, mockNext);
    expect(mockServiceError.getCampaignSetupInfo).toHaveBeenCalled();
    expect(mockRes.status).toHaveBeenCalledWith(404);
    expect(mockRes.status().json).toHaveBeenCalledWith({ message: 'Not found' });
  });

  it('should return error if Internal server error', async() => {
    const mockInternalServerError = {
      getCampaignSetupInfo: jest.fn().mockRejectedValue({
        response: {
          status: 500,
          data: { message: 'Internal server error' },
        },
      }),
    };

    const handler = Route(mockInternalServerError);
    const mockRes = {
      status: jest.fn().mockReturnValue({
        json: jest.fn(),
      }),
    };
    const mockNext = jest.fn();
    await handler(null, mockRes, mockNext);
    expect(mockInternalServerError.getCampaignSetupInfo).toHaveBeenCalled();
    expect(mockNext).toHaveBeenCalledWith(expect.any(CustomError));
  });
});
