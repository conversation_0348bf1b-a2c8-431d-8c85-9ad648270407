const { CustomError, ForbiddenError, BadRequestError } = require('../../error');
const { canUpdateCampaign } = require('../../permissions/workflow');

const {
  submittedToDraft,
  reviewedToDraft,
  reviewedToPublished,
  draftToSubmitted,
  submittedToReviewed,
} = require('../../permissions/workflow/transactions');

const CampaignConstants = require('./constants');

const {
  CAMPAIGNS_MANAGE,
  CAMPAIGNS_APPROVE,
  CAMPAIGNS_REVIEW,
  CCAU_CAMPAIGNS_MANAGE,
  CCAU_CAMPAIGNS_APPROVE,
  CCAU_CAMPAIGNS_REVIEW,
} = require('../../permissions');

const permissionStatusMap = {
  'draft': [ CAMPAIGNS_MANAGE ],
  'submitted': [ CAMPAIGNS_REVIEW ],
  'reviewed': [ CAMPAIGNS_APPROVE ],
  'published': [ CAMPAIGNS_MANAGE ],
};

const permissionStatusMapCCAU = {
  'draft': [ CCAU_CAMPAIGNS_MANAGE ],
  'submitted': [ CCAU_CAMPAIGNS_REVIEW ],
  'reviewed': [ CCAU_CAMPAIGNS_APPROVE ],
  'published': [ CCAU_CAMPAIGNS_MANAGE ],
};

const updateCampaign = (logger, campaignService, ruleService, applicationService) => async(req, res, next) => {
  const params = res.locals.validatedParams;
  const body = res.locals.validatedBody;

  const campaignServiceResponse = await campaignService.getCampaign(params.ruleId);
  const campaignBefore = campaignServiceResponse.data;
  const allow = canUpdateCampaign(res.locals.user, campaignBefore, body);

  if (!allow) {
    return next(new ForbiddenError());
  }

  const application = await applicationService.getApplicationByName(body.application);
  if (!application) {
    return next(new BadRequestError('Invalid application'));
  }
  if (!application.ruleTypes.includes(body.type)) {
    return next(new BadRequestError('Invalid combination of application & rule type'));
  }

  const sid = res.locals.user && res.locals.user.sid;
  body.updated_by = sid;

  const transition = transitionFunction => transitionFunction(campaignBefore, body);

  try {
    // verify user assignee list for campaign
    try {
      if (
        (transition(submittedToDraft) || transition(reviewedToDraft)) &&
        (body.assignees && (!body.assignees.includes(campaignBefore.created_by) || !body.assignees.includes(campaignBefore.updated_by)))) {
        return next(new BadRequestError(`Cannot assign users while campaign is in draft status`));
      } else if (transition(reviewedToPublished) && body.assignees) {
        // no assignees should be included in the request
        return next(new BadRequestError('Cannot assign users to campaign while publishing.'));
      // if no assignees when transitioning forward to submitted or reviewed states
      } else if (
        (!body.assignees || body.assignees.length === 0) &&
        (transition(draftToSubmitted) || transition(submittedToReviewed))
      ) {
        return next(new BadRequestError(`Cannot move campaign to ${body.status} status without assigning to a user`));
      // no assignees
      } else if ((body.status === campaignBefore.status) &&
        (body.status === CampaignConstants.CAMPAIGN_STATUS_DRAFT) &&
        (body.assignees && body.assignees.length > 0) &&
        (!(body.assignees.includes(campaignBefore.created_by) || body.assignees.includes(campaignBefore.updated_by)))) {
        return next(new BadRequestError(`Cannot assign users while campaign is draft status.`));
      } else {
        if (body.assignees && body.assignees.length > 0) {
          const requiredPermissions = campaignBefore.type === CampaignConstants.CCAU_CAMPAIGN_RULE_TYPE ? permissionStatusMapCCAU[body.status] : permissionStatusMap[body.status];
          const result = await ruleService.validateUsers(body.assignees, requiredPermissions);
          if (result.success === false) {
            return next(new BadRequestError(`Invalid users detected: \n\n${JSON.stringify(result.error)}`));
          }
        }
      }
    } catch (err) {
      logger.error({ message: err.messsage, err: { code: err.code, stack: err.stack } });
      next(new CustomError(500, 'Error validating users for campaign assignment.'));
      return;
    }

    const campaign = await campaignService.updateCampaign(params.ruleId, body);
    // possibly save this result for error recovery
    let assignmentResult = [];
    if (body.status === CampaignConstants.CAMPAIGN_STATUS_DRAFT) {
      await ruleService.removeUsersFromCampaign(campaignBefore.id);
      assignmentResult = await ruleService.assignUserToCampaign(campaignBefore.created_by, campaignBefore.id);
      // this is submitted
    } else if (body.assignees) {
      await ruleService.removeUsersFromCampaign(campaignBefore.id);
      assignmentResult = await ruleService.assignUserListToCampaign(body.assignees, campaignBefore.id);
    }

    res.status(200).json(Object.assign({ assignees: assignmentResult }, campaign.data));
  } catch (err) {
    logger.error({ message: err.messsage, err: { code: err.code, stack: err.stack } });

    if (err.response) { // the request was made and a response was received
      let errorMessage;
      if (err.response.data && err.response.data.message) {
        if (err.response.data.message.includes('duplicate')) {
          errorMessage = 'Campaign with this name already exists. Use a unique name for the new campaign.';
          return next(new BadRequestError(errorMessage));
        } else {
          errorMessage = err.response.data.message;
        }
      } else {
        errorMessage = JSON.stringify(err.response.data);
      }
      return next(new CustomError(err.response.status, errorMessage));
    } else if (err.request) { // the request was made and there is no response
      return next(new CustomError(500, JSON.stringify(err.request)));
    } else {
      const clientMessage = err && err.errorMessage;
      return next(new CustomError(500, JSON.stringify(err.message), clientMessage));
    }
  }
};

module.exports = updateCampaign;
