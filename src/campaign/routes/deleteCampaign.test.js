const DeleteCampaign = require('./deleteCampaign');
const permissionsServiceMock = require('../../__mocks__/permissionService');

// mock Campaign API client
const mockCampaignApiClient = {
  deleteCampaign: jest.fn().mockResolvedValue({ id: '123456ab' }),
};
// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();
mockStatus.mockReturnValue({ json: mockJson });
const mockRes = {
  status: mockStatus,
  locals: {
    validatedParams: {},
    auth: { sid: 's1234567' },
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
    },
  },
};

describe('Campaign Rules: DELETE /campaign-rules/:ruleId', () => {
  beforeEach(() => {
    mockCampaignApiClient.deleteCampaign.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });

  test('should return an error on error response from campaign api client', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockResponse = {
      response: {
        status: 400,
        data: {
          code: 'HTTP_BAD_REQUEST',
          message: 'Database error',
          uuid: '04d69d4d-4c41-4373-b7f5-9b351a1f345d',
          timestamp: '2018-08-28T20:43:59.319Z',
          metadata: [ 'The duplicate key value is (sample name #38).' ],
        },
      },
    };
    mockCampaignApiClient.deleteCampaign.mockRejectedValueOnce(mockResponse);
    const deleteCampaign = DeleteCampaign(mockCampaignApiClient, permissionsServiceMock);
    await deleteCampaign({ params: mockParams }, mockRes, mockNext);
    expect(mockCampaignApiClient.deleteCampaign).toBeCalled();
    expect(mockStatus).toBeCalled();
    expect(mockStatus).toBeCalledWith(mockResponse.response.status);
    expect(mockJson).toBeCalled();
    expect(mockJson).toBeCalledWith(mockResponse.response.data);
    expect(mockNext).not.toBeCalled();
  });

  test('should return error 500 when no response field is missing from service call', async() => {
    const mockParams = { ruleId: '123456ab' };
    mockCampaignApiClient.deleteCampaign.mockRejectedValueOnce({});
    const deleteCampaign = DeleteCampaign(mockCampaignApiClient, permissionsServiceMock);
    await deleteCampaign({ params: mockParams }, mockRes, mockNext);
    expect(mockCampaignApiClient.deleteCampaign).toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
  });

  test('should return an error on unknown error response from campaign api client', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockResponse = {
      response: {
        status: 501,
        data: {
          code: 'HTTP_NOT_IMPLEMENTED_ERROR',
          message: 'Not implemented',
          uuid: '04d69d4d-4c41-4373-b7f5-9b351a1f345d',
          timestamp: '2018-08-28T20:43:59.319Z',
          metadata: [],
        },
      },
    };
    mockCampaignApiClient.deleteCampaign.mockRejectedValueOnce(mockResponse);
    const deleteCampaign = DeleteCampaign(mockCampaignApiClient, permissionsServiceMock);
    await deleteCampaign({ params: mockParams }, mockRes, mockNext);
    expect(mockCampaignApiClient.deleteCampaign).toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0] instanceof Error).toEqual(true);
    expect(mockNext.mock.calls[0][0].message).toEqual(JSON.stringify(mockResponse.response.data));
  });

  test('should not return an error on valid request', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      testkey: 'testval',
    };
    const mockResponse = {
      data: { id: mockParams.ruleId, ...mockBody },
    };
    mockCampaignApiClient.deleteCampaign.mockResolvedValueOnce(mockResponse);
    const deleteCampaign = DeleteCampaign(mockCampaignApiClient, permissionsServiceMock);
    await deleteCampaign({ params: mockParams }, mockRes, mockNext);
    expect(mockCampaignApiClient.deleteCampaign).toBeCalled();
    expect(mockStatus).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockJson).toBeCalledWith(mockResponse.data);
    expect(mockNext).not.toBeCalled();
  });
});
