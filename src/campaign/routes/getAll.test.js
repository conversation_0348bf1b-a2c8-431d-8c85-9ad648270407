const getAll = require('./getAll');
const { CustomError } = require('../../error');

describe('GET /api/v1/campaign-rules - getAll middleware', () => {
  let mockCampaignService;
  let reqMock;
  let resMock;
  let nextMock;

  beforeEach(() => {
    mockCampaignService = {
      getAllCampaignsByAccess: jest.fn().mockResolvedValue({
        data: {
          items: [ { id: 1 }, { id: 2 }, { id: 3 } ],
          total: 3,
        },
      }),
    };

    reqMock = { query: {} };

    resMock = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      locals: {
        validatedQuery: { offset: 0, limit: 2 },
        user: {
          id: 1,
          role_id: 1,
          permissions: [ 'admin' ],
          access: {
            campaigns: {
              containers: {
                nova: { view: [ 'c1' ], manage: [ 'c2' ] },
                sol: { view: [ 'c3' ] }, // excluded
              },
              pages: {
                nova: { view: [ 'p1' ], manage: [ 'p2' ] },
                storefront: { view: [ 'p3' ] }, // excluded
              },
              ruleSubTypes: {
                nova: { view: [ 'type1' ], manage: [ 'type2' ] },
                sol: { view: [ 'type3' ] }, // excluded
              },
            },
          },
        },
      },
    };

    nextMock = jest.fn();
  });

  test('should return 200 and filter access correctly', async() => {
    const handler = getAll(mockCampaignService);
    await handler(reqMock, resMock, nextMock);

    const accessQuery = mockCampaignService.getAllCampaignsByAccess.mock.calls[0][1];

    expect(accessQuery).toEqual({
      accessContainers: { nova: [ 'c1', 'c2' ] },
      accessPages: { nova: [ 'p1', 'p2' ] },
      accessRuleSubTypes: { nova: [ 'TYPE1', 'TYPE2' ] },
    });

    expect(resMock.status).toHaveBeenCalledWith(200);
    expect(resMock.json).toHaveBeenCalledWith({
      items: [ { id: 1 }, { id: 2 } ],
      total: 3,
      limit: 2,
      offset: 0,
    });
  });

  test('should return empty list if no access', async() => {
    resMock.locals.user.access.campaigns = {
      containers: {},
      pages: {},
      ruleSubTypes: {},
    };

    const handler = getAll(mockCampaignService);
    await handler(reqMock, resMock, nextMock);

    expect(mockCampaignService.getAllCampaignsByAccess).not.toHaveBeenCalled();
    expect(resMock.status).toHaveBeenCalledWith(200);
    expect(resMock.json).toHaveBeenCalledWith({ items: [], total: 0 });
  });

  test('should return upper-case ruleSubTypes.manage values', async() => {
    resMock.locals.validatedQuery = { offset: 0, limit: 10 };
    resMock.locals.user.access.campaigns.ruleSubTypes = {
      nova: { view: [], manage: [ 'campaign', 'mass' ] },
    };

    mockCampaignService.getAllCampaignsByAccess.mockResolvedValue({
      data: {
        items: [ { id: 1 }, { id: 2 } ],
        total: 2,
      },
    });

    const handler = getAll(mockCampaignService);
    await handler(reqMock, resMock, nextMock);

    expect(mockCampaignService.getAllCampaignsByAccess).toHaveBeenCalledWith(
      { offset: 0, limit: 10 },
      expect.objectContaining({
        accessRuleSubTypes: { nova: [ 'CAMPAIGN', 'MASS' ] },
      }),
    );

    expect(resMock.status).toHaveBeenCalledWith(200);
    expect(resMock.json).toHaveBeenCalledWith({
      items: [ { id: 1 }, { id: 2 } ],
      total: 2,
      offset: 0,
      limit: 10,
    });
  });

  test('should handle missing view/manage in access objects gracefully', async() => {
    resMock.locals.user.access.campaigns = {
      containers: {
        nova: {}, // no view/manage
      },
      pages: {
        nova: { manage: [ 'page1' ] }, // only manage
      },
      ruleSubTypes: {
        nova: { view: [ 'type1' ] }, // only view
      },
    };

    const handler = getAll(mockCampaignService);
    await handler(reqMock, resMock, nextMock);

    const accessQuery = mockCampaignService.getAllCampaignsByAccess.mock.calls[0][1];

    expect(accessQuery.accessContainers).toEqual({ nova: [] });
    expect(accessQuery.accessPages).toEqual({ nova: [ 'page1' ] });
    expect(accessQuery.accessRuleSubTypes).toEqual({ nova: [ 'TYPE1' ] });
  });

  test('should return empty list if only accessPages is empty', async() => {
    resMock.locals.user.access.campaigns = {
      containers: {
        nova: { view: [ 'c1' ] },
      },
      pages: {}, // empty
      ruleSubTypes: {
        nova: { view: [ 'type1' ] },
      },
    };

    const handler = getAll(mockCampaignService);
    await handler(reqMock, resMock, nextMock);

    expect(mockCampaignService.getAllCampaignsByAccess).not.toHaveBeenCalled();
    expect(resMock.status).toHaveBeenCalledWith(200);
    expect(resMock.json).toHaveBeenCalledWith({ items: [], total: 0 });
  });

  test('should handle unknown error', async() => {
    mockCampaignService.getAllCampaignsByAccess.mockRejectedValue(new Error('unknown'));

    const handler = getAll(mockCampaignService);
    await handler(reqMock, resMock, nextMock);

    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0]).toBeInstanceOf(CustomError);
  });

  test('should handle 401 error response', async() => {
    mockCampaignService.getAllCampaignsByAccess.mockRejectedValue({
      response: { status: 401, data: { message: 'Unauthorized' } },
    });

    const handler = getAll(mockCampaignService);
    await handler(reqMock, resMock, nextMock);

    expect(resMock.status).toHaveBeenCalledWith(401);
    expect(resMock.json).toHaveBeenCalledWith({ message: 'Unauthorized' });
    expect(nextMock).not.toHaveBeenCalled();
  });

  test('should handle 500 error response', async() => {
    mockCampaignService.getAllCampaignsByAccess.mockRejectedValue({
      response: { status: 500, data: { error: 'Server error' } },
    });

    const handler = getAll(mockCampaignService);
    await handler(reqMock, resMock, nextMock);

    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0]).toBeInstanceOf(CustomError);
  });

  test('should handle error with no response object', async() => {
    const error = new Error('Network failure');

    mockCampaignService.getAllCampaignsByAccess.mockRejectedValue(error);

    const handler = getAll(mockCampaignService);
    await handler(reqMock, resMock, nextMock);

    expect(nextMock).toHaveBeenCalledWith(
      expect.any(CustomError),
    );
  });
});
