// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`getProductBook get products api response matches snapshot 1`] = `
[
  {
    "category": "borrowing",
    "code": "LC",
    "description": " Scotia RSP Catch-Up LOC",
    "id": " 660170 ",
    "ownership": "R",
    "sub_code": "LRL",
  },
  {
    "category": "banking",
    "code": "DDA",
    "description": "DDA",
    "id": "100020",
    "ownership": "B",
  },
  {
    "category": "banking",
    "code": "DDA",
    "description": "DDA",
    "id": "100031",
    "ownership": "R",
  },
  {
    "category": "banking",
    "code": "DDA",
    "description": "Current Account",
    "id": "100060",
    "ownership": "B",
    "sub_code": "CA",
  },
  {
    "category": "banking",
    "code": "DDA",
    "description": "Current Account",
    "id": "100071",
    "ownership": "R",
    "sub_code": "CA",
  },
  {
    "category": "banking",
    "code": "DDA",
    "description": "Scotia Chequing",
    "id": "100100",
    "ownership": "B",
    "sub_code": "SC",
  },
  {
    "category": "banking",
    "code": "DDA",
    "description": "Scotia Chequing",
    "id": "100111",
    "ownership": "R",
    "sub_code": "SC",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Savings/Chequing",
    "id": "200010",
    "ownership": "R",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Savings/Chequing",
    "id": "200020",
    "ownership": "B",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "AgriInvest Account",
    "id": "200050",
    "ownership": "R",
    "sub_code": "AG",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "AgriInvest Account",
    "id": "200060",
    "ownership": "B",
    "sub_code": "AG",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Basic",
    "id": "200070",
    "ownership": "R",
    "sub_code": "BB",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Basic",
    "id": "200080",
    "ownership": "B",
    "sub_code": "BB",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Blue Chip",
    "id": "200090",
    "ownership": "R",
    "sub_code": "BC",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Blue Chip",
    "id": "200100",
    "ownership": "B",
    "sub_code": "BC",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Money Master",
    "id": "200110",
    "ownership": "R",
    "sub_code": "BM",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Money Master",
    "id": "200120",
    "ownership": "B",
    "sub_code": "BM",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Canadian Agricultural Income Stabilization Program",
    "id": "200130",
    "ownership": "R",
    "sub_code": "CS",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Canadian Agricultural Income Stabilization Program",
    "id": "200140",
    "ownership": "B",
    "sub_code": "CS",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Daily Interest Savings",
    "id": "200150",
    "ownership": "R",
    "sub_code": "DI",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Daily Interest Savings",
    "id": "200160",
    "ownership": "B",
    "sub_code": "DI",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Scotia One Service",
    "id": "200170",
    "ownership": "R",
    "sub_code": "EM",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Scotia One Service",
    "id": "200180",
    "ownership": "B",
    "sub_code": "EM",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Gain Plan",
    "id": "200190",
    "ownership": "R",
    "sub_code": "GP",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Gain Plan",
    "id": "200200",
    "ownership": "B",
    "sub_code": "GP",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "DIS Getting There",
    "id": "200210",
    "ownership": "R",
    "sub_code": "GT",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "DIS Getting There",
    "id": "200220",
    "ownership": "B",
    "sub_code": "GT",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Scotia Power Savings",
    "id": "200230",
    "ownership": "R",
    "sub_code": "HI",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Scotia Power Savings",
    "id": "200240",
    "ownership": "B",
    "sub_code": "HI",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Money Master for business",
    "id": "200250",
    "ownership": "R",
    "sub_code": "MB",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Money Master for business",
    "id": "200260",
    "ownership": "B",
    "sub_code": "MB",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Momentum Savings",
    "id": "200270",
    "ownership": "R",
    "sub_code": "MS",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "AgriInvest Account",
    "id": "200390",
    "ownership": "R",
    "sub_code": "NI",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "AgriInvest Account",
    "id": "200400",
    "ownership": "B",
    "sub_code": "NI",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Private Banking",
    "id": "200410",
    "ownership": "R",
    "sub_code": "PB",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Private Banking",
    "id": "200420",
    "ownership": "B",
    "sub_code": "PB",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Powerchequing",
    "id": "200430",
    "ownership": "R",
    "sub_code": "PC",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Powerchequing",
    "id": "200440",
    "ownership": "B",
    "sub_code": "PC",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Preferred Package",
    "id": "200470",
    "ownership": "R",
    "sub_code": "SO",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Preferred Package",
    "id": "200480",
    "ownership": "B",
    "sub_code": "SO",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Ultimate Package",
    "id": "200490",
    "ownership": "R",
    "sub_code": "AU",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "USD Savings",
    "id": "200491",
    "ownership": "R",
    "sub_code": "UD",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Ultimate Package",
    "id": "200500",
    "ownership": "B",
    "sub_code": "AU",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "USD Savings",
    "id": "200501",
    "ownership": "B",
    "sub_code": "UD",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Scotia Value Acct",
    "id": "200530",
    "ownership": "R",
    "sub_code": "VL",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Scotia Value Acct",
    "id": "200540",
    "ownership": "B",
    "sub_code": "VL",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Scotiabank Momentum Chequing",
    "id": "200550",
    "ownership": "R",
    "sub_code": "MA",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Scotiabank Momentum Chequing",
    "id": "200560",
    "ownership": "B",
    "sub_code": "MA",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Basic Plus",
    "id": "200570",
    "ownership": "R",
    "sub_code": "BP",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Basic Plus",
    "id": "200580",
    "ownership": "B",
    "sub_code": "BP",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Student Banking Advantage Plan",
    "id": "200590",
    "ownership": "R",
    "sub_code": "BA",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Student Banking Advantage Plan",
    "id": "200600",
    "ownership": "B",
    "sub_code": "BA",
  },
  {
    "category": "banking",
    "code": "BSA",
    "description": "Momentum PLUS Savings",
    "id": "200610",
    "ownership": "R",
    "sub_code": "MP",
  },
  {
    "category": "banking",
    "code": "BSA",
    "description": "Momentum PLUS Savings",
    "id": "200630",
    "ownership": "R",
    "sub_code": "P1",
  },
  {
    "category": "banking",
    "code": "BSA",
    "description": "90 Day Premium Period",
    "id": "200650",
    "ownership": "R",
    "sub_code": "P2",
  },
  {
    "category": "banking",
    "code": "BSA",
    "description": "180 Day Premium Period",
    "id": "200670",
    "ownership": "R",
    "sub_code": "P3",
  },
  {
    "category": "banking",
    "code": "BSA",
    "description": "270 Day Premium Period",
    "id": "200690",
    "ownership": "R",
    "sub_code": "P4",
  },
  {
    "category": "banking",
    "code": "BSA",
    "description": "360 Day Premium Period",
    "id": "200710",
    "ownership": "R",
    "sub_code": "P5",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Euro Savings Account",
    "id": "200810",
    "ownership": "R",
    "sub_code": "EU",
  },
  {
    "category": "banking",
    "code": "SAV",
    "description": "Euro Savings Account",
    "id": "200820",
    "ownership": "B",
    "sub_code": "EU",
  },
  {
    "category": "banking",
    "code": "VPP",
    "description": "Scotiabank Prepaid Visa Card(s)",
    "id": "250010",
    "ownership": "R",
  },
  {
    "category": "banking",
    "code": "VPP",
    "description": "Scotiabank Prepaid Visa Card(s)",
    "id": "250020",
    "ownership": "B",
  },
  {
    "category": "banking",
    "code": "VPP",
    "description": "Scotiabank Prepaid Visa Card",
    "id": "250030",
    "ownership": "R",
    "sub_code": "RG",
  },
  {
    "category": "banking",
    "code": "VPP",
    "description": "Scotiabank Prepaid Visa Card",
    "id": "250040",
    "ownership": "B",
    "sub_code": "RG",
  },
  {
    "category": "banking",
    "code": "VPP",
    "description": "Scotiabank SCENE Prepaid Visa Card",
    "id": "250050",
    "ownership": "R",
    "sub_code": "SC",
  },
  {
    "category": "banking",
    "code": "VPP",
    "description": "Scotiabank SCENE Prepaid Visa Card",
    "id": "250060",
    "ownership": "B",
    "sub_code": "SC",
  },
  {
    "category": "banking",
    "code": "VPP",
    "description": "Scotiabank USD Prepaid Travel Visa Card",
    "id": "250090",
    "ownership": "R",
    "sub_code": "TC",
  },
  {
    "category": "banking",
    "code": "VPP",
    "description": "Scotiabank USD Prepaid Travel Visa Card",
    "id": "250100",
    "ownership": "B",
    "sub_code": "TC",
  },
  {
    "category": "investing",
    "code": "IPP",
    "description": "Investment Account",
    "id": "340010",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "IPP",
    "description": "Investment Account",
    "id": "340020",
    "ownership": "B",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS & SSI",
    "id": "340030",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS & SSI",
    "id": "340040",
    "ownership": "B",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS",
    "id": "340050",
    "ownership": "R",
    "sub_code": "NP",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS",
    "id": "340060",
    "ownership": "B",
    "sub_code": "NP",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS",
    "id": "340080",
    "ownership": "B",
    "sub_code": "WD",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS",
    "id": "340100",
    "ownership": "B",
    "sub_code": "WT",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS",
    "id": "340120",
    "ownership": "B",
    "sub_code": "ST",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS",
    "id": "340140",
    "ownership": "B",
    "sub_code": "SN",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS",
    "id": "340160",
    "ownership": "B",
    "sub_code": "FT",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS",
    "id": "340180",
    "ownership": "B",
    "sub_code": "HT",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS",
    "id": "340190",
    "ownership": "R",
    "sub_code": "CP",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS",
    "id": "340220",
    "ownership": "B",
    "sub_code": "CN",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS",
    "id": "340240",
    "ownership": "B",
    "sub_code": "CD",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS",
    "id": "340260",
    "ownership": "B",
    "sub_code": "CT",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS",
    "id": "340280",
    "ownership": "B",
    "sub_code": "EP",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - BNS",
    "id": "340300",
    "ownership": "B",
    "sub_code": "EN",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - SSI",
    "id": "340310",
    "ownership": "R",
    "sub_code": "PR",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - SSI",
    "id": "340340",
    "ownership": "B",
    "sub_code": "BU",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - SSI",
    "id": "340360",
    "ownership": "B",
    "sub_code": "TP",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - SSI",
    "id": "340380",
    "ownership": "B",
    "sub_code": "TN",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - SSI",
    "id": "340400",
    "ownership": "B",
    "sub_code": "PE",
  },
  {
    "category": "investing",
    "code": "NRS",
    "description": "Non-Reg Savings - SSI",
    "id": "340420",
    "ownership": "B",
    "sub_code": "NE",
  },
  {
    "category": "investing",
    "code": "LIE",
    "description": "Registered Income - BNS",
    "id": "340430",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "LIE",
    "description": "Registered Income - SSI",
    "id": "340450",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "LIR",
    "description": "Registered Savings - BNS",
    "id": "340470",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "LIR",
    "description": "Registered Savings - SSI",
    "id": "340490",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "LIS",
    "description": "Registered Savings - BNS",
    "id": "340510",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "LIS",
    "description": "Registered Savings - SSI",
    "id": "340530",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "LRI",
    "description": "Registered Income - BNS",
    "id": "340550",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "LRI",
    "description": "Registered Income - SSI",
    "id": "340570",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "PRI",
    "description": "Registered Income - SSI",
    "id": "340590",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "RIS",
    "description": "Registered Income - BNS",
    "id": "340610",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "RIS",
    "description": "Registered Income - SSI",
    "id": "340630",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "RRI",
    "description": "Registered Income - BNS",
    "id": "340650",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "RRI",
    "description": "Registered Income - SSI",
    "id": "340670",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "RRS",
    "description": "Registered Savings - BNS",
    "id": "340690",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "RRS",
    "description": "Registered Savings - BNS",
    "id": "340700",
    "ownership": "B",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "RRS",
    "description": "Registered Savings - SSI",
    "id": "340710",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "RRS",
    "description": "Registered Savings - SSI",
    "id": "340720",
    "ownership": "B",
  },
  {
    "category": "investing",
    "code": "RSS",
    "description": "Registered Savings - BNS",
    "id": "340730",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "RSS",
    "description": "Registered Savings - SSI",
    "id": "340750",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "RSL",
    "description": "Restricted Locked In Savings Plan - BNS",
    "id": "340770",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "RSL",
    "description": "Restricted Locked In Savings Plan - SSI",
    "id": "340790",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "RLI",
    "description": "Restricted Life Income Plan - BNS",
    "id": "340810",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "RLI",
    "description": "Restricted Life Income Plan - SSI",
    "id": "340830",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "TFS",
    "description": "Tax-Free Savings - BNS",
    "id": "340850",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "TFS",
    "description": "Tax-Free Savings - BNS",
    "id": "340860",
    "ownership": "B",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "TFS",
    "description": "Tax-Free Savings - SSI",
    "id": "340870",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "TFS",
    "description": "Tax-Free Savings - SSI",
    "id": "340880",
    "ownership": "B",
  },
  {
    "category": "investing",
    "code": "RDS",
    "description": "Reg. Disability Sav Plan - BNS",
    "id": "340890",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "RDS",
    "description": "Reg. Disability Sav Plan - SSI",
    "id": "340910",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "REI",
    "description": "RESP Individual - BNS",
    "id": "340930",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "REI",
    "description": "RESP Individual - BNS",
    "id": "340940",
    "ownership": "B",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "REI",
    "description": "RESP Individual - SSI",
    "id": "340950",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "REI",
    "description": "RESP Individual - SSI",
    "id": "340960",
    "ownership": "B",
  },
  {
    "category": "investing",
    "code": "REF",
    "description": "RESP Family - BNS",
    "id": "340970",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "REF",
    "description": "RESP Family - BNS",
    "id": "340980",
    "ownership": "B",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "REF",
    "description": "RESP Family - SSI",
    "id": "340990",
    "ownership": "R",
  },
  {
    "category": "investing",
    "code": "REF",
    "description": "RESP Family - SSI",
    "id": "341000",
    "ownership": "B",
  },
  {
    "category": "investing",
    "code": "PRI",
    "description": "Registered Income - BNS",
    "id": "341010",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "FHS",
    "description": "First Home Savings Account - BNS",
    "id": "341030",
    "ownership": "R",
    "sub_code": "SB",
  },
  {
    "category": "investing",
    "code": "FHS",
    "description": "First Home Savings Account - SSI",
    "id": "341050",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "MOR",
    "description": "Scotia Mortgage",
    "id": "400010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "MOR",
    "description": "Scotia Mortgage",
    "id": "400020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "MOR",
    "description": "Scotia Mortgage",
    "id": "400030",
    "ownership": "R",
    "sub_code": "MT",
  },
  {
    "category": "borrowing",
    "code": "MOR",
    "description": "Scotia Mortgage",
    "id": "400040",
    "ownership": "B",
    "sub_code": "MT",
  },
  {
    "category": "borrowing",
    "code": "MOR",
    "description": "Scotia Mortgage",
    "id": "400050",
    "ownership": "R",
    "sub_code": "DI",
  },
  {
    "category": "borrowing",
    "code": "MOR",
    "description": "Scotia Mortgage",
    "id": "400060",
    "ownership": "B",
    "sub_code": "DI",
  },
  {
    "category": "borrowing",
    "code": "MOR",
    "description": "Scotia Mortgage",
    "id": "400070",
    "ownership": "R",
    "sub_code": "UN",
  },
  {
    "category": "borrowing",
    "code": "MOR",
    "description": "Scotia Mortgage",
    "id": "400080",
    "ownership": "B",
    "sub_code": "UN",
  },
  {
    "category": "borrowing",
    "code": "MOR",
    "description": "Scotia Mortgage",
    "id": "400090",
    "ownership": "R",
    "sub_code": "IN",
  },
  {
    "category": "borrowing",
    "code": "MOR",
    "description": "Scotia Mortgage",
    "id": "400100",
    "ownership": "B",
    "sub_code": "IN",
  },
  {
    "category": "borrowing",
    "code": "MOR",
    "description": "Scotia Mortgage",
    "id": "400110",
    "ownership": "R",
    "sub_code": "LN",
  },
  {
    "category": "borrowing",
    "code": "MOR",
    "description": "Scotia Mortgage",
    "id": "400120",
    "ownership": "B",
    "sub_code": "LN",
  },
  {
    "category": "borrowing",
    "code": "OLL",
    "description": "Loan",
    "id": "410010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "OLL",
    "description": "Business Loan",
    "id": "410020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "OLL",
    "description": "Loan",
    "id": "410030",
    "ownership": "R",
    "sub_code": "OL",
  },
  {
    "category": "borrowing",
    "code": "OLL",
    "description": "Business Loan",
    "id": "410040",
    "ownership": "B",
    "sub_code": "OL",
  },
  {
    "category": "borrowing",
    "code": "OLL",
    "description": "Loan",
    "id": "411030",
    "ownership": "R",
    "sub_code": "OP",
  },
  {
    "category": "borrowing",
    "code": "OLL",
    "description": "Business Loan",
    "id": "411040",
    "ownership": "B",
    "sub_code": "OP",
  },
  {
    "category": "borrowing",
    "code": "OLL",
    "description": "Loan",
    "id": "411050",
    "ownership": "R",
    "sub_code": "TM",
  },
  {
    "category": "borrowing",
    "code": "OLL",
    "description": "Business Loan",
    "id": "411060",
    "ownership": "B",
    "sub_code": "TM",
  },
  {
    "category": "borrowing",
    "code": "SPL",
    "description": "Scotia Plan Loan",
    "id": "420010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "SPL",
    "description": "Scotia Plan Loan",
    "id": "420020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "SPL",
    "description": "Scotia Plan Loan",
    "id": "420030",
    "ownership": "R",
    "sub_code": "DI",
  },
  {
    "category": "borrowing",
    "code": "SPL",
    "description": "Scotia Plan Loan",
    "id": "420040",
    "ownership": "R",
    "sub_code": "IN",
  },
  {
    "category": "borrowing",
    "code": "SPL",
    "description": "Small Business Loan",
    "id": "420050",
    "ownership": "B",
    "sub_code": "SB",
  },
  {
    "category": "borrowing",
    "code": "SPL",
    "description": "Scotia Dealer Advantage Loan",
    "id": "420070",
    "ownership": "R",
    "sub_code": "SD",
  },
  {
    "category": "borrowing",
    "code": "TEQ",
    "description": "Scotia Total Equity Plan",
    "id": "430010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "TEQ",
    "description": "Scotia Total Equity Plan",
    "id": "430020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "TEP",
    "description": "Scotia Total Equity Plan(s)",
    "id": "430030",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "TEP",
    "description": "Scotia Total Equity Plan(s)",
    "id": "430040",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "RTA",
    "description": "Scotiabank FastLine for business",
    "id": "430060",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "LOC",
    "description": "Govt Student Loan",
    "id": "440010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "LOC",
    "description": "Canada Student Loan",
    "id": "440030",
    "ownership": "R",
    "sub_code": "FD",
  },
  {
    "category": "borrowing",
    "code": "LOC",
    "description": "BC Student Loans",
    "id": "440050",
    "ownership": "R",
    "sub_code": "BC",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "ScotiaLine Line of Credit",
    "id": "441020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "ScotiaLine Line of Credit",
    "id": "441030",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "ScotiaLine Line of Credit",
    "id": "441070",
    "ownership": "R",
    "sub_code": "IN",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "ScotiaLine Line of Credit",
    "id": "441080",
    "ownership": "B",
    "sub_code": "IN",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "Scotia RSP Catch-Up LOC",
    "id": "441090",
    "ownership": "R",
    "sub_code": "RL",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "Scotia RSP Catch-Up LOC",
    "id": "441100",
    "ownership": "B",
    "sub_code": "RL",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "Scotia Investment LOC",
    "id": "441110",
    "ownership": "R",
    "sub_code": "IL",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "Scotia Investment LOC",
    "id": "441120",
    "ownership": "B",
    "sub_code": "IL",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "Scotia Invest. Management LOC",
    "id": "441130",
    "ownership": "R",
    "sub_code": "IM",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "Scotia Invest. Management LOC",
    "id": "441140",
    "ownership": "B",
    "sub_code": "IM",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "ScotiaLine Line of Credit",
    "id": "441150",
    "ownership": "R",
    "sub_code": "CS",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "ScotiaLine Line of Credit",
    "id": "441160",
    "ownership": "B",
    "sub_code": "CS",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "ScotiaLine Line of Credit",
    "id": "441170",
    "ownership": "R",
    "sub_code": "RS",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "ScotiaLine Line of Credit",
    "id": "441180",
    "ownership": "B",
    "sub_code": "RS",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "ScotiaLine Line of Credit",
    "id": "441190",
    "ownership": "R",
    "sub_code": "INE",
  },
  {
    "category": "borrowing",
    "code": "SCL",
    "description": "ScotiaLine Line of Credit",
    "id": "441200",
    "ownership": "B",
    "sub_code": "INE",
  },
  {
    "category": "borrowing",
    "code": "SSL",
    "description": "Scotia Student Line of Cr/Loan",
    "id": "442010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "SSL",
    "description": "Scotia Student Loan",
    "id": "442050",
    "ownership": "R",
    "sub_code": "LN",
  },
  {
    "category": "borrowing",
    "code": "SSL",
    "description": "ScotiaLine for Students",
    "id": "442090",
    "ownership": "R",
    "sub_code": "LI",
  },
  {
    "category": "borrowing",
    "code": "SSL",
    "description": "Scotia Professional Stud. Plan",
    "id": "442130",
    "ownership": "R",
    "sub_code": "SP",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank VISA card",
    "id": "450010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank VISA card",
    "id": "450020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Momentum VISA card",
    "id": "450070",
    "ownership": "R",
    "sub_code": "RG",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Momentum VISA card",
    "id": "450080",
    "ownership": "B",
    "sub_code": "RG",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Momentum No-Fee VISA",
    "id": "450110",
    "ownership": "R",
    "sub_code": "NF",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Momentum No-Fee VISA",
    "id": "450120",
    "ownership": "B",
    "sub_code": "NF",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Momentum VISA card",
    "id": "450130",
    "ownership": "R",
    "sub_code": "ST",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Momentum VISA card",
    "id": "450140",
    "ownership": "B",
    "sub_code": "ST",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Moneyback VISA card",
    "id": "450170",
    "ownership": "R",
    "sub_code": "SC",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Moneyback VISA card",
    "id": "450180",
    "ownership": "B",
    "sub_code": "SC",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Moneyback VISA card",
    "id": "450210",
    "ownership": "R",
    "sub_code": "SL",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Moneyback VISA card",
    "id": "450220",
    "ownership": "B",
    "sub_code": "SL",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Moneyback VISA card",
    "id": "450230",
    "ownership": "R",
    "sub_code": "AD",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Moneyback VISA card",
    "id": "450240",
    "ownership": "B",
    "sub_code": "AD",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Moneyback VISA card",
    "id": "450250",
    "ownership": "R",
    "sub_code": "SU",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Moneyback VISA card",
    "id": "450260",
    "ownership": "B",
    "sub_code": "SU",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Momentum VISA card",
    "id": "450270",
    "ownership": "R",
    "sub_code": "RE",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Momentum VISA card",
    "id": "450280",
    "ownership": "B",
    "sub_code": "RE",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Momentum VISA card",
    "id": "450290",
    "ownership": "R",
    "sub_code": "GR",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Momentum VISA card",
    "id": "450300",
    "ownership": "B",
    "sub_code": "GR",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450330",
    "ownership": "R",
    "sub_code": "CC",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450340",
    "ownership": "B",
    "sub_code": "CC",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450350",
    "ownership": "R",
    "sub_code": "AE",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450360",
    "ownership": "B",
    "sub_code": "AE",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450370",
    "ownership": "R",
    "sub_code": "RF",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450380",
    "ownership": "B",
    "sub_code": "RF",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450390",
    "ownership": "R",
    "sub_code": "AC",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450400",
    "ownership": "B",
    "sub_code": "AC",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450410",
    "ownership": "R",
    "sub_code": "AI",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450420",
    "ownership": "B",
    "sub_code": "AI",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450430",
    "ownership": "R",
    "sub_code": "AM",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450440",
    "ownership": "B",
    "sub_code": "AM",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450450",
    "ownership": "R",
    "sub_code": "AN",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450460",
    "ownership": "B",
    "sub_code": "AN",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450470",
    "ownership": "R",
    "sub_code": "AP",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450480",
    "ownership": "B",
    "sub_code": "AP",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450490",
    "ownership": "R",
    "sub_code": "AR",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450500",
    "ownership": "B",
    "sub_code": "AR",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450510",
    "ownership": "R",
    "sub_code": "AS",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotiabank Classic VISA card",
    "id": "450520",
    "ownership": "B",
    "sub_code": "AS",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Learn VISA CARD",
    "id": "450550",
    "ownership": "R",
    "sub_code": "SV",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Learn VISA CARD",
    "id": "450560",
    "ownership": "B",
    "sub_code": "SV",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scene+ Visa card",
    "id": "450590",
    "ownership": "R",
    "sub_code": "ZZ",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scene+ Visa card",
    "id": "450600",
    "ownership": "B",
    "sub_code": "ZZ",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scene+ Visa card",
    "id": "450630",
    "ownership": "R",
    "sub_code": "SS",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scene+ Visa card",
    "id": "450640",
    "ownership": "B",
    "sub_code": "SS",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "ScotiaHockey NHL VISA card",
    "id": "450670",
    "ownership": "R",
    "sub_code": "NH",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "ScotiaHockey NHL VISA card",
    "id": "450680",
    "ownership": "B",
    "sub_code": "NH",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Momentum VISA Infinite",
    "id": "450690",
    "ownership": "R",
    "sub_code": "DM",
  },
  {
    "category": "borrowing",
    "code": "VCL",
    "description": "Scotia Momentum VISA Infinite",
    "id": "450700",
    "ownership": "B",
    "sub_code": "DM",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "ScotiaGold VISA card",
    "id": "460010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "ScotiaGold VISA card",
    "id": "460020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "ScotiaGold Passport VISA card",
    "id": "460070",
    "ownership": "R",
    "sub_code": "RG",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "ScotiaGold Passport VISA card",
    "id": "460080",
    "ownership": "B",
    "sub_code": "RG",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "ScotiaGold Passport VISA card",
    "id": "460090",
    "ownership": "R",
    "sub_code": "RE",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "ScotiaGold Passport VISA card",
    "id": "460100",
    "ownership": "B",
    "sub_code": "RE",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "ScotiaGold Passport VISA card",
    "id": "460110",
    "ownership": "R",
    "sub_code": "SP",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "ScotiaGold Passport VISA card",
    "id": "460120",
    "ownership": "B",
    "sub_code": "SP",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "No-Fee ScotiaGold VISA card",
    "id": "460150",
    "ownership": "R",
    "sub_code": "NF",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "No-Fee ScotiaGold VISA card",
    "id": "460160",
    "ownership": "B",
    "sub_code": "NF",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "ScotiaGold Passport VISA card",
    "id": "460170",
    "ownership": "R",
    "sub_code": "PB",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "ScotiaGold Passport VISA card",
    "id": "460180",
    "ownership": "B",
    "sub_code": "PB",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "ScotiaGold Passport VISA card",
    "id": "460190",
    "ownership": "R",
    "sub_code": "PE",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "ScotiaGold Passport VISA card",
    "id": "460200",
    "ownership": "B",
    "sub_code": "PE",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "ScotiaGold Passport VISA card",
    "id": "460210",
    "ownership": "R",
    "sub_code": "ST",
  },
  {
    "category": "borrowing",
    "code": "VGD",
    "description": "ScotiaGold Passport VISA card",
    "id": "460220",
    "ownership": "B",
    "sub_code": "ST",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "ScotiaLine Line of Credit",
    "id": "470010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "ScotiaLine Line of Credit",
    "id": "470020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "ScotiaLine Line of Credit",
    "id": "470070",
    "ownership": "R",
    "sub_code": "RG",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "ScotiaLine Line of Credit",
    "id": "470080",
    "ownership": "B",
    "sub_code": "RG",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "ScotiaLine for business VISA",
    "id": "470110",
    "ownership": "R",
    "sub_code": "IN",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "ScotiaLine for business VISA",
    "id": "470120",
    "ownership": "B",
    "sub_code": "IN",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "ScotiaLine for business VISA",
    "id": "470150",
    "ownership": "R",
    "sub_code": "UN",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "ScotiaLine for business VISA",
    "id": "470160",
    "ownership": "B",
    "sub_code": "UN",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "ScotiaGold Passport fb VISA",
    "id": "470190",
    "ownership": "R",
    "sub_code": "YY",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "ScotiaGold Passport fb VISA",
    "id": "470200",
    "ownership": "B",
    "sub_code": "YY",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "Agriculture Line of Credit",
    "id": "470230",
    "ownership": "R",
    "sub_code": "AI",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "Agriculture Line of Credit",
    "id": "470240",
    "ownership": "B",
    "sub_code": "AI",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "Agriculture Line of Credit",
    "id": "470250",
    "ownership": "R",
    "sub_code": "AU",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "Agriculture Line of Credit",
    "id": "470260",
    "ownership": "B",
    "sub_code": "AU",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "ScotiaLine for business VISA",
    "id": "470270",
    "ownership": "R",
    "sub_code": "CV",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "ScotiaLine for business VISA",
    "id": "470280",
    "ownership": "B",
    "sub_code": "CV",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "ScotiaLine Line of Credit",
    "id": "470290",
    "ownership": "R",
    "sub_code": "RS",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "ScotiaLine Line of Credit",
    "id": "470300",
    "ownership": "B",
    "sub_code": "RS",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "ScotiaLine for business VISA",
    "id": "470310",
    "ownership": "R",
    "sub_code": "IN",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "ScotiaLine for business VISA",
    "id": "470320",
    "ownership": "B",
    "sub_code": "IN",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "ScotiaLine for business VISA",
    "id": "470350",
    "ownership": "R",
    "sub_code": "UN",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "ScotiaLine for business VISA",
    "id": "470360",
    "ownership": "B",
    "sub_code": "UN",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "ScotiaGold Passport fb VISA",
    "id": "470390",
    "ownership": "R",
    "sub_code": "YY",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "ScotiaGold Passport fb VISA",
    "id": "470400",
    "ownership": "B",
    "sub_code": "YY",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "Scotia Momentum fb VISA",
    "id": "470410",
    "ownership": "R",
    "sub_code": "MB",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "Scotia Momentum fb VISA",
    "id": "470420",
    "ownership": "B",
    "sub_code": "MB",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "Agriculture Line of Credit",
    "id": "470450",
    "ownership": "R",
    "sub_code": "AGI",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "Agriculture Line of Credit",
    "id": "470460",
    "ownership": "B",
    "sub_code": "AGI",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "Agriculture Line of Credit",
    "id": "470470",
    "ownership": "R",
    "sub_code": "AGU",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "Agriculture Line of Credit",
    "id": "470480",
    "ownership": "B",
    "sub_code": "AGU",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "ScotiaLine for business VISA",
    "id": "470490",
    "ownership": "R",
    "sub_code": "CV",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "ScotiaLine for business VISA",
    "id": "470500",
    "ownership": "B",
    "sub_code": "CV",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "ScotiaLine for business VISA",
    "id": "470510",
    "ownership": "R",
    "sub_code": "INC",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "ScotiaLine for business VISA",
    "id": "470520",
    "ownership": "B",
    "sub_code": "INC",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "ScotiaLine for business VISA",
    "id": "470530",
    "ownership": "R",
    "sub_code": "UNI",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "ScotiaLine for business VISA",
    "id": "470540",
    "ownership": "B",
    "sub_code": "UNI",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "Scotia Momentum fb VISA",
    "id": "470550",
    "ownership": "R",
    "sub_code": "MB",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "Scotia Momentum fb VISA",
    "id": "470560",
    "ownership": "B",
    "sub_code": "MB",
  },
  {
    "category": "borrowing",
    "code": "VSC",
    "description": "Scene+ Visa card",
    "id": "470810",
    "ownership": "R",
    "sub_code": "ZZ",
  },
  {
    "category": "borrowing",
    "code": "VSC",
    "description": "Scene+ Visa card",
    "id": "470820",
    "ownership": "B",
    "sub_code": "ZZ",
  },
  {
    "category": "borrowing",
    "code": "VSC",
    "description": "Scene+ Visa card",
    "id": "470830",
    "ownership": "R",
    "sub_code": "SS",
  },
  {
    "category": "borrowing",
    "code": "VSC",
    "description": "Scene+ Visa card",
    "id": "470840",
    "ownership": "B",
    "sub_code": "SS",
  },
  {
    "category": "borrowing",
    "code": "VLR",
    "description": "Scotiabank Value VISA",
    "id": "480010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "VLR",
    "description": "Scotiabank Value VISA",
    "id": "480020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "VLR",
    "description": "Scotiabank Value VISA",
    "id": "480070",
    "ownership": "R",
    "sub_code": "RG",
  },
  {
    "category": "borrowing",
    "code": "VLR",
    "description": "Scotiabank Value VISA",
    "id": "480080",
    "ownership": "B",
    "sub_code": "RG",
  },
  {
    "category": "borrowing",
    "code": "VLR",
    "description": "Scotiabank Value VISA",
    "id": "480110",
    "ownership": "R",
    "sub_code": "NF",
  },
  {
    "category": "borrowing",
    "code": "VLR",
    "description": "Scotiabank Value VISA",
    "id": "480120",
    "ownership": "B",
    "sub_code": "NF",
  },
  {
    "category": "borrowing",
    "code": "VLR",
    "description": "Scotiabank Rewards VISA Card",
    "id": "480130",
    "ownership": "R",
    "sub_code": "RC",
  },
  {
    "category": "borrowing",
    "code": "VLR",
    "description": "Scotiabank Rewards VISA Card",
    "id": "480140",
    "ownership": "B",
    "sub_code": "RC",
  },
  {
    "category": "borrowing",
    "code": "VAX",
    "description": "Scotiabank Amex Card",
    "id": "500010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "VAX",
    "description": "Scotiabank Amex Card",
    "id": "500020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "VAX",
    "description": "Scotiabank Amex Card",
    "id": "500030",
    "ownership": "R",
    "sub_code": "SC",
  },
  {
    "category": "borrowing",
    "code": "VAX",
    "description": "Scotiabank Amex Card",
    "id": "500040",
    "ownership": "B",
    "sub_code": "SC",
  },
  {
    "category": "borrowing",
    "code": "VAX",
    "description": "Scotiabank Amex Card",
    "id": "500050",
    "ownership": "R",
    "sub_code": "SS",
  },
  {
    "category": "borrowing",
    "code": "VAX",
    "description": "Scotiabank Amex Card",
    "id": "500060",
    "ownership": "B",
    "sub_code": "SS",
  },
  {
    "category": "borrowing",
    "code": "VAX",
    "description": "Scotiabank Gold Amex Card",
    "id": "500070",
    "ownership": "R",
    "sub_code": "GC",
  },
  {
    "category": "borrowing",
    "code": "VAX",
    "description": "Scotiabank Gold Amex Card",
    "id": "500080",
    "ownership": "B",
    "sub_code": "GC",
  },
  {
    "category": "borrowing",
    "code": "VAX",
    "description": "Scotiabank Gold Amex Card",
    "id": "500090",
    "ownership": "R",
    "sub_code": "GS",
  },
  {
    "category": "borrowing",
    "code": "VAX",
    "description": "Scotiabank Gold Amex Card",
    "id": "500100",
    "ownership": "B",
    "sub_code": "GS",
  },
  {
    "category": "borrowing",
    "code": "VAX",
    "description": "Scotiabank Platinum Amex Card",
    "id": "500110",
    "ownership": "R",
    "sub_code": "PC",
  },
  {
    "category": "borrowing",
    "code": "VAX",
    "description": "Scotiabank Platinum Amex Card",
    "id": "500120",
    "ownership": "B",
    "sub_code": "PC",
  },
  {
    "category": "borrowing",
    "code": "AXS",
    "description": "Scotiabank Amex Card",
    "id": "500210",
    "ownership": "R",
    "sub_code": "SC",
  },
  {
    "category": "borrowing",
    "code": "AXS",
    "description": "Scotiabank Amex Card",
    "id": "500220",
    "ownership": "B",
    "sub_code": "SC",
  },
  {
    "category": "borrowing",
    "code": "AXS",
    "description": "Scotiabank Amex Card",
    "id": "500230",
    "ownership": "R",
    "sub_code": "SS",
  },
  {
    "category": "borrowing",
    "code": "AXS",
    "description": "Scotiabank Amex Card",
    "id": "500240",
    "ownership": "B",
    "sub_code": "SS",
  },
  {
    "category": "borrowing",
    "code": "AXG",
    "description": "Scotiabank Gold Amex Card",
    "id": "500250",
    "ownership": "R",
    "sub_code": "GC",
  },
  {
    "category": "borrowing",
    "code": "AXG",
    "description": "Scotiabank Gold Amex Card",
    "id": "500260",
    "ownership": "B",
    "sub_code": "GC",
  },
  {
    "category": "borrowing",
    "code": "AXG",
    "description": "Scotiabank Gold Amex Card",
    "id": "500270",
    "ownership": "R",
    "sub_code": "GS",
  },
  {
    "category": "borrowing",
    "code": "AXG",
    "description": "Scotiabank Gold Amex Card",
    "id": "500280",
    "ownership": "B",
    "sub_code": "GS",
  },
  {
    "category": "borrowing",
    "code": "AXP",
    "description": "Scotiabank Platinum Amex Card",
    "id": "500290",
    "ownership": "R",
    "sub_code": "PC",
  },
  {
    "category": "borrowing",
    "code": "AXP",
    "description": "Scotiabank Platinum Amex Card",
    "id": "500300",
    "ownership": "B",
    "sub_code": "PC",
  },
  {
    "category": "borrowing",
    "code": "VFA",
    "description": "Future Classic VISA",
    "id": "510010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "VFA",
    "description": "Future Classic VISA",
    "id": "510020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "VFA",
    "description": "Scotiabank GM VISA",
    "id": "510030",
    "ownership": "R",
    "sub_code": "A1",
  },
  {
    "category": "borrowing",
    "code": "VFA",
    "description": "Scotiabank GM VISA",
    "id": "510040",
    "ownership": "B",
    "sub_code": "A1",
  },
  {
    "category": "borrowing",
    "code": "VFA",
    "description": "Future Classic VISA A2",
    "id": "510050",
    "ownership": "R",
    "sub_code": "A2",
  },
  {
    "category": "borrowing",
    "code": "VFA",
    "description": "Future Classic VISA A2",
    "id": "510060",
    "ownership": "B",
    "sub_code": "A2",
  },
  {
    "category": "borrowing",
    "code": "VFA",
    "description": "Scotiabank More Rewards Visa Card",
    "id": "510070",
    "ownership": "R",
    "sub_code": "A3",
  },
  {
    "category": "borrowing",
    "code": "VFA",
    "description": "Scotiabank More Rewards Visa Card",
    "id": "510080",
    "ownership": "B",
    "sub_code": "A3",
  },
  {
    "category": "borrowing",
    "code": "VFA",
    "description": "Future Classic VISA A4",
    "id": "510090",
    "ownership": "R",
    "sub_code": "A4",
  },
  {
    "category": "borrowing",
    "code": "VFA",
    "description": "Future Classic VISA A4",
    "id": "510100",
    "ownership": "B",
    "sub_code": "A4",
  },
  {
    "category": "borrowing",
    "code": "VFB",
    "description": "Small Business VISA",
    "id": "520010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "VFB",
    "description": "Small Business VISA",
    "id": "520020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "VFB",
    "description": "Scotiabank GM Visa Business Card",
    "id": "520030",
    "ownership": "R",
    "sub_code": "B1",
  },
  {
    "category": "borrowing",
    "code": "VFB",
    "description": "Scotiabank GM Visa Business Card",
    "id": "520040",
    "ownership": "B",
    "sub_code": "B1",
  },
  {
    "category": "borrowing",
    "code": "VFB",
    "description": "Future SB VISA B2",
    "id": "520050",
    "ownership": "R",
    "sub_code": "B2",
  },
  {
    "category": "borrowing",
    "code": "VFB",
    "description": "Future SB VISA B2",
    "id": "520060",
    "ownership": "B",
    "sub_code": "B2",
  },
  {
    "category": "borrowing",
    "code": "VFB",
    "description": "Scotiabank Passport Visa Infinite Business",
    "id": "520070",
    "ownership": "R",
    "sub_code": "TB",
  },
  {
    "category": "borrowing",
    "code": "VFB",
    "description": "Scotiabank Passport Visa Infinite Business",
    "id": "520080",
    "ownership": "B",
    "sub_code": "TB",
  },
  {
    "category": "borrowing",
    "code": "VFC",
    "description": "Future Corporate VISA",
    "id": "530010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "VFC",
    "description": "Future Corporate VISA",
    "id": "530020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "VFC",
    "description": "Future Corporate VISA C1",
    "id": "530030",
    "ownership": "R",
    "sub_code": "C1",
  },
  {
    "category": "borrowing",
    "code": "VFC",
    "description": "Future Corporate VISA C1",
    "id": "530040",
    "ownership": "B",
    "sub_code": "C1",
  },
  {
    "category": "borrowing",
    "code": "VFC",
    "description": "Future Corporate VISA C2",
    "id": "530050",
    "ownership": "R",
    "sub_code": "C2",
  },
  {
    "category": "borrowing",
    "code": "VFC",
    "description": "Future Corporate VISA C2",
    "id": "530060",
    "ownership": "B",
    "sub_code": "C2",
  },
  {
    "category": "borrowing",
    "code": "VFF",
    "description": "Visa Infinite",
    "id": "540010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "VFF",
    "description": "Visa Infinite",
    "id": "540020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "VFF",
    "description": "Scotia GM VISA Infinite Card",
    "id": "540030",
    "ownership": "R",
    "sub_code": "F1",
  },
  {
    "category": "borrowing",
    "code": "VFF",
    "description": "Scotia GM VISA Infinite Card",
    "id": "540040",
    "ownership": "B",
    "sub_code": "F1",
  },
  {
    "category": "borrowing",
    "code": "VFF",
    "description": "Future Infinite VISA F2",
    "id": "540050",
    "ownership": "R",
    "sub_code": "F2",
  },
  {
    "category": "borrowing",
    "code": "VFF",
    "description": "Future Infinite VISA F2",
    "id": "540060",
    "ownership": "B",
    "sub_code": "F2",
  },
  {
    "category": "borrowing",
    "code": "VFG",
    "description": "Steel Gold VISA",
    "id": "550010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "VFG",
    "description": "Steel Gold VISA",
    "id": "550020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "VFG",
    "description": "Steel Gold VISA G1",
    "id": "550030",
    "ownership": "R",
    "sub_code": "G1",
  },
  {
    "category": "borrowing",
    "code": "VFG",
    "description": "Steel Gold VISA G1",
    "id": "550040",
    "ownership": "B",
    "sub_code": "G1",
  },
  {
    "category": "borrowing",
    "code": "VFG",
    "description": "Future Gold VISA G2",
    "id": "550050",
    "ownership": "R",
    "sub_code": "G2",
  },
  {
    "category": "borrowing",
    "code": "VFG",
    "description": "Future Gold VISA G2",
    "id": "550060",
    "ownership": "B",
    "sub_code": "G2",
  },
  {
    "category": "borrowing",
    "code": "VFG",
    "description": "Future Gold VISA G3",
    "id": "550070",
    "ownership": "R",
    "sub_code": "G3",
  },
  {
    "category": "borrowing",
    "code": "VFG",
    "description": "Future Gold VISA G3",
    "id": "550080",
    "ownership": "B",
    "sub_code": "G3",
  },
  {
    "category": "borrowing",
    "code": "VFG",
    "description": "Future Gold VISA G4",
    "id": "550090",
    "ownership": "R",
    "sub_code": "G4",
  },
  {
    "category": "borrowing",
    "code": "VFG",
    "description": "Future Gold VISA G4",
    "id": "550100",
    "ownership": "B",
    "sub_code": "G4",
  },
  {
    "category": "borrowing",
    "code": "VFP",
    "description": "Future Platinum VISA",
    "id": "560010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "VFP",
    "description": "Future Platinum VISA",
    "id": "560020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "VFP",
    "description": "Future Platinum VISA P1",
    "id": "560030",
    "ownership": "R",
    "sub_code": "P1",
  },
  {
    "category": "borrowing",
    "code": "VFP",
    "description": "Future Platinum VISA P1",
    "id": "560040",
    "ownership": "B",
    "sub_code": "P1",
  },
  {
    "category": "borrowing",
    "code": "VFP",
    "description": "Future Platinum VISA P2",
    "id": "560050",
    "ownership": "R",
    "sub_code": "P2",
  },
  {
    "category": "borrowing",
    "code": "VFP",
    "description": "Future Platinum VISA P2",
    "id": "560060",
    "ownership": "B",
    "sub_code": "P2",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "Scotia Home Hardware PRO Visa Business Card",
    "id": "560070",
    "ownership": "R",
    "sub_code": "HH",
  },
  {
    "category": "borrowing",
    "code": "VIC",
    "description": "Scotia Home Hardware PRO Visa Business Card",
    "id": "560080",
    "ownership": "B",
    "sub_code": "HH",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "Scotia Home Hardware PRO Visa Business Card",
    "id": "560110",
    "ownership": "R",
    "sub_code": "HHB",
  },
  {
    "category": "borrowing",
    "code": "BLV",
    "description": "Scotia Home Hardware PRO Visa Business Card",
    "id": "560120",
    "ownership": "B",
    "sub_code": "HHB",
  },
  {
    "category": "borrowing",
    "code": "AFA",
    "description": "Future Amex Standard",
    "id": "570010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "AFA",
    "description": "Future Amex Standard",
    "id": "570020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "AFA",
    "description": "Future Amex Standard A1",
    "id": "570030",
    "ownership": "R",
    "sub_code": "A1",
  },
  {
    "category": "borrowing",
    "code": "AFA",
    "description": "Future Amex Standard A1",
    "id": "570040",
    "ownership": "B",
    "sub_code": "A1",
  },
  {
    "category": "borrowing",
    "code": "AFA",
    "description": "Future Amex Standard A2",
    "id": "570050",
    "ownership": "R",
    "sub_code": "A2",
  },
  {
    "category": "borrowing",
    "code": "AFA",
    "description": "Future Amex Standard A2",
    "id": "570060",
    "ownership": "B",
    "sub_code": "A2",
  },
  {
    "category": "borrowing",
    "code": "AFB",
    "description": "Future Amex SB",
    "id": "580010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "AFB",
    "description": "Future Amex SB",
    "id": "580020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "AFB",
    "description": "Future Amex SB B1",
    "id": "580030",
    "ownership": "R",
    "sub_code": "B1",
  },
  {
    "category": "borrowing",
    "code": "AFB",
    "description": "Future Amex SB B1",
    "id": "580040",
    "ownership": "B",
    "sub_code": "B1",
  },
  {
    "category": "borrowing",
    "code": "AFC",
    "description": "Future Amex Corporate",
    "id": "590010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "AFC",
    "description": "Future Amex Corporate",
    "id": "590020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "AFC",
    "description": "Future Amex Corporate C1",
    "id": "590030",
    "ownership": "R",
    "sub_code": "C1",
  },
  {
    "category": "borrowing",
    "code": "AFC",
    "description": "Future Amex Corporate C1",
    "id": "590040",
    "ownership": "B",
    "sub_code": "C1",
  },
  {
    "category": "borrowing",
    "code": "AFF",
    "description": "Future Amex Infinite",
    "id": "600010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "AFF",
    "description": "Future Amex Infinite",
    "id": "600020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "AFF",
    "description": "Future Amex Infinite F1",
    "id": "600030",
    "ownership": "R",
    "sub_code": "F1",
  },
  {
    "category": "borrowing",
    "code": "AFF",
    "description": "Future Amex Infinite F1",
    "id": "600040",
    "ownership": "B",
    "sub_code": "F1",
  },
  {
    "category": "borrowing",
    "code": "AFG",
    "description": "Future Amex Gold",
    "id": "610010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "AFG",
    "description": "Future Amex Gold",
    "id": "610020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "AFG",
    "description": "Future Amex Gold G1",
    "id": "610030",
    "ownership": "R",
    "sub_code": "G1",
  },
  {
    "category": "borrowing",
    "code": "AFG",
    "description": "Future Amex Gold G1",
    "id": "610040",
    "ownership": "B",
    "sub_code": "G1",
  },
  {
    "category": "borrowing",
    "code": "AFG",
    "description": "Future Amex Gold G2",
    "id": "610050",
    "ownership": "R",
    "sub_code": "G2",
  },
  {
    "category": "borrowing",
    "code": "AFG",
    "description": "Future Amex Gold G2",
    "id": "610060",
    "ownership": "B",
    "sub_code": "G2",
  },
  {
    "category": "borrowing",
    "code": "AFP",
    "description": "Future Amex Platinum",
    "id": "620010",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "AFP",
    "description": "Future Amex Platinum",
    "id": "620020",
    "ownership": "B",
  },
  {
    "category": "borrowing",
    "code": "AFP",
    "description": "Future Amex Platinum P1",
    "id": "620030",
    "ownership": "R",
    "sub_code": "P1",
  },
  {
    "category": "borrowing",
    "code": "AFP",
    "description": "Future Amex Platinum P1",
    "id": "620040",
    "ownership": "B",
    "sub_code": "P1",
  },
  {
    "category": "borrowing",
    "code": "VUS",
    "description": "Scotiabank USD VISA Card",
    "id": "630010",
    "ownership": "R",
    "sub_code": "RG",
  },
  {
    "category": "borrowing",
    "code": "VUS",
    "description": "Scotiabank USD VISA Card",
    "id": "630020",
    "ownership": "B",
    "sub_code": "RG",
  },
  {
    "category": "borrowing",
    "code": "STD",
    "description": "Scotia Momentum Mastercard",
    "id": "640010",
    "ownership": "R",
    "sub_code": "MC",
  },
  {
    "category": "borrowing",
    "code": "STA",
    "description": "Scotia Momentum Mastercard",
    "id": "640030",
    "ownership": "R",
    "sub_code": "MC",
  },
  {
    "category": "borrowing",
    "code": "SEA",
    "description": "Scotiabank Retailer Card",
    "id": "640050",
    "ownership": "R",
    "sub_code": "P1",
  },
  {
    "category": "borrowing",
    "code": "SAC",
    "description": "Scotiabank Retailer Card",
    "id": "640070",
    "ownership": "R",
    "sub_code": "P1",
  },
  {
    "category": "borrowing",
    "code": "STR",
    "description": "Scotiabank Voyage Mastercard",
    "id": "640090",
    "ownership": "R",
    "sub_code": "MC",
  },
  {
    "category": "borrowing",
    "code": "STD",
    "description": "Scotia Momentum Mastercard",
    "id": "640110",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "STA",
    "description": "Scotia Momentum Mastercard",
    "id": "640130",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "SEA",
    "description": "Scotiabank Retailer Card",
    "id": "640150",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "SAC",
    "description": "Scotiabank Retailer Card",
    "id": "640170",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "STR",
    "description": "Scotiabank Voyage Mastercard",
    "id": "640190",
    "ownership": "R",
  },
  {
    "category": "borrowing",
    "code": "VFF",
    "description": "Scotiabank Passport Visa Infinite card",
    "id": "650030",
    "ownership": "R",
    "sub_code": "TR",
  },
  {
    "category": "borrowing",
    "code": "VFF",
    "description": "Scotiabank Passport Visa Infinite card",
    "id": "650040",
    "ownership": "B",
    "sub_code": "TR",
  },
  {
    "category": "borrowing",
    "code": "VFF",
    "description": "Scotiabank Passport Visa Infinite card",
    "id": "650050",
    "ownership": "R",
    "sub_code": "TS",
  },
  {
    "category": "borrowing",
    "code": "VFF",
    "description": "Scotiabank Passport Visa Infinite card",
    "id": "650060",
    "ownership": "B",
    "sub_code": "TS",
  },
  {
    "category": "borrowing",
    "code": "VFF",
    "description": "Scotiabank Passport Visa Infinite Privilege Card",
    "id": "650070",
    "ownership": "R",
    "sub_code": "IP",
  },
  {
    "category": "borrowing",
    "code": "AG",
    "description": "Scotiabank Gold Amex Card",
    "id": "660010",
    "ownership": "R",
    "sub_code": "AGC",
  },
  {
    "category": "borrowing",
    "code": "VS",
    "description": "Scene+ Visa card",
    "id": "660030",
    "ownership": "R",
    "sub_code": "VSN",
  },
  {
    "category": "borrowing",
    "code": "VB",
    "description": "Scotia Home Hardware PRO Visa Business Card",
    "id": "660050",
    "ownership": "R",
    "sub_code": "HHB",
  },
  {
    "category": "borrowing",
    "code": "MC",
    "description": "Scotia Momentum Mastercard",
    "id": "660070",
    "ownership": "R",
    "sub_code": "STD",
  },
  {
    "category": "borrowing",
    "code": "MC",
    "description": "Scotiabank Voyage Mastercard",
    "id": "660090",
    "ownership": "R",
    "sub_code": "STR",
  },
  {
    "category": "borrowing",
    "code": "P1",
    "description": "Scotiabank Retailer Card",
    "id": "660110",
    "ownership": "R",
    "sub_code": "SAC",
  },
  {
    "category": "borrowing",
    "code": "P1",
    "description": "Scotiabank Retailer Card",
    "id": "660130",
    "ownership": "R",
    "sub_code": "SEA",
  },
  {
    "category": "borrowing",
    "code": "LC",
    "description": "ScotiaLine Line of Credit",
    "id": "660150",
    "ownership": "R",
    "sub_code": "LIN",
  },
  {
    "category": "borrowing",
    "code": "LC",
    "description": " Scotia Invest. Management LOC",
    "id": "660190",
    "ownership": "R",
    "sub_code": "LIM",
  },
  {
    "category": "borrowing",
    "code": "LC",
    "description": "ScotiaLine Line of Credit",
    "id": "660210",
    "ownership": "R",
    "sub_code": "LCS",
  },
  {
    "category": "borrowing",
    "code": "LC",
    "description": "ScotiaLine Line of Credit",
    "id": "660230",
    "ownership": "R",
    "sub_code": "LRS",
  },
  {
    "category": "borrowing",
    "code": "LC",
    "description": "ScotiaLine for Students",
    "id": "660250",
    "ownership": "R",
    "sub_code": "SLI",
  },
  {
    "category": "borrowing",
    "code": "LC",
    "description": "Scotia Professional Stud. Plan",
    "id": "660270",
    "ownership": "R",
    "sub_code": "SSP",
  },
  {
    "category": "borrowing",
    "code": "VS",
    "description": "Scotia Momentum VISA card",
    "id": "660290",
    "ownership": "R",
    "sub_code": "MRG",
  },
  {
    "category": "borrowing",
    "code": "VS",
    "description": "Scotia Momentum No-Fee VISA",
    "id": "660310",
    "ownership": "R",
    "sub_code": "MNF",
  },
  {
    "category": "borrowing",
    "code": "VI",
    "description": "Scotia Momentum VISA Infinite",
    "id": "660330",
    "ownership": "R",
    "sub_code": "MIF",
  },
  {
    "category": "borrowing",
    "code": "VG",
    "description": "ScotiaGold Passport VISA card",
    "id": "660350",
    "ownership": "R",
    "sub_code": "PRG",
  },
  {
    "category": "borrowing",
    "code": "VS",
    "description": "ScotiaLine Line of Credit",
    "id": "660370",
    "ownership": "R",
    "sub_code": "SRG",
  },
  {
    "category": "borrowing",
    "code": "VB",
    "description": "ScotiaLine for business VISA",
    "id": "660390",
    "ownership": "R",
    "sub_code": "CBA",
  },
  {
    "category": "borrowing",
    "code": "VB",
    "description": "ScotiaGold Passport fb VISA",
    "id": "660410",
    "ownership": "R",
    "sub_code": "PFB",
  },
  {
    "category": "borrowing",
    "code": "LC",
    "description": "Agriculture Line of Credit",
    "id": "660430",
    "ownership": "R",
    "sub_code": "AGU",
  },
  {
    "category": "borrowing",
    "code": "VB",
    "description": "ScotiaLine for business VISA",
    "id": "660450",
    "ownership": "R",
    "sub_code": "BCV",
  },
  {
    "category": "borrowing",
    "code": "VS",
    "description": "ScotiaLine Line of Credit",
    "id": "660470",
    "ownership": "R",
    "sub_code": "SRS",
  },
  {
    "category": "borrowing",
    "code": "VB",
    "description": "Scotia Momentum fb VISA",
    "id": "660490",
    "ownership": "R",
    "sub_code": "MFB",
  },
  {
    "category": "borrowing",
    "code": "VS",
    "description": "Scotiabank Value VISA",
    "id": "660510",
    "ownership": "R",
    "sub_code": "VRG",
  },
  {
    "category": "borrowing",
    "code": "AE",
    "description": "Scotiabank Amex Card",
    "id": "660530",
    "ownership": "R",
    "sub_code": "ASC",
  },
  {
    "category": "borrowing",
    "code": "AP",
    "description": "Scotiabank Platinum Amex Card",
    "id": "660550",
    "ownership": "R",
    "sub_code": "APC",
  },
  {
    "category": "borrowing",
    "code": "VS",
    "description": "Scotiabank GM VISA",
    "id": "660570",
    "ownership": "R",
    "sub_code": "GMR",
  },
  {
    "category": "borrowing",
    "code": "VB",
    "description": "Scotiabank GM Visa Business Card",
    "id": "660590",
    "ownership": "R",
    "sub_code": "GMB",
  },
  {
    "category": "borrowing",
    "code": "VU",
    "description": "Scotiabank Passport Visa Infinite Business",
    "id": "660610",
    "ownership": "R",
    "sub_code": "PIB",
  },
  {
    "category": "borrowing",
    "code": "VI",
    "description": "Scotia GM VISA Infinite Card",
    "id": "660630",
    "ownership": "R",
    "sub_code": "GMI",
  },
  {
    "category": "borrowing",
    "code": "VS",
    "description": "Scotiabank USD VISA Card",
    "id": "660650",
    "ownership": "R",
    "sub_code": "VUS",
  },
  {
    "category": "borrowing",
    "code": "VI",
    "description": "Scotiabank Passport Visa Infinite card",
    "id": "660670",
    "ownership": "R",
    "sub_code": "PIF",
  },
  {
    "category": "borrowing",
    "code": "VB",
    "description": " ScotiaLine for business VISA",
    "id": "660690",
    "ownership": "R",
    "sub_code": "BIC",
  },
  {
    "category": "borrowing",
    "code": "VB",
    "description": "ScotiaLine for business VISA",
    "id": "660710",
    "ownership": "R",
    "sub_code": "BUN",
  },
  {
    "category": "borrowing",
    "code": "VI",
    "description": "Scotiabank Passport Visa Infinite Privilege Card",
    "id": "660730",
    "ownership": "R",
    "sub_code": "PIP",
  },
  {
    "category": "banking",
    "code": "ABC",
    "description": "test description",
    "id": "test-product-addition",
    "ownership": "R",
  },
]
`;
