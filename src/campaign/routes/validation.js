const Joi = require('joi');
const { CustomError, ERROR_HANDLER } = require('../../error');
const { customJoi } = require('../../utils/validation');
const { SCHEMA } = require('../../constants/validate');

const DEFAULT_OPTS = { stripUnknown: true };

const VALIDATION_TYPE = {
  BODY: 'body',
  PARAMS: 'params',
  QUERY: 'query',
};

const schemaValidationMiddleware = (schema, type = VALIDATION_TYPE.BODY, opts = DEFAULT_OPTS) => (req, res, next) => {
  if (!Object.values(VALIDATION_TYPE).includes(type)) {
    return next(new CustomError(500, 'Invalid joi validation type, must be: body, params, or query'));
  }
  const { error, value } = schema.validate(req[type], opts);
  if (error) {
    return next(ERROR_HANDLER(`Joi validation error on req.${type}`, error, next));
  }

  switch (type) {
    case VALIDATION_TYPE.BODY:
      res.locals.validatedBody = value;
      break;
    case VALIDATION_TYPE.PARAMS:
      res.locals.validatedParams = value;
      break;
    case VALIDATION_TYPE.QUERY:
      res.locals.validatedQuery = value;
      break;
      /* istanbul ignore next */
    default:
      break;
  }
  next();
};

// field level validators

// platform
const platform = Joi.string().min(1).max(20).regex(/^[a-z0-9]+$/);

// placement
const application = SCHEMA.APPLICATION_CREATE.extract('applicationId'); // app id string
const page = Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_\-.]+$/); // page name
const container = Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/); // container name

// disabled
const disabled = Joi.bool();

// status
const status = Joi.string().allow('draft', 'submitted', 'reviewed', 'published', 'terminated');

// ruleId
const ruleId = Joi.string().min(1).max(20).regex(/^[a-zA-Z0-9]+$/);

// external_ref
// Define regex patterns for targeted campaign id and offer id
const campaignIdPattern = Joi.string().min(4).max(15).regex(/^[A-Z\d]+$/);
const offerIdPattern = /^(OFF|BEN|PER)-\d{1,10}$/i;
const externalRef = Joi.alternatives().try(campaignIdPattern, offerIdPattern);

// SID format
const scotiaUser = Joi.string().min(4).max(10).regex(/^s[0-9]+$/);

const assignee = Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/);

const ruleTypeGet = Joi.string().valid('campaign', 'ccau_campaign').default('campaign');
const ruleTypeUpdate = Joi.string().valid('campaign', 'ccau_campaign').optional();

const platformTargetingItem = Joi.object()
  .keys({
    app_version: customJoi.appVersion(),
    os_version: customJoi.osVersion(),
    device_model: Joi.string().min(1).max(100),
  });

const platformTargeting = Joi.object()
  .keys({
    v: Joi.number().min(1),
    platform: Joi.string().min(1).max(20).regex(/^[a-z0-9]+$/),
    items: Joi.array().items(platformTargetingItem),
  });

const productItem = Joi.object()
  .keys({
    ownership: Joi.string().valid('B', 'R').required(),
    code: Joi.string().min(1).max(3).regex(/^[A-Z0-9]+$/),
    sub_code: Joi.string().min(1).max(3).regex(/^[A-Z0-9]+$/),
  }).min(1);

const massTargetingByProduct = Joi.object()
  .keys({
    any_of: Joi.array().min(1).items(productItem),
    none_of: Joi.array().min(1).items(productItem),
    all_of: Joi.array().min(1).items(productItem),
  });

const massTargetingByScenePoints = Joi.object()
  .keys({
    targeting_criteria: Joi.string().valid('equal', 'less', 'lessEqual', 'greater', 'greaterEqual', 'range').required(),
    range_min: Joi.number().min(0),
    range_max: Joi.number().min(0),
    points: Joi.number().min(0),
  });

const byDemographic = Joi.object()
  .keys({
    languages: Joi.array().items(Joi.valid('en', 'fr')).optional(),
    country: Joi.array().items(Joi.valid('canada', 'non-canada')).optional(),
    provinces: Joi.array().items(Joi.valid('AB', 'BC', 'MB', 'NB', 'NL', 'NT', 'NS', 'NU', 'ON', 'PE', 'QC', 'SK', 'YT')).optional(),
    gender: Joi.array().items(Joi.valid('mr', 'ms', 'undisclosed')).optional(),
    age_min: Joi.alternatives().try(
      Joi.number().integer().min(1).max(99),
      Joi.string().allow('').custom((value, helpers) => {
        if (value === '') return value;
        const num = parseInt(value, 10);
        if (isNaN(num) || num < 1 || num > 99) {
          return helpers.error('number.base');
        }
        return num;
      }),
    ).optional(),
    age_max: Joi.alternatives().try(
      Joi.number().integer().min(1).max(99),
      Joi.string().allow('').custom((value, helpers) => {
        if (value === '') return value;
        const num = parseInt(value, 10);
        if (isNaN(num) || num < 1 || num > 99) {
          return helpers.error('number.base');
        }
        return num;
      }),
    ).optional(),
  })
  .custom((value, helpers) => {
    // Validate that age_min <= age_max when both are provided
    if (value.age_min && value.age_max) {
      const min = typeof value.age_min === 'string' ? parseInt(value.age_min, 10) : value.age_min;
      const max = typeof value.age_max === 'string' ? parseInt(value.age_max, 10) : value.age_max;

      if (min > max) {
        return helpers.error('custom.ageRange');
      }
    }
    return value;
  })
  .messages({
    'custom.ageRange': 'Minimum age cannot be greater than maximum age',
  });

const massTargeting = Joi.object()
  .keys({
    v: Joi.number().min(1),
    by_product: massTargetingByProduct,
    by_scene_points: massTargetingByScenePoints,
    // TODO create dedicated db column for json structured used in targeting metadata or dynamic page placement
    product_pages: massTargetingByProduct,
    enrollment_status: Joi.array().items(Joi.valid('REGISTERED', 'ENROLL_REQUIRED', null, 'NEW')).optional(),
    languages: Joi.array().items(Joi.valid('fr', 'en', 'es')).optional(),
    device_lock: Joi.boolean().optional(),
    wealth_lobs: Joi.array().items(Joi.string()).min(1).optional(),
    by_demographic: byDemographic.optional(),
    iclub_tiers: Joi.array().items(Joi.string()).optional(),
    segment_ids: Joi.array().items(Joi.string()).optional(),
    investment_knowledge: Joi.object({
      mf_knowledge: Joi.array().items(Joi.string().valid('L', 'M', 'H').optional()),
      fix_income_knowledge: Joi.array().items(Joi.string().valid('L', 'M', 'H').optional()),
      stock_knowledge: Joi.array().items(Joi.string().valid('L', 'M', 'H').optional()),
      margin_knowledge: Joi.array().items(Joi.string().valid('L', 'M', 'H').optional()),
      equity_options_knowledge: Joi.array().items(Joi.string().valid('L', 'M', 'H').optional()),
      short_sale_knowledge: Joi.array().items(Joi.string().valid('L', 'M', 'H').optional()),
    }).optional(),
  });

// common campaign rule properties
const rule = {
  status,
  container,
  external_ref: externalRef.allow(null),
  name: Joi.string().min(5).max(100),
  start_at: Joi.date().iso().raw(true),
  end_at: Joi.date().iso().raw(true),
  pages: Joi.array().min(1).unique().items(page),
  platforms: Joi.array().unique().items(platform),
  mass_targeting: massTargeting,
  platforms_targeting: Joi.array().items(platformTargeting),
  disabled: Joi.bool(),
  urgent: Joi.bool(),
  dismissable_flag: Joi.bool(),
  app_version: Joi.string().min(1).max(50).allow(null),
  created_by: Joi.string().min(1).max(20).regex(/^[a-z0-9]+$/),
  updated_by: Joi.string().min(1).max(20).regex(/^[a-z0-9]+$/),
  content_space: Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/),
  content_type: Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/),
  content_id: Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/),
  assignees: Joi.array().min(0).items(assignee),
  mass: Joi.bool(),
  targeting: Joi.object(),
  application,
  type: ruleTypeGet,
};

// create campaign rule
const createSchema = Joi.object()
  .keys(Object.assign({
    ...rule,
    platforms: rule.platforms.default([]),
    disabled: rule.disabled.default(false),
    urgent: rule.urgent.default(false),
    dismissable_flag: rule.dismissable_flag.default(true),
    status: rule.status.default('draft'),
    type: ruleTypeGet,
  }))
  .fork([ 'name', 'start_at', 'end_at', 'container', 'content_space', 'content_type', 'content_id', 'pages', 'application' ], (schema) => schema.required());

const updateSchema = Joi.object()
  .keys({ ...rule })
  .fork([ 'status', 'application' ], (schema) => schema.required());

// get list of campaigns
const getListSchema = Joi.object()
  .keys({
    platform,
    page,
    container,
    disabled,
    status,
    now: Joi.date().iso().raw(true),
    offset: Joi.number().min(0).default(0),
    app_version: Joi.string().min(1),
    limit: Joi.number().min(1).default(50),
    sort: Joi.string().min(2).regex(/^[+-]?[a-z_]+$/),
    external_ref: customJoi.stringArray().min(1).items(externalRef.allow('-')),
    name: Joi.string().min(1).max(100),
    search: Joi.string().min(1).max(100),
    created_by: scotiaUser,
    updated_by: scotiaUser,
    application,
    start_date_lt: Joi.date().iso().raw(true),
    start_date_gt: Joi.date().iso().raw(true),
    end_date_lt: Joi.date().iso().raw(true),
    end_date_gt: Joi.date().iso().raw(true),
    type: ruleTypeGet,
    campaign_id: Joi.string(),
  });

const ruleExportSchema = (
  Joi.object()
    .keys({
      platform,
      container,
      status,
      disabled,
      now: Joi.date().iso().raw(true),
      offset: Joi.number().min(0).default(0),
      app_version: Joi.string().min(1),
      limit: Joi.number().min(1).default(50),
      sort: Joi.string().min(2).regex(/^[+-]?[a-z_]+$/),
      external_ref: customJoi.stringArray().min(1).items(externalRef.allow('-')),
      name: Joi.string().min(1).max(100),
      search: Joi.string().min(1).max(100),
      start_date_lt: Joi.date().iso().raw(true),
      start_date_gt: Joi.date().iso().raw(true),
      end_date_lt: Joi.date().iso().raw(true),
      end_date_gt: Joi.date().iso().raw(true),
      created_by: scotiaUser,
      updated_by: scotiaUser,
      application,
      export: customJoi.stringArray().min(1).items(Joi.string().valid('external_ref', 'name', 'content_id', 'date', 'status', 'platforms', 'application', 'pages', 'container')),
    })
);
// get campaign by its id
const getPathSchema = Joi.object()
  .keys({ ruleId })
  .fork([ 'ruleId' ], (schema) => schema.required());

// get campaign by its id
const optionalGetPathSchema = Joi.object()
  .keys({ ruleId });

const updatePartialSchema = Joi.object()
  .keys({ ...rule, type: ruleTypeUpdate })
  .fork([ 'status' ], (schema) => schema.required());

module.exports = {
  createSchema,
  getListSchema,
  getPathSchema,
  optionalGetPathSchema,
  updatePartialSchema,
  updateSchema,
  ruleExportSchema,
  VALIDATION_TYPE,
  schemaValidationMiddleware,
};
