const UpdatePartialCampaign = require('./updatePartialCampaign');
const { CustomError, BadRequestError } = require('../../error');

// mock Campaign API client
const mockCampaignApiClient = {
  patchCampaign: jest.fn(),
  getCampaign: jest.fn(),
};
// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn().mockReturnValue({ json: mockJson });
const mockRes = {
  status: mockStatus,
  locals: {
    validatedParams: {},
    validatedBody: {},
    auth: { sid: 's1234567' },
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
    },
  },
};

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

const mockRuleService = {
  validateUsers: jest.fn(),
  assignUserToCampaign: jest.fn(),
  assignUserListToCampaign: jest.fn(),
  removeUsersFromCampaign: jest.fn(),
};

describe('Campaign Rules: PATCH /campaign-rules/:ruleId', () => {
  beforeEach(() => {
    mockCampaignApiClient.getCampaign.mockClear();
    mockCampaignApiClient.patchCampaign.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
    mockLogger.info.mockClear();
    mockRuleService.validateUsers.mockClear();
    mockRuleService.assignUserToCampaign.mockClear();
    mockRuleService.assignUserListToCampaign.mockClear();
    mockRuleService.removeUsersFromCampaign.mockClear();
  });

  test('should not return an error on valid request', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: '12345',
      application: 'nova',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-12-31T23:59:59Z',
      container: 'PRE_LOGIN',
      content_space: '4szkpx38resvm',
      content_type: 'notification',
      content_id: '6zGhwB6kz6ii6IQ2Yg86wy',
      'assignees': [ 's1234567' ],
      status: 'submitted',
    };
    const mockResponse = {
      data: { id: mockParams.ruleId, ...mockBody },
    };
    const mockCampaignBeforeResponse = {
      status: 200,
      data: {
        'assignees': [ 's1234567' ],
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'submitted',
        'disabled': false,
        'created_by': 's7435292',
        'updated_by': 's7435292',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-03T19:50:51.250Z',
      },
    };
    mockCampaignApiClient.getCampaign.mockResolvedValueOnce(mockCampaignBeforeResponse);
    mockCampaignApiClient.patchCampaign.mockResolvedValueOnce(mockResponse);
    mockRuleService.validateUsers.mockResolvedValueOnce({ success: true });
    mockRuleService.removeUsersFromCampaign.mockResolvedValueOnce({});

    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignApiClient, mockRuleService);

    await updatePartialCampaign({ params: mockParams, body: mockBody }, mockRes, mockNext);
    expect(mockCampaignApiClient.patchCampaign).toBeCalled();
    expect(mockCampaignApiClient.patchCampaign).toBeCalledTimes(1);
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('should return error when campaignAPI client unavailable', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = { status: 'draft', name: '12345', application: 'nova' };
    const mockResponse = {
      response: {
        status: 400,
        data: {
          code: 'HTTP_BAD_REQUEST',
          message: 'Database error',
          uuid: '04d69d4d-4c41-4373-b7f5-9b351a1f345d',
          timestamp: '2018-08-28T20:43:59.319Z',
          metadata: [ 'The duplicate key value is (sample name #38).' ],
        },
      },
    };
    const mockCampaignBefore = {
      data: {
        'name': 'dpalma-may-37-3554',
        'application': 'nova',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'status': 'draft',
        'external_ref': 'asdfc1234',
        'pages': [
          'activities',
        ],
        'urgent': false,
        'assignees': [ 's123456', 's1234567' ],
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce(mockCampaignBefore);
    mockCampaignApiClient.patchCampaign.mockRejectedValueOnce(mockResponse);
    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignApiClient, mockRuleService);

    const mockResLocal = Object.assign({}, mockRes);
    mockResLocal.status = jest.fn();

    await updatePartialCampaign({ params: mockParams, body: mockBody }, mockResLocal, mockNext);

    expect(mockCampaignApiClient.patchCampaign).toBeCalled();
    expect(mockCampaignApiClient.patchCampaign).toHaveBeenCalledTimes(1);
    expect(mockStatus).toHaveBeenCalledTimes(0);
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
  });

  test('should return Forbidden error if user does not have required permissions', async() => {
    const localMockRes = {
      status: mockStatus,
      locals: {
        validatedParams: {},
        validatedBody: {},
        auth: { sid: 's1234567' },
        user: {
          id: 1,
          role_id: 1,
          permissions: [ 'notification_manager' ],
        },
      },
    };
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: '12345',
      'assignees': [ 's1234567' ],
      status: 'draft',
    };
    const mockCampaignBeforeResponse = {
      status: 200,
      data: {
        'assignees': [ 's1234567' ],
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'submitted',
        'disabled': false,
        'created_by': 's7435292',
        'updated_by': 's7435292',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-03T19:50:51.250Z',
      },
    };
    mockCampaignApiClient.getCampaign.mockReturnValueOnce(mockCampaignBeforeResponse);

    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignApiClient, mockRuleService);
    await updatePartialCampaign({ params: mockParams, body: mockBody }, localMockRes, mockNext);

    expect(mockCampaignApiClient.patchCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
  });

  test('should return with BadRequest when trying to update draft campaign with assignees', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: '12345',
      'assignees': [ 's1234567' ],
      status: 'draft',
    };
    const localMockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: mockBody,
      },
    };
    const mockCampaignBeforeResponse = {
      status: 200,
      data: {
        'assignees': [ 's1234567' ],
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'draft',
        'disabled': false,
        'created_by': 's7435292',
        'updated_by': 's7435292',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-03T19:50:51.250Z',
      },
    };
    mockCampaignApiClient.getCampaign.mockReturnValueOnce(mockCampaignBeforeResponse);
    mockRuleService.validateUsers.mockResolvedValueOnce({ success: true });
    mockRuleService.removeUsersFromCampaign.mockResolvedValueOnce({});

    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignApiClient, mockRuleService);
    await updatePartialCampaign({ params: mockParams, body: mockBody }, localMockRes, mockNext);

    expect(mockCampaignApiClient.patchCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
    expect(mockNext.mock.calls[0][0].message).toBe('Cannot assign users while campaign is draft status.');
  });

  test('should return with BadRequest when trying to update campaign from subitted/reviewed to draft with assignees', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: '12345',
      'assignees': [ 's1234567' ],
      status: 'draft',
    };
    const localMockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: mockBody,
      },
    };
    const mockCampaignBeforeResponse = {
      status: 200,
      data: {
        'assignees': [ 's1234567' ],
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'submitted',
        'disabled': false,
        'created_by': 's7435292',
        'updated_by': 's7435292',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-03T19:50:51.250Z',
      },
    };
    mockCampaignApiClient.getCampaign.mockReturnValueOnce(mockCampaignBeforeResponse);
    mockRuleService.validateUsers.mockResolvedValueOnce({ success: true });
    mockRuleService.removeUsersFromCampaign.mockResolvedValueOnce({});

    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignApiClient, mockRuleService);
    await updatePartialCampaign({ params: mockParams, body: mockBody }, localMockRes, mockNext);

    expect(mockCampaignApiClient.patchCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
  });

  test('should return with BadRequest when trying to update campaign to published with assignees', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: '12345',
      'assignees': [ 's1234567' ],
      status: 'published',
    };
    const localMockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: mockBody,
      },
    };
    const mockCampaignBeforeResponse = {
      status: 200,
      data: {
        'assignees': [ 's1234567' ],
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'reviewed',
        'disabled': false,
        'created_by': 's7435292',
        'updated_by': 's7435292',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-03T19:50:51.250Z',
      },
    };
    mockCampaignApiClient.getCampaign.mockReturnValueOnce(mockCampaignBeforeResponse);
    mockRuleService.validateUsers.mockResolvedValueOnce({ success: true });
    mockRuleService.removeUsersFromCampaign.mockResolvedValueOnce({});

    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignApiClient, mockRuleService);
    await updatePartialCampaign({ params: mockParams, body: mockBody }, localMockRes, mockNext);

    expect(mockCampaignApiClient.patchCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
  });

  test('should return with BadRequest when trying to update draft campaign with assignees when updated_by does not include assignees', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: '12345',
      'assignees': [ 's1234567' ],
      status: 'draft',
    };
    const localMockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: mockBody,
      },
    };
    const mockCampaignBeforeResponse = {
      status: 200,
      data: {
        'assignees': [ 's1234567' ],
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'submitted',
        'disabled': false,
        'created_by': 's1234567',
        'updated_by': 's7435292',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-03T19:50:51.250Z',
      },
    };
    mockCampaignApiClient.getCampaign.mockReturnValueOnce(mockCampaignBeforeResponse);
    mockRuleService.validateUsers.mockResolvedValueOnce({ success: true });
    mockRuleService.removeUsersFromCampaign.mockResolvedValueOnce({});

    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignApiClient, mockRuleService);
    await updatePartialCampaign({ params: mockParams, body: mockBody }, localMockRes, mockNext);

    expect(mockCampaignApiClient.patchCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
    expect(mockNext.mock.calls[0][0].message).toBe('Cannot assign users while campaign is draft status');
  });

  test('should return Invalid users detected when premissions fail', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: '12345',
      'assignees': [ 's1234567' ],
      status: 'reviewed',
      disabled: true,
    };
    const localMockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: mockBody,
      },
    };
    const mockCampaignBeforeResponse = {
      status: 200,
      data: {
        'assignees': [ 's1234567' ],
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'published',
        'disabled': false,
        'created_by': 's1234567',
        'updated_by': 's7435292',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-03T19:50:51.250Z',
      },
    };
    const mockRuleServiceLocal = {
      validateUsers: jest.fn(),
      assignUserToCampaign: jest.fn(),
      assignUserListToCampaign: jest.fn(),
      removeUsersFromCampaign: jest.fn(),
    };
    mockCampaignApiClient.getCampaign.mockReturnValueOnce(mockCampaignBeforeResponse);
    mockRuleServiceLocal.validateUsers.mockResolvedValueOnce({ success: false });
    // mockRuleService.removeUsersFromCampaign.mockResolvedValueOnce({});

    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignApiClient, mockRuleServiceLocal);
    await updatePartialCampaign({ params: mockParams, body: mockBody }, localMockRes, mockNext);

    expect(mockCampaignApiClient.patchCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
    expect(mockNext.mock.calls[0][0].message).toContain('Invalid users detected');
  });

  test('should return Invalid users detected when premissions fail CCAU', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: '12345',
      'assignees': [ 's1234567' ],
      status: 'reviewed',
      disabled: true,
    };
    const localMockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: mockBody,
      },
    };
    const mockCampaignBeforeResponse = {
      status: 200,
      data: {
        'assignees': [ 's1234567' ],
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'published',
        'disabled': false,
        'created_by': 's1234567',
        'updated_by': 's7435292',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-03T19:50:51.250Z',
        type: 'ccau_campaign',
      },
    };
    const mockRuleServiceLocal = {
      validateUsers: jest.fn(),
      assignUserToCampaign: jest.fn(),
      assignUserListToCampaign: jest.fn(),
      removeUsersFromCampaign: jest.fn(),
    };
    mockCampaignApiClient.getCampaign.mockReturnValueOnce(mockCampaignBeforeResponse);
    mockRuleServiceLocal.validateUsers.mockResolvedValueOnce({ success: false });
    // mockRuleService.removeUsersFromCampaign.mockResolvedValueOnce({});

    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignApiClient, mockRuleServiceLocal);
    await updatePartialCampaign({ params: mockParams, body: mockBody }, localMockRes, mockNext);

    expect(mockCampaignApiClient.patchCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
    expect(mockNext.mock.calls[0][0].message).toContain('Invalid users detected');
  });

  test('should assign users if premission suceeds', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: '12345',
      'assignees': [ 's1234567' ],
      status: 'reviewed',
      disabled: true,
    };
    const localMockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: mockBody,
      },
    };
    const mockCampaignBeforeResponse = {
      status: 200,
      data: {
        'assignees': [ 's1234567' ],
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'published',
        'disabled': false,
        'created_by': 's1234567',
        'updated_by': 's7435292',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-03T19:50:51.250Z',
        type: 'ccau_campaign',
      },
    };

    mockCampaignApiClient.getCampaign.mockReturnValueOnce(mockCampaignBeforeResponse);
    mockRuleService.validateUsers.mockResolvedValueOnce({ success: true });
    mockRuleService.removeUsersFromCampaign.mockResolvedValueOnce({});
    mockCampaignApiClient.patchCampaign.mockResolvedValueOnce({
      data: { id: mockParams.ruleId, ...mockBody },
    });

    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignApiClient, mockRuleService);
    await updatePartialCampaign({ params: mockParams, body: mockBody }, localMockRes, mockNext);

    expect(mockCampaignApiClient.patchCampaign).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('should remove and assign users when campaign is in draft', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = {
      name: '12345',
      'assignees': [ 's1234567' ],
      status: 'reviewed',
      disabled: true,
    };
    const localMockRes = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedParams: mockParams,
        validatedBody: { ...mockBody, status: 'draft' },
      },
    };
    const mockCampaignBeforeResponse = {
      status: 200,
      data: {
        'assignees': [ 's1234567' ],
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'published',
        'disabled': false,
        'created_by': 's1234567',
        'updated_by': 's7435292',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-03T19:50:51.250Z',
        type: 'ccau_campaign',
      },
    };

    mockCampaignApiClient.getCampaign.mockReturnValueOnce(mockCampaignBeforeResponse);
    mockRuleService.validateUsers.mockResolvedValueOnce({ success: true });
    mockRuleService.removeUsersFromCampaign.mockResolvedValueOnce({});
    mockCampaignApiClient.patchCampaign.mockResolvedValueOnce({
      data: { id: mockParams.ruleId, ...mockBody },
    });

    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignApiClient, mockRuleService);
    await updatePartialCampaign({ params: mockParams, body: mockBody }, localMockRes, mockNext);

    expect(mockCampaignApiClient.patchCampaign).toBeCalled();
    expect(mockNext).not.toBeCalled();
    mockRuleService.validateUsers.mockResolvedValueOnce({ success: true });
    expect(mockRuleService.removeUsersFromCampaign).toBeCalled();
    expect(mockRuleService.assignUserToCampaign).toBeCalled();
  });

  test('should return error when request fails', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = { status: 'draft', name: '12345', application: 'nova' };
    const mockResponse = {
      request: {
        message: 'request failed',
      },
    };
    const mockCampaignBefore = {
      data: {
        'name': 'dpalma-may-37-3554',
        'application': 'nova',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'status': 'draft',
        'external_ref': 'asdfc1234',
        'pages': [
          'activities',
        ],
        'urgent': false,
        'assignees': [ 's123456', 's1234567' ],
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce(mockCampaignBefore);
    mockCampaignApiClient.patchCampaign.mockRejectedValueOnce(mockResponse);
    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignApiClient, mockRuleService);

    const mockResLocal = Object.assign({}, mockRes);
    mockResLocal.status = jest.fn();

    await updatePartialCampaign({ params: mockParams, body: mockBody }, mockResLocal, mockNext);

    expect(mockCampaignApiClient.patchCampaign).toBeCalled();
    expect(mockCampaignApiClient.patchCampaign).toHaveBeenCalledTimes(1);
    expect(mockStatus).toHaveBeenCalledTimes(0);
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    // expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(CustomError);
    // eslint-disable-next-line no-useless-escape
    expect(mockNext.mock.calls[0][0].message).toBe(JSON.stringify({
      message: 'request failed',
    }));
  });

  test('should return error when request fails', async() => {
    const mockParams = { ruleId: '123456ab' };
    const mockBody = { status: 'draft', name: '12345', application: 'nova' };
    const mockResponse = {
      message: 'request failed',
    };
    const mockCampaignBefore = {
      data: {
        'name': 'dpalma-may-37-3554',
        'application': 'nova',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'status': 'draft',
        'external_ref': 'asdfc1234',
        'pages': [
          'activities',
        ],
        'urgent': false,
        'assignees': [ 's123456', 's1234567' ],
      },
    };

    mockCampaignApiClient.getCampaign.mockResolvedValueOnce(mockCampaignBefore);
    mockCampaignApiClient.patchCampaign.mockRejectedValueOnce(mockResponse);
    const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignApiClient, mockRuleService);

    const mockResLocal = Object.assign({}, mockRes);
    mockResLocal.status = jest.fn();

    await updatePartialCampaign({ params: mockParams, body: mockBody }, mockResLocal, mockNext);

    expect(mockCampaignApiClient.patchCampaign).toBeCalled();
    expect(mockCampaignApiClient.patchCampaign).toHaveBeenCalledTimes(1);
    expect(mockStatus).toHaveBeenCalledTimes(0);
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    // expect(mockNext.mock.calls[0][0] instanceof CustomError).toBe(true);
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(CustomError);
    // eslint-disable-next-line no-useless-escape
    expect(mockNext.mock.calls[0][0].message).toBe(JSON.stringify('request failed'));
  });

  // test('Should re-assign campaign rule to creator when campaign-rule is rejected', async()=>{
  //   const mockParams = { ruleId: '123456ab' };
  //   const mockCampaignBefore = {
  //     "assignees": [
  //       {
  //         "full_name": "Campaign Rule Approver",
  //         "sid": "s912334578"
  //       }
  //     ],
  //     "id": "SYV6DqeX0qxP",
  //     "name": "abcd-123-dpalma",
  //     "start_at": "2019-08-14T04:00:00.000Z",
  //     "end_at": "2019-08-15T04:00:00.000Z",
  //     "content_space": "4szkx38resvm",
  //     "content_type": "standingCampaignTemplate1Details",
  //     "content_id": "2eozqemlHm8s8UQsaccwei",
  //     "container": "priority-box",
  //     "pages": [
  //       "accounts"
  //     ],
  //     "external_ref": "abc123",
  //     "platforms": [
  //       "ios",
  //       "android"
  //     ],
  //     "app_version": null,
  //     "urgent": false,
  //     "status": "submitted",
  //     "disabled": false,
  //     "created_by": "s7435292",
  //     "updated_by": "s7435292",
  //     "created_at": "2019-08-13T17:36:13.153Z",
  //     "updated_at": "2019-08-14T15:58:43.857Z"
  //   };
  //   const mockBody = {
  //     'status': 'draft',
  //   };

  //   const mockUpdatedCampaignResponse = {
  //     "assignees": [
  //       {
  //         "full_name": "Donovan Palma",
  //         "sid": "s7435292"
  //       }
  //     ],
  //     "id": "SYV6DqeX0qxP",
  //     "name": "abcd-123-dpalma",
  //     "start_at": "2019-08-14T04:00:00.000Z",
  //     "end_at": "2019-08-15T04:00:00.000Z",
  //     "content_space": "4szkx38resvm",
  //     "content_type": "standingCampaignTemplate1Details",
  //     "content_id": "2eozqemlHm8s8UQsaccwei",
  //     "container": "priority-box",
  //     "pages": [
  //       "accounts"
  //     ],
  //     "external_ref": "abc123",
  //     "platforms": [
  //       "ios",
  //       "android"
  //     ],
  //     "app_version": null,
  //     "urgent": false,
  //     "status": "draft",
  //     "disabled": false,
  //     "created_by": "s7435292",
  //     "updated_by": "s7435292",
  //     "created_at": "2019-08-13T17:36:13.153Z",
  //     "updated_at": "2019-08-14T16:05:13.607Z"
  //   };

  //   mockCampaignApiClient.getCampaign.mockResolvedValueOnce({ data: mockCampaignBefore });
  //   mockCampaignApiClient.patchCampaign.mockResolvedValueOnce(mockUpdatedCampaignResponse);
  //   mockRuleService.validateUsers.mockReturnValueOnce({ success: true });
  //   mockRuleService.removeUsersFromCampaign.mockResolvedValueOnce({});
  //   mockRuleService.assignUserToCampaign.mockResolvedValueOnce([{ full_name: 'Test User', sid: mockCampaignBefore.created_by } ]);

  //   debugger;

  //   const updatePartialCampaign = UpdatePartialCampaign(mockLogger, mockCampaignApiClient, mockRuleService);

  //   await updatePartialCampaign({ params: mockParams, body: mockBody }, mockRes, mockNext);

  //   expect(mockCampaignApiClient.patchCampaign).toBeCalled();
  //   expect(mockStatus).toBeCalled();
  //   expect(mockJson).toBeCalled();
  //   expect(mockNext).not.toBeCalled();
  //   expect(mockJson.mock.calls[0][0].assignees !== undefined).toBe(true);
  //   expect(mockJson.mock.calls[0][0].assignees.some(function(item) {
  //     return item.sid === mockCampaignBefore.created_by;
  //   }));
  // });
});
