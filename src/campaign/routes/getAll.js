const { CustomError } = require('../../error');
const { uniq } = require('lodash');

const EXCLUDED_APPS = [ 'sol', 'storefront' ];

const getAll = function(campaignService) {
  return async function(req, res, next) {
    const value = res.locals.validatedQuery;

    console.log('**************************************************************************\n\n\n');
    console.log('get all env');
    console.log(process.env);
    console.log('\n\n\n**************************************************************************');

    try {
      // filter rules by pages, containers, & rule sub types that the user has access to
      const { access } = res.locals.user;
      const { containers, pages, ruleSubTypes } = access.campaigns;
      const accessContainers = {};
      const accessPages = {};
      const accessRuleSubTypes = {};

      Object.keys(containers).filter(app => !EXCLUDED_APPS.includes(app)).forEach(app => {
        accessContainers[app] = uniq([ ...(containers[app].view || []), ...(containers[app].manage || []) ]);
      });
      Object.keys(pages).filter(app => !EXCLUDED_APPS.includes(app)).forEach(app => {
        accessPages[app] = uniq([ ...(pages[app].view || []), ...(pages[app].manage || []) ]);
      });
      Object.keys(ruleSubTypes).filter(app => !EXCLUDED_APPS.includes(app)).forEach(app => {
        accessRuleSubTypes[app] = uniq([
          ...(ruleSubTypes[app].view ? ruleSubTypes[app].view.map(val => val.toUpperCase()) : []),
          ...(ruleSubTypes[app].manage ? ruleSubTypes[app].manage.map(val => val.toUpperCase()) : []),
        ]);
      });

      if (!Object.keys(accessContainers).length || !Object.keys(accessPages).length || !Object.keys(accessRuleSubTypes).length) {
        return res.status(200).json({ items: [], total: 0 });
      }

      const accessQuery = {
        accessContainers,
        accessPages,
        accessRuleSubTypes,
      };

      // Retain the original query params to use in the response object
      const offset = { ...value }.offset;
      const limit = { ...value }.limit;
      // Reset the query params since the service does not have built-in pagination feature
      value.offset = 0;
      value.limit = offset + limit;
      // Fetch all rules starting from 0 to offset + limit
      let allRules = await campaignService.getAllCampaignsByAccess(value, accessQuery);
      // Set the retained original query params to the return object for frontend to use
      allRules.data.limit = limit;
      allRules.data.offset = offset;
      // Slice and return only the current page
      allRules.data.items = allRules.data.items.slice(offset, offset + limit);
      res.status(200).json(allRules.data);
    } catch (err) {
      if (err.response) {
        if (err.response.status < 500) {
          res.status(err.response.status).json(err.response.data);
        } else {
          next(new CustomError(err.response.status, JSON.stringify(err.response.data)));
        }
      } else {
        next(new CustomError(500, err.toString()));
      }
    }
  };
};

module.exports = getAll;
