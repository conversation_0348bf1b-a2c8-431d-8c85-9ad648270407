const CreateCampaign = require('./createCampaign');
const { BadRequestError, ForbiddenError, CustomError } = require('../../error');

const mockCampaignApiClient = {
  createCampaign: jest.fn(),
};

const mockRuleService = {
  validateUsers: jest.fn(),
  assignUserToCampaign: jest.fn(),
  assignUserListToCampaign: jest.fn(),
};

const mockUserService = {
  getUser: jest.fn().mockReturnValue([ 's000001' ]),
};
const mockApplicationsService = {
  getApplicationByName: jest.fn().mockReturnValue({ ruleTypes: [ 'campaign' ] }),
};
// mock next
const mockNext = jest.fn();
// mock Response object
const mockJson = jest.fn();
const mockStatus = jest.fn();
mockStatus.mockReturnValue({ json: mockJson });
const mockRes = {
  status: mockStatus,
  locals: {
    auth: { sid: 's1234567' },
    validatedBody: {},
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
    },
  },
};
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

describe('Campaign Rules: routes > createCampaign', () => {
  beforeEach(() => {
    mockCampaignApiClient.createCampaign.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
    mockRuleService.validateUsers.mockClear();
    mockRuleService.assignUserToCampaign.mockClear();
    mockRuleService.assignUserListToCampaign.mockClear();
  });

  test('Should return valid response for valid campaign data - submitted campaign', async() => {
    const mockBody = {
      name: 'sample name',
      application: 'nova',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      content_space: '4szkx38resvm',
      pages: [ 'my-activity' ],
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      platforms_targeting: [
        { platform: 'ios',
          items: [
            { app_version: '1.0.0 - 2.2.0-alpha', os_version: '1 - 1.0.3', device_model: 'iPhone11,2' },
            { app_version: '<=32.0.0', os_version: '4.3' },
          ] },
        { platform: 'android', items: [] },
      ],
      status: 'submitted',
      assignees: [ 's123456', 's912341' ],
      type: 'campaign',
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      status: 200,
      data: {
        'assignees': [
          {
            'assigned_admin_user_id': 1,
            'campaign_rule_id': 'ZgFK7zPnEGr9',
          },
        ],
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'application': 'nova',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'submitted',
        'disabled': false,
        'created_by': 's7435292',
        'updated_by': 's7435292',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-03T19:50:51.250Z',
      },
    };

    mockRuleService.validateUsers.mockReturnValueOnce({ success: true });
    mockRuleService.assignUserToCampaign.mockReturnValueOnce({});
    mockCampaignApiClient.createCampaign.mockReturnValueOnce(fakeResponse);

    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockUserService, mockApplicationsService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClient.createCampaign).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('should support bad request response from Campaign API client', async() => {
    const mockBody = {
      name: 'sample name',
      application: 'nova',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      pages: [ 'my-activity' ],
      content_space: '4szkx38resvm',
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      status: 'draft',
      type: 'campaign',
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      response: {
        status: 400,
        data: {
          code: 'HTTP_BAD_REQUEST',
          message: 'Database error',
          uuid: '04d69d4d-4c41-4373-b7f5-9b351a1f345d',
          timestamp: '2018-08-28T20:43:59.319Z',
          metadata: [ 'The duplicate key value is (sample name #38).' ],
        },
      },
    };

    mockCampaignApiClient.createCampaign.mockRejectedValueOnce(fakeResponse);

    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockUserService, mockApplicationsService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClient.createCampaign).toBeCalled();
    expect(mockNext).toBeCalled();
  });

  test('should call next with Error on 500+ response status', async() => {
    const mockBody = {
      name: 'sample name',
      application: 'nova',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      content_space: '4szkx38resvm',
      pages: [ 'my-activity' ],
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      status: 'draft',
      type: 'campaign',
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      response: {
        status: 500,
        data: {
          code: 'HTTP_INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
          uuid: '04d69d4d-4c41-4373-b7f5-9b351a1f345d',
          timestamp: '2018-08-28T20:43:59.319Z',
          metadata: [],
        },
      },
    };

    mockCampaignApiClient.createCampaign.mockRejectedValueOnce(fakeResponse);
    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockUserService, mockApplicationsService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClient.createCampaign).toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls.length).toEqual(1);
    expect(mockNext.mock.calls[0][0] instanceof Error).toBe(true);
  });

  test('should call next with BadRequest (Invalid users detected) if user assignee user validation fails', async() => {
    const mockBody = {
      name: 'sample name',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      content_space: '4szkx38resvm',
      pages: [ 'my-activity' ],
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      status: 'submitted',
      assignees: [ 's123456', 's912341' ],
      type: 'campaign',
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      status: 200,
      data: {},
    };

    mockRuleService.validateUsers.mockReturnValueOnce({ success: false });
    mockCampaignApiClient.createCampaign.mockReturnValueOnce(fakeResponse);

    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockUserService, mockApplicationsService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClient.createCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls.length).toEqual(1);
    expect(mockNext.mock.calls[0][0].constructor.name === BadRequestError.name).toBe(true);
  });

  test('Should call next with CustomError if ruleService throws exception', async() => {
    const mockBody = {
      name: 'sample name',
      application: 'nova',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      content_space: '4szkx38resvm',
      pages: [ 'my-activity' ],
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      status: 'submitted',
      assignees: [ 's000001' ],
      type: 'campaign',
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      status: 200,
      data: {},
    };

    mockRuleService.validateUsers.mockRejectedValueOnce({
      code: 500,
    });

    mockCampaignApiClient.createCampaign.mockReturnValueOnce(fakeResponse);

    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockUserService, mockApplicationsService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClient.createCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls.length).toEqual(1);
  });

  test('Should respond with BadRequestError - Cannot assign user to a campaign rule in draft status.', async() => {
    const mockBody = {
      name: 'sample name',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      content_space: '4szkx38resvm',
      pages: [ 'my-activity' ],
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      status: 'draft',
      assignees: [ 's123456', 's912341' ],
      type: 'campaign',
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      status: 200,
      data: {},
    };

    mockCampaignApiClient.createCampaign.mockReturnValueOnce(fakeResponse);

    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockUserService, mockApplicationsService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClient.createCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls.length).toEqual(1);
    expect(mockNext.mock.calls[0][0].constructor.name === BadRequestError.name).toBe(true);
  });

  test('Should respond with BadRequestError - Invalid application', async() => {
    const mockBody = {
      name: 'sample name',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      content_space: '4szkx38resvm',
      pages: [ 'my-activity' ],
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      status: 'draft',
      assignees: [ 's123456', 's912341' ],
      type: 'campaign',
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      status: 200,
      data: {},
    };

    mockCampaignApiClient.createCampaign.mockReturnValueOnce(fakeResponse);
    mockApplicationsService.getApplicationByName.mockReturnValueOnce(null);

    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockUserService, mockApplicationsService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClient.createCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls.length).toEqual(1);
    expect(mockNext.mock.calls[0][0].constructor.name === BadRequestError.name).toBe(true);
  });

  test('Should respond with BadRequestError - Invalid combination of application & rule type', async() => {
    const mockBody = {
      name: 'sample name',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      content_space: '4szkx38resvm',
      pages: [ 'my-activity' ],
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      status: 'draft',
      assignees: [ 's123456', 's912341' ],
      type: 'campaign',
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      status: 200,
      data: {},
    };

    mockCampaignApiClient.createCampaign.mockReturnValueOnce(fakeResponse);
    mockApplicationsService.getApplicationByName.mockReturnValueOnce({ ruleTypes: [ 'ccau_campaign' ] });

    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockUserService, mockApplicationsService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClient.createCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls.length).toEqual(1);
    expect(mockNext.mock.calls[0][0].constructor.name === BadRequestError.name).toBe(true);
  });

  test('Should return valid response for valid campaign data - draft campaign', async() => {
    const mockBody = {
      name: 'sample name',
      application: 'nova',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      content_space: '4szkx38resvm',
      pages: [ 'my-activity' ],
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      status: 'draft',
      type: 'campaign',
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      status: 200,
      data: {
        'assignees': [
          {
            'assigned_admin_user_id': 1,
            'campaign_rule_id': 'ZgFK7zPnEGr9',
          },
        ],
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'submitted',
        'disabled': false,
        'created_by': 's7435292',
        'updated_by': 's7435292',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-03T19:50:51.250Z',
      },
    };

    mockRuleService.assignUserToCampaign.mockReturnValueOnce({});
    mockCampaignApiClient.createCampaign.mockReturnValueOnce(fakeResponse);

    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockUserService, mockApplicationsService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClient.createCampaign).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('CCAU Should return valid response for valid campaign data - submitted campaign', async() => {
    const mockBody = {
      name: 'sample name',
      type: 'ccau_campaign',
      application: 'nova',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      content_space: '4szkx38resvm',
      pages: [ 'my-activity' ],
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      platforms_targeting: [
        { platform: 'ios',
          items: [
            { app_version: '1.0.0 - 2.2.0-alpha', os_version: '1 - 1.0.3', device_model: 'iPhone11,2' },
            { app_version: '<=32.0.0', os_version: '4.3' },
          ] },
        { platform: 'android', items: [] },
      ],
      status: 'submitted',
      assignees: [ 's123456', 's912341' ],
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      status: 200,
      data: {
        'assignees': [
          {
            'assigned_admin_user_id': 1,
            'campaign_rule_id': 'ZgFK7zPnEGr9',
          },
        ],
        'id': 'ZgFK7zPnEGr9',
        'name': 'dpalma-may-37-3554',
        'type': 'ccau_campaign',
        'application': 'nova',
        'start_at': '2019-04-29T04:00:00.000Z',
        'end_at': '2019-04-30T04:00:00.000Z',
        'content_space': '4szkx38resvm',
        'content_type': 'standingCampaign',
        'content_id': '0qjYVZSfhTiDAKpHwRU43',
        'container': 'my-activity',
        'pages': [
          'activities',
        ],
        'external_ref': 'asdfc1234',
        'platforms': [
          'ios',
          'android',
        ],
        'app_version': null,
        'urgent': false,
        'status': 'submitted',
        'disabled': false,
        'created_by': 's7435292',
        'updated_by': 's7435292',
        'created_at': '2019-06-03T19:50:51.250Z',
        'updated_at': '2019-06-03T19:50:51.250Z',
      },
    };

    mockRuleService.validateUsers.mockReturnValueOnce({ success: true });
    mockRuleService.assignUserToCampaign.mockReturnValueOnce({});
    mockCampaignApiClient.createCampaign.mockReturnValueOnce(fakeResponse);
    mockApplicationsService.getApplicationByName.mockReturnValueOnce({ ruleTypes: [ 'ccau_campaign' ] });

    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockUserService, mockApplicationsService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClient.createCampaign).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    expect(mockNext).not.toBeCalled();
  });

  test('Should respond with ForbiddenError - when campaign is disabled', async() => {
    const mockBody = {
      name: 'sample name',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      content_space: '4szkx38resvm',
      pages: [ 'my-activity' ],
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      status: 'draft',
      assignees: [ 's123456', 's912341' ],
      type: 'campaign',
    };
    const mockReq = {
      body: { ...mockBody, disabled: true },
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: { ...mockBody },
      },
    };
    const fakeResponse = {
      status: 200,
      data: {},
    };

    mockCampaignApiClient.createCampaign.mockReturnValueOnce(fakeResponse);
    mockApplicationsService.getApplicationByName.mockReturnValueOnce(null);

    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockUserService, mockApplicationsService);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClient.createCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls.length).toEqual(1);
    expect(mockNext.mock.calls[0][0].constructor.name === ForbiddenError.name).toBe(true);
  });

  test('Should return with BadRequestError - Campaign must include assignees.', async() => {
    const mockBody = {
      name: 'sample name',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      content_space: '4szkx38resvm',
      pages: [ 'my-activity' ],
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      status: 'submitted',
      // assignees: [ 's123456', 's912341' ],
      type: 'campaign',
    };
    const mockReq = {
      body: { ...mockBody },
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: { ...mockBody },
      },
    };
    const fakeResponse = {
      status: 200,
      data: {},
    };

    const mockApplicationsServiceLocal = {
      getApplicationByName: jest.fn().mockReturnValue({ ruleTypes: [ 'campaign' ] }),
    };

    mockCampaignApiClient.createCampaign.mockReturnValueOnce(fakeResponse);

    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClient, mockRuleService, mockUserService, mockApplicationsServiceLocal);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClient.createCampaign).not.toBeCalled();
    expect(mockStatus).not.toBeCalled();
    expect(mockJson).not.toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls.length).toEqual(1);
    expect(mockNext.mock.calls[0][0].constructor.name === BadRequestError.name).toBe(true);
    expect(mockNext.mock.calls[0][0].message).toBe('Campaign must include assignees.');
  });

  test('should return error with data object', async() => {
    const mockBody = {
      name: 'error_object',
      application: 'nova',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      pages: [ 'my-activity' ],
      content_space: '4szkx38resvm',
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      status: 'draft',
      type: 'campaign',
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      response: {
        status: 422,
        data: {
          uuid: '10',
          error: 'api service error',
        },
      },
    };

    const mockCampaignApiClientLocal = {
      createCampaign: jest.fn().mockReturnValue(Promise.reject(fakeResponse)),
    };

    const mockApplicationsServiceLocal = {
      getApplicationByName: jest.fn().mockReturnValue({ ruleTypes: [ 'campaign' ] }),
    };

    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClientLocal, mockRuleService, mockUserService, mockApplicationsServiceLocal);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClientLocal.createCampaign).toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(CustomError);
    expect(mockNext.mock.calls[0][0].message).toContain(JSON.stringify({
      uuid: '10',
      error: 'api service error',
    }));
  });

  test('should return error if duplicate campaign', async() => {
    const mockBody = {
      name: 'error_object',
      application: 'nova',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      pages: [ 'my-activity' ],
      content_space: '4szkx38resvm',
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      status: 'draft',
      type: 'campaign',
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      response: {
        data: {
          message: 'duplicate',
        },
      },
    };

    const mockCampaignApiClientLocal = {
      createCampaign: jest.fn().mockReturnValue(Promise.reject(fakeResponse)),
    };

    const mockApplicationsServiceLocal = {
      getApplicationByName: jest.fn().mockReturnValue({ ruleTypes: [ 'campaign' ] }),
    };

    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClientLocal, mockRuleService, mockUserService, mockApplicationsServiceLocal);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClientLocal.createCampaign).toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
    expect(mockNext.mock.calls[0][0].message).toBe('Campaign with this name already exists. Use a unique name for the new campaign.');
  });

  test('should return error if request fails', async() => {
    const mockBody = {
      name: 'error_object',
      application: 'nova',
      start_at: '2018-01-01T00:00:00Z',
      end_at: '2018-01-01T00:00:00Z',
      container: 'PRE_LOGIN',
      created_by: 's1234567',
      updated_by: 's1234567',
      pages: [ 'my-activity' ],
      content_space: '4szkx38resvm',
      content_type: 'notification',
      content_id: '4Sq6YGTHHOykcQ2kAqQOiU',
      status: 'draft',
      type: 'campaign',
    };
    const mockReq = {
      body: mockBody,
    };
    const res = {
      status: mockStatus,
      locals: {
        ...mockRes.locals,
        validatedBody: mockBody,
      },
    };
    const fakeResponse = {
      request: {
        message: 'request failed',
      },
    };

    const mockCampaignApiClientLocal = {
      createCampaign: jest.fn().mockReturnValue(Promise.reject(fakeResponse)),
    };

    const mockApplicationsServiceLocal = {
      getApplicationByName: jest.fn().mockReturnValue({ ruleTypes: [ 'campaign' ] }),
    };

    const createCampaign = CreateCampaign(mockLogger, mockCampaignApiClientLocal, mockRuleService, mockUserService, mockApplicationsServiceLocal);
    await createCampaign(mockReq, res, mockNext);
    expect(mockCampaignApiClientLocal.createCampaign).toBeCalled();
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(CustomError);
    expect(mockNext.mock.calls[0][0].message).toBe(JSON.stringify({
      message: 'request failed',
    }));
  });
});
