
const {
  getPathSchema,
  schemaValidationMiddleware,
  createSchema,
  DEFAULT_OPTS,
  VALIDATION_TYPE,
  getListSchema,
} = require('./validation');

const { CustomError } = require('../../error');

describe('Campaign routes validation', () => {
  const nextMock = jest.fn();

  beforeEach(() => {
    nextMock.mockClear();
  });

  it('schemaValidationMiddleware - should throw error if invalid param type passed', () => {
    schemaValidationMiddleware(createSchema, 'invalidType', DEFAULT_OPTS)({}, {}, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
    expect(nextMock.mock.calls[0][0].message).toBe(`Invalid joi validation type, must be: body, params, or query`);
  });

  it('schemaValidationMiddleware - should throw error if fails validation schema', () => {
    const localReq = {
      params: { ruleId: 'test-id' },
    };
    schemaValidationMiddleware(getPathSchema, 'params')(localReq, {}, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(2);
    expect(nextMock.mock.calls[0][0].message).toBe(`ValidationError: "test-id" is not valid for field "ruleId" ("ruleId" with value "test-id" fails to match the required pattern: /^[a-zA-Z0-9]+$/).`);
  });

  it('schemaValidationMiddleware - should pass validation - body', () => {
    const localReq = {
      body: {
        name: 'test create',
        start_at: '2024-03-12T04:00:00.000Z',
        end_at: '2024-03-19T04:00:00.000Z',
        platforms: [
          'ios',
          'android',
        ],
        app_version: null,
        content_space: '4szkx38resvm',
        content_type: 'priorityCampaign',
        content_id: '4nQXvRZ4kBIERim3KrvenE',
        container: 'priority-box',
        pages: [
          'mortgages',
        ],
        targeting: {
          products: [
          ],
        },
        status: 'draft',
        application: 'nova',
        mass_targeting: {
          product_pages: {
          },
          enrollment_status: [
          ],
        },
        platforms_targeting: [
          {
            platform: 'ios',
            items: [
            ],
          },
          {
            platform: 'android',
            items: [
            ],
          },
        ],
        external_ref: 'TESTN',
        dismissable_flag: true,
        disabled: false,
        urgent: false,
        type: 'campaign',
      },
    };
    const localRes = {
      locals: {
        validatedBody: localReq.body,
      },
    };
    schemaValidationMiddleware(createSchema, undefined, DEFAULT_OPTS)(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedBody).toEqual(localReq.body);
  });

  it('schemaValidationMiddleware - should pass validation - params', () => {
    const localReq = {
      params: { ruleId: 'abcd1234' },
    };
    const localRes = {
      locals: {
        validatedParams: localReq.params,
      },

    };
    schemaValidationMiddleware(getPathSchema, VALIDATION_TYPE.PARAMS)(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedParams).toEqual(localReq.params);
  });

  it('schemaValidationMiddleware - should pass validation - query', () => {
    const localReq = {
      query: {
        limit: '10',
        offset: '0',
        sort: '-updated_at',
        type: 'campaign',
      },
    };
    const localRes = {
      locals: {
        validatedQuery: {
          ...localReq.query,
          limit: 10,
          offset: 0,
        },
      },
    };
    schemaValidationMiddleware(getListSchema, VALIDATION_TYPE.QUERY)(localReq, localRes, nextMock);
    expect(nextMock).toHaveBeenCalledTimes(1);
    expect(localRes.locals.validatedQuery).toEqual(
      {
        ...localReq.query,
        // schema converts string to numbers
        limit: 10,
        offset: 0,
      },
    );
  });

  describe('Wealth LOB validation', () => {
    it('should accept valid wealth_lobs array with valid LOB codes', () => {
      const localReq = {
        body: {
          name: 'test wealth campaign',
          start_at: '2024-03-12T04:00:00.000Z',
          end_at: '2024-03-19T04:00:00.000Z',
          platforms: [ 'ios', 'android' ],
          app_version: null,
          content_space: '4szkx38resvm',
          content_type: 'priorityCampaign',
          content_id: '4nQXvRZ4kBIERim3KrvenE',
          container: 'priority-box',
          pages: [ 'accounts' ],
          languages: [ 'en', 'fr' ],
          targeting: {
            products: [],
          },
          status: 'draft',
          application: 'atlantis',
          mass_targeting: {
            product_pages: {},
            enrollment_status: [],
            wealth_lobs: [ 'SDBI', 'SMI', 'CASL' ],
          },
          platforms_targeting: [
            { platform: 'ios', items: [] },
            { platform: 'android', items: [] },
          ],
          external_ref: 'TESTN',
          dismissable_flag: true,
          disabled: false,
          urgent: false,
          type: 'wealth',
        },
      };
      const localRes = {
        locals: {
          validatedBody: localReq.body,
        },
      };

      schemaValidationMiddleware(createSchema, undefined, DEFAULT_OPTS)(localReq, localRes, nextMock);
      expect(nextMock).toHaveBeenCalledTimes(2);
      expect(localRes.locals.validatedBody.mass_targeting.wealth_lobs).toEqual([ 'SDBI', 'SMI', 'CASL' ]);
    });

    it('should accept single wealth_lob in array', () => {
      const localReq = {
        body: {
          name: 'test wealth campaign',
          start_at: '2024-03-12T04:00:00.000Z',
          end_at: '2024-03-19T04:00:00.000Z',
          platforms: [ 'ios' ],
          app_version: null,
          content_space: '4szkx38resvm',
          content_type: 'priorityCampaign',
          content_id: '4nQXvRZ4kBIERim3KrvenE',
          container: 'priority-box',
          pages: [ 'accounts' ],
          languages: [ 'en' ],
          targeting: {
            products: [],
          },
          status: 'draft',
          application: 'starburst',
          mass_targeting: {
            product_pages: {},
            enrollment_status: [],
            wealth_lobs: [ 'SDBI' ],
          },
          platforms_targeting: [
            { platform: 'ios', items: [] },
          ],
          external_ref: 'TESTN',
          dismissable_flag: true,
          disabled: false,
          urgent: false,
          type: 'wealth',
        },
      };
      const localRes = {
        locals: {
          validatedBody: localReq.body,
        },
      };

      schemaValidationMiddleware(createSchema, undefined, DEFAULT_OPTS)(localReq, localRes, nextMock);
      expect(nextMock).toHaveBeenCalledTimes(2);
      expect(localRes.locals.validatedBody.mass_targeting.wealth_lobs).toEqual([ 'SDBI' ]);
    });

    it('should accept all valid LOB codes', () => {
      const validLOBs = [ 'SDBI', 'SMI', 'CASL', 'TRST', 'SPCGIIA', 'REGL' ];
      const localReq = {
        body: {
          name: 'test wealth campaign',
          start_at: '2024-03-12T04:00:00.000Z',
          end_at: '2024-03-19T04:00:00.000Z',
          platforms: [ 'ios', 'android' ],
          app_version: null,
          content_space: '4szkx38resvm',
          content_type: 'priorityCampaign',
          content_id: '4nQXvRZ4kBIERim3KrvenE',
          container: 'priority-box',
          pages: [ 'accounts' ],
          languages: [ 'en', 'fr' ],
          targeting: {
            products: [],
          },
          status: 'draft',
          application: 'default',
          mass_targeting: {
            product_pages: {},
            enrollment_status: [],
            wealth_lobs: validLOBs,
          },
          platforms_targeting: [
            { platform: 'ios', items: [] },
            { platform: 'android', items: [] },
          ],
          external_ref: 'TESTN',
          dismissable_flag: true,
          disabled: false,
          urgent: false,
          type: 'wealth',
        },
      };
      const localRes = {
        locals: {
          validatedBody: localReq.body,
        },
      };

      schemaValidationMiddleware(createSchema, undefined, DEFAULT_OPTS)(localReq, localRes, nextMock);
      expect(nextMock).toHaveBeenCalledTimes(2);
      expect(localRes.locals.validatedBody.mass_targeting.wealth_lobs).toEqual(validLOBs);
    });

    it('should fail validation with empty wealth_lobs array', () => {
      const localReq = {
        body: {
          name: 'test wealth campaign',
          start_at: '2024-03-12T04:00:00.000Z',
          end_at: '2024-03-19T04:00:00.000Z',
          platforms: [ 'ios' ],
          app_version: null,
          content_space: '4szkx38resvm',
          content_type: 'priorityCampaign',
          content_id: '4nQXvRZ4kBIERim3KrvenE',
          container: 'priority-box',
          pages: [ 'accounts' ],
          languages: [ 'en' ],
          targeting: {
            products: [],
          },
          status: 'draft',
          application: 'starburst',
          mass_targeting: {
            product_pages: {},
            enrollment_status: [],
            wealth_lobs: [],
          },
          platforms_targeting: [
            { platform: 'ios', items: [] },
          ],
          external_ref: 'TESTN',
          dismissable_flag: true,
          disabled: false,
          urgent: false,
          type: 'wealth',
        },
      };

      schemaValidationMiddleware(createSchema, undefined, DEFAULT_OPTS)(localReq, {}, nextMock);
      expect(nextMock).toHaveBeenCalledTimes(2); // Called once for error, once for next()
      expect(nextMock.mock.calls[0][0].message).toContain('wealth_lobs');
    });

    it('should pass validation when wealth_lobs is omitted (optional field)', () => {
      const localReq = {
        body: {
          name: 'test regular campaign',
          start_at: '2024-03-12T04:00:00.000Z',
          end_at: '2024-03-19T04:00:00.000Z',
          platforms: [ 'ios', 'android' ],
          app_version: null,
          content_space: '4szkx38resvm',
          content_type: 'priorityCampaign',
          content_id: '4nQXvRZ4kBIERim3KrvenE',
          container: 'priority-box',
          pages: [ 'accounts' ],
          languages: [ 'en', 'fr' ],
          targeting: {
            products: [],
          },
          status: 'draft',
          application: 'nova',
          mass_targeting: {
            product_pages: {},
            enrollment_status: [],
            // wealth_lobs omitted intentionally
          },
          platforms_targeting: [
            { platform: 'ios', items: [] },
            { platform: 'android', items: [] },
          ],
          external_ref: 'TESTN',
          dismissable_flag: true,
          disabled: false,
          urgent: false,
          type: 'campaign',
        },
      };
      const localRes = {
        locals: {
          validatedBody: localReq.body,
        },
      };

      schemaValidationMiddleware(createSchema, undefined, DEFAULT_OPTS)(localReq, localRes, nextMock);
      expect(nextMock).toHaveBeenCalledTimes(1);
      expect(localRes.locals.validatedBody.mass_targeting.wealth_lobs).toBeUndefined();
    });

    it('should fail validation with non-array wealth_lobs', () => {
      const localReq = {
        body: {
          name: 'test wealth campaign',
          start_at: '2024-03-12T04:00:00.000Z',
          end_at: '2024-03-19T04:00:00.000Z',
          platforms: [ 'ios' ],
          app_version: null,
          content_space: '4szkx38resvm',
          content_type: 'priorityCampaign',
          content_id: '4nQXvRZ4kBIERim3KrvenE',
          container: 'priority-box',
          pages: [ 'accounts' ],
          languages: [ 'en' ],
          targeting: {
            products: [],
          },
          status: 'draft',
          application: 'starburst',
          mass_targeting: {
            product_pages: {},
            enrollment_status: [],
            wealth_lobs: 'SDBI', // Should be array, not string
          },
          platforms_targeting: [
            { platform: 'ios', items: [] },
          ],
          external_ref: 'TESTN',
          dismissable_flag: true,
          disabled: false,
          urgent: false,
          type: 'wealth',
        },
      };

      schemaValidationMiddleware(createSchema, undefined, DEFAULT_OPTS)(localReq, {}, nextMock);
      expect(nextMock).toHaveBeenCalledTimes(2);
      expect(nextMock.mock.calls[0][0].message).toContain('wealth_lobs');
    });
  });
});
