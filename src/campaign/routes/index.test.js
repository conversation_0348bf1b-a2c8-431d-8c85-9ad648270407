jest.mock('../../db', () => {
  return {
    db: jest.fn().mockReturnValue({ query: jest.fn() }),
  };
});
const campaignIndex = require('.');
const config = require('../../constants/config');

describe('/campaign route', () => {
  const campaignServiceMock = { getAllCampaigns: jest.fn() };
  test('should return valid routes', () => {
    const logger = { error: jest.fn() };
    const router = campaignIndex(config, logger, {}, {}, campaignServiceMock);
    const routes = [
      '/campaign-users/:ruleId',
      '/campaign-users',
      '/campaign-rules',
      '/export-campaign-rules',
      '/campaign-rules/:ruleId',
      '/campaign-setup',
      '/product-book',
      '/campaign-rules/:ruleId',
      '/campaign-rules',
      '/campaign-rules/:ruleId',
      '/campaign-rules/:ruleId',
    ];
    routes.forEach((route, index) => {
      expect(router.stack[index].route.path).toEqual(route);
    });
  });
});
