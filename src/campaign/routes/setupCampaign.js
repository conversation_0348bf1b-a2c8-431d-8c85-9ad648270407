const { CustomError } = require('../../error');

const setupCampaign = (service) => async(req, res, next) => {
  try {
    const campaignSetupInfo = await service.getCampaignSetupInfo();

    const { products, ...returnData } = campaignSetupInfo;
    const mapSubCategoryToKey = {
      'Borrowing': 'borrowingProducts',
      'Banking': 'bankingProducts',
      'Invest-Retail': 'investingRetail',
      'Invest-Wealth': 'investingWealth',
    };

    Object.keys(mapSubCategoryToKey).forEach((key) => { returnData[mapSubCategoryToKey[key]] = []; });
    returnData.otherProducts = [];

    campaignSetupInfo.products.forEach((element) => {
      const mappedName = mapSubCategoryToKey[element.sub_category_description] || 'otherProducts';
      returnData[mappedName].push(element);
    });

    return res.status(200).json(returnData);
  } catch (err) {
    if (err.response.status < 500) {
      return res.status(err.response.status).json(err.response.data);
    }
    next(new CustomError(err.response.status, JSON.stringify(err.response.data)));
  }
};

module.exports = setupCampaign;
