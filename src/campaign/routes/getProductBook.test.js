const { getProductBook, applyOverrides, mapToSpecs } = require('./getProductBook');
const seedData = require('../../constants/productBook/seed.json');
const overrideData = require('../../constants/productBook/overrides.json');

const mockRes = { send: jest.fn() };
const mockData = {
  data: {
    product: [ {
      product_id: '340030',
      product_types: [ { product_domain: 'default', product_code: 'NRS' } ],
      ownership: 'R',
      properties: [ { type: 'CORNERSTONE_ID', value: 'Investing' } ],
      descriptions: [ { locale: 'en_CA', value: 'new description' } ],
    } ],
  },
};
const config = { refreshIntervalMillis: 24 * 60 * 60 * 1000, seedData, overrideData };
const logger = { info: jest.fn(), warn: jest.fn(), error: jest.fn(), debug: jest.fn() };
const marvelProductApi = { getProducts: jest.fn().mockImplementation(() => Promise.resolve(mockData)) };

describe('getProductBook', () => {
  beforeEach(() => {
    mockRes.send.mockReset();
    logger.info.mockClear();
    logger.warn.mockClear();
    logger.error.mockClear();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  test('it returns products with overrides applied', () => {
    const testNewProduct = {
      action: 'update',
      id: 'test-product-addition',
      code: 'ABC',
      category: 'banking',
      ownership: 'R',
      description: 'test description',
    };
    config.overrideData = [ ...overrideData, testNewProduct ];
    const routes = getProductBook(config, logger, marvelProductApi);
    expect(logger.error).not.toHaveBeenCalled();
    expect(logger.info).toHaveBeenCalled();
    routes.getProducts(null, mockRes);
    const results = mockRes.send.mock.calls[0][0];
    expect(results).not.toHaveLength(0);

    const properties = [ 'category', 'description', 'code', 'id', 'ownership' ];
    results.forEach(r => {
      properties.forEach(p => {
        expect(r).toHaveProperty(p);
      });
    });

    // assert deletes
    const deletes = config.overrideData
      .filter(o => o.action === 'delete' && !!o.category)
      .map(o => o.category); // sample by category
    expect(!results.some(r => deletes.includes(r.category)));

    // assert additions
    expect(results.some(r => r.id === testNewProduct.id));

    // assert updates
    // test product is the retail non registered savings account group
    const override = config.overrideData.find(o => o.ownership === 'R' &&
      o.code === 'NRS' &&
      !o.sub_code &&
      o.category === 'investing' &&
      o.description === 'Non-Reg Savings - BNS & SSI',
    );
    const updatedProduct = results.find(p => p.ownership === 'R' &&
      p.code === 'NRS' &&
      !p.sub_code &&
      p.category === 'investing',
    );
    expect(updatedProduct.description === override.description);
  });

  test('get products api response matches snapshot', () => {
    const routes = getProductBook(config, logger, marvelProductApi);
    routes.getProducts(null, mockRes);
    expect(logger.error).not.toHaveBeenCalled();
    const results = mockRes.send.mock.calls[0][0];
    expect(results).toMatchSnapshot();
  });

  test('there are no duplicate category,ownership,code,sub_code combinations', () => {
    const routes = getProductBook(config, logger, marvelProductApi);
    routes.getProducts(null, mockRes);
    const results = mockRes.send.mock.calls[0][0];

    const keys = new Set();
    // eslint-disable-next-line camelcase
    results.forEach(({ category, ownership, code, sub_code }) => {
      // eslint-disable-next-line camelcase
      const identifier = `${sub_code}.${code}.${ownership}.${category}`;
      expect(keys.has(identifier)).toBeFalsy();
      keys.add(identifier);
    });

    expect([ ...keys ]).toHaveLength(results.length);
  });

  test('product book auto updates itself', () => {
    let refreshJob;
    try {
      const routes = getProductBook(config, logger, marvelProductApi);
      routes.getProducts(null, mockRes);
      refreshJob = routes.refreshJob;
      const results = mockRes.send.mock.calls[0][0];
      jest.advanceTimersByTime(1000);
      // test product is the retail non registered savings account
      const beforeRefresh = results.find(p => p.ownership === 'R' &&
        p.code === 'NRS' &&
        p.category === 'investing' &&
        !p.sub_code,
      );
      expect(beforeRefresh.description === 'Non-Reg Savings - BNS');

      // refresh products
      jest.advanceTimersByTime(config.refreshIntervalMillis);
      expect(marvelProductApi.getProducts).toHaveBeenCalled();
      routes.getProducts(null, mockRes);
      const resultsAfterRefresh = mockRes.send.mock.calls[1][0];
      const afterRefresh = resultsAfterRefresh.find(p => p.ownership === 'R' &&
        p.code === 'NRS' &&
        p.category === 'investing' &&
        !p.sub_code,
      );
      expect(afterRefresh.description === 'new description');
    } finally {
      clearInterval(refreshJob);
    }
  });
});

describe('applyOverrides', () => {
  let logger;
  let products;
  let overrides;

  beforeEach(() => {
    logger = { warn: jest.fn(), info: jest.fn(), debug: jest.fn() };
    products = mockData.data.product;
    overrides = [
      { 'action': 'delete', 'category': 'BRK' },
      { 'action': 'delete', 'category': 'Other' },
      { 'action': 'update', 'ownership': 'B', 'category': 'investing', 'code': 'NRS', 'description': 'Non-Reg Savings - BNS & SSI' },
      { 'action': 'update', 'ownership': 'B', 'category': 'borrowing', 'code': 'VCL', 'sub_code': 'SV', 'description': 'Learn VISA CARD' },
    ];
  });

  test('should add new valid items', () => {
    const mappedProducts = mapToSpecs(logger, products);
    const result = applyOverrides(logger, mappedProducts, overrides);
    expect(result).toEqual(expect.arrayContaining([ expect.objectContaining({ id: '340030' }) ]));
  });

  test('should log a warn and ignore duplicate override items', () => {
    let duplicateProducts = [ products[0], products[0] ];
    const mappedProducts = mapToSpecs(logger, duplicateProducts);
    const result = applyOverrides(logger, mappedProducts, overrides);
    expect(logger.warn).toHaveBeenCalled();
    expect(logger.debug).toHaveBeenCalled();
    expect(result).toEqual(expect.arrayContaining([ expect.objectContaining({ id: '340030' }) ]));
  });
});
