const { CustomError, ForbiddenError } = require('../../error');
const { uniq, set, get } = require('lodash');

const getCampaign = function(campaignService, ruleService) {
  return async function(req, res, next) {
    const value = res.locals.validatedParams;

    try {
      const rule = await campaignService.getCampaign(value.ruleId);
      /**
       * We are updating existing campaign data to ensure consistency across all entries.
       * Some of the older campaigns are missing the languages field. To address this,
       * we will add a languages array to those campaigns, setting it to include both English (en) and French (fr) as default values.
       * This ensures that all campaigns have a standardized structure and support for both official languages moving forward.
       */
      if (!get(rule, 'data.mass_targeting.languages')) {
        set(rule, 'data.mass_targeting.languages', [ 'en', 'fr' ]);
      }

      // check user has access to view the page, container & sub type of the rule
      const { containers, pages, ruleSubTypes } = res.locals.user.access.campaigns;
      const hasContainerAccess = containers[rule.data.application] &&
        uniq([ ...(containers[rule.data.application].view || []), ...(containers[rule.data.application].manage || []) ])
          .includes(rule.data.container);
      const hasPageAccess = pages[rule.data.application] &&
        uniq([ ...(pages[rule.data.application].view || []), ...(pages[rule.data.application].manage || []) ])
          .some(page => rule.data.pages.includes(page));
      const hasRuleSubTypeAccess = ruleSubTypes[rule.data.application] &&
        uniq([ ...(ruleSubTypes[rule.data.application].view || []), ...(ruleSubTypes[rule.data.application].manage || []) ])
          .includes([ 'MASS', 'MESSAGE', 'WEALTH' ].some(ref => ref === rule.data.external_ref) ? rule.data.external_ref.toLowerCase() : 'targeted');

      if (!hasContainerAccess || !hasPageAccess || !hasRuleSubTypeAccess) {
        next(new ForbiddenError());
        return;
      }

      // get assignees for campaign
      const assignees = await ruleService.getAssigneesForCampaign(value.ruleId);

      // add assignees to campaign data - TODO: verify there are assignees to assign to the campaign
      if (assignees && assignees.length > 0) {
        rule.data = Object.assign({ assignees: assignees }, rule.data);
      }

      res.status(200).json(rule.data);
    } catch (err) {
      if (err.response) {
        if (err.response.status < 500) {
          res.status(err.response.status).json(err.response.data);
        } else {
          next(new CustomError(err.response.status, JSON.stringify(err.response.data)));
        }
      } else {
        next(new CustomError(500, err.toString()));
      }
    }
  };
};

module.exports = getCampaign;
