const getCampaign = require('./getCampaign');
const { CustomError, ForbiddenError } = require('../../error');
const { MockError } = require('__mocks__/MockError');

const mockService = {
  getCampaign: jest.fn().mockReturnValue({
    data: {
      application: 'nova',
      container: 'offers-and-programs',
      pages: [ 'accounts' ],
      external_ref: '12345',
    },
  }),
};

const jsonResp = {
  json: jest.fn(),
};

const resMock = {
  status: jest.fn().mockReturnValue(jsonResp),
  locals: {
    validatedParams: {},
    user: {
      id: 1,
      role_id: 1,
      permissions: [ 'admin' ],
      access: {
        campaigns: {
          containers: {
            nova: {
              view: [ 'offers-and-programs' ],
            },
          },
          pages: {
            nova: {
              view: [ 'accounts' ],
            },
          },
          ruleSubTypes: {
            nova: {
              view: [ 'targeted' ],
            },
          },
        },
      },
    },
  },
};

const reqMock = {
  params: {
    ruleId: 'asdfas123',
  },
};

const nextMock = jest.fn();

const ruleServiceMock = {
  getAssigneesForCampaign: jest.fn(),
};

describe('GET /api/v1/campaigns - get a campaign by id', () => {
  beforeEach(() => {
    nextMock.mockClear();
    ruleServiceMock.getAssigneesForCampaign.mockClear();
  });

  test('should return 200 with valid req', async() => {
    const asyncGetCampaign = getCampaign(mockService, ruleServiceMock);
    await asyncGetCampaign(reqMock, resMock, nextMock);

    expect(mockService.getCampaign).toHaveBeenCalledTimes(1);
    expect(nextMock).not.toHaveBeenCalled();
    expect(resMock.status).toHaveBeenCalledWith(200);
  });

  test('should return ForbiddenError user does not have access to the container/page/subtype', async() => {
    const getCampaignResMock = {
      status: jest.fn().mockReturnValue(jsonResp),
      locals: {
        validatedParams: {},
        validatedQuery: {},
        validatedBody: {},
        user: {
          id: 1,
          role_id: 1,
          permissions: [ ],
          access: {
            campaigns: {
              containers: {},
              pages: {},
              ruleSubTypes: {},
            },
          },
        },
      },
    };
    const asyncGetCampaign = getCampaign(mockService, ruleServiceMock);
    await asyncGetCampaign(reqMock, getCampaignResMock, nextMock);

    expect(mockService.getCampaign).toHaveBeenCalledTimes(1);
    expect(nextMock.mock.calls[0][0] instanceof ForbiddenError).toBe(true);
    expect(getCampaignResMock.status).not.toHaveBeenCalled();
  });

  test('should return CustomError if unhandled exception occurs with service', async() => {
    const badMockService = {
      getCampaign: jest.fn().mockRejectedValueOnce(new Error()),
    };

    const asyncGetCampaign = getCampaign(badMockService, ruleServiceMock);
    await asyncGetCampaign(reqMock, resMock, nextMock);

    expect(badMockService.getCampaign).toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
    expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
  });

  test('should handle 5XX response', async() => {
    const errResponse = {
      status: 500,
      data: {},
    };

    const errMockService = {
      getCampaign: jest.fn().mockRejectedValueOnce(new MockError(500, '', errResponse)),
    };

    const asyncGetCampaign = getCampaign(errMockService, ruleServiceMock);
    await asyncGetCampaign(reqMock, resMock, nextMock);

    expect(errMockService.getCampaign).toHaveBeenCalled();
    expect(nextMock).toHaveBeenCalled();
    expect(nextMock.mock.calls[0][0] instanceof CustomError).toBe(true);
  });

  test('sholud handle 3xx-4xx response', async() => {
    const errResponse = {
      status: 400,
      data: {},
    };

    const errMockService = {
      getCampaign: jest.fn().mockRejectedValueOnce(new MockError(400, '', errResponse)),
    };

    const asyncGetCampaign = getCampaign(errMockService, ruleServiceMock);
    await asyncGetCampaign(reqMock, resMock, nextMock);

    expect(errMockService.getCampaign).toHaveBeenCalled();
    expect(nextMock).not.toHaveBeenCalled();
    expect(resMock.status).toHaveBeenCalled();
    expect(resMock.status).toHaveBeenCalledWith(errResponse.status);
    expect(jsonResp.json).toHaveBeenCalled();
    expect(jsonResp.json).toHaveBeenCalledWith(errResponse.data);
  });
});
