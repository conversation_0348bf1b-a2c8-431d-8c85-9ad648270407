---
applications:
  - name: admin-ist
    buildpacks:
      - nodejs_buildpack-v1_8_27
    stack: cflinuxfs4
    command: npm run start
    memory: 512MB
    instances: 1
    services:
      - pigeon-cache
      - pigeon-ist-failover-ist
    routes:
      - route: pigeon-admin-ist.apps.stg.azr-use2-pcf.cloud.bns
      - route: pigeon-admin-ist.apps.cloud.bns
    health-check-type: http
    health-check-http-endpoint: /health
    env:
      SERVER_PORT: 8080
      RULE_API_URL: https://rule-api-ist.apps.cloud.bns
      CONTENT_API_URL: https://content-api-ist.apps.cloud.bns/v1
      MARVEL_PRODUCT_API_URL: https://cdb-int-product-ist.apps.stg.azr-use2-pcf.cloud.bns
      CAMPAIGN_MANAGEMENT_API_URL: https://pigeon-campaign-mgmt-api-ist.nonp.atlas.bns
      OFFERS_MANAGEMENT_API_URL: https://cdb-int-offer-management-nane2-ist.nonp.atlas.bns
      NODE_TLS_REJECT_UNAUTHORIZED: 0
      ADMIN_REDIRECT_URL: https://pigeon-admin-ist.apps.cloud.bns
      PASSPORT_API_URI: https://passport-oauth-ist.apps.cloud.bns/oauth2/v1
      SLACK_API_URL: https://slack.com/api
      PROXY_URL: http://pp-webproxy.bns:8080
      PASSPORT_API_TIMEOUT: 3000
      PASSPORT_API_S2S_SCOPE: ca:baas:alert-rules:read,ca:baas:alert-rules:write,ca:baas:campaign-rules:read,ca:baas:campaign-rules:write,ca:baas:contents:read,cdb.pigeon.campaign-management.campaigns.read,cdb.pigeon.campaign-management.campaigns.write,cdb.pigeon.campaign-management.offers.read,cdb.pigeon.campaign-management.offers.write,customer.offer-management.bjqx.admin.read,customer.offer-management.bjqx.admin.write,customer.offer-management.bjqx.admin.delete
      PASSPORT_API_S2S_TTL: 60
      PASSPORT_API_S2S_ALGORITHM: RS256
      PASSPORT_API_S2S_CLIENTID: f4dd8dcc-8229-45dc-88a0-3e720745ef20
      USER_TOKEN_DURATION: 15
      CONTENTFUL_ENTRY_URL: https://app.contentful.com/spaces/%s/environments/IST/entries/%s
      FAILOVER_GROUP: pigeon-ist-failover-ist
      BITBUCKET_STATIC_URL: https://bitbucket.agile.bns/projects/PIGEONSOL/repos/dm-authenticated/raw
      AUTH_S2S_CLAIM_EXPIRESIN: 1h
      AUTH_S2S_CLAIM_NOTBEFORE: -10s
      AUTH_S2S_PUBLIC_KEY: LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF5YjBTbENaTWl1NmhqOXpCNGtvMwpxUXc4VUc1aFpYVkx3SjU5S2xhWE5NQlBnb2NPZWhtQWVEQytYRFpxazQzb2tRZHY2cmpRQUJzY2x5eU9LY1NXCkZKRXBLWjZhL3B3Y3JsQkpYVVhmbjVXY3BqTGl5QjhUS2lJd3I3SStTam9OczB2WnRHRE10aVVrb1Zrdk03VnEKb2VMZmJsRnhxNTBhRkQvcnRYRTRoSjN6eVh5dlp5ei9Kbnp5YTZIMTF1MTJMUlBWUExYcFV5RUIrdnlpYWtxbQpPblhMTEZTNVUxam5wVlNXY2g4NnBuUU1EU0o4enlNSWQ0VEVnR2NNdkwzeW53N3FmK2p3MDdhQUFpdDlETzJjClZ5aHBRa3d0QVR0RC9maHd5eDBWYVl1MnVsZDBGUnNONTQ0YUpUSUNsU1RsL3JCNUlObW5JTmVIdzl6TDVZR04KL1FJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t
      # content security policy
      CSP_IMAGE_SRC: '["https://*.ctfassets.net","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net","https://wam-ist.cloud.bns","data:"]'
      CSP_FRAME_SRC: '["https://*.demdex.net"]'
      CSP_SCRIPT_SRC: '["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net","https://wam-ist.cloud.bns"]'
      CSP_CONNECT_SRC: '["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net","https://wam-ist.cloud.bns"]'
      # Secure cookie
      SECURE_COOKIE: 1
      # Rate limiting fallback config
      RATE_LIMIT_CLIENT_MAX: 180
      RATE_LIMIT_OVERALL_MAX: 2000
      RATE_LIMIT_CDP_TRUSTED_IP: '***********/23'
      # Launch darkly
      LAUNCH_DARKLY_USER_ID: d30c0d12-bea3-11ed-afa1-0242ac120002
      # WAM authentication
      WAM_TOKEN_URL: https://wam-ist.cloud.bns/sso/oauth2/bns/access_token
      WAM_TOKEN_INFO_URL: https://wam-ist.cloud.bns/sso/oauth2/bns/tokeninfo
      WAM_AUTHORIZE_URL: https://wam-ist.cloud.bns/sso/oauth2/bns/authorize
      WAM_CLIENT_ID: PigeonAdmin
      WAM_REDIRECT_URL: https://pigeon-admin-ist.apps.cloud.bns/authorization
      WAM_POST_LOGOUT_REDIRECT_URL: 'https://wam-ist.cloud.bns/sso/XUI/?realm=/bns#logout/&goto=https://pigeon-admin-ist.apps.cloud.bns'
      WAM_JWKS_URL: https://wam-ist.cloud.bns/sso/oauth2/bns/connect/jwk_uri
      VALID_RETURN_DOMAIN: '.cloud.bns'
      WAM_LOCAL_SESSION_TTL: 3600
      # logging options
      LOG_NAME: admin-ist
      LOG_OBFUSCATE: 1
      LOG_COLORIZE: 0
      LOG_PRETTY_PRINT: 0
      LOG_SIEM: 1
      IGNORED_ROUTES_FOR_LOGGING: '["/health"]'
