---
applications:
  - name: admin-nft
    buildpacks:
      - nodejs_buildpack-v1_8_27
    stack: cflinuxfs4
    command: npm run start
    memory: 1024MB
    instances: 1
    services:
      - pigeon-cache
      - pigeon-content-failover-nft
      - pigeon-autoscaler
    routes:
      - route: pigeon-admin-nft.apps.stg.azr-cc-pcf.cloud.bns
      - route: pigeon-admin-nft.apps.cloud.bns
    health-check-type: http
    health-check-http-endpoint: /health
    env:
      SERVER_PORT: 8080
      RULE_API_URL: https://rule-api-nft.apps.cloud.bns
      CONTENT_API_URL: https://content-api-nft.apps.cloud.bns/v1
      MARVEL_PRODUCT_API_URL: https://cdb-int-product-nft.apps.stg.azr-cc-pcf.cloud.bns
      NODE_TLS_REJECT_UNAUTHORIZED: 0
      ADMIN_REDIRECT_URL: https://pigeon-admin-nft.apps.cloud.bns
      SLACK_API_URL: https://slack.com/api
      PROXY_URL: http://pp-webproxy.bns:8080
      USER_TOKEN_DURATION: 15
      FAILOVER_GROUP: pigeon-content-failover-nft
      BITBUCKET_STATIC_URL: https://bitbucket.agile.bns/projects/PIGEONSOL/repos/dm-authenticated/raw
      AUTH_S2S_CLAIM_EXPIRESIN: 1h
      AUTH_S2S_CLAIM_NOTBEFORE: -10s
      AUTH_S2S_PUBLIC_KEY: LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUFxZGx1RThZN1B4Wm90dVdOM3VNOQp6R0JjTDRmbWlrWkx5Z3k4aVBBUkhjQWpnenZpYUptM0pySExUU1hCejV4UzVCRy84SnZNSFBBa0xueFBaL29VCjhvOHlEemo0YXE3bkpGUVpQWkxyZ0JNTmU4dW54MDRJa25yZlBzM1R1MGFvdXFUS2cvY1NHUzd1anMvNWNOUmUKcXlsejkrWjFneE9YeEJXa1FEeGwrQ1hEdzd1dm1xKzdSNjdZemViSHhoVmM3RWd1eFJqOXh6OVFkczdVZG1BNgp6amkvNWRnMkI2TFIrcWxuZm5mK0g2ejcwN2hRZXNkTFlYbzBYS042UTlSbW1tZ2tldmd3YzVvQW41SDBJb0dhCnBrTkF6TXQrcE1RQzFlcFpsR1huWVBUOVEzYWZwZ0dENCtvNjRXNFRORDJ3MnRrdUZzbnc1UnNSeDh1SnJXMEcKUlFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t

      # content security policy
      CSP_IMAGE_SRC: '["https://*.ctfassets.net","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net","https://wam-nft.cloud.bns","data:"]'
      CSP_FRAME_SRC: '["https://*.demdex.net"]'
      CSP_SCRIPT_SRC: '["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net","https://wam-nft.cloud.bns"]'
      CSP_CONNECT_SRC: '["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net","https://wam-nft.cloud.bns"]'
      # WAM authentication
      WAM_TOKEN_URL: https://wam-nft.cloud.bns/sso/oauth2/bns/access_token
      WAM_TOKEN_INFO_URL: https://wam-nft.cloud.bns/sso/oauth2/bns/tokeninfo
      WAM_AUTHORIZE_URL: https://wam-nft.cloud.bns/sso/oauth2/bns/authorize
      WAM_CLIENT_ID: PigeonAdmin
      WAM_REDIRECT_URL: https://pigeon-admin-nft.apps.cloud.bns/authorization
      WAM_POST_LOGOUT_REDIRECT_URL: 'https://wam-nft.cloud.bns/sso/XUI/?realm=/bns#logout/&goto=https://pigeon-admin-nft.apps.cloud.bns'
      WAM_JWKS_URL: https://wam-nft.cloud.bns/sso/oauth2/bns/connect/jwk_uri
      VALID_RETURN_DOMAIN: '.cloud.bns'
      WAM_LOCAL_SESSION_TTL: 3600
