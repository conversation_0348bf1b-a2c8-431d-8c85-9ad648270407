diff --git a/src/permissions/workflow/index.js b/src/permissions/workflow/index.js
index dbbd3233..9ebfe7e8 100644
--- a/src/permissions/workflow/index.js
+++ b/src/permissions/workflow/index.js
@@ -9,15 +9,12 @@ const {
   enableCampaign,
   terminateCampaign,
   createdWithValidStatus,
-  saveVariableMappingSetDraft,
-  submitVariableMappingSetDraft,
-  approveVariableMappingSet,
+  activeVariableMappingSetDraft,
+  draftVariableMappingSetPending,
   editVariableMappingSetDraft,
-  deleteVariableMappingSet,
-  unPublishVariableMappingSet,
-  rejectVariableMappingSet,
-  canApproveVariableMappingSet,
-  canUnPublishVariableMappingSet,
+  pendingVariableMappingSetActive,
+  draftVariableMappingSetDeleted,
+  pendingVariableMappingSetDraft,
 } = require('./transactions');
 
 const {
@@ -34,7 +31,6 @@ const {
   PERMISSION_CAMPAIGNS_CREATE,
   PERMISSION_VARIABLE_MAPPINGS_UPDATE,
   PERMISSION_VARIABLE_MAPPINGS_APPROVE,
-  PERMISSION_ADMIN,
 } = require('../index');
 
 const canUpdateCampaign = (user, campaignBefore, body) => {
@@ -52,24 +48,27 @@ const canUpdateCampaign = (user, campaignBefore, body) => {
   return conditions.filter(condition => condition === true).length === 1;
 };
 
-const canUpdateVariableMappings = (user, originalVariableMappingSet, body) => {
+const canCreateCampaign = (user, body) => {
   const conditions = [
-    saveVariableMappingSetDraft(originalVariableMappingSet, body) && resolvePermission(user, PERMISSION_VARIABLE_MAPPINGS_UPDATE),
-    editVariableMappingSetDraft(originalVariableMappingSet, body) && resolvePermission(user, PERMISSION_VARIABLE_MAPPINGS_UPDATE),
-    submitVariableMappingSetDraft(originalVariableMappingSet, body) && resolvePermission(user, PERMISSION_VARIABLE_MAPPINGS_UPDATE),
-    approveVariableMappingSet(originalVariableMappingSet, body) && (canApproveVariableMappingSet(originalVariableMappingSet, user) || resolvePermission(user, PERMISSION_ADMIN)) && resolvePermission(user, PERMISSION_VARIABLE_MAPPINGS_APPROVE),
-    deleteVariableMappingSet(originalVariableMappingSet, body) && resolvePermission(user, PERMISSION_VARIABLE_MAPPINGS_UPDATE),
-    unPublishVariableMappingSet(originalVariableMappingSet, body, user) && (canUnPublishVariableMappingSet(originalVariableMappingSet, user) || resolvePermission(user, PERMISSION_ADMIN)) && resolvePermission(user, PERMISSION_VARIABLE_MAPPINGS_UPDATE),
-    rejectVariableMappingSet(originalVariableMappingSet, body) && resolvePermission(user, PERMISSION_VARIABLE_MAPPINGS_APPROVE),
+    createdWithValidStatus(body) && resolvePermission(user, PERMISSION_CAMPAIGNS_CREATE),
   ];
-  return conditions.filter(condition => condition === true).length >= 1;
+  return conditions.filter(condition => condition === true).length === 1;
 };
 
-const canCreateCampaign = (user, body) => {
+const userIsEditor = (originalVariableMappingSet, user) =>
+  originalVariableMappingSet.created_by === user.sid ||
+  originalVariableMappingSet.updated_by === user.sid;
+
+const canUpdateVariableMappings = (user, originalVariableMappingSet, body) => {
   const conditions = [
-    createdWithValidStatus(body) && resolvePermission(user, PERMISSION_CAMPAIGNS_CREATE),
+    draftVariableMappingSetPending(originalVariableMappingSet, body) && resolvePermission(user, PERMISSION_VARIABLE_MAPPINGS_UPDATE),
+    draftVariableMappingSetDeleted(originalVariableMappingSet, body) && resolvePermission(user, PERMISSION_VARIABLE_MAPPINGS_UPDATE),
+    activeVariableMappingSetDraft(originalVariableMappingSet, body) && resolvePermission(user, PERMISSION_VARIABLE_MAPPINGS_UPDATE),
+    editVariableMappingSetDraft(originalVariableMappingSet, body) && resolvePermission(user, PERMISSION_VARIABLE_MAPPINGS_UPDATE),
+    pendingVariableMappingSetDraft(originalVariableMappingSet, body, user) && resolvePermission(user, PERMISSION_VARIABLE_MAPPINGS_APPROVE),
+    (pendingVariableMappingSetActive(originalVariableMappingSet, body) && !userIsEditor(originalVariableMappingSet, user)) && resolvePermission(user, PERMISSION_VARIABLE_MAPPINGS_APPROVE),
   ];
-  return conditions.filter(condition => condition === true).length === 1;
+  return conditions.filter(condition => condition === true).length >= 1;
 };
 
 module.exports = {
diff --git a/src/permissions/workflow/transactions.js b/src/permissions/workflow/transactions.js
index d5b7bdfc..b711ae4a 100644
--- a/src/permissions/workflow/transactions.js
+++ b/src/permissions/workflow/transactions.js
@@ -104,35 +104,30 @@ const createdWithValidStatus = (body) => {
   return !disabled && (status && (status === 'draft' || status === 'submitted'));
 };
 
-const saveVariableMappingSetDraft = (originalVariableMappingSet, body) => originalVariableMappingSet.status === 'active' && body.status === 'draft';
+const activeVariableMappingSetDraft = (originalVariableMappingSet, body) => originalVariableMappingSet.status === 'active' && body.status === 'draft';
 
+//i don't think this is needed
 const editVariableMappingSetDraft = (originalVariableMappingSet, body) => originalVariableMappingSet.status === 'draft' && body.status === 'draft';
 
-const submitVariableMappingSetDraft = (originalVariableMappingSet, body) => originalVariableMappingSet.status === 'draft' && body.status === 'pending';
+const draftVariableMappingSetPending = (originalVariableMappingSet, body) => originalVariableMappingSet.status === 'draft' && body.status === 'pending';
 
-const approveVariableMappingSet = (originalVariableMappingSet, body) =>
+const pendingVariableMappingSetActive = (originalVariableMappingSet, body) =>
   originalVariableMappingSet.status === 'pending' &&
   body.status === 'active';
 
-const canApproveVariableMappingSet = (originalVariableMappingSet, user) =>
-  originalVariableMappingSet.updated_by !== user.sid;
-
-const deleteVariableMappingSet = (originalVariableMappingSet, body) =>
+const draftVariableMappingSetDeleted = (originalVariableMappingSet, body) =>
   originalVariableMappingSet.status === 'draft' &&
   body.status === 'deleted';
 
-const rejectVariableMappingSet = (originalVariableMappingSet, body) =>
+const pendingVariableMappingSetDraft = (originalVariableMappingSet, body) =>
   originalVariableMappingSet.status === 'pending' &&
   body.status === 'draft';
 
+//this seems to be a duplicate of the check above (^^)
 const unPublishVariableMappingSet = (originalVariableMappingSet, body) =>
   originalVariableMappingSet.status === 'pending' &&
   body.status === 'draft';
 
-const canUnPublishVariableMappingSet = (originalVariableMappingSet, user) =>
-  originalVariableMappingSet.created_by === user.sid ||
-  originalVariableMappingSet.updated_by === user.sid;
-
 module.exports = {
   draftToSubmitted,
   submittedToDraft,
@@ -144,13 +139,11 @@ module.exports = {
   enableCampaign,
   terminateCampaign,
   createdWithValidStatus,
-  saveVariableMappingSetDraft,
-  submitVariableMappingSetDraft,
+  activeVariableMappingSetDraft,
+  draftVariableMappingSetPending,
   editVariableMappingSetDraft,
-  approveVariableMappingSet,
-  deleteVariableMappingSet,
+  pendingVariableMappingSetActive,
+  draftVariableMappingSetDeleted,
   unPublishVariableMappingSet,
-  rejectVariableMappingSet,
-  canApproveVariableMappingSet,
-  canUnPublishVariableMappingSet,
+  pendingVariableMappingSetDraft,
 };
