# Stage 1: copy dependencies and build the application
# Using alpine image to keep image size small
# Pipeline: FROM us-docker.pkg.dev/pbyqs-8001-gkemgmt-5af67598/common-images/common/node/node:22.13.1-alpine3.20 AS builder
# Local: FROM us-docker.pkg.dev/nbyqs-8001-gkemgmt-aa8503ee/unscanned-images/pigeon/node22.19.0 AS builder
FROM us-docker.pkg.dev/pbyqs-8001-gkemgmt-5af67598/common-images/common/node/node:22.13.1-alpine3.20 AS builder
USER root

WORKDIR /build

# Bundle app source
COPY certs /etc/ssl/certs
COPY apk /etc/apk
COPY .cfignore .eslintrc.js GitVersion.yml jest.config.js ./
COPY package*.json permissions.diff sonar-project.properties swagger.yml ./
COPY migrations migrations
COPY src src
COPY node_modules node_modules

# Test files cleanup
RUN set +e && \
    find ./src -name "*.test.js" -delete 2>/dev/null && \
    # Targeted cleanup for AquaSec flagged files, only delete from test directories
    find ./node_modules -path "*/test/*" -name "*.pem" -delete 2>/dev/null && \
    find ./node_modules -path "*/test/*" -name "*.priv" -delete 2>/dev/null && \
    rm -f ./node_modules/pem-jwk/test/priv.pem && \
    # Remove development-only artifacts
    rm -rf ./coverage ./front-end/coverage ./front-end/__mocks__ && \
    set -e

# Bundle frontend folder
COPY front-end front-end

WORKDIR /build/front-end
RUN npm run build

USER node
# Stage 2: package production image
# Using distroless image to exclude all unnecessary packages and avoid vulnerabilities in build
# Pipeline: FROM us-docker.pkg.dev/pbyqs-8001-gkemgmt-5af67598/common-images/node/nodejs22-20250915:nonroot AS deployment
# Local: FROM us-docker.pkg.dev/nbyqs-8001-gkemgmt-aa8503ee/unscanned-images/pigeon/node22.19.0 AS deployment
FROM us-docker.pkg.dev/pbyqs-8001-gkemgmt-5af67598/common-images/node/nodejs22-20250915:nonroot AS deployment

LABEL owner=digital
LABEL os=alpine
LABEL description='PIGEON'
LABEL CDPMigration=W3
LABEL EPM=BFB6

# Add ENV var for Dynatrace monitoring
ENV DT_CUSTOM_PROP='UNIQUE_ID=AL_PIGEON EPM=BFB6'
ENV NODE_ENV='production'


# Copy Scotia CA certs
COPY certs /etc/ssl/certs

WORKDIR /build

COPY --from=builder /build .
ENV NODE_EXTRA_CA_CERTS='/etc/ssl/certs/ca-certificates.crt'
ENV PORT='8080'

# Export port
EXPOSE ${PORT}

CMD ["src/"]
