const Axios = {
  interceptors: {
    request: {
      use: (resolve, reject) => {
        if (resolve) {
          Axios.reqRes = resolve;
        }
        if (reject) {
          Axios.reqRej = reject;
        }
      },
    },
    response: {
      use: (resolve, reject) => {
        if (resolve) {
          Axios.resRes = resolve;
        }
        if (reject) {
          Axios.resRej = reject;
        }
      },
    },
  },

  delete: jest.fn((url, config) => {
    return Axios.fail ? Axios.resRej({ response:{ status: 403 } }) : Promise.resolve(Axios.resRes({ data: { url, config } }));
  }),
  get: jest.fn((url, config) => {
    return Axios.fail ? Axios.resRej({ response:{ status: 404 } }) : Promise.resolve(Axios.resRes({ data: { url, config } }));
  }),
  patch: jest.fn((url, data, config) => {
    return Axios.fail ? Axios.resRej(data) : Promise.resolve(Axios.resRes({ data: { url, config } }));
  }),
  post: jest.fn((url, data, config) => {
    return Axios.fail ? Axios.resRej(data) : Promise.resolve(Axios.resRes({ data: { url, config } }));
  }),
  put: jest.fn((url, data, config) => {
    return Axios.fail ? Axios.resRej(data) : Promise.resolve(Axios.resRes({ data: { url, config } }));
  }),
  create: jest.fn(() => Axios),

  reset: () => {
    Axios.delete.mockClear();
    Axios.get.mockClear();
    Axios.patch.mockClear();
    Axios.post.mockClear();
    Axios.put.mockClear();
    Axios.fail = false;
    Axios.reqRes = config => config;
    Axios.reqRej = error => Promise.reject(error);
    Axios.resRes = res => res;
    Axios.resRej = error => Promise.reject(error);
  },
  defaults: {},
};

Axios.reset();

module.exports = Axios;
