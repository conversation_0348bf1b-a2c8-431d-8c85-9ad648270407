{"compilerOptions": {"target": "ES2020", "module": "esnext", "moduleResolution": "node", "jsx": "react", "baseUrl": "./src", "paths": {"*": ["*"]}, "allowSyntheticDefaultImports": true, "checkJs": false, "lib": ["DOM", "DOM.Iterable", "ES2020"]}, "typeAcquisition": {"enable": true, "include": ["jest"]}, "include": ["**/*"], "exclude": ["node_modules", "dist", "build", "**/*.test.js", "**/*.test.jsx", "**/__snapshots__"]}