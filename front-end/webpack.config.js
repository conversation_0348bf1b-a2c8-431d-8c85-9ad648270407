const webpack = require('webpack');
const path = require('path');

const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');

const BUILD_DIRECTORY = path.join(__dirname, 'build');
const SRC_DIRECTORY = path.join(__dirname, 'src');

module.exports = (env, argv) => {
  const DEV = argv.mode !== 'production';
  const config = {
    entry: {
      index: [ SRC_DIRECTORY ],
      preview: [ path.join(SRC_DIRECTORY, 'preview') ],
    },
    optimization: {
      splitChunks: {
        cacheGroups: {
          commons: {
            name: 'commons',
            chunks: 'initial',
            minChunks: 2,
          },
        },
      },
    },
    output: {
      path: BUILD_DIRECTORY,
      filename: '[name].[fullhash].js',
      publicPath: '/assets/',
    },
    resolve: {
      extensions: [ '.js', '.jsx' ],
      modules: [ path.resolve(__dirname, 'src', '..'), 'node_modules' ],
    },
    module: {
      rules: [ {
        test: /\.(sc|c)ss$/,
        use: [
          { loader: MiniCssExtractPlugin.loader },
          'css-loader',
          { loader: 'sass-loader', options: { implementation: require('sass'), sassOptions: { includePaths: [ SRC_DIRECTORY ] } } },
        ],
      }, {
        test: /\.(eot|svg|ttf|woff|woff2|png|otf)$/,
        type: 'asset/resource',
        dependency: { not: [ 'url' ] },
      }, {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        loader: 'babel-loader',
      }, {
        test: /\.html$/,
        loader: 'html-loader',
      } ],
    },
  };

  if (DEV) {
    const HtmlWebpackHarddiskPlugin = require('html-webpack-harddisk-plugin');
    const devConfig = {
      devtool: 'source-map',
      mode: 'development',
      devServer: {
        historyApiFallback: {
          rewrites: [
            { from: /^\/preview/, to: '/assets/preview.html' },
          ],
        },
        static: BUILD_DIRECTORY,
      },
      plugins: [
        new CopyPlugin({
          patterns: [
            { from: '../node_modules/ejs/ejs.min.js', to: '.' },
        ] }),
        new MiniCssExtractPlugin({
          filename: '[name].[contenthash].css',
        }),
        new HtmlWebpackPlugin({
          alwaysWriteToDisk: true,
          template: path.join(SRC_DIRECTORY, 'assets', 'index.html'),
          chunks: [ 'commons', 'index' ],
          filename: 'index.html',
        }),
        new HtmlWebpackPlugin({
          alwaysWriteToDisk: true,
          template: path.join(SRC_DIRECTORY, 'assets', 'preview.html'),
          chunks: [ 'commons', 'preview' ],
          filename: 'preview.html',
        }),
        new HtmlWebpackHarddiskPlugin(),
        new webpack.HotModuleReplacementPlugin(),
        new NodePolyfillPlugin(),
      ],
    };
    return { ...config, ...devConfig };
  } else {
    const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
    const prodConfig = {
      mode: 'production',
      plugins: [
        new CopyPlugin({
          patterns: [
            { from: '../node_modules/ejs/ejs.min.js', to: '.' },
        ] }),
        new MiniCssExtractPlugin({
          filename: '[name].[contenthash].css',
        }),
        new CssMinimizerPlugin(),
        new HtmlWebpackPlugin({
          template: path.join(SRC_DIRECTORY, 'assets', 'index.html'),
          chunks: [ 'commons', 'index' ],
          filename: 'index.html',
        }),
        new HtmlWebpackPlugin({
          template: path.join(SRC_DIRECTORY, 'assets', 'preview.html'),
          chunks: [ 'commons', 'preview' ],
          filename: 'preview.html',
        }),
        new webpack.IgnorePlugin({
          resourceRegExp: /^\.\/locale$/,
          contextRegExp: /moment$/,
        }),
        new webpack.ProvidePlugin({
          'Math.random': 'math-random',
        }),
        new NodePolyfillPlugin(),
      ],
    };
    return { ...config, ...prodConfig };
  }
};
