// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Message Centre campaigns list Should not call get messages at initial load 1`] = `
<body
  style="overflow: unset;"
>
  <div>
    <div
      class="page-info__title-action"
    >
      <h1
        class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iejpSD"
        color="black"
        data-testid="page-info-title"
      >
        Messages
         
      </h1>
    </div>
    <div
      class="admin-list__msg-centre-filter-accordion"
    >
      <div
        class="admin-list__msg-centre-search"
      >
        <div
          class="admin-list__msg-centre-search-dropdown"
        >
          <div
            class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
          >
            <div
              class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
            >
              <div
                class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
              >
                <label
                  class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                  for="search-field-selector"
                />
              </div>
              <select
                aria-describedby=""
                class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                id="search-field-selector"
                name="search-field-selector"
              >
                <option
                  data-testid="cssNumber-option"
                  value="cssNumber"
                >
                  ScotiaCard Number
                </option>
                <option
                  data-testid="cidNumber-option"
                  value="cidNumber"
                >
                  Customer ID
                </option>
                <option
                  data-testid="messageId-option"
                  value="messageId"
                >
                  Message ID
                </option>
              </select>
              <svg
                aria-hidden="true"
                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                color="currentColor"
                focusable="false"
                role="presentation"
                size="18"
                viewBox="0 0 30 30"
              >
                <path
                  d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
          </div>
        </div>
        <div
          class="admin-list__msg-centre-search-input"
        >
          <div
            class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline"
          >
            <label
              class="Searchstyle__SearchLabelDesktop-canvas-core__sc-xli5u-3 kreROw Search__label--desktop"
              for="name-input"
              id="name-label-desktop"
            >
              <span
                class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
              >
                Search ScotiaCard Number
              </span>
            </label>
            <div
              class="Searchstyle__InputWrapper-canvas-core__sc-xli5u-0 faizXP"
              role="search"
            >
              <label
                class="Searchstyle__SearchLabelMobile-canvas-core__sc-xli5u-2 gTlGrz Search__label--mobile"
                for="name-input"
                id="name-label-mobile"
              >
                <button
                  class="Searchstyle__SearchButtonMobile-canvas-core__sc-xli5u-5 jHXkhr Search__button Search__button--search"
                  type="submit"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon Search__Icon"
                    color="black"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M15.6248 15.6249L21.6227 21.6228"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      clip-rule="evenodd"
                      d="M17.7135 9.85775C17.7135 5.51885 14.1957 2 9.85677 2C5.51787 2 2 5.51885 2 9.85775C2 14.1957 5.51787 17.7135 9.85677 17.7135C14.1957 17.7135 17.7135 14.1957 17.7135 9.85775Z"
                      fill="none"
                      fill-rule="evenodd"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <span
                    class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                  >
                    Search ScotiaCard Number
                  </span>
                </button>
              </label>
              <input
                aria-describedby="Search__errors-name"
                class="Searchstyle__SearchInput-canvas-core__sc-xli5u-1 jtIJGo Search__input"
                id="name-input"
                placeholder="Search ScotiaCard Number"
                value=""
              />
              <button
                aria-label="Search ScotiaCard Number"
                class="Searchstyle__SearchButton-canvas-core__sc-xli5u-4 kmxzup Search__button Search__button--search"
                type="submit"
              >
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon Search__buttonIcon"
                  color="black"
                  focusable="false"
                  role="presentation"
                  size="18"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M15.6248 15.6249L21.6227 21.6228"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    clip-rule="evenodd"
                    d="M17.7135 9.85775C17.7135 5.51885 14.1957 2 9.85677 2C5.51787 2 2 5.51885 2 9.85775C2 14.1957 5.51787 17.7135 9.85677 17.7135C14.1957 17.7135 17.7135 14.1957 17.7135 9.85775Z"
                    fill="none"
                    fill-rule="evenodd"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
              
            </div>
            <div
              id="Search__errors-name"
            />
          </div>
        </div>
      </div>
      <div
        class="admin-list__action-button-container"
      >
        <button
          class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button"
          color="blue"
        >
          <svg
            aria-hidden="true"
            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
            color="currentColor"
            focusable="false"
            role="presentation"
            size="16"
            viewBox="0 0 30 30"
          >
            <path
              clip-rule="evenodd"
              d="M15.0911 7.80238C13.8895 7.80238 12.9182 6.83003 12.9182 5.62953C12.9182 4.42903 13.8895 3.45667 15.0911 3.45667C16.2916 3.45667 17.2639 4.42903 17.2639 5.62953C17.2639 6.83003 16.2916 7.80238 15.0911 7.80238Z"
              fill="none"
              fill-rule="evenodd"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              clip-rule="evenodd"
              d="M9.96851 17.1728C8.76801 17.1728 7.79565 16.2004 7.79565 14.9999C7.79565 13.7994 8.76801 12.8271 9.96851 12.8271C11.169 12.8271 12.1414 13.7994 12.1414 14.9999C12.1414 16.2004 11.169 17.1728 9.96851 17.1728Z"
              fill="none"
              fill-rule="evenodd"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              clip-rule="evenodd"
              d="M19.9636 26.5432C18.762 26.5432 17.7908 25.5708 17.7908 24.3703C17.7908 23.1698 18.762 22.1975 19.9636 22.1975C21.1641 22.1975 22.1365 23.1698 22.1365 24.3703C22.1365 25.5708 21.1641 26.5432 19.9636 26.5432Z"
              fill="none"
              fill-rule="evenodd"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M12.7056 15.1696H28.2969"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M1.33056 5.53895H12.7049"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M17.4854 5.79923H28.3174"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M1.31748 24.2797H17.4852"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M22.7002 24.54H28.2944"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M1.34448 14.9093H6.83822"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          <span
            class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
          >
            Filters
          </span>
        </button>
      </div>
    </div>
    <div
      class="admin-list admin-list__listings-table"
    >
      <div
        class="admin-list__action-bar"
      >
        <h1
          class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iejpSD admin-list__header"
          color="black"
        >
          
           
        </h1>
      </div>
      <div
        class="Tablestyle__StyledTable-canvas-core__sc-mfsabp-0 dvntiZ Table"
      >
        <div
          class="Tablestyle__StyledTableWrapper-canvas-core__sc-mfsabp-1 ikRFBm Table__wrapper"
          id="table-admin-list-table"
        >
          <table
            class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable admin-list__table"
          >
            <caption
              aria-live="polite"
              class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP TableHeadingstyle__StyledHeading-canvas-core__sc-2qo8vm-0 bPEdEe Table__title"
              color="black"
              size="21"
            />
            <thead
              class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
              role="rowgroup"
            >
              <tr
                class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
                role="row"
              >
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 bURnut"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Type
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    ID
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 enavWZ"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Channel
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 enavWZ"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Language
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 enavWZ"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Status
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 enavWZ"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Priority
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 enavWZ"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Last Update Date
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 kQJGxh"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Subject
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 enavWZ"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Action
                  </p>
                </th>
              </tr>
            </thead>
            <tbody
              class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
              role="rowgroup"
            >
              <tr
                class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jGMNii"
                role="row"
              >
                <th
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 fIopTL"
                  role="rowheader"
                  scope="row"
                  style="display: flex; flex-flow: wrap;"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                    color="black"
                  >
                    ABM
                  </p>
                </th>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                  role="cell"
                  style="text-align: left;"
                >
                  <a
                    class="Linkstyle__Wrapper-canvas-core__sc-nzeldc-1 hqbNYC admin-list__name-link"
                    href="/campaigns?external_ref=TARGETED&campaign_id=PAC79"
                    size="16"
                    theme="[object Object]"
                    type="emphasis"
                    weight="bold"
                  >
                    <span
                      class="Linkstyle__Text-canvas-core__sc-nzeldc-0 ictHEk"
                      type="emphasis"
                    >
                      PAC79
                    </span>
                  </a>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bjmDAX"
                  role="cell"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                    color="black"
                  >
                    SOL
                  </p>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bjmDAX"
                  role="cell"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                    color="black"
                  >
                    English
                  </p>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bjmDAX"
                  role="cell"
                  style="height: 100%;"
                >
                  <span
                    class="StatusBadgestyle__StyledStatusBadge-canvas-core__sc-1r5tuo3-0 eZXykc"
                  >
                    <span
                      class="sr-Only"
                    >
                      NEW
                    </span>
                    <span
                      aria-hidden="true"
                      class="capsText"
                    >
                      NEW
                    </span>
                  </span>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bjmDAX"
                  role="cell"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                    color="black"
                  >
                    Urgent
                  </p>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bjmDAX"
                  role="cell"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                    color="black"
                  >
                    Invalid date
                  </p>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                  role="cell"
                  style="text-align: left; word-break: break-word;"
                >
                  <button
                    class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button admin-list__name-link"
                    color="blue"
                  >
                    <span
                      class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                    >
                      Bill Payee removed - action required.
                    </span>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 jvjhqp TextButton__icon--right"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="16"
                      viewBox="0 0 24 24"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M22.7997 11.9998C22.7997 17.9634 17.9633 22.7998 11.9997 22.7998C6.03418 22.7998 1.19971 17.9634 1.19971 11.9998C1.19971 6.03428 6.03418 1.1998 11.9997 1.1998C17.9633 1.1998 22.7997 6.03428 22.7997 11.9998Z"
                        fill="none"
                        fill-rule="evenodd"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        clip-rule="evenodd"
                        d="M11.9997 17.3248C12.331 17.3248 12.5997 17.0562 12.5997 16.7248V11.3248C12.5997 10.9935 12.331 10.7248 11.9997 10.7248C11.6683 10.7248 11.3997 10.9935 11.3997 11.3248V16.7248C11.3997 17.0562 11.6683 17.3248 11.9997 17.3248Z"
                        fill-rule="evenodd"
                        stroke="none"
                      />
                      <ellipse
                        cx="11.9996"
                        cy="7.30462"
                        rx="0.747122"
                        ry="0.746635"
                        stroke="none"
                      />
                    </svg>
                  </button>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bjmDAX"
                  role="cell"
                >
                  <div
                    class="ActionMenustyle__MenuWrapper-canvas-core__sc-bc19ak-0 kqkdaS"
                  >
                    <button
                      aria-expanded="false"
                      aria-haspopup="true"
                      aria-label="Action menu"
                      class="ActionMenustyle__WrapperKebob-canvas-core__sc-bc19ak-1 kvdpEH ActionMenu__KebabButton"
                      data-testid="ActionMenuList-test"
                      id="kebob-button"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 cuYZRS SvgIcon__icon rotate90"
                        color="black"
                        focusable="false"
                        role="presentation"
                        size="24"
                        viewBox="0 0 32 32"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M13 5.49992C13 3.84392 14.344 2.49992 16 2.49992C17.6575 2.49992 19 3.84392 19 5.49992C19 7.15591 17.6575 8.49991 16 8.49991C14.344 8.49991 13 7.15591 13 5.49992ZM13 26.5C13 24.844 14.344 23.5 16 23.5C17.6575 23.5 19 24.844 19 26.5C19 28.156 17.6575 29.5 16 29.5C14.344 29.5 13 28.156 13 26.5ZM16 12.9999C14.344 12.9999 13 14.3439 13 15.9999C13 17.6559 14.344 18.9999 16 18.9999C17.6575 18.9999 19 17.6559 19 15.9999C19 14.3439 17.6575 12.9999 16 12.9999Z"
                          fill-rule="evenodd"
                          stroke="none"
                        />
                      </svg>
                    </button>
                    <div>
                      <ul
                        class="ActionMenuListDesktopstyle__WrapperDesktop-canvas-core__sc-1x08cuj-0 hCExhu ActionMenu__dialog"
                        data-testid="actionmenu-ul"
                      >
                        <li
                          class="ActionMenuListItemstyle__MenuListItem-canvas-core__sc-41fdr-2 cezwAS"
                        >
                          <button
                            class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 eEjFkP TextButton__button ActionMenuListItemstyle__StyledButton-canvas-core__sc-41fdr-0 eMBMPI"
                            color="black"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="16"
                              viewBox="0 0 30 30"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M18.2141 28.4999H4.07129V9.21421L11.7856 1.49992H25.9284V15.6428V28.4999H18.2141Z"
                                fill="none"
                                fill-rule="evenodd"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M4.07129 9.21421H11.7856V1.49992"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M8.76514 14.291H21.4386"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M8.76514 18.5155H21.4386"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M8.76514 22.7394H21.4386"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                            </svg>
                            <span
                              class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                            >
                              View
                            </span>
                          </button>
                        </li>
                        <li
                          class="ActionMenuListItemstyle__MenuListItem-canvas-core__sc-41fdr-2 cezwAS"
                        >
                          <button
                            class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 eEjFkP TextButton__button ActionMenuListItemstyle__StyledButton-canvas-core__sc-41fdr-0 eMBMPI"
                            color="black"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="16"
                              viewBox="0 0 30 30"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M28.5 8.99917L8.99965 28.4999H1.5V21.0007L21.0004 1.49992L28.5 8.99917Z"
                                fill="none"
                                fill-rule="evenodd"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M16.1143 6.47101L23.4159 13.7729"
                                fill="none"
                                stroke-linecap="square"
                              />
                            </svg>
                            <span
                              class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                            >
                              Edit
                            </span>
                          </button>
                        </li>
                      </ul>
                    </div>
                  </div>
                </td>
              </tr>
              <tr
                class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jGMNii"
                role="row"
              >
                <th
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 fIopTL"
                  role="rowheader"
                  scope="row"
                  style="display: flex; flex-flow: wrap;"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                    color="black"
                  >
                    SOL
                  </p>
                </th>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                  role="cell"
                  style="text-align: left;"
                >
                  <a
                    class="Linkstyle__Wrapper-canvas-core__sc-nzeldc-1 hqbNYC admin-list__name-link"
                    href="/campaigns/sol?campaign_id=PAC79"
                    size="16"
                    theme="[object Object]"
                    type="emphasis"
                    weight="bold"
                  >
                    <span
                      class="Linkstyle__Text-canvas-core__sc-nzeldc-0 ictHEk"
                      type="emphasis"
                    >
                      PAC79
                    </span>
                  </a>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bjmDAX"
                  role="cell"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                    color="black"
                  >
                    SOL
                  </p>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bjmDAX"
                  role="cell"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                    color="black"
                  >
                    English
                  </p>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bjmDAX"
                  role="cell"
                  style="height: 100%;"
                >
                  <span
                    class="StatusBadgestyle__StyledStatusBadge-canvas-core__sc-1r5tuo3-0 fxzlCQ"
                  >
                    <span
                      class="sr-Only"
                    >
                      SUSPENDED
                    </span>
                    <span
                      aria-hidden="true"
                      class="capsText"
                    >
                      SUSPENDED
                    </span>
                  </span>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bjmDAX"
                  role="cell"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                    color="black"
                  >
                    Urgent
                  </p>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bjmDAX"
                  role="cell"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                    color="black"
                  >
                    Oct 28, 2024
                  </p>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                  role="cell"
                  style="text-align: left; word-break: break-word;"
                >
                  <button
                    class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button admin-list__name-link"
                    color="blue"
                  >
                    <span
                      class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                    >
                      Bill Payee removed - action required.
                    </span>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 jvjhqp TextButton__icon--right"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="16"
                      viewBox="0 0 24 24"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M22.7997 11.9998C22.7997 17.9634 17.9633 22.7998 11.9997 22.7998C6.03418 22.7998 1.19971 17.9634 1.19971 11.9998C1.19971 6.03428 6.03418 1.1998 11.9997 1.1998C17.9633 1.1998 22.7997 6.03428 22.7997 11.9998Z"
                        fill="none"
                        fill-rule="evenodd"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        clip-rule="evenodd"
                        d="M11.9997 17.3248C12.331 17.3248 12.5997 17.0562 12.5997 16.7248V11.3248C12.5997 10.9935 12.331 10.7248 11.9997 10.7248C11.6683 10.7248 11.3997 10.9935 11.3997 11.3248V16.7248C11.3997 17.0562 11.6683 17.3248 11.9997 17.3248Z"
                        fill-rule="evenodd"
                        stroke="none"
                      />
                      <ellipse
                        cx="11.9996"
                        cy="7.30462"
                        rx="0.747122"
                        ry="0.746635"
                        stroke="none"
                      />
                    </svg>
                  </button>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bjmDAX"
                  role="cell"
                >
                  <div
                    class="ActionMenustyle__MenuWrapper-canvas-core__sc-bc19ak-0 kqkdaS"
                  >
                    <button
                      aria-expanded="false"
                      aria-haspopup="true"
                      aria-label="Action menu"
                      class="ActionMenustyle__WrapperKebob-canvas-core__sc-bc19ak-1 kvdpEH ActionMenu__KebabButton"
                      data-testid="ActionMenuList-test"
                      id="kebob-button"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 cuYZRS SvgIcon__icon rotate90"
                        color="black"
                        focusable="false"
                        role="presentation"
                        size="24"
                        viewBox="0 0 32 32"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M13 5.49992C13 3.84392 14.344 2.49992 16 2.49992C17.6575 2.49992 19 3.84392 19 5.49992C19 7.15591 17.6575 8.49991 16 8.49991C14.344 8.49991 13 7.15591 13 5.49992ZM13 26.5C13 24.844 14.344 23.5 16 23.5C17.6575 23.5 19 24.844 19 26.5C19 28.156 17.6575 29.5 16 29.5C14.344 29.5 13 28.156 13 26.5ZM16 12.9999C14.344 12.9999 13 14.3439 13 15.9999C13 17.6559 14.344 18.9999 16 18.9999C17.6575 18.9999 19 17.6559 19 15.9999C19 14.3439 17.6575 12.9999 16 12.9999Z"
                          fill-rule="evenodd"
                          stroke="none"
                        />
                      </svg>
                    </button>
                    <div>
                      <ul
                        class="ActionMenuListDesktopstyle__WrapperDesktop-canvas-core__sc-1x08cuj-0 hCExhu ActionMenu__dialog"
                        data-testid="actionmenu-ul"
                      >
                        <li
                          class="ActionMenuListItemstyle__MenuListItem-canvas-core__sc-41fdr-2 cezwAS"
                        >
                          <button
                            class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 eEjFkP TextButton__button ActionMenuListItemstyle__StyledButton-canvas-core__sc-41fdr-0 eMBMPI"
                            color="black"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="16"
                              viewBox="0 0 30 30"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M18.2141 28.4999H4.07129V9.21421L11.7856 1.49992H25.9284V15.6428V28.4999H18.2141Z"
                                fill="none"
                                fill-rule="evenodd"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M4.07129 9.21421H11.7856V1.49992"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M8.76514 14.291H21.4386"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M8.76514 18.5155H21.4386"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M8.76514 22.7394H21.4386"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                            </svg>
                            <span
                              class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                            >
                              View
                            </span>
                          </button>
                        </li>
                        <li
                          class="ActionMenuListItemstyle__MenuListItem-canvas-core__sc-41fdr-2 cezwAS"
                        >
                          <button
                            class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 eEjFkP TextButton__button ActionMenuListItemstyle__StyledButton-canvas-core__sc-41fdr-0 eMBMPI"
                            color="black"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="16"
                              viewBox="0 0 30 30"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M28.5 8.99917L8.99965 28.4999H1.5V21.0007L21.0004 1.49992L28.5 8.99917Z"
                                fill="none"
                                fill-rule="evenodd"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M16.1143 6.47101L23.4159 13.7729"
                                fill="none"
                                stroke-linecap="square"
                              />
                            </svg>
                            <span
                              class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                            >
                              Edit
                            </span>
                          </button>
                        </li>
                      </ul>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <nav
      aria-label="Pagination Navigation"
      class="DesktopPaginationstyle__Wrapper-canvas-core__sc-6osluo-0 dEkrda DesktopPagination__container DesktopPagination__container--card"
      id="message-centre-messages-pagination"
    >
      <span
        aria-live="polite"
        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 bhzbbw TextCaption__text"
        color="gray"
      >
        NaN-NaN of 0 results
      </span>
      <ul
        class="DesktopPagination__list"
      >
        <li
          class="PaginationItemstyle__Wrapper-canvas-core__sc-1gx7xda-0 ctDMuy PaginationItem"
        >
          <button
            aria-current="false"
            aria-label="Previous"
            class="PaginationItemstyle__Button-canvas-core__sc-1gx7xda-1 hnTLVo PaginationItem__btn"
          >
            <div
              class="PaginationItemstyle__ContentBox-canvas-core__sc-1gx7xda-2 jQIOPj PaginationItem__contentBox"
            >
              <svg
                aria-hidden="true"
                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon"
                color="currentColor"
                focusable="false"
                role="presentation"
                size="18"
                viewBox="0 0 30 30"
              >
                <path
                  d="M21.75 28.4999L8.25 14.9999L21.75 1.49991"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
          </button>
        </li>
        <li
          class="PaginationItemstyle__Wrapper-canvas-core__sc-1gx7xda-0 ctDMuy PaginationItem"
        >
          <button
            aria-current="false"
            aria-label="Next"
            class="PaginationItemstyle__Button-canvas-core__sc-1gx7xda-1 hnTLVo PaginationItem__btn"
          >
            <div
              class="PaginationItemstyle__ContentBox-canvas-core__sc-1gx7xda-2 jQIOPj PaginationItem__contentBox"
            >
              <svg
                aria-hidden="true"
                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon"
                color="currentColor"
                focusable="false"
                role="presentation"
                size="18"
                viewBox="0 0 30 30"
              >
                <path
                  d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
          </button>
        </li>
      </ul>
    </nav>
  </div>
</body>
`;

exports[`Message Centre campaigns list Should render spinner while loading 1`] = `
<body>
  <div>
    <div
      class="page-info__title-action"
    >
      <h1
        class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iejpSD"
        color="black"
        data-testid="page-info-title"
      >
        Messages
         
      </h1>
    </div>
    <div
      class="admin-list__msg-centre-filter-accordion"
    >
      <div
        class="admin-list__msg-centre-search"
      >
        <div
          class="admin-list__msg-centre-search-dropdown"
        >
          <div
            class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
          >
            <div
              class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
            >
              <div
                class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
              >
                <label
                  class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                  for="search-field-selector"
                />
              </div>
              <select
                aria-describedby=""
                class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                id="search-field-selector"
                name="search-field-selector"
              >
                <option
                  data-testid="cssNumber-option"
                  value="cssNumber"
                >
                  ScotiaCard Number
                </option>
                <option
                  data-testid="cidNumber-option"
                  value="cidNumber"
                >
                  Customer ID
                </option>
                <option
                  data-testid="messageId-option"
                  value="messageId"
                >
                  Message ID
                </option>
              </select>
              <svg
                aria-hidden="true"
                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                color="currentColor"
                focusable="false"
                role="presentation"
                size="18"
                viewBox="0 0 30 30"
              >
                <path
                  d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
          </div>
        </div>
        <div
          class="admin-list__msg-centre-search-input"
        >
          <div
            class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline"
          >
            <label
              class="Searchstyle__SearchLabelDesktop-canvas-core__sc-xli5u-3 kreROw Search__label--desktop"
              for="name-input"
              id="name-label-desktop"
            >
              <span
                class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
              >
                Search ScotiaCard Number
              </span>
            </label>
            <div
              class="Searchstyle__InputWrapper-canvas-core__sc-xli5u-0 faizXP"
              role="search"
            >
              <label
                class="Searchstyle__SearchLabelMobile-canvas-core__sc-xli5u-2 gTlGrz Search__label--mobile"
                for="name-input"
                id="name-label-mobile"
              >
                <button
                  class="Searchstyle__SearchButtonMobile-canvas-core__sc-xli5u-5 jHXkhr Search__button Search__button--search"
                  type="submit"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon Search__Icon"
                    color="black"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M15.6248 15.6249L21.6227 21.6228"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      clip-rule="evenodd"
                      d="M17.7135 9.85775C17.7135 5.51885 14.1957 2 9.85677 2C5.51787 2 2 5.51885 2 9.85775C2 14.1957 5.51787 17.7135 9.85677 17.7135C14.1957 17.7135 17.7135 14.1957 17.7135 9.85775Z"
                      fill="none"
                      fill-rule="evenodd"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <span
                    class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                  >
                    Search ScotiaCard Number
                  </span>
                </button>
              </label>
              <input
                aria-describedby="Search__errors-name"
                class="Searchstyle__SearchInput-canvas-core__sc-xli5u-1 jtIJGo Search__input"
                id="name-input"
                placeholder="Search ScotiaCard Number"
                value=""
              />
              <button
                aria-label="Search ScotiaCard Number"
                class="Searchstyle__SearchButton-canvas-core__sc-xli5u-4 kmxzup Search__button Search__button--search"
                type="submit"
              >
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon Search__buttonIcon"
                  color="black"
                  focusable="false"
                  role="presentation"
                  size="18"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M15.6248 15.6249L21.6227 21.6228"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    clip-rule="evenodd"
                    d="M17.7135 9.85775C17.7135 5.51885 14.1957 2 9.85677 2C5.51787 2 2 5.51885 2 9.85775C2 14.1957 5.51787 17.7135 9.85677 17.7135C14.1957 17.7135 17.7135 14.1957 17.7135 9.85775Z"
                    fill="none"
                    fill-rule="evenodd"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
              
            </div>
            <div
              id="Search__errors-name"
            />
          </div>
        </div>
      </div>
      <div
        class="admin-list__action-button-container"
      >
        <button
          class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button"
          color="blue"
        >
          <svg
            aria-hidden="true"
            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
            color="currentColor"
            focusable="false"
            role="presentation"
            size="16"
            viewBox="0 0 30 30"
          >
            <path
              clip-rule="evenodd"
              d="M15.0911 7.80238C13.8895 7.80238 12.9182 6.83003 12.9182 5.62953C12.9182 4.42903 13.8895 3.45667 15.0911 3.45667C16.2916 3.45667 17.2639 4.42903 17.2639 5.62953C17.2639 6.83003 16.2916 7.80238 15.0911 7.80238Z"
              fill="none"
              fill-rule="evenodd"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              clip-rule="evenodd"
              d="M9.96851 17.1728C8.76801 17.1728 7.79565 16.2004 7.79565 14.9999C7.79565 13.7994 8.76801 12.8271 9.96851 12.8271C11.169 12.8271 12.1414 13.7994 12.1414 14.9999C12.1414 16.2004 11.169 17.1728 9.96851 17.1728Z"
              fill="none"
              fill-rule="evenodd"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              clip-rule="evenodd"
              d="M19.9636 26.5432C18.762 26.5432 17.7908 25.5708 17.7908 24.3703C17.7908 23.1698 18.762 22.1975 19.9636 22.1975C21.1641 22.1975 22.1365 23.1698 22.1365 24.3703C22.1365 25.5708 21.1641 26.5432 19.9636 26.5432Z"
              fill="none"
              fill-rule="evenodd"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M12.7056 15.1696H28.2969"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M1.33056 5.53895H12.7049"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M17.4854 5.79923H28.3174"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M1.31748 24.2797H17.4852"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M22.7002 24.54H28.2944"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M1.34448 14.9093H6.83822"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          <span
            class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
          >
            Filters
          </span>
        </button>
      </div>
    </div>
    <div
      class="admin-list admin-list__listings-table"
    >
      <div
        class="admin-list__action-bar"
      >
        <h1
          class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iejpSD admin-list__header"
          color="black"
        >
          
           
          <svg
            aria-hidden="true"
            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 lasFnB SvgIcon__icon"
            color="inherit"
            focusable="false"
            preserveAspectRatio="xMidYMid"
            role="presentation"
            size="24"
            viewBox="0 0 100 100"
          >
            <circle
              cx="50"
              cy="50"
              fill="none"
              r="45"
              stroke-dasharray="164.93361431346415 56.97787143782138"
              stroke-width="7"
              transform="rotate(243.787 50 50)"
            >
              <animatetransform
                attributeName="transform"
                begin="0s"
                calcMode="linear"
                dur="6s"
                keyTimes="0;1"
                repeatCount="indefinite"
                type="rotate"
                values="0 50 50;360 50 50"
              />
            </circle>
          </svg>
        </h1>
      </div>
      <div
        class="Tablestyle__StyledTable-canvas-core__sc-mfsabp-0 dvntiZ Table"
      >
        <div
          class="Tablestyle__StyledTableWrapper-canvas-core__sc-mfsabp-1 ikRFBm Table__wrapper"
          id="table-admin-list-table"
        >
          <table
            class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable admin-list__table"
          >
            <caption
              aria-live="polite"
              class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP TableHeadingstyle__StyledHeading-canvas-core__sc-2qo8vm-0 bPEdEe Table__title"
              color="black"
              size="21"
            />
            <thead
              class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
              role="rowgroup"
            >
              <tr
                class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
                role="row"
              >
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 bURnut"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Type
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    ID
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 enavWZ"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Channel
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 enavWZ"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Language
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 enavWZ"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Status
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 enavWZ"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Priority
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 enavWZ"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Last Update Date
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 kQJGxh"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Subject
                  </p>
                </th>
                <th
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 enavWZ"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Action
                  </p>
                </th>
              </tr>
            </thead>
            <tbody
              class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
              role="rowgroup"
            />
          </table>
        </div>
      </div>
    </div>
  </div>
</body>
`;
