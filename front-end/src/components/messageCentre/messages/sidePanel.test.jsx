import React from 'react';
import { fireEvent, queryByText, waitFor } from '@testing-library/react';
import { within } from '@testing-library/dom';
import { renderPage } from '../../../utils/testing-library-utils';
import { Router } from 'react-router-dom';

import { MESSAGE_STATUS_MAPPING } from './constants';
import mockMessageDetails from '../../../../../src/__mocks__/mockMessageDetails';
import MessageCentreMessageList from './listContainer';

const mockLocation = { pathname: '/message-centre/messages' };

describe('MessageCentreMessageSidePanel', () => {
  test('side panel - should display all required fields', async() => {
    const { getAllByText } = await renderPage(<MessageCentreMessageList />, {
      initialState: { messageCentreMessages: { items: mockMessageDetails } },
    });
    fireEvent.click(getAllByText(mockMessageDetails[0].subject_line)[0]);

    const sidePanelSection = document.getElementsByClassName('admin-list__panel-offset')[0];

    expect(queryByText(sidePanelSection, 'Created by (Team)')).toBeInTheDocument();
    expect(queryByText(sidePanelSection, 'Created by (User)')).toBeInTheDocument();
    expect(queryByText(sidePanelSection, 'Created on')).toBeInTheDocument();
    expect(queryByText(sidePanelSection, 'Last updated by (Team)')).toBeInTheDocument();
    expect(queryByText(sidePanelSection, 'Last updated by (User)')).toBeInTheDocument();
    expect(queryByText(sidePanelSection, 'Last updated on')).toBeInTheDocument();
  });

  test('side panel - close modal', async() => {
    const { getAllByText } = await renderPage(<MessageCentreMessageList />, {
      initialState: { messageCentreMessages: { items: mockMessageDetails } },
    });
    fireEvent.click(getAllByText(mockMessageDetails[0].subject_line)[0]);

    const panelSection = document.getElementsByClassName('admin-list__panel-offset')[0];
    expect(panelSection).toBeInTheDocument();

    // Button: #0 = Close, #1 = View, #2 = Edit
    fireEvent.click(within(panelSection).getAllByRole('button')[0]);

    expect(
      within(panelSection).getAllByRole('presentation', { hidden: true }).length > 0,
    ).toBeTruthy();
  });

  test('side panel - should display correct status', async() => {
    const { getAllByText } = await renderPage(<MessageCentreMessageList />, {
      initialState: { messageCentreMessages: { items: mockMessageDetails } },
    });
    fireEvent.click(getAllByText(mockMessageDetails[0].subject_line)[0]);

    const panelSection = document.getElementsByClassName('admin-list__panel-offset')[0];
    const msgStatus = MESSAGE_STATUS_MAPPING[mockMessageDetails[0].msg_status];

    expect(within(panelSection).queryAllByText(msgStatus).length > 0).toBeTruthy();
  });

  test('side panel  - should render correct buttons based on status', async() => {
    const { getAllByText } = await renderPage(<MessageCentreMessageList />, {
      initialState: { messageCentreMessages: { items: mockMessageDetails } },
    });

    const getOneMessageByStatus = (messages, status) =>
      messages.filter(message => MESSAGE_STATUS_MAPPING[message.msg_status] === status)[0];

    // New message should have three buttons: close, view, edit
    const newMessage = getOneMessageByStatus(mockMessageDetails, 'new');
    fireEvent.click(getAllByText(newMessage.subject_line)[0]);
    let panelSection = document.getElementsByClassName('admin-list__panel-offset')[0];
    expect(within(panelSection).getAllByRole('button')).toHaveLength(3);

    fireEvent.click(within(panelSection).getAllByRole('button')[0]);

    // Suspended message should also have three buttons
    const suspendedMessage = getOneMessageByStatus(mockMessageDetails, 'suspended');
    fireEvent.click(getAllByText(suspendedMessage.subject_line)[1]);
    panelSection = document.getElementsByClassName('admin-list__panel-offset')[0];
    expect(within(panelSection).getAllByRole('button')).toHaveLength(3);

    fireEvent.click(within(panelSection).getAllByRole('button')[0]);

    // Deleted message should have only two buttons: close, view
    const deletedMessage = getOneMessageByStatus(mockMessageDetails, 'deleted');
    fireEvent.click(getAllByText(deletedMessage.subject_line)[2]);
    panelSection = document.getElementsByClassName('admin-list__panel-offset')[0];
    expect(within(panelSection).getAllByRole('button')).toHaveLength(2);
  });

  test('view button on side panel should route user to view page', async() => {
    const push = jest.fn();
    const history = { push, listen: jest.fn(), location: mockLocation, createHref: jest.fn() };
    const { getAllByText } = await renderPage(
      <Router history={history}>
        <MessageCentreMessageList />
      </Router>,
      {
        initialState: { messageCentreMessages: { items: mockMessageDetails } },
      },
    );
    push.mockReset();

    fireEvent.click(getAllByText(mockMessageDetails[0].subject_line)[0]);
    const panelSection = document.getElementsByClassName('admin-list__panel-offset')[0];

    fireEvent.click(within(panelSection).getAllByRole('button')[1]);
    await waitFor(() => {
      expect(history.push).toHaveBeenCalledWith(
        { pathname: `/message-centre/messages/${mockMessageDetails[0].id}/view`,
        'search': 'language=E&channel=ABM',
        }
      );
    });
  });
  test('edit button on side panel should route user to the edit page', async() => {
    const push = jest.fn();
    const history = { push, listen: jest.fn(), location: mockLocation, createHref: jest.fn() };
    const { getAllByText } = await renderPage(
      <Router history={history}>
        <MessageCentreMessageList />
      </Router>,
      {
        initialState: { messageCentreMessages: { items: mockMessageDetails } },
      },
    );
    push.mockReset();

    fireEvent.click(getAllByText(mockMessageDetails[0].subject_line)[0]);
    const panelSection = document.getElementsByClassName('admin-list__panel-offset')[0];

    fireEvent.click(within(panelSection).getAllByRole('button')[2]);
    await waitFor(() => {
      expect(history.push).toHaveBeenCalledWith(
       { pathname: `/message-centre/messages/${mockMessageDetails[0].id}/edit`,
      'search': 'language=E&channel=ABM',
      }
      );
    });
  });
});
