.message-details {
  &__header {
    &.TextIntroduction__text {
      font-size: 3.2rem;
    }
  }

  &__sub-header {
    margin-bottom: 3.6rem;

    &.TextIntroduction__text {
      font-size: 2.4rem;
    }
  }

  &__card {
    margin: 3.6rem 0;
  }

  &__fields {
    margin-bottom: 3.6rem;

    @include mq($from: tablet) {
      display: flex;
      gap: 3.6rem;
      margin-bottom: 0;
    }
  }

  &__field {
    margin-bottom: 3.6rem;

    @include mq($from: tablet) {
      flex: 1;
      margin-bottom: 0;
    }
  }

  &__date {
    margin-bottom: 3.6rem;

    @include mq($from: tablet) {
      flex: 1;
      margin-bottom: 0;
      max-width: 36rem;
    }
  }

  &__label {
    &.TextBody__text {
      font-size: 1.6rem;
    }
  }

  &__text {
    overflow-wrap: break-word;

    &.TextBody__text {
      font-size: 2rem;
    }
  }

  &__channel-label-margin {
    margin-bottom: 1.2rem;
  }

  &__msg-info {
    margin-bottom: 3.6rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  //  &:firs-child  not working for first element
  &__msg-info-first {
    margin-top: 3.6rem;
  }
}
