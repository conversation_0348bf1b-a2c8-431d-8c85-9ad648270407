import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';
import qs from 'qs';
import { omit, difference } from 'lodash';

import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';
import AlertBanner from 'canvas-core-react/lib/AlertBanner';
import Card from 'canvas-core-react/lib/Card';
import TextCaption from 'canvas-core-react/lib/TextCaption';

import PageInfo from '../../pageInfo';

import { useMesssages } from '../../../hooks/useMessages';
import { removeFalsyKeys } from '../../../utils';
import List from '../../listing/list';
import { tableColumns } from './messageColumns';
import Filters from './filters';
import {
  CAMPAIGN_TYPE_OPTIONS,
  CAMPAIGN_CHANNEL_OPTIONS,
  LANGUAGES,
  MESSAGE_STATUS_OPTIONS,
  MESSAGE_PRIORITY_OPTIONS } from './constants';
import IconSummary from 'canvas-core-react/lib/IconSummary';
import IconEdit from 'canvas-core-react/lib/IconEdit';
import ActionMenuListItem from 'canvas-core-react/lib/ActionMenuListItem';
import SidePanel from './sidePanel';
import permissionsList from '../../../constants/permissionsList';
import { exportMessageCentreMessages } from '../../../api/message-centre-messages';

const PAGE_SIZE = 10;
const BASE_QUERY = {
  limit: PAGE_SIZE,
  pageNumber: 1,
  searchQuery: '',
  searchField: '',
  language: '',
  type: '',
  priority: '',
  status: '',
  channel: '',
};

const MessageCentreMessageList = () => {
  const history = useHistory();

  const queryParams = qs.parse(history.location.search, {
    ignoreQueryPrefix: true,
  });
  const initialFilters = {
    ...BASE_QUERY,
    ...omit(
      queryParams,
      difference(Object.keys(queryParams), Object.keys(BASE_QUERY))
    ),
  };

  const [ filters, setFilters ] = useState(initialFilters);
  const [ isActionListOpen, setIsActionListOpen ] = useState(false);
  const [ isSidePanelOpen, setIsSidePanelOpen ] = useState(false);
  const [ selectedMessage, setSelectedMessage ] = useState(false);
  const [ isMessageCentreMessagesInitialLoad, setIsMessageCentreMessagesInitialLoad ] = useState(true);
  const { messagesLoading, messages, pagination, currentUser } = useMesssages(
    removeFalsyKeys(filters)
  );
  const { permissions } = currentUser;

  const canView =
    permissions &&
    (permissions['admin'] ||
      permissions[permissionsList.MESSAGE_CENTRE_MESSAGES_VIEW]);

  if (!canView) {
    return (
      <AlertBanner type="error">
        You do not have permissions to view this page
      </AlertBanner>
    );
  }

  const filtersChanged = (newFilters) => {
    setFilters(newFilters);
  };

  const handleExport = (e) => {
    exportMessageCentreMessages(filters);
  };

  const getActionItems = (row) => {
    if (!row) {
      return;
    }

    const { id } = row;
    const actionListItems = [
      {
        iconType: IconSummary,
        onClick: () => {
          setIsActionListOpen(false);
          // using setTimeout as a workaround as clicking action menu adds overflow: none and is not removed after navigation
          setTimeout(
            () =>
              history.push({
                pathname: `/message-centre/messages/${id}/view`,
                search: new URLSearchParams({
                  language: row?.language,
                  channel: row?.channel,
                }).toString(),
              }),
            0
          );
        },
        menuName: 'View',
      },
      {
        iconType: IconEdit,
        onClick: () => {
          setIsActionListOpen(false);
          // using setTimeout as a workaround as clicking action menu adds overflow: none and is not removed after navigation
          setTimeout(
            () =>
              history.push({
                pathname: `/message-centre/messages/${id}/edit`,
                search: new URLSearchParams({
                  language: row?.language,
                  channel: row?.channel,
                }).toString(),
              }),
            0
          );
        },
        menuName: 'Edit',
      },
    ];
    const msgStatus = row.msg_status;
    const actionList = [ ...actionListItems ];
    if (msgStatus === 'D') {
      actionList.pop();
    }

    return {
      actionList,
      actionItems: actionList.map((action) => (
        <ActionMenuListItem
          key={action.menuName}
          icon={action.iconType}
          onClick={action.onClick}
        >
          { action.menuName }
        </ActionMenuListItem>
      )),
    };
  };

  const displayInitialEmptyPagination = isMessageCentreMessagesInitialLoad && messages?.length === 0;

  return (
    <>
      <PageInfo
        title="Messages"
        TitleComponent={TextHeadline}
        titleComponentPorps={{ component: 'h1' }}
        isLoading={false}
        showActionButton={false}
        onClick={() => {}}
      />
      <Filters
        filters={filters}
        onFiltersChange={setFilters}
        setIsMessageCentreMessagesInitialLoad={setIsMessageCentreMessagesInitialLoad}
        filterFields={[
          {
            label: 'Type',
            key: 'type',
            options: CAMPAIGN_TYPE_OPTIONS,
            defaultOptionLabel: 'Select campaign type',
          },
          {
            label: 'Channel',
            key: 'channel',
            options: CAMPAIGN_CHANNEL_OPTIONS,
            defaultOptionLabel: 'Select campaign channel',
          },
          {
            label: 'Language',
            key: 'language',
            options: LANGUAGES.at(0).options,
            defaultOptionLabel: 'Select language',
          },
          {
            label: 'Status',
            key: 'status',
            options: MESSAGE_STATUS_OPTIONS,
            defaultOptionLabel: 'Select status',
          },
          {
            label: 'Priority',
            key: 'priority',
            options: MESSAGE_PRIORITY_OPTIONS,
            defaultOptionLabel: 'Select priority',
          },
        ]}
        handleExport={handleExport}
        includeDateTime
      />
      <List
        isLoading={messagesLoading}
        entityName=""
        className="admin-list__listings-table"
        columnFixed={false}
        columns={tableColumns({
          messages,
          getActionItems,
          isActionListOpen,
          setIsActionListOpen,
          setIsSidePanelOpen,
          setSelectedMessage,
        })}
        data={messages}
        showMangePrefix={false}
        isMessageCentreMessagesInitialLoad={isMessageCentreMessagesInitialLoad}
      />
      { !messagesLoading && messages?.length > 0 && (
        <DesktopPagination
          id='message-centre-messages-pagination'
          totalResultCount={pagination?.total || 0}
          onChange={(pageNumber) => {
            filtersChanged({ ...filters, pageNumber });
            window.scrollTo(0, 0);
          }}
          firstButtonLabel="First"
          prevButtonLabel="Previous"
          nextButtonLabel="Next"
          lastButtonLabel="Last"
          visiblePages={5}
          navigationLabel="Pagination Navigation"
          pageSize={pagination?.limit || 0}
          currentPage={messages ? pagination?.offset / pagination?.limit + 1 : 1}
          containerType="card"
        />
      ) }
      { displayInitialEmptyPagination && (
      <Card
        id='message-centre-messages-pagination-empty'
        lgPadding={20}>
          <TextCaption color='gray'>0 result</TextCaption>
      </Card>) }
      { selectedMessage && (
        <SidePanel
          isSidePanelOpen={isSidePanelOpen}
          setIsSidePanelOpen={setIsSidePanelOpen}
          selectedMessage={selectedMessage}
          actionList={getActionItems(selectedMessage).actionList}
          filters={filters}
          messagesLoading={messagesLoading}
        />
      ) }
    </>
  );
};

export default MessageCentreMessageList;
