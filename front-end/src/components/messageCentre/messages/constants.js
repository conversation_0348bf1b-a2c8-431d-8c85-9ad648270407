export const CAMPAIGN_STATUS_MAPPING = {
  N: 'new',
  S: 'suspended',
  E: 'expired',
};

export const MESSAGE_STATUS_MAPPING = {
  N: 'new',
  S: 'suspended',
  D: 'deleted',
};

export const LANGUAGES = [
  {
    label: 'Languages',
    name: 'language',
    options: [
      { label: 'English', value: 'E', name: 'language' },
      { label: 'French', value: 'F', name: 'language' },
    ],
  },
];

export const MESSAGE_STATUS_OPTIONS = [
  { value: 'N', name: 'N - New', label: 'New' },
  { value: 'S', name: 'S - Suspended', label: 'Suspended' },
  { value: 'D', name: 'D - Deleted', label: 'Deleted' },
];

export const CAMPAIGN_CHANNELS = {
  ABM: 'ABM',
  SOL: 'SOL',
  MOB: 'MOB',
  BRN: 'BRN',
  SBD: 'SBD',
};

export const LANGUAGE_MAPPING = {
  E: 'English',
  F: 'French',
};

export const MESSAGE_PRIORITY_MAPPING = {
  U: 'Urgent',
  N: 'Normal',
  X: 'Urgent, intercept not dimissible',
  V: 'Viewed',
};

export const MESSAGE_RESPONSE_OPTIONS = [
  { name: 'P - Yes, Refer to Personal Banker', value: 'P' },
  { name: 'N - No', value: 'N' },
  { name: 'D - Deleted', value: 'D' },
  { name: 'E - Yes, Customer Convenience Completed', value: 'E' },
  { name: 'F - No, Customer Convenience Not Completed', value: 'F' },
  { name: 'Y - Yes', value: 'Y' },
  { name: 'C - Other Credit Used', value: 'C' },
  { name: 'V - Viewed', value: 'V' },
  { name: 'A - Takes Application', value: 'A' },
  { name: '', value: '' },
];

const CHANNELS_OPTIONS = [
  { label: 'Yes', value: 'Yes' },
  { label: 'No', value: 'No' },
];

export const MESSAGE_PRIORITY_OPTIONS = [
  { value: 'U', name: 'U - Urgent', label: 'Urgent' },
  { value: 'N', name: 'N - Normal', label: 'Normal' },
  {
    value: 'X',
    name: 'X - Urgent, intercept not dismissible',
    label: 'Urgent, intercept not dismissible',
  },
  { value: 'V', name: 'V - Viewed', label: 'Viewed' },
];

export const CAMPAIGN_TYPE_OPTIONS = [
  { id: 'ABM', value: 'ABM', name: 'ABM', label: 'ABM' },
  { id: 'FFT', value: 'FFT', name: 'FFT', label: 'FFT' },
  { id: 'SOL', value: 'SOL', name: 'SOL', label: 'SOL' },
  { id: 'MOB', value: 'MOB', name: 'MOB', label: 'MOB' },
  { id: '000', value: '000', name: '000', label: '000' },
];

export const CAMPAIGN_CHANNEL_OPTIONS = [
  { id: 'ABM', value: 'ABM', name: 'ABM', label: 'ABM' },
  { id: 'MOB', value: 'MOB', name: 'MOB (Mobile)', label: 'MOB (Mobile)' },
  { id: 'SOL', value: 'SOL', name: 'SOL (Scotia Online)', label: 'SOL (Scotia Online)' },
  { id: 'BRN', value: 'BRN', name: 'BRN (SPOT - Branch)', label: 'BRN (SPOT - Branch)' },
  { id: 'SBD', value: 'SBD', name: 'SBD (PAL - Branch)', label: 'SBD (PAL - Branch)' },
  { id: 'IBD', value: 'IBD', name: 'IBD (inbound)', label: 'IBD (inbound)' },
  { id: 'OBD', value: 'OBD', name: 'OBD (outbound)', label: 'OBD (outbound)' },
];

export const CHANNELS = [
  {
    name: 'channel_display_sol',
    label: 'SOL',
    options: CHANNELS_OPTIONS,
  },
  {
    name: 'channel_display_abm',
    label: 'ABM',
    options: CHANNELS_OPTIONS,
  },
  {
    name: 'channel_display_csr',
    label: 'Contact Centre',
    options: CHANNELS_OPTIONS,
  },
  {
    name: 'message_display_spot',
    label: 'Branch - SPOT',
    options: CHANNELS_OPTIONS,
  },
  {
    name: 'message_display_pal',
    label: 'Branch - PAL',
    options: CHANNELS_OPTIONS,
  },
  {
    name: 'message_display_mob',
    label: 'MOBILE',
    options: CHANNELS_OPTIONS,
  },
];

export const ID_TYPES_OPTIONS = [
  {
    id: 'access_id',
    value: 'accessId',
    name: 'Access ID',
    label: 'Access ID',
  },
  {
    id: 'customer_id',
    value: 'customerId',
    name: 'Customer ID',
    label: 'Customer ID',
  },
  {
    id: 'message_id',
    value: 'messageId',
    name: 'Message ID',
    label: 'Message ID',
  },
];

export const MESSAGE_DETAILS_FIELDS = {
  msg_priority: '',
  msg_status: '',
  msg_response: '',
  start_date: '',
  end_date: '',
  channel_display_sol: false,
  channel_display_abm: false,
  channel_display_csr: false,
  message_display_spot: false,
  message_display_pal: false,
  message_display_mob: false,
};

export const MESSAGE_LISTING_SEARCH_FIELDS = [
  {
    id: 'cssNumber',
    value: 'cssNumber',
    name: 'ScotiaCard Number',
    label: 'ScotiaCard Number',
  },
  {
    id: 'cidNumber',
    value: 'cidNumber',
    name: 'Customer ID',
    label: 'Customer ID',
  },
  {
    id: 'messageId',
    value: 'messageId',
    name: 'Message ID',
    label: 'Message ID',
  },
];
