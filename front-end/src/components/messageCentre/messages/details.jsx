import React, { useEffect, useState, useMemo } from 'react';

import Card from 'canvas-core-react/lib/Card';
import TextIntroduction from 'canvas-core-react/lib/TextIntroduction';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import BackButton from 'canvas-core-react/lib/BackButton';
import TextBody from 'canvas-core-react/lib/TextBody';
import AlertBanner from 'canvas-core-react/lib/AlertBanner';

import { useForm } from 'react-hook-form';
import { useParams, useHistory } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import qs from 'qs';

import {
  InputDateField,
  InputSelectField,
  InputRadioGroupButtonField,
} from '../../formFields/reactHookForm';

import { required, requiredDate } from '../../../utils/validation';
import { capitalize, emptyStringsToNull } from '../../../utils';
import {
  CHANNELS,
  MESSAGE_PRIORITY_OPTIONS,
  MESSAGE_STATUS_OPTIONS,
  MESSAGE_RESPONSE_OPTIONS,
  LANGUAGE_MAPPING,
} from './constants';
import {
  INVALID_DATE_COMPARISON_ERROR_MSG,
} from '../campaigns/constants';
import {
  getMessageDetail,
  updateMessageDetail,
} from '../../../api/message-centre-messages';
import { addSnackbar } from '../../../store/actions/snackbar';
import { addAlert } from '../../../store/actions/alertBanner';
import permissionsList from '../../../constants/permissionsList';
import { LONG_MONTH_FORMAT } from '../../../constants';

function MessageDetails() {
  const history = useHistory();
  const dispatch = useDispatch();
  const { id, action } = useParams();
  const [ details, setDetails ] = useState({});
  const mode = action;

  const queryParams = qs.parse(history.location.search, {
    ignoreQueryPrefix: true,
  });

  const authenticated = useSelector(state => state.authenticated);
  const permissions = useMemo(
    () => authenticated?.permissions || {},
    [ authenticated ]
  );

  const canEdit =
    permissions &&
    (permissions['admin'] ||
      permissions[permissionsList.MESSAGE_CENTRE_MESSAGES_MANAGE]);

  const canView =
    permissions &&
    (permissions['admin'] ||
      permissions[permissionsList.MESSAGE_CENTRE_MESSAGES_VIEW]);

  if (!canView) {
    return (
      <AlertBanner type="error">
        You do not have permissions to view this page
      </AlertBanner>
    );
  }

  const { handleSubmit, control, getValues, reset, setValue, watch, trigger } =
    useForm({
      mode: 'onChange',
      defaultValues: async() =>
        getMessageDetail(id, queryParams).then(res => {
          setDetails(res.data);
          return formatData(res.data);
        }),
    });

  const watchMessageResponse = watch('msg_response');
  useEffect(() => {
    if (watchMessageResponse === 'D') {
      setValue('channel_display_sol', 'No');
      setValue('channel_display_abm', 'No');
      setValue('channel_display_csr', 'No');
      setValue('message_display_spot', 'No');
      setValue('message_display_pal', 'No');
      setValue('message_display_mob', 'No');
    }
  }, [ watchMessageResponse ]);

  const formatData = data => {
    return {
      ...data,
      start_date: moment(data.start_date),
      end_date: moment(data.end_date),
      channel_display_sol: data.channel_display_sol ? 'Yes' : 'No',
      channel_display_abm: data.channel_display_abm ? 'Yes' : 'No',
      channel_display_csr: data.channel_display_csr ? 'Yes' : 'No',
      message_display_spot: data.message_display_spot ? 'Yes' : 'No',
      message_display_pal: data.message_display_pal ? 'Yes' : 'No',
      message_display_mob: data.message_display_mob ? 'Yes' : 'No',
    };
  };

  const handleForm = async(values, e) => {
    e.preventDefault();
    trigger();

    const data = emptyStringsToNull({
      msg_priority: values.msg_priority,
      msg_status: values.msg_status,
      msg_response: values.msg_response,
      start_date: new Date(values.start_date),
      end_date: new Date(values.end_date),
      channel_display_sol: values.channel_display_sol === 'Yes',
      channel_display_abm: values.channel_display_abm === 'Yes',
      channel_display_csr: values.channel_display_csr === 'Yes',
      message_display_spot:
        values.message_display_spot === 'Yes',
      message_display_pal: values.message_display_pal === 'Yes',
      message_display_mob: values.message_display_mob === 'Yes',
      // channel, and language
      ...queryParams,
    });

    try {
      await updateMessageDetail(id, data).then(res => {
        reset(formatData(res.data));
        return setDetails(res.data);
      });
      dispatch(
        addSnackbar({
          message: `Details has been edited successfully`,
        })
      );
    } catch (error) {
      dispatch(addAlert({ message: `Failed to save details` }));
    }
  };

  return (
    <div className="message-details">
      <form onSubmit={handleSubmit(handleForm)}>
        <div className="message-details__action-bar">
          <TextIntroduction component="h1" className="message-details__header">
            { capitalize(mode) } Message Details
          </TextIntroduction>
        </div>

        <Card className="message-details__card" type="floatLow">
          <TextHeadline
            component="h2"
            size={21}
            className="message-details__sub-header"
          >
            Inquiry Details
          </TextHeadline>
          <div className="message-details__fields">
            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                ScotiaCard Number
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { details.card_number }
              </TextBody>
            </div>

            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Message ID
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { details.msg_id }
              </TextBody>
            </div>

            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Customer ID
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { details.cid }
              </TextBody>
            </div>
          </div>
        </Card>

        <Card className="message-details__card" type="floatLow">
          <TextHeadline
            component="h2"
            size={21}
            className="message-details__sub-header"
          >
            Campaign Information
          </TextHeadline>
          <div className="message-details__fields">
            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Campaign Type
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { details.campaign_type }
              </TextBody>
            </div>

            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Campaign ID
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { details.campaign_id }
              </TextBody>
            </div>

            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Campaign Key
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { details.campaign_key }
              </TextBody>
            </div>

            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Channel
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { queryParams['channel'] && queryParams['channel'] }
              </TextBody>
            </div>

            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Language
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { queryParams['language'] &&
                  LANGUAGE_MAPPING[queryParams['language']] }
              </TextBody>
            </div>
          </div>
        </Card>

        <Card className="message-details__card" type="floatLow">
          <TextHeadline
            component="h2"
            size={21}
            className="message-details__sub-header"
          >
            Message Settings
          </TextHeadline>
          <div className="message-details__fields">
            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Message Category
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { details.msg_category }
              </TextBody>
            </div>
            <div className="message-details__field">
              <InputSelectField
                control={control}
                className="message-centre-details__message-select-field"
                name="msg_priority"
                label="Message Priority"
                placeholder="Message Priority"
                disabled={mode === 'view'}
                options={MESSAGE_PRIORITY_OPTIONS}
                optionValue="value"
                rules={{
                  validate: {
                    required,
                  },
                }}
              />
            </div>
            <div className="message-details__field">
              <InputSelectField
                control={control}
                className="message-centre-details__message-select-field"
                name="msg_status"
                label="Message Status"
                placeholder="Message Status"
                disabled={mode === 'view'}
                options={MESSAGE_STATUS_OPTIONS}
                optionValue="value"
                rules={{
                  validate: {
                    required,
                  },
                }}
              />
            </div>
            <div className="message-details__field">
              <InputSelectField
                control={control}
                className="message-centre-details__message-select-field"
                name="msg_response"
                label="Message Response"
                placeholder="Message Response"
                disabled={mode === 'view'}
                options={MESSAGE_RESPONSE_OPTIONS}
                 optionValue="value"
                // rules={{
                //   validate: {
                //     requiredSpecific: validations.requiredMessageResponse,
                //   },
                // }}
              />
            </div>
          </div>
        </Card>

        <Card className="message-details__card" type="floatLow">
          <TextHeadline
            component="h2"
            size={21}
            className="message-details__sub-header"
          >
            Timing
          </TextHeadline>
          <div className="message-details__fields">
            <InputDateField
              control={control}
              disabled={mode === 'view'}
              name="start_date"
              className="message-details__date"
              rules={{
                validate: {
                  requiredDate,
                },
              }}
              label="Start date"
              placeholder="MM/DD/YYYY"
            />
            <InputDateField
              control={control}
              disabled={mode === 'view'}
              name="end_date"
              className="message-details__date"
              label="End date"
              placeholder="MM/DD/YYYY"
                 rules={{
                  validate: {
                    requiredDate,
                    moreThanStartDate: value =>
                      moment(getValues('start_date')) &&
                      moment(value) &&
                      moment(value).isSameOrBefore(moment(getValues('start_date')))
                        ? INVALID_DATE_COMPARISON_ERROR_MSG
                        : undefined,
                  },
                }}
            />
          </div>
        </Card>

        <Card className="message-details__card" type="floatLow">
          <TextHeadline
            component="h2"
            size={21}
            className="message-details__sub-header"
          >
            Channel Information
          </TextHeadline>
          <TextBody
            component="div"
            className="message-details__label message-details__channel-label-margin"
            bold
          >
            Display Channel
          </TextBody>
          <div className="message-details__fields">
            { CHANNELS.map(item => (
              <div className="message-details__field" key={item.name}>
                <InputRadioGroupButtonField
                  control={control}
                  disabled={mode === 'view' || watchMessageResponse === 'D'}
                  name={item.name}
                  label={item.label}
                  options={item.options}
                  inputGroupClassName="message-details__field"
                />
              </div>
            )) }
          </div>
        </Card>

        <Card className="message-details__card" type="floatLow">
          <TextHeadline
            component="h2"
            size={21}
            className="message-details__sub-header"
          >
            Message Information
          </TextHeadline>
          <div className="message-details__fields">
            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Update User ID
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { details.updated_user_id }
              </TextBody>
            </div>

            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Last Update Date
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { details.updated_date &&
                  moment(details.updated_date).format(LONG_MONTH_FORMAT) }
              </TextBody>
            </div>

            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Viewed Channel
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { details.msg_viewed_channel }
              </TextBody>
            </div>

            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Pointer Indicator
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { details.pointer_indicator &&
                  '“' + details.pointer_indicator + '”' }
              </TextBody>
            </div>
          </div>

          <div className="message-details__msg-info message-details__msg-info-first">
            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Message Pointer Text
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { details.pointer_text }
              </TextBody>
            </div>
          </div>

          <div className="message-details__msg-info">
            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Message Subject Line
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { details.subject_line }
              </TextBody>
            </div>
          </div>

          <div className="message-details__msg-info">
            <div className="message-details__field">
              <TextBody component="div" className="message-details__label" bold>
                Message Display Parameters
              </TextBody>
              <TextBody component="div" className="message-details__text">
                { JSON.stringify(details.display_params) }
              </TextBody>
            </div>
          </div>
        </Card>
        <div className="message-centre-details__action-buttons">
          <BackButton onClick={() => history.goBack()} type="button">
            Cancel
          </BackButton>
          { mode === 'view' || !canEdit ? null : (
            <PrimaryButton
              type="submit"
              className="message-centre-details__action-button"
            >
              Submit
            </PrimaryButton>
          ) }
        </div>
      </form>
    </div>
  );
}

export default MessageDetails;
