import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { renderPage } from '../../../utils/testing-library-utils';

import Details from './details';
import { MemoryRouter, Route } from 'react-router-dom';
import { addSnackbar } from '../../../store/actions/snackbar';
import { addAlert } from '../../../store/actions/alertBanner';

import {
  getMessageDetail,
  updateMessageDetail,
} from '../../../api/message-centre-messages';

import mockMessageDetail from '../../../../../src/__mocks__/mockMessageDetail';
import mockUpdateMessageDetail from '../../../../../src/__mocks__/mockUpdateMessageDetail';

jest.mock('../../../api/message-centre-messages');

describe('MessageDetails', () => {
  it('should render', async() => {
    getMessageDetail.mockResolvedValue({ data: mockMessageDetail });

    // use Route component to mock params
    await renderPage(
      <MemoryRouter
        initialEntries={[
          '/message-centre/messages/1/edit?language=E&channel=SOL',
        ]}
      >
        <Route
          path="/message-centre/messages/:id/:action?"
          component={Details}
        />
      </MemoryRouter>
    );

    expect(screen.getByText('Edit Message Details')).toBeInTheDocument();
  });

  it('should set display values to No when message reponse is D', async() => {
    getMessageDetail.mockResolvedValue({ data: mockMessageDetail });
    const { container } = await renderPage(
      <MemoryRouter
        initialEntries={[
          '/message-centre/messages/1/edit?language=E&channel=SOL',
        ]}
      >
        <Route
          path="/message-centre/messages/:id/:action?"
          component={Details}
        />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(
        container.querySelector('#radio-channel_display_sol-No')
      ).toBeChecked();

      expect(
        container.querySelector('#radio-channel_display_abm-No')
      ).toBeChecked();

      expect(
        container.querySelector('#radio-channel_display_csr-No')
      ).toBeChecked();
      expect(
        container.querySelector('#radio-message_display_spot-No')
      ).toBeChecked();
      expect(
        container.querySelector('#radio-message_display_pal-No')
      ).toBeChecked();
      expect(
        container.querySelector('#radio-message_display_mob-No')
      ).toBeChecked();
    });
  });

  it('should format display values to Yes', async() => {
    getMessageDetail.mockResolvedValueOnce({
      data: {
        ...mockMessageDetail,
        msg_response: '',
        channel_display_sol: true,
        channel_display_abm: true,
        channel_display_csr: true,
        message_display_spot: true,
        message_display_pal: true,
        message_display_mob: true,
      },
    });
    const { container } = await renderPage(
      <MemoryRouter
        initialEntries={[
          '/message-centre/messages/1/edit?language=E&channel=SOL',
        ]}
      >
        <Route
          path="/message-centre/messages/:id/:action?"
          component={Details}
        />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(
        container.querySelector('#radio-channel_display_sol-Yes')
      ).toBeChecked();
      expect(
        container.querySelector('#radio-channel_display_abm-Yes')
      ).toBeChecked();
      expect(
        container.querySelector('#radio-channel_display_csr-Yes')
      ).toBeChecked();
      expect(
        container.querySelector('#radio-message_display_spot-Yes')
      ).toBeChecked();
      expect(
        container.querySelector('#radio-message_display_pal-Yes')
      ).toBeChecked();
      expect(
        container.querySelector('#radio-message_display_mob-Yes')
      ).toBeChecked();
    });
  });

  it('should format display values to No', async() => {
    getMessageDetail.mockResolvedValueOnce({
      data: {
        ...mockMessageDetail,
        msg_response: 'D',
        channel_display_sol: false,
        channel_display_abm: false,
        channel_display_csr: false,
        message_display_spot: false,
        message_display_pal: false,
        message_display_mob: false,
      },
    });
    const { container } = await renderPage(
      <MemoryRouter
        initialEntries={[
          '/message-centre/messages/1/edit?language=E&channel=SOL',
        ]}
      >
        <Route
          path="/message-centre/messages/:id/:action?"
          component={Details}
        />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(
        container.querySelector('#radio-channel_display_sol-No')
      ).toBeChecked();
      expect(
        container.querySelector('#radio-channel_display_abm-No')
      ).toBeChecked();
      expect(
        container.querySelector('#radio-channel_display_csr-No')
      ).toBeChecked();
      expect(
        container.querySelector('#radio-message_display_spot-No')
      ).toBeChecked();
      expect(
        container.querySelector('#radio-message_display_pal-No')
      ).toBeChecked();
      expect(
        container.querySelector('#radio-message_display_mob-No')
      ).toBeChecked();
    });
  });

  test('should handle valid dates', async() => {
    getMessageDetail.mockResolvedValueOnce({
      data: { ...mockMessageDetail, start_date: '', end_date: '' },
    });

    await renderPage(
      <MemoryRouter
        initialEntries={[
          '/message-centre/messages/1/edit?language=E&channel=SOL',
        ]}
      >
        <Route
          path="/message-centre/messages/:id/:action?"
          component={Details}
        />
      </MemoryRouter>
    );

    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: '05/01/2024 12:00 AM' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /end date/i }), {
      target: { value: '04/01/2024 12:00 AM' },
    });
    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(
        screen.queryByText(
          'You must enter a publication End date later than the Start date.'
        )
      ).toBeInTheDocument();
    });
  });

  it('should submit form successfully', async() => {
    getMessageDetail.mockResolvedValueOnce({ data: mockMessageDetail });
    updateMessageDetail.mockResolvedValueOnce({ data: mockMessageDetail });
    const { store } = await renderPage(
      <MemoryRouter
        initialEntries={[
          '/message-centre/messages/1/edit?language=E&channel=SOL',
        ]}
      >
        <Route
          path="/message-centre/messages/:id/:action?"
          component={Details}
        />
      </MemoryRouter>
    );

    fireEvent.click(screen.getByRole('button', { name: /submit/i }));
    await waitFor(() => {
      const expectedSnackbar = addSnackbar({
        message: 'Details has been edited successfully',
      });
      expect(store.getActions()).toContainEqual(expectedSnackbar);
    });
  });

  it('should handle update error', async() => {
    getMessageDetail.mockResolvedValueOnce({ data: mockMessageDetail });
    updateMessageDetail.mockRejectedValueOnce(mockUpdateMessageDetail);
    const { store } = await renderPage(
      <MemoryRouter
        initialEntries={[
          '/message-centre/messages/1/edit?language=E&channel=SOL',
        ]}
      >
        <Route
          path="/message-centre/messages/:id/:action?"
          component={Details}
        />
      </MemoryRouter>
    );

    fireEvent.click(screen.getByRole('button', { name: /submit/i }));
    await waitFor(() => {
      const expectedSnackbar = addAlert({ message: 'Failed to save details' });
      expect(store.getActions()).toContainEqual(expectedSnackbar);
    });
  });
});
