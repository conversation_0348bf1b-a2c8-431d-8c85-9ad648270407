import React, { useRef, useState, useCallback } from 'react';
import qs from 'qs';
import PropTypes from 'prop-types';

import Card from 'canvas-core-react/lib/Card';
import DatePicker from 'canvas-core-react/lib/DatePicker';
import Filter from 'canvas-core-react/lib/Filter';
import TextButton from 'canvas-core-react/lib/TextButton';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import LoadingIndicator from 'canvas-core-react/lib/LoadingIndicator';
import Search from 'canvas-core-react/lib/Search';
import IconFilter from 'canvas-core-react/lib/IconFilter';
import IconClose from 'canvas-core-react/lib/IconClose';
// import IconDownload from 'canvas-core-react/lib/IconDownload';
import Selector from 'canvas-core-react/lib/Selector';

import { SHORT_MONTH_FORMAT } from '../../../constants';
import { MESSAGE_LISTING_SEARCH_FIELDS } from './constants';
import { createIdFromName, debounce, removeFalsyKeys } from '../../../utils';

import classnames from 'classnames';
import moment from 'moment';
import { omit } from 'lodash';
import { useHistory } from 'react-router-dom';

const FilterLoader = () => {
  return (
    <div className="admin-filter__loader">
      <LoadingIndicator color="grey" />
    </div>
  );
};

const MessageCentreCampaignFilter = ({
  title,
  filters,
  includeDateTime,
  className,
  onFiltersChange,
  filterFields,
  handleExport,
  setIsMessageCentreMessagesInitialLoad,
}) => {
  const history = useHistory();

  const startDatePickerRef = useRef();
  const endDatePickerRef = useRef();
  const [ filtersCollapsed, setFiltersCollapsed ] = useState(true);
  const [ selectedSearchField, setSelectedSearchField ] = useState(MESSAGE_LISTING_SEARCH_FIELDS[0]);

  const handleChangingFilters = (newValues) => {
    const queryParams = removeFalsyKeys(
      omit(newValues, [
        'limit',
        ...(newValues.pageNumber === 1 ? [ 'pageNumber' ] : []),
      ])
    );
    history.push({
      search: qs.stringify(queryParams, { addQueryPrefix: true }),
    });
    onFiltersChange(newValues);
  };

  const handleSearch = (searchQuery, selectedSearchField) => {
    if (searchQuery && searchQuery !== '' && selectedSearchField) {
      setIsMessageCentreMessagesInitialLoad(false);
    }
    handleChangingFilters({ ...filters, searchQuery, searchField: selectedSearchField.value, pageNumber: 1 });
  };

  const handleSelectSearchField = (value) => {
    const selectedField = MESSAGE_LISTING_SEARCH_FIELDS.filter((field) => field.value === value)[0];
    setSelectedSearchField(selectedField);
    handleChangingFilters({ ...filters, searchField: selectedField.value, pageNumber: 1 });
  };

  const debouncedHandleSearch = useCallback(debounce(handleSearch, 500), []);

  const renderedFilters = filterFields.map(
    ({ label, key, options, defaultOptionLabel, isLoading }) => {
      const filterKey = key || createIdFromName(label.toLowerCase());
      return (
        <div key={filterKey} className="admin-filter__item">
          <Filter
            name={`admin-filter-${label}`}
            id={`admin-filter-${key}`}
            label={label}
            isLabelVisible
            onChange={(e) =>
              handleChangingFilters({
                ...filters,
                [filterKey]: e.target.value,
              })
            }
            value={filters[filterKey] || ''}
            tooltip={isLoading ? <FilterLoader /> : false}
          >
            <option key={`${filterKey}-all`} value={''}>
              { defaultOptionLabel || 'All' }
            </option>
            { options.map(({ value, label: optionLabel, disabled }) => (
              <option
                key={`${filterKey}-${value}`}
                value={value}
                disabled={disabled}
              >
                { optionLabel }
              </option>
            )) }
          </Filter>
        </div>
      );
    }
  );

  const onDateTimeChange = (value, forField) => {
    if (!moment(value, SHORT_MONTH_FORMAT, true).isValid()) return;
    const date = moment(value).toISOString();
    handleChangingFilters({
      ...filters,
      [forField]: date,
    });
  };

  const renderDateTimeFilters = () => (
    <>
      <DatePicker
        position="top"
        className="admin-filter__item"
        id="datepicker-start-date"
        locale="en-CA"
        inputDateFormat={SHORT_MONTH_FORMAT}
        overrides={{ 'en-CA': { startDateLabel: 'Last Update Date Start' } }}
        onChange={(value) => onDateTimeChange(value, 'updated_date_gt')}
        selected={filters.start_date ? new Date(filters.start_date) : null}
        onDatePickerClear={() => onDateTimeChange(undefined, 'updated_date_gt')}
        datePickerComponentRef={startDatePickerRef}
      />
      <DatePicker
        className="admin-filter__item"
        id="datepicker-end-date"
        locale="en-CA"
        inputDateFormat={SHORT_MONTH_FORMAT}
        overrides={{ 'en-CA': { startDateLabel: 'Last Update Date End' } }}
        errorStrings={{
          label: 'error',
          start: {
            range: 'The end date selection is outside of the allowed range',
            disallowedDate: 'The end date selection is invalid',
            invalidRange: '',
          },
          end: {
            range: '',
            disallowedDate: '',
            invalidRange: '',
          },
        }}
        onChange={(value) => onDateTimeChange(value, 'updated_date_lt')}
        onDatePickerClear={() => onDateTimeChange(undefined, 'updated_date_lt')}
        datePickerComponentRef={endDatePickerRef}
      />
    </>
  );

  const renderClearBtn = () => {
    return (
      <div className="admin-filter__item-inline">
        <TextButton
          className="admin-filter__item-clear-inline"
           onClick={() => {
            startDatePickerRef.current.clearDateField();
            endDatePickerRef.current.clearDateField();
            handleChangingFilters({});
          }}
        >
          Clear all
        </TextButton>
      </div>
    );
  };

  return (
    <>
      <div className="admin-list__msg-centre-filter-accordion">
        <div className="admin-list__msg-centre-search">
          <div className="admin-list__msg-centre-search-dropdown">
            <Selector
              name="search-field-selector"
              id="search-field-selector"
              value={selectedSearchField.value}
              onChange={(e) => handleSelectSearchField(e.target.value)}
            >
              { MESSAGE_LISTING_SEARCH_FIELDS &&
                MESSAGE_LISTING_SEARCH_FIELDS.map((field, index) => (
                  <option
                    key={`${field.id}-${index}`}
                    data-testid={`${field.id}-option`}
                    value={field.value}
                  >
                    { field.name }
                  </option>
                )) }
            </Selector>
          </div>
          <div className="admin-list__msg-centre-search-input">
            <Search
              id="name"
              placeholder={selectedSearchField.name}
              onChange={(e) => {
                debouncedHandleSearch(e.target.value, selectedSearchField);
              }}
              showLabel={false}
              value={filters?.searchQuery || ''}
              clearButtonLabel="Clear search"
              searchButtonLabel={`Search ${selectedSearchField.name}`}
              label={`Search ${selectedSearchField.name}`}
            />
          </div>
        </div>
        <div className="admin-list__action-button-container">
          { /* disabling Export button for PIGEON-5345, retaining the code as this feature can be requested back in future */ }
          { /* <TextButton Icon={IconDownload} onClick={handleExport}>
            Export
          </TextButton> */ }
          <TextButton
            Icon={filtersCollapsed ? IconFilter : IconClose}
            onClick={() => setFiltersCollapsed(!filtersCollapsed)}
          >
            { filtersCollapsed ? 'Filters' : 'Cancel' }
          </TextButton>
        </div>
      </div>

      { !filtersCollapsed && (
        <Card className={classnames('admin-filter', className)}>
          <div className="admin-filter__header-container">
            <TextSubtitle component="legend" className="admin-filter__header-title">
              { title }
            </TextSubtitle>
            { renderClearBtn() }
          </div>

          <div className="admin-filter__filter-container">
            { renderedFilters }
            { includeDateTime && renderDateTimeFilters() }
          </div>
        </Card>
      ) }
    </>
  );
};

MessageCentreCampaignFilter.defaultProps = {
  title: 'Filters',
  filters: {},
  includeDateTime: false,
  className: 'admin-list__filter-options',
  onFiltersChange: () => {},
  handleExport: () => {},
  filterFields: [],
};

MessageCentreCampaignFilter.propTypes = {
  title: PropTypes.string,
  filters: PropTypes.object,
  includeDateTime: PropTypes.bool,
  className: PropTypes.string,
  filterFields: PropTypes.array,
  onFiltersChange: PropTypes.func,
  handleExport: PropTypes.func,
  setIsMessageCentreMessagesInitialLoad: PropTypes.func,
};

export default MessageCentreCampaignFilter;
