import React from 'react';
import moment from 'moment';
import { Link } from 'react-router-dom';

import TextCaption from 'canvas-core-react/lib/TextCaption';
import TextButton from 'canvas-core-react/lib/TextButton';
import CanvasBadge from 'canvas-core-react/lib/StatusBadge';
import IconInfo from 'canvas-core-react/lib/IconInfo';
import ActionMenuList from 'canvas-core-react/lib/ActionMenuList';
import CanvasLink from 'canvas-core-react/lib/Link';

import { LANGUAGE_MAPPING, MESSAGE_PRIORITY_MAPPING, MESSAGE_STATUS_MAPPING } from './constants';

export const tableColumns = ({
  messages,
  getActionItems,
  isActionListOpen,
  setIsActionListOpen,
  setIsSidePanelOpen,
  setSelectedMessage,
}) => {
  const columns = [
    {
      name: 'Type',
      cellFormatter: (row) => (
        <>
          <TextCaption component="p">{ row.campaign_type || 'N/A' }</TextCaption>
        </>
      ),
      selector: '',
      bodyStyle: { display: 'flex', flexFlow: 'wrap' },
      minWidth: 'auto',
      grow: 0.8,
    },
    {
      name: `ID`,
      cellFormatter: (row) => (
        <CanvasLink
        href=""
        component={Link}
        type="emphasis"
        className="admin-list__name-link"
        to={{
          pathname: `/campaigns${row.campaign_type === 'SOL' ? '/sol' : '' }`,
          search: `?${row.campaign_type !== 'SOL' ? 'external_ref=TARGETED&' : '' }campaign_id=${row.campaign_id}`,
          state: { from: 'message-centre' },
        }}
      >
        { row.campaign_id }
      </CanvasLink>
      ),
      selector: '',
      bodyStyle: { textAlign: 'left' },
    },
    {
      name: 'Channel',
      cellFormatter: (row) => (
        <>
          <TextCaption component="p">{ row.channel || 'N/A' }</TextCaption>
        </>
      ),
      selector: '',
      minWidth: 'auto',
    },
    {
      name: 'Language',
      cellFormatter: (row) => (
        <>
          <TextCaption component="p">{ LANGUAGE_MAPPING[row.language] || 'N/A' }</TextCaption>
        </>
      ),
      selector: '',
      minWidth: 'auto',
    },
    {
      name: 'Status',
      cellFormatter: (row) => {
        let status = 'default';
        if (row.msg_status === 'N') {
          status = 'success-emphasis';
        } else if (row.msg_status === 'E' || row.msg_status === 'D') {
          status = 'error';
        }
        return (
          <CanvasBadge type={status}>
            { MESSAGE_STATUS_MAPPING[row.msg_status]?.toUpperCase() }
          </CanvasBadge>
        );
      },
      selector: '',
      minWidth: 'auto',
      bodyStyle: { height: '100%' },
    },
    {
      name: 'Priority',
      cellFormatter: (row) => (
        <>
          <TextCaption component="p">
            { MESSAGE_PRIORITY_MAPPING[row.msg_priority] || 'N/A' }
          </TextCaption>
        </>
      ),
      selector: '',
      minWidth: 'auto',
    },
    {
      name: 'Last Update Date',
      cellFormatter: (row) => (
        <TextCaption component="p">{ moment(row.updated_date).format('ll') }</TextCaption>
      ),
      selector: '',
      minWidth: 'auto',
    },
    {
      name: `Subject`,
      cellFormatter: (row) => (
        <TextButton
          Icon={IconInfo}
          iconPosition="right"
          className="admin-list__name-link"
          onClick={() => {
            setSelectedMessage(row);
            setIsSidePanelOpen(true);
          }}
        >
          { row.subject_line }
        </TextButton>
      ),
      selector: '',
      grow: 2,
      bodyStyle: { textAlign: 'left', wordBreak: 'break-word', width: 'max-content' },
    },
    {
      name: 'Action',
      cellFormatter: (row) => {
        const index = messages?.findIndex((msg) => msg.id === row.id);
        const isBottom = messages?.length > 3 && messages.length - index > 3;
        return (
          <ActionMenuList
            isMenuOpen={row.id + row.channel + row.language === isActionListOpen}
            setIsMenuOpen={(v) => setIsActionListOpen(v ? row.id + row.channel + row.language : false)}
            bottomSheet={{ heading: '' }}
            iconType="horizontal-small"
            dialogPosition={isBottom ? 'top-center' : 'bottom-center'}
          >
            { getActionItems(row).actionItems }
          </ActionMenuList>
        );
      },
      selector: '',
      minWidth: 'auto',
    },
  ];
  return columns;
};
