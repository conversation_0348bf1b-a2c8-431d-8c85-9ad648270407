import React, { useState, useEffect, useMemo } from 'react';
import classnames from 'classnames';
import moment from 'moment';
import PropTypes from 'prop-types';

import SideSheet from 'canvas-core-react/lib/SideSheet';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';
import StatusBadge from 'canvas-core-react/lib/StatusBadge';
import IconClose from 'canvas-core-react/lib/IconClose';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import TextBody from 'canvas-core-react/lib/TextBody';
import TextLegal from 'canvas-core-react/lib/TextLegal';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import TextCaption from 'canvas-core-react/lib/TextCaption';

import { MESSAGE_STATUS_MAPPING } from './constants';
import { useUsers } from '../../../hooks/useUsers';
import { removeFalsyKeys } from '../../../utils';

const headerHeight = 72;

const SidePanel = ({
  isSidePanelOpen,
  setIsSidePanelOpen,
  selectedMessage,
  actionList,
  filters,
  messagesLoading,
}) => {
  const [ panelOffset, setPanelOffset ] = useState(headerHeight);
  const { users } = useUsers({ ...removeFalsyKeys(filters), limit: null });
  const currentUser = users.filter(user => user.sid === selectedMessage.updated_user_id)[0];

  useEffect(() => {
    const handleScroll = () => {
      setPanelOffset(window.scrollY < headerHeight ? headerHeight - scrollY : 0);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const badgeType = useMemo(() => {
    let type = 'default';
    let messageStatus = MESSAGE_STATUS_MAPPING[selectedMessage.msg_status];
    if (messageStatus === 'new') {
      type = 'success-emphasis';
    } else if (messageStatus === 'deleted') {
      type = 'error';
    }

    return type;
  }, [ selectedMessage ]);

  return (
    <SideSheet
      headline=""
      isSheetVisible={isSidePanelOpen}
      setSheetVisible={setIsSidePanelOpen}
      className={classnames('admin-list__sidepanel', { 'admin-list__panel-offset': panelOffset })}
      style={{ top: `${panelOffset}px` }}
      type="persistent"
      hideHeading
    >
      <div className="admin-list__sidepanel__container">
        <div className="admin-list__panel-section">
          { messagesLoading ? (
            <IconSpinner size={32} className="admin-list__panel-badge" />
          ) : (
            <StatusBadge className="admin-list__panel-badge" type={badgeType}>
              { MESSAGE_STATUS_MAPPING[selectedMessage.msg_status] }
            </StatusBadge>
          ) }
          <button className="admin-list__panel-icon" onClick={() => setIsSidePanelOpen(false)}>
            <IconClose />
          </button>
        </div>
        <TextHeadline className="admin-list__panel-heading" component="h2" size={21}>
          { selectedMessage.campaign_id }
        </TextHeadline>
        <TextBody className="admin-list__panel-name" component="p">
          { selectedMessage.subject_line }
        </TextBody>
        <div className="admin-list__panel-section admin-list__panel-icons-with-gap">
          { actionList.map(action => (
            <div
              key={`key-${action.menuName}`}
              className={`admin-list__panel-icon-container ${messagesLoading ? 'admin-list__panel-icon-container--disabled' : ''}`}
            >
              <button
                className={`admin-list__panel-icon ${messagesLoading ? 'admin-list__panel-icon--disabled' : ''}`}
                onClick={action.onClick}
                disabled={messagesLoading}
              >
                { action.iconType({ color: `${messagesLoading ? 'gray' : 'blue'}` }) }
              </button>
              <TextLegal component="p" bold={true}>
                { action.menuName }
              </TextLegal>
            </div>
          )) }
        </div>
        <ul>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Created by (Team)
            </TextSubtitle>
            <TextCaption component="p">{ currentUser ? currentUser.teamName : 'N/A' }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Created by (User)
            </TextSubtitle>
            <TextCaption component="p">{ currentUser ? currentUser.name : 'N/A' }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Created on
            </TextSubtitle>
            <TextCaption component="p">
              { moment(selectedMessage.msg_gen_date).format('lll') }
            </TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Last updated by (Team)
            </TextSubtitle>
            <TextCaption component="p">{ currentUser ? currentUser.teamName : 'N/A' }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Last updated by (User)
            </TextSubtitle>
            <TextCaption component="p">{ currentUser ? currentUser.name : 'N/A' }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Last updated on
            </TextSubtitle>
            <TextCaption component="p">
              { moment(selectedMessage.updated_date).format('lll') }
            </TextCaption>
          </li>
        </ul>
      </div>
    </SideSheet>
  );
};

SidePanel.propTypes = {
  isSidePanelOpen: PropTypes.bool,
  setIsSidePanelOpen: PropTypes.func,
  selectedMessage: PropTypes.object,
  filters: PropTypes.object,
  actionList: PropTypes.array,
  messagesLoading: PropTypes.bool,
};

export default SidePanel;
