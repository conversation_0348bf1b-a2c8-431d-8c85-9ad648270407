import React from 'react';
import mockAxios from 'axios';
import { fireEvent } from '@testing-library/react';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';

import { renderPage, defaultData } from '../../../utils/testing-library-utils';
import MessagesList from './listContainer';
import permissionsList from '../../../constants/permissionsList';

const mockStore = configureMockStore([ thunk ]);

describe('Message Centre campaigns list', () => {
  afterEach(() => {
    mockAxios.reset();
  });

  test('Should render spinner while loading', async() => {
    const { baseElement } = await renderPage(<MessagesList />, {
      initialState: { messageCentreMessages: { isLoading: true } },
    });
    expect(baseElement).toMatchSnapshot(); // no text element to assert, have to resort to snapshot matching
  });

  test('Should not call get messages at initial load', async() => {
    const { baseElement } = await renderPage(<MessagesList />);
    expect(mockAxios.get).toHaveBeenCalledTimes(0);
    expect(baseElement).toMatchSnapshot(); // no text element to assert, have to resort to snapshot matching
  });

  test('Side panel open', async() => {
    const { container, getAllByRole } = await renderPage(
      <MessagesList />,
    );
    const allButtons = getAllByRole((role, element) => role === 'button' && element.className.includes('admin-list__name-link'));
    fireEvent.click((allButtons[0]));
    const sidePanel = container.querySelector('.admin-list__sidepanel__container');
    expect(sidePanel).toBeInTheDocument();
  });

  test('No permissions - check for alert banner', async() => {
    const { getByText } = await renderPage(
      <MessagesList />,
      {
        store: mockStore({
          authenticated: { permissions: { [permissionsList.MESSAGE_CENTRE_MESSAGES_VIEW]: false } },
          messageCentreMessages: { isLoading: false, items: [] } }
      ) }
    );
    expect(getByText('You do not have permissions to view this page')).toBeInTheDocument();
  });

  test('Should redirect to campaigns page if clicked on campaign id', async() => {
    const history = {
      push: jest.fn(),
      listen: jest.fn(),
      location: {
        pathname: '/message-centre/messages',
      },
      createHref: jest.fn(),
    };
    const { getAllByText } = await renderPage(<MessagesList />, { history });
    fireEvent.click(getAllByText(defaultData.messageCentreMessages[0].campaign_id)[0]);
    expect(history.push).toHaveBeenCalledWith(
      { 'pathname': '/campaigns', 'search': '?external_ref=TARGETED&campaign_id=PAC79', 'state': { 'from': 'message-centre' } },
    );
  });

  test('Should redirect to campaigns sol tab if clicked on campaign id with type sol', async() => {
    const history = {
      push: jest.fn(),
      listen: jest.fn(),
      location: {
        pathname: '/message-centre/messages',
      },
      createHref: jest.fn(),
    };
    const { getAllByText } = await renderPage(<MessagesList />, { history });
    fireEvent.click(getAllByText(defaultData.messageCentreMessages[0].campaign_id)[1]);
    expect(history.push).toHaveBeenCalledWith(
      { 'pathname': '/campaigns/sol', 'search': '?campaign_id=PAC79', 'state': { 'from': 'message-centre' } },
    );
  });
});
