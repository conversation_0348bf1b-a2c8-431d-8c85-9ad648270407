import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderPage } from '../../../utils/testing-library-utils';

import Details from './details';
import { createMemoryHistory } from 'history';
import { MemoryRouter, Route } from 'react-router-dom';
import { addSnackbar } from '../../../store/actions/snackbar';
import { getMessageCentreCampaign, updateMessageCentreCampaign, createMessageCentreCampaign, deleteMessageCentreCampaign } from '../../../api/message-centre-campaigns';
import { INVALID_DATE_COMPARISON_ERROR_MSG, INVALID_DATE_ERROR_MSG } from './constants';

import mockMessageCentreCampaign from '../../../../../src/__mocks__/mockMessageCentreCampaign';
import moment from 'moment';

jest.mock('../../../api/message-centre-campaigns');

describe('MessageCentreCampaignDetails', () => {
  test('should render MessageCentreCampaignDetails', async() => {
    await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/message-centre/campaigns/create' ],
      }),
    });
    expect(screen.getByText('Create a New Campaign')).toBeInTheDocument();
  });

  test('Targeting Dimension should trigger validations', async() => {
    await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/message-centre/campaigns/create' ],
      }),
    });
    fireEvent.click(screen.getByRole('button', { name: /submit/i }));
    await waitFor(() => {
      // Validate start_date and end_date
      expect(screen.getAllByText(INVALID_DATE_ERROR_MSG)).toHaveLength(2);
    });
  });

  test('Targeting Dimension - start_date is greater than end_date', async() => {
    await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/message-centre/campaigns/create' ],
      }),
    });
    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: '05/01/2024 12:00 AM' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /end date/i }), {
      target: { value: '04/01/2024 12:00 AM' },
    });
    await waitFor(() => {
      expect(
        screen.getByText(
          INVALID_DATE_COMPARISON_ERROR_MSG
        )
      ).toBeInTheDocument();
    });
  });

  test('Targeting dimension - invalid same date campaign', async() => {
    await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/message-centre/campaigns/create' ],
      }),
    });
    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: '05/01/2024 12:00 AM' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /end date/i }), {
      target: { value: '05/01/2024 12:00 AM' },
    });
    await waitFor(() => {
      expect(
        screen.getByText(
          INVALID_DATE_COMPARISON_ERROR_MSG
        )
      ).toBeInTheDocument();
    });
  });

  test('Targeting Dimension handle valid dates', async() => {
    await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/message-centre/campaigns/create' ],
      }),
    });
    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: '05/01/2024 12:00 AM' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /end date/i }), {
      target: { value: '06/01/2024 12:00 AM' },
    });
    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    expect(
      screen.queryByText(
        INVALID_DATE_COMPARISON_ERROR_MSG
      )
    ).toBeNull();
  });

  test('Message Settings', async() => {
    await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/message-centre/campaigns/create' ],
      }),
    });

    const messageCategorySelect = screen.getByLabelText(/message category/i);
    const messagePrioritySelect = screen.getByLabelText(/message priority/i);
    const messageStatusSelect = screen.getByLabelText(/message status/i);

    expect(messageCategorySelect).toHaveValue('');
    expect(messagePrioritySelect).toHaveValue('');
    expect(messageStatusSelect).toHaveValue('');

    await userEvent.selectOptions(messageCategorySelect, 'ALT');
    await userEvent.selectOptions(messagePrioritySelect, 'U');
    await userEvent.selectOptions(messageStatusSelect, 'S');

    expect(messageCategorySelect).toHaveValue('ALT');
    expect(messagePrioritySelect).toHaveValue('U');
    expect(messageStatusSelect).toHaveValue('S');

    fireEvent.change(screen.getByRole('textbox', { name: /message text/i }), {
      target: { value: 'hello world' },
    });

    expect(screen.getByLabelText('Message Text')).toHaveValue('hello world');
  });

  test('Campaign Information filed should trigger validations', async() => {
    await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/message-centre/campaigns/create' ],
      }),
    });
    fireEvent.click(screen.getByRole('button', { name: /submit/i }));
    await waitFor(() => {
      /* There are 5 fields in the form with general 'Required' error message:
      Campaign Short Name, Campaign Type, Campaign Channel, KT Campaign, and Languages
      */
      expect(screen.getAllByText('Required')).toHaveLength(5);
    });
  });

  test('Invalid Campaign ID should trigger validation', async() => {
    await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/message-centre/campaigns/create' ],
      }),
    });
    fireEvent.change(screen.getByRole('textbox', { name: /KT Campaign/i }), {
      target: { value: 'abm' },
    });
    await waitFor(() => {
      expect(
        screen.getByText(
          'Campaign ID can only contain alphanumeric characters and it should be capital letters'
        )
      ).toBeInTheDocument();
    });

    fireEvent.change(screen.getByRole('textbox', { name: /KT Campaign/i }), {
      target: { value: 'ABM123' },
    });
    await waitFor(() => {
      expect(
        screen.getByText('Must be 5 characters or less')
      ).toBeInTheDocument();
    });
  });

  test('should render for create mode', async() => {
    await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/message-centre/campaigns/create' ],
      }),
    });
    expect(screen.getByText('Create a New Campaign')).toBeInTheDocument();
  });

  test('should render for view mode', async() => {
    getMessageCentreCampaign.mockResolvedValue({
      data: mockMessageCentreCampaign,
    });
    await renderPage(
      <MemoryRouter initialEntries={[ '/message-centre/campaigns/1/view' ]}>
        <Route path="/message-centre/campaigns/:id/:action?" component={Details} />
      </MemoryRouter>
    );
    await waitFor(() => {
    expect(screen.getByText('View Campaign')).toBeInTheDocument();
    });
  });

  test('should render for duplicate mode', async() => {
    getMessageCentreCampaign.mockResolvedValue({
      data: mockMessageCentreCampaign,
    });
    await renderPage(
      <MemoryRouter initialEntries={[ '/message-centre/campaigns/1/duplicate' ]}>
        <Route path="/message-centre/campaigns/:id/:action?" component={Details} />
      </MemoryRouter>
    );
    await waitFor(() => {
    expect(screen.getByText('Create a New Campaign')).toBeInTheDocument();
    });
  });

  test('should reset start_date and end_date if campaign expired for duplicate mode', async() => {
    getMessageCentreCampaign.mockResolvedValue({
      data: mockMessageCentreCampaign,
    });
    await renderPage(
      <MemoryRouter initialEntries={[ '/message-centre/campaigns/1/duplicate' ]}>
        <Route path="/message-centre/campaigns/:id/:action?" component={Details} />
      </MemoryRouter>
    );
    await waitFor(() => {
      expect(screen.getByRole('textbox', { name: /start date/i })).toHaveValue('');
      expect(screen.getByRole('textbox', { name: /end date/i })).toHaveValue('');
    });
  });

  test('should be able to submit with required field create mode', async() => {
    createMessageCentreCampaign.mockResolvedValue({ data: { mockMessageCentreCampaign, url_params: [] } });
    const { store } = await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/message-centre/campaigns/create' ],
      }),
    });
    fireEvent.change(screen.getByPlaceholderText('Campaign Short Name'), {
      target: { value: 'shortid' },
    });

    fireEvent.change(
      screen.getByRole(
        'textbox',
        { name: /campaign long name/i },
        { target: { value: 'campaign long name' } }
      )
    );
    fireEvent.change(screen.getByRole('combobox', { name: /campaign type/i }), {
      target: { value: '000' },
    });
    userEvent.selectOptions(screen.getByLabelText(/campaign channel/i), 'ABM');

    fireEvent.change(
      screen.getByRole('combobox', { name: /message category/i }),
      {
        target: { value: 'ALT' },
      }
    );
    fireEvent.change(screen.getByPlaceholderText('Campaign ID'), {
      target: { value: 'TEST1' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: moment().add(2, 'days').format('MM/DD/YYYY 12:00') },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /end date/i }), {
      target: { value: moment().add(10, 'days').format('MM/DD/YYYY 12:00') },
    });
    fireEvent.change(
      screen.getByRole('spinbutton', { name: /campaign duration \/ delay/i }),
      {
        target: { value: 2 },
      }
    );
    fireEvent.click(screen.getByRole('radio', { name: /english/i }));

    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(createMessageCentreCampaign).toHaveBeenCalled();
      const expectedSnackbar = addSnackbar({ message: 'Campaign "shortid" has been created successfully' });
      expect(store.getActions()).toContainEqual(expectedSnackbar);
    });
  });

  test('should be able to submit with required field edit mode', async() => {
    getMessageCentreCampaign.mockResolvedValue({ data: mockMessageCentreCampaign });
    updateMessageCentreCampaign.mockResolvedValue({ data: mockMessageCentreCampaign });

    const { store } = await renderPage(
      <MemoryRouter initialEntries={[ '/message-centre/campaigns/1/edit' ]}>
        <Route path="/message-centre/campaigns/:id/:action?" component={Details} />
      </MemoryRouter>
    );
    fireEvent.change(screen.getByPlaceholderText('Campaign Short Name'), {
      target: { value: 'shortid' },
    });

    fireEvent.change(
      screen.getByRole(
        'textbox',
        { name: /campaign long name/i },
        { target: { value: 'campaign long name' } }
      )
    );
    fireEvent.change(screen.getByRole('combobox', { name: /campaign type/i }), {
      target: { value: '000' },
    });
    userEvent.selectOptions(screen.getByLabelText(/campaign channel/i), 'ABM');

    fireEvent.change(
      screen.getByRole('combobox', { name: /message category/i }),
      {
        target: { value: 'ALT' },
      }
    );
    fireEvent.change(screen.getByPlaceholderText('Campaign ID'), {
      target: { value: 'TEST1' },
    });
    // Use future dates to avoid triggering the modal
    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: moment().add(2, 'days').format('MM/DD/YYYY 12:00') },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /end date/i }), {
      target: { value: moment().add(10, 'days').format('MM/DD/YYYY 12:00') },
    });
    fireEvent.change(
      screen.getByRole('spinbutton', { name: /campaign duration \/ delay/i }),
      {
        target: { value: 2 },
      }
    );

    fireEvent.click(screen.getByRole('radio', { name: /french/i }));

    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      const expectedSnackbar = addSnackbar({ message: 'Campaign "shortid" has been edited successfully' });
      expect(store.getActions()).toContainEqual(expectedSnackbar);
    });
  });

  test('should handle delete action', async() => {
    getMessageCentreCampaign.mockResolvedValue({ data: mockMessageCentreCampaign });
    deleteMessageCentreCampaign.mockResolvedValue({ data: {} });

    const { store } = await renderPage(
      <MemoryRouter initialEntries={[ '/message-centre/campaigns/1/edit' ]}>
        <Route path="/message-centre/campaigns/:id/:action?" component={Details} />
      </MemoryRouter>
    );
    fireEvent.change(screen.getByPlaceholderText('Campaign Short Name'), {
      target: { value: 'shortid' },
    });

    fireEvent.change(
      screen.getByRole(
        'textbox',
        { name: /campaign long name/i },
        { target: { value: 'campaign long name' } }
      )
    );
    fireEvent.change(screen.getByRole('combobox', { name: /campaign type/i }), {
      target: { value: '000' },
    });
    userEvent.selectOptions(screen.getByLabelText(/campaign channel/i), 'ABM');

    fireEvent.change(
      screen.getByRole('combobox', { name: /message category/i }),
      {
        target: { value: 'ALT' },
      }
    );
    fireEvent.change(screen.getByPlaceholderText('Campaign ID'), {
      target: { value: 'TEST1' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: '05/01/2024 12:00 AM' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /end date/i }), {
      target: { value: '06/01/2024 12:00 AM' },
    });
    fireEvent.change(
      screen.getByRole('spinbutton', { name: /campaign duration \/ delay/i }),
      {
        target: { value: 2 },
      }
    );

    fireEvent.click(screen.getByRole('radio', { name: /french/i }));

    fireEvent.change(
      screen.getByRole('combobox', { name: /message status/i }),
      {
        target: { value: 'D' },
      }
    );

    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(screen.getByText('Are you sure you want to delete this message?')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByRole('button', { name: /delete/i }));

      await waitFor(() => {
        const expectedSnackbar = addSnackbar({ message: 'Campaign "shortid" has been deleted successfully' });
        expect(store.getActions()).toContainEqual(expectedSnackbar);
      });
  });

  test('should handle cancel from delete modal', async() => {
    getMessageCentreCampaign.mockResolvedValue({ data: mockMessageCentreCampaign });

    await renderPage(
      <MemoryRouter initialEntries={[ '/message-centre/campaigns/1/edit' ]}>
        <Route path="/message-centre/campaigns/:id/:action?" component={Details} />
      </MemoryRouter>
    );
    fireEvent.change(screen.getByPlaceholderText('Campaign Short Name'), {
      target: { value: 'shortid' },
    });

    fireEvent.change(
      screen.getByRole(
        'textbox',
        { name: /campaign long name/i },
        { target: { value: 'campaign long name' } }
      )
    );
    fireEvent.change(screen.getByRole('combobox', { name: /campaign type/i }), {
      target: { value: '000' },
    });
    userEvent.selectOptions(screen.getByLabelText(/campaign channel/i), 'ABM');

    fireEvent.change(
      screen.getByRole('combobox', { name: /message category/i }),
      {
        target: { value: 'ALT' },
      }
    );
    fireEvent.change(screen.getByPlaceholderText('Campaign ID'), {
      target: { value: 'TEST1' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: '05/01/2024 12:00 AM' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /end date/i }), {
      target: { value: '06/01/2024 12:00 AM' },
    });
    fireEvent.change(
      screen.getByRole('spinbutton', { name: /campaign duration \/ delay/i }),
      {
        target: { value: 2 },
      }
    );

    fireEvent.click(screen.getByRole('radio', { name: /french/i }));

    fireEvent.change(
      screen.getByRole('combobox', { name: /message status/i }),
      {
        target: { value: 'D' },
      }
    );

    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(screen.getByText('Are you sure you want to delete this message?')).toBeInTheDocument();
      });

      fireEvent.click(screen.getAllByRole('button', { name: /cancel/i })[1]);

      await waitFor(() => {
        expect(screen.queryByText('Are you sure you want to delete this message?')).not.toBeInTheDocument();
        });
  });
});

describe('Start Date Validation Modal', () => {
  test('should show start date modal when start date is in the past', async () => {
    await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/message-centre/campaigns/create' ],
      }),
    });

    // Fill required fields minimally
    fireEvent.change(screen.getByPlaceholderText('Campaign Short Name'), {
      target: { value: 'shortid' },
    });
    fireEvent.change(screen.getByRole('combobox', { name: /campaign type/i }), {
      target: { value: '000' },
    });
    userEvent.selectOptions(screen.getByLabelText(/campaign channel/i), 'ABM');
    fireEvent.change(screen.getByPlaceholderText('Campaign ID'), {
      target: { value: 'TEST1' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: moment().subtract(1, 'days').format('MM/DD/YYYY 12:00') },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /end date/i }), {
      target: { value: moment().add(7, 'days').format('MM/DD/YYYY 12:00') },
    });
    fireEvent.click(screen.getByRole('radio', { name: /english/i }));

    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(screen.getByText('Start date is in the past')).toBeInTheDocument();
      expect(
        screen.getByText(
          'The start date you entered is in the past. Are you sure you want to continue with this date?'
        )
      ).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Go back' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Submit anyway' })).toBeInTheDocument();
    });
  });

  test('should close modal and not submit when "Go back" is clicked', async () => {
    createMessageCentreCampaign.mockClear();

    await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/message-centre/campaigns/create' ],
      }),
    });

    // Fill required fields minimally
    fireEvent.change(screen.getByPlaceholderText('Campaign Short Name'), {
      target: { value: 'shortid' },
    });
    fireEvent.change(screen.getByRole('combobox', { name: /campaign type/i }), {
      target: { value: '000' },
    });
    userEvent.selectOptions(screen.getByLabelText(/campaign channel/i), 'ABM');
    fireEvent.change(screen.getByPlaceholderText('Campaign ID'), {
      target: { value: 'TEST1' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: moment().subtract(1, 'days').format('MM/DD/YYYY 12:00') },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /end date/i }), {
      target: { value: moment().add(7, 'days').format('MM/DD/YYYY 12:00') },
    });
    fireEvent.click(screen.getByRole('radio', { name: /english/i }));

    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(screen.getByText('Start date is in the past')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByRole('button', { name: 'Go back' }));

    await waitFor(() => {
      expect(screen.queryByText('Start date is in the past')).not.toBeInTheDocument();
    });
    expect(createMessageCentreCampaign).not.toHaveBeenCalled();
  });

  test('should proceed with submission when "Submit anyway" is clicked', async () => {
    createMessageCentreCampaign.mockResolvedValue({ data: { id: '123' } });

    const { store } = await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/message-centre/campaigns/create' ],
      }),
    });

    // Fill required fields minimally
    fireEvent.change(screen.getByPlaceholderText('Campaign Short Name'), {
      target: { value: 'shortid' },
    });
    fireEvent.change(screen.getByRole('combobox', { name: /campaign type/i }), {
      target: { value: '000' },
    });
    userEvent.selectOptions(screen.getByLabelText(/campaign channel/i), 'ABM');
    fireEvent.change(screen.getByPlaceholderText('Campaign ID'), {
      target: { value: 'TEST1' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: moment().subtract(1, 'days').format('MM/DD/YYYY 12:00') },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /end date/i }), {
      target: { value: moment().add(7, 'days').format('MM/DD/YYYY 12:00') },
    });
    fireEvent.click(screen.getByRole('radio', { name: /english/i }));

    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(screen.getByText('Start date is in the past')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByRole('button', { name: 'Submit anyway' }));

    await waitFor(() => {
      expect(screen.queryByText('Start date is in the past')).not.toBeInTheDocument();
      expect(createMessageCentreCampaign).toHaveBeenCalled();
    });
  });

  test('should not show modal when start date is today or future', async () => {
    await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/message-centre/campaigns/create' ],
      }),
    });

    // Fill required fields minimally
    fireEvent.change(screen.getByPlaceholderText('Campaign Short Name'), {
      target: { value: 'shortid' },
    });
    fireEvent.change(screen.getByRole('combobox', { name: /campaign type/i }), {
      target: { value: '000' },
    });
    userEvent.selectOptions(screen.getByLabelText(/campaign channel/i), 'ABM');
    fireEvent.change(screen.getByPlaceholderText('Campaign ID'), {
      target: { value: 'TEST1' },
    });
    // Today
    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: moment().format('MM/DD/YYYY 12:00') },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /end date/i }), {
      target: { value: moment().add(7, 'days').format('MM/DD/YYYY 12:00') },
    });
    fireEvent.click(screen.getByRole('radio', { name: /english/i }));

    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(screen.queryByText('Start date is in the past')).not.toBeInTheDocument();
    });

    // Future
    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: moment().add(2, 'days').format('MM/DD/YYYY 12:00') },
    });
    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(screen.queryByText('Start date is in the past')).not.toBeInTheDocument();
    });
  });
});
