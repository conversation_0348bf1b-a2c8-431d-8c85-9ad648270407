import React, { useEffect, useMemo, useState } from 'react';
import classnames from 'classnames';
import moment from 'moment';
import PropTypes from 'prop-types';

import SideSheet from 'canvas-core-react/lib/SideSheet';
import CanvasBadge from 'canvas-core-react/lib/StatusBadge';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import TextBody from 'canvas-core-react/lib/TextBody';
import TextLegal from 'canvas-core-react/lib/TextLegal';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';

import IconClose from 'canvas-core-react/lib/IconClose';

import { CAMPAIGN_STATUS_MAPPING, URL_PARAMETERS_DISPLAY_NAMES } from './constants';

const headerHeight = 72;

const SidePanel = ({
  isSidePanelOpen,
  setIsSidePanelOpen,
  selectedCampaign,
  actionList,
  campaignsLoading,
}) => {
  const [ panelOffset, setPanelOffset ] = useState(headerHeight);

  useEffect(() => {
    const handleScroll = () => {
      setPanelOffset(window.scrollY < headerHeight ? headerHeight - scrollY : 0);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const badgeType = useMemo(() => {
    const isExpired = new Date(selectedCampaign.end_date) < Date.now();
    let status = 'default';
    if (CAMPAIGN_STATUS_MAPPING[selectedCampaign.msg_status] === 'new') {
      status = !isExpired ? 'success-emphasis' : 'error';
    }
    return status;
  }, [ selectedCampaign ]);

  const msgStatus = useMemo(() => {
    let status = selectedCampaign.msg_status;
    const isExpired = new Date(selectedCampaign.end_date) < Date.now();
    if (status === 'N' && isExpired) {
      status = 'E';
    }
    return status;
  }, [ selectedCampaign ]);

  return (
    <SideSheet
      headline=""
      isSheetVisible={isSidePanelOpen}
      setSheetVisible={setIsSidePanelOpen}
      className={classnames('admin-list__sidepanel', { 'admin-list__panel-offset': panelOffset })}
      style={{ top: `${panelOffset}px` }}
      type="persistent"
      hideHeading
    >
      <div className="admin-list__sidepanel__container">
        <div className="admin-list__panel-section">
          { campaignsLoading ? (
            <IconSpinner size={32} className="admin-list__panel-badge" />
          ) : (
            <CanvasBadge className="admin-list__panel-badge" type={badgeType}>
              { CAMPAIGN_STATUS_MAPPING[msgStatus]?.toUpperCase() }
            </CanvasBadge>
          ) }
          <button className="admin-list__panel-icon" onClick={() => setIsSidePanelOpen(false)}>
            <IconClose />
          </button>
        </div>
        <TextHeadline className="admin-list__panel-heading" component="h2" size={21}>
          { selectedCampaign.kt_campaign_id }
        </TextHeadline>
        <TextBody className="admin-list__panel-name" component="p">
          { selectedCampaign.name }
        </TextBody>
        <div className="admin-list__panel-section admin-list__panel-icons">
          { actionList.map(action => {
            const isDisabled =
              campaignsLoading && (action.menuName === 'Activate' || action.menuName === 'Suspend');
            return (
              <div
                key={`action-${action.menuName}`}
                className={`admin-list__panel-icon-container ${isDisabled ? 'admin-list__panel-icon-container--disabled' : ''}`}
              >
                <button
                  className={`admin-list__panel-icon ${isDisabled ? 'admin-list__panel-icon--disabled' : ''}`}
                  onClick={action.onClick}
                  disabled={isDisabled}
                >
                  { action.iconType({ color: `${isDisabled ? 'gray' : 'blue'}` }) }
                </button>
                <TextLegal component="p" bold={true}>
                  { action.menuName }
                </TextLegal>
              </div>
            );
          }) }
        </div>
        <ul>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Message Category
            </TextSubtitle>
            <TextCaption component="p">{ selectedCampaign.msg_category }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Message Text
            </TextSubtitle>
            <TextCaption component="p" className="admin-list__panel-content-name">
              { selectedCampaign.msg_text }
            </TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Customer Name
            </TextSubtitle>
            <TextCaption component="p">
              { selectedCampaign.url_params.includes(URL_PARAMETERS_DISPLAY_NAMES['NAME']) ? 'Yes' : 'No' }
            </TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Product Name
            </TextSubtitle>
            <TextCaption component="p">{ selectedCampaign.url_params.includes(URL_PARAMETERS_DISPLAY_NAMES['PRODUCT']) ? 'Yes' : 'No' }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Account Number
            </TextSubtitle>
            <TextCaption component="p">
            { selectedCampaign.url_params.includes(URL_PARAMETERS_DISPLAY_NAMES['ACCT']) ? 'Yes' : 'No' }
            </TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Credit Limit
            </TextSubtitle>
            <TextCaption component="p">{ selectedCampaign.url_params.includes(URL_PARAMETERS_DISPLAY_NAMES['LIMIT']) ? 'Yes' : 'No' }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Other
            </TextSubtitle>
            <TextCaption component="p">{ selectedCampaign.url_params.includes(URL_PARAMETERS_DISPLAY_NAMES['OTHER']) ? 'Yes' : 'No' }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Update User ID
            </TextSubtitle>
            <TextCaption component="p">{ selectedCampaign.updated_user_id }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Last Update Date
            </TextSubtitle>
            <TextCaption component="p">
              { moment(selectedCampaign.updated_date).format('ll') }
            </TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Message Generation Date
            </TextSubtitle>
            <TextCaption component="p">
              { moment(selectedCampaign.msg_gen_date).format('ll') }
            </TextCaption>
          </li>
        </ul>
      </div>
    </SideSheet>
  );
};

SidePanel.propTypes = {
  isSidePanelOpen: PropTypes.bool,
  setIsSidePanelOpen: PropTypes.func,
  selectedCampaign: PropTypes.object,
  actionList: PropTypes.array,
  campaignsLoading: PropTypes.bool,
};

export default SidePanel;
