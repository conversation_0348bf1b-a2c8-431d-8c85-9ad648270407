.message-centre-details {
  &__header {
    &.TextIntroduction__text {
      font-size: 3.2rem;
    }
  }

  &__sub-header {
    margin-bottom: 3.6rem;

    &.TextIntroduction__text {
      font-size: 2.4rem;
    }
  }

  &__card {
    margin: 3.6rem 0;
  }

  &__field {
    margin-bottom: 2.5rem;
    max-width: 60rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__date-fields {
    display: flex;
    margin-bottom: 3.6rem;
    flex-direction: column;

    @include mq($from: desktop) {
      flex-direction: row;
    }
  }

  &__date-field {
    flex: 1;

    &:not(:last-child) {
      margin-bottom: 1.8rem;

      @include mq($from: desktop) {
        margin-right: 2.9rem;
      }
    }
  }

  &__message-select-fields {
    display: flex;
    margin-bottom: 3.6rem;
    flex-direction: column;
    gap: 3.6rem;

    @include mq($from: tablet) {
      flex-direction: row;
    }
  }

  &__message-select-field {
    flex: 1;
  }

  &__message-text-field {
    width: 100%;
    max-width: 100%;
  }

  &__information_fields {
    display: flex;
    margin-bottom: 3.6rem;
    flex-direction: column;
    gap: 3.6rem;
    align-self: stretch;

    @include mq($from: tablet) {
      flex-direction: row;
    }
  }

  &__information_field {
    flex: 1;
  }

  &__input-group-language {
    display: flex;
    // flex-direction: row;
    margin-top: 3.6rem;
  }

  &__radio-container {
    display: flex;
    align-items: center;

    .form__input--radio,
    .RadioButton__container {
      // new Canvas radio button
      margin-top: 0;

      &:last-child {
        margin-left: 2.6rem;
      }
    }
  }

  &__url-parameters-fields {
    @include mq($from: desktop) {
      display: flex;
    }
  }

  &__url-parameters-field {
    flex: 1;
  }

  &__url-label {
    font-size: 1.6rem !important;
  }

  &__static-url-fields {
    @include mq($from: tablet) {
      display: flex;
    }
  }

  &__static-url-indicator {
    margin-bottom: 3.6rem;

    @include mq($from: tablet) {
      margin-bottom: 0;
    }
  }

  &__static-url-text {
    flex: 1;
    margin-bottom: 3.6rem;

    @include mq($from: tablet) {
      margin-left: 3.6rem;
      margin-bottom: 0;
    }
  }

  &__action-buttons {
    display: flex;
    align-content: center;
    justify-content: space-between;
  }

  &__action-button {
    margin-left: 3rem;

    &:first-child {
      margin-left: 0;
    }
  }
}
