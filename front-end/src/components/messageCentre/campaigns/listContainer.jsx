import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';
import qs from 'qs';
import { omit, difference } from 'lodash';
import classnames from 'classnames';
import { useDispatch } from 'react-redux';

import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';
import ActionMenuListItem from 'canvas-core-react/lib/ActionMenuListItem';

import IconEdit from 'canvas-core-react/lib/IconEdit';
import IconClose from 'canvas-core-react/lib/IconClose';
import IconCheck from 'canvas-core-react/lib/IconCheck';
import AlertBanner from 'canvas-core-react/lib/AlertBanner';
import IconDuplicate from '../../../assets/svgs/IconDuplicate';

import PageInfo from '../../pageInfo';
import List from '../../listing/list';
import { tableColumns } from './campaignColumns';
import { useCampaigns } from '../../../hooks/useCampaigns';
import Filters from './filters';
import {
  CAMPAIGN_TYPE_OPTIONS,
  CAMPAIGN_CHANNEL_OPTIONS,
  LANGUAGES,
  CAMPAIGN_STATUS_OPTIONS,
  MESSAGE_PRIORITY_OPTIONS,
} from './constants';

import { removeFalsyKeys } from '../../../utils';
import SidePanel from './sidePanel';

import { setCampaignActive } from '../../../store/actions/message-centre-campaigns';
import { exportMessageCentreCampaigns } from '../../../api/message-centre-campaigns';
import permissionsList from '../../../constants/permissionsList';

const PAGE_SIZE = 10;
const BASE_QUERY = {
  limit: PAGE_SIZE,
  pageNumber: 1,
  search: '',
  language: '',
  type: '',
  priority: '',
  status: '',
  channel: '',
};

const MessageCentreCampaignList = () => {
  const history = useHistory();
  const dispatch = useDispatch();

  const queryParams = qs.parse(history.location.search, { ignoreQueryPrefix: true });
  const initialFilters = {
    ...BASE_QUERY,
    ...omit(queryParams, difference(Object.keys(queryParams), Object.keys(BASE_QUERY))),
  };
  const [ filters, setFilters ] = useState(initialFilters);
  const [ isActionListOpen, setIsActionListOpen ] = useState(false);
  const [ isSidePanelOpen, setIsSidePanelOpen ] = useState(false);
  const [ selectedCampaign, setSelectedCampaign ] = useState(null);

  const { campaignsLoading, isSetCampaignActiveLoading, campaigns, pagination, currentUser } = useCampaigns(removeFalsyKeys(filters));
  const { permissions } = currentUser;
  const canCreate = permissions && (
    permissions['admin'] ||
    permissions[permissionsList.MESSAGE_CENTRE_CAMPAIGNS_MANAGE]
  );

  const canView = permissions && (
    permissions['admin'] ||
    permissions[permissionsList.MESSAGE_CENTRE_CAMPAIGNS_VIEW]
  );

  if (!canView) {
    return (<AlertBanner type="error">You do not have permissions to view this page</AlertBanner>);
  };

  const filtersChanged = (newFilters) => {
    setFilters(newFilters);
  };

  const getActionItems = (row) => {
    if (!row) {
      return;
    }
    const { id } = row;
    const isExpired = new Date(row.end_date) < Date.now();
    const actionListOptions = {
      edit: {
        iconType: IconEdit,
        onClick: () => {
          // using setTimeout as a workaround as clicking action menu adds overflow: none and is not removed after navigation
          setIsActionListOpen(false);
          setTimeout(
            () =>
              history.push(
                `/message-centre/campaigns/${id}/${canCreate && !isExpired ? 'edit' : 'view'}`
              ),
            0
          );
        },
        menuName: canCreate && !isExpired ? 'Edit' : 'View',
      },
      duplicate: {
        iconType: IconDuplicate,
        onClick: () => {
          // using setTimeout as a workaround as clicking action menu adds overflow: none and is not removed after navigation
          setIsActionListOpen(false);
          setTimeout(
            () => history.push(`/message-centre/campaigns/${id}/duplicate`),
            0
          );
        },
        menuName: 'Duplicate',
      },
      suspend: {
        iconType: IconClose,
        onClick: () => {
          dispatch(setCampaignActive(row.id, 'S'));
          setIsActionListOpen(false);
        },
        menuName: 'Suspend',
      },
      activate: {
        iconType: IconCheck,
        onClick: () => {
          dispatch(setCampaignActive(row.id, 'N'));
          setIsActionListOpen(false);
        },
        menuName: 'Activate',
      },
    };
    const status = row.msg_status;
    const actionList = [];
    if (status === 'N') {
      actionList.push(actionListOptions.edit);
      actionList.push(actionListOptions.duplicate);
      if (!isExpired) {
        actionList.push(actionListOptions.suspend);
      }
    }

    if (status === 'S') {
      actionList.push(actionListOptions.edit);
      actionList.push(actionListOptions.duplicate);
      actionList.push(actionListOptions.activate);
    }

    if (status === 'D') {
      actionList.push(actionListOptions.edit);
      actionList.push(actionListOptions.duplicate);
    }

    return {
      actionList,
      actionItems: actionList.map((action) => (
        <ActionMenuListItem key={action.menuName} icon={action.iconType} onClick={action.onClick}>
          { action.menuName }
        </ActionMenuListItem>
      )),
    };
  };

  const handleExport = (e) => {
    exportMessageCentreCampaigns(filters);
  };

  return (
    <div className={classnames('admin-list')}>
      <PageInfo
        title="Campaigns"
        buttonText="Create New"
        TitleComponent={TextHeadline}
        ButtonComponent={SecondaryButton}
        titleComponentPorps={{ component: 'h1' }}
        isLoading={false}
        showActionButton={!!canCreate}
        onClick={() => history.push('/message-centre/campaigns/create')}
      />
      <Filters
        filters={filters}
        onFiltersChange={setFilters}
        filterFields={[
          {
            label: 'Type',
            key: 'type',
            options: CAMPAIGN_TYPE_OPTIONS,
            defaultOptionLabel: 'Select campaign type',
          },
          {
            label: 'Channel',
            key: 'channel',
            options: CAMPAIGN_CHANNEL_OPTIONS,
            defaultOptionLabel: 'Select campaign channel',
          },
          {
            label: 'Language',
            key: 'language',
            options: LANGUAGES.at(0).options,
            defaultOptionLabel: 'Select language',
          },
          {
            label: 'Status',
            key: 'status',
            options: CAMPAIGN_STATUS_OPTIONS,
            defaultOptionLabel: 'Select status',
          },
          {
            label: 'Priority',
            key: 'priority',
            options: MESSAGE_PRIORITY_OPTIONS,
            defaultOptionLabel: 'Select priority',
          },
        ]}
        handleExport={handleExport}
        includeDateTime
      />
      <List
        isLoading={campaignsLoading}
        entityName=""
        className="admin-list__listings-table"
        columnFixed={false}
        columns={tableColumns({
          campaigns,
          getActionItems,
          isActionListOpen,
          setIsActionListOpen,
          setIsSidePanelOpen,
          setSelectedCampaign,
          history,
        })}
        data={campaigns}
        showMangePrefix={false}
      />
      { selectedCampaign && (
        <SidePanel
          isSidePanelOpen={isSidePanelOpen}
          setIsSidePanelOpen={setIsSidePanelOpen}
          selectedCampaign={selectedCampaign}
          actionList={getActionItems(selectedCampaign).actionList}
          campaignsLoading={isSetCampaignActiveLoading}
        />
      ) }
      { !campaignsLoading && campaigns?.length > 0 && (
        <DesktopPagination
          id="message-centre-campaigns-pagination"
          totalResultCount={pagination?.total || 0}
          onChange={(pageNumber) => {
            filtersChanged({ ...filters, pageNumber });
            window.scrollTo(0, 0);
          }}
          firstButtonLabel="First"
          prevButtonLabel="Previous"
          nextButtonLabel="Next"
          lastButtonLabel="Last"
          visiblePages={5}
          navigationLabel="Pagination Navigation"
          pageSize={pagination?.limit || 0}
          currentPage={campaigns ? pagination?.offset / pagination?.limit + 1 : 1}
          containerType="card"
        />
      ) }
    </div>
  );
};

export default MessageCentreCampaignList;
