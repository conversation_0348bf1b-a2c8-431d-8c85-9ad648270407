import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { createMemoryHistory } from 'history';
import { Router } from 'react-router-dom';

import Filters from './filters';
import { CAMPAIGN_CHANNEL_OPTIONS, CAMPAIGN_TYPE_OPTIONS, LANGUAGES, MESSAGE_PRIORITY_OPTIONS, MESSAGE_STATUS_OPTIONS } from './constants';
import { renderPage } from '../../../utils/testing-library-utils';

describe('Message Centre Messages List', () => {
    test('Filters', async() => {
        const { baseElement } = await renderPage(
            <Filters
                filters={{
                    limit: 30,
                    pageNumber: 1,
                }}
                filterFields={[
                    {
                        label: 'Type',
                        key: 'type',
                        options: CAMPAIGN_TYPE_OPTIONS,
                        defaultOptionLabel: 'Select campaign type',
                        isLoading: true,
                    },
                    {
                        label: 'Channel',
                        key: 'channel',
                        options: CAMPAIGN_CHANNEL_OPTIONS,
                        defaultOptionLabel: 'Select campaign channel',
                    },
                    {
                        label: 'Language',
                        key: 'language',
                        options: LANGUAGES.at(0).options,
                        defaultOptionLabel: 'Select language',
                    },
                    {
                        label: 'Status',
                        key: 'status',
                        options: MESSAGE_STATUS_OPTIONS,
                        defaultOptionLabel: 'Select status',
                    },
                    {
                        label: 'Priority',
                        key: 'priority',
                        options: MESSAGE_PRIORITY_OPTIONS,
                        defaultOptionLabel: 'Select priority',
                    },
                ]}
                includeDateTime
            />
        );
        expect(baseElement).toMatchSnapshot();
    });
});

describe('MessageCentreCampaignFilter', () => {
  const renderWithRouter = (component, { route = '/' } = {}) => {
    const history = createMemoryHistory({ initialEntries: [ route ] });
    return {
      ...render(
        <Router history={history}>
          { component }
        </Router>
      ),
      history,
    };
  };

  test('handleChangingFilters updates URL and calls onFiltersChange', async() => {
    const onFiltersChange = jest.fn();

    const { getByLabelText, history } = renderWithRouter(
      <Filters
        filters={{}}
        onFiltersChange={onFiltersChange}
        filterFields={[
          {
            label: 'Status',
            key: 'status',
            options: [
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' },
            ],
          },
        ]}
      />
    );

    // Open filters
    fireEvent.click(screen.getByText('Filters'));

    // Change filter value
    fireEvent.change(getByLabelText('Status'), { target: { value: 'active' } });

    // Check if URL was updated
    expect(history.location.search).toBe('?status=active');

    // Check if onFiltersChange was called with correct params
    expect(onFiltersChange).toHaveBeenCalledWith(
      expect.objectContaining({
        status: 'active',
      })
    );

    // Reset mock to check next call clearly
    onFiltersChange.mockClear();

    // Change filter value again
    fireEvent.change(getByLabelText('Status'), {
      target: { value: 'inactive' },
    });

    // Check if onFiltersChange was called with correct params
    expect(onFiltersChange).toHaveBeenCalledWith({
      status: 'inactive',
    });

    // Reset mock again
    onFiltersChange.mockClear();
  });

  test('handleChangingFilters removes falsy values and pagination for page 1', () => {
    const onFiltersChange = jest.fn();
    const { getByLabelText, history } = renderWithRouter(
      <Filters
        filters={{
          status: 'active',
          pageNumber: 1,
          limit: 10,
          emptyValue: '',
          nullValue: null,
        }}
        onFiltersChange={onFiltersChange}
        filterFields={[
          {
            label: 'Status',
            key: 'status',
            options: [
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' },
            ],
          },
        ]}
      />
    );

    // Open filters
    fireEvent.click(screen.getByText('Filters'));

    // Change filter value
    fireEvent.change(getByLabelText('Status'), { target: { value: 'inactive' } });

    // Check if URL was updated without falsy values and page 1
    expect(history.location.search).toBe('?status=inactive');

    // Verify onFiltersChange was called with all values (including falsy)
    expect(onFiltersChange).toHaveBeenCalledWith(
      expect.objectContaining({
        status: 'inactive',
        pageNumber: 1,
        limit: 10,
      })
    );
  });
});

describe('MessageCentreCampaignFilter Snapshots', () => {
  // Setup matchMedia mock before all tests
  beforeAll(() => {
    window.matchMedia = jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(), // Deprecated
      removeListener: jest.fn(), // Deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    }));
  });

  const defaultProps = {
    title: 'Test Filters',
    filters: {},
    includeDateTime: false,
    className: 'test-class',
    onFiltersChange: jest.fn(),
    filterFields: [
      {
        label: 'Status',
        key: 'status',
        options: [
          { value: 'active', label: 'Active' },
          { value: 'inactive', label: 'Inactive' },
        ],
      },
    ],
  };

  it('renders correctly with filters expanded', () => {
    const { asFragment, getByRole } = render(
      <Router history={createMemoryHistory()}>
        <Filters {...defaultProps} />
      </Router>
    );

    // Find and click the button using role and text content
    const filterButton = getByRole('button', { name: /filters/i });
    fireEvent.click(filterButton);

    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with default props', () => {
    const { asFragment } = render(<Filters {...defaultProps} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with filters collapsed', () => {
    const { asFragment } = render(
      <Filters {...defaultProps} />
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with date time filters included', () => {
    const propsWithDateTime = {
      ...defaultProps,
      includeDateTime: true,
    };
    const { asFragment } = render(<Filters {...propsWithDateTime} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly after clear button is clicked', () => {
    const propsWithDateTime = {
      ...defaultProps,
      includeDateTime: true,
    };
    const { asFragment } = render(
      <Router history={createMemoryHistory()}>
        <Filters {...propsWithDateTime} />
      </Router>
    );

    // Find and click the button using role and text content
    fireEvent.click(screen.getByText('Filters'));
    fireEvent.click(screen.getByText('Clear all'));

    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with custom filter fields', () => {
    const customFilterFields = [
      {
        label: 'Priority',
        key: 'priority',
        options: [
          { value: 'high', label: 'High' },
          { value: 'low', label: 'Low' },
        ],
      },
    ];
    const propsWithCustomFields = {
      ...defaultProps,
      filterFields: customFilterFields,
    };
    const { asFragment } = render(<Filters {...propsWithCustomFields} />);
    expect(asFragment()).toMatchSnapshot();
  });
});

describe('MessageCentreCampaignFilter Search Functionality', () => {
  // Setup matchMedia mock before all tests
  beforeAll(() => {
    window.matchMedia = jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    }));
  });

  const renderWithRouter = (component, { route = '/' } = {}) => {
    const history = createMemoryHistory({ initialEntries: [ route ] });
    return {
      ...render(
        <Router history={history}>
          { component }
        </Router>
      ),
      history,
    };
  };

  const defaultProps = {
    filters: {},
    onFiltersChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('handles search input with debounce', async() => {
    const onFiltersChange = jest.fn();

    const { getByPlaceholderText } = renderWithRouter(
      <Filters
        {...defaultProps}
        onFiltersChange={onFiltersChange}
      />
    );

    // Type in search
    fireEvent.change(getByPlaceholderText('Search'), { target: { value: 'test search' } });

    // Fast-forward timers to trigger debounced function
    jest.advanceTimersByTime(500);

    await waitFor(() => {
      expect(onFiltersChange).toHaveBeenCalledWith(
        expect.objectContaining({
          search: 'test search',
          pageNumber: 1,
        })
      );
    });
  });

  it('preserves existing filters when searching', async() => {
    const existingFilters = {
      status: 'active',
      type: 'campaign',
    };

    const onFiltersChange = jest.fn();
    const { getByPlaceholderText } = renderWithRouter(
      <Filters
        {...defaultProps}
        filters={existingFilters}
        onFiltersChange={onFiltersChange}
      />
    );

    // Type in search
    fireEvent.change(getByPlaceholderText('Search'), { target: { value: 'test search' } });

    // Fast-forward timers to trigger debounced function
    jest.advanceTimersByTime(500);

    await waitFor(() => {
      expect(onFiltersChange).toHaveBeenCalledWith(
        expect.objectContaining({
          ...existingFilters,
          search: 'test search',
          pageNumber: 1,
        })
      );
    });
  });

  it('updates URL when search changes', async() => {
    const { getByPlaceholderText, history } = renderWithRouter(
      <Filters {...defaultProps} />
    );

    // Type in search
    fireEvent.change(getByPlaceholderText('Search'), { target: { value: 'test search' } });

    // Fast-forward timers to trigger debounced function
    jest.advanceTimersByTime(500);

    await waitFor(() => {
      expect(history.location.search).toContain('search=test%20search');
    });
  });

  it('handles empty search query', async() => {
    const onFiltersChange = jest.fn();

    const { getByPlaceholderText } = renderWithRouter(
      <Filters
        {...defaultProps}
        onFiltersChange={onFiltersChange}
      />
    );

    // Type in search and then clear it
    fireEvent.change(getByPlaceholderText('Search'), { target: { value: 'test search' } });
    jest.advanceTimersByTime(500);

    fireEvent.change(getByPlaceholderText('Search'), { target: { value: '' } });
    jest.advanceTimersByTime(500);

    await waitFor(() => {
      // Should not set initial load to false for empty search
      expect(onFiltersChange).toHaveBeenLastCalledWith(
        expect.objectContaining({
          search: '',
          pageNumber: 1,
        })
      );
    });
  });
});
