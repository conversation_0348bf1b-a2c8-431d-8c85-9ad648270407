/* eslint-disable no-unused-expressions */
import React, { useMemo, useState } from 'react';

import Card from 'canvas-core-react/lib/Card';
import TextIntroduction from 'canvas-core-react/lib/TextIntroduction';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import BackButton from 'canvas-core-react/lib/BackButton';
import AlertBanner from 'canvas-core-react/lib/AlertBanner';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';

import moment from 'moment';
import { useForm } from 'react-hook-form';
import { useParams, useHistory } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';

import {
  InputRadioGroupButtonField,
  InputDateField,
  InputTextField,
  InputSelectField,
} from '../../formFields/reactHookForm';
import { required, requiredDate, max, campaignId } from '../../../utils/validation';
import {
  CAMPAIGN_CHANNEL_OPTIONS,
  CAMPAIGN_TYPE_OPTIONS,
  MESSAGE_CATEGORY_OPTIONS,
  MESSAGE_PRIORITY_OPTIONS,
  MESSAGE_STATUS_OPTIONS,
  URL_PARAMETERS,
  LANGUAGES,
  MESSAGE_CENTRE_CAMPAIGN_FIELDS,
  URL_PARAMETERS_DISPLAY_NAMES,
  INVALID_DATE_COMPARISON_ERROR_MSG,
} from './constants';
import {
  createMessageCentreCampaign,
  updateMessageCentreCampaign,
  getMessageCentreCampaign,
  deleteMessageCentreCampaign,
} from '../../../api/message-centre-campaigns';
import { addSnackbar } from '../../../store/actions/snackbar';
import { addAlert } from '../../../store/actions/alertBanner';
import permissionsList from '../../../constants/permissionsList';
import { emptyStringsToNull } from '../../../utils';

function MessageCentreCampaignDetails() {
  const dispatch = useDispatch();
  const { action, id } = useParams();
  const history = useHistory();

  const mode = action || 'create';

  const authenticated = useSelector(state => state.authenticated);
  const permissions = useMemo(() => authenticated?.permissions || {}, [ authenticated ]);

  const [ isDeleteModalVisible, setIsDeleteModalVisible ] = useState(false);
  const [ isStartDateModalVisible, setIsStartDateModalVisible ] = useState(false);
  const [ pendingFormValues, setPendingFormValues ] = useState(null);

  const canEdit = permissions && (
    permissions['admin'] ||
    permissions[permissionsList.MESSAGE_CENTRE_CAMPAIGNS_MANAGE]
  );

  const canView = permissions && (
    permissions['admin'] ||
    permissions[permissionsList.MESSAGE_CENTRE_CAMPAIGNS_VIEW]
  );

  if (!canView) {
    return (<AlertBanner type="error">You do not have permissions to view this page</AlertBanner>);
  };

  const {
    handleSubmit,
    control,
    getValues,
    reset,
    trigger,
  } = useForm({
    mode: 'onChange',
    defaultValues: id
      ? async() => getMessageCentreCampaign(id).then(({ data }) => formatData(data))
      : MESSAGE_CENTRE_CAMPAIGN_FIELDS,
  });

  const formatData = (data) => {
    const isExpiredCampaign = new Date(data.end_date) < Date.now();
    return {
      ...data,
      start_date: isExpiredCampaign ? '' : moment(data.start_date),
      end_date: isExpiredCampaign ? '' : moment(data.end_date),
      customer_name: data.url_params.includes(URL_PARAMETERS_DISPLAY_NAMES.NAME) ? 'Yes' : 'No',
      account_number: data.url_params.includes(URL_PARAMETERS_DISPLAY_NAMES.ACCT) ? 'Yes' : 'No',
      credit_limit: data.url_params.includes(URL_PARAMETERS_DISPLAY_NAMES.LIMIT) ? 'Yes' : 'No',
      product_name: data.url_params.includes(URL_PARAMETERS_DISPLAY_NAMES.PRODUCT) ? 'Yes' : 'No',
      other: data.url_params.includes(URL_PARAMETERS_DISPLAY_NAMES.OTHER) ? 'Yes' : 'No',
    };
  };

  const submitForm = async(values) => {
    const urlParams = [];
    values.customer_name === 'Yes' && urlParams.push(URL_PARAMETERS_DISPLAY_NAMES.NAME);
    values.account_number === 'Yes' && urlParams.push(URL_PARAMETERS_DISPLAY_NAMES.ACCT);
    values.credit_limit === 'Yes' && urlParams.push(URL_PARAMETERS_DISPLAY_NAMES.LIMIT);
    values.product_name === 'Yes' && urlParams.push(URL_PARAMETERS_DISPLAY_NAMES.PRODUCT);
    values.other === 'Yes' && urlParams.push(URL_PARAMETERS_DISPLAY_NAMES.OTHER);

    const data = emptyStringsToNull({
      // Campaign Information
      name: values.name,
      long_name: values.long_name,
      campaign_type: values.campaign_type,
      campaign_channel: values.campaign_channel,
      kt_campaign_id: values.kt_campaign_id,
      // Targeting Dimension
      start_date: new Date(values.start_date),
      end_date: new Date(values.end_date),
      duration: values.duration ? values.duration : null,
      language: values.language,
      // add message fields dynamically as its optional and only strings are allowed
      ...(values.msg_category && { msg_category: values.msg_category }),
      ...(values.msg_priority && { msg_priority: values.msg_priority }),
      ...(values.msg_status && { msg_status: values.msg_status }),
      msg_text: values.msg_text,
      // URL Parameters
      url_params: urlParams,
      // Static URL
      static_url_ind: values.static_url_ind,
      static_url_text: values.static_url_text,
    });

    const formatAction = mode === 'edit' ? 'edited' : 'created';
    try {
      mode === 'create' || mode === 'duplicate'
        ? await createMessageCentreCampaign(data).then(res =>
          history.push(`/message-centre/campaigns/${res.data.id}/edit`))
        : await updateMessageCentreCampaign(id, data).then(res => {
            reset(formatData(res.data));
          });
      dispatch(
        addSnackbar({
          message: `Campaign "${values.name}" has been ${formatAction} successfully`,
        }),
      );
    } catch (error) {
      const errorMessage = error.response.data.errorMessage || 'Failed to save campaign';
      dispatch(addAlert({ message: errorMessage }));
    }
  };

  const handleForm = async(values, e) => {
    e.preventDefault();
    trigger();

    // perform delete when message status is Delete
    if (values.msg_status === 'D') {
      setIsDeleteModalVisible(true);
      return;
    }

    // Check if start date is in the past
    const startDate = moment(values.start_date);
    const today = moment().startOf('day');

    if ((mode === 'create' || mode === 'duplicate') && startDate.isBefore(today)) {
      setPendingFormValues(values);
      setIsStartDateModalVisible(true);
      return;
    }

    // Proceed with normal submission
    await submitForm(values);
  };

  const handleStartDateConfirm = async() => {
    setIsStartDateModalVisible(false);
    if (pendingFormValues) {
      await submitForm(pendingFormValues);
      setPendingFormValues(null);
    }
  };

  const handleStartDateCancel = () => {
    setIsStartDateModalVisible(false);
    setPendingFormValues(null);
  };

  const handleDelete = async() => {
    try {
      await deleteMessageCentreCampaign(id);

      dispatch(
        addSnackbar({
          message: `Campaign "${getValues(
            'name'
          )}" has been deleted successfully`,
        })
      );
      history.push('/message-centre/campaigns');
    } catch (error) {
      dispatch(addAlert({ message: `Failed to delete campaign` }));
    }
  };

  return (
    <>
      <div className="message-centre-details">
        <form onSubmit={handleSubmit(handleForm)}>
          <div className="message-centre-details__action-bar">
            <TextIntroduction
              component="h1"
              className="message-centre-details__header"
            >
              { mode === 'create' || mode === 'duplicate' ? (
                <> Create a New Campaign </>
              ) : mode === 'edit' ? (
                <> Edit Campaign </>
              ) : (
                <> View Campaign </>
              ) }
            </TextIntroduction>
          </div>

          <Card className="message-centre-details__card" type="floatLow">
            <TextHeadline
              component="h2"
              size={21}
              className="message-centre-details__sub-header"
            >
              Campaign Information
            </TextHeadline>
            <div className="message-centre-details__information_fields">
              <InputTextField
                control={control}
                disabled={mode === 'view'}
                className="message-centre-details__information_field"
                name="name"
                label="Campaign Short Name(max 31 characters)"
                placeholder="Campaign Short Name"
                rules={{
                  validate: {
                    max: max(31),
                    required,
                  },
                }}
              />
              <InputTextField
                control={control}
                className="message-centre-details__information_field"
                name="long_name"
                label="Campaign Long Name"
                placeholder="Campaign Long Name"
                disabled={mode === 'view'}
              />
            </div>
            <div className="message-centre-details__information_fields">
              <InputSelectField
                className="message-centre-details__information_field"
                control={control}
                disabled={mode === 'view'}
                name="campaign_type"
                label="Campaign Type"
                placeholder="Campaign Type"
                rules={{
                  validate: {
                    required,
                  },
                }}
                options={CAMPAIGN_TYPE_OPTIONS}
                optionValue="value"
              />
              <InputSelectField
                className="message-centre-details__information_field"
                control={control}
                disabled={mode === 'view'}
                name="campaign_channel"
                label="Campaign Channel"
                placeholder="Campaign Channel"
                rules={{
                  validate: {
                    required,
                  },
                }}
                options={CAMPAIGN_CHANNEL_OPTIONS}
                optionValue="value"
              />
              <InputTextField
                control={control}
                disabled={mode === 'view'}
                className="message-centre-details__information_field"
                name="kt_campaign_id"
                label="KT Campaign"
                placeholder="Campaign ID"
                rules={{
                  validate: {
                    required,
                    campaignId,
                    max: max(5),
                  },
                }}
              />
            </div>
          </Card>

          <Card className="message-centre-details__card" type="floatLow">
            <TextHeadline
              component="h2"
              size={21}
              className="message-centre-details__sub-header"
            >
              Targeting Dimension
            </TextHeadline>
            <div className="message-centre-details__date-fields">
              <InputDateField
                control={control}
                disabled={mode === 'view'}
                name="start_date"
                className="message-centre-details__date-field"
                rules={{
                  validate: {
                    requiredDate,
                  },
                }}
                label="Start date"
                placeholder="MM/DD/YYYY"
              />
              <InputDateField
                control={control}
                disabled={mode === 'view'}
                name="end_date"
                className="message-centre-details__date-field"
                label="End date"
                placeholder="MM/DD/YYYY"
                rules={{
                  validate: {
                    requiredDate,
                    moreThanStartDate: value =>
                      moment(getValues('start_date')) &&
                      moment(value) &&
                      moment(value).isSameOrBefore(
                        moment(getValues('start_date'))
                      )
                        ? INVALID_DATE_COMPARISON_ERROR_MSG
                        : undefined,
                  },
                }}
              />
              <InputTextField
                control={control}
                disabled={mode === 'view'}
                name="duration"
                label="Campaign Duration / Delay"
                placeholder="Number of days"
                type="number"
                className="message-centre-details__date-field"
              />
            </div>
            <div>
              { LANGUAGES.map(language => (
                <InputRadioGroupButtonField
                  control={control}
                  disabled={mode === 'view'}
                  label={language.label}
                  name={language.name}
                  key={language.name}
                  options={language.options}
                  inline={true}
                  inputGroupClassName="message-centre-details__input-group-langauge"
                  radioContainerClassName="message-centre-details__radio-container"
                  rules={{
                    validate: {
                      required,
                    },
                  }}
                />
              )) }
            </div>
          </Card>

          <Card className="message-centre-details__card" type="floatLow">
            <TextHeadline
              component="h2"
              size={21}
              className="message-centre-details__sub-header"
            >
              Message Settings and Information
            </TextHeadline>
            <div className="message-centre-details__message-select-fields">
              <InputSelectField
                control={control}
                disabled={mode === 'view'}
                className="message-centre-details__message-select-field"
                name="msg_category"
                label="Message Category"
                placeholder="Message Category"
                options={MESSAGE_CATEGORY_OPTIONS}
                optionValue="value"
              />
              <InputSelectField
                control={control}
                disabled={mode === 'view'}
                className="message-centre-details__message-select-field"
                name="msg_priority"
                label="Message Priority"
                placeholder="Message Priority"
                options={MESSAGE_PRIORITY_OPTIONS}
                optionValue="value"
              />
              <InputSelectField
                control={control}
                disabled={mode === 'view'}
                className="message-centre-details__message-select-field"
                name="msg_status"
                label="Message Status"
                placeholder="Message Status"
                options={MESSAGE_STATUS_OPTIONS.filter(
                  i =>
                    // filter out delete from create mode
                    !(mode === 'create' && i.value === 'D')
                )}
                optionValue="value"
              />
            </div>
            <InputTextField
              control={control}
              disabled={mode === 'view'}
              name="msg_text"
              label="Message Text"
              placeholder="Message Text"
              className="message-centre-details__message-text-field"
            />
          </Card>
          <Card className="message-centre-details__card" type="floatLow">
            <div>
              <TextHeadline
                component="h2"
                size={21}
                className="message-centre-details__sub-header"
              >
                URL Parameters
              </TextHeadline>
              <div className="message-centre-details__url-parameters-fields">
                { URL_PARAMETERS.map(item => (
                  <div
                    className="message-centre-details__url-parameters-field"
                    key={item.name}
                  >
                    <InputRadioGroupButtonField
                      control={control}
                      disabled={mode === 'view'}
                      name={item.name}
                      label={item.label}
                      options={item.options}
                      inputGroupClassName="message-centre-details__url-parameters-field"
                    />
                  </div>
                )) }
              </div>
            </div>
          </Card>
          <Card className="message-centre-details__card" type="floatLow">
            <TextHeadline
              component="h2"
              size={21}
              className="message-centre-details__sub-header"
            >
              Static URL
            </TextHeadline>
            <div className="message-centre-details__static-url-fields">
              <InputTextField
                control={control}
                disabled={mode === 'view'}
                name="static_url_ind"
                label="Static URL Indicator"
                placeholder="Static URL Indicator"
                className="message-centre-details__static-url-indicator"
              />
              <InputTextField
                control={control}
                disabled={mode === 'view'}
                name="static_url_text"
                label="Static URL Text"
                placeholder="Static URL Text"
                className="message-centre-details__static-url-text"
              />
            </div>
          </Card>
          <div className="message-centre-details__action-buttons">
            <BackButton onClick={() => history.goBack()} type="button">
              Cancel
            </BackButton>

            { mode === 'view' || !canEdit ? null : (
              <PrimaryButton
                type="submit"
                className="message-centre-details__action-button"
              >
                Submit
              </PrimaryButton>
            ) }
          </div>
        </form>
      </div>
      
      {/* Delete Modal */}
      <ModalDialogue
        headline="Are you sure you want to delete this message?"
        primaryButtonLabel="Delete"
        secondaryButtonLabel="Cancel"
        isModalVisible={isDeleteModalVisible}
        primaryAction={handleDelete}
        secondaryAction={() => {
          setIsDeleteModalVisible(false);
        }}
      >
        This action cannot be undone. Are you sure you want to delete this
        campaign?
      </ModalDialogue>

      {/* Start Date Validation Modal */}
      <ModalDialogue
        headline="Start date is in the past"
        primaryButtonLabel="Go back"
        secondaryButtonLabel="Submit anyway"
        isModalVisible={isStartDateModalVisible}
        primaryAction={handleStartDateCancel}
        secondaryAction={handleStartDateConfirm}
      >
        The start date you entered is in the past. Are you sure you want to continue with this date?
      </ModalDialogue>
    </>
  );
}

export default MessageCentreCampaignDetails;
