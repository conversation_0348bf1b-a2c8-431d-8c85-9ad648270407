import React from 'react';
import moment from 'moment';
import { Link } from 'react-router-dom';

import TextCaption from 'canvas-core-react/lib/TextCaption';
import TextButton from 'canvas-core-react/lib/TextButton';
import CanvasBadge from 'canvas-core-react/lib/StatusBadge';
import IconInfo from 'canvas-core-react/lib/IconInfo';
import ActionMenuList from 'canvas-core-react/lib/ActionMenuList';
import CanvasLink from 'canvas-core-react/lib/Link';

import { LANGUAGE_MAPPING, MESSAGE_PRIORITY_MAPPING, CAMPAIGN_STATUS_MAPPING } from './constants';

export const tableColumns = ({
  campaigns,
  getActionItems,
  isActionListOpen,
  setIsActionListOpen,
  setIsSidePanelOpen,
  setSelectedCampaign,
  history,
}) => {
  const columns = [
    {
      name: 'Type',
      cellFormatter: (row) => (
        <>
          <TextCaption component="p">{ row.campaign_type || 'N/A' }</TextCaption>
        </>
      ),
      selector: '',
      bodyStyle: { display: 'flex', flexFlow: 'wrap' },
      minWidth: 'auto',
      grow: 0.8,
    },
    {
      name: `ID`,
      cellFormatter: (row) => (
        <CanvasLink
          href=""
          component={Link}
          type="emphasis"
          className="admin-list__name-link"
          to={{
            pathname: `/campaigns${row.campaign_type === 'SOL' ? '/sol' : '' }`,
            search: `?${row.campaign_type !== 'SOL' ? 'external_ref=TARGETED&' : '' }campaign_id=${row.kt_campaign_id}`,
            state: { from: 'message-centre' },
          }}
        >
          { row.kt_campaign_id }
        </CanvasLink>
      ),
      selector: '',
      bodyStyle: { textAlign: 'left' },
    },
    {
      name: 'Channel',
      cellFormatter: (row) => (
        <>
          <TextCaption component="p">{ row.campaign_channel || 'N/A' }</TextCaption>
        </>
      ),
      selector: '',
      minWidth: 'auto',
    },
    {
      name: 'Language',
      cellFormatter: (row) => (
        <>
          <TextCaption component="p">{ LANGUAGE_MAPPING[row.language] || 'N/A' }</TextCaption>
        </>
      ),
      selector: '',
      minWidth: 'auto',
    },
    {
      name: 'Status',
      cellFormatter: (row) => {
        let badgeType = 'default';
        if (row.msg_status === 'N') {
          badgeType = 'success-emphasis';
        } else if (row.msg_status === 'D') {
          badgeType = 'error';
        }

        let msgStatus = row.msg_status;
        if (msgStatus === 'N') {
          if (new Date(row.end_date) < Date.now()) {
            msgStatus = 'E';
            badgeType = 'error';
          }
        }
        return (
          <CanvasBadge type={badgeType}>
            { CAMPAIGN_STATUS_MAPPING[msgStatus]?.toUpperCase() }
          </CanvasBadge>
        );
      },
      selector: '',
      minWidth: 'auto',
      bodyStyle: { height: '100%' },
    },
    {
      name: 'Priority',
      cellFormatter: (row) => (
        <>
          <TextCaption component="p">
            { MESSAGE_PRIORITY_MAPPING[row.msg_priority] || 'N/A' }
          </TextCaption>
        </>
      ),
      selector: '',
      minWidth: 'auto',
    },
    {
      name: 'Start Date',
      cellFormatter: (row) => (
        <TextCaption component="p">{ moment(row.start_date).format('ll') }</TextCaption>
      ),
      selector: '',
      minWidth: 'auto',
    },
    {
      name: 'End Date',
      cellFormatter: (row) => (
        <TextCaption component="p">{ moment(row.end_date).format('ll') }</TextCaption>
      ),
      selector: '',
      minWidth: 'auto',
    },
    {
      name: `Campaign Name`,
      cellFormatter: (row) => (
        <TextButton
          Icon={IconInfo}
          iconPosition="right"
          className="admin-list__name-link"
          onClick={() => {
            setSelectedCampaign(row);
            setIsSidePanelOpen(true);
          }}
        >
          { row.name }
        </TextButton>
      ),
      selector: '',
      grow: 2,
      bodyStyle: { textAlign: 'left', wordBreak: 'break-word', width: 'max-content' },
    },
    {
      name: 'Action',
      cellFormatter: (row) => {
        const index = campaigns?.findIndex((_) => _.id === row.id);
        const isBottom = campaigns?.length > 3 && campaigns.length - index > 3;
        return (
          <ActionMenuList
            isMenuOpen={row.id === isActionListOpen}
            setIsMenuOpen={(v) => setIsActionListOpen(v ? row.id : false)}
            bottomSheet={{ heading: '' }}
            iconType="horizontal-small"
            dialogPosition={isBottom ? 'top-center' : 'bottom-center'}
          >
            { getActionItems(row).actionItems }
          </ActionMenuList>
        );
      },
      selector: '',
      minWidth: 'auto',
    },
  ];
  return columns;
};
