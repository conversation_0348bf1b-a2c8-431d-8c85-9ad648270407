import React from 'react';
import mockAxios from 'axios';
import { fireEvent, waitFor } from '@testing-library/react';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';

import { renderPage, defaultData } from '../../../utils/testing-library-utils';
import CampaignsList from './listContainer';
import permissionsList from '../../../constants/permissionsList';
import { exportMessageCentreCampaigns } from '../../../api/message-centre-campaigns';

// Add mock for exportMessageCentreCampaigns
jest.mock('../../../api/message-centre-campaigns', () => ({
  exportMessageCentreCampaigns: jest.fn(),
}));

const mockStore = configureMockStore([ thunk ]);

describe('Message Centre campaigns list', () => {
    beforeEach(() => {
      // Mock window.scrollTo
      window.scrollTo = jest.fn();
    });
  afterEach(() => {
    mockAxios.reset();
    Date.now = jest.fn(() => new Date(Date.UTC(2024, 1, 1)).valueOf());
  });

  test('Should render spinner while loading', async() => {
    const { baseElement } = await renderPage(
      <CampaignsList />,
      { initialState: { messageCentreCampaigns: { isLoading: true } } },
    );
    expect(baseElement).toMatchSnapshot(); // no text element to assert, have to resort to snapshot matching
  });

  test('Should call get campagins', async() => {
    const { baseElement } = await renderPage(
      <CampaignsList />,
    );
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
    expect(baseElement).toMatchSnapshot(); // no text element to assert, have to resort to snapshot matching
  });

  test('Should render list', async() => {
    const { baseElement } = await renderPage(<CampaignsList />, {
      initialState: {
        messageCentreCampaigns: {
          items: defaultData.messageCentreCampaigns,
          isLoading: false,
          pagination: {
            total: 3,
            limit: 1,
            offset: 0,
          },
        },
      },
    });
    expect(baseElement).toMatchSnapshot(); // no text element to assert, have to resort to snapshot matching
  });

  test('Side panel open', async() => {
    const { container, getAllByRole } = await renderPage(
      <CampaignsList />,
    );
    const allButtons = getAllByRole((role, element) => role === 'button' && element.className.includes('admin-list__name-link'));
    fireEvent.click((allButtons[0]));
    const sidePanel = container.querySelector('.admin-list__sidepanel__container');
    expect(sidePanel).toBeInTheDocument();
  });

  test('Hide create button if no access', async() => {
    const { queryByText } = await renderPage(
      <CampaignsList />,
      {
        store: mockStore({
          authenticated: { permissions: { [permissionsList.MESSAGE_CENTRE_CAMPAIGNS_VIEW]: true, [permissionsList.MESSAGE_CENTRE_CAMPAIGNS_MANAGE]: false } },
          messageCentreCampaigns: { isLoading: false, items: [] } }
      ) }
    );
    await waitFor(() => {
      expect(queryByText('Create New')).not.toBeInTheDocument();
    });
  });

  test('No permissions - check for alert banner', async() => {
    const { getByText } = await renderPage(
      <CampaignsList />,
      {
        store: mockStore({
          authenticated: { permissions: { [permissionsList.MESSAGE_CENTRE_CAMPAIGNS_VIEW]: false } },
          messageCentreCampaigns: { isLoading: false, items: [] } }
      ) }
    );
    expect(getByText('You do not have permissions to view this page')).toBeInTheDocument();
  });

  test('Should redirect to campaigns page if clicked on campaign id', async() => {
    const history = {
      push: jest.fn(),
      listen: jest.fn(),
      location: {
        pathname: '/message-centre/campaigns',
      },
      createHref: jest.fn(),
    };
    const { getAllByText } = await renderPage(<CampaignsList />, { history });
    fireEvent.click(getAllByText(defaultData.messageCentreCampaigns[0].kt_campaign_id)[0]);
    expect(history.push).toHaveBeenCalledWith(
      { 'pathname': '/campaigns', 'search': '?external_ref=TARGETED&campaign_id=PAC79', 'state': { 'from': 'message-centre' } },
    );
  });

  test('Should redirect to campaigns sol tab if clicked on campaign id with type sol', async() => {
    const history = {
      push: jest.fn(),
      listen: jest.fn(),
      location: {
        pathname: '/message-centre/campaigns',
      },
      createHref: jest.fn(),
    };
    const { getAllByText } = await renderPage(<CampaignsList />, { history });
    fireEvent.click(getAllByText(defaultData.messageCentreCampaigns[0].kt_campaign_id)[2]);
    expect(history.push).toHaveBeenCalledWith(
      { 'pathname': '/campaigns/sol', 'search': '?campaign_id=PAC79', 'state': { 'from': 'message-centre' } },
    );
  });

  test('Should call handleExport when Export button is clicked', async() => {
    const { getAllByRole } = await renderPage(<CampaignsList />);
    const exportButton = getAllByRole('button', { name: /Export/i })[0];
    fireEvent.click(exportButton);
    expect(exportMessageCentreCampaigns).toHaveBeenCalled();
  });

  test('should navigate to create campaign page when Create New button is clicked', async() => {
    const history = {
      push: jest.fn(),
      listen: jest.fn(),
      location: {
        pathname: '/message-centre/campaigns',
      },
      createHref: jest.fn(),
    };
    const { getAllByText } = await renderPage(
      <CampaignsList />,
      { history,
        initialState: {
          currentUser: {
            permissions: {
              admin: true, // or MESSAGE_CENTRE_CAMPAIGNS_MANAGE: true
            },
          },
        },
      }
    );

    const createButton = getAllByText('Create New')[0];
    fireEvent.click(createButton);

    expect(history.push).toHaveBeenCalledWith('/message-centre/campaigns/create');
  });

  test('should update filters and scroll to top when page changes', async() => {
    const { getByLabelText } = await renderPage(
      <CampaignsList />,
      {
        initialState: {
          currentUser: {
            permissions: { admin: true },
          },
        },
      }
    );

    // Find and click the "Next" button
    const nextButton = getByLabelText('Next');
    fireEvent.click(nextButton);

    // Check if filters were updated with new page number
    expect(window.scrollTo).toHaveBeenCalledWith(0, 0);
  });

  test('should navigate to specific page when page number is clicked', async() => {
    const { getByText } = await renderPage(
      <CampaignsList />,
      {
        initialState: {
          currentUser: {
            permissions: { admin: true },
          },
          messageCentreCampaigns: {
            items: defaultData.messageCentreCampaigns,
            isLoading: false,
            pagination: {
            total: 3,
            limit: 1,
            offset: 0,
            },
          },
        },
      }
    );

    // Click page 2
    const pageButton = getByText('2');
    fireEvent.click(pageButton);

    // Verify scroll to top was called
    expect(window.scrollTo).toHaveBeenCalledWith(0, 0);
  });
});
