export const MESSAGE_CATEGORY_OPTIONS = [
  { value: 'ALT', name: 'ALT - Intercept + Message Centre + Alt' },
  { value: 'BRD', name: 'BRD - Broadcast' },
  { value: 'DCN', name: 'DCN - New Notice Widget' },
  { value: 'IN1', name: 'IN1 - Service Intercept' },
  { value: 'IN2', name: 'IN2 - Service Intercept + Message Centre' },
  { value: 'IN3', name: 'IN3 - Service Intercept + Message Centre + Service KT' },
  { value: 'IN4', name: 'IN4 - Service Intercept + Message Centre + Service KT' },
  { value: 'M01', name: 'M01 - Message Centre(MB)' },
  { value: 'M03', name: 'M03 - Message Centre(MB) + Marketing Intercept(MB)' },
  { value: 'M06', name: 'M06 - Message Centre(MB) + Message Centre(SOL)' },
  { value: 'M10', name: 'M10 - Message Centre(MB) + Service Intercept(MB) + Message Centre(SOL) + Service KT(SOL)' },
  { value: 'M14', name: 'M14 - Message Centre(MB) + Marketing Intercept(MB) + Message Centre(SOL) + Marketing Intercept(SOL)' },
  { value: 'M15', name: 'M15 - Message Centre(MB) + Marketing Intercept(MB) + Service KT(MB) + Message Centre(SOL) + Marketing Intercept(SOL) + Service KT(SOL)' },
  { value: 'MK1', name: 'MK1 - Marketing Intercept' },
  { value: 'MK2', name: 'MK2 - Marketing Intercept + Message Centre' },
  { value: 'MK3', name: 'MK3 - Marketing Intercept + Message Centre + Service KT' },
  { value: 'PAL', name: 'PAL - Pre-approved leads' },
  { value: 'SDD', name: 'SDD - Day-to-Day Opportunity' },
  { value: 'SER', name: 'SER' },
  { value: 'SLS', name: 'SLS - Message Centre Only' },
  { value: 'SLY', name: 'SLY' },
  { value: 'SNC', name: 'SNC - E-statement Notification in SOL Widget' },
  { value: 'SNV', name: 'SNV - Non pre-approved Visa Opportunity' },
  { value: 'SPV', name: 'SPV - Pre-approved Visa Opportunity' },
  { value: 'VDD', name: 'VDD - Day-to-Day Opportunity' },
  { value: 'VEM', name: 'VEM - Bank the Rest' },
];

// label is used for filters in lists, name is used for dropdowns
export const MESSAGE_PRIORITY_OPTIONS = [
  { value: 'U', name: 'U - Urgent', label: 'Urgent' },
  { value: 'N', name: 'N - Normal', label: 'Normal' },
  {
    value: 'X',
    name: 'X - Urgent, intercept not dismissible',
    label: 'Urgent, intercept not dismissible',
  },
];

export const MESSAGE_STATUS_OPTIONS = [
  { value: 'N', name: 'N - New', label: 'New' },
  { value: 'S', name: 'S - Suspended', label: 'Suspended' },
  { value: 'D', name: 'D - Deleted', label: 'Delete' },
];

export const CAMPAIGN_STATUS_OPTIONS = [
  { value: 'N', name: 'N - New', label: 'New' },
  { value: 'S', name: 'S - Suspended', label: 'Suspended' },
  { value: 'E', name: 'E - Expired', label: 'Expired' },
];

export const CAMPAIGN_TYPE_OPTIONS = [
  { id: 'ABM', value: 'ABM', name: 'ABM', label: 'ABM' },
  { id: 'FFT', value: 'FFT', name: 'FFT', label: 'FFT' },
  { id: 'SOL', value: 'SOL', name: 'SOL', label: 'SOL' },
  { id: 'MOB', value: 'MOB', name: 'MOB', label: 'MOB' },
  { id: '000', value: '000', name: '000', label: '000' },
];

export const CAMPAIGN_CHANNEL_OPTIONS = [
  { id: 'ABM', value: 'ABM', name: 'ABM', label: 'ABM' },
  { id: 'MOB', value: 'MOB', name: 'MOB (Mobile)', label: 'MOB (Mobile)' },
  { id: 'SOL', value: 'SOL', name: 'SOL (Scotia Online)', label: 'SOL (Scotia Online)' },
  { id: 'BRN', value: 'BRN', name: 'BRN (SPOT - Branch)', label: 'BRN (SPOT - Branch)' },
  { id: 'SBD', value: 'SBD', name: 'SBD (PAL - Branch)', label: 'SBD (PAL - Branch)' },
  { id: 'IBD', value: 'IBD', name: 'IBD (inbound)', label: 'IBD (inbound)' },
  { id: 'OBD', value: 'OBD', name: 'OBD (outbound)', label: 'OBD (outbound)' },
];

const URL_PARAMETERS_OPTIONS = [
  { label: 'Yes', value: 'Yes' },
  { label: 'No', value: 'No' },
];

export const URL_PARAMETERS = [
  {
    name: 'customer_name',
    label: 'Customer Name',
    options: URL_PARAMETERS_OPTIONS,
  },
  {
    name: 'account_number',
    label: 'Account Number',
    options: URL_PARAMETERS_OPTIONS,
  },
  {
    name: 'credit_limit',
    label: 'Credit Limit',
    options: URL_PARAMETERS_OPTIONS,
  },
  {
    name: 'product_name',
    label: 'Product Name',
    options: URL_PARAMETERS_OPTIONS,
  },
  {
    name: 'other',
    label: 'Other',
    options: URL_PARAMETERS_OPTIONS,
  },
];

export const LANGUAGES = [
  {
    label: 'Languages',
    name: 'language',
    options: [
      { label: 'English', value: 'E', name: 'language' },
      { label: 'French', value: 'F', name: 'language' },
    ],
  },
];

export const CAMPAIGN_STATUS_MAPPING = {
  N: 'new',
  S: 'suspended',
  D: 'deleted',
  E: 'expired',
};

export const MESSAGE_STATUS_MAPPING = {
  N: 'new',
  S: 'suspended',
  D: 'deleted',
};

export const LANGUAGE_MAPPING = {
  E: 'English',
  F: 'French',
};

export const MESSAGE_PRIORITY_MAPPING = {
  U: 'Urgent',
  N: 'Normal',
  X: 'Urgent (Intercept not dimissible)',
};

export const URL_PARAMETERS_DISPLAY_NAMES = {
  NAME: 'NAME',
  ACCT: 'ACCT',
  LIMIT: 'LIMIT',
  PRODUCT: 'PRODUCT',
  OTHER: 'OTHER',
};

export const MESSAGE_CENTRE_CAMPAIGN_FIELDS = {
  name: '',
  long_name: '',
  campaign_type: '',
  campaign_channel: '',
  kt_campaign_id: '',
  start_date: '',
  end_date: '',
  duration: '',
  language: '',
  msg_category: '',
  msg_priority: '',
  msg_status: '',
  msg_text: '',
  customer_name: 'No',
  product_name: 'No',
  account_number: 'No',
  credit_limit: 'No',
  other: 'No',
  static_url_ind: '',
  static_url_text: '',
};

// Date time error message
export const INVALID_DATE_ERROR_MSG = 'You must enter a valid date (MM/DD/YYYY)';
export const INVALID_DATE_COMPARISON_ERROR_MSG = 'You must enter a publication End date later than the Start date.';
