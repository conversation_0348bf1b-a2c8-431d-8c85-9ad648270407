/* eslint-disable camelcase */
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { change } from 'redux-form';
import PropTypes from 'prop-types';
import TextBody from 'canvas-core-react/lib/TextBody';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import TextButton from 'canvas-core-react/lib/TextButton';
import IconDelete from 'canvas-core-react/lib/IconDelete';

import { VersionTargetingModal } from '../modal/versionTargetingModal';
import { targetingCriteriaOptions, versionTypeOptions } from './constants';

export const SavedVersions = ({ platform, editingMode, formName }) => {
  const dispatch = useDispatch();
  const targetingState = useSelector(state => state.form[formName].values.platforms_targeting);
  const platformIndex = targetingState.findIndex(x => x.platform === platform);
  const items = platformIndex !== -1 && targetingState[platformIndex].items;

  const deleteRow = (e, index) => {
    e.preventDefault();
    const targetingStateCopy = [ ...targetingState ];
    targetingStateCopy[platformIndex].items.splice(index, 1);
    dispatch(change(formName, 'platforms_targeting', targetingStateCopy));
  };

  const renderVersion = (versionTypeName, value) => (
    <TextSubtitle component="span" type="2">
      { versionTypeName }: <TextBody component="span" type="2">{ value }</TextBody>
    </TextSubtitle>
  );

  const renderButtons = (index) => {
    return (
      <div className="saved-versions__buttons">
        <VersionTargetingModal platform={platform} selected={index} formName={formName} />
        <TextButton
          Icon={IconDelete}
          className="saved-versions__button--delete"
          onClick={e => deleteRow(e, index)}
        >
          Delete
        </TextButton>
      </div>
    );
  };

  const getRow = (item) => (
    Object.keys(item).map((key, index) => {
      const { versionType, targetingCriteria, version, minVersion, maxVersion, model } = item[key];
      const versionTypeName = versionTypeOptions.find(({ id }) => id === versionType).displayName;
      const targetingCriteriaObj = targetingCriteriaOptions.find(({ id }) => id === targetingCriteria);
      const targetingCriteriaSymbol = targetingCriteriaObj ? targetingCriteriaObj.symbol : '';

      const value = targetingCriteria === 'range'
        ? `${minVersion} - ${maxVersion}`
        : `${targetingCriteriaSymbol} ${version || model}`;
      return (
        <div key={index} className="saved-versions__item">
          { renderVersion(versionTypeName, value) }
        </div>
      );
    })
  );

  return (
    <ul className="saved-versions__list">
      { items && items.map((item, index) => {
        const row = getRow(item);
        return <li key={index}>{ row } { editingMode && renderButtons(index) }</li>;
      }) }
    </ul>
  );
};

SavedVersions.propTypes = {
  platform: PropTypes.string.isRequired,
  editingMode: PropTypes.bool.isRequired,
  formName: PropTypes.string.isRequired,
};
