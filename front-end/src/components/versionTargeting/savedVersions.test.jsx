import React from 'react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import { SavedVersions } from '../versionTargeting/savedVersions';

describe('savedVersions', () => {
  const newState = targetingState => ({
    form: {
      campaignDetails: {
        values: {
          platforms_targeting: targetingState,
        },
      },
    },
  });
  const mockStore = configureStore();
  let store;

  const renderSavedVersions = (targetingState = [], platform = 'ios', editingMode = true) => {
    store = mockStore(newState(targetingState));
    const renderedObject = render(
      <Provider store={store}>
        <SavedVersions
          platform={platform}
          editingMode={editingMode}
          formName="campaignDetails"
        />
      </Provider>
    );
    return renderedObject;
  };

  it('Renders items (delete button as proxy) if there are items to display', async() => {
    const { findAllByText } = renderSavedVersions([ {
      platform: 'ios',
      items: [
        [ { versionType: 'app', targetingCriteria: 'greater', version: '1.2.3' },
          { versionType: 'os', targetingCriteria: 'less', version: '10.2.3' } ],
        [ { versionType: 'device', targetingCriteria: 'equal', model: 'iPhone11,2' } ],
      ],
    } ]);
    const deleteButtons = await findAllByText('Delete');
    expect(deleteButtons).toHaveLength(2); // First modal has 2 rows
  });

  it('Does not render delete button if editingMode is false', () => {
    const targetingState = [
      {
        platform: 'ios',
        items: [ [ { versionType: 'device', targetingCriteria: 'equal', model: 'iPhone11,2' } ] ],
      } ];
    const { queryByText } = renderSavedVersions(targetingState, 'ios', false);
    expect(queryByText('Delete')).toBeNull();
  });

  it('displays the right info for ios', async() => {
    const targetingState = [
      {
        platform: 'ios',
        items: [ [ { versionType: 'device', targetingCriteria: 'equal', model: 'iPhone11,2' } ] ],
      } ];
    const { getByText } = renderSavedVersions(targetingState, 'ios');
    expect(getByText('Device model:')).toBeInTheDocument();
    expect(getByText('iPhone11,2')).toBeInTheDocument();
  });

  it('calls the delete function when the delete button is clicked', async() => {
    const e = { preventDefault: jest.fn() };
    const assertDefaultPrevented = jest.fn();
    store = mockStore(newState([ {
      platform: 'ios',
      items: [ [ { versionType: 'app', targetingCriteria: 'greater', version: '1.2.3' } ] ],
    } ]));
    const renderedObject = render(
      <div onClick={assertDefaultPrevented}>
        <Provider store={store}>
          <SavedVersions
            platform='ios'
            editingMode={true}
            formName="campaignDetails"
          />
        </Provider>
      </div>
    );
    const { getAllByRole } = renderedObject;
    const deleteButtons = await getAllByRole('button');
    fireEvent.click(deleteButtons[1], e);
    expect(assertDefaultPrevented).toHaveBeenCalled();
  });
});
