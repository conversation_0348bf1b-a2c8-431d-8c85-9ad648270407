import React from 'react';
import PropTypes from 'prop-types';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import Tooltip from 'canvas-core-react/lib/Tooltip';

const TooltipBase = ({ heading, id, body }) => (
  <Tooltip
    heading={heading}
    id={id}
    closeButtonLabel="Close"
    infoButtonLabel="Tooltip info"
  >
    <TextCaption component="p">{ body }</TextCaption>
  </Tooltip>
);

TooltipBase.propTypes = {
  heading: PropTypes.string.isRequired,
  id: PropTypes.string.isRequired,
  body: PropTypes.string.isRequired,
};

export const appVersionTooltip = TooltipBase({
  heading: 'Version',
  id: 'app-version-tooltip',
  body: 'App versions follow the format of: MAJOR.MINOR.PATCH-BUILD (ex. 12.5.5-1)',
});

export const appMinVersionTooltip = TooltipBase({
  heading: 'Minimum version',
  id: 'app-min-version-tooltip',
  body: 'This app version should be earlier than the latest version of the app and follow the format of: MAJOR.MINOR.PATCH-BUILD (ex: 12.5.5-1)',
});

export const appMaxVersionTooltip = TooltipBase({
  heading: 'Maximum version',
  id: 'app-max-version-tooltip',
  body: 'This app version should be later than the earlier version of the app, and follow the format of: MAJOR.MINOR.PATCH-BUILD (ex: 13.5.5-1)',
});

export const modelTooltipIos = TooltipBase({
  heading: 'Model',
  id: 'model-tooltip-ios',
  body: 'Enter the model identifier for the device you would like to target. For example, "iPhone 12,1".',
});

export const modelTooltipAndroid = TooltipBase({
  heading: 'Model',
  id: 'model-tooltip-android',
  body: 'Enter in the model name for the device you would like to target. For example, "SM-G991W".',
});

export const osVersionTooltip = TooltipBase({
  heading: 'Version',
  id: 'os-version-tooltip',
  body: 'OS versions follow the format of: MAJOR.MINOR.PATCH (ex. 12.5.5)',
});

export const osMinVersionTooltip = TooltipBase({
  heading: 'Minimum version',
  id: 'os-version-tooltip',
  body: 'This OS version should be earlier than the latest OS version and follow the format of: MAJOR.MINOR.PATCH (ex: 12.5.5)',
});

export const osMaxVersionTooltip = TooltipBase({
  heading: 'Maximum version',
  id: 'os-version-tooltip',
  body: 'This OS version should be later than the earlier OS version and follow the format of: MAJOR.MINOR.PATCH (ex: 13.5.5)',
});
