import { cloneDeep } from 'lodash';

export const versionTypeOptions = [
  { id: 'app', name: 'App', displayName: 'App version' },
  { id: 'device', name: 'Device', displayName: 'Device model' },
  { id: 'os', name: 'OS', displayName: 'OS version' },
];

export const deviceModelOptions = {
  ios: [
    { id: 'iphone8', name: 'iPhone 8' },
    { id: 'iphone10', name: 'iPhone 10' },
    { id: 'iphone11', name: 'iPhone 11' },
    { id: 'iphone12', name: 'iPhone 12' },
    { id: 'iphone13', name: 'iPhone 13' },
    { id: 'other', name: 'Other' },
  ],
  android: [
    { id: 'pixel5', name: 'Pixel 5' },
    { id: 'pixel4', name: 'Pixel 4' },
    { id: 'pixel3', name: 'Pixel 3' },
    { id: 'pixel2', name: 'Pixel 2' },
    { id: 'pixel', name: 'Pixel' },
    { id: 'nexusS', name: 'Nexus S' },
    { id: 'nexusOne', name: 'Nexus One' },
    { id: 'galaxyNexus', name: 'Galaxy Nexus' },
    { id: 'other', name: 'Other' },
  ],
};

export const targetingCriteriaOptions = [
  { id: 'equal', name: 'Equal (=)', symbol: '', sortOrder: 1 },
  { id: 'less', name: 'Less than (<)', symbol: '<', sortOrder: 2 },
  { id: 'lessEqual', name: 'Less than or equal to (<=)', symbol: '<=', sortOrder: 3 },
  { id: 'greater', name: 'Greater than (>)', symbol: '>', sortOrder: 4 },
  { id: 'greaterEqual', name: 'Greater than or equal to (>=)', symbol: '>=', sortOrder: 5 },
  { id: 'range', name: 'Range between', symbol: '', sortOrder: 6 },
];

export const osVersionPattern = /^(0|[1-9]\d*)(\.(0|[1-9]\d*)){0,2}$/;

// Convert FE input field structure to DB semver structure
export const convertToSemver = (platformTargetingUIValues) => {
  const platformTargetingClone = cloneDeep(platformTargetingUIValues);
  platformTargetingClone.map(platform => {
    platform.items = platform.items.map(detailsRow => {
      const modalObj = {};
      detailsRow.map(modalRow => {
        if (modalRow.versionType === 'device') {
          modalObj.device_model = modalRow.model;
        } else if (modalRow.versionType === 'app') {
          if (modalRow.targetingCriteria === 'range') {
            modalObj.app_version = `${modalRow.minVersion} - ${modalRow.maxVersion}`;
          } else {
            modalObj.app_version = `${targetingCriteriaOptions.find(({ id }) => id === modalRow.targetingCriteria).symbol}${modalRow.version}`;
          }
        } else if (modalRow.versionType === 'os') {
          if (modalRow.targetingCriteria === 'range') {
            modalObj.os_version = `${modalRow.minVersion} - ${modalRow.maxVersion}`;
          } else {
            modalObj.os_version = `${targetingCriteriaOptions.find(({ id }) => id === modalRow.targetingCriteria).symbol}${modalRow.version}`;
          }
        }
      });
      return modalObj;
    });
    return platform;
  });
  return platformTargetingClone;
};

// Convert DB semver structure to FE input field structure
export const convertFromSemver = (platformTargetingDBValues) => {
  const platformTargetingClone = cloneDeep(platformTargetingDBValues);
  platformTargetingClone.map(platform => {
    platform.items = platform.items.map(item => {
      const modalRows = [];
      if (item.app_version) {
        const modalObj = { versionType: 'app' };
        const arr = item.app_version.split(/([0-9]+)/);
        const op = arr[0];
        arr.shift();
        const version = arr.join('').split(' - ');
        if (version.length > 1) {
          modalObj.targetingCriteria = 'range';
          modalObj.minVersion = version[0];
          modalObj.maxVersion = version[1];
        } else {
          const tcOption = targetingCriteriaOptions.find(({ symbol }) => symbol === op);
          modalObj.targetingCriteria = op && tcOption ? tcOption.id : 'equal';
          modalObj.version = version[0] || op;
        }
        modalRows.push(modalObj);
      }

      if (item.os_version) {
        const modalObj = { versionType: 'os' };
        const arr = item.os_version.split(/([0-9]+)/);
        const op = arr[0];
        arr.shift();
        const version = arr.join('').split(' - ');
        if (version.length > 1) {
          modalObj.targetingCriteria = 'range';
          modalObj.minVersion = version[0];
          modalObj.maxVersion = version[1];
        } else {
          const tcOption = targetingCriteriaOptions.find(({ symbol }) => symbol === op);
          modalObj.targetingCriteria = op && tcOption ? tcOption.id : 'equal';
          modalObj.version = version[0] || op;
        }
        modalRows.push(modalObj);
      }

      if (item.device_model) {
        const modalObj = {
          versionType: 'device',
          targetingCriteria: 'equal',
        };
        modalObj.model = item.device_model;
        modalRows.push(modalObj);
      }
      return modalRows;
    });
    return platform;
  });
  return platformTargetingClone;
};
