import {
  convertFromSemver,
  convertToSemver,
} from './constants';

const databaseValues = [
  {
    items: [
      {
        app_version: '1.0.0 - 2.2.0-alpha',
        os_version: '1 - 1.0.3',
        device_model: 'iPhone11,2',
      }, {
        app_version: '<=32.0.0',
        os_version: '4.3',
      },
    ],
    platform: 'ios',
    v: 1,
  },
  {
    items: [],
    platform: 'android',
    v: 1,
  },
];

const uiValues = [
  {
    items: [
      [ {
        maxVersion: '2.2.0-alpha',
        minVersion: '1.0.0',
        targetingCriteria: 'range',
        versionType: 'app',
      }, {
        maxVersion: '1.0.3',
        minVersion: '1',
        targetingCriteria: 'range',
        versionType: 'os',
      }, {
        targetingCriteria: 'equal',
        model: 'iPhone11,2',
        versionType: 'device',
      } ],
      [ {
        targetingCriteria: 'lessEqual',
        version: '32.0.0',
        versionType: 'app',
      }, {
        targetingCriteria: 'equal',
        version: '4.3',
        versionType: 'os',
      } ],
    ],
    platform: 'ios',
    v: 1,
  },
  {
    items: [],
    platform: 'android',
    v: 1,
  },
];

describe('Version Targeting Constants & Helpers', () => {
  it('should convert from DB semver structure to UI redux form structure', () => {
    expect(uiValues).toStrictEqual(convertFromSemver(databaseValues));
  });

  it('should convert from UI redux form structure to DB semver structure', () => {
    expect(databaseValues).toStrictEqual(convertToSemver(uiValues));
  });
});
