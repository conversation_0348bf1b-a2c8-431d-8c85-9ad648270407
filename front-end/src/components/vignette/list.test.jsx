import React from 'react';
import { createMemoryHistory } from 'history';
import { shallow } from 'enzyme';

import List from './list';
import { ListContainer } from './listContainer';
import { STATUS, ruleTypes } from '../../constants';
import PillButton from 'canvas-core-react/lib/PillButton';

const rules = {
  total: 4,
  offset: 0,
  limit: 50,
  items: [
    {
      id: 'PtD8ZEPFpLcj',
      name: 'Sample rule #1',
      start_date: '2018-07-13T00:00:00.000Z',
      end_date: '2038-07-30T23:59:59.000Z',
      updated_at: '2018-07-13T00:00:00.000Z',
      content_space: null,
      content_type: null,
      content_id: null,
      container: null,
      platforms: [],
      app_version: null,
      status: 'draft',
      disabled: false,
      created_by: null,
      updated_by: null,
      targeting: { campaign_name: 'hiyo' },
    },
    {
      id: 'jhrotTqfCeiN',
      name: 'Sample rule #2',
      start_date: '2018-07-15T00:00:00.000Z',
      end_date: '2018-08-30T23:59:59.000Z',
      updated_at: '2018-07-15T00:00:00.000Z',
      content_space: null,
      content_type: null,
      content_id: null,
      container: null,
      platforms: [],
      app_version: null,
      status: 'draft',
      disabled: true,
      created_by: null,
      updated_by: null,
      targeting: { campaign_name: 'hiyo' },
    },
    {
      id: 'uVEPJeHe0hTr',
      name: 'Sample rule #3',
      start_date: '2018-08-01T00:00:00.000Z',
      updated_at: '2018-08-01T00:00:00.000Z',
      end_date: '2018-08-30T23:59:59.000Z',
      content_space: null,
      content_type: null,
      content_id: null,
      container: null,
      platforms: [],
      app_version: null,
      status: 'inactive',
      disabled: false,
      created_by: null,
      updated_by: null,
      targeting: { campaign_name: 'hiyo' },
    },
    {
      id: 'LFPvqZR0EDZ2',
      name: 'Sample rule #4',
      start_date: '2018-08-01T00:00:00.000Z',
      updated_at: '2018-08-01T00:00:00.000Z',
      end_date: '2018-08-30T23:59:59.000Z',
      content_space: null,
      content_type: null,
      content_id: null,
      container: null,
      platforms: [],
      app_version: null,
      status: 'published',
      disabled: false,
      created_by: null,
      updated_by: null,
      targeting: { campaign_name: 'hiyo' },
    },
  ],
};
const access = {
  containers: {
    sol: { view: [ 'LoginSecurityCentre' ], manage: [ 'LoginSecurityCentre' ] },
    storefront: { view: [ 'sf-marquee-bankingaccounts' ], manage: [ 'sf-marquee-bankingaccounts' ] },
  },
  pages: {
    sol: { view: [ 'Login' ], manage: [ 'Login' ] },
    storefront: { view: [ 'sf-accessibility' ], manage: [ 'sf-accessibility' ] },
  },
  ruleSubTypes: {
    sol: { view: [ 'targeted', 'mass' ], manage: [ 'targeted', 'mass' ] },
    storefront: { view: [ 'targeted', 'mass' ], manage: [ 'targeted', 'mass' ] },
  },
};

describe('List', () => {
  const createClicked = jest.fn();
  const sortClicked = jest.fn();
  const mockLocation = {
    pathname: '/campaigns/sol',
  };
  const wrapper = shallow(
    <List
      listings={{ data: rules }}
      users={{
        items: {
          1: { sid: 's1234567', name: 'First Last' },
          2: { sid: 's7654321', name: 'A B' },
        },
      }}
      createClicked={createClicked}
      pageClicked={jest.fn()}
      statusClicked={jest.fn()}
      searchClicked={jest.fn()}
      createdByClicked={jest.fn()}
      updatedByClicked={jest.fn()}
      clearAllClicked={jest.fn()}
      sortClicked={sortClicked}
      nameUpdated={jest.fn()}
      type={ruleTypes.VIGNETTE}
      access={access}
      permissions={{ campaigns_manage: true }}
      query={{}}
      location={mockLocation}
    />
  );

  global.snapshot(wrapper);

  it('Create button clicked', () => {
    wrapper.find(PillButton).first().simulate('click');
    expect(createClicked).toHaveBeenCalledTimes(1);
  });

  it('Expand search', () => {
    wrapper.setState({ searchCollapsed: false });
    expect(wrapper).toMatchSnapshot();
  });

  it('Loading snapshot', () => {
    const wrapperLoading = shallow(
      <List
        listings={{ isLoading: true }}
        createClicked={createClicked}
        pageClicked={jest.fn()}
        statusClicked={jest.fn()}
        searchClicked={jest.fn()}
        sortClicked={sortClicked}
        nameUpdated={jest.fn()}
        initialPage={0}
        type={ruleTypes.SOL}
        access={{
          containers: {},
          pages: {},
          ruleSubTypes: {},
        }}
        permissions={{}}
        query={{}}
        createdByClicked={jest.fn()}
        updatedByClicked={jest.fn()}
        clearAllClicked={jest.fn()}
        users={{
          items: {
            1: { sid: 's1234567', name: 'First Last' },
            2: { sid: 's7654321', name: 'A B' },
          },
        }}
        location={mockLocation}
      />
    );
    expect(wrapperLoading).toMatchSnapshot();
  });
});

describe('List Container', () => {
  const history = createMemoryHistory();
  const getListings = jest.fn();
  const getUsers = jest.fn();
  const wrapper = shallow(
    <ListContainer
      listings={{ data: rules }}
      getListings={getListings}
      getUsers={getUsers}
      history={history}
      location={{ search: '' }}
      match={{ params: { type: 'sol' } }}
      type={ruleTypes.SOL}
      users={{
        items: {
          1: { sid: 's1234567', name: 'First Last' },
          2: { sid: 's7654321', name: 'A B' },
        },
      }}
      access={{
        campaigns: access,
      }}
      permissions={{ campaigns_view: true, campaigns_manage: true }}
    />
  );
  const instance = wrapper.instance();

  global.snapshot(wrapper);

  it('getListings to be called once', () => {
    expect(getListings).toHaveBeenCalledTimes(1);
  });

  it('getUsers to be called once', () => {
    expect(getUsers).toHaveBeenCalledTimes(1);
  });

  it('createClicked', () => {
    instance.createClicked();
    expect(history.location.pathname).toStrictEqual('/campaigns/sol/create');
  });

  it('pageClicked', () => {
    instance.pageClicked({ selected: 0 });
    instance.pageClicked({ selected: 1 });
  });

  it('statusClicked', () => {
    instance.statusClicked({ target: { value: STATUS.DRAFT } });
    instance.statusClicked({ target: { value: STATUS.PUBLISHED } });
  });

  it('sortClicked', () => {
    instance.sortClicked('name');
    instance.sortClicked('name', true);
  });

  it('No Access Snapshot', () => {
    const noAccessWrapper = shallow(
      <ListContainer
        listings={{ data: rules }}
        getListings={getListings}
        getUsers={getUsers}
        history={history}
        location={{ search: '' }}
        match={{ params: { type: 'sol' } }}
        type={ruleTypes.SOL}
        users={{
          items: {
            1: { sid: 's1234567', name: 'First Last' },
            2: { sid: 's7654321', name: 'A B' },
          },
        }}
        access={{
          campaigns: {},
        }}
        permissions={{ campaigns_view: false, campaigns_manage: false }}
      />
    );
    expect(noAccessWrapper).toMatchSnapshot();
  });
});
