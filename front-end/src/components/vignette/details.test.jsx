import React from 'react';
import moment from 'moment';
import { shallow } from 'enzyme';

import Details from './details';
import { DetailsContainer, validate } from './detailsContainer';
import { STATUS, ruleTypes } from '../../constants';

const testPages = [
   { id: 1, pageId: 'Login', name: 'Login', application: 1 },
   { id: 2, pageId: 'Logout', name: 'Logout', application: 1 },
];
const testContainers = {
  LoginSecurityCentre: { id: 1, containerId: 'LoginSecurityCentre', name: 'LoginSecurityCentre', pages: [ 1 ], rule_type: 'vignette' },
};

describe('Details Snapshot', () => {
  // Base props - new campaign
  let baseProps = {
    id: undefined,
    status: undefined,
    title: 'title',
    formValues: {},
    onSubmit: jest.fn(),
    onApprove: jest.fn(),
    onPublish: jest.fn(),
    onActivate: jest.fn(),
    onCancel: jest.fn(),
    onDuplicate: jest.fn(),
    onDelete: jest.fn(),
    deleteContent: jest.fn(),
    openFragmentModal: jest.fn(),
    updateDetails: jest.fn(),
    onTerminate: jest.fn(),
    formChange: jest.fn(),
    type: 'vignette',
    access: {
      containers: {
        sol: { view: [ 'LoginSecurityCentre' ], manage: [ 'LoginSecurityCentre' ] },
        storefront: { view: [ 'sf-marquee-bankingaccounts' ], manage: [ 'sf-marquee-bankingaccounts' ] },
      },
      pages: {
        sol: { view: [ 'Login' ], manage: [ 'Login' ] },
        storefront: { view: [ 'sf-accessibility' ], manage: [ 'sf-accessibility' ] },
      },
      ruleSubTypes: {
        sol: { view: [ 'targeted', 'mass' ], manage: [ 'targeted', 'mass' ] },
        storefront: { view: [ 'targeted', 'mass' ], manage: [ 'targeted', 'mass' ] },
      },
    },
    permissions: { 'campaigns_manage': true },
    containers: testContainers,
    metadata: { device: [] },
    pages: testPages,
    downloadXML: jest.fn(),
  };

  const wrapper = shallow(
    <Details {...baseProps} />
  );
  global.snapshot(wrapper);

  it('snap no status', () => {
    const wrapper = shallow(
      <Details {...baseProps}/>
    );
    expect(wrapper).toMatchSnapshot();
  });

  it('snap status published', () => {
    const props = {
      ...baseProps,
      status: STATUS.PUBLISHED,
    };
    const wrapper = shallow(
      <Details {...props}/>
    );
    expect(wrapper).toMatchSnapshot();
  });

  it('status status submitted', () => {
    const props = {
      ...baseProps,
      status: STATUS.SUBMITTED,
    };
    const wrapper = shallow(
      <Details {...props}/>
    );
    expect(wrapper).toMatchSnapshot();
  });

  it('snap status inactive', () => {
    const props = {
      ...baseProps,
      status: STATUS.INACTIVE,
    };
    const wrapper = shallow(
      <Details {...props}/>
    );
    expect(wrapper).toMatchSnapshot();
  });

  it('snap with status draft', () => {
    const props = {
      ...baseProps,
      status: STATUS.DRAFT,
      formValues: {
        ...baseProps.formValues,
        id: '12345',
        container: 'LoginSecurityCentre',
        pages: [ 'Login' ],
      },
    };
    const wrapper = shallow(
      <Details {...props}/>
    );
    expect(wrapper).toMatchSnapshot();
  });

  it('insufficient permissions banner', () => {
    const props = {
      ...baseProps,
      access: {
        containers: {},
        pages: {},
        ruleSubTypes: {},
      },
    };
    const wrapper = shallow(
      <Details {...props}/>
    );
    expect(wrapper).toMatchSnapshot();
  });

  it('snap with targeted targeting', () => {
    const props = {
      ...baseProps,
      status: STATUS.PUBLISHED,
      formValues: {
        ...baseProps.formValues,
        id: '12345',
        type: 'targeted',
      },
    };
    const wrapper = shallow(
      <Details {...props}/>
    );
    expect(wrapper).toMatchSnapshot();
  });

  it('snap with mass targeting', () => {
    const props = {
      ...baseProps,
      status: STATUS.PUBLISHED,
      formValues: {
        ...baseProps.formValues,
        id: '12345',
        type: 'mass',
      },
    };
    const wrapper = shallow(
      <Details {...props}/>
    );
    expect(wrapper).toMatchSnapshot();
  });

  it('snap with estore (storefront)', () => {
    const props = {
      ...baseProps,
      type: 'estore',
    };
    const wrapper = shallow(
      <Details {...props}/>
    );
    expect(wrapper).toMatchSnapshot();
  });

  it('snap with download button', () => {
    const props = {
      ...baseProps,
      status: STATUS.PUBLISHED,
      type: 'estore',
      formValues: {
        ...baseProps.formValues,
        end_date: '2028-01-01T04:59:00.000Z',
      },
    };
    const wrapper = shallow(
      <Details {...props}/>
    );
    expect(wrapper).toMatchSnapshot();
  });
});

describe('DetailsContainer Snapshot', () => {
  const props = {
    formValues: {},
    formChange: jest.fn(),
    match: {
      params: {
        id: '123',
        action: '345',
      },
    },
    history: {
      goBack: jest.fn(),
      push: jest.fn(),
      replace: jest.fn(),
      listen: jest.fn(),
    },
    location: {
      pathname: '/',
    },
    reset: jest.fn(),
    handleSubmit: jest.fn(),
    populateDetails: jest.fn(),
    getContainers: jest.fn(),
    getPages: jest.fn(),
    updateDetails: jest.fn(),
    openFragmentModalAndLoadFragments: jest.fn(),
    addSnackbar: jest.fn(),
    type: ruleTypes.VIGNETTE,
    access: {
      ruleSubTypes: {
        sol: { view: [ 'targeted', 'mass' ], manage: [ 'targeted', 'mass' ] },
        storefront: { view: [ 'targeted', 'mass' ], manage: [ 'targeted', 'mass' ] },
      },
    },
    permissions: {},
    metadata: {},
    containers: {
      container_id: {
        rule_type: 'vignette',
      },
      isLoading: false,
    },
  };

  const wrapper = shallow(<DetailsContainer {...props} getWebFragmentMetadata= {jest.fn()}/>);
  const instance = wrapper.instance();
  global.snapshot(wrapper);

  it('All Errors', () => {
    expect(validate({}, props)).toMatchSnapshot();
  });

  it('Start/end date validation', () => {
    const values = {
      start_date: '2018-07-13T00:00:00.000Z',
      end_date: '2018-07-12T00:00:00.000Z',
    };
    expect(moment(values.end_date).isBefore(values.start_date)).toBe(true);
    expect(validate(values, props).end_date).toBeTruthy();
  });

  it('No Errors', () => {
    const values = {
      type: 'mass',
      start_date: '2018-07-13T00:00:00.000Z',
      end_date: '2018-07-15T00:00:00.000Z',
      language: 'en',
      name: 'Mike Test',
      urgent: true,
      container: 'container_id',
      pages: [
        'activities',
      ],
      subject_line: 'Hey there',
      BU: [
        'B:',
        'CASL:',
      ],
      BU_NOT: [],
      REGS: [
        'B',
        'I',
      ],
      REGS_NOT: [],
      PR1: [
        'Banking:DDA:DDA:DDA:',
        'Banking:Savings:SAV:SAV:CS',
      ],
      PR2: [],
      PR3: [],
      PR4: [],
      PR1_NOT: [],
      PR2_NOT: [],
      PR3_NOT: [],
      PR4_NOT: [],
      PROV: [
        'AB',
      ],
      BU_RADIO: 'BU_AND',
      REGS_RADIO: 'REGS_AND',
      PR_RADIO: 'PR_AND',
      content_id: '7777',
    };
    expect(instance.formatValues(values)).toMatchSnapshot();
    expect(validate(values, props)).toStrictEqual({});
  });
  it('No Errors for DEVC', () => {
    const values = {
      type: 'mass',
      urgent: true,
      container: 'container_id',
      pages: [ 'activities' ],
      DEVC: [ 'Android' ],
      DEVC_RADIO: 'DEVC_AND',
    };
    const payload = instance.formatValues(values);
    expect(payload.targeting.products).toStrictEqual([ {
      attribute_mode: 'and',
      attribute_type: 'DEVC',
      attribute_values: values.DEVC,
    } ]);
  });

  it('No Errors for device exclude DEVC_NOT', () => {
    const values = {
      type: 'mass',
      start_date: '2018-07-13T00:00:00.000Z',
      end_date: '2018-07-15T00:00:00.000Z',
      language: 'en',
      name: 'Mike Test',
      urgent: true,
      container: 'container_id',
      pages: [ 'activities' ],
      subject_line: 'Hey there',
      BU: [ 'B:', 'CASL:' ],
      BU_NOT: [],
      REGS: [ 'B', 'I' ],
      REGS_NOT: [],
      PR1: [ 'Banking:DDA:DDA:DDA:', 'Banking:Savings:SAV:SAV:CS' ],
      PR2: [],
      PR3: [],
      PR4: [],
      PR1_NOT: [],
      PR2_NOT: [],
      PR3_NOT: [],
      PR4_NOT: [],
      DEVC_NOT: [ 'and' ],
      BU_RADIO: 'BU_AND',
      REGS_RADIO: 'REGS_AND',
      PR_RADIO: 'PR_AND',
      content_id: '7777',
    };
    expect(instance.formatValues(values)).toMatchSnapshot();
    expect(validate(values, props)).toStrictEqual({});
  });

  it('getStatus duplicate should return null', () => {
    const localProps = {
      ...props,
      match: {
        params: {
          id: '123',
          action: 'duplicate',
        },
      },
    };
    const wrapper = shallow(
      <DetailsContainer {...localProps} getWebFragmentMetadata={jest.fn()} />
    );
    const instance = wrapper.instance();
    expect(instance.status).toBeNull();
  });

  it('getStatus duplicate should return inactive if formValues is disabled', () => {
    const localProps = {
      ...props,
      formValues: {
        disabled: true,
      },
    };
    const wrapper = shallow(
      <DetailsContainer {...localProps} getWebFragmentMetadata={jest.fn()} />
    );
    const instance = wrapper.instance();
    expect(instance.status).toBe('inactive');
  });

  it('should format with VIGNETTE_BROADCAST U', () => {
    const localProps = {
      ...props,
      containers: {
        my_container: { rule_type: 'vignette-broadcast' },
      },
    };
    const wrapper = shallow(
      <DetailsContainer {...localProps} getWebFragmentMetadata={jest.fn()} />
    );
    const instance = wrapper.instance();
    const values = {
      container: 'my_container',
      pages: [],
      urgent: true,
    };
    const payload = instance.formatValues(values);
    expect(payload.message_type).toBe('U');
  });

  it('should format with VIGNETTE_BROADCAST N', () => {
    const localProps = {
      ...props,
      containers: {
        my_container: { rule_type: 'vignette-broadcast' },
      },
    };
    const wrapper = shallow(
      <DetailsContainer {...localProps} getWebFragmentMetadata={jest.fn()} />
    );
    const instance = wrapper.instance();
    const values = {
      container: 'my_container',
      pages: [],
      urgent: false,
    };
    const payload = instance.formatValues(values);
    expect(payload.message_type).toBe('N');
  });

  it('should format with VIGNETTE_PRIORITY P', () => {
    const localProps = {
      ...props,
      containers: {
        my_container: { rule_type: 'vignette-priority' },
      },
    };
    const wrapper = shallow(
      <DetailsContainer {...localProps} getWebFragmentMetadata={jest.fn()} />
    );
    const instance = wrapper.instance();
    const values = {
      container: 'my_container',
      pages: [],
      urgent: true,
    };
    const payload = instance.formatValues(values);
    expect(payload.message_type).toBe('P');
  });

  it('should add subject line to contents', () => {
    const localProps = {
      ...props,
      containers: {
        my_container: { rule_type: 'vignette-priority' },
      },
    };
    const wrapper = shallow(
      <DetailsContainer {...localProps} getWebFragmentMetadata={jest.fn()} />
    );
    const instance = wrapper.instance();
    const values = {
      container: 'my_container',
      content_id: 'content_id',
      subject_line: 'important ',
      urgent: true,
      contents: [],
      pages: [ 'activities' ],
    };
    const payload = instance.formatValues(values);
    expect(payload.contents).toStrictEqual(
      expect.arrayContaining([
        {
          container: 'my_container',
          content_id: 'content_id',
          content_space: 'db',
          content_type: 'webfragment',
          page: 'activities',
          subject: 'important',
        },
      ])
    );
  });

  it('should add campaign name if type is targeted', () => {
    const wrapper = shallow(
      <DetailsContainer {...props} getWebFragmentMetadata={jest.fn()} />
    );
    const instance = wrapper.instance();
    const values = {
      type: 'targeted',
      campaign_name: 'camp aign',
      container: 'container_id',
      pages: [],
    };
    const payload = instance.formatValues(values);
    expect(payload.targeting.campaign_name).toStrictEqual('CAMP AIGN');
  });

  it('should return mass empty for array payload.targeting.products', () => {
    const wrapper = shallow(
      <DetailsContainer {...props} getWebFragmentMetadata={jest.fn()} />
    );
    const instance = wrapper.instance();
    const values = {
      type: 'mass',
      campaign_name: 'camp aign',
      container: 'container_id',
      pages: [],
    };
    const payload = instance.formatValues(values);
    expect(payload.targeting.products).toStrictEqual([]);
  });

  it('should return  empty for array payload.targeting.products for unknown type', () => {
    const wrapper = shallow(
      <DetailsContainer {...props} getWebFragmentMetadata={jest.fn()} />
    );
    const instance = wrapper.instance();
    const values = {
      type: '',
      campaign_name: 'camp aign',
      container: 'container_id',
      pages: [],
    };
    const payload = instance.formatValues(values);
    expect(payload.targeting.products).toStrictEqual([]);
  });

  it('should return  values for Business lines BU_NOT', () => {
    const wrapper = shallow(
      <DetailsContainer {...props} getWebFragmentMetadata={jest.fn()} />
    );
    const instance = wrapper.instance();
    const values = {
      type: 'mass',
      campaign_name: 'camp aign',
      container: 'container_id',
      pages: [],
      BU_NOT: [ 'HW:' ],
    };
    const payload = instance.formatValues(values);
    expect(payload.targeting.products).toStrictEqual([
      {
        attribute_mode: 'not',
        attribute_type: 'BU',
        attribute_values: [ 'HW:' ],
      },
    ]);
  });

  it('should return  values for Registration Status RU_NOT', () => {
    const wrapper = shallow(
      <DetailsContainer {...props} getWebFragmentMetadata={jest.fn()} />
    );
    const instance = wrapper.instance();
    const values = {
      type: 'mass',
      campaign_name: 'campaign',
      container: 'container_id',
      pages: [],
      REGS_NOT: [ 'MOB' ],
    };
    const payload = instance.formatValues(values);
    expect(payload.targeting.products).toStrictEqual([
      {
        attribute_mode: 'not',
        attribute_type: 'REGS',
        attribute_values: [ 'MOB' ],
      },
    ]);
  });

  it('should return  values for Advaned Targetting PR1_NOT', () => {
    const wrapper = shallow(
      <DetailsContainer {...props} getWebFragmentMetadata={jest.fn()} />
    );
    const instance = wrapper.instance();
    const values = {
      type: 'mass',
      campaign_name: 'campaign',
      container: 'container_id',
      pages: [],
      PR1_NOT: [ 'Banking:Savings:SAV:SAV:BP' ],
    };
    const payload = instance.formatValues(values);
    expect(payload.targeting.products).toStrictEqual([
      {
        attribute_mode: 'not',
        attribute_type: 'PR',
        attribute_values: [ 'Banking:Savings:SAV:SAV:BP' ],
      },
    ]);
  });

  it('should return  values for when Relationship Settings radio buttons is OR', () => {
    const wrapper = shallow(
      <DetailsContainer {...props} getWebFragmentMetadata={jest.fn()} />
    );
    const instance = wrapper.instance();
    const values = {
      type: 'mass',
      campaign_name: 'campaign',
      container: 'container_id',
      pages: [],
      BU_RADIO: 'BU_OR',
      REGS_RADIO: 'REGS_OR',
      PR_RADIO: 'PR_OR',
      DEVC_RADIO: 'DEVC_OR',
      BU: [ 'B:', 'CASL:' ],
      REGS: [ 'B', 'I' ],
      PR1: [ 'Banking:Savings:SAV:SAV:BP' ],
      DEVC: [ 'Android' ],
    };
    const payload = instance.formatValues(values);
    expect(payload.targeting.products).toStrictEqual([
      {
        attribute_mode: 'or',
        attribute_type: 'BU',
        attribute_values: values.BU,
      },
      {
        attribute_mode: 'or',
        attribute_type: 'REGS',
        attribute_values: values.REGS,
      },
      {
        attribute_mode: 'or',
        attribute_type: 'PR',
        attribute_values: values.PR1,
      },
      {
        attribute_mode: 'or',
        attribute_type: 'DEVC',
        attribute_values: values.DEVC,
      },
    ]);
  });

  it('Validate should return error if field validation fails', () => {
    const errors = validate(
      {
        type: 'targeted',
        pages: [],
        container: 'container_id',
        subject_line: undefined,
        BU_RADIO: undefined,
        BU: [ 'A' ],
        REGS_RADIO: undefined,
        REGS: [ 'A' ],
        PR_RADIO: undefined,
        PR1: [ 'A' ],
        PR2: [ 'A' ],
        PR3: [ 'A' ],
        PR4: [ 'A' ],
      },
      {
        ...props,
        containers: {
          container_id: {
            rule_type: 'vignette-broadcast',
          },
        },
      }
    );
    expect(errors).toStrictEqual(
      expect.objectContaining({
        campaign_name: 'You must enter a campaign ID.',
        pages: 'You must select at least one page.',
        subject_line: 'You must enter a subject line',
        BU_RADIO: 'You must select AND/OR',
        REGS_RADIO: 'You must select AND/OR',
        PR_RADIO: 'You must select AND/OR',
      })
    );
  });

  it('Validate should return error if AND/OR missing for in Advanced Targeting', () => {
    const propsLocal = {
      ...props,
      containers: {
        container_id: {
          rule_type: 'vignette-broadcast',
        },
      },
    };
    const erros = [ 'PR2', 'PR3', 'PR4' ].map(field => {
      return validate(
        {
          PR_RADIO: undefined,
          [field]: [ 'A' ],
        },
        propsLocal
      );
    });

      erros.forEach(item => expect(item).toStrictEqual(
        expect.objectContaining({
          PR_RADIO: 'You must select AND/OR',
        })
      ));
    });
});
