import React from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import moment from 'moment';
import throttle from 'lodash/throttle';
import Card from 'canvas-core-react/lib/Card';
import PillButton from 'canvas-core-react/lib/PillButton';
import Table from 'canvas-core-react/lib/Table';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import Filter from 'canvas-core-react/lib/Filter';
import IconCloseCircle from 'canvas-core-react/lib/IconCloseCircle';
import Search from 'canvas-core-react/lib/Search';
import TextButton from 'canvas-core-react/lib/TextButton';
import TextIntroduction from 'canvas-core-react/lib/TextIntroduction';
import IconChevronUp from 'canvas-core-react/lib/IconChevronUp';
import IconChevronDown from 'canvas-core-react/lib/IconChevronDown';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';
import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';
import CanvasLink from 'canvas-core-react/lib/Link';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import TextField from 'canvas-core-react/lib/TextField';

import { ruleTypesDisplayName, statusByType, ruleTypes } from '../../constants';
import { capitalize } from '../../utils';
import StatusBadge from '../core/statusBadge';

export default class List extends React.PureComponent {
  static propTypes = {
    listings: PropTypes.shape({
      data: PropTypes.shape({
        items: PropTypes.arrayOf(PropTypes.object),
        total: PropTypes.number,
        limit: PropTypes.number,
        offset: PropTypes.number,
      }),
      isLoading: PropTypes.bool,
    }).isRequired,
    users: PropTypes.shape({
      items: PropTypes.object,
      isLoading: PropTypes.bool,
    }).isRequired,
    createClicked: PropTypes.func.isRequired,
    pageClicked: PropTypes.func.isRequired,
    type: PropTypes.string.isRequired,
    query: PropTypes.object,
    statusClicked: PropTypes.func.isRequired,
    createdByClicked: PropTypes.func.isRequired,
    updatedByClicked: PropTypes.func.isRequired,
    campaignIdFilterChanged: PropTypes.func.isRequired,
    nameUpdated: PropTypes.func.isRequired,
    clearAllClicked: PropTypes.func.isRequired,
    sortClicked: PropTypes.func.isRequired,
    access: PropTypes.object.isRequired,
    permissions: PropTypes.object.isRequired,
    location: PropTypes.object,
  };

  state = { searchCollapsed: this.props.location?.state?.from !== 'message-centre' };
  throttledNameUpdate = throttle(this.props.nameUpdated, 500, { leading: false });

  toggleSearch = () => {
    this.setState({ searchCollapsed: !this.state.searchCollapsed });
  };

  nameUpdated = e => {
    this.throttledNameUpdate(e.target.value);
  };

  sortableColumnProperties = columnKey => {
    const onColumnSort = columnKey => (row, direction) => this.props.sortClicked(columnKey, direction === 1);
    const initialSortDirection = columnKey => {
      const { sort } = this.props.query;
      if (!sort) {
        return 0;
      }

      if (sort.includes(columnKey)) {
        return (sort.charAt(0) === '-') ? 1 : -1;
      }

      return 0;
    };

    return ({
      sortable: true,
      overrideSortBehaviour: onColumnSort(columnKey),
      initialSortDirection: initialSortDirection(columnKey),
    });
  };

  render() {
    const { data, isLoading } = this.props.listings;
    const { type, query, createClicked, access, permissions } = this.props;
    const { searchCollapsed } = this.state;

    const application = type === ruleTypes.ESTORE ? 'storefront' : 'sol';
    const manageAccessContainers = access.containers[application]?.manage || [];
    const manageAccessPages = access.pages[application]?.manage || [];
    const manageAccessRuleSubTypes = access.ruleSubTypes[application]?.manage || [];
    const hasMinimumManageAccess = manageAccessContainers.length && manageAccessPages.length && manageAccessRuleSubTypes.length;

    return (
      <div className="admin-list">
        <div className="admin-list__action-bar">
          <TextIntroduction
            component="h1"
            className="admin-list__header"
          >
            Manage { `${ruleTypesDisplayName[type]}s` }
            { isLoading && <IconSpinner size={24} /> }
          </TextIntroduction>
          { permissions['campaigns_manage'] && !!hasMinimumManageAccess &&
          <PillButton
            type="caution"
            buttonType="button"
            onClick={createClicked}
          >
            Create a new { ruleTypesDisplayName[type] }
          </PillButton>
          }
        </div>
        <Card className="admin-list__filter">
          <div className="admin-list__filter-accordion">
            <div className="admin-list__search">
              <Search
                id="name"
                onChange={this.nameUpdated}
                value={query.name || ''}
                clearButtonLabel="Clear search"
                searchButtonLabel="Search"
                label="Search"
                showLabel={false}
              />
            </div>
            <button
              className="admin-list__accordion-button"
              onClick={this.toggleSearch}
              aria-expanded={!searchCollapsed}
              aria-label={searchCollapsed ? 'Show filtering options ' : 'Hide filtering options'}
              aria-controls="filter-section"
            >
              { searchCollapsed
                ? <IconChevronDown />
                : <IconChevronUp />
              }
            </button>
          </div>
          { !searchCollapsed &&
          <fieldset className="admin-filter admin-list__filter-options">
            <TextSubtitle component="h3">Filters</TextSubtitle>
            <div className="admin-filter__filter-container">
              <div className="admin-filter__item">
                <Filter
                  id="status-filter"
                  label="Status"
                  isLabelVisible
                  name=''
                  placeholder="Status"
                  value={query.status || 'all'}
                  onChange={this.props.statusClicked}>
                  <option value="all">All</option>
                  { statusByType[type].map(status =>
                    <option value={status} key={status}>{ capitalize(status) }</option>
                  ) }
                </Filter>
              </div>
              <div className="admin-filter__item">
                <Filter
                  id="created-by-filter"
                  label="Created by"
                  placeholder="Created by"
                  isLabelVisible
                  value={query.created_by || 'all'}
                  name=''
                  onChange={this.props.createdByClicked}
                >
                  <option value="all">All</option>
                  { Object.values(this.props.users.items || [])
                    .sort((a, b) => a.name.localeCompare(b.name))
                    .map(user =>
                      <option
                        value={user.sid}
                        key={user.sid}
                      >
                        { user.name }
                      </option>,
                    ) }
                </Filter>
              </div>
              <div className="admin-filter__item">
                <Filter
                  id="updated-by-filter"
                  label="Last updated by"
                  placeholder="Last updated by"
                  isLabelVisible
                  name=''
                  value={query.updated_by || 'all'}
                  onChange={this.props.updatedByClicked}
                >
                  <option value="all">All</option>
                  { Object.values(this.props.users.items || [])
                    .sort((a, b) => a.name.localeCompare(b.name))
                    .map(user =>
                      <option
                        value={user.sid}
                        key={user.sid}
                      >{ user.name }</option>,
                    ) }
                </Filter>
              </div>
              <div className="admin-filter__item">
                <TextField
                  name='CampaignID-Filter'
                  id='filter-campaign_id'
                  label='Campaign ID'
                  placeholder="Enter Campaign ID"
                  onChange={this.props.campaignIdFilterChanged}
                  value={query.campaign_id || ''}
                />
              </div>
              <TextButton
                className="admin-filter__item admin-filter__clear-button"
                Icon={IconCloseCircle}
                onClick={this.props.clearAllClicked}
              >
                Clear All
              </TextButton>
            </div>
          </fieldset>

          }
        </Card>
        <Table
          id="legacy-listing-table"
          title=""
          className="admin-list__listings-table"
          resetSortOnDataChange={false}
          columns={[
            {
              name: 'Campaign name',
              cellFormatter: row => (
                <CanvasLink
                  href=""
                  component={Link}
                  type="emphasis"
                  className="admin-list__name-link"
                  to={`/campaigns/${this.props.type}/${row.id}`}
                >
                  { row.name }
                </CanvasLink>
              ),
              selector: '',
              grow: 2,
              style: { textAlign: 'left' },
              ...this.sortableColumnProperties('name'),
            },
            {
              name: 'Start date',
              cellFormatter: row => <TextCaption component="p">{ moment(row.start_date).format('lll') }</TextCaption>,
              selector: '',
              ...this.sortableColumnProperties('start_date'),
            }, {
              name: 'End date',
              cellFormatter: row => <TextCaption component="p">{ moment(row.end_date).format('lll') }</TextCaption>,
              selector: '',
              ...this.sortableColumnProperties('end_date'),
            }, {
              name: 'Campaign ID',
              cellFormatter: row => <TextCaption component="p">{ row.targeting.campaign_name || 'N/A' }</TextCaption>,
              selector: '',
            }, {
              name: 'Created by',
              selector: 'created_by',
            }, {
              name: 'Last updated by',
              selector: 'updated_by',
            },
            {
              name: 'Status',
              cellFormatter: row => <StatusBadge className="admin-list__status-badge" status={row.status} />,
              selector: '',
              ...this.sortableColumnProperties('rule_status'),
            }, {
              name: 'Last updated at',
              cellFormatter: row => <TextCaption component="p">{ moment(row.updated_at).format('lll') }</TextCaption>,
              selector: '',
              ...this.sortableColumnProperties('updated_at'),

            },
          ]}
          data={data?.items || []}
        >
        </Table>
        { data && data.total === 0 && <TextCaption component="p">No results available</TextCaption>
        }
        { data && (data.total > data.limit) &&
        <DesktopPagination
          id="pagination"
          className="admin-list__pagination"
          totalResultCount={data.total}
          onChange={this.props.pageClicked}
          firstButtonLabel="First"
          prevButtonLabel="Previous"
          nextButtonLabel="Next"
          lastButtonLabel="Last"
          navigationLabel="Pagination Navigation"
          currentPage={data ? (data.offset / data.limit) + 1 : 1}
          pageSize={data.limit}
        />
        }
      </div>
    );
  }
}
