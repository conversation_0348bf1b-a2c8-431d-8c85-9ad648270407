import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import moment from 'moment';

import AlertBanner from 'canvas-core-react/lib/AlertBanner';
import BackButton from 'canvas-core-react/lib/BackButton';
import Card from 'canvas-core-react/lib/Card';
import IconAdd from 'canvas-core-react/lib/IconAdd';
import IconDelete from 'canvas-core-react/lib/IconDelete';
import IconDownload from 'canvas-core-react/lib/IconDownload';
import InputGroup from 'canvas-core-react/lib/InputGroup';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import PillButton from 'canvas-core-react/lib/PillButton';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import Table from 'canvas-core-react/lib/Table';
import TextButton from 'canvas-core-react/lib/TextButton';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import TextIntroduction from 'canvas-core-react/lib/TextIntroduction';

import InputCheckboxField from '../formFields/inputCheckboxField';
import IconExternal from 'canvas-core-react/lib/IconExternal';
import InputGroupField from '../formFields/inputGroupField';
import InputRadioButtonField from '../formFields/inputRadioButtonField';
import InputTextField from '../formFields/inputTextField';
import DateField from '../formFields/inputDateField';

import Autosuggest from '../autosuggest/massCampaignAutosuggest';
import AdvancedTargeting from '../vignette/advancedTargeting';
import {
  STATUS,
  ruleTypes,
  mapPropToKey,
  ruleTypesDisplayName,
} from '../../constants';
import FragmentModalContainer from '../modal/fragmentContainer';
import StatusBadge from '../core/statusBadge';
import { min, max, campaignId } from '../../utils/validation';

const validation = {
  min5: min(5),
  max15: max(15),
  max100: max(100),
};

export default class Details extends React.Component {
  static propTypes = {
    id: PropTypes.string,
    status: PropTypes.string,
    title: PropTypes.string.isRequired,
    formValues: PropTypes.object.isRequired,
    formChange: PropTypes.func.isRequired,
    onSubmit: PropTypes.func.isRequired,
    onApprove: PropTypes.func.isRequired,
    onPublish: PropTypes.func.isRequired,
    onTerminate: PropTypes.func.isRequired,
    onCancel: PropTypes.func.isRequired,
    onDuplicate: PropTypes.func.isRequired,
    onDelete: PropTypes.func.isRequired,
    deleteContent: PropTypes.func.isRequired,
    openFragmentModal: PropTypes.func.isRequired,
    updateDetails: PropTypes.func.isRequired,
    type: PropTypes.string.isRequired,
    access: PropTypes.object.isRequired,
    permissions: PropTypes.object.isRequired,
    containers: PropTypes.shape({
      items: PropTypes.object,
      isLoading: PropTypes.bool,
    }).isRequired,
    pages: PropTypes.array.isRequired,
    metadata: PropTypes.object.isRequired,
    downloadXML: PropTypes.func.isRequired,
    selectedWebFragment: PropTypes.object,
  };

  state = {
    popup: false,
    showDownloadTip: false,
    accessRuleSubTypes: [],
  };

  openPopupDelete = () => {
    this.setState({ popup: 'delete' });
  };

  openPopupTerminate = () => {
    this.setState({ popup: 'terminate' });
  };

  closePopup = () => {
    this.setState({ popup: false });
  };

  terminateClosePopup = () => {
    this.props.onTerminate();
    this.setState({ popup: false, showDownloadTip: false });
  };

  multiselectChange = formKey => v => {
    this.props.updateDetails({ [formKey]: v.map(i => i.code) });
  };

  handleClickPreviewLink = (type, fragmentId) => () => {
    const newUrl = `/web-fragment-preview/${type}/${fragmentId}/`;
    window.open(newUrl, 'fragmentPreview', 'height=1000,width=1200,top=0,left=0');
  };

  duplicate = () => {
    this.setState({ showDownloadTip: false });
    this.props.onDuplicate();
  }

  downloadClicked = () => {
    this.setState({ showDownloadTip: true });
    this.props.downloadXML();
  }

  isExpired = () => {
    return new Date(this.props.formValues.end_date) < Date.now();
  }

  componentDidMount() {
    const { access, formChange, formValues, type } = this.props;
    if (!formValues.type) {
      const manageAccessRuleSubTypes = access.ruleSubTypes[type === ruleTypes.ESTORE ? 'storefront' : 'sol']?.manage || [];
      formChange('vignetteDetails', 'type', manageAccessRuleSubTypes[0]);
    }
  }

  render() {
    const { access, status, type, permissions: perm, formValues, pages, selectedWebFragment } = this.props;
    const application = type === ruleTypes.ESTORE ? 'storefront' : 'sol';
    const manageAccessContainers = access.containers[application]?.manage || [];
    const manageAccessPages = access.pages[application]?.manage || [];
    const manageAccessRuleSubTypes = access.ruleSubTypes[application]?.manage || [];

    if (!formValues.id) {
      // Ensure the user has manage permissions to at least one container, page & sub type (required to create a rule)
      if (!manageAccessContainers.length || !manageAccessPages.length || !manageAccessRuleSubTypes.length) {
        return (<AlertBanner type="error">You do not have permissions to view this page</AlertBanner>);
      }
    }

    const containers = mapPropToKey(Object.values(this.props.containers).filter(c => {
      return c.rule_type.startsWith(type) && c.pages.length && manageAccessContainers.includes(c.containerId);
    }), 'containerId');
    const pagesForContainer = formValues.container
      ? (containers[formValues.container]?.pages || [])
          .map(containerPageId => pages.length && pages.find(p => p.id === containerPageId))
          .filter(p => manageAccessPages.includes(p?.pageId))
      : [];
    const massTargetingDataLegend = { keyName: 'code', valueName: 'description' };

    // Form should be disabled if rule is not editable or if rule is in draft but user doesn't have manage access to the rules container, page or subtype
    const formDisabled = [ STATUS.SUBMITTED, STATUS.REVIEWED, STATUS.PUBLISHED, STATUS.TERMINATED, STATUS.INACTIVE ].includes(status) ||
      (status === STATUS.DRAFT && (!manageAccessContainers.includes(formValues.container) || !manageAccessPages.some(p => formValues.pages.includes(p)) || !manageAccessRuleSubTypes.includes(formValues.type)));

    return (
      <div className="admin-details">
        <form>
          <div className="admin-details__action-bar">
            <TextIntroduction
              component="h1"
              className="admin-details__header"
            >
              { this.props.title }
            </TextIntroduction>
            <div>
              <div className="admin-details__action-buttons">
                { status === STATUS.PUBLISHED && type === ruleTypes.ESTORE && !this.isExpired() &&
                <PillButton
                  type="caution"
                  buttonType="button"
                  Icon={IconDownload}
                  className="admin-details__action-button"
                  variant="info"
                  onClick={this.downloadClicked}
                >
                  Download
                </PillButton>
                }
                { status && perm['campaigns_manage'] &&
                <PillButton
                  type="caution"
                  buttonType="button"
                  className="admin-details__action-button"
                  onClick={this.duplicate}
                >
                  Duplicate
                </PillButton>
                }
                { status === STATUS.PUBLISHED && perm['campaigns_manage'] &&
                <PillButton
                  buttonType="button"
                  type="caution"
                  className="admin-details__action-button"
                  onClick={this.openPopupTerminate}
                >
                  Terminate
                </PillButton>
                }
                { status && (status === STATUS.DRAFT || status === STATUS.SUBMITTED || status === STATUS.REVIEWED) && perm['campaigns_manage'] &&
                <PillButton
                  buttonType="button"
                  type="caution"
                  className="admin-details__action-button"
                  onClick={this.openPopupDelete
                  }
                >
                  Delete
                </PillButton>
                }
              </div>
              { this.state.showDownloadTip && (
                <div className="admin-details__action-info">
                  <TextCaption component="p">Please merge this XML file into the Product & Services repository on
                    Bitbucket.</TextCaption>
                </div>) }
            </div>

          </div>
          <div>
            { status && (
              <TextIntroduction
                component="h2"
                className="admin-details__sub-header"
              >
                Current Status:
                <StatusBadge
                  status={status}
                  deleted={formValues.deleted}
                />
              </TextIntroduction>
            ) }
          </div>
          <Card className="admin-details__card">
            <TextIntroduction
              component="h2"
              className="admin-details__sub-header"
            >
              Campaign Information
            </TextIntroduction>
            <Field
              className="admin-details__field"
              required
              name="name"
              label="Campaign Name"
              placeholder="Campaign Name"
              component={InputTextField}
              disabled={formDisabled}
              validate={[ validation.min5, validation.max100 ]}
              autoComplete="off"
            />
          </Card>

          <Card className="admin-details__card">
            <TextIntroduction
              component="h2"
              className="admin-details__sub-header"
            >
              Campaign Type
            </TextIntroduction>
            <InputGroup
              name="type"
              id="campaign-type-group"
              legend=""
            >
              <div className="admin-details__radio-container">
                { (formDisabled || manageAccessRuleSubTypes.includes('targeted')) &&
                 <Field
                  id="targeted"
                  name="type"
                  label="Targeted Campaign"
                  component={InputRadioButtonField}
                  disabled={formDisabled}
                />
                }
               { (formDisabled || manageAccessRuleSubTypes.includes('mass')) &&
                <Field
                  id="mass"
                  name="type"
                  label="Mass Campaign"
                  component={InputRadioButtonField}
                  disabled={formDisabled}
                />
               }

              </div>
            </InputGroup>
          </Card>

          { formValues.type === 'targeted' &&
          <Card className="admin-details__card">
            <TextIntroduction
              component="h2"
              className="admin-details__sub-header"
            >
              Targeting by Campaign ID
            </TextIntroduction>
            <Field
              required
              name="campaign_name"
              label="Campaign ID"
              placeholder="Campaign ID"
              component={InputTextField}
              validate={[ campaignId, validation.min5, validation.max15 ]}
              autoComplete="off"
              disabled={formDisabled}
            />
          </Card>
          }

          { formValues.type?.toLowerCase() === 'mass' &&
          <AdvancedTargeting {...this.props} disabled={formDisabled} />
          }

          <Card className="admin-details__card">
            <TextIntroduction
              component="h2"
              className="admin-details__sub-header"
            >
              Targeting Dimension
            </TextIntroduction>
            <div className="admin-details__date-fields">
              <Field
                className="admin-details__date-field"
                required
                name="start_date"
                label="Start date"
                placeholder="MM/DD/YYYY HH:MM AM/PM"
                component={DateField}
                disabled={formDisabled}
                autoComplete="off"
              />
              <Field
                className="admin-details__date-field"
                required
                name="end_date"
                label="End date"
                placeholder="MM/DD/YYYY HH:MM AM/PM"
                component={DateField}
                disabled={formDisabled}
                autoComplete="off"
              />
            </div>
            <Field
              className="admin-details__field"
              name="language"
              component={InputGroupField}
            >
              <div className="admin-details__radio-container">
                <span className="input-group__legend">Language</span>
                <Field
                  id="en"
                  name="language"
                  label="EN"
                  component={InputRadioButtonField}
                  disabled={formDisabled}
                />
                <Field
                  id="fr"
                  name="language"
                  label="FR"
                  component={InputRadioButtonField}
                  disabled={formDisabled}
                />
              </div>
            </Field>
          </Card>

          { type === ruleTypes.ESTORE && (
            <Card className="admin-details__card">
              <TextIntroduction
                component="h2"
                className="admin-details__sub-header"
              >
                Targeting by Device
              </TextIntroduction>
              <div className="admin-details__autosuggest-table">
                <div className="admin-details__autosuggest-column">
                  <Autosuggest
                    label="Include"
                    data={this.props.metadata.device}
                    hideSuggestionsAfterSelection
                    dataLegend={massTargetingDataLegend}
                    placeholder="Device Type"
                    onChange={this.multiselectChange('DEVC')}
                    editable={!formDisabled}
                    initialSelection={formValues.DEVC}
                    excludes={formValues.DEVC_NOT}
                  />
                </div>
                <div className="admin-details__autosuggest-column">
                  <Autosuggest
                    label="Exclude"
                    data={this.props.metadata.device}
                    hideSuggestionsAfterSelection
                    dataLegend={massTargetingDataLegend}
                    placeholder="Device Type"
                    onChange={this.multiselectChange('DEVC_NOT')}
                    editable={!formDisabled}
                    initialSelection={formValues.DEVC_NOT}
                    excludes={formValues.DEVC}
                  />
                </div>
              </div>
              <Field
                name="DEVC_RADIO"
                component={InputGroupField}
              >
                <div className="admin-details__radio-container">
                  <span className="input-group__legend">Relationship Settings</span>
                  <Field
                    id="DEVC_OR"
                    name="DEVC_RADIO"
                    label="OR"
                    component={InputRadioButtonField}
                    disabled={formDisabled}
                  />
                  <Field
                    id="DEVC_AND"
                    name="DEVC_RADIO"
                    label="AND"
                    component={InputRadioButtonField}
                    disabled={formDisabled}
                  />
                </div>
              </Field>
            </Card>
          ) }

          <Card className="admin-details__card">
            <TextIntroduction
              component="h2"
              className="admin-details__sub-header"
            >
              Container and Pages
            </TextIntroduction>
            <Field
              className="admin-details__field"
              label=""
              name="container"
              component={InputGroupField}
            >
              <Autosuggest
                label="Container Name"
                data={formDisabled ? Object.values(this.props.containers) : Object.values(containers)}
                hideSuggestionsAfterSelection
                dataLegend={{ keyName: 'containerId', valueName: 'name' }}
                placeholder="Select a container&hellip;"
                editable={!formDisabled}
                onChange={(e) => {
                  const container = e.length > 0 ? e[0].containerId : undefined;
                  this.props.updateDetails({ container, pages: [], subject_line: '' });
                  return e;
                }}
                initialSelection={[ formValues.container ]}
                limit={1}
              />
            </Field>
            { (formDisabled || (containers[formValues.container] && containers[formValues.container].pages.length > 0)) &&
            <Field
              label=""
              name="pages"
              component={InputGroupField}
              className="admin-details__field"
            >
              <Autosuggest
                label="Page Name"
                data={formDisabled
                  ? Object.values(this.props.pages).map(p => ({ description: p.name, code: p.pageId }))
                  : pagesForContainer.map(p => ({ description: p.name, code: p.pageId }))}
                hideSuggestionsAfterSelection
                dataLegend={massTargetingDataLegend}
                placeholder="Pages"
                editable={!formDisabled}
                onChange={this.multiselectChange('pages')}
                initialSelection={formValues.pages}
              />
            </Field>
            }
            { containers[formValues.container] && [ ruleTypes.VIGNETTE_BROADCAST, ruleTypes.VIGNETTE_PRIORITY ].includes(containers[formValues.container].rule_type) &&
            <Field
              required
              name="subject_line"
              label="Subject Line"
              placeholder="Subject Line"
              component={InputTextField}
              disabled={formDisabled}
              autoComplete="off"
            />
            }
            { containers[formValues.container] && containers[formValues.container].rule_type === ruleTypes.VIGNETTE_BROADCAST &&
            <Field
              className="admin-details__field"
              name="urgent"
              label="Urgent Campaign"
              component={InputCheckboxField}
              disabled={formDisabled}
            />
            }
          </Card>
          <Card className="admin-details__card">
            <TextIntroduction
              component="h2"
              className="admin-details__sub-header"
            >
              Code Fragment
            </TextIntroduction>
            {
              formValues.content_id ? (
                <Table
                  id="sol-details-content-table"
                  title=""
                  className="admin-details__content-table"
                  columns={[
                    {
                      name: 'Fragment Name',
                      cellFormatter: row => (
                        <TextCaption component="p">
                          { row ? row.name : formValues.content_id }
                        </TextCaption>
                      ),
                      selector: '',
                      grow: 2,
                      style: { textAlign: 'left' },
                    }, {
                      name: 'Updated Timestamp',
                      cellFormatter: row => moment(row?.updated_ts).format('lll'),
                      selector: '',
                    }, {
                      name: 'Preview Link',
                      cellFormatter: row => (
                        <TextButton
                          className="admin-details__table-button"
                          Icon={IconExternal}
                          iconPosition="right"
                          type="button"
                          onClick={this.handleClickPreviewLink(row?.space, row?.web_fragment_id)}
                        >
                            Preview
                        </TextButton>
                      ),
                      selector: '',
                    }, {
                      name: 'Delete',
                      cellFormatter: () => !formDisabled && (
                        <TextButton
                          className="admin-details__table-button"
                          Icon={IconDelete}
                          iconPosition="right"
                          type="button"
                          onClick={this.props.deleteContent}
                        >
                            Delete
                        </TextButton>
                      ),
                      selector: '',
                    },
                  ]}
                  data={[ selectedWebFragment ]}
                />
              )
                : (formDisabled
                  ? <TextCaption component="p">No web fragment attached</TextCaption>
                  : <Field
                    className="admin-details__field"
                    name="content_id"
                    component={InputGroupField}
                  >
                    <TextButton
                      Icon={IconAdd}
                      iconPosition="left"
                      type="button"
                      onClick={() => this.props.openFragmentModal(type)}
                    >
                      Search and add web fragment
                    </TextButton>
                  </Field>
                )
            }
          </Card>

          <div className="admin-details__action-bar">
            <BackButton
              type="button"
              onClick={this.props.onCancel}
            >
              Cancel
            </BackButton>
            <div className="admin-details__action-buttons">
              { ((!status || status === STATUS.DRAFT) && perm['campaigns_manage']) &&
              <SecondaryButton
                className="admin-details__action-button"
                onClick={this.props.onSubmit(STATUS.DRAFT)}
              >
                Save as Draft
              </SecondaryButton>
              }
              { status === STATUS.SUBMITTED && [ ruleTypes.VIGNETTE, ruleTypes.ESTORE ].includes(type) &&
              perm['campaigns_review'] &&
              <SecondaryButton
                type="button"
                className="admin-details__action-button"
                onClick={this.props.onApprove(false)}
              >
                Reject
              </SecondaryButton>
              }
              { (status === STATUS.REVIEWED && perm['campaigns_approve']) &&
              <SecondaryButton
                type="button"
                className="admin-details__action-button"
                onClick={this.props.onPublish(false)}
              >
                Reject
              </SecondaryButton>
              }
              { ((!status || status === STATUS.DRAFT) && perm['campaigns_manage']) &&
              <PrimaryButton
                className="admin-details__action-button"
                onClick={this.props.onSubmit()}
              >
                Submit for Review
              </PrimaryButton>
              }
              { status === STATUS.SUBMITTED && [ ruleTypes.VIGNETTE, ruleTypes.ESTORE ].includes(type) &&
              perm['campaigns_review'] &&
              <PrimaryButton
                type="button"
                className="admin-details__action-button"
                onClick={this.props.onApprove()}
              >
                Approve
              </PrimaryButton>
              }
              { (status === STATUS.REVIEWED && perm['campaigns_approve']) &&
              <PrimaryButton
                type="button"
                className="admin-details__action-button"
                onClick={this.props.onPublish()}
              >
                Publish
              </PrimaryButton>
              }
            </div>
          </div>
        </form>
        <ModalDialogue
          headline="Are you sure?"
          primaryButtonLabel={this.state.popup === 'terminate' ? 'Terminate' : 'Delete'}
          primaryAction={() => {
            if (this.state.popup === 'terminate') {
              this.props.onTerminate();
            } else {
              this.props.onDelete();
            }
            this.closePopup();
          }}
          secondaryButtonLabel="Cancel"
          secondaryAction={this.closePopup}
          isModalVisible={this.state.popup}
          setModalVisible={this.closePopup}
        >
          This { ruleTypesDisplayName[this.props.type] } will
          be { this.state.popup === 'delete' && 'deleted' }{ this.state.popup === 'terminate' && 'terminated' }.
          { this.props.type === ruleTypes.ESTORE && (<><br /><strong>Make sure you remove any downloaded XML files
            ({ `${this.props.id}.xml` }) associated to this rule from the Products and Services Bitbucket
            Repository.</strong></>) }
        </ModalDialogue>
        <FragmentModalContainer type={type} />
      </div>
    );
  }
}
