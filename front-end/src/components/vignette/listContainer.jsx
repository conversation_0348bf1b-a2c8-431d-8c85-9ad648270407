import React from 'react';
import { connect } from 'react-redux';
import { Redirect } from 'react-router-dom';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import qs from 'qs';
import { isEmpty } from 'lodash';

import AlertBanner from 'canvas-core-react/lib/AlertBanner';

import { getVignettes } from '../../store/actions/vignettes';
import { getUsers } from '../../store/actions/users';
import { canViewManageRuleType } from '../rules/utils';

import List from './list';
import {
  setBrowserTitle,
  ruleRouteApiMapping,
  ruleTypesDisplayName,
  ruleTypes,
} from '../../constants';

export class ListContainer extends React.PureComponent {
  static propTypes = {
    listings: PropTypes.shape({
      data: PropTypes.shape({
        items: PropTypes.arrayOf(PropTypes.object),
      }),
      isLoading: PropTypes.bool,
    }).isRequired,
    match: PropTypes.shape({
      params: PropTypes.shape({
        type: PropTypes.string,
      }).isRequired,
    }).isRequired,
    users: PropTypes.shape({
      items: PropTypes.object,
      isLoading: PropTypes.bool,
    }).isRequired,
    getListings: PropTypes.func.isRequired,
    getUsers: PropTypes.func.isRequired,
    history: PropTypes.shape({
      push: PropTypes.func.isRequired,
    }).isRequired,
    location: PropTypes.shape({
      search: PropTypes.string.isRequired,
    }).isRequired,
    access: PropTypes.object.isRequired,
    permissions: PropTypes.object.isRequired,
  };
  state = { page: 1, sort: '-updated_at', ...qs.parse(this.props.location.search, { ignoreQueryPrefix: true }) };
  priorityUrls = [ 'campaigns', 'ccau_campaigns', 'sol', 'estore', 'alerts' ];

  componentDidMount() {
    const { match: { params: { type } }, getUsers, access, permissions } = this.props;
    const { viewSOL, viewStoreFront } = canViewManageRuleType(access, permissions);
    const hasMinimumViewAccess = type === ruleTypes.ESTORE ? viewStoreFront : viewSOL;
    if (hasMinimumViewAccess) {
      this.updateListingsForType(type);
      getUsers();
    }
  }

  update = (newQuery) => {
    const { match: { params: { type } }, getListings, history } = this.props;
    getListings({
      ...newQuery,
      type: ruleRouteApiMapping[type],
    });
    if (newQuery.page === 1) {
      delete newQuery.page;
    }
    if (newQuery.sort === '-updated_at') {
      delete newQuery.sort;
    }
    history.push({ search: qs.stringify(newQuery, { addQueryPrefix: true }) });
  };

  updateListingsForType = (type) => {
    setBrowserTitle(`Manage ${ruleTypesDisplayName[type]}s`);
    this.props.getListings({
      ...this.state,
      type: ruleRouteApiMapping[type],
    });
  };

  tabClicked = () => {
    if (isEmpty(qs.parse(this.props.location.search, { ignoreQueryPrefix: true }))) {
      this.setState({ ...this.state, name: undefined });
    }
  }

  componentDidUpdate = (prevProps) => {
    const { match: { params: { type } } } = this.props;
    const { match: { params: { type: oldType } } } = prevProps;
    if (type !== oldType) {
      this.tabClicked();
      this.updateListingsForType(type);
    }
  }

  createClicked = () => {
    const { match: { params: { type } }, history } = this.props;
    history.push(`/campaigns/${type}/create`);
  };

  pageClicked = page => {
    this.setState({ page });
    this.update({ ...this.state, page });
  };

  statusClicked = e => {
    const status = e.target.value === 'all' ? undefined : e.target.value;
    this.setState({ status: status, page: 1 });
    this.update({ ...this.state, status: status, page: 1 });
  };

  createdByClicked = e => {
    const createdBy = e.target.value === 'all' ? undefined : [ e.target.value ];
    this.setState({ created_by: createdBy, page: 1 });
    this.update({ ...this.state, created_by: createdBy, page: 1 });
  };

  updatedByClicked = e => {
    const updatedBy = e.target.value === 'all' ? undefined : [ e.target.value ];
    this.setState({ updated_by: updatedBy, page: 1 });
    this.update({ ...this.state, updated_by: updatedBy, page: 1 });
  };

  campaignIdFilterChanged = e => {
    const campaignId = e.target.value || undefined;
    this.setState({ campaign_id: campaignId, page: 1 });
    this.update({ ...this.state, campaign_id: campaignId, page: 1 });
  };

  clearAllClicked = () => {
    this.setState({ page: 1, sort: '-updated_at', name: undefined, status: undefined, created_by: undefined, updated_by: undefined, campaign_id: undefined });
    this.update({ page: 1, sort: '-updated_at', name: undefined, status: undefined, created_by: undefined, updated_by: undefined, campaign_id: undefined });
  };

  sortClicked = (column, descending) => {
    if ((!descending && this.state.sort === column) || (descending && this.state.sort !== `-${column}`)) {
      this.setState({ sort: `-${column}`, page: 1 });
      this.update({ ...this.state, sort: `-${column}`, page: 1 });
    } else {
      this.setState({ sort: column, page: 1 });
      this.update({ ...this.state, sort: column, page: 1 });
    }
  };

  nameUpdated = value => {
    this.setState({ name: value || undefined, page: 1 });
    this.update({ ...this.state, name: value || undefined, page: 1 });
  };

  render() {
    const { match: { params: { type } }, access, permissions } = this.props;

    // If user doesn't have access to sol/storefront, try to redirect to a campaigns/alert page they do have access to otherwise show permission error
    const { viewCampaigns, viewCCAUCampaigns, viewSOL, viewStoreFront, viewAlert } = canViewManageRuleType(access, permissions);
    const hasMinimumViewAccess = type === ruleTypes.ESTORE ? viewStoreFront : viewSOL;
    if (!hasMinimumViewAccess) {
      const redirectTo = this.priorityUrls.find(url => {
        switch (url) {
          case 'campaigns':
            return viewCampaigns;
          case 'ccau_campaigns':
            return viewCCAUCampaigns;
          case 'sol':
            return viewSOL;
          case 'estore':
            return viewStoreFront;
          case 'alerts':
            return viewAlert;
        }
      });
      if (redirectTo) {
        return <Redirect to={'/' + redirectTo} />;
      }
      return (<AlertBanner type="error">You do not have permissions to view this page</AlertBanner>);
    }

    return (
      <List
        listings={this.props.listings}
        users={this.props.users}
        createClicked={this.createClicked}
        pageClicked={this.pageClicked}
        type={type.toLowerCase()}
        query={this.state}
        statusClicked={this.statusClicked}
        createdByClicked={this.createdByClicked}
        updatedByClicked={this.updatedByClicked}
        campaignIdFilterChanged={this.campaignIdFilterChanged}
        clearAllClicked={this.clearAllClicked}
        nameUpdated={this.nameUpdated}
        sortClicked={this.sortClicked}
        access={this.props.access.campaigns}
        permissions={this.props.permissions}
        location={this.props.location}
      />
    );
  }
}

const vignetteMapStateToProps = state => ({
  listings: state.rules,
  users: state.users,
  access: state.authenticated.access || {},
  permissions: state.authenticated.permissions || {},
});

const vignetteMapDispatchToProps = dispatch => bindActionCreators({
  getListings: getVignettes,
  getUsers,
}, dispatch);

@connect(vignetteMapStateToProps, vignetteMapDispatchToProps)
export class VignetteList extends ListContainer {}
