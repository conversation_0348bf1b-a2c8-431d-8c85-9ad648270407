import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import Card from 'canvas-core-react/lib/Card';
import InputGroup<PERSON>ield from '../formFields/inputGroupField';
import InputRadioButtonField from '../formFields/inputRadioButtonField';
import Autosuggest from '../autosuggest/massCampaignAutosuggest';
import TextIntroduction from 'canvas-core-react/lib/TextIntroduction';

const massTargetingDataLegend = { keyName: 'code', valueName: 'description' };

class AdvancedTargeting extends React.Component {
  multiselectChange = formKey => v => {
    const itemToUpdateWith = v.map(i => i.code);
    this.props.updateDetails({ [formKey]: itemToUpdateWith });
  };

  render() {
    const { disabled, formValues } = this.props;

    return (
      <React.Fragment>
        <Card className="admin-details__card">
          <TextIntroduction
            component="h2"
            className="admin-details__sub-header"
          >
            Targeting by Business Lines
          </TextIntroduction>
          <div className="details__autosuggest-table">
            <div className="details__autosuggest-column">
              <Autosuggest
                label="Include"
                data={this.props.metadata.businessLines}
                hideSuggestionsAfterSelection
                dataLegend={massTargetingDataLegend}
                placeholder="Business Line"
                onChange={this.multiselectChange('BU')}
                editable={!disabled}
                initialSelection={formValues.BU}
                excludes={formValues.BU_NOT}
              />
            </div>
            <div className="details__autosuggest-column">
              <Autosuggest
                label="Exclude"
                data={this.props.metadata.businessLines}
                hideSuggestionsAfterSelection
                dataLegend={massTargetingDataLegend}
                placeholder="Business Line"
                onChange={this.multiselectChange('BU_NOT')}
                editable={!disabled}
                initialSelection={formValues.BU_NOT}
                excludes={formValues.BU}
              />
            </div>
          </div>
          <Field
            name="BU_RADIO"
            component={InputGroupField}
          >
            <div className="details__radio-container">
              <legend className="input-group__legend">Relationship Settings</legend>
              <Field
                id="BU_OR"
                name="BU_RADIO"
                label="OR"
                component={InputRadioButtonField}
                disabled={disabled}
              />
              <Field
                id="BU_AND"
                name="BU_RADIO"
                label="AND"
                component={InputRadioButtonField}
                disabled={disabled}
              />
            </div>
          </Field>

        </Card>

        <Card className="admin-details__card">
          <TextIntroduction
            component="h2"
            className="admin-details__sub-header"
          >
            Targeting by Registration Status
          </TextIntroduction>
          <div className="details__autosuggest-table">
            <div className="details__autosuggest-column">
              <Autosuggest
                label="Include"
                data={this.props.metadata.registrationStatus}
                hideSuggestionsAfterSelection
                dataLegend={massTargetingDataLegend}
                placeholder="Registration Status"
                onChange={this.multiselectChange('REGS')}
                editable={!disabled}
                initialSelection={formValues.REGS}
                excludes={formValues.REGS_NOT}
              />
            </div>
            <div className="details__autosuggest-column">
              <Autosuggest
                label="Exclude"
                data={this.props.metadata.registrationStatus}
                hideSuggestionsAfterSelection
                dataLegend={massTargetingDataLegend}
                placeholder="Registration Status"
                onChange={this.multiselectChange('REGS_NOT')}
                editable={!disabled}
                initialSelection={formValues.REGS_NOT}
                excludes={formValues.REGS}
              />
            </div>
          </div>
          <Field
            name="REGS_RADIO"
            component={InputGroupField}
          >
            <div className="details__radio-container">
              <legend className="input-group__legend">Relationship Settings</legend>
              <Field
                id="REGS_OR"
                name="REGS_RADIO"
                label="OR"
                component={InputRadioButtonField}
                disabled={disabled}
              />
              <Field
                id="REGS_AND"
                name="REGS_RADIO"
                label="AND"
                component={InputRadioButtonField}
                disabled={disabled}
              />
            </div>
          </Field>
        </Card>
        <Card className="admin-details__card">
          <TextIntroduction
            component="h2"
            className="admin-details__sub-header"
          >
            Advanced Targeting
          </TextIntroduction>
          <div className="details__autosuggest-table">
            <div className="details__autosuggest-column">
              <Autosuggest
                label="Include"
                data={this.props.metadata.bankingProducts}
                placeholder="Banking Products"
                dataLegend={massTargetingDataLegend}
                onChange={this.multiselectChange('PR1')}
                editable={!disabled}
                initialSelection={formValues.PR1}
                excludes={formValues.PR1_NOT}
              />
              <Autosuggest
                data={this.props.metadata.borrowingProducts}
                dataLegend={massTargetingDataLegend}
                placeholder="Borrowing Products"
                onChange={this.multiselectChange('PR2')}
                editable={!disabled}
                initialSelection={formValues.PR2}
                excludes={formValues.PR2_NOT}
              />
              <Autosuggest
                data={this.props.metadata.investingRetail}
                dataLegend={massTargetingDataLegend}
                placeholder="Investing - Retail"
                onChange={this.multiselectChange('PR3')}
                editable={!disabled}
                initialSelection={formValues.PR3}
                excludes={formValues.PR3_NOT}
              />
              <Autosuggest
                data={this.props.metadata.investingWealth}
                dataLegend={massTargetingDataLegend}
                placeholder="Investing - Wealth"
                onChange={this.multiselectChange('PR4')}
                editable={!disabled}
                initialSelection={formValues.PR4}
                excludes={formValues.PR4_NOT}
              />
            </div>
            <div className="details__autosuggest-column">
              <Autosuggest
                label="Exclude"
                data={this.props.metadata.bankingProducts}
                dataLegend={massTargetingDataLegend}
                placeholder="Banking Products"
                onChange={this.multiselectChange('PR1_NOT')}
                editable={!disabled}
                initialSelection={formValues.PR1_NOT}
                excludes={formValues.PR1}
              />
              <Autosuggest
                data={this.props.metadata.borrowingProducts}
                dataLegend={massTargetingDataLegend}
                placeholder="Borrowing Products"
                onChange={this.multiselectChange('PR2_NOT')}
                editable={!disabled}
                initialSelection={formValues.PR2_NOT}
                excludes={formValues.PR2}
              />
              <Autosuggest
                data={this.props.metadata.investingRetail}
                dataLegend={massTargetingDataLegend}
                placeholder="Investing - Retail"
                onChange={this.multiselectChange('PR3_NOT')}
                editable={!disabled}
                initialSelection={formValues.PR3_NOT}
                excludes={formValues.PR3}
              />
              <Autosuggest
                data={this.props.metadata.investingWealth}
                dataLegend={massTargetingDataLegend}
                placeholder="Investing - Wealth"
                onChange={this.multiselectChange('PR4_NOT')}
                editable={!disabled}
                initialSelection={formValues.PR4_NOT}
                excludes={formValues.PR4}
              />
            </div>
          </div>
          <Field
            name="PR_RADIO"
            component={InputGroupField}
          >
            <div className="details__radio-container">
              <legend className="input-group__legend">Relationship Settings</legend>
              <Field
                id="PR_OR"
                name="PR_RADIO"
                label="OR"
                component={InputRadioButtonField}
                disabled={disabled}
              />
              <Field
                id="PR_AND"
                name="PR_RADIO"
                label="AND"
                component={InputRadioButtonField}
                disabled={disabled}
              />
            </div>
          </Field>
        </Card>

        <Card className="admin-details__card">
          <TextIntroduction
            component="h2"
            className="admin-details__sub-header"
          >
            Targeting by Geography
          </TextIntroduction>
          <Autosuggest
            className="admin-details__field"
            data={this.props.metadata.geography}
            dataLegend={massTargetingDataLegend}
            placeholder="Province"
            onChange={this.multiselectChange('PROV')}
            editable={!disabled}
            initialSelection={formValues.PROV}
          />
        </Card>
      </React.Fragment>
    );
  }
}

AdvancedTargeting.propTypes = {
  metadata: PropTypes.shape({
    investingRetail: PropTypes.array,
    investingWealth: PropTypes.array,
    borrowingProducts: PropTypes.array,
    bankingProducts: PropTypes.array,
    registrationStatus: PropTypes.array,
    businessLines: PropTypes.array,
    geography: PropTypes.array,
  }).isRequired,
  updateDetails: PropTypes.func.isRequired,
  formValues: PropTypes.object.isRequired,
  disabled: PropTypes.bool.isRequired,
};

export default AdvancedTargeting;
