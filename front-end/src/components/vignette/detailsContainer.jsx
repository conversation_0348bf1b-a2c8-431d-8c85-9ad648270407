import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { reduxForm, change } from 'redux-form';

import { req } from '../../api'; // @todo remove all these imports from this file and refactor the flux way
import { populateVignetteDetails, updateVignetteDetails } from '../../store/actions/vignettes';
import { openFragmentModalAndLoadFragments } from '../../store/actions/modal';
import { addSnackbar } from '../../store/actions/snackbar';
import { getContainers } from '../../store/actions/containers';
import { getWebFragmentMetadata } from '../../store/actions/content';
import { getPages } from '../../store/actions/pages';
import Details from './details';
import {
  STATUS,
  DATE_ISO_FORMAT,
  mapCampaignStatusToTitle,
  setBrowserTitle,
  scrollToTop,
  formatWord,
  ruleTypes,
  mapPropToKey,
  ruleRouteApiMapping,
} from '../../constants';

export class DetailsContainer extends React.PureComponent {
  static propTypes = {
    getPages: PropTypes.func,
    pages: PropTypes.object,
    selectedWebFragment: PropTypes.object,
    formChange: PropTypes.func.isRequired,
    formValues: PropTypes.object.isRequired,
    match: PropTypes.shape({
      params: PropTypes.shape({
        id: PropTypes.string,
        action: PropTypes.string,
        type: PropTypes.string,
      }).isRequired,
    }).isRequired,
    history: PropTypes.shape({
      goBack: PropTypes.func.isRequired,
      push: PropTypes.func.isRequired,
      replace: PropTypes.func.isRequired,
      listen: PropTypes.func.isRequired,
    }).isRequired,
    location: PropTypes.shape({
      pathname: PropTypes.string,
    }),
    reset: PropTypes.func.isRequired,
    handleSubmit: PropTypes.func.isRequired,
    populateDetails: PropTypes.func.isRequired,
    getContainers: PropTypes.func.isRequired,
    updateDetails: PropTypes.func.isRequired,
    openFragmentModalAndLoadFragments: PropTypes.func.isRequired,
    addSnackbar: PropTypes.func.isRequired,
    access: PropTypes.object.isRequired,
    permissions: PropTypes.object.isRequired,
    getWebFragmentMetadata: PropTypes.func.isRequired,
    containers: PropTypes.shape({
      items: PropTypes.object,
      isLoading: PropTypes.bool,
    }).isRequired,
  };

  state = { ready: false };

  init() {
    req.get('/campaign-setup').then(res => {
      this.setState({ metadata: res.data });
      if (this.ID) {
        this.fetchById(this.ID).then(() => {
          setBrowserTitle(this.title);
        });
      } else {
        this.setState({ ready: true });
        setBrowserTitle(this.title);
      }
    });
  }

  componentDidMount() {
    this.init();
    this.props.getContainers();
    this.props.getPages();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.location.pathname !== this.props.location.pathname) {
      this.init();
    }
  }

  get ID() {
    return this.props.match.params.id;
  }

  get status() {
    if (this.duplicated) {
      return null;
    }
    if (this.props.formValues.disabled) {
      return STATUS.INACTIVE;
    }
    return this.props.formValues.status;
  }

  get type() {
    return this.props.match.params.type;
  }

  get ruleApiRouteType() {
    return ruleRouteApiMapping[this.type];
  }

  get title() {
    return mapCampaignStatusToTitle(this.status, this.props.match.params.type);
  }

  get duplicated() {
    return this.props.match.params.action === 'duplicate';
  }

  fetchById = (id) => {
    return req.get(`/${this.ruleApiRouteType}/rules/${id}`, { baseURL: '/api/v2' })
      .then(res => {
        // on load, populate the metadata of the content, since we only have the content_id
        if (res.data.contents && res.data.contents.length > 0) {
          const contentId = res.data.contents[0]['content_id'];
          if (contentId) {
            this.props.getWebFragmentMetadata(contentId);
          }
        }
        this.props.populateDetails({ ...res.data, metadata: this.state.metadata });
        this.setState({ ready: true });
      })
      .catch(err => {
        if (err.response?.status === 404) {
          this.goBack();
        }
      });
  };

  goBack = () => {
    this.props.history.push(`/campaigns/${this.type}`);
  };

  duplicate = () => {
    this.props.history.push(`/campaigns/${this.type}/${this.ID}/duplicate`);
  };

  formatValues = (values, status) => {
    const payload = {
      name: values.name && values.name.trim(),
      type: this.ruleApiRouteType,
      status,
      start_date: moment(values.start_date).toISOString(),
      end_date: moment(values.end_date).toISOString(),
      urgent: this.props.containers[values.container].rule_type === ruleTypes.VIGNETTE_BROADCAST ? values.urgent : false,
      contents: values.pages.map(page => ({
        page,
        container: values.container,
        content_space: 'db',
        content_type: 'webfragment',
        content_id: values.content_id,
      })),
      targeting: {
        language_code: values.language,
        country_code: 'CA',
        platforms: [ {
          platform_name: 'web',
        } ],
        products: [],
        devices: [],
        campaign_name: null,
      },
    };
    switch (this.props.containers[values.container].rule_type) {
      case ruleTypes.VIGNETTE:
        payload.message_type = 'M';
        break;
      case ruleTypes.VIGNETTE_BROADCAST:
        payload.message_type = values.urgent ? 'U' : 'N';
        break;
      case ruleTypes.VIGNETTE_PRIORITY:
        payload.message_type = 'P';
    }
    if (values.subject_line &&
      [
        ruleTypes.VIGNETTE_BROADCAST,
        ruleTypes.VIGNETTE_PRIORITY,
      ].includes(this.props.containers[values.container].rule_type)) {
      payload.contents.forEach(i => {
        i.subject = values.subject_line && values.subject_line.trim();
      });
    }
    if (values.type === 'targeted') {
      payload.targeting.campaign_name = values.campaign_name && values.campaign_name.trim().toUpperCase();
    }
    if (values.type === 'mass') {
      const massTargeting = [];
      if (values.BU && values.BU.length) {
        massTargeting.push({
          attribute_mode: values.BU_RADIO.includes('AND') ? 'and' : 'or',
          attribute_type: 'BU',
          attribute_values: values.BU,
        });
      }
      if (values.BU_NOT && values.BU_NOT.length) {
        massTargeting.push({
          attribute_mode: 'not',
          attribute_type: 'BU',
          attribute_values: values.BU_NOT,
        });
      }
      if (values.REGS && values.REGS.length) {
        massTargeting.push({
          attribute_mode: values.REGS_RADIO.includes('AND') ? 'and' : 'or',
          attribute_type: 'REGS',
          attribute_values: values.REGS,
        });
      }
      if (values.REGS_NOT && values.REGS_NOT.length) {
        massTargeting.push({
          attribute_mode: 'not',
          attribute_type: 'REGS',
          attribute_values: values.REGS_NOT,
        });
      }
      const keys = [ 'PR1', 'PR2', 'PR3', 'PR4' ];
      const attributeValueArray = [];
      const attributeNotValueArray = [];
      keys.forEach((key) => {
        // Advanced Targeted attrribute mapping (Includes)
        if (values[key] && values[key].length) {
          attributeValueArray.push(...values[key]);
        }
        // Advanced Targeted attrribute mapping (Excludes)
        if (values[`${key}_NOT`] && values[`${key}_NOT`].length) {
          attributeNotValueArray.push(...values[`${key}_NOT`]);
        }
      });
      // pushing Includes section
      if (attributeValueArray.length > 0) {
        massTargeting.push({
          attribute_mode: values.PR_RADIO.includes('AND') ? 'and' : 'or',
          attribute_type: 'PR',
          attribute_values: attributeValueArray,
        });
      }
      // pushing Excludes section
      if (attributeNotValueArray.length > 0) {
        massTargeting.push({
          attribute_mode: 'not',
          attribute_type: 'PR',
          attribute_values: attributeNotValueArray,
        });
      }

      if (values.PROV && values.PROV.length) {
        massTargeting.push({
          attribute_mode: 'or',
          attribute_type: 'PROV',
          attribute_values: values.PROV,
        });
      }
      payload.targeting.products = massTargeting;
    }

    if (values.DEVC && values.DEVC.length) {
      payload.targeting.products.push({
        attribute_mode: values.DEVC_RADIO.includes('AND') ? 'and' : 'or',
        attribute_type: 'DEVC',
        attribute_values: values.DEVC,
      });
    }
    if (values.DEVC_NOT && values.DEVC_NOT.length) {
      payload.targeting.products.push({
        attribute_mode: 'not',
        attribute_type: 'DEVC',
        attribute_values: values.DEVC_NOT,
      });
    }
    return payload;
  };

  makeToast = (name, action) => {
    this.props.addSnackbar({ message: `${formatWord(this.ruleApiRouteType, { capitalize: true })} "${name}" has been ${action} successfully.` });
  };

  onActionSuccess = (data, action) => {
    this.makeToast(data.name, action);
    scrollToTop();
    this.props.populateDetails(data);
  };

  submit = (status = STATUS.SUBMITTED) => this.props.handleSubmit(values => {
    const isUpdate = this.ID && !this.duplicated;
    const request = isUpdate ? req.patch(`/${this.ruleApiRouteType}/rules/${this.ID}`, this.formatValues(values, status), { baseURL: '/api/v2' }) : req.post(`/${this.ruleApiRouteType}/rules`, this.formatValues(values, status), { baseURL: '/api/v2' });
    request.then(res => {
      this.onActionSuccess(res.data, status === STATUS.SUBMITTED ? 'submitted' : 'saved');
      if (!isUpdate) {
        this.props.history.replace(`/campaigns/${this.type}/${res.data.id}`);
      }
    });
  });

  approve = (approved = true) => () => {
    req.patch(`/${this.ruleApiRouteType}/rules/${this.ID}`, { status: approved ? STATUS.REVIEWED : STATUS.DRAFT }, { baseURL: '/api/v2' })
      .then(res => {
        this.onActionSuccess(res.data, approved ? 'approved' : 'rejected');
      });
  };

  publish = (approved = true) => () => {
    req.patch(`/${this.ruleApiRouteType}/rules/${this.ID}`, { status: approved ? STATUS.PUBLISHED : STATUS.DRAFT }, { baseURL: '/api/v2' })
      .then(res => {
        this.onActionSuccess(res.data, approved ? 'published' : 'rejected');
      });
  };

  terminate = () => {
    req.patch(`/${this.ruleApiRouteType}/rules/${this.ID}`, { status: STATUS.TERMINATED }, { baseURL: '/api/v2' })
      .then(res => {
        this.onActionSuccess(res.data, 'terminated');
      });
  };

  delete = () => {
    req.delete(`/${this.ruleApiRouteType}/rules/${this.ID}`, { baseURL: '/api/v2' })
      .then(() => {
        this.makeToast(this.props.formValues.name, 'deleted');
        this.goBack();
      });
  };

  deleteContent = () => {
    this.props.updateDetails({
      content_id: undefined,
    });
  };

  downloadXML = () => {
    window.open(`/api/v2/vignette/rules/${this.ID}/xml`, '_blank');
  }

  render() {
    return this.state.ready &&
      this.props.containers &&
      this.props.pages.items
      ? <Details
        id={this.ID}
        status={this.status}
        formChange={this.props.formChange}
        formValues={this.props.formValues}
        onSubmit={this.submit}
        onApprove={this.approve}
        onPublish={this.publish}
        onTerminate={this.terminate}
        onCancel={this.goBack}
        onDuplicate={this.duplicate}
        onDelete={this.delete}
        title={this.title}
        deleteContent={this.deleteContent}
        downloadXML={this.downloadXML}
        openFragmentModal={this.props.openFragmentModalAndLoadFragments}
        selectedWebFragment={this.props.selectedWebFragment}
        updateDetails={this.props.updateDetails}
        type={this.ruleApiRouteType}
        access={this.props.access}
        permissions={this.props.permissions}
        containers={this.props.containers}
        pages={this.props.pages.items}
        metadata={this.state.metadata}/> : null;
  }
}

export const validate = (values, props) => {
  const errors = {};
  // validation logic
  if (!values.name) {
    errors.name = 'You must enter a name for this campaign.';
  }
  if (!values.start_date || !moment(values.start_date, DATE_ISO_FORMAT, true).isValid()) {
    errors.start_date = `You must enter a valid date (MM/DD/YYYY HH:MM AM/PM).`;
  }
  if (!values.end_date || !moment(values.end_date, DATE_ISO_FORMAT, true).isValid()) {
    errors.end_date = `You must enter a valid date (MM/DD/YYYY HH:MM AM/PM).`;
  }
  if (values.start_date && values.end_date && moment(values.end_date).isBefore(values.start_date)) {
    errors.end_date = 'You must enter a publication End date later than the Start date.';
  }

  if (values.type === 'targeted' && !values.campaign_name) {
    errors.campaign_name = 'You must enter a campaign ID.';
  }
  if (!values.language) {
    errors.language = 'You must select a language.';
  }
  if (!values.container) {
    errors.container = 'You must select a container.';
  }
  if (values.pages && values.pages.length === 0) {
    errors.pages = 'You must select at least one page.';
  }
  if (props.containers &&
    props.containers[values.container] &&
    [
      ruleTypes.VIGNETTE_BROADCAST,
      ruleTypes.VIGNETTE_PRIORITY,
    ].includes(props.containers[values.container].rule_type) &&
    !values.subject_line) {
    errors.subject_line = 'You must enter a subject line';
  }
  if (!values.content_id) {
    errors.content_id = 'You must select a web fragment.';
  }

  if (values.BU && values.BU.length && !values.BU_RADIO) {
    errors.BU_RADIO = 'You must select AND/OR';
  }
  if (values.REGS && values.REGS.length && !values.REGS_RADIO) {
    errors.REGS_RADIO = 'You must select AND/OR';
  }
  if (((values.PR1 && values.PR1.length) ||
    (values.PR2 && values.PR2.length) ||
    (values.PR3 && values.PR3.length) ||
    (values.PR4 && values.PR4.length)) &&
    !values.PR_RADIO) {
    errors.PR_RADIO = 'You must select AND/OR';
  }
  return errors;
};

const mapVignetteStateToProps = state => ({
  formValues: state.form.vignetteDetails.values,
  access: state.authenticated.access?.campaigns || {},
  permissions: state.authenticated.permissions,
  containers: state.containers.items ? mapPropToKey(Object.values(state.containers.items), 'containerId') : {},
  pages: state.pages,
  selectedWebFragment: state.content.webFragments && state.content.webFragments.selected,
});

const mapVignetteDispatchToProps = dispatch => bindActionCreators({
  // TODO
  populateDetails: populateVignetteDetails,
  updateDetails: updateVignetteDetails,
  openFragmentModalAndLoadFragments,
  addSnackbar,
  getContainers,
  getPages,
  getWebFragmentMetadata,
  formChange: (formName, formField, formValue) => change(formName, formField, formValue),
}, dispatch);

@connect(mapVignetteStateToProps, mapVignetteDispatchToProps)
@reduxForm({
  form: 'vignetteDetails',
  validate,
  initialValues: {
    BU_RADIO: 'BU_OR',
    REGS_RADIO: 'REGS_OR',
    PR_RADIO: 'PR_OR',
    DEVC_RADIO: 'DEVC_OR',
  },
})
export class VignetteDetails extends DetailsContainer { }
