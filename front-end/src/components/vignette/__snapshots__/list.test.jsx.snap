// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`List Container No Access Snapshot 1`] = `
<M
  gridMargins={{}}
  isScreenWidthVariant={false}
  separatedLines={false}
  statusBadgeText="New"
  type="error"
>
  You do not have permissions to view this page
</M>
`;

exports[`List Container Snapshot 1`] = `
<List
  access={
    {
      "containers": {
        "sol": {
          "manage": [
            "LoginSecurityCentre",
          ],
          "view": [
            "LoginSecurityCentre",
          ],
        },
        "storefront": {
          "manage": [
            "sf-marquee-bankingaccounts",
          ],
          "view": [
            "sf-marquee-bankingaccounts",
          ],
        },
      },
      "pages": {
        "sol": {
          "manage": [
            "Login",
          ],
          "view": [
            "Login",
          ],
        },
        "storefront": {
          "manage": [
            "sf-accessibility",
          ],
          "view": [
            "sf-accessibility",
          ],
        },
      },
      "ruleSubTypes": {
        "sol": {
          "manage": [
            "targeted",
            "mass",
          ],
          "view": [
            "targeted",
            "mass",
          ],
        },
        "storefront": {
          "manage": [
            "targeted",
            "mass",
          ],
          "view": [
            "targeted",
            "mass",
          ],
        },
      },
    }
  }
  campaignIdFilterChanged={[Function]}
  clearAllClicked={[Function]}
  createClicked={[Function]}
  createdByClicked={[Function]}
  listings={
    {
      "data": {
        "items": [
          {
            "app_version": null,
            "container": null,
            "content_id": null,
            "content_space": null,
            "content_type": null,
            "created_by": null,
            "disabled": false,
            "end_date": "2038-07-30T23:59:59.000Z",
            "id": "PtD8ZEPFpLcj",
            "name": "Sample rule #1",
            "platforms": [],
            "start_date": "2018-07-13T00:00:00.000Z",
            "status": "draft",
            "targeting": {
              "campaign_name": "hiyo",
            },
            "updated_at": "2018-07-13T00:00:00.000Z",
            "updated_by": null,
          },
          {
            "app_version": null,
            "container": null,
            "content_id": null,
            "content_space": null,
            "content_type": null,
            "created_by": null,
            "disabled": true,
            "end_date": "2018-08-30T23:59:59.000Z",
            "id": "jhrotTqfCeiN",
            "name": "Sample rule #2",
            "platforms": [],
            "start_date": "2018-07-15T00:00:00.000Z",
            "status": "draft",
            "targeting": {
              "campaign_name": "hiyo",
            },
            "updated_at": "2018-07-15T00:00:00.000Z",
            "updated_by": null,
          },
          {
            "app_version": null,
            "container": null,
            "content_id": null,
            "content_space": null,
            "content_type": null,
            "created_by": null,
            "disabled": false,
            "end_date": "2018-08-30T23:59:59.000Z",
            "id": "uVEPJeHe0hTr",
            "name": "Sample rule #3",
            "platforms": [],
            "start_date": "2018-08-01T00:00:00.000Z",
            "status": "inactive",
            "targeting": {
              "campaign_name": "hiyo",
            },
            "updated_at": "2018-08-01T00:00:00.000Z",
            "updated_by": null,
          },
          {
            "app_version": null,
            "container": null,
            "content_id": null,
            "content_space": null,
            "content_type": null,
            "created_by": null,
            "disabled": false,
            "end_date": "2018-08-30T23:59:59.000Z",
            "id": "LFPvqZR0EDZ2",
            "name": "Sample rule #4",
            "platforms": [],
            "start_date": "2018-08-01T00:00:00.000Z",
            "status": "published",
            "targeting": {
              "campaign_name": "hiyo",
            },
            "updated_at": "2018-08-01T00:00:00.000Z",
            "updated_by": null,
          },
        ],
        "limit": 50,
        "offset": 0,
        "total": 4,
      },
    }
  }
  location={
    {
      "search": "",
    }
  }
  nameUpdated={[Function]}
  pageClicked={[Function]}
  permissions={
    {
      "campaigns_manage": true,
      "campaigns_view": true,
    }
  }
  query={
    {
      "page": 1,
      "sort": "-updated_at",
    }
  }
  sortClicked={[Function]}
  statusClicked={[Function]}
  type="sol"
  updatedByClicked={[Function]}
  users={
    {
      "items": {
        "1": {
          "name": "First Last",
          "sid": "s1234567",
        },
        "2": {
          "name": "A B",
          "sid": "s7654321",
        },
      },
    }
  }
/>
`;

exports[`List Expand search 1`] = `
<div
  className="admin-list"
>
  <div
    className="admin-list__action-bar"
  >
    <f
      bold={false}
      className="admin-list__header"
      color="black"
      component="h1"
      italic={false}
    >
      Manage 
      SOL Campaigns
    </f>
    <d
      buttonType="button"
      disabled={false}
      invertedFocusState="blue"
      onClick={
        [MockFunction] {
          "calls": [
            [],
          ],
          "results": [
            {
              "type": "return",
              "value": undefined,
            },
          ],
        }
      }
      type="caution"
    >
      Create a new 
      SOL Campaign
    </d>
  </div>
  <f
    className="admin-list__filter"
    lgPadding={36}
    mdPadding={30}
    smPadding={24}
    type="flatSolid"
    xsPadding={18}
  >
    <div
      className="admin-list__filter-accordion"
    >
      <div
        className="admin-list__search"
      >
        <v
          className={null}
          clearButtonLabel="Clear search"
          disabled={false}
          error={null}
          id="name"
          inputRef={null}
          label="Search"
          onChange={[Function]}
          onClear={null}
          onSearchClick={null}
          searchButtonLabel="Search"
          secondaryLabel={null}
          showLabel={false}
          tooltip={null}
          value=""
          variant="default"
        />
      </div>
      <button
        aria-controls="filter-section"
        aria-expanded={true}
        aria-label="Hide filtering options"
        className="admin-list__accordion-button"
        onClick={[Function]}
      >
        <Component />
      </button>
    </div>
    <fieldset
      className="admin-filter admin-list__filter-options"
    >
      <f
        color="black"
        component="h3"
        italic={false}
        type="1"
      >
        Filters
      </f>
      <div
        className="admin-filter__filter-container"
      >
        <div
          className="admin-filter__item"
        >
          <m
            id="status-filter"
            isLabelVisible={true}
            label="Status"
            name=""
            onChange={[MockFunction]}
            placeholder="Status"
            tooltip={null}
            value="all"
            withBackground={false}
          >
            <option
              value="all"
            >
              All
            </option>
            <option
              key="draft"
              value="draft"
            >
              Draft
            </option>
            <option
              key="submitted"
              value="submitted"
            >
              Submitted
            </option>
            <option
              key="reviewed"
              value="reviewed"
            >
              Reviewed
            </option>
            <option
              key="published"
              value="published"
            >
              Published
            </option>
            <option
              key="terminated"
              value="terminated"
            >
              Terminated
            </option>
          </m>
        </div>
        <div
          className="admin-filter__item"
        >
          <m
            id="created-by-filter"
            isLabelVisible={true}
            label="Created by"
            name=""
            onChange={[MockFunction]}
            placeholder="Created by"
            tooltip={null}
            value="all"
            withBackground={false}
          >
            <option
              value="all"
            >
              All
            </option>
            <option
              key="s7654321"
              value="s7654321"
            >
              A B
            </option>
            <option
              key="s1234567"
              value="s1234567"
            >
              First Last
            </option>
          </m>
        </div>
        <div
          className="admin-filter__item"
        >
          <m
            id="updated-by-filter"
            isLabelVisible={true}
            label="Last updated by"
            name=""
            onChange={[MockFunction]}
            placeholder="Last updated by"
            tooltip={null}
            value="all"
            withBackground={false}
          >
            <option
              value="all"
            >
              All
            </option>
            <option
              key="s7654321"
              value="s7654321"
            >
              A B
            </option>
            <option
              key="s1234567"
              value="s1234567"
            >
              First Last
            </option>
          </m>
        </div>
        <div
          className="admin-filter__item"
        >
          <m
            disabled={false}
            error={null}
            icon={null}
            id="filter-campaign_id"
            inputRef={null}
            label="Campaign ID"
            name="CampaignID-Filter"
            onBlur={[Function]}
            onChange={[Function]}
            placeholder="Enter Campaign ID"
            readOnly={false}
            renderProp={null}
            secondaryLabel={null}
            supplementaryText={null}
            tooltip={null}
            value=""
            withBackground={false}
          />
        </div>
        <ForwardRef
          Icon={[Function]}
          className="admin-filter__item admin-filter__clear-button"
          color="blue"
          iconPosition="left"
          onClick={[MockFunction]}
          weight="bold"
        >
          Clear All
        </ForwardRef>
      </div>
    </fieldset>
  </f>
  <ForwardRef
    className="admin-list__listings-table"
    columnFixed={false}
    columns={
      [
        {
          "cellFormatter": [Function],
          "grow": 2,
          "initialSortDirection": 0,
          "name": "Campaign name",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
          "style": {
            "textAlign": "left",
          },
        },
        {
          "cellFormatter": [Function],
          "initialSortDirection": 0,
          "name": "Start date",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
        },
        {
          "cellFormatter": [Function],
          "initialSortDirection": 0,
          "name": "End date",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
        },
        {
          "cellFormatter": [Function],
          "name": "Campaign ID",
          "selector": "",
        },
        {
          "name": "Created by",
          "selector": "created_by",
        },
        {
          "name": "Last updated by",
          "selector": "updated_by",
        },
        {
          "cellFormatter": [Function],
          "initialSortDirection": 0,
          "name": "Status",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
        },
        {
          "cellFormatter": [Function],
          "initialSortDirection": 0,
          "name": "Last updated at",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
        },
      ]
    }
    data={
      [
        {
          "app_version": null,
          "container": null,
          "content_id": null,
          "content_space": null,
          "content_type": null,
          "created_by": null,
          "disabled": false,
          "end_date": "2038-07-30T23:59:59.000Z",
          "id": "PtD8ZEPFpLcj",
          "name": "Sample rule #1",
          "platforms": [],
          "start_date": "2018-07-13T00:00:00.000Z",
          "status": "draft",
          "targeting": {
            "campaign_name": "hiyo",
          },
          "updated_at": "2018-07-13T00:00:00.000Z",
          "updated_by": null,
        },
        {
          "app_version": null,
          "container": null,
          "content_id": null,
          "content_space": null,
          "content_type": null,
          "created_by": null,
          "disabled": true,
          "end_date": "2018-08-30T23:59:59.000Z",
          "id": "jhrotTqfCeiN",
          "name": "Sample rule #2",
          "platforms": [],
          "start_date": "2018-07-15T00:00:00.000Z",
          "status": "draft",
          "targeting": {
            "campaign_name": "hiyo",
          },
          "updated_at": "2018-07-15T00:00:00.000Z",
          "updated_by": null,
        },
        {
          "app_version": null,
          "container": null,
          "content_id": null,
          "content_space": null,
          "content_type": null,
          "created_by": null,
          "disabled": false,
          "end_date": "2018-08-30T23:59:59.000Z",
          "id": "uVEPJeHe0hTr",
          "name": "Sample rule #3",
          "platforms": [],
          "start_date": "2018-08-01T00:00:00.000Z",
          "status": "inactive",
          "targeting": {
            "campaign_name": "hiyo",
          },
          "updated_at": "2018-08-01T00:00:00.000Z",
          "updated_by": null,
        },
        {
          "app_version": null,
          "container": null,
          "content_id": null,
          "content_space": null,
          "content_type": null,
          "created_by": null,
          "disabled": false,
          "end_date": "2018-08-30T23:59:59.000Z",
          "id": "LFPvqZR0EDZ2",
          "name": "Sample rule #4",
          "platforms": [],
          "start_date": "2018-08-01T00:00:00.000Z",
          "status": "published",
          "targeting": {
            "campaign_name": "hiyo",
          },
          "updated_at": "2018-08-01T00:00:00.000Z",
          "updated_by": null,
        },
      ]
    }
    defaultSortOrder="desc"
    highlightOnHover={false}
    id="legacy-listing-table"
    resetSortOnDataChange={false}
    size="small"
    striped={false}
    title=""
    titleSize={21}
    translateHeaderLabels={[Function]}
  />
</div>
`;

exports[`List Loading snapshot 1`] = `
<div
  className="admin-list"
>
  <div
    className="admin-list__action-bar"
  >
    <f
      bold={false}
      className="admin-list__header"
      color="black"
      component="h1"
      italic={false}
    >
      Manage 
      SOL Campaigns
      <s
        color="inherit"
        size={24}
      />
    </f>
  </div>
  <f
    className="admin-list__filter"
    lgPadding={36}
    mdPadding={30}
    smPadding={24}
    type="flatSolid"
    xsPadding={18}
  >
    <div
      className="admin-list__filter-accordion"
    >
      <div
        className="admin-list__search"
      >
        <v
          className={null}
          clearButtonLabel="Clear search"
          disabled={false}
          error={null}
          id="name"
          inputRef={null}
          label="Search"
          onChange={[Function]}
          onClear={null}
          onSearchClick={null}
          searchButtonLabel="Search"
          secondaryLabel={null}
          showLabel={false}
          tooltip={null}
          value=""
          variant="default"
        />
      </div>
      <button
        aria-controls="filter-section"
        aria-expanded={false}
        aria-label="Show filtering options "
        className="admin-list__accordion-button"
        onClick={[Function]}
      >
        <Component />
      </button>
    </div>
  </f>
  <ForwardRef
    className="admin-list__listings-table"
    columnFixed={false}
    columns={
      [
        {
          "cellFormatter": [Function],
          "grow": 2,
          "initialSortDirection": 0,
          "name": "Campaign name",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
          "style": {
            "textAlign": "left",
          },
        },
        {
          "cellFormatter": [Function],
          "initialSortDirection": 0,
          "name": "Start date",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
        },
        {
          "cellFormatter": [Function],
          "initialSortDirection": 0,
          "name": "End date",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
        },
        {
          "cellFormatter": [Function],
          "name": "Campaign ID",
          "selector": "",
        },
        {
          "name": "Created by",
          "selector": "created_by",
        },
        {
          "name": "Last updated by",
          "selector": "updated_by",
        },
        {
          "cellFormatter": [Function],
          "initialSortDirection": 0,
          "name": "Status",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
        },
        {
          "cellFormatter": [Function],
          "initialSortDirection": 0,
          "name": "Last updated at",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
        },
      ]
    }
    data={[]}
    defaultSortOrder="desc"
    highlightOnHover={false}
    id="legacy-listing-table"
    resetSortOnDataChange={false}
    size="small"
    striped={false}
    title=""
    titleSize={21}
    translateHeaderLabels={[Function]}
  />
</div>
`;

exports[`List Snapshot 1`] = `
<div
  className="admin-list"
>
  <div
    className="admin-list__action-bar"
  >
    <f
      bold={false}
      className="admin-list__header"
      color="black"
      component="h1"
      italic={false}
    >
      Manage 
      SOL Campaigns
    </f>
    <d
      buttonType="button"
      disabled={false}
      invertedFocusState="blue"
      onClick={[MockFunction]}
      type="caution"
    >
      Create a new 
      SOL Campaign
    </d>
  </div>
  <f
    className="admin-list__filter"
    lgPadding={36}
    mdPadding={30}
    smPadding={24}
    type="flatSolid"
    xsPadding={18}
  >
    <div
      className="admin-list__filter-accordion"
    >
      <div
        className="admin-list__search"
      >
        <v
          className={null}
          clearButtonLabel="Clear search"
          disabled={false}
          error={null}
          id="name"
          inputRef={null}
          label="Search"
          onChange={[Function]}
          onClear={null}
          onSearchClick={null}
          searchButtonLabel="Search"
          secondaryLabel={null}
          showLabel={false}
          tooltip={null}
          value=""
          variant="default"
        />
      </div>
      <button
        aria-controls="filter-section"
        aria-expanded={false}
        aria-label="Show filtering options "
        className="admin-list__accordion-button"
        onClick={[Function]}
      >
        <Component />
      </button>
    </div>
  </f>
  <ForwardRef
    className="admin-list__listings-table"
    columnFixed={false}
    columns={
      [
        {
          "cellFormatter": [Function],
          "grow": 2,
          "initialSortDirection": 0,
          "name": "Campaign name",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
          "style": {
            "textAlign": "left",
          },
        },
        {
          "cellFormatter": [Function],
          "initialSortDirection": 0,
          "name": "Start date",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
        },
        {
          "cellFormatter": [Function],
          "initialSortDirection": 0,
          "name": "End date",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
        },
        {
          "cellFormatter": [Function],
          "name": "Campaign ID",
          "selector": "",
        },
        {
          "name": "Created by",
          "selector": "created_by",
        },
        {
          "name": "Last updated by",
          "selector": "updated_by",
        },
        {
          "cellFormatter": [Function],
          "initialSortDirection": 0,
          "name": "Status",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
        },
        {
          "cellFormatter": [Function],
          "initialSortDirection": 0,
          "name": "Last updated at",
          "overrideSortBehaviour": [Function],
          "selector": "",
          "sortable": true,
        },
      ]
    }
    data={
      [
        {
          "app_version": null,
          "container": null,
          "content_id": null,
          "content_space": null,
          "content_type": null,
          "created_by": null,
          "disabled": false,
          "end_date": "2038-07-30T23:59:59.000Z",
          "id": "PtD8ZEPFpLcj",
          "name": "Sample rule #1",
          "platforms": [],
          "start_date": "2018-07-13T00:00:00.000Z",
          "status": "draft",
          "targeting": {
            "campaign_name": "hiyo",
          },
          "updated_at": "2018-07-13T00:00:00.000Z",
          "updated_by": null,
        },
        {
          "app_version": null,
          "container": null,
          "content_id": null,
          "content_space": null,
          "content_type": null,
          "created_by": null,
          "disabled": true,
          "end_date": "2018-08-30T23:59:59.000Z",
          "id": "jhrotTqfCeiN",
          "name": "Sample rule #2",
          "platforms": [],
          "start_date": "2018-07-15T00:00:00.000Z",
          "status": "draft",
          "targeting": {
            "campaign_name": "hiyo",
          },
          "updated_at": "2018-07-15T00:00:00.000Z",
          "updated_by": null,
        },
        {
          "app_version": null,
          "container": null,
          "content_id": null,
          "content_space": null,
          "content_type": null,
          "created_by": null,
          "disabled": false,
          "end_date": "2018-08-30T23:59:59.000Z",
          "id": "uVEPJeHe0hTr",
          "name": "Sample rule #3",
          "platforms": [],
          "start_date": "2018-08-01T00:00:00.000Z",
          "status": "inactive",
          "targeting": {
            "campaign_name": "hiyo",
          },
          "updated_at": "2018-08-01T00:00:00.000Z",
          "updated_by": null,
        },
        {
          "app_version": null,
          "container": null,
          "content_id": null,
          "content_space": null,
          "content_type": null,
          "created_by": null,
          "disabled": false,
          "end_date": "2018-08-30T23:59:59.000Z",
          "id": "LFPvqZR0EDZ2",
          "name": "Sample rule #4",
          "platforms": [],
          "start_date": "2018-08-01T00:00:00.000Z",
          "status": "published",
          "targeting": {
            "campaign_name": "hiyo",
          },
          "updated_at": "2018-08-01T00:00:00.000Z",
          "updated_by": null,
        },
      ]
    }
    defaultSortOrder="desc"
    highlightOnHover={false}
    id="legacy-listing-table"
    resetSortOnDataChange={false}
    size="small"
    striped={false}
    title=""
    titleSize={21}
    translateHeaderLabels={[Function]}
  />
</div>
`;
