// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Details Snapshot Snapshot 1`] = `
<div
  className="admin-details"
>
  <form>
    <div
      className="admin-details__action-bar"
    >
      <f
        bold={false}
        className="admin-details__header"
        color="black"
        component="h1"
        italic={false}
      >
        title
      </f>
      <div>
        <div
          className="admin-details__action-buttons"
        />
      </div>
    </div>
    <div />
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Information
      </f>
      <Field
        autoComplete="off"
        className="admin-details__field"
        component={[Function]}
        disabled={false}
        label="Campaign Name"
        name="name"
        placeholder="Campaign Name"
        required={true}
        validate={
          [
            [Function],
            [Function],
          ]
        }
      />
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Type
      </f>
      <d
        className={null}
        disabled={false}
        error={null}
        form={null}
        id="campaign-type-group"
        inline={false}
        inputRef={null}
        labelLeft={false}
        legend=""
        name="type"
        tooltip={null}
      >
        <div
          className="admin-details__radio-container"
        >
          <Field
            component={[Function]}
            disabled={false}
            id="targeted"
            label="Targeted Campaign"
            name="type"
          />
          <Field
            component={[Function]}
            disabled={false}
            id="mass"
            label="Mass Campaign"
            name="type"
          />
        </div>
      </d>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Targeting Dimension
      </f>
      <div
        className="admin-details__date-fields"
      >
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={false}
          label="Start date"
          name="start_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={false}
          label="End date"
          name="end_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
      </div>
      <Field
        className="admin-details__field"
        component={[Function]}
        name="language"
      >
        <div
          className="admin-details__radio-container"
        >
          <span
            className="input-group__legend"
          >
            Language
          </span>
          <Field
            component={[Function]}
            disabled={false}
            id="en"
            label="EN"
            name="language"
          />
          <Field
            component={[Function]}
            disabled={false}
            id="fr"
            label="FR"
            name="language"
          />
        </div>
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Container and Pages
      </f>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="container"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "containerId": "LoginSecurityCentre",
                "id": 1,
                "name": "LoginSecurityCentre",
                "pages": [
                  1,
                ],
                "rule_type": "vignette",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "containerId",
              "valueName": "name",
            }
          }
          editable={true}
          hideSuggestionsAfterSelection={true}
          initialSelection={
            [
              undefined,
            ]
          }
          label="Container Name"
          limit={1}
          onChange={[Function]}
          placeholder="Select a container…"
        />
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Code Fragment
      </f>
      <Field
        className="admin-details__field"
        component={[Function]}
        name="content_id"
      >
        <ForwardRef
          Icon={[Function]}
          className={null}
          color="blue"
          iconPosition="left"
          onClick={[Function]}
          type="button"
          weight="bold"
        >
          Search and add web fragment
        </ForwardRef>
      </Field>
    </f>
    <div
      className="admin-details__action-bar"
    >
      <f
        onClick={[MockFunction]}
        type="button"
      >
        Cancel
      </f>
      <div
        className="admin-details__action-buttons"
      >
        <d
          className="admin-details__action-button"
          disabled={false}
          labelPadding={36}
          onClick={null}
          size="regular"
        >
          Save as Draft
        </d>
        <d
          className="admin-details__action-button"
          disabled={false}
          labelPadding={36}
          onClick={null}
          size="regular"
        >
          Submit for Review
        </d>
      </div>
    </div>
  </form>
  <g
    alwaysStackButtons={false}
    headline="Are you sure?"
    isModalVisible={false}
    primaryAction={[Function]}
    primaryButtonLabel="Delete"
    secondaryAction={[Function]}
    secondaryButtonLabel="Cancel"
    setModalVisible={[Function]}
    usePrimaryButtons={false}
    width="wide"
  >
    This 
    SOL Campaign
     will be 
    .
  </g>
  <Connect(ReduxForm)
    type="vignette"
  />
</div>
`;

exports[`Details Snapshot insufficient permissions banner 1`] = `
<M
  gridMargins={{}}
  isScreenWidthVariant={false}
  separatedLines={false}
  statusBadgeText="New"
  type="error"
>
  You do not have permissions to view this page
</M>
`;

exports[`Details Snapshot snap no status 1`] = `
<div
  className="admin-details"
>
  <form>
    <div
      className="admin-details__action-bar"
    >
      <f
        bold={false}
        className="admin-details__header"
        color="black"
        component="h1"
        italic={false}
      >
        title
      </f>
      <div>
        <div
          className="admin-details__action-buttons"
        />
      </div>
    </div>
    <div />
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Information
      </f>
      <Field
        autoComplete="off"
        className="admin-details__field"
        component={[Function]}
        disabled={false}
        label="Campaign Name"
        name="name"
        placeholder="Campaign Name"
        required={true}
        validate={
          [
            [Function],
            [Function],
          ]
        }
      />
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Type
      </f>
      <d
        className={null}
        disabled={false}
        error={null}
        form={null}
        id="campaign-type-group"
        inline={false}
        inputRef={null}
        labelLeft={false}
        legend=""
        name="type"
        tooltip={null}
      >
        <div
          className="admin-details__radio-container"
        >
          <Field
            component={[Function]}
            disabled={false}
            id="targeted"
            label="Targeted Campaign"
            name="type"
          />
          <Field
            component={[Function]}
            disabled={false}
            id="mass"
            label="Mass Campaign"
            name="type"
          />
        </div>
      </d>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Targeting Dimension
      </f>
      <div
        className="admin-details__date-fields"
      >
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={false}
          label="Start date"
          name="start_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={false}
          label="End date"
          name="end_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
      </div>
      <Field
        className="admin-details__field"
        component={[Function]}
        name="language"
      >
        <div
          className="admin-details__radio-container"
        >
          <span
            className="input-group__legend"
          >
            Language
          </span>
          <Field
            component={[Function]}
            disabled={false}
            id="en"
            label="EN"
            name="language"
          />
          <Field
            component={[Function]}
            disabled={false}
            id="fr"
            label="FR"
            name="language"
          />
        </div>
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Container and Pages
      </f>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="container"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "containerId": "LoginSecurityCentre",
                "id": 1,
                "name": "LoginSecurityCentre",
                "pages": [
                  1,
                ],
                "rule_type": "vignette",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "containerId",
              "valueName": "name",
            }
          }
          editable={true}
          hideSuggestionsAfterSelection={true}
          initialSelection={
            [
              undefined,
            ]
          }
          label="Container Name"
          limit={1}
          onChange={[Function]}
          placeholder="Select a container…"
        />
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Code Fragment
      </f>
      <Field
        className="admin-details__field"
        component={[Function]}
        name="content_id"
      >
        <ForwardRef
          Icon={[Function]}
          className={null}
          color="blue"
          iconPosition="left"
          onClick={[Function]}
          type="button"
          weight="bold"
        >
          Search and add web fragment
        </ForwardRef>
      </Field>
    </f>
    <div
      className="admin-details__action-bar"
    >
      <f
        onClick={[MockFunction]}
        type="button"
      >
        Cancel
      </f>
      <div
        className="admin-details__action-buttons"
      >
        <d
          className="admin-details__action-button"
          disabled={false}
          labelPadding={36}
          onClick={null}
          size="regular"
        >
          Save as Draft
        </d>
        <d
          className="admin-details__action-button"
          disabled={false}
          labelPadding={36}
          onClick={null}
          size="regular"
        >
          Submit for Review
        </d>
      </div>
    </div>
  </form>
  <g
    alwaysStackButtons={false}
    headline="Are you sure?"
    isModalVisible={false}
    primaryAction={[Function]}
    primaryButtonLabel="Delete"
    secondaryAction={[Function]}
    secondaryButtonLabel="Cancel"
    setModalVisible={[Function]}
    usePrimaryButtons={false}
    width="wide"
  >
    This 
    SOL Campaign
     will be 
    .
  </g>
  <Connect(ReduxForm)
    type="vignette"
  />
</div>
`;

exports[`Details Snapshot snap status inactive 1`] = `
<div
  className="admin-details"
>
  <form>
    <div
      className="admin-details__action-bar"
    >
      <f
        bold={false}
        className="admin-details__header"
        color="black"
        component="h1"
        italic={false}
      >
        title
      </f>
      <div>
        <div
          className="admin-details__action-buttons"
        >
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Duplicate
          </d>
        </div>
      </div>
    </div>
    <div>
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Current Status:
        <StatusBadge
          status="inactive"
        />
      </f>
    </div>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Information
      </f>
      <Field
        autoComplete="off"
        className="admin-details__field"
        component={[Function]}
        disabled={true}
        label="Campaign Name"
        name="name"
        placeholder="Campaign Name"
        required={true}
        validate={
          [
            [Function],
            [Function],
          ]
        }
      />
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Type
      </f>
      <d
        className={null}
        disabled={false}
        error={null}
        form={null}
        id="campaign-type-group"
        inline={false}
        inputRef={null}
        labelLeft={false}
        legend=""
        name="type"
        tooltip={null}
      >
        <div
          className="admin-details__radio-container"
        >
          <Field
            component={[Function]}
            disabled={true}
            id="targeted"
            label="Targeted Campaign"
            name="type"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="mass"
            label="Mass Campaign"
            name="type"
          />
        </div>
      </d>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Targeting Dimension
      </f>
      <div
        className="admin-details__date-fields"
      >
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={true}
          label="Start date"
          name="start_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={true}
          label="End date"
          name="end_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
      </div>
      <Field
        className="admin-details__field"
        component={[Function]}
        name="language"
      >
        <div
          className="admin-details__radio-container"
        >
          <span
            className="input-group__legend"
          >
            Language
          </span>
          <Field
            component={[Function]}
            disabled={true}
            id="en"
            label="EN"
            name="language"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="fr"
            label="FR"
            name="language"
          />
        </div>
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Container and Pages
      </f>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="container"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "containerId": "LoginSecurityCentre",
                "id": 1,
                "name": "LoginSecurityCentre",
                "pages": [
                  1,
                ],
                "rule_type": "vignette",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "containerId",
              "valueName": "name",
            }
          }
          editable={false}
          hideSuggestionsAfterSelection={true}
          initialSelection={
            [
              undefined,
            ]
          }
          label="Container Name"
          limit={1}
          onChange={[Function]}
          placeholder="Select a container…"
        />
      </Field>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="pages"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "code": "Login",
                "description": "Login",
              },
              {
                "code": "Logout",
                "description": "Logout",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={false}
          hideSuggestionsAfterSelection={true}
          label="Page Name"
          onChange={[Function]}
          placeholder="Pages"
        />
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Code Fragment
      </f>
      <f
        bold={false}
        color="black"
        component="p"
        italic={false}
        numeric={false}
      >
        No web fragment attached
      </f>
    </f>
    <div
      className="admin-details__action-bar"
    >
      <f
        onClick={[MockFunction]}
        type="button"
      >
        Cancel
      </f>
      <div
        className="admin-details__action-buttons"
      />
    </div>
  </form>
  <g
    alwaysStackButtons={false}
    headline="Are you sure?"
    isModalVisible={false}
    primaryAction={[Function]}
    primaryButtonLabel="Delete"
    secondaryAction={[Function]}
    secondaryButtonLabel="Cancel"
    setModalVisible={[Function]}
    usePrimaryButtons={false}
    width="wide"
  >
    This 
    SOL Campaign
     will be 
    .
  </g>
  <Connect(ReduxForm)
    type="vignette"
  />
</div>
`;

exports[`Details Snapshot snap status published 1`] = `
<div
  className="admin-details"
>
  <form>
    <div
      className="admin-details__action-bar"
    >
      <f
        bold={false}
        className="admin-details__header"
        color="black"
        component="h1"
        italic={false}
      >
        title
      </f>
      <div>
        <div
          className="admin-details__action-buttons"
        >
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Duplicate
          </d>
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Terminate
          </d>
        </div>
      </div>
    </div>
    <div>
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Current Status:
        <StatusBadge
          status="published"
        />
      </f>
    </div>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Information
      </f>
      <Field
        autoComplete="off"
        className="admin-details__field"
        component={[Function]}
        disabled={true}
        label="Campaign Name"
        name="name"
        placeholder="Campaign Name"
        required={true}
        validate={
          [
            [Function],
            [Function],
          ]
        }
      />
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Type
      </f>
      <d
        className={null}
        disabled={false}
        error={null}
        form={null}
        id="campaign-type-group"
        inline={false}
        inputRef={null}
        labelLeft={false}
        legend=""
        name="type"
        tooltip={null}
      >
        <div
          className="admin-details__radio-container"
        >
          <Field
            component={[Function]}
            disabled={true}
            id="targeted"
            label="Targeted Campaign"
            name="type"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="mass"
            label="Mass Campaign"
            name="type"
          />
        </div>
      </d>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Targeting Dimension
      </f>
      <div
        className="admin-details__date-fields"
      >
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={true}
          label="Start date"
          name="start_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={true}
          label="End date"
          name="end_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
      </div>
      <Field
        className="admin-details__field"
        component={[Function]}
        name="language"
      >
        <div
          className="admin-details__radio-container"
        >
          <span
            className="input-group__legend"
          >
            Language
          </span>
          <Field
            component={[Function]}
            disabled={true}
            id="en"
            label="EN"
            name="language"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="fr"
            label="FR"
            name="language"
          />
        </div>
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Container and Pages
      </f>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="container"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "containerId": "LoginSecurityCentre",
                "id": 1,
                "name": "LoginSecurityCentre",
                "pages": [
                  1,
                ],
                "rule_type": "vignette",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "containerId",
              "valueName": "name",
            }
          }
          editable={false}
          hideSuggestionsAfterSelection={true}
          initialSelection={
            [
              undefined,
            ]
          }
          label="Container Name"
          limit={1}
          onChange={[Function]}
          placeholder="Select a container…"
        />
      </Field>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="pages"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "code": "Login",
                "description": "Login",
              },
              {
                "code": "Logout",
                "description": "Logout",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={false}
          hideSuggestionsAfterSelection={true}
          label="Page Name"
          onChange={[Function]}
          placeholder="Pages"
        />
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Code Fragment
      </f>
      <f
        bold={false}
        color="black"
        component="p"
        italic={false}
        numeric={false}
      >
        No web fragment attached
      </f>
    </f>
    <div
      className="admin-details__action-bar"
    >
      <f
        onClick={[MockFunction]}
        type="button"
      >
        Cancel
      </f>
      <div
        className="admin-details__action-buttons"
      />
    </div>
  </form>
  <g
    alwaysStackButtons={false}
    headline="Are you sure?"
    isModalVisible={false}
    primaryAction={[Function]}
    primaryButtonLabel="Delete"
    secondaryAction={[Function]}
    secondaryButtonLabel="Cancel"
    setModalVisible={[Function]}
    usePrimaryButtons={false}
    width="wide"
  >
    This 
    SOL Campaign
     will be 
    .
  </g>
  <Connect(ReduxForm)
    type="vignette"
  />
</div>
`;

exports[`Details Snapshot snap with download button 1`] = `
<div
  className="admin-details"
>
  <form>
    <div
      className="admin-details__action-bar"
    >
      <f
        bold={false}
        className="admin-details__header"
        color="black"
        component="h1"
        italic={false}
      >
        title
      </f>
      <div>
        <div
          className="admin-details__action-buttons"
        >
          <d
            Icon={[Function]}
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
            variant="info"
          >
            Download
          </d>
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Duplicate
          </d>
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Terminate
          </d>
        </div>
      </div>
    </div>
    <div>
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Current Status:
        <StatusBadge
          status="published"
        />
      </f>
    </div>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Information
      </f>
      <Field
        autoComplete="off"
        className="admin-details__field"
        component={[Function]}
        disabled={true}
        label="Campaign Name"
        name="name"
        placeholder="Campaign Name"
        required={true}
        validate={
          [
            [Function],
            [Function],
          ]
        }
      />
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Type
      </f>
      <d
        className={null}
        disabled={false}
        error={null}
        form={null}
        id="campaign-type-group"
        inline={false}
        inputRef={null}
        labelLeft={false}
        legend=""
        name="type"
        tooltip={null}
      >
        <div
          className="admin-details__radio-container"
        >
          <Field
            component={[Function]}
            disabled={true}
            id="targeted"
            label="Targeted Campaign"
            name="type"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="mass"
            label="Mass Campaign"
            name="type"
          />
        </div>
      </d>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Targeting Dimension
      </f>
      <div
        className="admin-details__date-fields"
      >
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={true}
          label="Start date"
          name="start_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={true}
          label="End date"
          name="end_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
      </div>
      <Field
        className="admin-details__field"
        component={[Function]}
        name="language"
      >
        <div
          className="admin-details__radio-container"
        >
          <span
            className="input-group__legend"
          >
            Language
          </span>
          <Field
            component={[Function]}
            disabled={true}
            id="en"
            label="EN"
            name="language"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="fr"
            label="FR"
            name="language"
          />
        </div>
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Targeting by Device
      </f>
      <div
        className="admin-details__autosuggest-table"
      >
        <div
          className="admin-details__autosuggest-column"
        >
          <MassCampaignAutosuggest
            data={[]}
            dataLegend={
              {
                "keyName": "code",
                "valueName": "description",
              }
            }
            editable={false}
            hideSuggestionsAfterSelection={true}
            label="Include"
            onChange={[Function]}
            placeholder="Device Type"
          />
        </div>
        <div
          className="admin-details__autosuggest-column"
        >
          <MassCampaignAutosuggest
            data={[]}
            dataLegend={
              {
                "keyName": "code",
                "valueName": "description",
              }
            }
            editable={false}
            hideSuggestionsAfterSelection={true}
            label="Exclude"
            onChange={[Function]}
            placeholder="Device Type"
          />
        </div>
      </div>
      <Field
        component={[Function]}
        name="DEVC_RADIO"
      >
        <div
          className="admin-details__radio-container"
        >
          <span
            className="input-group__legend"
          >
            Relationship Settings
          </span>
          <Field
            component={[Function]}
            disabled={true}
            id="DEVC_OR"
            label="OR"
            name="DEVC_RADIO"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="DEVC_AND"
            label="AND"
            name="DEVC_RADIO"
          />
        </div>
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Container and Pages
      </f>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="container"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "containerId": "LoginSecurityCentre",
                "id": 1,
                "name": "LoginSecurityCentre",
                "pages": [
                  1,
                ],
                "rule_type": "vignette",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "containerId",
              "valueName": "name",
            }
          }
          editable={false}
          hideSuggestionsAfterSelection={true}
          initialSelection={
            [
              undefined,
            ]
          }
          label="Container Name"
          limit={1}
          onChange={[Function]}
          placeholder="Select a container…"
        />
      </Field>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="pages"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "code": "Login",
                "description": "Login",
              },
              {
                "code": "Logout",
                "description": "Logout",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={false}
          hideSuggestionsAfterSelection={true}
          label="Page Name"
          onChange={[Function]}
          placeholder="Pages"
        />
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Code Fragment
      </f>
      <f
        bold={false}
        color="black"
        component="p"
        italic={false}
        numeric={false}
      >
        No web fragment attached
      </f>
    </f>
    <div
      className="admin-details__action-bar"
    >
      <f
        onClick={[MockFunction]}
        type="button"
      >
        Cancel
      </f>
      <div
        className="admin-details__action-buttons"
      />
    </div>
  </form>
  <g
    alwaysStackButtons={false}
    headline="Are you sure?"
    isModalVisible={false}
    primaryAction={[Function]}
    primaryButtonLabel="Delete"
    secondaryAction={[Function]}
    secondaryButtonLabel="Cancel"
    setModalVisible={[Function]}
    usePrimaryButtons={false}
    width="wide"
  >
    This 
    Storefront Campaign
     will be 
    .
    <br />
    <strong>
      Make sure you remove any downloaded XML files (
      undefined.xml
      ) associated to this rule from the Products and Services Bitbucket Repository.
    </strong>
  </g>
  <Connect(ReduxForm)
    type="estore"
  />
</div>
`;

exports[`Details Snapshot snap with estore (storefront) 1`] = `
<div
  className="admin-details"
>
  <form>
    <div
      className="admin-details__action-bar"
    >
      <f
        bold={false}
        className="admin-details__header"
        color="black"
        component="h1"
        italic={false}
      >
        title
      </f>
      <div>
        <div
          className="admin-details__action-buttons"
        />
      </div>
    </div>
    <div />
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Information
      </f>
      <Field
        autoComplete="off"
        className="admin-details__field"
        component={[Function]}
        disabled={false}
        label="Campaign Name"
        name="name"
        placeholder="Campaign Name"
        required={true}
        validate={
          [
            [Function],
            [Function],
          ]
        }
      />
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Type
      </f>
      <d
        className={null}
        disabled={false}
        error={null}
        form={null}
        id="campaign-type-group"
        inline={false}
        inputRef={null}
        labelLeft={false}
        legend=""
        name="type"
        tooltip={null}
      >
        <div
          className="admin-details__radio-container"
        >
          <Field
            component={[Function]}
            disabled={false}
            id="targeted"
            label="Targeted Campaign"
            name="type"
          />
          <Field
            component={[Function]}
            disabled={false}
            id="mass"
            label="Mass Campaign"
            name="type"
          />
        </div>
      </d>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Targeting Dimension
      </f>
      <div
        className="admin-details__date-fields"
      >
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={false}
          label="Start date"
          name="start_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={false}
          label="End date"
          name="end_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
      </div>
      <Field
        className="admin-details__field"
        component={[Function]}
        name="language"
      >
        <div
          className="admin-details__radio-container"
        >
          <span
            className="input-group__legend"
          >
            Language
          </span>
          <Field
            component={[Function]}
            disabled={false}
            id="en"
            label="EN"
            name="language"
          />
          <Field
            component={[Function]}
            disabled={false}
            id="fr"
            label="FR"
            name="language"
          />
        </div>
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Targeting by Device
      </f>
      <div
        className="admin-details__autosuggest-table"
      >
        <div
          className="admin-details__autosuggest-column"
        >
          <MassCampaignAutosuggest
            data={[]}
            dataLegend={
              {
                "keyName": "code",
                "valueName": "description",
              }
            }
            editable={true}
            hideSuggestionsAfterSelection={true}
            label="Include"
            onChange={[Function]}
            placeholder="Device Type"
          />
        </div>
        <div
          className="admin-details__autosuggest-column"
        >
          <MassCampaignAutosuggest
            data={[]}
            dataLegend={
              {
                "keyName": "code",
                "valueName": "description",
              }
            }
            editable={true}
            hideSuggestionsAfterSelection={true}
            label="Exclude"
            onChange={[Function]}
            placeholder="Device Type"
          />
        </div>
      </div>
      <Field
        component={[Function]}
        name="DEVC_RADIO"
      >
        <div
          className="admin-details__radio-container"
        >
          <span
            className="input-group__legend"
          >
            Relationship Settings
          </span>
          <Field
            component={[Function]}
            disabled={false}
            id="DEVC_OR"
            label="OR"
            name="DEVC_RADIO"
          />
          <Field
            component={[Function]}
            disabled={false}
            id="DEVC_AND"
            label="AND"
            name="DEVC_RADIO"
          />
        </div>
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Container and Pages
      </f>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="container"
      >
        <MassCampaignAutosuggest
          data={[]}
          dataLegend={
            {
              "keyName": "containerId",
              "valueName": "name",
            }
          }
          editable={true}
          hideSuggestionsAfterSelection={true}
          initialSelection={
            [
              undefined,
            ]
          }
          label="Container Name"
          limit={1}
          onChange={[Function]}
          placeholder="Select a container…"
        />
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Code Fragment
      </f>
      <Field
        className="admin-details__field"
        component={[Function]}
        name="content_id"
      >
        <ForwardRef
          Icon={[Function]}
          className={null}
          color="blue"
          iconPosition="left"
          onClick={[Function]}
          type="button"
          weight="bold"
        >
          Search and add web fragment
        </ForwardRef>
      </Field>
    </f>
    <div
      className="admin-details__action-bar"
    >
      <f
        onClick={[MockFunction]}
        type="button"
      >
        Cancel
      </f>
      <div
        className="admin-details__action-buttons"
      >
        <d
          className="admin-details__action-button"
          disabled={false}
          labelPadding={36}
          onClick={null}
          size="regular"
        >
          Save as Draft
        </d>
        <d
          className="admin-details__action-button"
          disabled={false}
          labelPadding={36}
          onClick={null}
          size="regular"
        >
          Submit for Review
        </d>
      </div>
    </div>
  </form>
  <g
    alwaysStackButtons={false}
    headline="Are you sure?"
    isModalVisible={false}
    primaryAction={[Function]}
    primaryButtonLabel="Delete"
    secondaryAction={[Function]}
    secondaryButtonLabel="Cancel"
    setModalVisible={[Function]}
    usePrimaryButtons={false}
    width="wide"
  >
    This 
    Storefront Campaign
     will be 
    .
    <br />
    <strong>
      Make sure you remove any downloaded XML files (
      undefined.xml
      ) associated to this rule from the Products and Services Bitbucket Repository.
    </strong>
  </g>
  <Connect(ReduxForm)
    type="estore"
  />
</div>
`;

exports[`Details Snapshot snap with mass targeting 1`] = `
<div
  className="admin-details"
>
  <form>
    <div
      className="admin-details__action-bar"
    >
      <f
        bold={false}
        className="admin-details__header"
        color="black"
        component="h1"
        italic={false}
      >
        title
      </f>
      <div>
        <div
          className="admin-details__action-buttons"
        >
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Duplicate
          </d>
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Terminate
          </d>
        </div>
      </div>
    </div>
    <div>
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Current Status:
        <StatusBadge
          status="published"
        />
      </f>
    </div>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Information
      </f>
      <Field
        autoComplete="off"
        className="admin-details__field"
        component={[Function]}
        disabled={true}
        label="Campaign Name"
        name="name"
        placeholder="Campaign Name"
        required={true}
        validate={
          [
            [Function],
            [Function],
          ]
        }
      />
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Type
      </f>
      <d
        className={null}
        disabled={false}
        error={null}
        form={null}
        id="campaign-type-group"
        inline={false}
        inputRef={null}
        labelLeft={false}
        legend=""
        name="type"
        tooltip={null}
      >
        <div
          className="admin-details__radio-container"
        >
          <Field
            component={[Function]}
            disabled={true}
            id="targeted"
            label="Targeted Campaign"
            name="type"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="mass"
            label="Mass Campaign"
            name="type"
          />
        </div>
      </d>
    </f>
    <AdvancedTargeting
      access={
        {
          "containers": {
            "sol": {
              "manage": [
                "LoginSecurityCentre",
              ],
              "view": [
                "LoginSecurityCentre",
              ],
            },
            "storefront": {
              "manage": [
                "sf-marquee-bankingaccounts",
              ],
              "view": [
                "sf-marquee-bankingaccounts",
              ],
            },
          },
          "pages": {
            "sol": {
              "manage": [
                "Login",
              ],
              "view": [
                "Login",
              ],
            },
            "storefront": {
              "manage": [
                "sf-accessibility",
              ],
              "view": [
                "sf-accessibility",
              ],
            },
          },
          "ruleSubTypes": {
            "sol": {
              "manage": [
                "targeted",
                "mass",
              ],
              "view": [
                "targeted",
                "mass",
              ],
            },
            "storefront": {
              "manage": [
                "targeted",
                "mass",
              ],
              "view": [
                "targeted",
                "mass",
              ],
            },
          },
        }
      }
      containers={
        {
          "LoginSecurityCentre": {
            "containerId": "LoginSecurityCentre",
            "id": 1,
            "name": "LoginSecurityCentre",
            "pages": [
              1,
            ],
            "rule_type": "vignette",
          },
        }
      }
      deleteContent={[MockFunction]}
      disabled={true}
      downloadXML={[MockFunction]}
      formChange={
        [MockFunction] {
          "calls": [
            [
              "vignetteDetails",
              "type",
              "targeted",
            ],
            [
              "vignetteDetails",
              "type",
              "targeted",
            ],
            [
              "vignetteDetails",
              "type",
              "targeted",
            ],
            [
              "vignetteDetails",
              "type",
              "targeted",
            ],
            [
              "vignetteDetails",
              "type",
              "targeted",
            ],
            [
              "vignetteDetails",
              "type",
              "targeted",
            ],
            [
              "vignetteDetails",
              "type",
              undefined,
            ],
          ],
          "results": [
            {
              "type": "return",
              "value": undefined,
            },
            {
              "type": "return",
              "value": undefined,
            },
            {
              "type": "return",
              "value": undefined,
            },
            {
              "type": "return",
              "value": undefined,
            },
            {
              "type": "return",
              "value": undefined,
            },
            {
              "type": "return",
              "value": undefined,
            },
            {
              "type": "return",
              "value": undefined,
            },
          ],
        }
      }
      formValues={
        {
          "id": "12345",
          "type": "mass",
        }
      }
      metadata={
        {
          "device": [],
        }
      }
      onActivate={[MockFunction]}
      onApprove={[MockFunction]}
      onCancel={[MockFunction]}
      onDelete={[MockFunction]}
      onDuplicate={[MockFunction]}
      onPublish={[MockFunction]}
      onSubmit={
        [MockFunction] {
          "calls": [
            [
              "draft",
            ],
            [],
            [
              "draft",
            ],
            [],
            [
              "draft",
            ],
            [],
          ],
          "results": [
            {
              "type": "return",
              "value": undefined,
            },
            {
              "type": "return",
              "value": undefined,
            },
            {
              "type": "return",
              "value": undefined,
            },
            {
              "type": "return",
              "value": undefined,
            },
            {
              "type": "return",
              "value": undefined,
            },
            {
              "type": "return",
              "value": undefined,
            },
          ],
        }
      }
      onTerminate={[MockFunction]}
      openFragmentModal={[MockFunction]}
      pages={
        [
          {
            "application": 1,
            "id": 1,
            "name": "Login",
            "pageId": "Login",
          },
          {
            "application": 1,
            "id": 2,
            "name": "Logout",
            "pageId": "Logout",
          },
        ]
      }
      permissions={
        {
          "campaigns_manage": true,
        }
      }
      status="published"
      title="title"
      type="vignette"
      updateDetails={[MockFunction]}
    />
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Targeting Dimension
      </f>
      <div
        className="admin-details__date-fields"
      >
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={true}
          label="Start date"
          name="start_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={true}
          label="End date"
          name="end_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
      </div>
      <Field
        className="admin-details__field"
        component={[Function]}
        name="language"
      >
        <div
          className="admin-details__radio-container"
        >
          <span
            className="input-group__legend"
          >
            Language
          </span>
          <Field
            component={[Function]}
            disabled={true}
            id="en"
            label="EN"
            name="language"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="fr"
            label="FR"
            name="language"
          />
        </div>
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Container and Pages
      </f>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="container"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "containerId": "LoginSecurityCentre",
                "id": 1,
                "name": "LoginSecurityCentre",
                "pages": [
                  1,
                ],
                "rule_type": "vignette",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "containerId",
              "valueName": "name",
            }
          }
          editable={false}
          hideSuggestionsAfterSelection={true}
          initialSelection={
            [
              undefined,
            ]
          }
          label="Container Name"
          limit={1}
          onChange={[Function]}
          placeholder="Select a container…"
        />
      </Field>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="pages"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "code": "Login",
                "description": "Login",
              },
              {
                "code": "Logout",
                "description": "Logout",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={false}
          hideSuggestionsAfterSelection={true}
          label="Page Name"
          onChange={[Function]}
          placeholder="Pages"
        />
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Code Fragment
      </f>
      <f
        bold={false}
        color="black"
        component="p"
        italic={false}
        numeric={false}
      >
        No web fragment attached
      </f>
    </f>
    <div
      className="admin-details__action-bar"
    >
      <f
        onClick={[MockFunction]}
        type="button"
      >
        Cancel
      </f>
      <div
        className="admin-details__action-buttons"
      />
    </div>
  </form>
  <g
    alwaysStackButtons={false}
    headline="Are you sure?"
    isModalVisible={false}
    primaryAction={[Function]}
    primaryButtonLabel="Delete"
    secondaryAction={[Function]}
    secondaryButtonLabel="Cancel"
    setModalVisible={[Function]}
    usePrimaryButtons={false}
    width="wide"
  >
    This 
    SOL Campaign
     will be 
    .
  </g>
  <Connect(ReduxForm)
    type="vignette"
  />
</div>
`;

exports[`Details Snapshot snap with status draft 1`] = `
<div
  className="admin-details"
>
  <form>
    <div
      className="admin-details__action-bar"
    >
      <f
        bold={false}
        className="admin-details__header"
        color="black"
        component="h1"
        italic={false}
      >
        title
      </f>
      <div>
        <div
          className="admin-details__action-buttons"
        >
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Duplicate
          </d>
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Delete
          </d>
        </div>
      </div>
    </div>
    <div>
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Current Status:
        <StatusBadge
          status="draft"
        />
      </f>
    </div>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Information
      </f>
      <Field
        autoComplete="off"
        className="admin-details__field"
        component={[Function]}
        disabled={true}
        label="Campaign Name"
        name="name"
        placeholder="Campaign Name"
        required={true}
        validate={
          [
            [Function],
            [Function],
          ]
        }
      />
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Type
      </f>
      <d
        className={null}
        disabled={false}
        error={null}
        form={null}
        id="campaign-type-group"
        inline={false}
        inputRef={null}
        labelLeft={false}
        legend=""
        name="type"
        tooltip={null}
      >
        <div
          className="admin-details__radio-container"
        >
          <Field
            component={[Function]}
            disabled={true}
            id="targeted"
            label="Targeted Campaign"
            name="type"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="mass"
            label="Mass Campaign"
            name="type"
          />
        </div>
      </d>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Targeting Dimension
      </f>
      <div
        className="admin-details__date-fields"
      >
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={true}
          label="Start date"
          name="start_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={true}
          label="End date"
          name="end_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
      </div>
      <Field
        className="admin-details__field"
        component={[Function]}
        name="language"
      >
        <div
          className="admin-details__radio-container"
        >
          <span
            className="input-group__legend"
          >
            Language
          </span>
          <Field
            component={[Function]}
            disabled={true}
            id="en"
            label="EN"
            name="language"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="fr"
            label="FR"
            name="language"
          />
        </div>
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Container and Pages
      </f>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="container"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "containerId": "LoginSecurityCentre",
                "id": 1,
                "name": "LoginSecurityCentre",
                "pages": [
                  1,
                ],
                "rule_type": "vignette",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "containerId",
              "valueName": "name",
            }
          }
          editable={false}
          hideSuggestionsAfterSelection={true}
          initialSelection={
            [
              "LoginSecurityCentre",
            ]
          }
          label="Container Name"
          limit={1}
          onChange={[Function]}
          placeholder="Select a container…"
        />
      </Field>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="pages"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "code": "Login",
                "description": "Login",
              },
              {
                "code": "Logout",
                "description": "Logout",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={false}
          hideSuggestionsAfterSelection={true}
          initialSelection={
            [
              "Login",
            ]
          }
          label="Page Name"
          onChange={[Function]}
          placeholder="Pages"
        />
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Code Fragment
      </f>
      <f
        bold={false}
        color="black"
        component="p"
        italic={false}
        numeric={false}
      >
        No web fragment attached
      </f>
    </f>
    <div
      className="admin-details__action-bar"
    >
      <f
        onClick={[MockFunction]}
        type="button"
      >
        Cancel
      </f>
      <div
        className="admin-details__action-buttons"
      >
        <d
          className="admin-details__action-button"
          disabled={false}
          labelPadding={36}
          onClick={null}
          size="regular"
        >
          Save as Draft
        </d>
        <d
          className="admin-details__action-button"
          disabled={false}
          labelPadding={36}
          onClick={null}
          size="regular"
        >
          Submit for Review
        </d>
      </div>
    </div>
  </form>
  <g
    alwaysStackButtons={false}
    headline="Are you sure?"
    isModalVisible={false}
    primaryAction={[Function]}
    primaryButtonLabel="Delete"
    secondaryAction={[Function]}
    secondaryButtonLabel="Cancel"
    setModalVisible={[Function]}
    usePrimaryButtons={false}
    width="wide"
  >
    This 
    SOL Campaign
     will be 
    .
  </g>
  <Connect(ReduxForm)
    type="vignette"
  />
</div>
`;

exports[`Details Snapshot snap with targeted targeting 1`] = `
<div
  className="admin-details"
>
  <form>
    <div
      className="admin-details__action-bar"
    >
      <f
        bold={false}
        className="admin-details__header"
        color="black"
        component="h1"
        italic={false}
      >
        title
      </f>
      <div>
        <div
          className="admin-details__action-buttons"
        >
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Duplicate
          </d>
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Terminate
          </d>
        </div>
      </div>
    </div>
    <div>
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Current Status:
        <StatusBadge
          status="published"
        />
      </f>
    </div>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Information
      </f>
      <Field
        autoComplete="off"
        className="admin-details__field"
        component={[Function]}
        disabled={true}
        label="Campaign Name"
        name="name"
        placeholder="Campaign Name"
        required={true}
        validate={
          [
            [Function],
            [Function],
          ]
        }
      />
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Type
      </f>
      <d
        className={null}
        disabled={false}
        error={null}
        form={null}
        id="campaign-type-group"
        inline={false}
        inputRef={null}
        labelLeft={false}
        legend=""
        name="type"
        tooltip={null}
      >
        <div
          className="admin-details__radio-container"
        >
          <Field
            component={[Function]}
            disabled={true}
            id="targeted"
            label="Targeted Campaign"
            name="type"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="mass"
            label="Mass Campaign"
            name="type"
          />
        </div>
      </d>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Targeting by Campaign ID
      </f>
      <Field
        autoComplete="off"
        component={[Function]}
        disabled={true}
        label="Campaign ID"
        name="campaign_name"
        placeholder="Campaign ID"
        required={true}
        validate={
          [
            [Function],
            [Function],
            [Function],
          ]
        }
      />
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Targeting Dimension
      </f>
      <div
        className="admin-details__date-fields"
      >
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={true}
          label="Start date"
          name="start_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={true}
          label="End date"
          name="end_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
      </div>
      <Field
        className="admin-details__field"
        component={[Function]}
        name="language"
      >
        <div
          className="admin-details__radio-container"
        >
          <span
            className="input-group__legend"
          >
            Language
          </span>
          <Field
            component={[Function]}
            disabled={true}
            id="en"
            label="EN"
            name="language"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="fr"
            label="FR"
            name="language"
          />
        </div>
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Container and Pages
      </f>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="container"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "containerId": "LoginSecurityCentre",
                "id": 1,
                "name": "LoginSecurityCentre",
                "pages": [
                  1,
                ],
                "rule_type": "vignette",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "containerId",
              "valueName": "name",
            }
          }
          editable={false}
          hideSuggestionsAfterSelection={true}
          initialSelection={
            [
              undefined,
            ]
          }
          label="Container Name"
          limit={1}
          onChange={[Function]}
          placeholder="Select a container…"
        />
      </Field>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="pages"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "code": "Login",
                "description": "Login",
              },
              {
                "code": "Logout",
                "description": "Logout",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={false}
          hideSuggestionsAfterSelection={true}
          label="Page Name"
          onChange={[Function]}
          placeholder="Pages"
        />
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Code Fragment
      </f>
      <f
        bold={false}
        color="black"
        component="p"
        italic={false}
        numeric={false}
      >
        No web fragment attached
      </f>
    </f>
    <div
      className="admin-details__action-bar"
    >
      <f
        onClick={[MockFunction]}
        type="button"
      >
        Cancel
      </f>
      <div
        className="admin-details__action-buttons"
      />
    </div>
  </form>
  <g
    alwaysStackButtons={false}
    headline="Are you sure?"
    isModalVisible={false}
    primaryAction={[Function]}
    primaryButtonLabel="Delete"
    secondaryAction={[Function]}
    secondaryButtonLabel="Cancel"
    setModalVisible={[Function]}
    usePrimaryButtons={false}
    width="wide"
  >
    This 
    SOL Campaign
     will be 
    .
  </g>
  <Connect(ReduxForm)
    type="vignette"
  />
</div>
`;

exports[`Details Snapshot status status submitted 1`] = `
<div
  className="admin-details"
>
  <form>
    <div
      className="admin-details__action-bar"
    >
      <f
        bold={false}
        className="admin-details__header"
        color="black"
        component="h1"
        italic={false}
      >
        title
      </f>
      <div>
        <div
          className="admin-details__action-buttons"
        >
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Duplicate
          </d>
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Delete
          </d>
        </div>
      </div>
    </div>
    <div>
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Current Status:
        <StatusBadge
          status="submitted"
        />
      </f>
    </div>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Information
      </f>
      <Field
        autoComplete="off"
        className="admin-details__field"
        component={[Function]}
        disabled={true}
        label="Campaign Name"
        name="name"
        placeholder="Campaign Name"
        required={true}
        validate={
          [
            [Function],
            [Function],
          ]
        }
      />
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Campaign Type
      </f>
      <d
        className={null}
        disabled={false}
        error={null}
        form={null}
        id="campaign-type-group"
        inline={false}
        inputRef={null}
        labelLeft={false}
        legend=""
        name="type"
        tooltip={null}
      >
        <div
          className="admin-details__radio-container"
        >
          <Field
            component={[Function]}
            disabled={true}
            id="targeted"
            label="Targeted Campaign"
            name="type"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="mass"
            label="Mass Campaign"
            name="type"
          />
        </div>
      </d>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Targeting Dimension
      </f>
      <div
        className="admin-details__date-fields"
      >
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={true}
          label="Start date"
          name="start_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
        <Field
          autoComplete="off"
          className="admin-details__date-field"
          component={[Function]}
          disabled={true}
          label="End date"
          name="end_date"
          placeholder="MM/DD/YYYY HH:MM AM/PM"
          required={true}
        />
      </div>
      <Field
        className="admin-details__field"
        component={[Function]}
        name="language"
      >
        <div
          className="admin-details__radio-container"
        >
          <span
            className="input-group__legend"
          >
            Language
          </span>
          <Field
            component={[Function]}
            disabled={true}
            id="en"
            label="EN"
            name="language"
          />
          <Field
            component={[Function]}
            disabled={true}
            id="fr"
            label="FR"
            name="language"
          />
        </div>
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Container and Pages
      </f>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="container"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "containerId": "LoginSecurityCentre",
                "id": 1,
                "name": "LoginSecurityCentre",
                "pages": [
                  1,
                ],
                "rule_type": "vignette",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "containerId",
              "valueName": "name",
            }
          }
          editable={false}
          hideSuggestionsAfterSelection={true}
          initialSelection={
            [
              undefined,
            ]
          }
          label="Container Name"
          limit={1}
          onChange={[Function]}
          placeholder="Select a container…"
        />
      </Field>
      <Field
        className="admin-details__field"
        component={[Function]}
        label=""
        name="pages"
      >
        <MassCampaignAutosuggest
          data={
            [
              {
                "code": "Login",
                "description": "Login",
              },
              {
                "code": "Logout",
                "description": "Logout",
              },
            ]
          }
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={false}
          hideSuggestionsAfterSelection={true}
          label="Page Name"
          onChange={[Function]}
          placeholder="Pages"
        />
      </Field>
    </f>
    <f
      className="admin-details__card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <f
        bold={false}
        className="admin-details__sub-header"
        color="black"
        component="h2"
        italic={false}
      >
        Code Fragment
      </f>
      <f
        bold={false}
        color="black"
        component="p"
        italic={false}
        numeric={false}
      >
        No web fragment attached
      </f>
    </f>
    <div
      className="admin-details__action-bar"
    >
      <f
        onClick={[MockFunction]}
        type="button"
      >
        Cancel
      </f>
      <div
        className="admin-details__action-buttons"
      />
    </div>
  </form>
  <g
    alwaysStackButtons={false}
    headline="Are you sure?"
    isModalVisible={false}
    primaryAction={[Function]}
    primaryButtonLabel="Delete"
    secondaryAction={[Function]}
    secondaryButtonLabel="Cancel"
    setModalVisible={[Function]}
    usePrimaryButtons={false}
    width="wide"
  >
    This 
    SOL Campaign
     will be 
    .
  </g>
  <Connect(ReduxForm)
    type="vignette"
  />
</div>
`;

exports[`DetailsContainer Snapshot All Errors 1`] = `
{
  "container": "You must select a container.",
  "content_id": "You must select a web fragment.",
  "end_date": "You must enter a valid date (MM/DD/YYYY HH:MM AM/PM).",
  "language": "You must select a language.",
  "name": "You must enter a name for this campaign.",
  "start_date": "You must enter a valid date (MM/DD/YYYY HH:MM AM/PM).",
}
`;

exports[`DetailsContainer Snapshot No Errors 1`] = `
{
  "contents": [
    {
      "container": "container_id",
      "content_id": "7777",
      "content_space": "db",
      "content_type": "webfragment",
      "page": "activities",
    },
  ],
  "end_date": "2018-07-15T00:00:00.000Z",
  "message_type": "M",
  "name": "Mike Test",
  "start_date": "2018-07-13T00:00:00.000Z",
  "status": undefined,
  "targeting": {
    "campaign_name": null,
    "country_code": "CA",
    "devices": [],
    "language_code": "en",
    "platforms": [
      {
        "platform_name": "web",
      },
    ],
    "products": [
      {
        "attribute_mode": "and",
        "attribute_type": "BU",
        "attribute_values": [
          "B:",
          "CASL:",
        ],
      },
      {
        "attribute_mode": "and",
        "attribute_type": "REGS",
        "attribute_values": [
          "B",
          "I",
        ],
      },
      {
        "attribute_mode": "and",
        "attribute_type": "PR",
        "attribute_values": [
          "Banking:DDA:DDA:DDA:",
          "Banking:Savings:SAV:SAV:CS",
        ],
      },
      {
        "attribute_mode": "or",
        "attribute_type": "PROV",
        "attribute_values": [
          "AB",
        ],
      },
    ],
  },
  "type": undefined,
  "urgent": false,
}
`;

exports[`DetailsContainer Snapshot No Errors for device exclude DEVC_NOT 1`] = `
{
  "contents": [
    {
      "container": "container_id",
      "content_id": "7777",
      "content_space": "db",
      "content_type": "webfragment",
      "page": "activities",
    },
  ],
  "end_date": "2018-07-15T00:00:00.000Z",
  "message_type": "M",
  "name": "Mike Test",
  "start_date": "2018-07-13T00:00:00.000Z",
  "status": undefined,
  "targeting": {
    "campaign_name": null,
    "country_code": "CA",
    "devices": [],
    "language_code": "en",
    "platforms": [
      {
        "platform_name": "web",
      },
    ],
    "products": [
      {
        "attribute_mode": "and",
        "attribute_type": "BU",
        "attribute_values": [
          "B:",
          "CASL:",
        ],
      },
      {
        "attribute_mode": "and",
        "attribute_type": "REGS",
        "attribute_values": [
          "B",
          "I",
        ],
      },
      {
        "attribute_mode": "and",
        "attribute_type": "PR",
        "attribute_values": [
          "Banking:DDA:DDA:DDA:",
          "Banking:Savings:SAV:SAV:CS",
        ],
      },
      {
        "attribute_mode": "not",
        "attribute_type": "DEVC",
        "attribute_values": [
          "and",
        ],
      },
    ],
  },
  "type": undefined,
  "urgent": false,
}
`;

exports[`DetailsContainer Snapshot Snapshot 1`] = `""`;
