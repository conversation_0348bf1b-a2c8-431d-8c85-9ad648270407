// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Advanced targeting component should render 1`] = `
<Fragment>
  <f
    className="admin-details__card"
    lgPadding={36}
    mdPadding={30}
    smPadding={24}
    type="flatSolid"
    xsPadding={18}
  >
    <f
      bold={false}
      className="admin-details__sub-header"
      color="black"
      component="h2"
      italic={false}
    >
      Targeting by Business Lines
    </f>
    <div
      className="details__autosuggest-table"
    >
      <div
        className="details__autosuggest-column"
      >
        <MassCampaignAutosuggest
          data={
            [
              "mock-busines-line",
            ]
          }
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={true}
          hideSuggestionsAfterSelection={true}
          initialSelection={[]}
          label="Include"
          onChange={[Function]}
          placeholder="Business Line"
        />
      </div>
      <div
        className="details__autosuggest-column"
      >
        <MassCampaignAutosuggest
          data={
            [
              "mock-busines-line",
            ]
          }
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={true}
          excludes={[]}
          hideSuggestionsAfterSelection={true}
          label="Exclude"
          onChange={[Function]}
          placeholder="Business Line"
        />
      </div>
    </div>
    <Field
      component={[Function]}
      name="BU_RADIO"
    >
      <div
        className="details__radio-container"
      >
        <legend
          className="input-group__legend"
        >
          Relationship Settings
        </legend>
        <Field
          component={[Function]}
          disabled={false}
          id="BU_OR"
          label="OR"
          name="BU_RADIO"
        />
        <Field
          component={[Function]}
          disabled={false}
          id="BU_AND"
          label="AND"
          name="BU_RADIO"
        />
      </div>
    </Field>
  </f>
  <f
    className="admin-details__card"
    lgPadding={36}
    mdPadding={30}
    smPadding={24}
    type="flatSolid"
    xsPadding={18}
  >
    <f
      bold={false}
      className="admin-details__sub-header"
      color="black"
      component="h2"
      italic={false}
    >
      Targeting by Registration Status
    </f>
    <div
      className="details__autosuggest-table"
    >
      <div
        className="details__autosuggest-column"
      >
        <MassCampaignAutosuggest
          data={[]}
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={true}
          hideSuggestionsAfterSelection={true}
          label="Include"
          onChange={[Function]}
          placeholder="Registration Status"
        />
      </div>
      <div
        className="details__autosuggest-column"
      >
        <MassCampaignAutosuggest
          data={[]}
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={true}
          hideSuggestionsAfterSelection={true}
          label="Exclude"
          onChange={[Function]}
          placeholder="Registration Status"
        />
      </div>
    </div>
    <Field
      component={[Function]}
      name="REGS_RADIO"
    >
      <div
        className="details__radio-container"
      >
        <legend
          className="input-group__legend"
        >
          Relationship Settings
        </legend>
        <Field
          component={[Function]}
          disabled={false}
          id="REGS_OR"
          label="OR"
          name="REGS_RADIO"
        />
        <Field
          component={[Function]}
          disabled={false}
          id="REGS_AND"
          label="AND"
          name="REGS_RADIO"
        />
      </div>
    </Field>
  </f>
  <f
    className="admin-details__card"
    lgPadding={36}
    mdPadding={30}
    smPadding={24}
    type="flatSolid"
    xsPadding={18}
  >
    <f
      bold={false}
      className="admin-details__sub-header"
      color="black"
      component="h2"
      italic={false}
    >
      Advanced Targeting
    </f>
    <div
      className="details__autosuggest-table"
    >
      <div
        className="details__autosuggest-column"
      >
        <MassCampaignAutosuggest
          data={[]}
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={true}
          label="Include"
          onChange={[Function]}
          placeholder="Banking Products"
        />
        <MassCampaignAutosuggest
          data={[]}
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={true}
          onChange={[Function]}
          placeholder="Borrowing Products"
        />
        <MassCampaignAutosuggest
          data={[]}
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={true}
          onChange={[Function]}
          placeholder="Investing - Retail"
        />
        <MassCampaignAutosuggest
          data={[]}
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={true}
          onChange={[Function]}
          placeholder="Investing - Wealth"
        />
      </div>
      <div
        className="details__autosuggest-column"
      >
        <MassCampaignAutosuggest
          data={[]}
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={true}
          label="Exclude"
          onChange={[Function]}
          placeholder="Banking Products"
        />
        <MassCampaignAutosuggest
          data={[]}
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={true}
          onChange={[Function]}
          placeholder="Borrowing Products"
        />
        <MassCampaignAutosuggest
          data={[]}
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={true}
          onChange={[Function]}
          placeholder="Investing - Retail"
        />
        <MassCampaignAutosuggest
          data={[]}
          dataLegend={
            {
              "keyName": "code",
              "valueName": "description",
            }
          }
          editable={true}
          onChange={[Function]}
          placeholder="Investing - Wealth"
        />
      </div>
    </div>
    <Field
      component={[Function]}
      name="PR_RADIO"
    >
      <div
        className="details__radio-container"
      >
        <legend
          className="input-group__legend"
        >
          Relationship Settings
        </legend>
        <Field
          component={[Function]}
          disabled={false}
          id="PR_OR"
          label="OR"
          name="PR_RADIO"
        />
        <Field
          component={[Function]}
          disabled={false}
          id="PR_AND"
          label="AND"
          name="PR_RADIO"
        />
      </div>
    </Field>
  </f>
  <f
    className="admin-details__card"
    lgPadding={36}
    mdPadding={30}
    smPadding={24}
    type="flatSolid"
    xsPadding={18}
  >
    <f
      bold={false}
      className="admin-details__sub-header"
      color="black"
      component="h2"
      italic={false}
    >
      Targeting by Geography
    </f>
    <MassCampaignAutosuggest
      className="admin-details__field"
      data={[]}
      dataLegend={
        {
          "keyName": "code",
          "valueName": "description",
        }
      }
      editable={true}
      onChange={[Function]}
      placeholder="Province"
    />
  </f>
</Fragment>
`;
