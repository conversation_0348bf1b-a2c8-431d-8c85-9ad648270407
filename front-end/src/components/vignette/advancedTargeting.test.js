import React from 'react';
import AdvancedTargeting from './advancedTargeting';
import { shallow } from 'enzyme';

describe('Advanced targeting component', () => {
  it('should render', () => {
    const wrapper = shallow(
      <AdvancedTargeting
        metadata={{
          businessLines: [ 'mock-busines-line' ],
          registrationStatus: [],
          investingRetail: [],
          investingWealth: [],
          borrowingProducts: [],
          bankingProducts: [],
          geography: [],
        }}
        formValues={{
          BU: [],
        }}
        updateDetails={jest.fn()}
        disabled={false}
      />
    );
    expect(wrapper).toMatchSnapshot();
  });
});
