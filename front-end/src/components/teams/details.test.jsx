import React from 'react';
import { fireEvent } from '@testing-library/react';
import { within } from '@testing-library/dom';
import mockAxios from 'axios';
import { pick } from 'ramda';
import { reduxForm } from 'redux-form';

import TeamDetails from './details';
import { renderPage, defaultData, wait } from '../../utils/testing-library-utils';
import { act } from 'react-dom/test-utils';
import { addSnackbar } from '../../store/actions/snackbar';

const { teams, users, roles } = defaultData;
const defaultMatch = { params: { id: teams[0].id }, isExact: true };

jest.setTimeout(10000); // needed until canvas 9 upgrade where waiting for modal focus onload is no longer required

describe('Team Details', () => {
  mockAxios.get.mockImplementation(() => Promise.resolve({ data: { data: {} } }));

  afterEach(() => {
    mockAxios.reset();
  });

  it('update team journey', async() => {
    mockAxios.patch.mockImplementation(() => Promise.resolve({ data: { data: {} } }));
    // mocking edited value due to mock-store not supporting reducers
    const mockTeamInitial = {
      ...teams[1],
      description: 'new team description',
      owners: users.filter(u => u.roles.includes(teams[1].ownerRoleId)),
    };
    const mockTeamEdited = {
      ...mockTeamInitial,
      active: false, // trigger deactivate modal
    };
    const { baseElement, getByRole, getByText, queryByText, history } = await renderPage(
      <TeamDetails match={{ params: { id: mockTeamInitial.id }, isExact: true }}/>,
      { initialState: { form: { teamDetails: { values: mockTeamEdited, initial: mockTeamInitial } } } },
    );
    expect(baseElement).toMatchSnapshot(); // assert happy path rendering
    await act(async() => fireEvent.click(getByRole('button', { name: 'Update Team' })));

    // deactivate modal tests

    const modalHeading = 'Are you sure you want to deactivate this team?';
    const modalBody = 'Deactivating team will also deactivate all of its users and roles.';
    expect(getByText(modalHeading)).toBeInTheDocument();
    const desc = getByText(modalBody);
    expect(desc).toBeInTheDocument();
    // test modal dismiss function triggers
    const modal = desc.closest('.Modal');
    fireEvent.click(within(modal).getByRole('button', { name: 'Cancel' })); // via cancel button
    await act(async() => fireEvent.click(getByRole('button', { name: 'Update Team' })));
     // via modal backdrop
    fireEvent.click(getByText(modalHeading).closest('.Modal').querySelector('.InternalOverlay'));
    expect(queryByText(modalHeading)).not.toBeInTheDocument();
    await act(async() => fireEvent.click(getByRole('button', { name: 'Update Team' })));

    await act(async() => fireEvent.click(getByRole('button', { name: 'Deactivate Team' })));

    // assert form values PATCH'ed to api
    expect(mockAxios.patch).toHaveBeenCalledWith(`/teams/${mockTeamInitial.id}`, mockTeamEdited, undefined);
    expect(history.location.pathname).toStrictEqual('/teams');
  });

  it('update team journey - re-activate', async() => {
    mockAxios.patch.mockImplementation(() => Promise.resolve({ data: { data: {} } }));
    const team = { // initial state
      ...teams[1],
      active: false,
      owners: users.filter(u => u.roles.includes(teams[1].ownerRoleId)),
    };
    const teamEdited = { ...teams[1], active: true }; // edited state
    const { getByRole, getByLabelText, getByText, queryByText, history } = await renderPage(
      <TeamDetails match={{ params: { id: team.id }, isExact: true }}/>,
      { initialState: { form: { teamDetails: { values: teamEdited, initial: team } } } },
    );

    // re-activate modal tests
    // fireEvent.click(getByLabelText('Inactive')); change is simulated via preloaded state
    await act(async() => fireEvent.click(getByRole('button', { name: 'Update Team' })));

    const modalHeading = 'Reactivate team';
    const modalBody1 = 'Any users in the team who have a "Team owner" ' +
      'role will be activated with the team reactivation. ' +
      'The base "Team owner" and "Viewer" roles will also be activated.';
    const modalBody2 = 'All other users and roles in the team will not be activated. ' +
      'To reactivate those as well, please select the checkbox below.';
    // ignore button label, look for only canvas heading text
    expect(getByText(modalHeading, { ignore: 'span' })).toBeInTheDocument();
    expect(getByText(modalBody1)).toBeInTheDocument();
    expect(getByText(modalBody2)).toBeInTheDocument();

    // test modal dismiss function triggers
    const modal = getByText(modalBody1).closest('.Modal');
    fireEvent.click(within(modal).getByRole('button', { name: 'Cancel' })); // via cancel button
    await act(async() => fireEvent.click(getByRole('button', { name: 'Update Team' })));
     // via modal backdrop
    fireEvent.click(getByText(modalBody1).closest('.Modal').querySelector('.InternalOverlay'));
    expect(queryByText(modalBody1)).not.toBeInTheDocument();
    await act(async() => fireEvent.click(getByRole('button', { name: 'Update Team' })));

    fireEvent.click(getByLabelText('Reactivate all users and roles in the team'));
    await act(async() => fireEvent.click(getByRole('button', { name: 'Reactivate team' })));

    expect(mockAxios.patch).toHaveBeenCalledWith(`/teams/${team.id}`, teamEdited, undefined);
    expect(mockAxios.post).toHaveBeenCalledWith(
      `/teams/${team.id}/activate?activateChildren=true`,
      undefined,
      undefined
    );
    expect(history.location.pathname).toStrictEqual('/teams');
  });

  // until redux form is replaced with react hook forms
  // separate test cases are required to simulate different team status
  it('should show active label on team status toggle for active team', async() => {
    const activeTeam = { ...teams[1], active: true };
    const { getByText, queryByText } = await renderPage(
      <TeamDetails match={{ params: { id: activeTeam.id }, isExact: true }}/>,
      { initialState: { form: { teamDetails: { values: activeTeam } } } },
    );
    expect(getByText('Active')).toBeInTheDocument();
    expect(queryByText('Inactive')).not.toBeInTheDocument();
  });

  it('should show inactive label on team status toggle for inactive team', async() => {
    const inactiveTeam = { ...teams[1], active: false };
    const { getByText, queryByText } = await renderPage(
      <TeamDetails match={{ params: { id: inactiveTeam.id }, isExact: true }}/>,
      { initialState: { form: { teamDetails: { values: inactiveTeam } } } },
    );
    expect(queryByText('Active')).not.toBeInTheDocument();
    expect(getByText('Inactive')).toBeInTheDocument();
  });

  it('should render spinner if loading', async() => {
    const { queryByText } = await renderPage(
      <TeamDetails match={defaultMatch}/>,
      { initialState: { teams: { isLoading: true } } },
    );
    expect(queryByText('Create Team')).not.toBeInTheDocument();
  });

  it('should redirect to list view if team id provided is invalid', async() => {
    const notExactMatch = { params: { id: 'invalid-team-id' }, isExact: false };
    const { history } = await renderPage(<TeamDetails match={notExactMatch}/>);
    expect(history.location.pathname).toStrictEqual('/teams');
  });

  it('cancel button', async() => {
    const { getByRole, history } = await renderPage(<TeamDetails match={defaultMatch}/>);
    fireEvent.click(getByRole('button', { name: 'Cancel' }));
    expect(history.location.pathname).toStrictEqual('/teams');
  });
});

describe('Team Details - Owner popup', () => {
  mockAxios.get.mockImplementation(() => Promise.resolve({ data: { access: { containers: [], ruleSubTypes: [], pages: [] }, permissions: [ 'users_view', 'users_manage', 'roles_view', 'roles_manage' ] } }));

  afterEach(() => {
    mockAxios.reset();
  });

  it('add owner journey', async() => {
    const mockOwner = users[1]; // pick a user not already sole owner of another team
    const { baseElement, getByText, getByPlaceholderText, getByRole, queryByPlaceholderText, store } = await renderPage(
      <TeamDetails match={defaultMatch}/>,
      { initialState: { form: { teamDetails: { values: { owners: [] } } },
      teams: {
        items: teams,
      },
    } },
    );
    const openModalButton = getByRole('button', { name: 'Add team owner' });
    fireEvent.click(openModalButton);

    // open modal
    const modal = getByPlaceholderText('Enter sID').closest('.Modal__card');
    const cancelButton = within(modal).getByText('Cancel').closest('button');
    fireEvent.click(cancelButton);
    await wait(500);
    expect(queryByPlaceholderText('Enter sID')).not.toBeInTheDocument();
    fireEvent.click(getByRole('button', { name: 'Add team owner' }));
    // fill in sid to prompt auto-suggest
    await act(async() => {
      fireEvent.change(baseElement.querySelector('#sID-input'), { target: { value: mockOwner.sid } });
    });

    // click on suggestion to auto-fill rest of form
    const autoSuggestRow = getByText(`${mockOwner.name} (${mockOwner.sid})`).closest('li');
    fireEvent.click(autoSuggestRow);

    // add new owner
    const newModal = getByPlaceholderText('Enter sID').closest('.Modal__card');
    const addButton = within(newModal).getByRole('button', { name: 'Add team owner' });
    await act(async() => fireEvent.click(addButton));
    expect(store.getActions().find(a => a.type === '@@redux-form/CHANGE' &&
      a.meta.form === 'teamDetails' &&
      a.meta.field === 'owners' &&
      a.payload[0].sid === mockOwner.sid &&
      a.payload[0].email === mockOwner.email &&
      a.payload[0].name === mockOwner.name
    )).toBeTruthy();
    fireEvent.click(openModalButton);
  });

  it('delete owner journey', async() => {
    const mockOwner = users[0];
    const { getAllByText, store } = await renderPage(
      <TeamDetails match={defaultMatch}/>,
      { initialState: { form: { teamDetails: { values: { owners: [
        pick([ 'sid', 'email', 'name' ], mockOwner),
        pick([ 'sid', 'email', 'name' ], users[1]),
      ] } } } } },
    );
    const sidLabels = getAllByText(mockOwner.sid);
    expect(sidLabels.length > 0).toBeTruthy();
    const row = sidLabels[0].closest('tr');
    const deleteButton = within(row).getByTestId(`delete-owner-btn-${mockOwner.sid}`);
    await act(async() => fireEvent.click(deleteButton));

    // due to mock store limitation, ui is not updated, but we can check if delete team owner action was dispatched
    const updateOwnersAction = store.getActions().find(a => a.type === '@@redux-form/CHANGE' &&
      a.meta.form === 'teamDetails' &&
      a.meta.field === 'owners'
    );
    expect(updateOwnersAction.payload.find(u => u.sid === mockOwner.sid)).toBeFalsy();
  });

  it('team owner field validations', async() => {
    const mockUsers = JSON.parse(JSON.stringify(users));
    const mockOwner = mockUsers[0];
    mockOwner.roles.push(roles.find(r => r.name.includes('Team owner')).id); // already owner of admin team
    const { baseElement, getByText, getByRole, getByPlaceholderText } = await renderPage(
      <TeamDetails match={{ params: { id: teams[1].id }, isExact: true }}/>, // try to add owner for regular team
      { initialState: {
        form: { teamDetails: { values: { owners: [] } } },
        users: { isLoading: false, items: mockUsers },
      } },
    );
    const openModalButton = getByRole('button', { name: 'Add team owner' });
    fireEvent.click(openModalButton);

    // should reject empty fields
    const newModal = getByPlaceholderText('Enter sID').closest('.Modal__card');
    const addButton = within(newModal).getByRole('button', { name: 'Add team owner' });
    await act(async() => fireEvent.click(addButton));
    expect(getByText('sID is required')).toBeInTheDocument();
    expect(getByText('Email is required')).toBeInTheDocument();
    expect(getByText('Name is required')).toBeInTheDocument();

    // should reject new email if sid already exists
    await act(async() => {
      fireEvent.change(baseElement.querySelector('#sID-input'), { target: { value: mockOwner.sid } });
      fireEvent.change(baseElement.querySelector('#email-input'), { target: { value: '<EMAIL>' } });
      fireEvent.change(baseElement.querySelector('#fullName-input'), { target: { value: mockOwner.name } });
    });
    await act(async() => fireEvent.click(addButton));
    expect(getByText('Please verify the sID and email to make sure they match the user’s information.')).toBeInTheDocument();
  });
});

describe('confirm team owner modal', () => {
  beforeEach(() => {
    mockAxios.reset();
  });

  it('all team owners are from current team', async() => {
    const owner1 = users[0];
    const owner2 = { ...owner1, id: 1002 };
    const mockTeamInitial = { ...teams[0], owners: [ owner1 ] };
    const mockTeamEdited = { ...teams[0], owners: [ owner1, owner2 ] };
    mockAxios.patch.mockImplementation(() => Promise.resolve({ data: teams[0] }));
    const RFTeamsDetails = reduxForm({ form: 'teamDetails' })(TeamDetails);
    const { getByRole, queryByText, history, store } = await renderPage(
      <RFTeamsDetails match={{ params: { id: teams[0].id }, isExact: true }}/>,
      { initialState: { form: { teamDetails: { values: mockTeamEdited, initial: mockTeamInitial } } } },
    );

    // submit
    const updateButton = getByRole('button', { name: 'Update Team' });
    await act(async() => fireEvent.click(updateButton));

    // happy path, expect confirm team owner modal not shown, and submit successfully go through mock bff
    expect(queryByText('Confirm team owners')).not.toBeInTheDocument();
    const [ url, body ] = mockAxios.patch.mock.calls[0];
    expect(url).toBe('/teams/1');
    expect(body.owners).toStrictEqual([ owner1, owner2 ]); // double check edited owner list was submitted
    expect(history.location.pathname).toBe('/teams');
    const expected = addSnackbar({ bold: 'Team', message: ' was updated successfully' });
    expect(store.getActions()).toContainEqual(expected);
  });

  it('a mix of newly added users, existing users from target and external teams', async() => {
    // mock owners
    const deletedUser = users[0];
    const newUser = { ...users[0], id: 5, name: 'New User', sid: 's1001000', team_id: undefined, roles: undefined };
    const extTeamName = teams.find(t => t.id === users[1].team_id).name; // external user display name within the owners list
    const extUser1 = { ...users[1], name: 'External 1', team_name: extTeamName };
    const extUser2 = { ...users[1], id: 2002, name: 'External 2', sid: 's1002002', team_name: extTeamName };
    const internalUser = { ...users[0], name: 'Internal 1' };

    // mock team
    const mockTeamInitial = { ...teams[0], owners: [ deletedUser ] };
    const mockEditedOwnerList = [ newUser, extUser1, extUser2, internalUser ];
    mockEditedOwnerList.forEach(o => {
      if (o.team_id && o.team_id !== mockTeamInitial.team_id) {
        o.displayName = o.team_id ? `${o.name} (${o.team_name})` : o.name;
      }
    });
    const mockTeamEdited = {
      ...teams[0],
      active: false, // to trigger deactivate modal's confirm owner change state
      owners: mockEditedOwnerList,
      confirmTeamOwners: [ extUser1.id ],
    };

    // render with preconditioned state
    mockAxios.patch.mockImplementation(() => Promise.resolve({ data: mockTeamEdited }));
    const RFTeamsDetails = reduxForm({ form: 'teamDetails' })(TeamDetails);
    const { getByRole, getByText, getAllByRole, getByLabelText, queryByText, history, store } = await renderPage(
      <RFTeamsDetails match={{ params: { id: mockTeamInitial.id }, isExact: true }} />,
      { initialState: { form: { teamDetails: { values: mockTeamEdited, initial: mockTeamInitial } } } },
    );

    // submit and trigger confirm owner modal
    const updateButton = getByRole('button', { name: 'Update Team' });
    await act(async() => fireEvent.click(updateButton));
    const modalTitle = getByText('Confirm team owners');
    expect(modalTitle).toBeInTheDocument();
    const modal = modalTitle.closest('.Modal__card');

    // validate list owners owners
    expect(getByText('Team Owners')).toBeInTheDocument();
    const newOwners = getAllByRole('listitem');
    expect(newOwners).toHaveLength(mockEditedOwnerList.length);
    newOwners.forEach(listItem => {
      const rendered = listItem.innerHTML;
      expect(mockEditedOwnerList.some(u => [ u.name, u.displayName ].includes(rendered))).toBeTruthy();
    });

    // validate consent checkbox
    expect(getByText('By checking this box, you are agreeing to remove access to select users\' previous teams.')).toBeInTheDocument();
    expect(queryByText('You must agree to proceed')).not.toBeInTheDocument();
    fireEvent.click(within(modal).getByText('Continue'));
    expect(getByText('You must agree to proceed')).toBeInTheDocument();
    fireEvent.click(getByLabelText('I agree with making these changes'));
    expect(queryByText('You must agree to proceed')).not.toBeInTheDocument();

    // deactivate modal for confirm owner flow
    await act(async() => fireEvent.click(getByRole('button', { name: 'Continue' })));
    expect(getByText('Are you sure you want to deactivate this team?')).toBeInTheDocument();
    expect(getByText('Deactivating team will also deactivate all of its users and roles. ' +
      'Please note that previous changes to the team will not be saved unless you proceed.'
    )).toBeInTheDocument();

    // redux state already pre-conditioned with external user selected, now submit.
    // cannot simulate manipulation of external user selection due to limitations of
    // redux-mock-store and redux-form in test environment
    await act(async() => fireEvent.click(getByRole('button', { name: 'Deactivate Team' })));

    // assertions
    const [ url, body ] = mockAxios.patch.mock.calls[0];
    expect(url).toBe(`/teams/${mockTeamEdited.id}`);
    mockEditedOwnerList.forEach(expected => {
      // check if expected owners were all submitted to bff
      expect(body.owners.some(actual => actual.id === expected.id)).toBeTruthy();
    });
    expect(history.location.pathname).toBe('/teams');
    const expected = addSnackbar({ bold: 'Team', message: ` was updated successfully` });
    expect(store.getActions()).toContainEqual(expected);
  });

  it('should not persist confirm owner agreement checkbox state after modal is dismissed', async() => {
    // mock owners
    const extTeamName = teams.find(t => t.id === users[1].team_id).name;
    const extUser1 = { ...users[1], name: 'External 1', team_name: extTeamName };

    // mock team
    const mockTeamInitial = { ...teams[0], owners: [] };
    const mockEditedOwnerList = [ extUser1 ];
    const mockTeamEdited = { ...teams[0], owners: mockEditedOwnerList };

    // render with preconditioned state
    const RFTeamsDetails = reduxForm({ form: 'teamDetails' })(TeamDetails);
    const { getByRole, getByText, getByLabelText } = await renderPage(
      <RFTeamsDetails match={{ params: { id: mockTeamInitial.id }, isExact: true }} />,
      { initialState: { form: { teamDetails: { values: mockTeamEdited, initial: mockTeamInitial } } } },
    );

    // submit and trigger confirm owner modal
    const updateButton = getByRole('button', { name: 'Update Team' });
    await act(async() => fireEvent.click(updateButton));
    const modalTitle = getByText('Confirm team owners');
    expect(modalTitle).toBeInTheDocument();
    const modal = modalTitle.closest('.Modal__card');

    // validate consent checkbox
    expect(getByText('By checking this box, you are agreeing to remove access to select users\' previous teams.')).toBeInTheDocument();
    const agreeCheckbox = getByLabelText('I agree with making these changes');
    expect(agreeCheckbox.checked).toBeFalsy();
    fireEvent.click(agreeCheckbox);
    expect(agreeCheckbox.checked).toBeTruthy();

    // close and reopen, then revalidate
    fireEvent.click(within(modal).getByRole('button', { name: 'Cancel' }));
    fireEvent.click(getByRole('button', { name: 'Update Team' }));
    const modalReopened = getByText('Confirm team owners').closest('.Modal__card');
    expect(getByLabelText('I agree with making these changes').checked).toBeFalsy();
    expect(getByText('By checking this box, you are agreeing to remove access to select users\' previous teams.')).toBeInTheDocument();
    fireEvent.click(within(modalReopened).getByText('Continue'));
    expect(getByText('You must agree to proceed')).toBeInTheDocument();
  });
});
