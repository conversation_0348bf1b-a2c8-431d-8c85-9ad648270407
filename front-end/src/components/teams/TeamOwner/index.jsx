import React, { useState, useRef } from 'react';
import { isEmpty } from 'lodash';
import PropTypes from 'prop-types';
import { useSelector, useDispatch } from 'react-redux';

import IconErrorUniversal from 'canvas-core-react/lib/IconErrorUniversal';
import Modal from 'canvas-core-react/lib/internal/Modal';
import Card from 'canvas-core-react/lib/Card';
import TextButton from 'canvas-core-react/lib/TextButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import CanvasTextField from 'canvas-core-react/lib/TextField';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import Table from 'canvas-core-react/lib/Table';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import AlertBanner from 'canvas-core-react/lib/AlertBanner';
import IconAdd from 'canvas-core-react/lib/IconAdd';

import { tableColumns } from './ownersColumns';
import { getTeamOwners } from '../../../store/actions/teams';
import AutoComplete from './autoComplete';
import { sid, email } from '../../../utils/validation';

const TeamOwner = ({
  formValues,
  onAddOwner,
  onDeletedOwner,
  users = [],
  isFormDisabled,
  meta,
  isModalVisible,
  setIsModalVisible,
}) => {
  const sidRef = useRef();
  const emailRef = useRef();
  const nameRef = useRef();
  const dispatch = useDispatch();
  const [ alert, setAlert ] = useState({ title: '', message: '' });
  const [ errors, setErrors ] = useState({
    sid: '',
    email: '',
    name: '',
  });
  const { teams } = useSelector((state) => state);
  const { teamOwners } = teams;

  const closeModal = () => {
    sidRef.current.value = '';
    emailRef.current.value = '';
    nameRef.current.value = '';
    formReset();
    setIsModalVisible(false);
  };

  const validateAddOwner = async() => {
    if (
      !sidRef.current.value ||
      !emailRef.current.value ||
      !nameRef.current.value
    ) {
      setErrors({
        sid: !sidRef.current.value ? 'sID is required' : '',
        email: !emailRef.current.value ? 'Email is required' : '',
        name: !nameRef.current.value ? 'Name is required' : '',
      });
      return false;
    } else {
      setErrors({
        sid: '',
        email: '',
        name: '',
      });
    }

    // Format validation
    if (
      sid(sidRef.current.value) ||
      email(emailRef.current.value)
    ) {
      setErrors({
        sid: sid(sidRef.current.value),
        email: email(emailRef.current.value),
        name: '',
      });
      return false;
    };

    const user = users.find(u => u.sid === sidRef.current.value);
    if (user && user.email !== emailRef.current.value) {
      setErrors({
        sid: 'Please verify sID',
        email: 'Please verify email',
      });
      setAlert({
        title: 'sID and Email don’t match. ',
        message:
          'Please verify the sID and email to make sure they match the user’s information.',
      });
      return false;
    }
    return true;
  };

  const formReset = () => {
    setErrors({
      sid: '',
      email: '',
      name: '',
    });
    setAlert({ title: '', message: '' });
  };

  const handeAddNewOwner = async() => {
    const sid = sidRef.current.value;
    const newOwner = {
      sid,
      email: emailRef.current.value,
      name: `${nameRef.current.value}`,
    };
    const isValid = await validateAddOwner();
    if (isValid) {
      const userId = users.find(u => u.sid === sid);
      const usersTeam = teams.items.find(team => team.id === userId.team_id);
      onAddOwner(userId ? { ...newOwner, id: userId.id, team_id: userId.team_id, team_name: usersTeam.name } : { ...newOwner });
      setIsModalVisible(false);
    }
  };

  const handleDeleteOwner = (user) => {
    onDeletedOwner(user);
  };

  const onSelectUser = (user) => {
    sidRef.current.value = user.sid;
    emailRef.current.value = user.email;
    nameRef.current.value = user.name;
    formReset();
  };

  const renderResult = (user) => {
    return (
      <div style={{ display: 'flex', padding: 20 }}>
        <TextCaption component="p" bold style={{ marginRight: 5 }}>
          { `${user.name} (${user.sid})  ` }
        </TextCaption>
        <TextCaption component="p">{ user.email }</TextCaption>
      </div>
    );
  };

  const ownersData = formValues.owners
    ? formValues.owners.map((user) => ({
        ...user,
      }))
    : [];

  return (
    <Card className="team-details__card">
      <TextHeadline
        className="team-details__sub-header"
        component="h2"
        size={21}
      >
        Team owners
      </TextHeadline>
      <Table
        className="admin-list__table team-details__owner_table"
        id="admin-list-table"
        title=""
        columnFixed
        columns={tableColumns({
          handleDeleteOwner,
          isFormDisabled,
          numberOfOwners: formValues.owners?.length,
        })}
        data={ownersData}
      />
      { isEmpty(formValues.owners) && (
        <p className="team-details__empty-owners">
          No team owners have been added
        </p>
      ) }
      <TextButton
        className="team-details__add-owner-button"
        type="button"
        Icon={IconAdd}
        onClick={() => {
          dispatch(getTeamOwners());
          setIsModalVisible(true);
        }}
        disabled={isFormDisabled}
      >
        Add team owner
      </TextButton>
      { meta.error && meta.touched && (
        <div className="permissions-table__error-container">
          <IconErrorUniversal />
          <div className="permissions-table__error-message">
            <span>{ meta.error }</span>
          </div>
        </div>
      ) }
      <Modal
        isModalVisible={isModalVisible}
        headline="Add team owner"
        width="narrow"
        isOnlyClosedByButton={false}
        setModalVisible={() => {}}
        className="add-team-owner"
      >
        { alert.message && alert.title && (
          <AlertBanner type="error">
            <strong>{ alert.title }</strong>
            { alert.message }
          </AlertBanner>
        ) }

        <div className="team-details__add-owner-field">
          <AutoComplete
            label="Name"
            id="fullName"
            placeholder="Enter full name"
            filterKey="name"
            ref={nameRef}
            limit={10}
            maxHeight={400}
            Component={CanvasTextField}
            options={users}
            disabled={users.filter(({ id }) => teamOwners?.includes(id))}
            customRender={renderResult}
            onSelect={onSelectUser}
            error={
              errors?.name ? { label: 'Error', messages: [ errors.name ] } : null
            }
          />
        </div>
        <div className="team-details__add-owner-field">
          <AutoComplete
            options={users}
            disabled={users.filter(({ id }) => teamOwners?.includes(id))}
            customRender={renderResult}
            onSelect={onSelectUser}
            label="sID"
            id="sID"
            placeholder="Enter sID"
            filterKey="sid"
            ref={sidRef}
            limit={10}
            maxHeight={400}
            Component={CanvasTextField}
            error={
              errors?.sid ? { label: 'Error', messages: [ errors.sid ] } : null
            }
          />
        </div>
        <div className="team-details__add-owner-field">
          <AutoComplete
            label="Email"
            id="email"
            placeholder="Enter email"
            filterKey="email"
            ref={emailRef}
            limit={10}
            maxHeight={400}
            Component={CanvasTextField}
            options={users}
            disabled={users.filter(({ id }) => teamOwners?.includes(id))}
            customRender={renderResult}
            onSelect={onSelectUser}
            error={
              errors?.email ? { label: 'Error', messages: [ errors.email ] } : null
            }
          />
        </div>
        <div className="team-details__add-owner-actions">
          <SecondaryButton
            type="button"
            className="team-details__add-owner-actions__cancel"
            onClick={closeModal}
          >
            Cancel
          </SecondaryButton>
          <PrimaryButton type="button" onClick={handeAddNewOwner}>
            Add team owner
          </PrimaryButton>
        </div>
      </Modal>
    </Card>
  );
};

TeamOwner.propTypes = {
  formValues: PropTypes.object.isRequired,
  onAddOwner: PropTypes.func.isRequired,
  onDeletedOwner: PropTypes.func.isRequired,
  users: PropTypes.array,
  isFormDisabled: PropTypes.bool,
  meta: PropTypes.object.isRequired,
  isModalVisible: PropTypes.bool,
  setIsModalVisible: PropTypes.func,
};

export default TeamOwner;
