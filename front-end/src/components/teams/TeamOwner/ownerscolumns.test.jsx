import { tableColumns } from './ownersColumns';

describe('OwnersColumns', () => {
  it('should retun 4 columns ', () => {
    const columns = tableColumns({ handleDeleteOwner: jest.fn(() => {}) });
    expect(columns).toHaveLength(4);
  });

  it('name name col', () => {
    const columns = tableColumns({ handleDeleteOwner: jest.fn(() => {}) });
    const nameCol = columns[0];
    expect(nameCol.name).toStrictEqual('Name');
    expect(nameCol.style.textAlign).toStrictEqual('left');
    expect(nameCol.selector).toStrictEqual('name');
  });

  it('sID col', () => {
    const columns = tableColumns({ roles: [], handleChangeUserStatus: jest.fn(() => {}), canEdit: true });
    const sIdCol = columns[1];
    expect(sIdCol.name).toStrictEqual('sID');
    expect(sIdCol.style.textAlign).toStrictEqual('left');
    expect(sIdCol.selector).toStrictEqual('sid');
  });

  it('email col', () => {
    const columns = tableColumns({ handleDeleteOwner: jest.fn(() => {}) });
    const emailCol = columns[2];
    expect(emailCol.name).toStrictEqual('Email');
    expect(emailCol.style.textAlign).toStrictEqual('left');
    expect(emailCol.selector).toStrictEqual('email');
  });

  it('Action col', () => {
    const columns = tableColumns({ handleDeleteOwner: jest.fn(() => {}) });
    const sIdCol = columns[3];
    expect(sIdCol.name).toStrictEqual('Action');
    expect(sIdCol.selector).toStrictEqual('active');
  });
});
