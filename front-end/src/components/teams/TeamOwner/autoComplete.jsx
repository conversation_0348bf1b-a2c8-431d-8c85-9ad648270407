import React, { useState, forwardRef, useMemo, useRef } from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';

const AutoComplete = forwardRef((
  {
    options = [],
    onSelect,
    customRender,
    filterKey,
    limit = 1,
    Component,
    maxHeight,
    disabled = [],
    ...props
  },
  ref
) => {
  const firstSuggestionRef = useRef(null);
  const [ searchValue, setSearchValue ] = useState('');
  const [ showSuggests, setShowSuggests ] = useState(true);

  const filteredResults = useMemo(() => options
    .filter((s) => s[filterKey].toLowerCase().includes(searchValue))
    .slice(0, limit), [ searchValue ]);

  const handleSelect = (option) => {
    onSelect(option);
    setShowSuggests(false);
  };

  const handleSuggestionKeydown = (e, option, isDisabled) => {
    if (isDisabled && (e.key === 'Enter' || e.key === ' ')) {
      return e.preventDefault();
    }
    if (e.key === 'Escape') { setShowSuggests(false); } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      e.target.nextElementSibling && e.target.nextElementSibling.focus(); // focus on input below
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      e.target.previousElementSibling && e.target.previousElementSibling.focus(); // focus on input above
    } else if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault(); // prevents a bug where the input is focused and triggers the tagDelete handler
      handleSelect(option);
      setShowSuggests(false);
    }
  };

  const handleInputKeyDown = (e) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      firstSuggestionRef.current.focus();
    }
  };

  const handlerOnBlur = () => {
    /* The issue here is onBlur will be triggered once the user left the text input
       so if the user want to select an owner from the suggested result this function
       will be fired and it will hide the result list and we can't fire the onClick function
       any more because the result div will be removed form the DOM .
       so that's why I added this setTimeout function */
    setTimeout(() => {
      if (!document.activeElement.classList.contains('owner_option')) {
        setShowSuggests(false);
      }
      }, 1);
  };

  return (
    <div>
      <Component
        {...props}
        type="text"
        inputRef={ref}
        onChange={(e) => {
          setSearchValue(e.target.value);
          setShowSuggests(true);
        }}
        onBlur={handlerOnBlur}
        autoComplete="off"
        onKeyDown={handleInputKeyDown}
        onFocus={() => setShowSuggests(true)}
      />
      { !!filteredResults.length && searchValue && showSuggests && (
        <ul className="autoComplete__suggests" style={{ maxHeight }}>
          {
            filteredResults.map((option, index) => {
              const isDisabled = disabled.some(
                (o) => o[filterKey] === option[filterKey]
              );
              return (
                <li
                  key={option[filterKey]}
                  onClick={() => !isDisabled && handleSelect(option)}
                  className={classnames(
                    'owner_option',
                    { 'autoComplete__suggests__disable': isDisabled }
                  )}
                  onBlur={handlerOnBlur}
                  onKeyDown={(e) => handleSuggestionKeydown(e, option, isDisabled)}
                  ref={index === 0 ? firstSuggestionRef : null}
                  tabIndex={-1}
                >
                  { customRender(option) }
                </li>
              );
            }) }
        </ul>
      ) }
    </div>
  );
});

AutoComplete.propTypes = {
  options: PropTypes.array.isRequired,
  disabled: PropTypes.array,
  onSelect: PropTypes.func,
  customRender: PropTypes.func,
  filterKey: PropTypes.string.isRequired,
  limit: PropTypes.number,
  maxHeight: PropTypes.number,
  Component: PropTypes.oneOfType([ PropTypes.func, PropTypes.element ]),
};

export default AutoComplete;
