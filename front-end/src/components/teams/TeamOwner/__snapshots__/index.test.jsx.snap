// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TeamOwner - match snapshot should render the correct structure 1`] = `
<DocumentFragment>
  <div
    class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container team-details__card"
    type="flatSolid"
  >
    <h2
      class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP team-details__sub-header"
      color="black"
      size="21"
    >
      Team owners
    </h2>
    <div
      class="Tablestyle__StyledTable-canvas-core__sc-mfsabp-0 dvntiZ Table"
    >
      <div
        class="Tablestyle__StyledTableWrapperColumnFixed-canvas-core__sc-mfsabp-2 dTfXWN Table__wrapper--columnFixed"
      >
        <table
          aria-hidden="true"
          class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable admin-list__table team-details__owner_table"
        >
          <thead
            class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
            role="rowgroup"
          >
            <tr
              class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
              role="row"
            >
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 cuQFVG"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  Name
                </p>
              </th>
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  sID
                </p>
              </th>
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  Email
                </p>
              </th>
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  Action
                </p>
              </th>
            </tr>
          </thead>
          <tbody
            class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
            role="rowgroup"
          />
        </table>
      </div>
      <div
        class="Tablestyle__StyledTableWrapper-canvas-core__sc-mfsabp-1 kTteCq Table__wrapper"
        id="table-admin-list-table"
      >
        <table
          class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable admin-list__table team-details__owner_table"
        >
          <caption
            aria-live="polite"
            class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP TableHeadingstyle__StyledHeading-canvas-core__sc-2qo8vm-0 bPEdEe Table__title"
            color="black"
            size="21"
          />
          <thead
            class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
            role="rowgroup"
          >
            <tr
              class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
              role="row"
            >
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  Name
                </p>
              </th>
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  sID
                </p>
              </th>
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  Email
                </p>
              </th>
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  Action
                </p>
              </th>
            </tr>
          </thead>
          <tbody
            class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
            role="rowgroup"
          />
        </table>
      </div>
    </div>
    <p
      class="team-details__empty-owners"
    >
      No team owners have been added
    </p>
    <button
      class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button team-details__add-owner-button"
      color="blue"
      type="button"
    >
      <svg
        aria-hidden="true"
        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
        color="currentColor"
        focusable="false"
        role="presentation"
        size="16"
        viewBox="0 0 30 30"
      >
        <path
          d="M15.0001 2.27197L15.0001 27.7278"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M27.728 14.9999H2.27213"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      <span
        class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
      >
        Add team owner
      </span>
    </button>
  </div>
</DocumentFragment>
`;

exports[`TeamOwner - match snapshot should render the wrapper 1`] = `
<DocumentFragment>
  <div
    class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container team-details__card"
    type="flatSolid"
  >
    <h2
      class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP team-details__sub-header"
      color="black"
      size="21"
    >
      Team owners
    </h2>
    <div
      class="Tablestyle__StyledTable-canvas-core__sc-mfsabp-0 dvntiZ Table"
    >
      <div
        class="Tablestyle__StyledTableWrapperColumnFixed-canvas-core__sc-mfsabp-2 dTfXWN Table__wrapper--columnFixed"
      >
        <table
          aria-hidden="true"
          class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable admin-list__table team-details__owner_table"
        >
          <thead
            class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
            role="rowgroup"
          >
            <tr
              class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
              role="row"
            >
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 cuQFVG"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  Name
                </p>
              </th>
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  sID
                </p>
              </th>
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  Email
                </p>
              </th>
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  Action
                </p>
              </th>
            </tr>
          </thead>
          <tbody
            class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
            role="rowgroup"
          />
        </table>
      </div>
      <div
        class="Tablestyle__StyledTableWrapper-canvas-core__sc-mfsabp-1 kTteCq Table__wrapper"
        id="table-admin-list-table"
      >
        <table
          class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable admin-list__table team-details__owner_table"
        >
          <caption
            aria-live="polite"
            class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP TableHeadingstyle__StyledHeading-canvas-core__sc-2qo8vm-0 bPEdEe Table__title"
            color="black"
            size="21"
          />
          <thead
            class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
            role="rowgroup"
          >
            <tr
              class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
              role="row"
            >
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  Name
                </p>
              </th>
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  sID
                </p>
              </th>
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  Email
                </p>
              </th>
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  Action
                </p>
              </th>
            </tr>
          </thead>
          <tbody
            class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
            role="rowgroup"
          />
        </table>
      </div>
    </div>
    <p
      class="team-details__empty-owners"
    >
      No team owners have been added
    </p>
    <button
      class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button team-details__add-owner-button"
      color="blue"
      type="button"
    >
      <svg
        aria-hidden="true"
        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
        color="currentColor"
        focusable="false"
        role="presentation"
        size="16"
        viewBox="0 0 30 30"
      >
        <path
          d="M15.0001 2.27197L15.0001 27.7278"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M27.728 14.9999H2.27213"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      <span
        class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
      >
        Add team owner
      </span>
    </button>
  </div>
</DocumentFragment>
`;
