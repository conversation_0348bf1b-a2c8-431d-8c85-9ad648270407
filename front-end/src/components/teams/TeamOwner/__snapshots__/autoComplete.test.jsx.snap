// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AutoComplete should render the wrapper 1`] = `
<div>
  <Component
    autoComplete="off"
    inputRef={null}
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    onKeyDown={[Function]}
    type="text"
  />
</div>
`;

exports[`AutoComplete should should has options  1`] = `
<div>
  <Component
    autoComplete="off"
    inputRef={null}
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    onKeyDown={[Function]}
    type="text"
  />
</div>
`;

exports[`AutoComplete show showSuggests  1`] = `
<div>
  <Component
    autoComplete="off"
    inputRef={null}
    onBlur={[Function]}
    onChange={[Function]}
    onFocus={[Function]}
    onKeyDown={[Function]}
    showSuggests={true}
    type="text"
  />
</div>
`;
