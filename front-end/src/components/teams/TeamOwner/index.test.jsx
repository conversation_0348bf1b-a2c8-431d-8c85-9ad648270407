import React from 'react';
import { Provider } from 'react-redux';
import { render } from '@testing-library/react';
import TeamOwner from './index';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';

const mockStore = configureMockStore([ thunk ]);
const defaultStore = mockStore({
  users: {
    items: [ {
      active: true,
      email: '<EMAIL>',
      id: 1,
      name: 'test user',
      roles: [ 1, 5, 23, 61 ],
      sid: 's123456',
      team_id: 1,
    } ],
  },
  teams: {
    teamOwners: [ 1 ],
  },
});

describe('TeamOwner - match snapshot', () => {
  it('should render the correct structure', () => {
    const { asFragment } = render(
      <Provider store={defaultStore}>
        <TeamOwner
          formValues={{ users: [] }}
          onAddOwner={() => {}}
          onDeletedOwner={() => {}}
          meta={{ error: false, touched: false }}
        />
      </Provider>
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the wrapper', () => {
    const { asFragment } = render(
      <Provider store={defaultStore}>
        <TeamOwner
          formValues={{ users: [] }}
          onAddOwner={() => {}}
          onDeletedOwner={() => {}}
          meta={{ error: false, touched: false }}
        />
      </Provider>
    );
    expect(asFragment()).toMatchSnapshot();
  });
});
