import React from 'react';
import IconDelete from 'canvas-core-react/lib/IconDelete';

export const tableColumns = ({ handleDeleteOwner, isFormDisabled, numberOfOwners }) => {
  const onDeleteOwner = (user) => {
    if (!isFormDisabled && numberOfOwners > 1) {
      handleDeleteOwner({ user });
    }
  };

  return [
    {
      name: 'Name',
      style: { textAlign: 'left' },
      selector: 'name',
    },
    {
      name: 'sID',
      style: { textAlign: 'left' },
      selector: 'sid',
    },
    {
      name: 'Email',
      style: { textAlign: 'left' },
      selector: 'email',
    },
    {
      name: 'Action',
      cellFormatter: (row) => (
        <IconDelete
          data-testid={`delete-owner-btn-${row.sid}`}
          size={20}
          onClick={() => onDeleteOwner(row)}
          style={{ cursor: isFormDisabled || numberOfOwners <= 1 ? 'not-allowed' : 'pointer' }}
        />
      ),
      selector: 'active',
    },
  ];
};
