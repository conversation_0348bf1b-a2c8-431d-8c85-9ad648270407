.autoComplete {
  &__suggests {
    position: absolute;
    width: 85%;
    background: #fff;
    border: 0.1rem solid #c6c6c6;
    box-shadow: 0 0.2rem 1rem rgba(0, 34, 91, 0.11);
    border-radius: 0 0 0.1rem 0.1rem;
    z-index: 10;
    cursor: pointer;
    overflow: scroll;

    &__disable {
      cursor: auto;

      * {
        color: #939393 !important;
      }
    }
  }
}

.owner_option {
  border: none;
  width: 100%;
}

.owner_option:hover {
  background: #ede6e6;
}

.owner_option:focus-visible {
  outline-color: #d86a76 !important; // should replace this color by Dark  Canvas Dark Red-100 once upgrading canvas version
  background: #ede6e6;
}

.add-team-owner {
  .Modal__card {
    max-height: 95vh;
    overflow: auto;
  }
}
