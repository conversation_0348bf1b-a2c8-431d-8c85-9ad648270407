import React from 'react';
import { shallow } from 'enzyme';
import AutoComplete from './autoComplete';
import CanvasTextField from 'canvas-core-react/lib/TextField';

describe('AutoComplete', () => {
  it('should render the wrapper', () => {
    const wrapper = shallow(<AutoComplete options={[]} filterKey="" Component={() => <CanvasTextField id='' label='' />} />);
    expect(wrapper).toMatchSnapshot();
  });

  it('should should has options ', () => {
    const wrapper = shallow(
      <AutoComplete
        Component={() => <CanvasTextField id='' label='' />}
        options={[
          {
            active: true,
            email: '<EMAIL>',
            id: 1,
            name: 'test user',
            roles: [ 1, 5, 23, 61 ],
            sid: 's123456',
            team_id: 1,
          },
        ]}
        onSelect={() => { }}
        customRender={() => { }}
        filterKey="sid"
      />
    );
    expect(wrapper).toMatchSnapshot();
  });

  it('show showSuggests ', () => {
    const wrapper = shallow(
      <AutoComplete
        Component={() => <CanvasTextField id='' label='' />}
        options={[
          {
            active: true,
            email: '<EMAIL>',
            id: 1,
            name: 'test user',
            roles: [ 1, 5, 23, 61 ],
            sid: 's123456',
            team_id: 1,
          },
        ]}
        onSelect={() => { }}
        customRender={() => { }}
        filterKey="sid"
        showSuggests
      />
    );
    expect(wrapper).toMatchSnapshot();
  });
});
