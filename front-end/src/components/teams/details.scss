.team-details {
  @extend .admin-details;
  &__card { @extend .admin-details__card; }

  &__field,
  &__ownership-row { @extend .admin-details__field; }
  &__sub-header { @extend .admin-details__sub-header; }

  &__ownership-row {
    @include mq($from: tablet) {
      display: flex;
      align-items: center;
    }
  }

  &__owner-field {
    @include mq($from: tablet) {
      &:not(:last-child) {
        margin-right: 1.8rem;
      }
      // I measured the fields in figma and used their widths to determine fluid percentages
      $figmaNameWidth: 375;
      $figmaSIDWidth: 227;
      $figmaEmailWidth: 428;
      $figmaFieldsTotalWidth: $figmaNameWidth + $figmaSIDWidth + $figmaEmailWidth;

      &--name {
        width: percentage($figmaNameWidth/$figmaFieldsTotalWidth);
      }

      &--sid {
        width: percentage($figmaSIDWidth/$figmaFieldsTotalWidth);
      }

      &--email {
        width: percentage($figmaEmailWidth/$figmaFieldsTotalWidth);
      }
    }
  }

  &__delete-button {
    padding: 1.2rem;
    background-color: transparent;
    border: 0.1rem solid transparent;
    cursor: pointer;
    height: min-content;

    &:focus-within,
    &:focus {
      border: 0.1rem solid $brand-blue;
      box-shadow: 0 0 0 0.1rem $brand-blue;
      border-radius: 0.3rem;
    }
  }

  &__add-owner-button {
    @include mq($from: tablet) {
      position: relative;
      left: -2.4rem; // width of left margin + padding
    }
  }

  &__action-buttons {
    display: flex;
    justify-content: flex-end;
  }

  &__action-button {
    &:not(:last-child) {
      margin-right: 1.6rem;
    }
  }

  &__empty-owners {
    padding: 1.6rem 0;
    text-align: center;
    font-size: 1.6rem;
    border-bottom: 0.1rem  solid $canvas-gray-400;
  }

  &__owner_table tr {
    border-bottom: 0.1rem  solid $canvas-gray-400;
  }

  &__add-owner-field {
    margin-top: 1.6rem;
  }

  &__add-owner-actions {
    margin-top: 1.6rem;
    display: flex;
    justify-content: flex-end;

    &__cancel {
      margin-right: 1.6rem;
    }
  }

  &__checkbox-field {
    margin-top: 2rem;
  }

  .confirm-owners {
    font-size: 1.6rem;

    &__owners-list {
      margin-top: 1.2rem;

      label {
        font-size: 1.6rem;
      }

      ul {
        margin: 1.8rem 0 0 2rem;

        li {
          margin-bottom: 0.8rem;
        }
      }
    }

    &__agreement {
      margin-top: 2.4rem;
    }

    // Temporary solution, there is code in main.scss to globally bold all checkbox labels
    // after updating the Canvas, we will have the option labelWeight to bold it on case by case basis
    // https://core-react-ist.apps.stg.azr-cc-pcf.cloud.bns/storybook/?path=/story/component-checkbox--single-checkbox&args=labelWeight:bold
    &__agreement-checkbox .Label__label--checkbox {
      font-weight: normal;
    }

    .Button__button--secondary {
      min-height: 4.8rem;
    }
  }
}
