import React, { useState } from 'react';
import PropTypes from 'prop-types';

import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import TextBody from 'canvas-core-react/lib/TextBody';
import Checkbox from 'canvas-core-react/lib/Checkbox';
import InputGroup from 'canvas-core-react/lib/InputGroup';

import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';

const confirmOwner = ({
  isModalVisible,
  onCancel,
  onSubmit,
  owners = [],
}) => {
  if (!isModalVisible) {
    return null;
  }

  const [ agreed, setAgreed ] = useState(undefined);
  const [ error, setError ] = useState(undefined);

  const handleAgree = e => {
    const selection = e.target.checked;
    setAgreed(selection);
    selection && setError(false);
  };

  const handleSubmit = () => {
    if (!agreed) {
      return setError({ label: 'Error', message: 'You must agree to proceed' });
    }
    onSubmit();
  };

  return (
    <ModalDialogue
      className="confirm-owners"
      headline="Confirm team owners"
      isModalVisible={isModalVisible}
      setModalVisible={onCancel}
      primaryAction={handleSubmit}
      primaryButtonLabel="Continue"
      secondaryAction={onCancel}
      secondaryButtonLabel="Cancel"
    >
      <TextBody component='p'>
        You have selected team owners who are already existing users on another team. If you would like to switch them to this new team, they will no longer have access to their previous team.
      </TextBody>
      { owners.length > 0 &&
        <div className="confirm-owners__owners-list">
          <TextSubtitle component='label'>Team Owners</TextSubtitle>
          <ul>
            { owners.map(o => <li key={`${o.id}-${o.name}`}>{ o.displayName || o.name }</li>) }
          </ul>
        </div>
      }
      <InputGroup
        error={error || undefined}
        id="confirm-owners__agreement"
        className="confirm-owners__agreement"
        legend="By checking this box, you are agreeing to remove access to select users' previous teams."
      >
        <Checkbox
          id="confirm-owners__agreement-checkbox"
          className="confirm-owners__agreement-checkbox"
          label="I agree with making these changes"
          labelWeight="regular"
          onChange={handleAgree}
        />
      </InputGroup>
    </ModalDialogue>
  );
};

confirmOwner.propTypes = {
  isModalVisible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  owners: PropTypes.array,
};

export default confirmOwner;
