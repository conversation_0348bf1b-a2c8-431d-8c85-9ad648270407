import React, { useEffect, useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Field, change, initialize, reduxForm, getFormValues, getFormInitialValues } from 'redux-form';
import { connect, useDispatch, useSelector } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Redirect, useHistory } from 'react-router-dom';

import Card from 'canvas-core-react/lib/Card';
import AlertBanner from 'canvas-core-react/lib/AlertBanner';
import Checkbox from 'canvas-core-react/lib/Checkbox';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';

import { InputTextField, InputToggleField } from '../formFields';
import { editTeamPageOnload, saveTeamValues, initializeTeamForm, setTeamActivation } from '../../store/actions/teams';
import { requireAtLeastOne, requiredSpecific } from '../../utils/validation';
import TeamOwner from './TeamOwner';
import AccessCard from './Access';
import ConfirmOwnersModal from './ConfirmOwnersModal';

const validations = {
  requiredName: requiredSpecific('Team name'),
  requiredDescription: requiredSpecific('Team description'),
};

export const TeamDetails = ({
  isLoading,
  formValues,
  saveTeamValues,
  handleSubmit,
  initialize,
  initializeTeamForm,
  dirty,
  setTeamActivation,
  editTeamPageOnload,
  match: {
    params: {
      id: teamId,
    },
    isExact,
  },
  users,
  formInitialValues,
}) => {
  // Re-route to main teams page if not an exact match (i.e. /teams/create) or invalid id format
  if (!isExact || (teamId && isNaN(teamId))) {
    return <Redirect to="/teams" />;
  }

  const dispatch = useDispatch();
  const history = useHistory();

  const authenticated = useSelector(state => state.authenticated);
  const { canViewOwnTeam, canEditAllTeams, canEditOwnTeam } = useMemo(() => authenticated?.permissionLevels || {}, [ authenticated ]);

  if (!canViewOwnTeam) {
    return (<AlertBanner type="error">You do not have permissions to view this page</AlertBanner>);
  };

  const teams = useSelector(state => state.teams.items);

  const [ isFormDisabled, setIsFormDisabled ] = useState(true);
  const [ isCancelPopupOpen, setIsCancelPopupOpen ] = useState(false);
  const [ isReactivationModalOpen, setIsReactivationModalOpen ] = useState(false);
  const [ reactivateChildren, setReactivateChildren ] = useState(false);
  const [ isDeactivationModalOpen, setIsDeactivationModalOpen ] = useState(false);
  const [ isTeamOwnerModalOpen, setIsTeamOwnerModalOpen ] = useState(false);
  const [ confirmTeamOwnersModal, setConfirmTeamOwnersModal ] = useState(false);
  const [ teamData, setTeamData ] = useState(false);

  const toActivate = useMemo(
    () => teamId && !formInitialValues.active && formValues.active,
    [ teamId, formInitialValues.active, formValues.active ]
  );
  const toDeactivate = useMemo(
    () => teamId && formInitialValues.active && !formValues.active,
    [ teamId, formInitialValues.active, formValues.active ]
  );

  const teamSwitcherExists = useMemo(() => {
    return (formValues.owners || []).some(u => u.team_id && u.team_id !== Number(teamId));
  }, [ formValues.owners, teamId ]);

  useEffect(() => {
    editTeamPageOnload();
  }, []);

  useEffect(() => {
    const init = async() => {
      const currentTeam = await initializeTeamForm(teamId, users, teams);

      setTeamData(currentTeam); // if is edit flow populate with data from backend
      initialize(currentTeam);
    };
    (users.length) && teams && teams.length && init();
  }, [ users, teams, teamId ]);

  useEffect(() => {
    setIsFormDisabled(formValues.id
      ? !(canEditAllTeams || (canEditOwnTeam && formValues.id === authenticated?.team_id))
      : !canEditAllTeams
    );
  }, [ formValues.id ]);

  const confirmationModals = (values) => {
    setConfirmTeamOwnersModal(false);
    if (toActivate) {
      setIsReactivationModalOpen(true);
    } else if (toDeactivate) {
      setIsDeactivationModalOpen(true);
    } else {
      submitTeamsForm(values, false);
    }
  };

  const onSubmit = async(values) => {
    return teamSwitcherExists
      ? setConfirmTeamOwnersModal(true)
      : confirmationModals(values);
  };

  const submitTeamsForm = async(values, reactivateChildren) => {
    const payload = {
      ...values,
      access: {
        ...(values.access?.containers && { containers: values.access.containers }),
        ...(values.access?.pages && { pages: values.access.pages }),
        ...(values.access?.ruleSubTypes && { ruleSubTypes: values.access.ruleSubTypes }),
      },
      accessApplicationIds: undefined,
      ownedApplications: undefined,
    };
    await saveTeamValues(teamId, payload);
    if (reactivateChildren) {
      setTeamActivation(teamId, true, true);
    }
    history.push('/teams');
  };

  const onAddOwner = (user) => {
    const isAlreadyAdded = formValues.owners.some(u => u.sid === user.sid);
    if (isAlreadyAdded) return;
    const displayName = user.team_id ? `${user.name} (${user.team_name})` : user.name;
    const newOwner = !user.team_id && user.team_id === Number(teamId) ? user : { ...user, displayName };
    const newOwners = [ ...formValues.owners, newOwner ];
    dispatch(change('teamDetails', 'owners', newOwners));
  };

  const onDeletedOwner = ({ user }) => {
    const newOwners = formValues.owners.filter(u => u.sid !== user.sid);
    dispatch(change('teamDetails', 'owners', newOwners));
  };

  const handleConfirmOwners = () => {
    const newOwners = formValues.owners.reduce((acc, curr) => {
      delete curr.displayName;
      acc.push(curr);
      return acc;
    }, []);
    confirmationModals({ ...formValues, owners: newOwners });
  };

  if (isLoading || isLoading === undefined) {
    return <IconSpinner size={32} />;
  }

  return (
    <div className="team-details">
      <TextHeadline component="h1">{ teamId ? 'Edit Team' : 'Create Team' }</TextHeadline>
      <div className="team-details__action-bar" />
      <form onSubmit={handleSubmit(onSubmit)}>
        <Card className="team-details__card">
          <TextHeadline className="team-details__sub-header" component="h2" size={21}>Team Information</TextHeadline>
          <Field
            className="team-details__field"
            disabled={isFormDisabled || Number(teamId) === 1}
            name="active"
            label=""
            secondaryLabel={formValues.active ? 'Active' : 'Inactive'}
            component={InputToggleField}
            options={[
              { id: true, name: 'Active' },
              { id: false, name: 'Inactive' },
            ]}
          />
          <Field
            className="team-details__field"
            disabled={isFormDisabled || Number(teamId) === 1}
            name="name"
            label="Team Name"
            placeholder="Enter team name"
            component={InputTextField}
            validate={[ validations.requiredName ]}
          />
          <Field
            className="team-details__field"
            disabled={isFormDisabled}
            name="description"
            label="Team Description"
            placeholder="Enter team description"
            component={InputTextField}
            validate={[ validations.requiredDescription ]}
          />
        </Card>
        <Field
          name="owners"
          validate={[ requireAtLeastOne ]}
          component={({ meta }) => (
            <TeamOwner
              formValues={formValues}
              onAddOwner={onAddOwner}
              onDeletedOwner={onDeletedOwner}
              isFormDisabled={isFormDisabled}
              users={users}
              meta={meta}
              setIsModalVisible={setIsTeamOwnerModalOpen}
              isModalVisible={isTeamOwnerModalOpen}
            />
          )}
        />
        <Field
          initialSelectionsAccess={teamData?.access || {}}
          initialSelectionsPermissions={teamData?.permissions || [ 'users_view', 'users_manage', 'roles_view', 'roles_manage' ]}
          name="access"
          component={AccessCard}
          isFormDisabled={isFormDisabled}
          teamName={teamData?.name || ''}
        />
        <div className="team-details__action-buttons">
          <SecondaryButton
            className="team-details__action-button"
            onClick={() => dirty ? setIsCancelPopupOpen(true) : history.push('/teams')}
            type="button"
          >
            Cancel
          </SecondaryButton>
          <PrimaryButton
            className="team-details__action-button"
            disabled={isFormDisabled}
            type="submit"
          >
            { teamId ? 'Update Team' : 'Create Team' }
          </PrimaryButton>
        </div>
      </form>

      <ModalDialogue
        isModalVisible={isCancelPopupOpen}
        headline="Are you sure?"
        primaryButtonLabel="Yes"
        primaryAction={() => history.push('/teams')}
        secondaryButtonLabel="No"
        secondaryAction={() => setIsCancelPopupOpen(false)}
        setModalVisible={() => setIsCancelPopupOpen(false)}
      >
        If you cancel, any unsaved changes will be lost.
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={isReactivationModalOpen}
        headline="Reactivate team"
        primaryButtonLabel="Reactivate team"
        primaryAction={() => {
          submitTeamsForm(formValues, reactivateChildren);
          setReactivateChildren(false);
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => {
          setIsReactivationModalOpen(false);
          setReactivateChildren(false);
        }}
        setModalVisible={() => setIsReactivationModalOpen(false)}
      >
          <p>Any users in the team who have a &quot;Team owner&quot; role will be activated with the team reactivation. The base &quot;Team owner&quot; and &quot;Viewer&quot; roles will also be activated.</p><br />
          <p>All other users and roles in the team will not be activated. To reactivate those as well, please select the checkbox below.</p>
          <Checkbox
            id="teams-list__modal-checkbox"
            checked={reactivateChildren}
            onChange={(e) => setReactivateChildren(e.target.checked)}
            label="Reactivate all users and roles in the team"
          />
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={isDeactivationModalOpen}
        headline="Are you sure you want to deactivate this team?"
        primaryButtonLabel="Deactivate Team"
        primaryAction={() => submitTeamsForm(formValues, false)}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setIsDeactivationModalOpen(false)}
        setModalVisible={() => setIsDeactivationModalOpen(false)}
      >
        <p>
          Deactivating team will also deactivate all of its users and roles.
          { teamSwitcherExists && ` Please note that previous changes to the team will not be saved unless you proceed.` }
        </p>
      </ModalDialogue>

      <ConfirmOwnersModal
        isModalVisible={confirmTeamOwnersModal}
        onCancel={() => setConfirmTeamOwnersModal(false)}
        onSubmit={handleSubmit(handleConfirmOwners)}
        owners={formValues.owners}
      />
    </div>
  );
};

TeamDetails.propTypes = {
  isLoading: PropTypes.bool.isRequired,
  saveTeamValues: PropTypes.func.isRequired,
  initialize: PropTypes.func.isRequired,
  initializeTeamForm: PropTypes.func.isRequired,
  setTeamActivation: PropTypes.func.isRequired,
  editTeamPageOnload: PropTypes.func.isRequired,
  dirty: PropTypes.bool.isRequired,
  match: PropTypes.object.isRequired,
  users: PropTypes.array,
  formValues: PropTypes.object.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  formInitialValues: PropTypes.object,
};

const mapStateToProps = state => ({
  isLoading: state.teams?.isLoading ||
    state.users?.isLoading ||
    state.applications?.isLoading ||
    state.containers?.isLoading ||
    state.pages?.isLoading ||
    state.ruleSubTypes?.isLoading ||
    state.ruleTypes?.isLoading ||
    false,
  users: state.users?.items || [],
  formInitialValues: getFormInitialValues('teamDetails')(state) || {},
  formValues: getFormValues('teamDetails')(state) || {},
});

const mapDispatchToProps = dispatch => bindActionCreators({
  saveTeamValues,
  initializeTeamForm,
  setTeamActivation,
  editTeamPageOnload,
  initialize: () => initialize('teamDetails'),
}, dispatch);

export default connect(mapStateToProps, mapDispatchToProps)(
  reduxForm({ form: 'teamDetails', initialValues: { active: true } })(TeamDetails)
);
