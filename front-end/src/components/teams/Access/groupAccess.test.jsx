import React from 'react';
import GroupAccess from './GroupAccess';
import { render, fireEvent } from '@testing-library/react';

const mockItems = [
  {
    type: 'group',
    label: 'Campaigns',
    expanded: true,
    id: 'campaigns',
    checked: {
      view: false,
      manage: false,
    },
    disabled: {
      view: false,
      manage: false,
    },
    children: [
      {
        type: 'group',
        expanded: true,
        checked: {
          view: false,
          manage: false,
        },
        disabled: {
          view: false,
          manage: false,
        },
        children: [
          {
            type: 'group',
            expanded: true,
            checked: {
              view: false,
              manage: false,
            },
            disabled: {
              view: false,
              manage: false,
            },
            children: [
              {
                type: 'item',
                checked: {
                  view: false,
                  manage: false,
                },
                disabled: {
                  view: false,
                  manage: false,
                },
                label: 'activities',
                id: 'campaigns-3-pages-1',
                data: {
                  page_id: 1,
                  application_id: 3,
                  rule_type_id: 2,
                  dataType: 'pages',
                },
              },
            ],
            label: 'Pages',
            id: 'campaigns-3-pages',
          },
        ],
        label: 'Nova Mobile Campaigns',
        id: 'campaigns-3',
      },
    ],
  },
];
describe('test render group access', () => {
  const onChangeStatus = jest.fn();
  const onChangeExpanded = jest.fn();
  it('should render the group access (enabled)', () => {
    const { asFragment } = render(
      <GroupAccess
        disabled={{ view: false, manage: false }}
        checked={{ view: false, manage: false }}
        id="test-label-id"
        groupChildren={mockItems}
        onChangeStatus={onChangeStatus}
        onChangeExpanded={onChangeExpanded}
        expanded={false}
        label="test-label"
        level={1}
        isFormDisabled={false}
      />
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the group access (enabled & expanded)', () => {
    const { asFragment } = render(
      <GroupAccess
        disabled={{ view: false, manage: false }}
        checked={{ view: false, manage: false }}
        id="test-label-id"
        groupChildren={mockItems}
        onChangeStatus={onChangeStatus}
        onChangeExpanded={onChangeExpanded}
        level={1}
        expanded
        label="test-label"
        isFormDisabled={false}
      />
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the group access (disabled)', () => {
    const { asFragment } = render(
      <GroupAccess
        disabled={{ view: true, manage: true }}
        checked={{ view: false, manage: false }}
        id="test-label-id"
        groupChildren={mockItems}
        onChangeStatus={onChangeStatus}
        onChangeExpanded={onChangeExpanded}
        level={1}
        expanded
        label="test-label"
        isFormDisabled={false}
      />
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('should check the label', () => {
    const { getAllByText } = render(
      <GroupAccess
        disabled={{ view: true, manage: true }}
        checked={{ view: false, manage: false }}
        id="test-label-id"
        groupChildren={mockItems}
        onChangeStatus={onChangeStatus}
        onChangeExpanded={onChangeExpanded}
        level={1}
        expanded
        label="test-label"
        isFormDisabled={false}
      />
    );
    const label = getAllByText('test-label');
    expect(label).toHaveLength(1);
  });

  it('should check change function (view)', () => {
    const onChangeStatus = jest.fn();
    const { getByTestId } = render(
      <GroupAccess
        disabled={{ view: true, manage: true }}
        label="test-label"
        id="test-label-id"
        onChangeStatus={onChangeStatus}
        onChangeExpanded={onChangeExpanded}
        level={1}
        groupChildren={mockItems}
        expanded
        checked={{ view: false, manage: false }}
        isFormDisabled={false}
      />
    );
    const textField = getByTestId('test-label-id-view');
    fireEvent.click(textField);
    expect(onChangeStatus).toHaveBeenCalled();
  });

  it('should check change function (manage)', () => {
    const onChangeStatus = jest.fn();
    const { getByTestId } = render(
      <GroupAccess
        disabled={{ view: true, manage: true }}
        label="test-label"
        id="test-label-id"
        onChangeStatus={onChangeStatus}
        onChangeExpanded={onChangeExpanded}
        level={1}
        expanded
        checked={{ view: false, manage: false }}
        groupChildren={mockItems}
        isFormDisabled={false}
      />
    );
    const textField = getByTestId('test-label-id-manage');
    fireEvent.click(textField);
    expect(onChangeStatus).toHaveBeenCalled();
  });
});
