import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { change } from 'redux-form';
import PropTypes from 'prop-types';
import { groupBy, concat, omit, isEqual, mapValues } from 'lodash';

import Card from 'canvas-core-react/lib/Card';
import IconErrorUniversal from 'canvas-core-react/lib/IconErrorUniversal';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import InputGroup from 'canvas-core-react/lib/InputGroup';
import Checkbox from 'canvas-core-react/lib/Checkbox';
import Grid from 'canvas-core-react/lib/Grid';
import Row from 'canvas-core-react/lib/Row';
import Column from 'canvas-core-react/lib/Column';

import { useManageData } from '../../../hooks/useManageData';
import { PIGEON_TEAM } from '../../../constants';
import AccessTooltip from './AccessTooltip';
import GroupAccess from './GroupAccess';

const VIEW_ACCESS_LEVEL = 'view';
const MANAGE_ACCESS_LEVEL = 'manage';

const AccessCard = ({
  input,
  initialSelectionsAccess,
  initialSelectionsPermissions,
  meta,
  isFormDisabled,
  teamName,
}) => {
  const dispatch = useDispatch();

  const { campaigns, alerts, placements, accesses, variables, messageCentreAccesses, offerManagementAccesses, loading } =
    useManageData({ initialSelectionsAccess, initialSelectionsPermissions });
  const [ data, setData ] = useState([]);
  const [ allViewChecked, setAllViewChecked ] = useState(false);
  const [ allManageChecked, setAllManageChecked ] = useState(false);

  const viewManageAllCheck = accessData => {
    if (accessData.every(d => d.checked.view)) {
      setAllViewChecked(true);
    } else if (accessData.some(d => d.checked.view)) {
      setAllViewChecked('indeterminate');
    } else {
      setAllViewChecked(false);
    }

    if (accessData.every(d => d.checked.manage)) {
      setAllManageChecked(true);
    } else if (accessData.some(d => d.checked.view)) {
      setAllManageChecked('indeterminate');
    } else {
      setAllManageChecked(false);
    }
  };

  useEffect(() => {
    if (!loading && loading !== undefined && initialSelectionsAccess) {
      const initData = [
        {
          type: 'group',
          label: 'Campaigns',
          expanded: false,
          id: 'campaigns',
          checked: {
            view: false,
            manage: false,
          },
          disabled: {
            view: false,
            manage: false,
          },
          children: [ ...campaigns ],
        },
        {
          type: 'group',
          label: 'Alerts',
          expanded: false,
          id: 'alerts',
          checked: {
            view: false,
            manage: false,
          },
          disabled: {
            view: false,
            manage: false,
          },
          children: [ ...alerts ],
        },
        {
          type: 'group',
          label: 'Placement',
          expanded: false,
          id: 'placements',
          checked: {
            view: false,
            manage: false,
          },
          disabled: {
            view: false,
            manage: false,
          },
          children: [ ...placements ],
        },
        {
          type: 'group',
          label: 'Access control',
          expanded: false,
          id: 'access-control',
          checked: {
            view: false,
            manage: false,
          },
          disabled: {
            view: false,
            manage: false,
          },
          children: [ ...accesses ],
        },
        {
          type: 'group',
          label: 'Variable mapping',
          expanded: false,
          id: 'variable-mapping',
          checked: {
            view: false,
            manage: false,
          },
          disabled: {
            view: false,
            manage: false,
          },
          children: [ ...variables ],
        },
        {
          type: 'group',
          label: 'Message Centre',
          expanded: false,
          id: 'message-centre',
          checked: {
            view: false,
            manage: false,
          },
          disabled: {
            view: false,
            manage: false,
          },
          children: [ ...messageCentreAccesses ],
        },
        {
          type: 'group',
          label: 'Offer Management',
          expanded: false,
          id: 'offers',
          checked: {
            view: false,
            manage: false,
          },
          disabled: {
            view: false,
            manage: false,
          },
          children: [ ...offerManagementAccesses ],
        },
      ];
      const finalData = initData.map(changeStatusForParentGroup);
      setData(finalData);
      // View and manage all checkboxes
      viewManageAllCheck(finalData);
      dispatch(
        change('teamDetails', 'permissions', initialSelectionsPermissions)
      );
    }
  }, [ loading, initialSelectionsAccess ]);

  /**
   * Change Expanded Status.
   * @param {string} id - id of the group or item.
   * @param {string} expanded - the new status of expanded.
   */

  const onChangeExpanded = (id, expanded = true) => {
    const iterate = e => {
      if (e.type === 'item') return e;
      if (e.type === 'group' && e.id === id) return { ...e, expanded };
      if (e.type === 'group' && e.id !== id) {
        return { ...e, children: e.children.map(iterate) };
      }
    };
    const updatedData = data.map(iterate);
    setData(updatedData);
  };

  /**
   * Get all items under a group .
   * @param {string} allItems - group childern.
   */

  const getAllItems = allItems => {
    const items = [];
    const iterate = e => {
      if (e.type === 'item') return items.push(e);
      if (e.type === 'group') return e.children.forEach(iterate);
    };
    allItems.forEach(iterate);
    return items;
  };

  /**
   * select or unselect All Items for the group according to checked and key.
   * @param {array} items - group childern.
   * @param {boolean} checked - the status of the checked .
   * @param {string} key - the key of access.
   * @param {boolean} shouldSelectView - flag to see if we should select view access.
   * @param {boolean} shouldRemoveManage - flag to see if we should select view access.
   */

  const selectAllItems = (
    items,
    checked,
    key,
    shouldSelectView = false,
    shouldRemoveManage = false
  ) => {
    const iterate = e => {
      if (e.type === 'item' && e.disabled[key] && e.id !== 'access-1') return e;
      return {
        ...e,
        checked: {
          view: shouldSelectView || e.checked.view,
          manage: shouldRemoveManage ? false : e.checked.manage,
          [key]: checked,
        },
        ...(e.type === 'group' ? { children: e.children.map(iterate) } : {}),
      };
    };
    return items.map(iterate);
  };

  /**
   * review all tree again and change the group status
   * @param {string} e - item ( group or item)
   */

  const changeStatusForParentGroup = e => {
    if (e.type === 'item' || (e.type === 'group' && !e.children.length)) {
       return {
        ...e,
        ...(teamName === PIGEON_TEAM && {
          disabled: {
            view: e.checked.view,
            manage: e.checked.manage,
          },
        }),
       };
      }
    const items = getAllItems(e.children);
    const viewItemsLength = items.filter(i => i?.checked.view).length;
    const manageItemsLength = items.filter(i => i?.checked.manage).length;
    const isPartSelectedManage =
      !!manageItemsLength && manageItemsLength !== items.length;
    const isPartSelectedView =
      !!viewItemsLength && viewItemsLength !== items.length;

    const isCheckedView = isPartSelectedView
      ? 'indeterminate'
      : !!viewItemsLength;
    const isCheckedManage = isPartSelectedManage
      ? 'indeterminate'
      : !!manageItemsLength;

    return {
      ...e,
      checked: {
        view: isCheckedView,
        manage: isCheckedManage,
      },
      ...(teamName === PIGEON_TEAM && {
        disabled: {
          view: isCheckedView,
          manage: isCheckedManage,
        },
      }),
      children: e.children.map(changeStatusForParentGroup),
    };
  };

  /**
   * select or unselect All Items for the group.
   * @param {string} id - id of the group or item.
   * @param {boolean} checked - the status of the checked .
   * @param {string} key - the key of access.
   */

  const onChangeStatus = (id, checked, key) => {
    const shouldSelectView = key === 'manage' && checked;
    const shouldRemoveManage = key === 'view' && !checked;

    // change the status of any element ( group / item )
    const changeStatus = e => {
      // in case of the user select item
      if (id && e.type === 'item' && e.id === id) {
        return {
          ...e,
          checked: {
            view: shouldSelectView || e.checked.view,
            manage: shouldRemoveManage ? false : e.checked.manage,
            [key]: checked,
          },
        };
      }
      // In case of the user select group
      if (e.type === 'group') {
        // For any group the user didn't select we should check childern
        if (id && e.id !== id) {
          return { ...e, children: e.children.map(changeStatus) };
        }
        // check if this group has has any disabled items
        const hasDisabledItems = getAllItems(e.children).some(
          i => i.disabled[key]
        );
        // if the group has any disabled items
        if (id && hasDisabledItems) {
          const hasSelectedItems = getAllItems(e.children).some(
            i => i.checked[key]
          );

          const isSelectedAll = getAllItems(e.children)
            .filter(e => !e.disabled[key])
            .every(e => e.checked[key]);
          const shouldSelectViewDisabled = key === 'manage' && !isSelectedAll;
          const shouldRemoveManageDisabled = key === 'view' && hasSelectedItems;
          return {
            ...e,
            checked: {
              view: shouldSelectViewDisabled || e.checked.view,
              manage: shouldRemoveManageDisabled ? false : e.checked.manage,
              [key]: checked,
            },
            children: selectAllItems(
              e.children,
              !isSelectedAll,
              key,
              shouldSelectViewDisabled,
              shouldRemoveManageDisabled
            ),
          };
        }
        // if the group doesn't have any disabled items
        return {
          ...e,
          checked: {
            view: shouldSelectView || e.checked.view,
            manage: shouldRemoveManage ? false : e.checked.manage,
            [key]: checked,
          },
          children: selectAllItems(
            e.children,
            checked,
            key,
            shouldSelectView,
            shouldRemoveManage
          ),
        };
      }
      // if item, group doesn't match the selected id
      if (id && e.id !== id) return e;
    };
    const finalData = data.map(changeStatus);
    const parentStatusChanged = finalData.map(changeStatusForParentGroup);
    setData(parentStatusChanged);

    const accessValues = getAllItems(finalData.map(changeStatusForParentGroup));
    // View and manage all checkboxes
    viewManageAllCheck(parentStatusChanged);

    const selectedViewItems = accessValues
      .filter(item => item.checked.view)
      .map(d => ({ ...d.data, access: VIEW_ACCESS_LEVEL }));
    const selectedManageItems = accessValues
      .filter(item => item.checked.manage)
      .map(d => ({ ...d.data, access: MANAGE_ACCESS_LEVEL }));

    // Set redux form permissions value
    const selectedViewPermissions = selectedViewItems
      .filter(i => i.dataType === 'permissions')
      .map(p => p.permission.view);
    const selectedManagePermissions = selectedManageItems
      .filter(i => i.dataType === 'permissions')
      .map(p => p.permission.manage);

    dispatch(
      change(
        'teamDetails',
        'permissions',
        concat(selectedViewPermissions, selectedManagePermissions)
      )
    );

    // Set redux form access value
    const finalSelectedItems = mapValues(
      groupBy(
        concat(
          selectedManageItems,
          selectedViewItems.filter(
            i =>
              !selectedManageItems.some(j =>
                isEqual(omit(i, 'access'), omit(j, 'access'))
              )
          )
        ),
        'dataType'
      ),
      selectedItems => selectedItems.map(item => omit(item, 'dataType'))
    );
    input.onChange(finalSelectedItems);
  };

  const selectAll = (e, key) => {
    const { checked } = e.target;
    onChangeStatus(null, checked, key);
  };

  return (
    <Card className="team-access__card">
      <TextHeadline
        component="h2"
        size={21}
        className="admin-details__sub-header"
      >
        Grant access
      </TextHeadline>
      <div className="team-access__header">
        <AccessTooltip />
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div className="team-access__view-cell">
            <Grid>
              <Row>
                <Column>
                  <TextSubtitle
                    type="2"
                    component="h3"
                    className="advanced-targeting-section__subtitle"
                  >
                    View
                  </TextSubtitle>
                </Column>
                <Column>
                  <div className="team-access__select-all-checkbox">
                    <Checkbox
                      id="view-access-checkbox"
                      label=""
                      data-testid="view-access-checkbox"
                      disabled={teamName === PIGEON_TEAM}
                      checked={allViewChecked}
                      onChange={e => selectAll(e, 'view')}
                    />
                  </div>
                </Column>
              </Row>
            </Grid>
          </div>
          <div className="team-access__manage-cell">
            <Grid>
              <Row>
                <Column>
                  <TextSubtitle
                    type="2"
                    component="h3"
                    className="advanced-targeting-section__subtitle"
                  >
                    Manage
                  </TextSubtitle>
                </Column>
                <Column>
                  <div className="team-access__select-all-checkbox">
                    <Checkbox
                      id="manage-access-checkbox"
                      label=""
                      data-testid="manage-access-checkbox"
                      disabled={teamName === PIGEON_TEAM}
                      checked={allManageChecked}
                      onChange={e => selectAll(e, 'manage')}
                    />
                  </div>
                </Column>
              </Row>
            </Grid>
          </div>
        </div>
      </div>
      <InputGroup id="checkbox-group-1" legend="" name="group-1">
        { data.map((access, i) => {
          return (
            access && (
              <GroupAccess
                key={access.id}
                groupChildren={access.children}
                {...access}
                onChangeExpanded={onChangeExpanded}
                onChangeStatus={onChangeStatus}
                level={0}
                isFormDisabled={isFormDisabled}
              />
            )
          );
        }) }
      </InputGroup>
      { meta.error && meta.touched && (
        <div className="permissions-table__error-container">
          <IconErrorUniversal />
          <div className="permissions-table__error-message">
            <span>{ meta.error }</span>
          </div>
        </div>
      ) }
    </Card>
  );
};

AccessCard.propTypes = {
  input: PropTypes.object,
  initialSelectionsAccess: PropTypes.object,
  initialSelectionsPermissions: PropTypes.array,
  meta: PropTypes.object,
  isFormDisabled: PropTypes.bool,
  teamName: PropTypes.string,
};
export default AccessCard;
