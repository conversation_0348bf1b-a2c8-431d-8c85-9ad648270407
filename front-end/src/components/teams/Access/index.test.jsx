import React from 'react';
import Access from './index';
import { render, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { PIGEON_TEAM } from '../../../constants';

const mockStore = configureMockStore([ thunk ]);

const mockItems = {
  applications: {
    items: {
      1: {
        id: 1,
        name: 'SOL',
        description: null,
        applicationId: 'sol',
        status: true,
        rule_version: 2,
        contentful_space: '4szkx38resvm',
        team_id: 1,
        platforms: [ 'Web' ],
        platformIds: [ 1 ],
        ruleTypes: [
          'vignette',
          'SOL Priority Campaign (Vignette)',
          'SOL Broadcast Campaign (Vignette)',
        ],
        ruleTypeIds: [ 3, 5, 6 ],
        ruleSubTypeIds: [],
        ruleSubTypes: [],
        team: [ 'Pigeon Team' ],
      },
      2: {
        id: 2,
        name: 'eExperience Storefront',
        description: null,
        applicationId: 'storefront',
        status: true,
        rule_version: 2,
        contentful_space: '4szkx38resvm',
        team_id: 1,
        platforms: [ 'Web' ],
        platformIds: [ 1 ],
        ruleTypes: [ 'eExperience Storefront Campaign (Vignette)' ],
        ruleTypeIds: [ 7 ],
        ruleSubTypeIds: [],
        ruleSubTypes: [],
        team: [ 'Pigeon Team' ],
      },
      3: {
        id: 3,
        name: 'Nova Mobile',
        description: null,
        applicationId: 'nova',
        status: true,
        rule_version: 1,
        contentful_space: '4szkx38resvm',
        team_id: 1,
        platforms: [ 'iOS', 'Android' ],
        platformIds: [ 2, 3 ],
        ruleTypes: [ 'alert', 'campaign' ],
        ruleTypeIds: [ 1, 2 ],
        ruleSubTypeIds: [ 1, 2, 3 ],
        ruleSubTypes: [ 'targeted', 'mass', 'message' ],
        team: [ 'Pigeon Team' ],
      },
      4: {
        id: 4,
        name: 'Phoenix',
        description: null,
        applicationId: 'phoenix',
        status: true,
        rule_version: 1,
        contentful_space: '4szkx38resvm',
        team_id: 1,
        platforms: [ 'Web' ],
        platformIds: [ 1 ],
        ruleTypes: [ 'alert' ],
        ruleTypeIds: [ 1 ],
        ruleSubTypeIds: [ 1, 2, 3 ],
        ruleSubTypes: [ 'targeted', 'mass', 'message' ],
        team: [ 'Pigeon Team' ],
      },
      5: {
        id: 5,
        name: 'ABM',
        description: null,
        applicationId: 'abm',
        status: true,
        rule_version: 1,
        contentful_space: '4szkx38resvm',
        team_id: 1,
        platforms: [ 'Web' ],
        platformIds: [ 1 ],
        ruleTypes: [ 'campaign' ],
        ruleTypeIds: [ 2 ],
        ruleSubTypeIds: [ 1, 2, 3 ],
        ruleSubTypes: [ 'targeted', 'mass', 'message' ],
        team: [ 'Pigeon Team' ],
      },
      12: {
        id: 12,
        name: 'full permissions',
        description: 'full permissions',
        applicationId: 'full permissions',
        status: true,
        rule_version: 1,
        contentful_space: 'gk5ecj6dpckc',
        team_id: 9,
        platforms: [ 'Web', 'iOS', 'Android' ],
        platformIds: [ 1, 2, 3 ],
        ruleTypes: [ 'alert', 'campaign' ],
        ruleTypeIds: [ 1, 2 ],
        ruleSubTypeIds: [ 1, 2, 3 ],
        ruleSubTypes: [ 'targeted', 'mass', 'message' ],
        team: [ 'fullPermissions' ],
      },
    },
    isLoading: false,
  },
  pages: {
    items: {
      1: {
        id: 1,
        application: 3,
        name: 'activities',
        pageId: 'activities',
        status: true,
        description: 'NOVA mobile - My Activity tab',
        containers: [ 2 ],
      },
      2: {
        id: 2,
        application: 3,
        name: 'accounts',
        pageId: 'accounts',
        status: true,
        description: 'NOVA mobile - My Accounts tab',
        containers: [ 3, 4 ],
      },
      17: {
        id: 17,
        application: 12,
        name: 'full permissions page',
        pageId: 'full-permi',
        status: true,
        description: 'full permissions page',
        containers: [ 41 ],
      },
    },
    isLoading: false,
  },
  containers: {
    items: {
      1: {
        id: 1,
        name: 'alert',
        containerId: 'alert',
        application: 3,
        status: true,
        description: 'Alert',
        content_type: [ 'alert' ],
        rule_type: 'alert',
        pages: [],
      },
      2: {
        id: 2,
        name: 'campaign',
        containerId: 'campaign',
        application: 3,
        status: true,
        description: 'Campaign',
        content_type: [ 'campaign' ],
        rule_type: 'campaign',
        pages: [],
      },
    },
    isLoading: false,
  },
  ruleSubTypes: {
    items: [
      {
        id: 1,
        description: 'Targeted Campaign',
        type: 'targeted',
        ruleTypeId: null,
      },
    ],
    isLoading: false,
  },
  ruleTypes: {
    items: [
      {
        id: 1,
        rule_type: 'alert',
        slug: 'alert',
      },
    ],
    isLoading: false,
  },
};

describe('test render access table', () => {
  const store = mockStore(mockItems);
  const input = {
    onChange: jest.fn(),
  };
  it('should render the group access (enabled)', () => {
    const { asFragment } = render(
      <Provider store={store}>
        <Access
          initialSelectionsPermissions={[]}
          initialSelectionsAccess={{}}
          input={input}
          meta={{ error: false, touched: false }}
          isFormDisabled={false}
        />
      </Provider>
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('should check change function (view)', () => {
    const { getByTestId } = render(
      <Provider store={mockStore(mockItems)}>
        <Access
          initialSelectionsPermissions={[]}
          initialSelectionsAccess={{}}
          input={input}
          meta={{ error: false, touched: false }}
          isFormDisabled={false}
        />
      </Provider>
    );
    const changeStatus = getByTestId('campaigns-view');
    fireEvent.click(changeStatus);
    expect(input.onChange).toHaveBeenCalled();
  });

  it('should check change function (manage - group)', () => {
    const { getByTestId } = render(
      <Provider store={mockStore(mockItems)}>
        <Access
          initialSelectionsPermissions={[]}
          initialSelectionsAccess={{}}
          input={input}
          meta={{ error: false, touched: false }}
          isFormDisabled={false}
        />
      </Provider>
    );
    const changeStatus = getByTestId('campaigns-manage');
    fireEvent.click(changeStatus);
    expect(input.onChange).toHaveBeenCalled();
  });

  it('should check change function (manage - item)', () => {
    const { getByTestId } = render(
      <Provider store={mockStore(mockItems)}>
        <Access
          initialSelectionsPermissions={[]}
          initialSelectionsAccess={{}}
          input={input}
          meta={{ error: false, touched: false }}
          isFormDisabled={false}
        />
      </Provider>
    );
    const varsGroup = getByTestId('team-access__item-label-variable-mapping');
    fireEvent.click(varsGroup);
    const item = getByTestId('variable-1-view');
    fireEvent.click(item);
    expect(input.onChange).toHaveBeenCalled();
  });

  it('checkboxes for Pigeon team should be checked and disabled', async() => {
    const { getByTestId } = render(
      <Provider store={mockStore(mockItems)}>
        <Access
          initialSelectionsPermissions={[]}
          initialSelectionsAccess={{}}
          input={input}
          meta={{ error: false, touched: false }}
          teamName={PIGEON_TEAM}
          isFormDisabled={false}
        />

      </Provider>

    );

    await waitFor(() => {
      expect(getByTestId('view-access-checkbox')).toBeDisabled();
      expect(getByTestId('manage-access-checkbox')).toBeDisabled();
    });
  });
});
