.team-access {
  &__card { @extend .admin-details__card; }

  &__item,
  &__group {
    border-top: 0.1rem  solid $canvas-gray-400;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__group_container:last-child {
    border-bottom: 0.1rem  solid $canvas-gray-400;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__chevron {
    background: none;
    border: none;
    padding: 0;
    margin-right: 1.5rem;
    min-width: 1.8rem;
    min-height: 1.8rem;
    transition: transform 120ms;

    &--expanded {
      transform: rotate(90deg);
    }
  }

  &__item-label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 3rem;
    border: 0;
    background-color: transparent;
  }

  &__view-cell {
    background-color: $canvas-gray-100;
    width: 10rem;
  }

  &__select-all-checkbox {
    padding-left: 0.4rem;
  }

  &__manage-cell {
    width: 10rem;
  }

  .Checkbox__span {
    margin: unset;
  }

  &__listing {
    padding-left: 1.8rem;
  }
}
