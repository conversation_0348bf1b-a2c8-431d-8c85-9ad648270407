import React from 'react';
import IconChevronRight from 'canvas-core-react/lib/IconChevronRight';
import classnames from 'classnames';
import ItemAccess from './ItemAccess';
import PropTypes from 'prop-types';
import Checkbox from 'canvas-core-react/lib/Checkbox';
import TextCaption from 'canvas-core-react/lib/TextCaption';

const GroupAccess = ({
  label,
  groupChildren,
  expanded,
  onChangeExpanded,
  id,
  disabled,
  onChangeStatus,
  checked,
  level,
  isFormDisabled,
}) => {
  return (
    <div className='team-access__group_container'>
      <div className="team-access__group">
        <button
          type='button'
          className="team-access__item-label"
          data-testid={`team-access__item-label-${id}`}
          onClick={() => {
            onChangeExpanded(id, !expanded);
          }}
          style={{ paddingLeft: `${level * 3}rem` }}
        >
          <IconChevronRight
            className={classnames('team-access__chevron', {
              'team-access__chevron--expanded': expanded,
            })}
          />
          <TextCaption bold component="h1">
            { label }
          </TextCaption>
        </button>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div className="team-access__view-cell">
            <Checkbox
              id={`${id}-view`}
              data-testid={`${id}-view`}
              label=""
              name={id}
              onChange={(event) =>
                onChangeStatus(id, event.target.checked, 'view')
              }
              checked={checked?.view}
              disabled={disabled.view || isFormDisabled}
            />
          </div>
          <div className="team-access__manage-cell">
            <Checkbox
              className="team-access__manage-cell"
              id={`${id}-manage`}
              label=""
              name={id}
              data-testid={`${id}-manage`}
              onChange={(event) =>
                onChangeStatus(id, event.target.checked, 'manage')
              }
              checked={checked?.manage}
              disabled={disabled.manage || isFormDisabled}
            />
          </div>
        </div>
      </div>
      { expanded && !!groupChildren.length && (
        <div>
          { groupChildren.map((access) => {
            if (access.type === 'group') {
              return (
                <GroupAccess
                  key={access.id}
                  groupChildren={access.children}
                  {...access}
                  onChangeExpanded={onChangeExpanded}
                  onChangeStatus={onChangeStatus}
                  level={level + 1}
                  isFormDisabled={isFormDisabled}
                />
              );
            }

            return (
              <ItemAccess
                key={access.id}
                {...access}
                onChangeStatus={onChangeStatus}
                level={level + 1}
                isFormDisabled={isFormDisabled}
              />
            );
          }) }
        </div>
      ) }
    </div>
  );
};

GroupAccess.propTypes = {
  label: PropTypes.string.isRequired,
  groupChildren: PropTypes.array,
  expanded: PropTypes.bool.isRequired,
  onChangeExpanded: PropTypes.func.isRequired,
  id: PropTypes.string.isRequired,
  disabled: PropTypes.object.isRequired,
  onChangeStatus: PropTypes.func.isRequired,
  checked: PropTypes.object.isRequired,
  level: PropTypes.number.isRequired,
  isFormDisabled: PropTypes.bool.isRequired,
};

export default GroupAccess;
