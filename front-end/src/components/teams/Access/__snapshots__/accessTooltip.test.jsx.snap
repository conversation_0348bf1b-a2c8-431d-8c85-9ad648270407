// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`test render Tooltip should render the Tooltip 1`] = `
<DocumentFragment>
  <h3
    class="TextSubtitlestyle__Text-canvas-core__sc-1l85m0x-0 gPYxBg TextSubtitle__text advanced-targeting-section__subtitle"
    color="black"
    type="2"
  >
    Functionality
    <div
      class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
      id="tooltip-container-campaign-enrollment-status-tooltip"
    >
      <div
        class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
      >
        <button
          aria-label="Info, Functionality"
          class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
          id="desktop-icon-campaign-enrollment-status-tooltip"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btSiGf"
            color="black"
            focusable="false"
            role="presentation"
            size="18"
            viewBox="0 0 30 30"
          >
            <path
              clip-rule="evenodd"
              d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
              fill="none"
              fill-rule="evenodd"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M15 20.9062V14.1562"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <circle
              cx="14.9998"
              cy="9.1309"
              r="0.7"
            />
          </svg>
        </button>
      </div>
    </div>
  </h3>
</DocumentFragment>
`;
