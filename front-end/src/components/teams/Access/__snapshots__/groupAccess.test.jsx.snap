// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`test render group access should render the group access (disabled) 1`] = `
<DocumentFragment>
  <div
    class="team-access__group_container"
  >
    <div
      class="team-access__group"
    >
      <button
        class="team-access__item-label"
        data-testid="team-access__item-label-test-label-id"
        style="padding-left: 3rem;"
        type="button"
      >
        <svg
          aria-hidden="true"
          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron team-access__chevron--expanded"
          color="currentColor"
          focusable="false"
          role="presentation"
          size="18"
          viewBox="0 0 30 30"
        >
          <path
            d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
        <h1
          class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
          color="black"
        >
          test-label
        </h1>
      </button>
      <div
        style="display: flex; align-items: center; justify-content: space-between;"
      >
        <div
          class="team-access__view-cell"
        >
          <div
            class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
          >
            <div
              class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
            >
              <label
                class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 CpfdO Checkbox__label Checkbox__label--disabled"
                disabled=""
                for="test-label-id-view"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 kgbyfK Input__input Input__input--disabled Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                  data-testid="test-label-id-view"
                  disabled=""
                  id="test-label-id-view"
                  name="test-label-id"
                  type="checkbox"
                  value=""
                />
                <span
                  class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                    color="currentColor"
                    disabled=""
                    focusable="false"
                    role="presentation"
                    size="12"
                    viewBox="0 0 15 2"
                  >
                    <path
                      d="M1.5 1H13.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                    />
                  </svg>
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                    color="currentColor"
                    disabled=""
                    focusable="false"
                    role="presentation"
                    size="12"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                      stroke="none"
                    />
                  </svg>
                </span>
                <div
                  class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                />
              </label>
            </div>
          </div>
        </div>
        <div
          class="team-access__manage-cell"
        >
          <div
            class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
          >
            <div
              class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
            >
              <label
                class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 CpfdO Checkbox__label Checkbox__label--disabled"
                disabled=""
                for="test-label-id-manage"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 kgbyfK Input__input Input__input--disabled Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                  data-testid="test-label-id-manage"
                  disabled=""
                  id="test-label-id-manage"
                  name="test-label-id"
                  type="checkbox"
                  value=""
                />
                <span
                  class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                    color="currentColor"
                    disabled=""
                    focusable="false"
                    role="presentation"
                    size="12"
                    viewBox="0 0 15 2"
                  >
                    <path
                      d="M1.5 1H13.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                    />
                  </svg>
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                    color="currentColor"
                    disabled=""
                    focusable="false"
                    role="presentation"
                    size="12"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                      stroke="none"
                    />
                  </svg>
                </span>
                <div
                  class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                />
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div
        class="team-access__group_container"
      >
        <div
          class="team-access__group"
        >
          <button
            class="team-access__item-label"
            data-testid="team-access__item-label-campaigns"
            style="padding-left: 6rem;"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron team-access__chevron--expanded"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="18"
              viewBox="0 0 30 30"
            >
              <path
                d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <h1
              class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
              color="black"
            >
              Campaigns
            </h1>
          </button>
          <div
            style="display: flex; align-items: center; justify-content: space-between;"
          >
            <div
              class="team-access__view-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="campaigns-view"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="campaigns-view"
                      id="campaigns-view"
                      name="campaigns"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
            <div
              class="team-access__manage-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="campaigns-manage"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="campaigns-manage"
                      id="campaigns-manage"
                      name="campaigns"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div
            class="team-access__group_container"
          >
            <div
              class="team-access__group"
            >
              <button
                class="team-access__item-label"
                data-testid="team-access__item-label-campaigns-3"
                style="padding-left: 9rem;"
                type="button"
              >
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron team-access__chevron--expanded"
                  color="currentColor"
                  focusable="false"
                  role="presentation"
                  size="18"
                  viewBox="0 0 30 30"
                >
                  <path
                    d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <h1
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
                  color="black"
                >
                  Nova Mobile Campaigns
                </h1>
              </button>
              <div
                style="display: flex; align-items: center; justify-content: space-between;"
              >
                <div
                  class="team-access__view-cell"
                >
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                  >
                    <div
                      class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                    >
                      <label
                        class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                        for="campaigns-3-view"
                      >
                        <input
                          class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                          data-testid="campaigns-3-view"
                          id="campaigns-3-view"
                          name="campaigns-3"
                          type="checkbox"
                          value=""
                        />
                        <span
                          class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                        >
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="12"
                            viewBox="0 0 15 2"
                          >
                            <path
                              d="M1.5 1H13.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                            />
                          </svg>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="12"
                            viewBox="0 0 24 24"
                          >
                            <path
                              d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                              stroke="none"
                            />
                          </svg>
                        </span>
                        <div
                          class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                        />
                      </label>
                    </div>
                  </div>
                </div>
                <div
                  class="team-access__manage-cell"
                >
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
                  >
                    <div
                      class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                    >
                      <label
                        class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                        for="campaigns-3-manage"
                      >
                        <input
                          class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                          data-testid="campaigns-3-manage"
                          id="campaigns-3-manage"
                          name="campaigns-3"
                          type="checkbox"
                          value=""
                        />
                        <span
                          class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                        >
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="12"
                            viewBox="0 0 15 2"
                          >
                            <path
                              d="M1.5 1H13.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                            />
                          </svg>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="12"
                            viewBox="0 0 24 24"
                          >
                            <path
                              d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                              stroke="none"
                            />
                          </svg>
                        </span>
                        <div
                          class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                        />
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div
                class="team-access__group_container"
              >
                <div
                  class="team-access__group"
                >
                  <button
                    class="team-access__item-label"
                    data-testid="team-access__item-label-campaigns-3-pages"
                    style="padding-left: 12rem;"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron team-access__chevron--expanded"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="18"
                      viewBox="0 0 30 30"
                    >
                      <path
                        d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                        fill="none"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                    <h1
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
                      color="black"
                    >
                      Pages
                    </h1>
                  </button>
                  <div
                    style="display: flex; align-items: center; justify-content: space-between;"
                  >
                    <div
                      class="team-access__view-cell"
                    >
                      <div
                        class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                      >
                        <div
                          class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                        >
                          <label
                            class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                            for="campaigns-3-pages-view"
                          >
                            <input
                              class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                              data-testid="campaigns-3-pages-view"
                              id="campaigns-3-pages-view"
                              name="campaigns-3-pages"
                              type="checkbox"
                              value=""
                            />
                            <span
                              class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                            >
                              <svg
                                aria-hidden="true"
                                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                                color="currentColor"
                                focusable="false"
                                role="presentation"
                                size="12"
                                viewBox="0 0 15 2"
                              >
                                <path
                                  d="M1.5 1H13.5"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                />
                              </svg>
                              <svg
                                aria-hidden="true"
                                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                                color="currentColor"
                                focusable="false"
                                role="presentation"
                                size="12"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                  stroke="none"
                                />
                              </svg>
                            </span>
                            <div
                              class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                            />
                          </label>
                        </div>
                      </div>
                    </div>
                    <div
                      class="team-access__manage-cell"
                    >
                      <div
                        class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
                      >
                        <div
                          class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                        >
                          <label
                            class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                            for="campaigns-3-pages-manage"
                          >
                            <input
                              class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                              data-testid="campaigns-3-pages-manage"
                              id="campaigns-3-pages-manage"
                              name="campaigns-3-pages"
                              type="checkbox"
                              value=""
                            />
                            <span
                              class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                            >
                              <svg
                                aria-hidden="true"
                                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                                color="currentColor"
                                focusable="false"
                                role="presentation"
                                size="12"
                                viewBox="0 0 15 2"
                              >
                                <path
                                  d="M1.5 1H13.5"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                />
                              </svg>
                              <svg
                                aria-hidden="true"
                                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                                color="currentColor"
                                focusable="false"
                                role="presentation"
                                size="12"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                  stroke="none"
                                />
                              </svg>
                            </span>
                            <div
                              class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                            />
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <div
                    class="team-access__item"
                  >
                    <h1
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                      color="black"
                      style="padding-left: 15rem;"
                    >
                      activities
                    </h1>
                    <div
                      style="display: flex; align-items: center; justify-content: space-between;"
                    >
                      <div
                        class="team-access__view-cell"
                      >
                        <div
                          class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                        >
                          <div
                            class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                          >
                            <label
                              class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                              for="campaigns-3-pages-1-view"
                            >
                              <input
                                class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                                data-testid="campaigns-3-pages-1-view"
                                id="campaigns-3-pages-1-view"
                                name="campaigns-3-pages-1"
                                type="checkbox"
                                value=""
                              />
                              <span
                                class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                              >
                                <svg
                                  aria-hidden="true"
                                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                                  color="currentColor"
                                  focusable="false"
                                  role="presentation"
                                  size="12"
                                  viewBox="0 0 15 2"
                                >
                                  <path
                                    d="M1.5 1H13.5"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                  />
                                </svg>
                                <svg
                                  aria-hidden="true"
                                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                                  color="currentColor"
                                  focusable="false"
                                  role="presentation"
                                  size="12"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                    stroke="none"
                                  />
                                </svg>
                              </span>
                              <div
                                class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                              />
                            </label>
                          </div>
                        </div>
                      </div>
                      <div
                        class="team-access__manage-cell"
                      >
                        <div
                          class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
                        >
                          <div
                            class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                          >
                            <label
                              class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                              for="campaigns-3-pages-1-manage"
                            >
                              <input
                                class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                                data-testid="campaigns-3-pages-1-manage"
                                id="campaigns-3-pages-1-manage"
                                name="campaigns-3-pages-1"
                                type="checkbox"
                                value=""
                              />
                              <span
                                class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                              >
                                <svg
                                  aria-hidden="true"
                                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                                  color="currentColor"
                                  focusable="false"
                                  role="presentation"
                                  size="12"
                                  viewBox="0 0 15 2"
                                >
                                  <path
                                    d="M1.5 1H13.5"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                  />
                                </svg>
                                <svg
                                  aria-hidden="true"
                                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                                  color="currentColor"
                                  focusable="false"
                                  role="presentation"
                                  size="12"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                    stroke="none"
                                  />
                                </svg>
                              </span>
                              <div
                                class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                              />
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`test render group access should render the group access (enabled & expanded) 1`] = `
<DocumentFragment>
  <div
    class="team-access__group_container"
  >
    <div
      class="team-access__group"
    >
      <button
        class="team-access__item-label"
        data-testid="team-access__item-label-test-label-id"
        style="padding-left: 3rem;"
        type="button"
      >
        <svg
          aria-hidden="true"
          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron team-access__chevron--expanded"
          color="currentColor"
          focusable="false"
          role="presentation"
          size="18"
          viewBox="0 0 30 30"
        >
          <path
            d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
        <h1
          class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
          color="black"
        >
          test-label
        </h1>
      </button>
      <div
        style="display: flex; align-items: center; justify-content: space-between;"
      >
        <div
          class="team-access__view-cell"
        >
          <div
            class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
          >
            <div
              class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
            >
              <label
                class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                for="test-label-id-view"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                  data-testid="test-label-id-view"
                  id="test-label-id-view"
                  name="test-label-id"
                  type="checkbox"
                  value=""
                />
                <span
                  class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="12"
                    viewBox="0 0 15 2"
                  >
                    <path
                      d="M1.5 1H13.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                    />
                  </svg>
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="12"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                      stroke="none"
                    />
                  </svg>
                </span>
                <div
                  class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                />
              </label>
            </div>
          </div>
        </div>
        <div
          class="team-access__manage-cell"
        >
          <div
            class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
          >
            <div
              class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
            >
              <label
                class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                for="test-label-id-manage"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                  data-testid="test-label-id-manage"
                  id="test-label-id-manage"
                  name="test-label-id"
                  type="checkbox"
                  value=""
                />
                <span
                  class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="12"
                    viewBox="0 0 15 2"
                  >
                    <path
                      d="M1.5 1H13.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                    />
                  </svg>
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="12"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                      stroke="none"
                    />
                  </svg>
                </span>
                <div
                  class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                />
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div
        class="team-access__group_container"
      >
        <div
          class="team-access__group"
        >
          <button
            class="team-access__item-label"
            data-testid="team-access__item-label-campaigns"
            style="padding-left: 6rem;"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron team-access__chevron--expanded"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="18"
              viewBox="0 0 30 30"
            >
              <path
                d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <h1
              class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
              color="black"
            >
              Campaigns
            </h1>
          </button>
          <div
            style="display: flex; align-items: center; justify-content: space-between;"
          >
            <div
              class="team-access__view-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="campaigns-view"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="campaigns-view"
                      id="campaigns-view"
                      name="campaigns"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
            <div
              class="team-access__manage-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="campaigns-manage"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="campaigns-manage"
                      id="campaigns-manage"
                      name="campaigns"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div
            class="team-access__group_container"
          >
            <div
              class="team-access__group"
            >
              <button
                class="team-access__item-label"
                data-testid="team-access__item-label-campaigns-3"
                style="padding-left: 9rem;"
                type="button"
              >
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron team-access__chevron--expanded"
                  color="currentColor"
                  focusable="false"
                  role="presentation"
                  size="18"
                  viewBox="0 0 30 30"
                >
                  <path
                    d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <h1
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
                  color="black"
                >
                  Nova Mobile Campaigns
                </h1>
              </button>
              <div
                style="display: flex; align-items: center; justify-content: space-between;"
              >
                <div
                  class="team-access__view-cell"
                >
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                  >
                    <div
                      class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                    >
                      <label
                        class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                        for="campaigns-3-view"
                      >
                        <input
                          class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                          data-testid="campaigns-3-view"
                          id="campaigns-3-view"
                          name="campaigns-3"
                          type="checkbox"
                          value=""
                        />
                        <span
                          class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                        >
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="12"
                            viewBox="0 0 15 2"
                          >
                            <path
                              d="M1.5 1H13.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                            />
                          </svg>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="12"
                            viewBox="0 0 24 24"
                          >
                            <path
                              d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                              stroke="none"
                            />
                          </svg>
                        </span>
                        <div
                          class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                        />
                      </label>
                    </div>
                  </div>
                </div>
                <div
                  class="team-access__manage-cell"
                >
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
                  >
                    <div
                      class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                    >
                      <label
                        class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                        for="campaigns-3-manage"
                      >
                        <input
                          class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                          data-testid="campaigns-3-manage"
                          id="campaigns-3-manage"
                          name="campaigns-3"
                          type="checkbox"
                          value=""
                        />
                        <span
                          class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                        >
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="12"
                            viewBox="0 0 15 2"
                          >
                            <path
                              d="M1.5 1H13.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                            />
                          </svg>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="12"
                            viewBox="0 0 24 24"
                          >
                            <path
                              d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                              stroke="none"
                            />
                          </svg>
                        </span>
                        <div
                          class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                        />
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div
                class="team-access__group_container"
              >
                <div
                  class="team-access__group"
                >
                  <button
                    class="team-access__item-label"
                    data-testid="team-access__item-label-campaigns-3-pages"
                    style="padding-left: 12rem;"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron team-access__chevron--expanded"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="18"
                      viewBox="0 0 30 30"
                    >
                      <path
                        d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                        fill="none"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                    <h1
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
                      color="black"
                    >
                      Pages
                    </h1>
                  </button>
                  <div
                    style="display: flex; align-items: center; justify-content: space-between;"
                  >
                    <div
                      class="team-access__view-cell"
                    >
                      <div
                        class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                      >
                        <div
                          class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                        >
                          <label
                            class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                            for="campaigns-3-pages-view"
                          >
                            <input
                              class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                              data-testid="campaigns-3-pages-view"
                              id="campaigns-3-pages-view"
                              name="campaigns-3-pages"
                              type="checkbox"
                              value=""
                            />
                            <span
                              class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                            >
                              <svg
                                aria-hidden="true"
                                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                                color="currentColor"
                                focusable="false"
                                role="presentation"
                                size="12"
                                viewBox="0 0 15 2"
                              >
                                <path
                                  d="M1.5 1H13.5"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                />
                              </svg>
                              <svg
                                aria-hidden="true"
                                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                                color="currentColor"
                                focusable="false"
                                role="presentation"
                                size="12"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                  stroke="none"
                                />
                              </svg>
                            </span>
                            <div
                              class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                            />
                          </label>
                        </div>
                      </div>
                    </div>
                    <div
                      class="team-access__manage-cell"
                    >
                      <div
                        class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
                      >
                        <div
                          class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                        >
                          <label
                            class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                            for="campaigns-3-pages-manage"
                          >
                            <input
                              class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                              data-testid="campaigns-3-pages-manage"
                              id="campaigns-3-pages-manage"
                              name="campaigns-3-pages"
                              type="checkbox"
                              value=""
                            />
                            <span
                              class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                            >
                              <svg
                                aria-hidden="true"
                                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                                color="currentColor"
                                focusable="false"
                                role="presentation"
                                size="12"
                                viewBox="0 0 15 2"
                              >
                                <path
                                  d="M1.5 1H13.5"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                />
                              </svg>
                              <svg
                                aria-hidden="true"
                                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                                color="currentColor"
                                focusable="false"
                                role="presentation"
                                size="12"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                  stroke="none"
                                />
                              </svg>
                            </span>
                            <div
                              class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                            />
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <div
                    class="team-access__item"
                  >
                    <h1
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                      color="black"
                      style="padding-left: 15rem;"
                    >
                      activities
                    </h1>
                    <div
                      style="display: flex; align-items: center; justify-content: space-between;"
                    >
                      <div
                        class="team-access__view-cell"
                      >
                        <div
                          class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                        >
                          <div
                            class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                          >
                            <label
                              class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                              for="campaigns-3-pages-1-view"
                            >
                              <input
                                class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                                data-testid="campaigns-3-pages-1-view"
                                id="campaigns-3-pages-1-view"
                                name="campaigns-3-pages-1"
                                type="checkbox"
                                value=""
                              />
                              <span
                                class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                              >
                                <svg
                                  aria-hidden="true"
                                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                                  color="currentColor"
                                  focusable="false"
                                  role="presentation"
                                  size="12"
                                  viewBox="0 0 15 2"
                                >
                                  <path
                                    d="M1.5 1H13.5"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                  />
                                </svg>
                                <svg
                                  aria-hidden="true"
                                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                                  color="currentColor"
                                  focusable="false"
                                  role="presentation"
                                  size="12"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                    stroke="none"
                                  />
                                </svg>
                              </span>
                              <div
                                class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                              />
                            </label>
                          </div>
                        </div>
                      </div>
                      <div
                        class="team-access__manage-cell"
                      >
                        <div
                          class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
                        >
                          <div
                            class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                          >
                            <label
                              class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                              for="campaigns-3-pages-1-manage"
                            >
                              <input
                                class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                                data-testid="campaigns-3-pages-1-manage"
                                id="campaigns-3-pages-1-manage"
                                name="campaigns-3-pages-1"
                                type="checkbox"
                                value=""
                              />
                              <span
                                class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                              >
                                <svg
                                  aria-hidden="true"
                                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                                  color="currentColor"
                                  focusable="false"
                                  role="presentation"
                                  size="12"
                                  viewBox="0 0 15 2"
                                >
                                  <path
                                    d="M1.5 1H13.5"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                  />
                                </svg>
                                <svg
                                  aria-hidden="true"
                                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                                  color="currentColor"
                                  focusable="false"
                                  role="presentation"
                                  size="12"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                    stroke="none"
                                  />
                                </svg>
                              </span>
                              <div
                                class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                              />
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`test render group access should render the group access (enabled) 1`] = `
<DocumentFragment>
  <div
    class="team-access__group_container"
  >
    <div
      class="team-access__group"
    >
      <button
        class="team-access__item-label"
        data-testid="team-access__item-label-test-label-id"
        style="padding-left: 3rem;"
        type="button"
      >
        <svg
          aria-hidden="true"
          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
          color="currentColor"
          focusable="false"
          role="presentation"
          size="18"
          viewBox="0 0 30 30"
        >
          <path
            d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
        <h1
          class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
          color="black"
        >
          test-label
        </h1>
      </button>
      <div
        style="display: flex; align-items: center; justify-content: space-between;"
      >
        <div
          class="team-access__view-cell"
        >
          <div
            class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
          >
            <div
              class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
            >
              <label
                class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                for="test-label-id-view"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                  data-testid="test-label-id-view"
                  id="test-label-id-view"
                  name="test-label-id"
                  type="checkbox"
                  value=""
                />
                <span
                  class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="12"
                    viewBox="0 0 15 2"
                  >
                    <path
                      d="M1.5 1H13.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                    />
                  </svg>
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="12"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                      stroke="none"
                    />
                  </svg>
                </span>
                <div
                  class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                />
              </label>
            </div>
          </div>
        </div>
        <div
          class="team-access__manage-cell"
        >
          <div
            class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
          >
            <div
              class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
            >
              <label
                class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                for="test-label-id-manage"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                  data-testid="test-label-id-manage"
                  id="test-label-id-manage"
                  name="test-label-id"
                  type="checkbox"
                  value=""
                />
                <span
                  class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="12"
                    viewBox="0 0 15 2"
                  >
                    <path
                      d="M1.5 1H13.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                    />
                  </svg>
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="12"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                      stroke="none"
                    />
                  </svg>
                </span>
                <div
                  class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                />
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
