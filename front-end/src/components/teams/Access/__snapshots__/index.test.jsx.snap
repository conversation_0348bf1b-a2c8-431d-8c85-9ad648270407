// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`test render access table should render the group access (enabled) 1`] = `
<DocumentFragment>
  <div
    class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container team-access__card"
    type="flatSolid"
  >
    <h2
      class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP admin-details__sub-header"
      color="black"
      size="21"
    >
      Grant access
    </h2>
    <div
      class="team-access__header"
    >
      <h3
        class="TextSubtitlestyle__Text-canvas-core__sc-1l85m0x-0 gPYxBg TextSubtitle__text advanced-targeting-section__subtitle"
        color="black"
        type="2"
      >
        Functionality
        <div
          class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
          id="tooltip-container-campaign-enrollment-status-tooltip"
        >
          <div
            class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
          >
            <button
              aria-label="Info, Functionality"
              class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
              id="desktop-icon-campaign-enrollment-status-tooltip"
              type="button"
            >
              <svg
                aria-hidden="true"
                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btSiGf"
                color="black"
                focusable="false"
                role="presentation"
                size="18"
                viewBox="0 0 30 30"
              >
                <path
                  clip-rule="evenodd"
                  d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                  fill="none"
                  fill-rule="evenodd"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M15 20.9062V14.1562"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <circle
                  cx="14.9998"
                  cy="9.1309"
                  r="0.7"
                />
              </svg>
            </button>
          </div>
        </div>
      </h3>
      <div
        style="display: flex; align-items: center; justify-content: space-between;"
      >
        <div
          class="team-access__view-cell"
        >
          <div
            class="Gridstyle__Wrapper-canvas-core__sc-6d4gtg-0 jtviWe Grid__container"
          >
            <div
              class="Rowstyle__Wrapper-canvas-core__sc-tnvou7-0 BXVYe Row__container"
            >
              <div
                class="Columnstyle__Wrapper-canvas-core__sc-z9aox0-0 ckffmr Column__container"
              >
                <h3
                  class="TextSubtitlestyle__Text-canvas-core__sc-1l85m0x-0 gPYxBg TextSubtitle__text advanced-targeting-section__subtitle"
                  color="black"
                  type="2"
                >
                  View
                </h3>
              </div>
              <div
                class="Columnstyle__Wrapper-canvas-core__sc-z9aox0-0 ckffmr Column__container"
              >
                <div
                  class="team-access__select-all-checkbox"
                >
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                  >
                    <div
                      class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                    >
                      <label
                        class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                        for="view-access-checkbox"
                      >
                        <input
                          class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                          data-testid="view-access-checkbox"
                          id="view-access-checkbox"
                          type="checkbox"
                          value=""
                        />
                        <span
                          class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                        >
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="12"
                            viewBox="0 0 15 2"
                          >
                            <path
                              d="M1.5 1H13.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                            />
                          </svg>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="12"
                            viewBox="0 0 24 24"
                          >
                            <path
                              d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                              stroke="none"
                            />
                          </svg>
                        </span>
                        <div
                          class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                        />
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="team-access__manage-cell"
        >
          <div
            class="Gridstyle__Wrapper-canvas-core__sc-6d4gtg-0 jtviWe Grid__container"
          >
            <div
              class="Rowstyle__Wrapper-canvas-core__sc-tnvou7-0 BXVYe Row__container"
            >
              <div
                class="Columnstyle__Wrapper-canvas-core__sc-z9aox0-0 ckffmr Column__container"
              >
                <h3
                  class="TextSubtitlestyle__Text-canvas-core__sc-1l85m0x-0 gPYxBg TextSubtitle__text advanced-targeting-section__subtitle"
                  color="black"
                  type="2"
                >
                  Manage
                </h3>
              </div>
              <div
                class="Columnstyle__Wrapper-canvas-core__sc-z9aox0-0 ckffmr Column__container"
              >
                <div
                  class="team-access__select-all-checkbox"
                >
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                  >
                    <div
                      class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                    >
                      <label
                        class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                        for="manage-access-checkbox"
                      >
                        <input
                          class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                          data-testid="manage-access-checkbox"
                          id="manage-access-checkbox"
                          type="checkbox"
                          value=""
                        />
                        <span
                          class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                        >
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="12"
                            viewBox="0 0 15 2"
                          >
                            <path
                              d="M1.5 1H13.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                            />
                          </svg>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="12"
                            viewBox="0 0 24 24"
                          >
                            <path
                              d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                              stroke="none"
                            />
                          </svg>
                        </span>
                        <div
                          class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                        />
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <fieldset
      class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 InputGroup__fieldset"
      id="checkbox-group-1-inputgroup"
      name="group-1"
    >
      <legend
        class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 hlGdcC"
      />
      <div
        class="team-access__group_container"
      >
        <div
          class="team-access__group"
        >
          <button
            class="team-access__item-label"
            data-testid="team-access__item-label-campaigns"
            style="padding-left: 0rem;"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="18"
              viewBox="0 0 30 30"
            >
              <path
                d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <h1
              class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
              color="black"
            >
              Campaigns
            </h1>
          </button>
          <div
            style="display: flex; align-items: center; justify-content: space-between;"
          >
            <div
              class="team-access__view-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="campaigns-view"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="campaigns-view"
                      id="campaigns-view"
                      name="campaigns"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
            <div
              class="team-access__manage-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="campaigns-manage"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="campaigns-manage"
                      id="campaigns-manage"
                      name="campaigns"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="team-access__group_container"
      >
        <div
          class="team-access__group"
        >
          <button
            class="team-access__item-label"
            data-testid="team-access__item-label-alerts"
            style="padding-left: 0rem;"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="18"
              viewBox="0 0 30 30"
            >
              <path
                d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <h1
              class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
              color="black"
            >
              Alerts
            </h1>
          </button>
          <div
            style="display: flex; align-items: center; justify-content: space-between;"
          >
            <div
              class="team-access__view-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="alerts-view"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="alerts-view"
                      id="alerts-view"
                      name="alerts"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
            <div
              class="team-access__manage-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="alerts-manage"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="alerts-manage"
                      id="alerts-manage"
                      name="alerts"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="team-access__group_container"
      >
        <div
          class="team-access__group"
        >
          <button
            class="team-access__item-label"
            data-testid="team-access__item-label-placements"
            style="padding-left: 0rem;"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="18"
              viewBox="0 0 30 30"
            >
              <path
                d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <h1
              class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
              color="black"
            >
              Placement
            </h1>
          </button>
          <div
            style="display: flex; align-items: center; justify-content: space-between;"
          >
            <div
              class="team-access__view-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="placements-view"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="placements-view"
                      id="placements-view"
                      name="placements"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
            <div
              class="team-access__manage-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="placements-manage"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="placements-manage"
                      id="placements-manage"
                      name="placements"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="team-access__group_container"
      >
        <div
          class="team-access__group"
        >
          <button
            class="team-access__item-label"
            data-testid="team-access__item-label-access-control"
            style="padding-left: 0rem;"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="18"
              viewBox="0 0 30 30"
            >
              <path
                d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <h1
              class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
              color="black"
            >
              Access control
            </h1>
          </button>
          <div
            style="display: flex; align-items: center; justify-content: space-between;"
          >
            <div
              class="team-access__view-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="access-control-view"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="access-control-view"
                      id="access-control-view"
                      name="access-control"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
            <div
              class="team-access__manage-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="access-control-manage"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="access-control-manage"
                      id="access-control-manage"
                      name="access-control"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="team-access__group_container"
      >
        <div
          class="team-access__group"
        >
          <button
            class="team-access__item-label"
            data-testid="team-access__item-label-variable-mapping"
            style="padding-left: 0rem;"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="18"
              viewBox="0 0 30 30"
            >
              <path
                d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <h1
              class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
              color="black"
            >
              Variable mapping
            </h1>
          </button>
          <div
            style="display: flex; align-items: center; justify-content: space-between;"
          >
            <div
              class="team-access__view-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="variable-mapping-view"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="variable-mapping-view"
                      id="variable-mapping-view"
                      name="variable-mapping"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
            <div
              class="team-access__manage-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="variable-mapping-manage"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="variable-mapping-manage"
                      id="variable-mapping-manage"
                      name="variable-mapping"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="team-access__group_container"
      >
        <div
          class="team-access__group"
        >
          <button
            class="team-access__item-label"
            data-testid="team-access__item-label-message-centre"
            style="padding-left: 0rem;"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="18"
              viewBox="0 0 30 30"
            >
              <path
                d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <h1
              class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
              color="black"
            >
              Message Centre
            </h1>
          </button>
          <div
            style="display: flex; align-items: center; justify-content: space-between;"
          >
            <div
              class="team-access__view-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="message-centre-view"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="message-centre-view"
                      id="message-centre-view"
                      name="message-centre"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
            <div
              class="team-access__manage-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="message-centre-manage"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="message-centre-manage"
                      id="message-centre-manage"
                      name="message-centre"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="team-access__group_container"
      >
        <div
          class="team-access__group"
        >
          <button
            class="team-access__item-label"
            data-testid="team-access__item-label-offers"
            style="padding-left: 0rem;"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="18"
              viewBox="0 0 30 30"
            >
              <path
                d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <h1
              class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
              color="black"
            >
              Offer Management
            </h1>
          </button>
          <div
            style="display: flex; align-items: center; justify-content: space-between;"
          >
            <div
              class="team-access__view-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="offers-view"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="offers-view"
                      id="offers-view"
                      name="offers"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
            <div
              class="team-access__manage-cell"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="offers-manage"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      data-testid="offers-manage"
                      id="offers-manage"
                      name="offers"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </fieldset>
  </div>
</DocumentFragment>
`;
