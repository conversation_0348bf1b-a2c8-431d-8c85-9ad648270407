import React from 'react';
import Checkbox from 'canvas-core-react/lib/Checkbox';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import PropTypes from 'prop-types';

const ItemAccess = ({ id, onChangeStatus, label, checked, disabled, level, isFormDisabled }) => {
  return (
    <div className="team-access__item">
      <TextCaption component="h1" style={{ paddingLeft: `${level * 3}rem` }}
      >{ label }</TextCaption>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <div className="team-access__view-cell">
          <Checkbox
            id={`${id}-view`}
            label=""
            name={id}
            data-testid={`${id}-view`}
            onChange={(event) =>
              onChangeStatus(id, event.target.checked, 'view')
            }
            checked={checked?.view}
            disabled={disabled?.view || isFormDisabled}
          />
        </div>
        <div className="team-access__manage-cell">
          <Checkbox
            className="team-access__manage-cell"
            id={`${id}-manage`}
            label=""
            name={id}
            data-testid={`${id}-manage`}
            onChange={(event) =>
              onChangeStatus(id, event.target.checked, 'manage')
            }
            checked={checked?.manage}
            disabled={disabled?.manage || isFormDisabled}
          />
        </div>
      </div>
    </div>
  );
};

ItemAccess.propTypes = {
  label: PropTypes.string.isRequired,
  id: PropTypes.string.isRequired,
  disabled: PropTypes.object.isRequired,
  onChangeStatus: PropTypes.func.isRequired,
  checked: PropTypes.object.isRequired,
  level: PropTypes.number.isRequired,
  isFormDisabled: PropTypes.bool.isRequired,
};
export default ItemAccess;
