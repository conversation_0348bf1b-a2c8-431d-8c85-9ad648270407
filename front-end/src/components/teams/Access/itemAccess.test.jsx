import React from 'react';
import ItemAccess from './ItemAccess';
import { render, fireEvent } from '@testing-library/react';

describe('test render access item', () => {
  const onChangeStatus = jest.fn();
  it('should render the item access (enabled)', () => {
    const { asFragment } = render(
      <ItemAccess
        disabled={{ view: false, manage: false }}
        level={1}
        label="test-label"
        id="test-item-id"
        onChangeStatus={onChangeStatus}
        checked={{ view: true, manage: true }}
        isFormDisabled={false}
      />
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render the item access (disabled)', () => {
    const { asFragment } = render(
      <ItemAccess
        disabled={{ view: true, manage: true }}
        level={1}
        label="test-label"
        id="test-item-id"
        onChangeStatus={onChangeStatus}
        checked={{ view: true, manage: true }}
        isFormDisabled={false}
      />
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('should check the label', () => {
    const { getAllByText } = render(
      <ItemAccess
        disabled={{ view: true, manage: true }}
        level={1}
        label="test-label"
        id="test-item-id"
        onChangeStatus={onChangeStatus}
        checked={{ view: true, manage: true }}
        isFormDisabled={false}
      />
    );
    const label = getAllByText('test-label');
    expect(label).toHaveLength(1);
  });

  it('should check change function (view)', () => {
    const { getByTestId } = render(
      <ItemAccess
        disabled={{ view: true, manage: true }}
        level={1}
        label="test-label"
        id="test-label-id"
        onChangeStatus={onChangeStatus}
        checked={{ view: true, manage: true }}
        isFormDisabled={false}
      />
    );
    const textField = getByTestId('test-label-id-view');
    fireEvent.click(textField);
    expect(onChangeStatus).toHaveBeenCalled();
  });

  it('should check change function (manage)', () => {
    const onChangeStatus = jest.fn();
    const { getByTestId } = render(
      <ItemAccess
        disabled={{ view: true, manage: true }}
        level={1}
        label="test-label"
        id="test-label-id"
        onChangeStatus={onChangeStatus}
        checked={{ view: true, manage: true }}
        isFormDisabled={false}
      />
    );
    const textField = getByTestId('test-label-id-manage');
    fireEvent.click(textField);
    expect(onChangeStatus).toHaveBeenCalled();
  });
});
