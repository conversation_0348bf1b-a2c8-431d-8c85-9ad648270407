import React from 'react';

import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import Tooltip from 'canvas-core-react/lib/Tooltip';
import TextBody from 'canvas-core-react/lib/TextBody';

const AccessTooltip = () => {
  return (
    <TextSubtitle
      type="2"
      component="h3"
      className="advanced-targeting-section__subtitle"
    >
      Functionality
      <Tooltip
        id='campaign-enrollment-status-tooltip'
        heading='Functionality'
        infoButtonLabel='Info'
        closeButtonLabel='close'
      >
        <TextBody
          component='p'
          type='2'
          className='roles__role_tooltip_item'
        >
          Assign permissions to the functionalities this team should have access to
        </TextBody>
        <TextBody
          component='p'
          type='2'
          className='roles__role_tooltip_item'
        >
          <TextBody bold component='p' className='roles__role_tooltip_title_item'>Own team/application: </TextBody> This team is able to manage this functionality for their own team/application only.
        </TextBody>
        <TextBody
          component='p'
          type='2'
          className='roles__role_tooltip_item'
        >
         <TextBody bold component='p' className='roles__role_tooltip_title_item'>All teams/applications: </TextBody> This team is able to manage this functionality for all teams/applications in Pigeon.
        </TextBody>
      </Tooltip>
    </TextSubtitle>
  );
};

export default AccessTooltip;
