import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { omit, difference } from 'lodash';
import qs from 'qs';

import AlertBanner from 'canvas-core-react/lib/AlertBanner';
import Checkbox from 'canvas-core-react/lib/Checkbox';
import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';

import PageActions from '../pageActions';
import AdminFilter from '../listing/AdminFilter';
import List from '../listing/list';
import { debounce, removeFalsyKeys } from '../../utils';

import { useTeams } from '../../hooks/useTeams';
import { tableColumns } from './teamsColumns';
import { setTeamActivation, getTeams, getTeamOwners } from '../../store/actions/teams';
import { FUNCTIONALITY_LIST, TEAMS_FLAGS } from '../../constants/permissionsList';

const PAGE_SIZE = 30;
const BASE_QUERY = {
  application_id: '',
  functionality_id: '',
  owner_id: '',
  active: '',
  search: '',
  sort: '',
  limit: PAGE_SIZE,
  flag: TEAMS_FLAGS.INCLUDE_FUNCTIONALITIES,
  pageNumber: 1,
};

const TeamsList = () => {
  const dispatch = useDispatch();
  const history = useHistory();

  const queryParams = qs.parse(history.location.search, { ignoreQueryPrefix: true });
  const initialFilters = {
    ...BASE_QUERY,
    ...omit(queryParams, difference(Object.keys(queryParams), Object.keys(BASE_QUERY))),
  };
  const [ filters, setFilters ] = useState(initialFilters);
  const { loading, teams, teamOwners, currentUser, applications, users, ruleTypes, pagination } = useTeams(removeFalsyKeys(initialFilters));
  const { canViewAllTeams, canViewOwnTeam, canEditAllTeams, canEditOwnTeam } = useMemo(() => currentUser?.permissionLevels || {}, [ currentUser ]);
  const { permissions } = useMemo(() => currentUser || {}, [ currentUser ]);

  const [ isFilterOpen, setIsFilterOpen ] = useState(false);
  const [ teamToDeactivate, setTeamToDeactivate ] = useState(null);
  const [ teamToReactivate, setTeamToReactivate ] = useState(null);
  const [ reactivateChildren, setReactivateChildren ] = useState(false);

  useEffect(() => {
    const queryParams = removeFalsyKeys(omit(filters, [ 'limit', 'flag', ...(filters.pageNumber === 1 ? [ 'pageNumber' ] : []) ]));
    history.push({ search: qs.stringify(queryParams, { addQueryPrefix: true }) });
  }, [ filters ]);

  useEffect(() => {
    if (isFilterOpen) {
      if (filters.application_id) {
        const selectedApp = applications.find(a => a.id === parseInt(filters.application_id)).id;
        dispatch(getTeamOwners({ application_id: selectedApp }));
      } else {
        dispatch(getTeamOwners());
      }
    }
  }, [ isFilterOpen, filters.application_id ]);

  const teamOwnerFilterOptions = useMemo(() => {
    if (users && teamOwners) {
      const ownershipList = users.filter(u => teamOwners.includes(u.id)).map(u => ({ label: u.name, value: u.id }));
      return ownershipList.sort((a, b) => a.label.localeCompare(b.label));
    }
    return [];
  }, [ users, teamOwners ]);

  const fetchTeams = (newFilters) => {
    dispatch(getTeams(removeFalsyKeys(newFilters)));
  };

  const debounceFetchTeams = useCallback(debounce(fetchTeams, 500), []);

  const handleOnChangeSearch = (e) => {
    const search = e.target.value;
    debounceFetchTeams({ ...filters, search, pageNumber: 1 });
    setFilters((f) => ({ ...f, search, pageNumber: 1 }));
  };

  const filtersChanged = (newFilters) => {
    const applicationFilterChanged = filters.application_id && newFilters.application_id !== filters.application_id;
    const newQuery = {
      ...newFilters,
      ...(applicationFilterChanged ? { owner: '' } : {}),
    };
    setFilters(newQuery);
    dispatch(getTeams(removeFalsyKeys(newQuery)));
  };

  const handleClearFilters = () => {
    setFilters(BASE_QUERY);
    dispatch(getTeams(removeFalsyKeys(BASE_QUERY)));
  };

  const initialSortDirection = columnKey => {
    const { sort } = filters;
    if (!filters.sort) {
      return 0;
    }
    // check if the sorted key has a - in front of it which would indicate it's in descending order, ascending order otherwise
    if (sort.includes(columnKey)) {
      return (sort.charAt(0) === '-') ? 1 : -1;
    }
    return 0;
  };

  const onColumnSort = (columnKey, currentFilters) =>
    (row, direction) => {
      filtersChanged({ ...currentFilters, pageNumber: 1, sort: `${direction > 0 ? '-' : ''}${columnKey}` });
  };

  const sortableColumnProperties = useCallback((columnKey, currentFilters) => ({
    sortable: canViewAllTeams,
    overrideSortBehaviour: onColumnSort(columnKey, currentFilters),
    initialSortDirection: initialSortDirection(columnKey),
  }), []);

  const handleChangeTeamStatus = (e, teamId) => {
    const { checked } = e.currentTarget;
    if (checked) {
      setTeamToReactivate(teamId);
    } else {
      setTeamToDeactivate(teamId);
    }
  };

  if (!canViewOwnTeam) {
    return (<AlertBanner type="error">You do not have permissions to view this page</AlertBanner>);
  };

  return (
    <>
      <List
        entityName="team"
        className="teams-list"
        columns={tableColumns({
          isLoading: loading,
          history,
          canEditAllTeams,
          canEditOwnTeam,
          currentUser,
          applications,
          users,
          ruleTypes,
          filters,
          handleChangeTeamStatus,
          sortableColumnProperties,
        })}
        isLoading={loading}
        data={teams}
        permissions={permissions}
        columnFixed={false}
        resetSortOnDataChange={false}
      >
        { canViewAllTeams &&
          <>
            <PageActions
              onChange={handleOnChangeSearch}
              value={filters.search}
              filterButtonText="Filter"
              setParentFilteringOptionsCollapsed={setIsFilterOpen}
            >
              <AdminFilter
                className="admin-list__filter-options"
                onChange={filtersChanged}
                onClearClick={handleClearFilters}
                filterValues={filters}
                fields={[
                  {
                    label: 'Application',
                    key: 'application_id',
                    defaultOptionLabel: `All`,
                    options: applications.map((app) => ({
                      label: app.name,
                      value: app.id,
                    })).sort((a, b) => a.label.localeCompare(b.label)),
                  },
                  {
                    label: 'Functionality',
                    key: 'functionality_id',
                    defaultOptionLabel: `All`,
                    options: FUNCTIONALITY_LIST,
                  },
                  {
                    label: 'Team Owner',
                    key: 'owner_id',
                    defaultOptionLabel: `All`,
                    options: teamOwnerFilterOptions,
                  },
                  {
                    label: 'Status',
                    key: 'active',
                    defaultOptionLabel: `All`,
                    options: [
                      { value: true, label: 'Active' },
                      { value: false, label: 'Inactive' },
                    ],
                  },
                ]}
                renderAsCard={false}
              />
            </PageActions>
          </>
        }
      </List>

      { !loading && teams?.length > 0 &&
        <DesktopPagination
          id="teams-pagination"
          totalResultCount={pagination?.total || 0}
          onChange={(pageNumber) => {
            filtersChanged({ ...filters, pageNumber });
            window.scrollTo(0, 0);
          }}
          firstButtonLabel="First"
          prevButtonLabel="Previous"
          nextButtonLabel="Next"
          lastButtonLabel="Last"
          visiblePages={5}
          navigationLabel="Pagination Navigation"
          pageSize={pagination?.limit || 0}
          currentPage={teams ? (pagination?.offset / pagination?.limit) + 1 : 1}
          containerType="card"
        />
      }

      { /* Modals */ }
      <ModalDialogue
        className="teams-list__confirmation-modal"
        isModalVisible={teamToDeactivate !== null}
        headline="Are you sure you want to deactivate this team?"
        primaryButtonLabel="Deactivate Team"
        primaryAction={() => {
          dispatch(setTeamActivation(teamToDeactivate, false, false, filters));
          setTeamToDeactivate(null);
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setTeamToDeactivate(null)}
        setModalVisible={() => setTeamToDeactivate(null)}
      >
        Deactivating team will also deactivate all of its users and roles.
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={teamToReactivate !== null}
        headline="Are you sure you want to reactivate this team?"
        primaryButtonLabel="Reactivate Team"
        primaryAction={() => {
          dispatch(setTeamActivation(teamToReactivate, true, reactivateChildren, filters));
          setTeamToReactivate(null);
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => {
          setTeamToReactivate(null);
          setReactivateChildren(false);
        }}
        setModalVisible={() => {
          setTeamToReactivate(null);
          setReactivateChildren(false);
        }}
      >
        <span>
          Any users in the team who have a &quot;Team owner&quot; role will
          be activated with the team reactivation. The base &quot;Team
          owner&quot; and &quot;Viewer&quot; roles will also be activated.
        </span>
        <br />
        <span>
          All other users and roles in the team will not be activated. To
          reactivate those as well, please select the checkbox below.
        </span>
        <Checkbox
          id="teams-list__modal-checkbox"
          checked={reactivateChildren}
          onChange={(e) => setReactivateChildren(e.target.checked)}
          label="Reactivate all users and roles in the team"
        />
      </ModalDialogue>
    </>
  );
};

export default TeamsList;
