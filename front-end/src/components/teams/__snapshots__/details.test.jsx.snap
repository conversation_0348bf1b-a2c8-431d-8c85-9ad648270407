// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Team Details update team journey 1`] = `
<body
  style="overflow: unset;"
>
  <div>
    <div
      class="team-details"
    >
      <h1
        class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iejpSD"
        color="black"
      >
        Edit Team
      </h1>
      <div
        class="team-details__action-bar"
      />
      <form>
        <div
          class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container team-details__card"
          type="flatSolid"
        >
          <h2
            class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP team-details__sub-header"
            color="black"
            size="21"
          >
            Team Information
          </h2>
          <div
            class="toggle-field team-details__field"
          >
            <label
              class="toggle-field__label"
              for="active"
            />
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline toggle-field__toggle"
            >
              <label
                class="ToggleSwitchstyle__Wrapper-canvas-core__sc-168jcna-0 iKYIja ToggleSwitch__label"
                for="active"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input ToggleInput"
                  id="active"
                  name="active"
                  type="checkbox"
                  value=""
                />
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label labelSpacing"
                >
                  <div
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW labelSpacing"
                  >
                    Inactive
                  </div>
                </div>
                <span
                  class="ToggleSwitchstyle__StyledSlider-canvas-core__sc-168jcna-1 fsEBQI ToggleSwitch__slider"
                >
                  <span
                    class="ToggleSwitchstyle__StyledSwitch-canvas-core__sc-168jcna-2 kWOVUq ToggleSwitch__switch"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-off"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 10 10"
                    >
                      <g
                        class="IconToggleSwitchOffstyle__StyledG-canvas-core__sc-r3kqbd-0 ezyJRW"
                        fill="none"
                        fill-rule="evenodd"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-496.000000, -153.000000)"
                        >
                          <g
                            transform="translate(439.000000, 143.000000)"
                          >
                            <g
                              transform="translate(47.000000, 0.000000)"
                            >
                              <g
                                id="Group"
                                transform="translate(11.000000, 11.000000)"
                              >
                                <line
                                  id="Stroke-184"
                                  x1="0"
                                  x2="8"
                                  y1="0"
                                  y2="8"
                                />
                                <line
                                  id="Stroke-185"
                                  x1="0"
                                  x2="8"
                                  y1="8"
                                  y2="0"
                                />
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-on"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 11 9"
                    >
                      <g
                        class="IconToggleSwitchOnstyle__StyledG-canvas-core__sc-16ds4pu-0 fVdUxI"
                        fill="none"
                        fill-rule="evenodd"
                        id="Page-1"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-522.000000, -111.000000)"
                        >
                          <g
                            transform="translate(439.000000, 100.000000)"
                          >
                            <g
                              transform="translate(84.000000, 12.000000)"
                            >
                              <polyline
                                points="0 3.73333333 3.15 7 9 0"
                              />
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </span>
                </span>
              </label>
            </div>
          </div>
          <div
            class="input-text-field team-details__field"
          >
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
            >
              <div
                class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
              >
                <label
                  class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                  for="name-input"
                  id="name-label"
                  label="Team Name"
                >
                  Team Name
                </label>
              </div>
              <input
                aria-describedby=""
                class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 ddelfF Input__input TextFieldstyle__StyledInput-canvas-core__sc-w9yen5-0 liAfho"
                id="name-input"
                placeholder="Enter team name"
                type="text"
                value="Regular team"
              />
            </div>
          </div>
          <div
            class="input-text-field team-details__field"
          >
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
            >
              <div
                class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
              >
                <label
                  class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                  for="description-input"
                  id="description-label"
                  label="Team Description"
                >
                  Team Description
                </label>
              </div>
              <input
                aria-describedby=""
                class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 ddelfF Input__input TextFieldstyle__StyledInput-canvas-core__sc-w9yen5-0 liAfho"
                id="description-input"
                placeholder="Enter team description"
                type="text"
                value="new team description"
              />
            </div>
          </div>
        </div>
        <div
          class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container team-details__card"
          type="flatSolid"
        >
          <h2
            class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP team-details__sub-header"
            color="black"
            size="21"
          >
            Team owners
          </h2>
          <div
            class="Tablestyle__StyledTable-canvas-core__sc-mfsabp-0 dvntiZ Table"
          >
            <div
              class="Tablestyle__StyledTableWrapperColumnFixed-canvas-core__sc-mfsabp-2 dTfXWN Table__wrapper--columnFixed"
            >
              <table
                aria-hidden="true"
                class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable admin-list__table team-details__owner_table"
              >
                <thead
                  class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
                  role="rowgroup"
                >
                  <tr
                    class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
                    role="row"
                  >
                    <th
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 cuQFVG"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        Name
                      </p>
                    </th>
                    <th
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        sID
                      </p>
                    </th>
                    <th
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        Email
                      </p>
                    </th>
                    <th
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        Action
                      </p>
                    </th>
                  </tr>
                </thead>
                <tbody
                  class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
                  role="rowgroup"
                >
                  <tr
                    class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jGMNii"
                    role="row"
                  >
                    <th
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 ecHgfY"
                      role="rowheader"
                      scope="row"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        Test Regular Team Owner
                      </p>
                    </th>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                      role="cell"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        s1002001
                      </p>
                    </td>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                      role="cell"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        <EMAIL>
                      </p>
                    </td>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                      role="cell"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 jJgVLK SvgIcon__icon"
                        color="currentColor"
                        data-testid="delete-owner-btn-s1002001"
                        focusable="false"
                        role="presentation"
                        size="20"
                        style="cursor: not-allowed;"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M18.8977 13.5471L17.3499 21.9875"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M11.5088 13.5471L13.0567 21.9837"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M9.52832 6.70208V3.37484C9.52832 2.33931 10.3678 1.49984 11.4033 1.49984L17.2502 1.49984H19.3921C20.4276 1.49984 21.2671 2.3393 21.2671 3.37484V6.70208"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M1.72485 6.90275H28.2754"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M26.4994 7.38814C25.4572 13.6422 24.4149 19.8962 23.3727 26.1503C23.1473 27.5031 21.9109 28.5 20.4584 28.5H9.62805C8.15415 28.5 6.90641 27.4743 6.70552 26.0976L3.91528 6.9758"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div
              class="Tablestyle__StyledTableWrapper-canvas-core__sc-mfsabp-1 kTteCq Table__wrapper"
              id="table-admin-list-table"
            >
              <table
                class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable admin-list__table team-details__owner_table"
              >
                <caption
                  aria-live="polite"
                  class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP TableHeadingstyle__StyledHeading-canvas-core__sc-2qo8vm-0 bPEdEe Table__title"
                  color="black"
                  size="21"
                />
                <thead
                  class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
                  role="rowgroup"
                >
                  <tr
                    class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
                    role="row"
                  >
                    <th
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        Name
                      </p>
                    </th>
                    <th
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        sID
                      </p>
                    </th>
                    <th
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        Email
                      </p>
                    </th>
                    <th
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        Action
                      </p>
                    </th>
                  </tr>
                </thead>
                <tbody
                  class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
                  role="rowgroup"
                >
                  <tr
                    class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jGMNii"
                    role="row"
                  >
                    <th
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                      role="rowheader"
                      scope="row"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        Test Regular Team Owner
                      </p>
                    </th>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                      role="cell"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        s1002001
                      </p>
                    </td>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                      role="cell"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        <EMAIL>
                      </p>
                    </td>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                      role="cell"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 jJgVLK SvgIcon__icon"
                        color="currentColor"
                        data-testid="delete-owner-btn-s1002001"
                        focusable="false"
                        role="presentation"
                        size="20"
                        style="cursor: not-allowed;"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M18.8977 13.5471L17.3499 21.9875"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M11.5088 13.5471L13.0567 21.9837"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M9.52832 6.70208V3.37484C9.52832 2.33931 10.3678 1.49984 11.4033 1.49984L17.2502 1.49984H19.3921C20.4276 1.49984 21.2671 2.3393 21.2671 3.37484V6.70208"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M1.72485 6.90275H28.2754"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M26.4994 7.38814C25.4572 13.6422 24.4149 19.8962 23.3727 26.1503C23.1473 27.5031 21.9109 28.5 20.4584 28.5H9.62805C8.15415 28.5 6.90641 27.4743 6.70552 26.0976L3.91528 6.9758"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <button
            class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button team-details__add-owner-button"
            color="blue"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="16"
              viewBox="0 0 30 30"
            >
              <path
                d="M15.0001 2.27197L15.0001 27.7278"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M27.728 14.9999H2.27213"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <span
              class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
            >
              Add team owner
            </span>
          </button>
        </div>
        <div
          class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container team-access__card"
          type="flatSolid"
        >
          <h2
            class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP admin-details__sub-header"
            color="black"
            size="21"
          >
            Grant access
          </h2>
          <div
            class="team-access__header"
          >
            <h3
              class="TextSubtitlestyle__Text-canvas-core__sc-1l85m0x-0 gPYxBg TextSubtitle__text advanced-targeting-section__subtitle"
              color="black"
              type="2"
            >
              Functionality
              <div
                class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                id="tooltip-container-campaign-enrollment-status-tooltip"
              >
                <div
                  class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                >
                  <button
                    aria-label="Info, Functionality"
                    class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
                    id="desktop-icon-campaign-enrollment-status-tooltip"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btSiGf"
                      color="black"
                      focusable="false"
                      role="presentation"
                      size="18"
                      viewBox="0 0 30 30"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                        fill="none"
                        fill-rule="evenodd"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M15 20.9062V14.1562"
                        fill="none"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <circle
                        cx="14.9998"
                        cy="9.1309"
                        r="0.7"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </h3>
            <div
              style="display: flex; align-items: center; justify-content: space-between;"
            >
              <div
                class="team-access__view-cell"
              >
                <div
                  class="Gridstyle__Wrapper-canvas-core__sc-6d4gtg-0 jtviWe Grid__container"
                >
                  <div
                    class="Rowstyle__Wrapper-canvas-core__sc-tnvou7-0 BXVYe Row__container"
                  >
                    <div
                      class="Columnstyle__Wrapper-canvas-core__sc-z9aox0-0 ckffmr Column__container"
                    >
                      <h3
                        class="TextSubtitlestyle__Text-canvas-core__sc-1l85m0x-0 gPYxBg TextSubtitle__text advanced-targeting-section__subtitle"
                        color="black"
                        type="2"
                      >
                        View
                      </h3>
                    </div>
                    <div
                      class="Columnstyle__Wrapper-canvas-core__sc-z9aox0-0 ckffmr Column__container"
                    >
                      <div
                        class="team-access__select-all-checkbox"
                      >
                        <div
                          class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                        >
                          <div
                            class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                          >
                            <label
                              class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                              for="view-access-checkbox"
                            >
                              <input
                                class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                                data-testid="view-access-checkbox"
                                id="view-access-checkbox"
                                type="checkbox"
                                value=""
                              />
                              <span
                                class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                              >
                                <svg
                                  aria-hidden="true"
                                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                                  color="currentColor"
                                  focusable="false"
                                  role="presentation"
                                  size="12"
                                  viewBox="0 0 15 2"
                                >
                                  <path
                                    d="M1.5 1H13.5"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                  />
                                </svg>
                                <svg
                                  aria-hidden="true"
                                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                                  color="currentColor"
                                  focusable="false"
                                  role="presentation"
                                  size="12"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                    stroke="none"
                                  />
                                </svg>
                              </span>
                              <div
                                class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                              />
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="team-access__manage-cell"
              >
                <div
                  class="Gridstyle__Wrapper-canvas-core__sc-6d4gtg-0 jtviWe Grid__container"
                >
                  <div
                    class="Rowstyle__Wrapper-canvas-core__sc-tnvou7-0 BXVYe Row__container"
                  >
                    <div
                      class="Columnstyle__Wrapper-canvas-core__sc-z9aox0-0 ckffmr Column__container"
                    >
                      <h3
                        class="TextSubtitlestyle__Text-canvas-core__sc-1l85m0x-0 gPYxBg TextSubtitle__text advanced-targeting-section__subtitle"
                        color="black"
                        type="2"
                      >
                        Manage
                      </h3>
                    </div>
                    <div
                      class="Columnstyle__Wrapper-canvas-core__sc-z9aox0-0 ckffmr Column__container"
                    >
                      <div
                        class="team-access__select-all-checkbox"
                      >
                        <div
                          class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                        >
                          <div
                            class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                          >
                            <label
                              class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                              for="manage-access-checkbox"
                            >
                              <input
                                class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                                data-testid="manage-access-checkbox"
                                id="manage-access-checkbox"
                                type="checkbox"
                                value=""
                              />
                              <span
                                class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                              >
                                <svg
                                  aria-hidden="true"
                                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                                  color="currentColor"
                                  focusable="false"
                                  role="presentation"
                                  size="12"
                                  viewBox="0 0 15 2"
                                >
                                  <path
                                    d="M1.5 1H13.5"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                  />
                                </svg>
                                <svg
                                  aria-hidden="true"
                                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                                  color="currentColor"
                                  focusable="false"
                                  role="presentation"
                                  size="12"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                    stroke="none"
                                  />
                                </svg>
                              </span>
                              <div
                                class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                              />
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <fieldset
            class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 InputGroup__fieldset"
            id="checkbox-group-1-inputgroup"
            name="group-1"
          >
            <legend
              class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 hlGdcC"
            >
              
            </legend>
            <div
              class="team-access__group_container"
            >
              <div
                class="team-access__group"
              >
                <button
                  class="team-access__item-label"
                  data-testid="team-access__item-label-campaigns"
                  style="padding-left: 0rem;"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <h1
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
                    color="black"
                  >
                    Campaigns
                  </h1>
                </button>
                <div
                  style="display: flex; align-items: center; justify-content: space-between;"
                >
                  <div
                    class="team-access__view-cell"
                  >
                    <div
                      class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                    >
                      <div
                        class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                      >
                        <label
                          class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                          for="campaigns-view"
                        >
                          <input
                            class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                            data-testid="campaigns-view"
                            id="campaigns-view"
                            name="campaigns"
                            type="checkbox"
                            value=""
                          />
                          <span
                            class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 15 2"
                            >
                              <path
                                d="M1.5 1H13.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                stroke="none"
                              />
                            </svg>
                          </span>
                          <div
                            class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                  <div
                    class="team-access__manage-cell"
                  >
                    <div
                      class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
                    >
                      <div
                        class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                      >
                        <label
                          class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                          for="campaigns-manage"
                        >
                          <input
                            class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                            data-testid="campaigns-manage"
                            id="campaigns-manage"
                            name="campaigns"
                            type="checkbox"
                            value=""
                          />
                          <span
                            class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 15 2"
                            >
                              <path
                                d="M1.5 1H13.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                stroke="none"
                              />
                            </svg>
                          </span>
                          <div
                            class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="team-access__group_container"
            >
              <div
                class="team-access__group"
              >
                <button
                  class="team-access__item-label"
                  data-testid="team-access__item-label-alerts"
                  style="padding-left: 0rem;"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <h1
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
                    color="black"
                  >
                    Alerts
                  </h1>
                </button>
                <div
                  style="display: flex; align-items: center; justify-content: space-between;"
                >
                  <div
                    class="team-access__view-cell"
                  >
                    <div
                      class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                    >
                      <div
                        class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                      >
                        <label
                          class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                          for="alerts-view"
                        >
                          <input
                            class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                            data-testid="alerts-view"
                            id="alerts-view"
                            name="alerts"
                            type="checkbox"
                            value=""
                          />
                          <span
                            class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 15 2"
                            >
                              <path
                                d="M1.5 1H13.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                stroke="none"
                              />
                            </svg>
                          </span>
                          <div
                            class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                  <div
                    class="team-access__manage-cell"
                  >
                    <div
                      class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
                    >
                      <div
                        class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                      >
                        <label
                          class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                          for="alerts-manage"
                        >
                          <input
                            class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                            data-testid="alerts-manage"
                            id="alerts-manage"
                            name="alerts"
                            type="checkbox"
                            value=""
                          />
                          <span
                            class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 15 2"
                            >
                              <path
                                d="M1.5 1H13.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                stroke="none"
                              />
                            </svg>
                          </span>
                          <div
                            class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="team-access__group_container"
            >
              <div
                class="team-access__group"
              >
                <button
                  class="team-access__item-label"
                  data-testid="team-access__item-label-placements"
                  style="padding-left: 0rem;"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <h1
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
                    color="black"
                  >
                    Placement
                  </h1>
                </button>
                <div
                  style="display: flex; align-items: center; justify-content: space-between;"
                >
                  <div
                    class="team-access__view-cell"
                  >
                    <div
                      class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                    >
                      <div
                        class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                      >
                        <label
                          class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                          for="placements-view"
                        >
                          <input
                            class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                            data-testid="placements-view"
                            id="placements-view"
                            name="placements"
                            type="checkbox"
                            value=""
                          />
                          <span
                            class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 15 2"
                            >
                              <path
                                d="M1.5 1H13.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                stroke="none"
                              />
                            </svg>
                          </span>
                          <div
                            class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                  <div
                    class="team-access__manage-cell"
                  >
                    <div
                      class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
                    >
                      <div
                        class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                      >
                        <label
                          class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                          for="placements-manage"
                        >
                          <input
                            class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                            data-testid="placements-manage"
                            id="placements-manage"
                            name="placements"
                            type="checkbox"
                            value=""
                          />
                          <span
                            class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 15 2"
                            >
                              <path
                                d="M1.5 1H13.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                stroke="none"
                              />
                            </svg>
                          </span>
                          <div
                            class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="team-access__group_container"
            >
              <div
                class="team-access__group"
              >
                <button
                  class="team-access__item-label"
                  data-testid="team-access__item-label-access-control"
                  style="padding-left: 0rem;"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <h1
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
                    color="black"
                  >
                    Access control
                  </h1>
                </button>
                <div
                  style="display: flex; align-items: center; justify-content: space-between;"
                >
                  <div
                    class="team-access__view-cell"
                  >
                    <div
                      class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                    >
                      <div
                        class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                      >
                        <label
                          class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                          for="access-control-view"
                        >
                          <input
                            class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                            data-testid="access-control-view"
                            id="access-control-view"
                            name="access-control"
                            type="checkbox"
                            value=""
                          />
                          <span
                            class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 15 2"
                            >
                              <path
                                d="M1.5 1H13.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                stroke="none"
                              />
                            </svg>
                          </span>
                          <div
                            class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                  <div
                    class="team-access__manage-cell"
                  >
                    <div
                      class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
                    >
                      <div
                        class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                      >
                        <label
                          class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                          for="access-control-manage"
                        >
                          <input
                            class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                            data-testid="access-control-manage"
                            id="access-control-manage"
                            name="access-control"
                            type="checkbox"
                            value=""
                          />
                          <span
                            class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 15 2"
                            >
                              <path
                                d="M1.5 1H13.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                stroke="none"
                              />
                            </svg>
                          </span>
                          <div
                            class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="team-access__group_container"
            >
              <div
                class="team-access__group"
              >
                <button
                  class="team-access__item-label"
                  data-testid="team-access__item-label-variable-mapping"
                  style="padding-left: 0rem;"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <h1
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
                    color="black"
                  >
                    Variable mapping
                  </h1>
                </button>
                <div
                  style="display: flex; align-items: center; justify-content: space-between;"
                >
                  <div
                    class="team-access__view-cell"
                  >
                    <div
                      class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                    >
                      <div
                        class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                      >
                        <label
                          class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                          for="variable-mapping-view"
                        >
                          <input
                            class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                            data-testid="variable-mapping-view"
                            id="variable-mapping-view"
                            name="variable-mapping"
                            type="checkbox"
                            value=""
                          />
                          <span
                            class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 15 2"
                            >
                              <path
                                d="M1.5 1H13.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                stroke="none"
                              />
                            </svg>
                          </span>
                          <div
                            class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                  <div
                    class="team-access__manage-cell"
                  >
                    <div
                      class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
                    >
                      <div
                        class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                      >
                        <label
                          class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                          for="variable-mapping-manage"
                        >
                          <input
                            class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                            data-testid="variable-mapping-manage"
                            id="variable-mapping-manage"
                            name="variable-mapping"
                            type="checkbox"
                            value=""
                          />
                          <span
                            class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 15 2"
                            >
                              <path
                                d="M1.5 1H13.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                stroke="none"
                              />
                            </svg>
                          </span>
                          <div
                            class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="team-access__group_container"
            >
              <div
                class="team-access__group"
              >
                <button
                  class="team-access__item-label"
                  data-testid="team-access__item-label-message-centre"
                  style="padding-left: 0rem;"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <h1
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
                    color="black"
                  >
                    Message Centre
                  </h1>
                </button>
                <div
                  style="display: flex; align-items: center; justify-content: space-between;"
                >
                  <div
                    class="team-access__view-cell"
                  >
                    <div
                      class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                    >
                      <div
                        class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                      >
                        <label
                          class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                          for="message-centre-view"
                        >
                          <input
                            class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                            data-testid="message-centre-view"
                            id="message-centre-view"
                            name="message-centre"
                            type="checkbox"
                            value=""
                          />
                          <span
                            class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 15 2"
                            >
                              <path
                                d="M1.5 1H13.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                stroke="none"
                              />
                            </svg>
                          </span>
                          <div
                            class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                  <div
                    class="team-access__manage-cell"
                  >
                    <div
                      class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
                    >
                      <div
                        class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                      >
                        <label
                          class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                          for="message-centre-manage"
                        >
                          <input
                            class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                            data-testid="message-centre-manage"
                            id="message-centre-manage"
                            name="message-centre"
                            type="checkbox"
                            value=""
                          />
                          <span
                            class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 15 2"
                            >
                              <path
                                d="M1.5 1H13.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                stroke="none"
                              />
                            </svg>
                          </span>
                          <div
                            class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="team-access__group_container"
            >
              <div
                class="team-access__group"
              >
                <button
                  class="team-access__item-label"
                  data-testid="team-access__item-label-offers"
                  style="padding-left: 0rem;"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon team-access__chevron"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <h1
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text"
                    color="black"
                  >
                    Offer Management
                  </h1>
                </button>
                <div
                  style="display: flex; align-items: center; justify-content: space-between;"
                >
                  <div
                    class="team-access__view-cell"
                  >
                    <div
                      class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
                    >
                      <div
                        class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                      >
                        <label
                          class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                          for="offers-view"
                        >
                          <input
                            class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                            data-testid="offers-view"
                            id="offers-view"
                            name="offers"
                            type="checkbox"
                            value=""
                          />
                          <span
                            class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 15 2"
                            >
                              <path
                                d="M1.5 1H13.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                stroke="none"
                              />
                            </svg>
                          </span>
                          <div
                            class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                  <div
                    class="team-access__manage-cell"
                  >
                    <div
                      class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container team-access__manage-cell"
                    >
                      <div
                        class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                      >
                        <label
                          class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                          for="offers-manage"
                        >
                          <input
                            class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                            data-testid="offers-manage"
                            id="offers-manage"
                            name="offers"
                            type="checkbox"
                            value=""
                          />
                          <span
                            class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 15 2"
                            >
                              <path
                                d="M1.5 1H13.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                              />
                            </svg>
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                              color="currentColor"
                              focusable="false"
                              role="presentation"
                              size="12"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                                stroke="none"
                              />
                            </svg>
                          </span>
                          <div
                            class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </fieldset>
        </div>
        <div
          class="team-details__action-buttons"
        >
          <button
            class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary team-details__action-button"
            type="button"
          >
            <span
              class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
              tabindex="-1"
            >
              <span
                class="ButtonCore__text"
              >
                Cancel
              </span>
            </span>
          </button>
          <button
            class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button PrimaryButtonstyle__StyledPrimaryButtonCore-canvas-core__sc-11pddd2-0 jbDssn Button__button--primary team-details__action-button"
            type="submit"
          >
            <span
              class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
              tabindex="-1"
            >
              <span
                class="ButtonCore__text"
              >
                Update Team
              </span>
            </span>
          </button>
        </div>
      </form>
    </div>
  </div>
</body>
`;
