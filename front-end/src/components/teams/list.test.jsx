import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import { within } from '@testing-library/dom';
import { act } from 'react-dom/test-utils';
import mockAxios from 'axios';
import { createMemoryHistory } from 'history';
import { Provider } from 'react-redux';
import { Router } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';

import { mapPropToKey } from '../../constants';
import { defaultData, renderPage } from '../../utils/testing-library-utils';
import TeamsList from './list';

const mockStore = configureMockStore([ thunk ]);
const defaultStore = {
  teams: {
    isLoading: false,
    items: defaultData.teams,
  },
  applications: {
    items: defaultData.applications,
    isLoading: false,
  },
  authenticated: {
    permissionLevels: { canViewAllTeams: true, canViewOwnTeam: true, canEditAllTeams: true, canEditOwnTeam: true },
  },
  users: {
    items: defaultData.users,
    isLoading: false,
  },
  ruleTypes: {
    items: defaultData.ruleTypes,
    isLoading: false,
  },
};
const commonProps = {
  applications: defaultData.applications,
  permissionLevels: { canViewAllTeams: true, canViewOwnTeam: true, canEditAllTeams: true, canEditOwnTeam: true },
  setTeamActivation: jest.fn(),
};

describe('Teams list', () => {
  mockAxios.post.mockImplementation(() => Promise.resolve({ data: { data: {} } }));

  afterEach(() => {
    mockAxios.reset();
  });

  it('create journey + happy path render', async() => {
    const mockTeams = defaultData.teams.map(team => ({ ...team, functionalities: [ 1, 2 ] }));
    const renderOptions = { initialState: { teams: { isLoading: false, items: mapPropToKey(mockTeams, 'id') } } };
    const { getByRole, history, baseElement } = await renderPage(<TeamsList />, renderOptions);
    expect(baseElement).toMatchSnapshot();
    fireEvent.click(getByRole('button', { name: 'Create Team' }));
    expect(history.location.pathname).toStrictEqual('/teams/create');
  });

  it('deactivate journey', async() => {
    const { getByText, getAllByText, getByRole, queryByText } = await renderPage(<TeamsList />);

    // confirm pigeon team cannot be modified
    const pigeonRow = getAllByText(defaultData.teams[0].name)[0];
    const pigeonToggle = within(pigeonRow.closest('tr')).getByTestId('deactivation-toggle-1');
    expect(pigeonToggle.disabled).toBeTruthy();

    // trigger deactivation warning
    const regularTeam = defaultData.teams[1];
    const regularRow = getAllByText(regularTeam.name)[0];
    const regularToggle = within(regularRow.closest('tr')).getByTestId('deactivation-toggle-2');
    expect(regularToggle.disabled).toBeFalsy();
    fireEvent.click(regularToggle);

    // due to known bug in canvas prior to 7.1.0, every time modal dialogue opens,
    // we must wait for focus animation to complete before closing the modal
    // https://bitbucket.agile.bns/projects/CANVAS/repos/core-react-develop/pull-requests/779/overview

    expect(getByText('Are you sure you want to deactivate this team?')).toBeInTheDocument();

    // test cancel/secondary button
    fireEvent.click(getByRole('button', { name: 'Cancel' }));
    expect(queryByText('Are you sure you want to deactivate this team?')).not.toBeInTheDocument();
    fireEvent.click(regularToggle);

    await act(async() => fireEvent.click(getByRole('button', { name: 'Deactivate Team' })));
    expect(mockAxios.post).toHaveBeenCalledWith(`/teams/${regularTeam.id}/deactivate`, undefined, undefined);
  });

  it('activate journey', async() => {
    const { getByText, getAllByText, getByRole } = await renderPage(<TeamsList />);

    // trigger activation warning
    const inactiveTeam = defaultData.teams[2];
    const inactiveRow = getAllByText(inactiveTeam.name)[0];
    const toggle = within(inactiveRow.closest('tr')).getByTestId('deactivation-toggle-3');
    fireEvent.click(toggle);

    // due to known bug in canvas prior to 7.1.0, every time modal dialogue opens,
    // we must wait for focus animation to complete before closing the modal
    // https://bitbucket.agile.bns/projects/CANVAS/repos/core-react-develop/pull-requests/779/overview

    expect(getByText('Reactivate all users and roles in the team')).toBeInTheDocument();

    // activate team only
    await act(async() => fireEvent.click(getByRole('button', { name: 'Reactivate Team' })));
    expect(mockAxios.post).toHaveBeenCalledWith(`/teams/${inactiveTeam.id}/activate?activateChildren=false`, undefined, undefined);

    // activate team, and its users and roles. can test in same test case as mock store doesn't update
    fireEvent.click(toggle);

    const checkbox = getByText('Reactivate all users and roles in the team').closest('label');
    await act(async() => fireEvent.click(checkbox));
     // checkbox onchange handler causes state change > modal re-render > must wait for modal animation
    await act(async() => fireEvent.click(getByRole('button', { name: 'Reactivate Team' })));
    expect(mockAxios.post).toHaveBeenCalledWith(`/teams/${inactiveTeam.id}/activate?activateChildren=true`, undefined, undefined);
  });

  it('should render spinner while loading', async() => {
    const { baseElement } = await renderPage(
      <TeamsList />,
      { initialState: { teams: { isLoading: true } } },
    );
    expect(baseElement).toMatchSnapshot(); // no text element to assert, have to resort to snapshot matching
  });

  test('Sorting for team list', async() => {
    const sortTeams = jest.fn();
    const history = createMemoryHistory();
    const store = mockStore(defaultStore);
    const { getByText } = render(
      <Provider store={store}>
        <Router history={history}>
          <TeamsList
            {...commonProps}
            sortTeams={sortTeams}
          />
        </Router>
      </Provider>
    );

    sortTeams.mockReset();
    await act(async() => fireEvent.click(getByText('Team')));
    const sortAsc = store.getActions().filter(s => s.type === 'TEAMS_LIST_DATA');
    expect(sortAsc[sortAsc.length - 1].data.url.search('sort=name')).toBeGreaterThan(0);

    await act(async() => fireEvent.click(getByText('Team')));
    const sortDes = store.getActions().filter(s => s.type === 'TEAMS_LIST_DATA');
    expect(sortDes[sortDes.length - 1].data.url.search('sort=-name')).toBeGreaterThan(0);
  });

  test('re-route to teams details page on clicking the team name', async() => {
    const history = createMemoryHistory();
    const { getAllByText } = render(
      <Provider store={mockStore(defaultStore)}>
        <Router history={history}>
          <TeamsList
            {...commonProps}
          />
        </Router>
      </Provider>
    );

    const firstTeam = defaultData.teams[0];

    fireEvent.click(getAllByText(firstTeam.name)[0]);
    expect(history.location.pathname).toBe(`/teams/${firstTeam.id}`);
  });

  test('No permissions - check for alert banner', () => {
    const history = createMemoryHistory();
    const noViewPermissionStore = { ...defaultStore, authenticated: { permissionLevels: { canViewOwnTeam: false } } };
    const { getByText } = render(
      <Provider store={mockStore(noViewPermissionStore)}>
        <Router history={history}>
          <TeamsList />
        </Router>
      </Provider>
    );
    expect(getByText('You do not have permissions to view this page')).toBeInTheDocument();
  });
});
