
import React from 'react';
import { findKey, uniq, startCase } from 'lodash';

import TextButton from 'canvas-core-react/lib/TextButton';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import ToggleSwitch from 'canvas-core-react/lib/ToggleSwitch';

import { STATIC_PERMISSION_SECTIONS } from '../../constants/permissionsList';

export const tableColumns = ({
  isLoading,
  history,
  canEditAllTeams,
  canEditOwnTeam,
  currentUser,
  applications,
  users,
  ruleTypes,
  filters,
  handleChangeTeamStatus,
  sortableColumnProperties,
}) => {
  const columns = [
    {
      name: 'Team',
      cellFormatter: (row) => (
        <TextButton
          className="teams-list__link-button"
          onClick={() => history.push(`/teams/${row.id}`)}
        >
          { row.name }
        </TextButton>
      ),
      selector: '',
      ...sortableColumnProperties('name', filters),
    },
    {
      name: 'Team Owners',
      cellFormatter: (row) => (
        <ul>
          { users
              .filter(u => u.roles.includes(row.ownerRoleId))
              .map((o, idx) => (
                <TextCaption key={`user-${idx}`} component="p">{ o.name }</TextCaption>
              ))
          }
        </ul>
      ),
      selector: '',
    },
    {
      name: 'Applications',
      cellFormatter: (row) => (
        <ul>
          { row.ownedApplications?.map((appName, index) => (
            <TextCaption key={`app-${index}`} component="p">
              { appName } <b>(Owner)</b>
            </TextCaption>
          )) }
          { row.accessApplicationIds
              ?.filter((appId) => !row.ownedApplications.includes(applications?.find((app) => app.id === appId)?.name))
              .map((appId) => (
                <TextCaption key={appId} component="p">
                  { applications.find((app) => app.id === appId)?.name }
                </TextCaption>
              ))
          }
        </ul>
      ),
      selector: '',
    },
    {
      name: 'Functionalities',
      cellFormatter: (row) => {
        const dynamicTeamAccess = row.functionalities
          ?.map(functionality => ruleTypes?.find(r => r.id === functionality)?.rule_type) || [];
        const staticTeamPermissions = uniq(row.permissions
          ?.map((permission) => findKey(STATIC_PERMISSION_SECTIONS, (o) => o.find((p) => p === permission)))
          .filter((permission) => !!permission)) || [];
        return (
          <ul>
            { /* Campaign, alert, estore, vignette */ }
            { dynamicTeamAccess.map(access => (
              <TextCaption key={access} component="p">{ startCase(access) }</TextCaption>
            )) }
            { /* Placement, access contol, variable mapping */ }
            { staticTeamPermissions.map((permission) => (
                <TextCaption key={permission} component="p">{ startCase(permission) }</TextCaption>
            )) }
          </ul>
        );
      },
      selector: '',
    },
    {
      name: 'Status',
      cellFormatter: (row) => (
        <ToggleSwitch
          label=""
          id={`deactivation-toggle-${row.id}`}
          data-testid={`deactivation-toggle-${row.id}`}
          name={`deactivation-toggle-${row.id}`}
          className="teams-list__toggle-switch"
          checked={row.active}
          disabled={
            isLoading ||
            row.id === 1 || // Pigeon (team id 1) cannot be deactivated
            !(canEditAllTeams || (canEditOwnTeam && row.id === currentUser?.team_id))
          }
          onChange={(e) => handleChangeTeamStatus(e, row.id)}
        />
      ),
      // TODO: Migrate to headerFormatter with tooltip component when on Canvas 9.3.0+
      tooltip: 'An active team can have active users and roles. An inactive team can only have inactive users and roles. By default, the Pigeon team can never be deactivated.',
    },
  ];
  return columns;
};
