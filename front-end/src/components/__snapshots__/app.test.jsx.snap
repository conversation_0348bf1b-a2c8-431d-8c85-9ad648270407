// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`App (Other Error) Snapshot 1`] = `
<Fragment>
  <SkipLink
    targetId="content-main"
  />
  <Connect(MainNavigation) />
  <Connect(SubNavigation) />
  <main
    className="margin padding-side"
    id="content-main"
  />
  <IENotSupported />
  <Footer />
</Fragment>
`;

exports[`App (logged in) - alert banner message Snapshot 1`] = `
<Fragment>
  <SkipLink
    targetId="content-main"
  />
  <Connect(MainNavigation) />
  <nav
    className="padding-side margin content__alert-banner"
    style={
      {
        "marginLeft": "auto",
        "marginRight": "auto",
        "maxWidth": "1440px",
      }
    }
  >
    <M
      closeButton={
        {
          "label": "close",
          "onClick": [MockFunction],
        }
      }
      gridMargins={{}}
      isScreenWidthVariant={false}
      separatedLines={false}
      statusBadgeText="New"
    >
      Test message
    </M>
  </nav>
  <Connect(SubNavigation) />
  <main
    className="margin padding-side"
    id="content-main"
  />
  <IENotSupported />
  <Footer />
</Fragment>
`;

exports[`App (logged in) Snapshot 1`] = `
<Fragment>
  <SkipLink
    targetId="content-main"
  />
  <Connect(MainNavigation) />
  <Connect(SubNavigation) />
  <main
    className="margin padding-side"
    id="content-main"
  />
  <IENotSupported />
  <Footer />
</Fragment>
`;

exports[`App (logged out) and snackbar Snapshot 1`] = `
<Fragment>
  <SkipLink
    targetId="content-main"
  />
  <Connect(MainNavigation) />
  <Connect(SubNavigation) />
  <main
    className="margin padding-side"
    id="content-main"
  >
    App
    <LoginContainer
      logout={[MockFunction]}
    />
  </main>
  <m
    duration={1500}
    onClose={[Function]}
    open={true}
  >
    <strong
      style={
        {
          "paddingRight": 5,
        }
      }
    >
      <Component />
    </strong>
  </m>
  <IENotSupported />
  <Footer />
</Fragment>
`;

exports[`App Snapshot 1`] = `
<Fragment>
  <SkipLink
    targetId="content-main"
  />
  <Connect(MainNavigation) />
  <Connect(SubNavigation) />
  <main
    className="margin padding-side"
    id="content-main"
  />
  <IENotSupported />
  <Footer />
</Fragment>
`;
