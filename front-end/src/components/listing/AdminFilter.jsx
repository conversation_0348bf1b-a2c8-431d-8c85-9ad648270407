import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import moment from 'moment';

import Card from 'canvas-core-react/lib/Card';
import DatePicker from 'canvas-core-react/lib/DatePicker';
import Filter from 'canvas-core-react/lib/Filter';
import IconCloseCircle from 'canvas-core-react/lib/IconCloseCircle';
import TextButton from 'canvas-core-react/lib/TextButton';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import LoadingIndicator from 'canvas-core-react/lib/LoadingIndicator';
import TextField from 'canvas-core-react/lib/TextField';

import { createIdFromName } from '../../utils';
import { SHORT_MONTH_FORMAT } from '../../constants';
import _ from 'lodash';

const FilterLoader = () => {
  return (
    <div className="admin-filter__loader">
      <LoadingIndicator color="grey" />
    </div>

  );
};

const AdminFilter = ({
  className,
  title,
  onChange,
  filterValues,
  fields,
  renderAsCard,
  onClearClick,
  retainDefaultValue,
  defaultValue,
  includeDateTime,
  startDateLowerLimit,
  startDateUpperLimit,
  endDateLowerLimit,
  endDateUpperLimit,
  includeCampaignIdFilter,
}) => {
  const startDatePickerRef = useRef();
  const endDatePickerRef = useRef();

  const updateFilterValues = (filterVal) => {
    if (onChange) {
      onChange({ ...filterVal, pageNumber: 1 });
    }
  };

  const onFilterChange = forField => e => {
    const { value } = e.target;
    const filters = { ...filterValues };
    if (value === defaultValue) {
      if (!retainDefaultValue) {
        delete filters[forField];
      } else {
        filters[forField] = defaultValue;
      }
      updateFilterValues(filters);
    } else {
      updateFilterValues({
        ...filters,
        [forField]: value,
      });
    }
  };

  const renderedFilters = fields.map(({ label, key, options, defaultOptionLabel, isLoading }) => {
    const filterKey = key || createIdFromName(label.toLowerCase());
    return (
      <div
        key={filterKey}
        className="admin-filter__item">
        <Filter
          name={`admin-filter-${label}`}
          id={`admin-filter-${key}`}
          label={label}
          isLabelVisible
          onChange={onFilterChange(filterKey)}
          value={filterValues[filterKey] || defaultValue}
          tooltip={isLoading ? <FilterLoader /> : false}
        >
          <option key={`${filterKey}-all`} value={defaultValue}>{ defaultOptionLabel || 'All' }</option>
          { options.map(({ value, label: optionLabel, disabled }) => (
            <option
              key={`${filterKey}-${value}`}
              value={value}
              disabled={disabled}
            >
              { optionLabel }
            </option>
          )) }
        </Filter>
      </div>
    );
  });

  const RootNodeComponent = renderAsCard ? Card : 'div';

  const onDateTimeChange = (value, forField) => {
    if (!moment(value, SHORT_MONTH_FORMAT, true).isValid()) return;
    const date = moment(value).toISOString();
    updateFilterValues({
      ...filterValues,
      [forField]: date,
    });
  };

  const renderDateTimeFilters = () => (
    <>
      <DatePicker
        position="top"
        className="admin-filter__item"
        id="datepicker-start-date"
        locale="en-CA"
        inputDateFormat={SHORT_MONTH_FORMAT}
        overrides={{ 'en-CA': { startDateLabel: 'Start Date' } }}
        onChange={value => onDateTimeChange(value, 'start_date_gt')}
        selected={filterValues.start_date_gt ? new Date(filterValues.start_date_gt) : null}
        onDatePickerClear={() => onDateTimeChange(undefined, 'start_date_gt')}
        allowedRange={[ startDateLowerLimit, startDateUpperLimit ]}
        datePickerComponentRef={startDatePickerRef}
      />
      <DatePicker
        className="admin-filter__item"
        id="datepicker-end-date"
        locale="en-CA"
        inputDateFormat={SHORT_MONTH_FORMAT}
        overrides={{ 'en-CA': { startDateLabel: 'End Date' } }}
        errorStrings={{
          label: 'error',
          start: {
            range: 'The end date selection is outside of the allowed range',
            disallowedDate: 'The end date selection is invalid',
            invalidRange: '',
          },
          end: {
            range: '',
            disallowedDate: '',
            invalidRange: '',
          },
        }}
        onChange={value => onDateTimeChange(value, 'end_date_lt')}
        onDatePickerClear={() => onDateTimeChange(undefined, 'end_date_lt')}
        allowedRange={[ endDateLowerLimit, endDateUpperLimit ]}
        datePickerComponentRef={endDatePickerRef}
      />
    </>
  );

  const renderCampaignIdFilter = () => (
    <>
    <div
      key={'campaign_id'}
      className="admin-filter__item">
        <TextField
          name='admin-filter-CampaignID'
          id='admin-filter-campaign_id'
          label='Campaign ID'
          placeholder="Enter Campaign ID"
          onChange={onFilterChange('campaign_id')}
          value={filterValues['campaign_id'] || defaultValue}
        />
    </div>
    </>
  );

  const renderClearBtn = () => {
    // Only display if filters are selected
    const filters = _.omit({ ...filterValues }, [ 'limit', 'sort', 'pageNumber' ]);
    if (Object.values(filters).length === 0 || Object.values(filters).every(filter => !filter)) {
      return null;
    }

    return (
      <div className="admin-filter__item">
        <TextButton
          className="admin-filter__item-clear"
          Icon={IconCloseCircle}
          onClick={() => {
            if (onClearClick) {
              if (includeDateTime) {
                startDatePickerRef.current.clearDateField();
                endDatePickerRef.current.clearDateField();
              }
              onClearClick();
            } else {
              updateFilterValues({});
            }
          }}
        >
          Clear all
        </TextButton>
      </div>
    );
  };

  return (
    <RootNodeComponent className={classnames('admin-filter', className)}>
      <TextSubtitle component="legend">{ title }</TextSubtitle>
      <div className="admin-filter__filter-container">
        { renderedFilters }
        { includeCampaignIdFilter && renderCampaignIdFilter() }
        { !includeDateTime && renderClearBtn() }
      </div>
      { includeDateTime &&
        <div className="admin-filter__filter-container">
          { renderDateTimeFilters() }
          { renderClearBtn() }
        </div>
      }
    </RootNodeComponent>
  );
};

AdminFilter.propTypes = {
  className: PropTypes.string,
  title: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  renderAsCard: PropTypes.bool,
  filterValues: PropTypes.object.isRequired,
  // the various filter fields
  fields: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string,
      label: PropTypes.string.isRequired,
      options: PropTypes.arrayOf(
        PropTypes.shape({
          label: PropTypes.string.isRequired,
          value: PropTypes.oneOfType([ PropTypes.string, PropTypes.number, PropTypes.bool ]).isRequired,
        })
      ),
    })
  ),
  // callback fired when Clear is clicked
  onClearClick: PropTypes.func,
  // when an item is reset, should we fire `onChange` with the defaultValue or should we remove the property entirely from the submitted object
  retainDefaultValue: PropTypes.bool.isRequired,
  // the default value of a dropdown when it is reset
  defaultValue: PropTypes.string.isRequired,
  includeDateTime: PropTypes.bool.isRequired,
  startDateLowerLimit: PropTypes.instanceOf(Date),
  startDateUpperLimit: PropTypes.instanceOf(Date),
  endDateLowerLimit: PropTypes.instanceOf(Date),
  endDateUpperLimit: PropTypes.instanceOf(Date),
  includeCampaignIdFilter: PropTypes.bool.isRequired,
};

AdminFilter.defaultProps = {
  title: 'Filters',
  renderAsCard: true,
  filterValues: {},
  retainDefaultValue: false,
  defaultValue: 'all',
  includeDateTime: false,
  includeCampaignIdFilter: false,
};

export default AdminFilter;
