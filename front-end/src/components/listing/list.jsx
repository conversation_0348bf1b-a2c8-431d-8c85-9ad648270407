import React, { useEffect, forwardRef } from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { useHistory } from 'react-router-dom';
import Table from 'canvas-core-react/lib/Table';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import TextCaption from 'canvas-core-react/lib/TextCaption';

import { formatWord, capitalize } from '../../utils';
import { setBrowserTitle } from '../../constants';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';

const List = forwardRef(({
  className,
  columns,
  data,
  entityName,
  isLoading,
  children,
  permissions,
  columnFixed = true,
  isMessageCentreMessagesInitialLoad,
  resetSortOnDataChange = true,
}, ref) => {
  const history = useHistory();
  const entityTitle = formatWord(entityName, { capitalize: true, plural: true });
  const canCreate = permissions && (
    permissions['admin'] ||
    (permissions[`${entityName}s_manage`] && entityName !== 'team') ||
    permissions[`${entityName}s_manage_super`]
  );

  useEffect(() => {
    setBrowserTitle(`${entityTitle}`);
  }, []);

  const gotoEntityCreationPage = () => {
    history.push(`/${formatWord(entityName, { plural: true })}/create`);
  };

  const renderListBody = () => {
    return (
      <>
        <Table
          className="admin-list__table"
          id="admin-list-table"
          title=""
          columnFixed={columnFixed}
          columns={columns}
          data={data}
          resetSortOnDataChange={resetSortOnDataChange}
          defaultSortOrder='asc'
        />
        { (!isLoading && !data.length && !isMessageCentreMessagesInitialLoad) && <TextCaption component="p"> No results available</TextCaption> }
      </>
    );
  };

  return (
    <div className={classnames('admin-list', className)} ref={ref}>
      <div className="admin-list__action-bar">
        <TextHeadline
          className="admin-list__header"
          component="h1"
        >
          { entityTitle } { isLoading && <IconSpinner size={24} /> }
        </TextHeadline>
        { canCreate &&
          <SecondaryButton
            type="caution"
            onClick={gotoEntityCreationPage}
          >
            Create { capitalize(entityName) }
          </SecondaryButton>
        }
      </div>
      { children }
      { renderListBody() }
    </div>
  );
});

List.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]),
  className: PropTypes.string,
  columns: PropTypes.array.isRequired,
  data: PropTypes.array,
  entityName: PropTypes.string.isRequired,
  isLoading: PropTypes.bool,
  permissions: PropTypes.object,
  columnFixed: PropTypes.bool,
  isMessageCentreMessagesInitialLoad: PropTypes.bool,
  resetSortOnDataChange: PropTypes.bool,
};

List.defaultProps = {
  data: [],
};

export default List;
