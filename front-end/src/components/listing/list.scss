.admin-list {

  // TODO: remove when migrated to headerFormatter with tooltip (Canvas 9.3.0+)
  .TextCaption__text.TableHead__tooltip {
    display: block;
    max-width: 40rem;
  }

  &--extended {
    min-height: 99rem;
  }

  &__header {
    @extend .admin-details__header;
  }

  &__sub-header {
    @extend .admin-details__sub-header;
  }

  &__filter-accordion {
    display: flex;
    justify-content: space-between;
  }

  &__msg-centre-filter-accordion {
    display: flex;
    justify-content: space-between;
  }

  &__msg-centre-search {
    display: flex;
    gap: 1rem;
  }

  &__msg-centre-search-input {
    margin-top: 1.5rem;
    width: 40rem;

    .Search__input {
      margin-top: 0;
    }
  }

  &__search {
    margin-top: 1.4rem;
    width: 44.5rem;

    .Search__input {
      margin-top: 0;
    }

    .Search__button--search {
      pointer-events: none;
    }
  }

  &__action-button-container {
    margin-top: 1.4rem;
    display: flex;
  }

  &__accordion-button {
    width: 4.8rem;
    height: 4.8rem;
    border: none;
    border-radius: 0.5rem;
  }

  &__filter-options {
    margin-top: 2.4rem;
    margin-bottom: 2.4rem;
    background: $canvas-gray-100;
    padding: 1.8rem 2.4rem 0.4rem 2.4rem;
  }

  &__title-section {
    display: flex;
    justify-content: space-between;
    margin: 3.6rem 0;
  }

  &__toggle-switch {
    .Label {
      padding-right: 0;
    }

    .ToggleSwitch__label {
      padding: 0;
    }
  }

  &__action-bar {
    @extend .admin-details__action-bar;

    margin-bottom: 3.6rem;
  }

  &__heading {
    font-size: 3.6rem;
  }

  &__table {
    .TableHead {
      padding-top: 0;
    }

    text-align: left;
    overflow-wrap: anywhere;
  }

  &__listings-table {
    @extend .admin-list__table;

    margin-top: 2.4rem;

    .TableBody__cell p:not(:last-child) {
      margin-bottom: 0.5rem;
    }
  }

  .DesktopTooltip__container {
    transform: translateX(15rem) !important;

    @include mq($from: desktop) {
      transform: translateX(18rem) !important;
    }
  }

  #table-admin-campaign-list-table,
  #table-admin-list-table {
    overflow-x: visible;
  }

  &__name-container {
    display: flex;
    align-items: center;
    width: 100%;
  }

  &__name-link {
    overflow: hidden;
    text-overflow: ellipsis;
    width: max-content;

    &.TextButton__button {
      padding: 0;
      margin: 0;
      height: fit-content;
      display: block;
      text-align: left;

      & > span {
        vertical-align: top;
      }

      &:focus {
        outline: 0.2rem solid $canvas-dark-blue;
        outline-offset: 0.2rem;

        &::after {
          opacity: 0;
        }
      }
    }

    &--disabled {
      &.Link__link {
        color: $canvas-gray-600;

        .Link__text {
          border-color: $canvas-gray-600;
        }

        &:hover {
          color: $canvas-gray-600;

          .Link__text {
            border-color: $canvas-gray-600;
          }
        }
      }
    }
  }

  &__cell-text {
    &--disabled {
      color: $canvas-gray-600;
    }
  }

  .Tooltip__container {
    margin-left: 0;
  }

  &__tooltip-text {
    text-align: left;
  }

  &__list-cell {
    max-width: 100%;
  }

  &__status-badge {
    max-height: 2.5rem;
  }

  &__pagination {
    margin-top: 2.4rem;
  }

  &__icon-duplicate {
    margin-left: -0.2rem;
    margin-right: 1.2rem;
  }

  .BottomSheet__ModalCore {
    .InternalOverlay {
      opacity: 0 !important;
    }
  }

  .BottomSheet__container {
    margin-top: 0;
  }

  .BottomSheet__heading {
    display: none;
  }

  &__panel-offset {
    .BottomSheet__card {
      border-top: none;
    }
  }

  &__panel-section {
    display: flex;
    justify-content: space-between;
  }

  &__panel-badge {
    margin-top: auto;
    margin-bottom: auto;
  }

  &__panel-heading {
    margin-top: 3.8rem;
    margin-bottom: 1.2rem;
  }

  &__panel-name {
    word-break: break-word;
  }

  &__panel-icons {
    margin: 3.6rem 0;
    text-align: center;
    justify-content: space-between;
  }

  &__panel-icons-with-gap {
    margin: 4rem 0;
    text-align: center;
    justify-content: flex-start;
    gap: 3rem;
  }

  &__panel-icon-container {
    .TextLegal__text {
      margin-top: 0.7rem;
      color: $canvas-dark-blue;
    }
  }

  &__panel-icon-container--disabled {
    .TextLegal__text {
      color: $canvas-gray-600;
    }
  }

  &__panel-icon {
    width: fit-content;
    background: $brand-white;
    border: 0.2rem solid $canvas-gray-500;
    border-radius: 50%;
    padding: 1.5rem;
    cursor: pointer;

    svg {
      display: block;
    }
  }

  &__panel-icon--disabled {
    cursor: not-allowed;
  }

  &__panel-list-item {
    padding-top: 1.4rem;
    padding-bottom: 1.4rem;
    border-top: 0.2rem solid $canvas-gray-400;
  }

  &__panel-content-name {
    max-width: 20rem;
    text-align: right;
    word-break: break-word;
  }

  &__panel-content-id {
    display: flex;
    flex-direction: column;
    text-align: right;
  }

  &__panel-date {
    max-width: 9rem;
    text-align: right;
  }

  &__panel-platform {
    text-align: right;
  }

  &__sidepanel {
    width: 38rem !important;
    padding: 2rem !important;

    &__container {
      margin: 1rem;
    }
  }
}

.application-list {
  .TextCaption__text.TableHead__tooltip {
    top: -7rem;
    left: -5rem;
  }
}

.container-list {
  .TextCaption__text.TableHead__tooltip {
    top: -12rem;
    left: -2rem;
  }
}

.page-list {
  .TextCaption__text.TableHead__tooltip {
    top: -10rem;
    left: -3rem;
  }
}

.teams-list {
  .TextCaption__text.TableHead__tooltip {
    top: 1.5rem;
    left: -7.5rem;
  }
}
