.admin-filter {
  &__loader {
    display: inline;
    margin-left: 0.8rem;

    .SvgIcon__icon {
      height: 1.4rem;
      width: 1.4rem;
    }
  }

  margin-bottom: 3.6rem;

  &.Card__container {
    background-color: $canvas-gray-100;
    border: $canvas-gray-100;
  }

  &__header-container {
    display: flex;
    justify-content: space-between;
  }

  &__header-title {
    padding-top: 0.8rem;
  }

  &__filter-container {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-items: flex-end;

    @include mq($from: tablet) {
      flex-direction: row;
    }
  }

  &__item {
    margin-top: 2.4rem;

    @include mq($from: tablet) {
      margin-right: 1.6rem;
    }

    .Filter__wrap {
      @include mq($from: tablet) {
        min-width: 10rem;
      }

      @include mq($from: desktop) {
        min-width: 15rem;
      }
    }

    .Filter__labelWrap { // canvas component label whitespace fix
      white-space: nowrap;
    }
  }

  &__item-clear {
    @include mq($from: tablet) {
      margin-top: 1.6rem;
    }
  }

  &__clear-button {
    @include mq($from: tablet) {
      align-self: flex-end;
    }
  }

  #datepicker-start-date .Input__input,
  #datepicker-end-date .Input__input {
    pointer-events: none;
  }
}
