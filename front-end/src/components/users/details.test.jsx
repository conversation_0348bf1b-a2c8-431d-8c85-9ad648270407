import React from 'react';
import { fireEvent, within } from '@testing-library/react';
import mockAxios from 'axios';
import { act } from 'react-dom/test-utils';
import qs from 'qs';

import { mapPropToKey, mapArrayToTrueProps } from '../../constants';
import { UserDetails } from './details';
import { renderPage, defaultData } from '../../utils/testing-library-utils';
import { addSnackbar } from '../../store/actions/snackbar';
import allPermissions, { mapPermissionsToPermissionLevels } from '../../constants/permissionsList';

const { users, teams, roles } = defaultData;
const user = users[0];
const current = { data: {
  id: 1,
  role_id: 1,
  permissionLevels: mapPermissionsToPermissionLevels(mapArrayToTrueProps(Object.values(allPermissions))),
  sid: 's999999',
  team_id: 1,
} };

describe('UserDetails', () => {
  mockAxios.get.mockImplementation((url) => {
    if (url.includes(`/users/${user.id}`)) {
      return Promise.resolve({ data: user });
    } else if (url.includes(`/users?active=true&teamId=${user.team_id}`)) {
      return Promise.resolve({ data: { items: [ user ], offset: null, total: 1 } });
    } else if (url.includes(`/users/current`)) {
      return Promise.resolve(current);
    } else if (url.includes(`/users/network-error`)) {
      throw new Error('Network Error');
    } else if (url.includes('/teams')) {
      return Promise.resolve({ data: teams });
    } else if (url.includes(`/roles?team_id=${user.team_id}`)) {
      return Promise.resolve({ data: roles.filter(r => r.team_id === user.team_id) });
    } else if (url === '/roles') {
      return Promise.resolve({ data: roles });
    } else if (url === `/validate?${qs.stringify({ table: 'user', column: 'sid', value: user.sid })}`) {
      return Promise.resolve(false);
    } else if (url === `/validate?${qs.stringify({ table: 'user', column: 'email', value: user.email })}`) {
      return Promise.resolve(false);
    }
  });

  afterAll(() => {
    mockAxios.reset();
  });

  it('update journey', async() => {
    const {
      getByRole, getByLabelText, getAllByTestId, history, store,
    } = await renderPage(<UserDetails match={{ params: { id: user.id } }} />);

    // warning for submit without change
    await act(async() => fireEvent.click(getByRole('button', { name: 'Update User' })));
    const expectedSnackbar = addSnackbar({ message: 'No change to be saved' });
    expect(store.getActions()).toContainEqual(expectedSnackbar);

    // make changes
    await act(async() => { fireEvent.change(getByLabelText('Assign Team'), { target: { value: teams[1].id } }); });
    const targetOption = getAllByTestId('team_id-option').find(o => Number(o.getAttribute('value')) === teams[1].id);
    expect(targetOption.selected).toBeTruthy();
    expect(getByLabelText('Team owner').checked).toBeFalsy();
    fireEvent.click(getByLabelText('Team owner'));
    expect(getByLabelText('Team owner').checked).toBeTruthy();

    // submit changes
    expect(history.location.pathname).toStrictEqual('/');
    await act(async() => fireEvent.click(getByRole('button', { name: 'Update User' })));
    expect(history.location.pathname).toBe('/users');
  });

  it('cancel journey', async() => {
    const { getByText, getByRole, getByLabelText, history } = await renderPage(<UserDetails/>);

    // cancel directly
    fireEvent.click(getByRole('button', { name: 'Cancel' }));
    expect(history.location.pathname).toStrictEqual('/users');
    history.goBack();
    expect(history.location.pathname).toStrictEqual('/');

    // modify form data to trigger warning modal on cancel
    await act(async() => { fireEvent.change(getByLabelText('Name'), { target: { value: 'Alexa' } }); });
    fireEvent.click(getByRole('button', { name: 'Cancel' }));

    expect(getByText('Are you sure?')).toBeInTheDocument();
    expect(getByText('If you cancel, any unsaved changes will be lost.')).toBeInTheDocument();
    fireEvent.click(getByRole('button', { name: 'Yes' }));
    expect(history.location.pathname).toStrictEqual('/users');
  });

  it('deactivate journey', async() => {
    mockAxios.patch.mockImplementationOnce(() => Promise.resolve({}));
    const {
      getByRole, getAllByRole, getByLabelText, getByText, queryByText, history,
    } = await renderPage(<UserDetails match={{ params: { id: user.id } }} />);

    // warning for submit without change
    fireEvent.click(getByLabelText('Active'));
    await act(async() => fireEvent.click(getByRole('button', { name: 'Update User' })));
    expect(getByText('Are you sure you want to deactivate this user?')).toBeInTheDocument();
    expect(getByText('User will no longer have access to the Pigeon platform.')).toBeInTheDocument();

    // test cancel button on warning modal
    const modalCancelBtn = within(getAllByRole('dialog')[1]).getByRole('button', { name: 'Cancel' });
    fireEvent.click(modalCancelBtn);
    expect(queryByText('Are you sure you want to deactivate this user?')).not.toBeInTheDocument();
    await act(async() => fireEvent.click(getByRole('button', { name: 'Update User' })));
    expect(getByText('Are you sure you want to deactivate this user?')).toBeInTheDocument();

    // deactivate user
    await act(async() => fireEvent.click(getByRole('button', { name: 'Deactivate User' })));
    const deactivatedUser = { ...user, active: false, id: undefined };
    expect(mockAxios.patch).toHaveBeenCalledWith(`/users/${user.id}`, deactivatedUser, undefined);
    expect(history.location.pathname).toBe('/users');
    expect(queryByText('Are you sure you want to deactivate this user?')).not.toBeInTheDocument();
  });

  it('should allow dismissal of cancel modal by clicking no button', async() => {
    const { getByRole, getByText, queryByText, getByLabelText } = await renderPage(<UserDetails/>);

    // modify form data to trigger warning modal on cancel
    const nameField = getByLabelText('Name'); // await findBy... doesn't seem to wait until element appears as documented
    expect(nameField).toBeInTheDocument();
    await act(async() => { fireEvent.change(nameField, { target: { value: 'Alexa' } }); });
    fireEvent.click(getByRole('button', { name: 'Cancel' }));

    expect(getByText('If you cancel, any unsaved changes will be lost.')).toBeInTheDocument();
    fireEvent.click(getByRole('button', { name: 'No' }));
    expect(queryByText('If you cancel, any unsaved changes will be lost.')).not.toBeInTheDocument();
  });

  it('should allow dismissal of cancel modal by clicking modal backdrop', async() => {
    const { getByRole, queryByText, getByLabelText } = await renderPage(<UserDetails/>);

    // modify form data to trigger warning modal on cancel
    await act(async() => { fireEvent.change(getByLabelText('Name'), { target: { value: 'Alexa' } }); });
    fireEvent.click(getByRole('button', { name: 'Cancel' }));

    expect(queryByText('If you cancel, any unsaved changes will be lost.')).toBeInTheDocument();
    fireEvent.click(getByRole('button', { name: 'No' }).closest('.Modal').querySelector('.InternalOverlay'));
    expect(queryByText('If you cancel, any unsaved changes will be lost.')).not.toBeInTheDocument();
  });

  it('should render spinner if loading', async() => {
    const { queryByText } = await renderPage(
      <UserDetails/>,
      { initialState: { teams: { isLoading: true }, roles: { isLoading: true }, users: { isLoading: true } } },
    );
    expect(queryByText('Create User')).not.toBeInTheDocument();
  });

  it('should show alert banner if error occurs while loading user', async() => {
    const { store } = await renderPage(<UserDetails match={{ params: { id: 'network-error' } }}/>);

    expect(store.getActions().find(e => e.data.message === 'An error occurred loading this user.')).toBeTruthy();
  });

  it('should disable teams and roles fields if sole or sole-active owner', async() => {
    // test user 1 belongs to team 1 and is sole owner there
    const mockTeams = [ { ...teams[0], owners: [ user ] } ];
    const mockRoles = roles.filter(r => r.team_id === user.team_id);
    const { getByText, getByLabelText, queryByText } = await renderPage(
      <UserDetails match={{ params: { id: user.id } }} />,
      { initialState: {
        teams: { isLoading: false, items: mapPropToKey(mockTeams, 'id') },
        roles: { isLoading: false, items: mapPropToKey(mockRoles, 'id') },
        authenticated: current.data,
      },
    });
    const statusToggle = getByLabelText('Active');
    expect(statusToggle).toBeDisabled();
    expect(getByLabelText('Assign Team')).toBeDisabled();
    expect(getByLabelText('Team owner')).toBeDisabled();
    expect(getByLabelText('Viewer')).toBeDisabled();

    const toggleField = statusToggle.closest('.toggle-field');
    const tooltip = within(toggleField).getByRole('button');
    expect(queryByText('Cannot deactivate')).not.toBeInTheDocument();
    fireEvent.click(tooltip);
    expect(getByText('Cannot deactivate')).toBeInTheDocument();
    expect(getByText('Team must have at least one active team owner.')).toBeInTheDocument();
  });
});
