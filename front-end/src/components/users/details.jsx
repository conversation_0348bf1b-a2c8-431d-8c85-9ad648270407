import React, { useState, useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import PropTypes from 'prop-types';

import Card from 'canvas-core-react/lib/Card';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';

import { addSnackbar } from '../../store/actions/snackbar';
import { saveUserValues } from '../../store/actions/users';
import { InputToggleField, InputTextField, InputSelectField, InputCheckboxGroupField } from '../formFields/reactHookForm';
import { email, sid, teamRequired, roleRequired, usernameRequired, requiredSpecific, ensureUnique } from '../../utils/validation';
import { sanitizeRoleName } from '../../constants';
import useUser from '../../hooks/useUser';
import { useDispatch } from 'react-redux';

const deactiveTooltip = {
  heading: 'Cannot deactivate',
  body: 'Team must have at least one active team owner.',
};
const reactivateTooltip = {
  heading: 'Cannot reactivate',
  body: 'The team that this user belongs to must also be reactivated before this user can be reactivated.',
};
const userRolesTooltip = {
  heading: 'Roles',
  text: 'Each role has a set of permissions assigned to it. Assigning a user to a role will give the user access to its permissions.\n \nBy default, all users will have the “Viewer” role which gives read-only access to all capabilities of the team.\n \nThere must also be at least one user with the team owner role on each team.',
};

export const UserDetails = ({ match }) => {
  const history = useHistory();
  const dispatch = useDispatch();
  const userId = match?.params?.id;

  const { handleSubmit, control, getValues, reset, setValue, formState: { isDirty, dirtyFields }, watch } = useForm({
    mode: 'onChange',
    defaultValues: { active: true },
  });

  const { isLoading, isSoleTeamOwner, isFormDisabled, isOnlyActiveTeamOwner, teams, roles, disabledRoleIds, currentUser,
  } = useUser({ id: userId, reset, watch, getValues, setValue });

  const [ isDeactivationModalOpen, setIsDeactivationModalOpen ] = useState(false);
  const [ isReactivationModalOpen, setIsReactivationModalOpen ] = useState(false);
  const [ isCancelPopupOpen, setIsCancelPopupOpen ] = useState(false);

  const isTeamDisabled = useMemo(() => {
    if (isLoading) return false;
    const team = Object.values(teams).find(team => team.id === Number(getValues('team_id')));
    return team && !team.active;
  }, [ isLoading, teams, watch('team_id') ]);

  const showStatusTooltip = () => {
    if (isTeamDisabled) return reactivateTooltip;
    if (isOnlyActiveTeamOwner) return deactiveTooltip;
    return null;
  };

  const savingUserValues = async(values) => {
    const payload = {
      name: values.name && values.name.trim(),
      sid: values.sid && values.sid.trim(),
      email: values.email && values.email.trim(),
      roles: values.roles,
      team_id: parseInt(values.team_id),
      active: !!values.active,
    };
    await saveUserValues(userId, payload)(dispatch);
    history.push('/users');
  };

  const onSubmit = async(values) => {
    if (!isDirty) {
      return dispatch(addSnackbar({ message: 'No change to be saved' }));
    }

    // If user has been deactivated manually (i.e. not as a result of assigning to a deactivated team) show deactivation confirmation modal
    const team = Object.values(teams).find(team => team.id === Number(getValues('team_id')));
    // if status is changed to inactive when the team is active or when a team is switched to an inactive team confirmation modal is displayed
    // Deactivation Modal is only displayed while editing a user
    if (userId !== undefined && ((!values.active && dirtyFields.active && team.active) || (dirtyFields.team_id && !team.active))) {
      setIsDeactivationModalOpen(true);
      return;
    }
    if (dirtyFields.active && values.active && userId !== undefined) return setIsReactivationModalOpen(true);
    const sanitizedRoles = values.roles.filter(newRoleId => Object.values(roles).some(r => r.id === newRoleId));
    await savingUserValues({ ...values, roles: sanitizedRoles });
  };

  if (isLoading) {
    return <IconSpinner size={32} />;
  }

  return (
    <div className="admin-details">
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="admin-details__action-bar">
          <TextHeadline className="admin-details__header" component="h1">{ userId ? 'Edit User' : 'Create User' }</TextHeadline>
        </div>
        <Card className="admin-details__card">
          <TextHeadline className="admin-details__sub-header" component="h2" size={21}>
            User Details
          </TextHeadline>

          <InputToggleField
            control={control}
            className="team-details__field"
            disabled={isFormDisabled || isOnlyActiveTeamOwner || isTeamDisabled}
            label='active'
            secondaryLabel={`${watch('active') ? 'Active' : 'Inactive'}`}
            tooltip={showStatusTooltip()}
          />

          <InputTextField
            control={control}
            disabled={isFormDisabled}
            label='Name'
            placeholder='Enter full name'
            rules={{ validate: { usernameRequired } }}
          />

          <InputTextField
            control={control}
            disabled={isFormDisabled}
            label='sID'
            placeholder='Enter sID'
            rules={{ validate: {
              requiredSpecific: requiredSpecific('sID'),
              sid,
              ensureUnique: ensureUnique('userSid', 'sid', dirtyFields),
             } }}
          />

          <InputTextField
            control={control}
            disabled={isFormDisabled}
            label='Email'
            placeholder='Enter email'
            rules={{ validate: {
              requiredSpecific: requiredSpecific('Email'),
              email,
              ensureUnique: ensureUnique('userEmail', 'email', dirtyFields),
            } }}
          />
        </Card>

        <Card>
          <TextHeadline
            component="h2"
            size={18}
            className="admin-details__sub-header"
          >
            Role
          </TextHeadline>

          { currentUser?.permissionLevels?.canEditAllUsers &&
            <InputSelectField
              className="team-details__field"
              control={control}
              disabled={isFormDisabled || isOnlyActiveTeamOwner || isSoleTeamOwner}
              name="team_id"
              label="Assign Team"
              placeholder="Select a team"
              rules={{ validate: { teamRequired } }}
              options={Object.values(teams || {}).map(({ id, name, active }) => active ? ({ id, name }) : ({ id, name: `${name} (Inactive)` }))}
            />
          }

          { getValues('team_id') && Object.values(roles || {}).length > 0 &&
            <InputCheckboxGroupField
              className="team-details__field"
              control={control}
              disabled={isFormDisabled}
              disabledIds={disabledRoleIds}
              label="Assign Roles"
              name="roles"
              options={Object.values(roles || {}).map(({ id, name }) => ({ id, name: sanitizeRoleName(name) }))}
              inline={false}
              rules={{ validate: { roleRequired } }}
              tooltip={userRolesTooltip}
            />
          }
        </Card>

        <div className="admin-details__action-right-bar">
          <div className="admin-details__action-buttons">
            <SecondaryButton
              type="button"
              className="admin-details__action-button"
              onClick={() => isDirty ? setIsCancelPopupOpen(true) : history.push('/users')}
            >
              Cancel
            </SecondaryButton>
            <PrimaryButton type="submit" className="admin-details__action-button" disabled={isFormDisabled}>
              { userId ? 'Update User' : 'Create User' }
            </PrimaryButton>
          </div>
        </div>
      </form>
      <ModalDialogue
        isModalVisible={isCancelPopupOpen}
        headline="Are you sure?"
        primaryButtonLabel="Yes"
        primaryAction={() => history.push('/users')}
        secondaryButtonLabel="No"
        secondaryAction={() => setIsCancelPopupOpen(false)}
        setModalVisible={() => setIsCancelPopupOpen(false)}
      >
        If you cancel, any unsaved changes will be lost.
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={isDeactivationModalOpen}
        headline="Are you sure you want to deactivate this user?"
        primaryButtonLabel="Deactivate User"
        primaryAction={() => {
          savingUserValues(watch());
          setIsDeactivationModalOpen(false);
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setIsDeactivationModalOpen(false)}
        setModalVisible={() => setIsDeactivationModalOpen(false)}
      >
        <p>User will no longer have access to the Pigeon platform.</p>
      </ModalDialogue>
        <ModalDialogue
        isModalVisible={isReactivationModalOpen}
        headline="Are you sure you want to reactivate this user?"
        primaryButtonLabel="Reactivate User"
        primaryAction={() => {
          savingUserValues(watch());
          setIsReactivationModalOpen(false);
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setIsReactivationModalOpen(false)}
        setModalVisible={() => setIsReactivationModalOpen(false)}
      >
        <p>User will have access to the Pigeon platform again.</p>
      </ModalDialogue>
    </div>
  );
};

UserDetails.propTypes = {
  match: PropTypes.object,
};

export default UserDetails;
