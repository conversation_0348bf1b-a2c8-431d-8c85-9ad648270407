import { tableColumns } from './usersColumns';
import { shallow } from 'enzyme';
import { defaultData } from '../../utils/testing-library-utils';

const user = {
  id: 1,
  teamName: defaultData.teams[0].name,
  teamActive: defaultData.teams[0].active,
  roleNames: [ 'Workflow Manager', 'Campaign Manager', 'Notification Manager', 'Viewer' ],
  name: 'user',
};
const colsTemplate = {
  handleChangeUserStatus: jest.fn(() => {}),
  canViewAllUsers: true,
  sortableColumnProperties: jest.fn(() => ({ overrideSortBehaviour: jest.fn(), sortable: true })),
};

describe('UsersColumns', () => {
  it('should retun 5 columns ', () => {
    const columns = tableColumns(colsTemplate);
    expect(columns).toHaveLength(5);
  });

  it('should retun 4 columns ', () => {
    const columns = tableColumns({ ...colsTemplate, canViewAllUsers: false });
    expect(columns).toHaveLength(4);
  });

  it('name col', () => {
    const columns = tableColumns({ ...colsTemplate, canEditOwnTeamUsers: false, canEditAllUsers: false });
    const nameCol = columns[0];
    const wrapper = shallow(
      nameCol.cellFormatter(user)
    );
    expect(wrapper).toMatchSnapshot();
    expect(nameCol.name).toStrictEqual('User');
    expect(nameCol.sortable).toStrictEqual(true);
    expect(nameCol.selector).toStrictEqual('name');
    expect(typeof nameCol.overrideSortBehaviour).toStrictEqual('function');
    expect(wrapper).toMatchSnapshot();
  });

  it('name sID col', () => {
    const columns = tableColumns(colsTemplate);
    const sIdCol = columns[1];
    expect(sIdCol.name).toStrictEqual('sID');
    expect(sIdCol.selector).toStrictEqual('sid');
  });

  it('team col', () => {
    const columns = tableColumns(colsTemplate);
    const teamCol = columns[2];
    const wrapper = shallow(
      teamCol.cellFormatter(user)
    );
    expect(wrapper).toMatchSnapshot();
    expect(teamCol.name).toStrictEqual('Team');
    expect(teamCol.sortable).toStrictEqual(true);
    expect(teamCol.selector).toStrictEqual('team');
    expect(typeof teamCol.overrideSortBehaviour).toStrictEqual('function');
    expect(wrapper).toMatchSnapshot();
  });
});
