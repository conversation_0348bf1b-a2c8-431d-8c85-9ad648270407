import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { omit, difference } from 'lodash';
import qs from 'qs';

import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';
import ModalAlert from 'canvas-core-react/lib/ModalAlert';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';

import PageInfo from '../pageInfo';
import PageActions from '../pageActions';
import AdminFilter from '../listing/AdminFilter';
import { useBrowserTitle } from '../../hooks/useBrowserTitle';
import { debounce, removeFalsyKeys } from '../../utils';
import { sanitizeRoleName } from '../../constants';

import { useUsers } from '../../hooks/useUsers';
import { setUserActivation, getUsers } from '../../store/actions/users';
import { tableColumns } from './usersColumns';
import List from './List';

const PAGE_SIZE = 30;
const BASE_QUERY = {
  team_id: '',
  role_id: '',
  active: '',
  search: '',
  sort: '',
  limit: PAGE_SIZE,
  pageNumber: 1,
};

export const UserListContainer = () => {
  useBrowserTitle({ title: 'Users' });
  const history = useHistory();
  const dispatch = useDispatch();

  const queryParams = qs.parse(history.location.search, { ignoreQueryPrefix: true });
  const initialFilters = {
    ...BASE_QUERY,
    ...omit(queryParams, difference(Object.keys(queryParams), Object.keys(BASE_QUERY))),
  };
  const { users, roles, loading, teams, pagination, currentUser } = useUsers(removeFalsyKeys(initialFilters));
  const { canViewAllUsers, canEditOwnTeamUsers } = useMemo(() => currentUser.permissionLevels || {}, [ currentUser ]);
  const [ filters, setFilters ] = useState(canViewAllUsers ? initialFilters : { ...initialFilters, team_id: currentUser.team_id });

  const [ userToDeactivate, setUserToDeactivate ] = useState(null);
  const [ userToReactivate, setUserToReactivate ] = useState(null);
  const [ isCannotReactivateModalOpen, setIsCannotReactivateModalOpen ] = useState(false);

  useEffect(() => {
    const queryParams = removeFalsyKeys(omit(filters, [
      'limit',
      ...(!canViewAllUsers ? [ 'team_id' ] : []),
      ...(filters.pageNumber === 1 ? [ 'pageNumber' ] : []),
    ]));
    history.push({ search: qs.stringify(queryParams, { addQueryPrefix: true }) });
  }, [ filters ]);

  const fetchUsers = (newFilters) => {
    dispatch(getUsers(removeFalsyKeys(newFilters)));
  };

  const debounceFetchUsers = useCallback(debounce(fetchUsers, 500), []);

  const handleOnChangeSearch = (e) => {
    const search = e.target.value;
    debounceFetchUsers({ ...filters, search, pageNumber: 1 });
    setFilters((f) => ({ ...f, search, pageNumber: 1 }));
  };

  const filtersChanged = (newFilters) => {
    const teamFilterChanged = canViewAllUsers && filters.team_id && newFilters.team_id !== filters.team_id;
    const newQuery = {
      ...newFilters,
      ...(teamFilterChanged && { role_id: '' }),
      ...(!canViewAllUsers && {
        team_id: currentUser.team_id,
      }),
    };
    setFilters(newQuery);
    dispatch(getUsers(removeFalsyKeys(newQuery)));
  };

  const handleClearFilters = () => {
    const newQuery = {
      ...BASE_QUERY,
      ...(!canViewAllUsers && {
        team_id: currentUser.team_id,
      }),
    };
    setFilters(newQuery);
    dispatch(getUsers(removeFalsyKeys(newQuery)));
  };

  const initialSortDirection = columnKey => {
    const { sort } = filters;
    if (!filters.sort) {
      return 0;
    }
    // check if the sorted key has a - in front of it which would indicate it's in descending order, ascending order otherwise
    if (sort.includes(columnKey)) {
      return (sort.charAt(0) === '-') ? 1 : -1;
    }
    return 0;
  };

  const onColumnSort = (columnKey, currentFilters) =>
    (row, direction) => {
      filtersChanged({ ...currentFilters, pageNumber: 1, sort: `${direction > 0 ? '-' : ''}${columnKey}` });
    };

  const sortableColumnProperties = useCallback((columnKey, currentFilters) => ({
    sortable: true,
    overrideSortBehaviour: onColumnSort(columnKey, currentFilters),
    initialSortDirection: initialSortDirection(columnKey),
  }), []);

  const handleChangeUserStatus = (e, userId, isUsersTeamActive) => {
    const { checked } = e.currentTarget;
    if (!checked) {
      e.preventDefault();
      setUserToDeactivate(userId);
    } else {
      if (!isUsersTeamActive) {
        setIsCannotReactivateModalOpen(true);
      } else {
        setUserToReactivate(userId);
      }
    }
  };

  return (
    <>
      <PageInfo
        title="Users"
        buttonText="Create User"
        TitleComponent={TextHeadline}
        ButtonComponent={SecondaryButton}
        titleComponentPorps={{ component: 'h1' }}
        isLoading={loading}
        showActionButton={canEditOwnTeamUsers}
        onClick={() => history.push('/users/create')}
      />

      <PageActions
        onChange={handleOnChangeSearch}
        value={filters.search}
        filterButtonText="Filter"
      >
        <AdminFilter
          className="admin-list__filter-options"
          onChange={filtersChanged}
          onClearClick={handleClearFilters}
          filterValues={canViewAllUsers ? filters : omit(filters, [ 'team_id' ])}
          fields={[
            ...(canViewAllUsers
              ? [ {
                  label: 'Team',
                  key: 'team_id',
                  options: teams.map((team) => ({
                    label: team.name,
                    value: team.id,
                  })).sort((a, b) => a.label.localeCompare(b.label)),
                } ]
              : []),
            {
              label: 'Role',
              key: 'role_id',
              options: filters.team_id ? roles.filter(role => role.team_id === parseInt(filters.team_id)).map((role) => ({
                label: sanitizeRoleName(role.name),
                value: role.id,
              })).sort((a, b) => a.label.localeCompare(b.label)) : [],
            },
            {
              label: 'Status',
              key: 'active',
              options: [
                { label: 'Active', value: 'true' },
                { label: 'Inactive', value: 'false' },
              ],
            },
          ]}
          renderAsCard={false}
        />
      </PageActions>

      <List
        entityName="user"
        className="users-list"
        columns={tableColumns({
          isLoading: loading,
          handleChangeUserStatus,
          canViewAllUsers,
          history,
          filters,
          sortableColumnProperties,
        })}
        data={users}
        showMangePrefix={false}
      />

      { users?.length > 0 &&
        <DesktopPagination
          id="users-pagination"
          totalResultCount={pagination?.total || 0}
          onChange={(pageNumber) => {
            filtersChanged({ ...filters, pageNumber });
            window.scrollTo(0, 0);
          }}
          firstButtonLabel="First"
          prevButtonLabel="Previous"
          nextButtonLabel="Next"
          lastButtonLabel="Last"
          navigationLabel="Pagination Navigation"
          pageSize={pagination?.limit || 0}
          currentPage={users ? (pagination?.offset / pagination?.limit) + 1 : 1}
          containerType="card"
        />
      }

      { /* Modals */ }
      <ModalDialogue
        isModalVisible={userToDeactivate !== null}
        headline="Are you sure you want to deactivate this user?"
        primaryButtonLabel="Deactivate User"
        primaryAction={() => {
          dispatch(setUserActivation(userToDeactivate, false, filters));
          setUserToDeactivate(null);
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setUserToDeactivate(null)}
        setModalVisible={() => setUserToDeactivate(null)}
      >
        User will no longer have access to the Pigeon platform.
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={userToReactivate !== null}
        headline="Are you sure you want to reactivate this user?"
        primaryButtonLabel="Reactivate User"
        primaryAction={() => {
          dispatch(setUserActivation(userToReactivate, true, filters));
          setUserToReactivate(null);
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setUserToReactivate(null)}
        setModalVisible={() => setUserToReactivate(null)}
      >
        User will have access to the Pigeon platform again.
      </ModalDialogue>
      <ModalAlert
        isModalVisible={isCannotReactivateModalOpen}
        headline="User cannot be reactivated"
        primaryButtonLabel="Done"
        primaryAction={() => setIsCannotReactivateModalOpen(false)}
        setModalVisible={() => setIsCannotReactivateModalOpen(false)}
      >
        This user belongs to a team that has been deactivated. Team needs to be
        reactivated before the user can be reactivated.
      </ModalAlert>
    </>
  );
};

export default UserListContainer;
