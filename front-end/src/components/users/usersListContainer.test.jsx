import React from 'react';
import { fireEvent } from '@testing-library/react';
import { within } from '@testing-library/dom';
import mockAxios from 'axios';

import UsersListContainer from './usersListContainer';
import { defaultData, renderPage } from '../../utils/testing-library-utils';

describe('usersListContainer', () => {
  afterAll(() => {
    mockAxios.reset();
  });

  it('create journey', async() => {
    const { getByRole, history } = await renderPage(<UsersListContainer />);
    fireEvent.click(getByRole('button', { name: 'Create User' }));
    expect(history.location.pathname).toStrictEqual('/users/create');
  });

  it('deactivate journey', async() => {
    const { baseElement, getByText, getAllByText, getByRole, getByLabelText } = await renderPage(<UsersListContainer />);
    const user = defaultData.users[0];

    // search for target user to deactivate using the search bar and filters
    const filterBtn = getByText('Filter');
    fireEvent.click(filterBtn);
    expect(getByLabelText('Team')).toBeInTheDocument();
    fireEvent.change(getByLabelText('Team'), { target: { value: user.team_id } });
    fireEvent.change(getByLabelText('Role'), { target: { value: user.roles[0] } });
    fireEvent.change(getByLabelText('Status'), { target: { value: user.active } });
    expect(getAllByText(user.name).length).toStrictEqual(1);
    fireEvent.click(getByRole('button', { name: 'Clear all' }));
    fireEvent.click(getByRole('button', { name: 'Cancel' }));
    fireEvent.change(baseElement.querySelector('#page-action-search-input'), { target: { value: user.name } });

    // trigger deactivation warning
    const targetUser = getAllByText(user.name)[0];
    const statusToggle = within(targetUser.closest('tr')).getByTestId('status-toggle-1');
    expect(statusToggle.checked).toBeTruthy();
    fireEvent.click(statusToggle);

    // due to known bug in canvas prior to 7.1.0, every time modal dialogue opens,
    // we must wait for focus animation to complete before closing the modal
    // https://bitbucket.agile.bns/projects/CANVAS/repos/core-react-develop/pull-requests/779/overview

    expect(getByText('Are you sure you want to deactivate this user?')).toBeInTheDocument();
    fireEvent.click(getByRole('button', { name: 'Deactivate User' }));
    expect(mockAxios.post).toHaveBeenCalledWith(`/users/${user.id}/deactivate`, undefined, undefined);
  });

  it('should disable teams and roles fields if sole or sole-active owner', async() => {
    // test data
    // 2 team owners, but only one is active
    const teamOwnerTemplate = defaultData.users.find(u => u.name === 'Test Pigeon Owner');
    const users = [
      { ...teamOwnerTemplate, id: 1, sid: 's01', name: 'sole active owner' },
      { ...teamOwnerTemplate, id: 2, sid: 's02', name: 'inactive owner', active: false },
    ];

    const { getByText } = await renderPage(
      <UsersListContainer />,
      { initialState: { users: { isLoading: false, items: users } } },
    );

    // assert active owner status toggled disabled
    const firstRow = getByText('sole active owner').closest('tr');
    expect(within(firstRow).getByTestId('status-toggle-1')).toBeDisabled();

    // assert inactive owner
    const secondRow = getByText('inactive owner').closest('tr');
    expect(within(secondRow).getByTestId('status-toggle-2')).toBeEnabled();
  });

  it('should block reactivating user belonging to deactivated team', async() => {
    const teams = JSON.parse(JSON.stringify(defaultData.teams));
    teams.forEach(t => { t.active = false; });
    const users = JSON.parse(JSON.stringify(defaultData.users));
    users.forEach(u => { u.active = false; });
    const user = defaultData.users[0];

    const { getByText, getAllByText, getByRole, queryByText } = await renderPage(
      <UsersListContainer />,
      { initialState: {
        users: { isLoading: false, items: users },
        teams: { isLoading: false, items: teams },
      } },
    );
    const targetUser = getAllByText(user.name)[0];
    const statusToggle = within(targetUser.closest('tr')).getByTestId('status-toggle-1');
    expect(statusToggle.checked).toBeFalsy();
    fireEvent.click(statusToggle);
    expect(getByText('User cannot be reactivated')).toBeInTheDocument();
    fireEvent.click(getByRole('button', { name: 'Done' }));
    expect(queryByText('User cannot be reactivated')).not.toBeInTheDocument();
  });

  it('should allow deactivation modal to be dismissed by clicking cancel button', async() => {
    const { getAllByText, getByRole, queryByText } = await renderPage(<UsersListContainer />);
    const user = defaultData.users[0];
    const targetUser = getAllByText(user.name)[0];
    fireEvent.click(within(targetUser.closest('tr')).getByTestId('status-toggle-1'));

    fireEvent.click(getByRole('button', { name: 'Cancel' }));
    expect(queryByText('Are you sure you want to deactivate this user')).not.toBeInTheDocument();
  });

  it('should allow deactivation modal to be dismissed by clicking modal back drop', async() => {
    const { getByText, getAllByText, queryByText } = await renderPage(<UsersListContainer />);
    const user = defaultData.users[0];
    const targetUser = getAllByText(user.name)[0];
    fireEvent.click(within(targetUser.closest('tr')).getByTestId('status-toggle-1'));

    const modalMsg = getByText('Are you sure you want to deactivate this user?');
    fireEvent.click(modalMsg.closest('.Modal').querySelector('.InternalOverlay'));
    expect(queryByText('Are you sure you want to deactivate this user')).not.toBeInTheDocument();
  });
  it('should allow reactivation modal to be dismissed by clicking cancel button', async() => {
    const { getAllByText, getByRole, queryByText } = await renderPage(<UsersListContainer />);
    const user = defaultData.users[6];
    const targetUser = getAllByText(user.name)[0];
    fireEvent.click(within(targetUser.closest('tr')).getByTestId('status-toggle-7'));

    fireEvent.click(getByRole('button', { name: 'Cancel' }));
    expect(queryByText('Are you sure you want to reactivate this user')).not.toBeInTheDocument();
  });

  it('should allow reactivation modal to be dismissed by clicking modal back drop', async() => {
    const { getByText, getAllByText } = await renderPage(<UsersListContainer />);
    const user = defaultData.users[6];
    const targetUser = getAllByText(user.name)[0];
    fireEvent.click(within(targetUser.closest('tr')).getByTestId('status-toggle-7'));

    const modalMsg = getByText('Are you sure you want to reactivate this user?');
    const modalMsgText = getByText('Are you sure you want to reactivate this user?');
    expect(modalMsg).toBeInTheDocument();
    expect(modalMsgText).toBeInTheDocument();
    fireEvent.click(modalMsg.closest('.Modal').querySelector('.InternalOverlay'));
    expect(modalMsg).not.toBeInTheDocument();
  });
  it('should render only selected team`s roles', async() => {
    const user = defaultData.users[0];
    const { getByText, getByLabelText, queryByRole } = await renderPage(<UsersListContainer />);
    const filterBtn = getByText('Filter');
    fireEvent.click(filterBtn);
    fireEvent.change(getByLabelText('Team'), { target: { value: user.team_id } });
    fireEvent.click(getByText('Role'));
    expect(queryByRole('option', { name: 'Admin' })).toBeInTheDocument(); // belong to team 1
    expect(queryByRole('option', { name: 'Viewer 1' })).not.toBeInTheDocument(); // belong to team 2
  });

  it.skip('column sort', async() => {
    // test data
    // 3 teams, each with 1 user named after the team, we'll sort on these stats
    // only team 1 user is active, used to test sort on status column
    const teams = JSON.parse(JSON.stringify(defaultData.teams));
    teams[0].name = 'Team 1';
    teams[1].name = 'Team 2';
    teams[2].name = 'Team 3';
    const users = JSON.parse(JSON.stringify(defaultData.users)).slice(0, 3);
    users.forEach((u, idx) => {
      u.name = `Team ${idx + 1} owner`;
      u.team_id = teams[`${idx}`].id;
      u.roles = [ teams[`${idx}`].ownerRoleId ];
      u.active = idx === 0;
    });

    const { getByText, getAllByText } = await renderPage(
      <UsersListContainer />,
      { initialState: {
        users: { isLoading: false, items: users },
        teams: { isLoading: false, items: teams },
      } },
    );

    // sort by user name - ascending order
    fireEvent.click(getByText('User'));
    const namesAscending = getAllByText('Team owner');
    namesAscending.forEach((r, idx) => {
      const name = within(r.closest('tr')).getByText(`Team ${idx + 1} owner`);
      expect(name).toBeTruthy();
    });

    // sort by user name - descending order
    fireEvent.click(getByText('User'));
    const namesDescending = getAllByText('Team owner');
    namesDescending.forEach((r, idx) => {
      const name = within(r.closest('tr')).getByText(`Team ${3 - idx} owner`);
      expect(name).toBeTruthy();
    });

    // sort by team name - ascending order
    fireEvent.click(getByText('Team'));
    const teamsAscending = getAllByText('Team owner');
    teamsAscending.forEach((r, idx) => {
      const team = within(r.closest('tr')).getByText(`Team ${idx + 1}`);
      expect(team).toBeTruthy();
    });

    // sort by team name - descending order
    fireEvent.click(getByText('Team'));
    const teamsDescending = getAllByText('Team owner');
    teamsDescending.forEach((r, idx) => {
      const team = within(r.closest('tr')).getByText(`Team ${3 - idx}`);
      expect(team).toBeTruthy();
    });
  });

  it.skip('pagination', async() => {
    // test data
    // 160 users, 5 pages of 30 users, 1 page of 10 users
    const users = [];
    const refUser = defaultData.users[0];
    for (let i = 0; i < 160; i++) {
      const padding = i < 10 ? '00' : (i < 100 ? '0' : '');
      const uid = `${i < 100 ? padding : ''}${i}`;
      users.push({ ...refUser, name: `User ${uid}` });
    }

    const { getByText, getAllByText, getByRole, queryByRole } = await renderPage(
      <UsersListContainer />,
      { initialState: {
        users: { isLoading: false, items: users },
      } },
    );

    // assert 1st page
    expect(getByText('1-30 of 160 results')).toBeInTheDocument();
    fireEvent.click(getByText('User'));
    const pageOneRows = getAllByText('Team owner');
    // assert only 1st and last row as this process is slow
    [ { row: 0, uid: '000' }, { row: 29, uid: '029' } ].forEach((expected) => {
      const name = within(pageOneRows[expected.row].closest('tr'))
        .getByText(`User ${expected.uid}`);
      expect(name).toBeTruthy();
    });
    expect(getByRole('button', { name: 'First' })).toBeDisabled();
    expect(queryByRole('button', { name: '6' })).not.toBeInTheDocument();
    expect(getByRole('button', { name: 'Last' })).toBeInTheDocument();

    // assert 2nd page - ensure expected data appears on 2nd page
    fireEvent.click(getByRole('button', { name: '2' }));
    expect(getByText('31-60 of 160 results')).toBeInTheDocument();
    const pageTwoRows = getAllByText('Team owner');
    [ { row: 0, uid: '030' }, { row: 29, uid: '059' } ].forEach((expected) => {
      const name = within(pageTwoRows[expected.row].closest('tr'))
        .getByText(`User ${expected.uid}`);
      expect(name).toBeTruthy();
    });

    // assert 3rd page - only check pagination bar
    fireEvent.click(getByRole('button', { name: '4' }));
    expect(getByRole('button', { name: '6' })).toBeInTheDocument();
    expect(getByText('91-120 of 160 results')).toBeInTheDocument();

    // assert last page - check data on last page
    fireEvent.click(getByRole('button', { name: 'Last' }));
    expect(getByText('151-160 of 160 results')).toBeInTheDocument();
    const lastPageRows = getAllByText('Team owner');
    [ { row: 0, uid: '150' }, { row: 9, uid: '159' } ].forEach((expected) => {
      const name = within(lastPageRows[expected.row].closest('tr'))
        .getByText(`User ${expected.uid}`);
      expect(name).toBeTruthy();
    });
  });
});
