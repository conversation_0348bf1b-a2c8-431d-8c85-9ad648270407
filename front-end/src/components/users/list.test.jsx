import React from 'react';
import { shallow } from 'enzyme';
import UsersList from './List';

const values = { isLoading: false, data: [], className: '', columns: [] };

describe('UsersList', () => {
  const wrapper = shallow(<UsersList entityName='' {...values} />);
  global.snapshot(wrapper);
});

describe('UsersList - loading ', () => {
  const wrapper = shallow(<UsersList entityName='' {...values} isLoading />);
  global.snapshot(wrapper);
});
