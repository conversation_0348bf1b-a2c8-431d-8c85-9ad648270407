import React from 'react';
import TextButton from 'canvas-core-react/lib/TextButton';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import ToggleSwitch from 'canvas-core-react/lib/ToggleSwitch';
import { reject } from 'lodash';

export const tableColumns = ({
  isLoading,
  handleChangeUserStatus,
  canViewAllUsers,
  history,
  filters,
  sortableColumnProperties,
}) => {
  const columns = [
    {
      name: 'User',
      cellFormatter: (row) => (
        <TextButton
          className="users-list__text-button"
          onClick={() => history.push(`/users/${row.id}`)}
          Icon={() => <></>}
        >
          { row.name }
        </TextButton>
      ),
      style: { textAlign: 'left' },
      sortable: true,
      selector: 'name',
      ...sortableColumnProperties('name', filters),
    },
    {
      name: 'sID',
      selector: 'sid',
    },
    {
      name: 'Team',
      cellFormatter: (row) => (
        <TextCaption component="p">{ row.teamName }</TextCaption>
      ),
      selector: 'team',
      sortable: true,
      ...sortableColumnProperties('team', filters),
    },
    {
      name: 'Role(s)',
      cellFormatter: (row) => (
        <div>
          { row.roleNames.map((role, index) =>
            <TextCaption key={`role-${index}`} component="p">{ role }</TextCaption>
          ) }
        </div>
      ),
      selector: '',
    },
    {
      name: 'Status',
      cellFormatter: (row) => (
        <ToggleSwitch
          id={`status-toggle-${row.id}`}
          className="users-list__toggle-switch"
          data-testid={`status-toggle-${row.id}`}
          label=""
          name=""
          onChange={(e) => handleChangeUserStatus(e, row.id, row.teamActive)}
          checked={!!row.status.active}
          disabled={isLoading || row.status.disabled}
        />
      ),
      selector: 'active',
      // TODO: Migrate to headerFormatter with tooltip component when on Canvas 9.3.0+
      tooltip: 'An active user can access Pigeon. An inactive user cannot access Pigeon. By default, if a user belongs to an inactive team, the user will also be inactive. If the user is the only "Team owner" for their team, they cannot be deactivated unless their team is also deactivated.',
    },
  ];
  return reject(columns, c => !canViewAllUsers && c.selector === 'team');
};
