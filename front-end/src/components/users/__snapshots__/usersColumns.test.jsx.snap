// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`UsersColumns name col 1`] = `
<TextButtonstyle__StyleTextButton
  className="TextButton__button users-list__text-button"
  color="blue"
  onClick={[Function]}
  theme={
    {
      "breakPoints": {
        "values": {
          "lg": 1025,
          "md": 768,
          "sm": 481,
          "xs": 0,
        },
      },
      "palette": {
        "actionMenuList": {
          "item": {
            "background": {
              "hover": "#F6F7FC",
            },
            "text": "#333",
          },
          "menuButton": {
            "background": {
              "hover": "#F6F7FC",
            },
            "border": {
              "focus": "#007EAB",
            },
            "text": "#333",
          },
        },
        "alertBanner": {
          "alert": {
            "background": "#FFF3EF",
            "border": "#FDB197",
          },
          "error": {
            "background": "#FEECED",
            "border": "#F5888C",
          },
          "info": {
            "background": "#EBF7FC",
            "border": "#7FCEEA",
          },
          "success": {
            "background": "#ECF5F3",
            "border": "#89C1B3",
          },
        },
        "backToTop": {
          "background": "#FFFFFF",
          "border": "#E2E8EE",
          "hover": "#949494",
        },
        "background": {
          "card": {
            "hover": "#949494",
            "keyline": "#E2E8EE",
            "primary": "#FFFFFF",
            "secondary": "#FFFFFF",
          },
          "modal": {
            "keyline": "#E2E8EE",
            "overlay": "rgba(0, 0, 0, 0.5)",
            "primary": "#FFFFFF",
          },
          "primary": "#FFFFFF",
        },
        "brandColors": {
          "dark": {
            "black": "#757575",
            "blue": "#91ddf8",
            "green": "#84d9c6",
            "orange": "#ffba8e",
            "pink": "#fda8de",
            "purple": "#aea9f4",
            "red": "#ff969c",
            "white": "#333333",
            "yellow": "#ffeaa5",
          },
          "light": {
            "black": "#333333",
            "blue": "#009dd6",
            "darkBlue": "#007EAB",
            "darkRed": "#BE061B",
            "green": "#138468",
            "orange": "#fb6330",
            "pink": "#f2609e",
            "purple": "#7849b8",
            "red": "#ec111a",
            "white": "#FFFFFF",
            "yellow": "#ffd42f",
          },
        },
        "button": {
          "close": {
            "color": "#333333",
            "hover": "#949494",
          },
          "disabled": {
            "background": "#F6F6F6",
            "border": "#D6D6D6",
            "color": "#757575",
          },
          "navigationButtons": {
            "backButtonColor": "#333333",
            "continueButtonColor": "#ec111a",
          },
          "pillButton": {
            "background": "transparent",
            "border": "#333333",
            "caution": {
              "color": "#ec111a",
            },
            "color": "#333333",
          },
          "primary": {
            "background": "#ec111a",
            "border": "#ec111a",
            "color": "#FFFFFF",
          },
          "secondary": {
            "background": "transparent",
            "border": "#ec111a",
            "color": "#ec111a",
          },
        },
        "canvasGrey": {
          "100": "#FAFBFD",
          "200": "#F6F7FC",
          "300": "#F6F6F6",
          "400": "#E2E8EE",
          "500": "#D6D6D6",
          "600": "#757575",
          "700": "#949494",
        },
        "canvasShadow": "rgba(0, 34, 91, 0.11)",
        "charts": {
          "lineChart": {
            "axisLineColor": "#757575",
            "gridLineColor": "#D6D6D6",
            "tooltip": {
              "borderColor": "#949494",
            },
          },
          "stackedColumn": {
            "axisLineColor": "#757575",
            "dataSetColors": {
              "limitedRetail": [
                "#333333",
                "#009DD6",
                "#7849B8",
                "#138468",
                "#FB6330",
              ],
              "retail": [
                "#009DD6",
                "#7849B8",
                "#138468",
                "#F2609E",
                "#F2609E",
              ],
            },
            "gridLineColor": "#D6D6D6",
            "tooltip": {
              "borderColor": "#383838",
            },
          },
        },
        "checkbox": {
          "border": "#757575",
          "checked": "#007EAB",
          "focus": {
            "boxShadowColor": {
              "a": "#FFFFFF",
              "b": "#007EAB",
            },
          },
          "indeterminate": "#949494",
        },
        "common": {
          "black": "#333333",
          "white": "#FFFFFF",
        },
        "error": "#be061b",
        "focusOutline": "#007EAB",
        "footer": {
          "base": {
            "background": "#F6F7FC",
          },
          "border": "#E2E8EE",
          "link": {
            "hover": "#333333",
          },
          "logo": {
            "fill": "#ec111a",
          },
        },
        "hamburger": {
          "menu": {
            "background": "#FFFFFF",
          },
        },
        "icons": {
          "category": {
            "circle": "#E2E8EE",
            "path": "#757575",
          },
          "functional": {
            "black": "#333333",
            "blue": "#007EAB",
            "darkBlue": "#007EAB",
            "darkRed": "#BE061B",
            "green": "#138468",
            "orange": "#fb6330",
            "pink": "#f2609e",
            "purple": "#7849b8",
            "red": "#ec111a",
            "white": "#FFFFFF",
            "yellow": "#ffd42f",
          },
        },
        "link": {
          "emphasis": {
            "color": "#007EAB",
            "hover": "#005E80",
          },
        },
        "loadingIndicator": {
          "background": "#949494",
        },
        "meter": {
          "borderColor": "#949494",
          "complete": "#138468",
          "incomplete": "#F6F6F6",
        },
        "pagination": {
          "disabled": "#949494",
          "selected": "#ec111a",
        },
        "pinTextField": {
          "pinItem": {
            "backgroundGray": "#F6F7FC",
          },
        },
        "primary": "#009dd6",
        "quickActionCard": {
          "chevron": {
            "black": "#333333",
          },
        },
        "radio": {
          "focus": {
            "boxShadowColor": {
              "a": "#FFFFFF",
              "b": "#007EAB",
            },
          },
        },
        "search": {
          "border": "#757575",
        },
        "snackBar": {
          "background": "#2c2c2e",
          "button": {
            "color": "#009DD6",
            "hover": "#05BCFF",
          },
          "color": "#FFFFFF",
        },
        "stepTracker": {
          "border": "#949494",
          "incompleteBackground": "#D6D6D6",
        },
        "tabs": {
          "border": "#383838",
          "hover": "#757575",
        },
        "text": {
          "disabled": "#949494",
          "highEmphasis": "#333333",
          "mediumEmphasis": "#757575",
          "placeholderText": "#949494",
        },
        "textArea": {
          "border": "#757575",
        },
        "toggleSwitch": {
          "iconOff": "#949494",
          "iconOn": "#007EAB",
        },
        "tooltip": {
          "color": {
            "blue": "#009dd6",
            "default": "#333333",
          },
        },
      },
      "tokens": {
        "action": {
          "focus": {
            "default": "#007EAB",
            "outline": "#009DD6",
          },
          "hover": {
            "background": {
              "actionMenuList": {
                "default": "rgba(226,232,238,0.5)",
              },
              "pillButton": {
                "caution": "#BE061B",
                "default": "#333333",
                "inverted": "#FFFFFF",
              },
              "pillButtonPrimary": {
                "default": "#BE061B",
                "inverted": "#333333",
              },
              "pillButtonSecondary": {
                "black": "#333333",
                "red": "#BE061B",
                "textOnly": "#333333",
              },
            },
            "border": {
              "pillButtonSecondary": {
                "black": "#333333",
                "red": "#BE061B",
                "textOnly": "transparent",
              },
            },
            "default": "#005E80",
            "text": {
              "pillButton": {
                "caution": "#FFFFFF",
                "default": "#FFFFFF",
              },
              "pillButtonPrimary": {
                "default": "#FFFFFF",
                "inverted": "#FFFFFF",
              },
              "pillButtonSecondary": {
                "black": "#FFFFFF",
                "red": "#FFFFFF",
                "textOnly": "#FFFFFF",
              },
              "snackbar": {
                "button": "#05BCFF",
              },
            },
          },
        },
        "background": {
          "alertBanner": {
            "alert": "rgba(251,99,48,0.08)",
            "error": "rgba(236,17,26,0.08)",
            "info": "rgba(0,157,214,0.08)",
            "new": "rgba(120,73,184,0.08)",
            "success": "rgba(19,132,104,0.08)",
          },
          "base": "#FFFFFF",
          "comboBox": {
            "default": "#FFFFFF",
            "listBoxItem": {
              "hover": "#F6F6F6",
            },
          },
          "footer": {
            "base": {
              "grey": "#F6F7FC",
              "white": "#FFFFFF",
            },
            "logo": {
              "default": "#EC111A",
            },
          },
          "header": {
            "black": "#333333",
            "red": "#EC111A",
            "white": "#FFFFFF",
          },
          "meter": {
            "default": "#138468",
            "incomplete": "#F6F6F6",
          },
          "modal": "#FFFFFF",
          "navigation": {
            "default": "#FFFFFF",
          },
          "overlay": "rgb(255,255,255,0.50)",
          "pillButtonPrimary": {
            "default": "#EC111A",
            "inverted": "#FFFFFF",
          },
          "pillButtonSecondary": {
            "black": "transparent",
            "red": "transparent",
            "textOnly": "transparent",
          },
          "primary": "#FFFFFF",
          "progressIndicator": {
            "bar": {
              "default": "#138468",
            },
            "container": {
              "default": "#F6F6F6",
            },
          },
          "secondary": "#FFFFFF",
          "skeleton": "#949494",
          "snackbar": "#2C2C2C",
          "statusBadge": {
            "default": "transparent",
            "emphasis": "transparent",
            "error": "transparent",
            "new": "#7849B8",
            "success": "#138468",
            "success-emphasis": "transparent",
          },
          "table": {
            "row": {
              "alternate": "#FAFBFD",
              "hover": "rgba(226, 232, 238, 0.4)",
            },
          },
        },
        "border": {
          "alertBanner": {
            "alert": "rgba(251,99,48,0.5)",
            "error": "rgba(236,17,26,0.5)",
            "info": "rgba(0,157,214,0.5)",
            "new": "rgba(120,73,184,0.5)",
            "success": "rgba(19,132,104,0.5)",
          },
          "card": "#E2E8EE",
          "default": "#707070",
          "header": {
            "white": "#E2E8EE",
          },
          "meter": {
            "default": "#949494",
          },
          "pillButton": {
            "caution": "#EC111A",
            "default": "#333333",
          },
          "pillButtonPrimary": {
            "default": "#EC111A",
            "inverted": "#FFFFFF",
          },
          "pillButtonSecondary": {
            "black": "#333333",
            "red": "#EC111A",
            "textOnly": "transparent",
          },
          "progressIndicator": {
            "container": {
              "default": "#949494",
            },
          },
          "skeleton": "#949494",
          "statusBadge": {
            "default": "#707070",
            "emphasis": "#007EAB",
            "error": "#BE061B",
            "new": "#7849B8",
            "success": "#138468",
            "success-emphasis": "#138468",
          },
        },
        "grid": {
          "breakPoints": {
            "lg": {
              "gutter": 3,
              "margin": "3.6rem",
              "maxCols": 12,
              "maxWidth": "4000px",
              "minWidth": "1025px",
              "rowMargin": "-1.8rem",
              "size": "lg",
            },
            "md": {
              "gutter": 3,
              "margin": [
                "3.6rem",
                "4.2rem",
                "4.8rem",
                "5.4rem",
              ],
              "maxCols": 12,
              "maxWidth": "1024px",
              "minWidth": "768px",
              "rowMargin": "-1.5rem",
              "size": "md",
            },
            "sm": {
              "gutter": 1.8,
              "margin": [
                "2.4rem",
                "3.0rem",
                "3.6rem",
                "4.2rem",
              ],
              "maxCols": 8,
              "maxWidth": "767px",
              "minWidth": "481px",
              "rowMargin": "-1.2rem",
              "size": "sm",
            },
            "xs": {
              "gutter": 1.8,
              "margin": [
                "1.8rem",
                "2.4rem",
                "3.0rem",
                "3.6rem",
              ],
              "maxCols": 4,
              "maxWidth": "480px",
              "minWidth": "0px",
              "rowMargin": "-0.6rem",
              "size": "xs",
            },
          },
          "containerMaxWidth": 1200,
          "containerMaxWidthMargins": 1272,
          "containerMinWidth": 320,
          "gridColumns": 12,
        },
        "icons": {
          "functional": {
            "black": "#333333",
            "blue": "#007EAB",
            "darkBlue": "#007EAB",
            "darkGray": "#707070",
            "darkRed": "#BE061B",
            "gray": "#949494",
            "green": "#138468",
            "orange": "#FB6330",
            "pink": "#F2609E",
            "purple": "#7849B8",
            "red": "#EC111A",
            "white": "#FFFFFF",
            "yellow": "#FFD42F",
          },
        },
        "loadingIndicator": {
          "darkGray": "#949494",
          "red": "#EC111A",
        },
        "mode": "light",
        "palette": {
          "color": {
            "base": "#FFFFFF",
            "black": "#333333",
            "blue": "#009DD6",
            "brandRed": "#EC111A",
            "darkBlue": "#007EAB",
            "darkBlue100": "#005E80",
            "darkGreen": "#117E63",
            "darkRed": "#BE061B",
            "darkRed100": "#BE061B",
            "green": "#138468",
            "green100": "rgb(132,217,198,0.15)",
            "orange": "#FB6330",
            "pink": "#F2609E",
            "primaryRed": "#EC111A",
            "purple": "#7849B8",
            "yellow": "#FFD42F",
          },
          "gray": {
            "0": "#FFFFFF",
            "100": "#FAFBFD",
            "200": "#F6F7FC",
            "300": "#F6F6F6",
            "400": "#E2E8EE",
            "500": "#D6D6D6",
            "550": "#949494",
            "600": "#707070",
            "700": "#333333",
            "800": "#2C2C2C",
          },
        },
        "pillButtonPrimary": {
          "default": {
            "backgroundColor": "#EC111A",
            "borderColor": "#EC111A",
            "textColor": "#FFFFFF",
          },
          "hover": {
            "default": {
              "backgroundColor": "#BE061B",
              "borderColor": "#BE061B",
              "textColor": "#FFFFFF",
            },
            "inverted": {
              "backgroundColor": "#333333",
              "borderColor": "#333333",
              "textColor": "#FFFFFF",
            },
          },
          "inverted": {
            "backgroundColor": "#FFFFFF",
            "borderColor": "#FFFFFF",
            "textColor": "#333333",
          },
        },
        "primary": {
          "dark": "#BE061B",
          "main": "#EC111A",
        },
        "secondary": {
          "dark": "#005E80",
          "main": "#007EAB",
        },
        "secondaryButton": {
          "default": {
            "backgroundColor": "#FFFFFF",
            "borderColor": "#EC111A",
            "textColor": "#EC111A",
          },
          "hover": {
            "default": {
              "backgroundColor": "#BE061B",
              "borderColor": "#BE061B",
              "textColor": "#FFFFFF",
            },
          },
        },
        "sidesheet": {
          "overlay": {
            "background": "#FFFFFF",
          },
          "persistent": {
            "background": "#FFFFFF",
          },
        },
        "size": {
          "borderRadius": {
            "0": "10rem",
            "100": "0.4rem",
            "200": "0.8rem",
            "300": "1.2rem",
            "50": "0.2rem",
          },
          "borderWidth": {
            "1": "0.1rem",
            "2": "0.2rem",
            "3": "0.3rem",
            "5": "0.5rem",
          },
          "breakPoints": {
            "lg": "1024px",
            "md": "768px",
            "sm": "375px",
            "xs": "0px",
          },
          "sizing": {
            "24": "2.4rem",
            "32": "3.2rem",
            "36": "3.6rem",
            "42": "4.2rem",
            "44": "4.4rem",
            "48": "4.8rem",
            "54": "5.4rem",
            "60": "6.0rem",
            "72": "7.2rem",
          },
          "spacing": {
            "1": "0.6rem",
            "10": "6.0rem",
            "11": "6.6rem",
            "12": "7.2rem",
            "13": "7.8rem",
            "14": "8.4rem",
            "15": "9.0rem",
            "16": "9.6rem",
            "17": "10.2rem",
            "18": "10.8rem",
            "19": "11.4rem",
            "2": "1.2rem",
            "20": "12.0rem",
            "3": "1.8rem",
            "4": "2.4rem",
            "5": "3.0rem",
            "6": "3.6rem",
            "7": "4.2rem",
            "8": "4.8rem",
            "9": "5.4rem",
          },
        },
        "state": {
          "disabled": {
            "background": "#F6F6F6",
            "border": "#D6D6D6",
            "text": "#949494",
          },
          "error": {
            "default": "#BE061B",
          },
        },
        "text": {
          "highEmphasis": "#333333",
          "medEmphasis": "#707070",
          "pillButton": {
            "caution": "#EC111A",
            "default": "#333333",
            "inverted": "#333333",
          },
          "pillButtonPrimary": {
            "default": "#FFFFFF",
            "inverted": "#333333",
          },
          "pillButtonSecondary": {
            "black": "#333333",
            "red": "#EC111A",
            "textOnly": "#333333",
          },
          "placeholder": "#707070",
          "secondaryButton": "#EC111A",
          "snackbar": {
            "button": "#009DD6",
            "content": "#E0E0E0",
          },
          "statusBadge": {
            "default": "#707070",
            "emphasis": "#007EAB",
            "error": "#BE061B",
            "new": "#FFFFFF",
            "success": "#FFFFFF",
            "success-emphasis": "#138468",
          },
        },
        "theme": "default",
        "transform": {
          "elevation": {
            "100": "0px 2px 10px rgba(0, 34, 91, 0.11)",
          },
          "motion": {
            "100": "200ms",
            "150": "350ms",
            "200": "500ms",
            "300": "0.1s",
            "400": "0.2s",
            "500": "0.3s",
            "600": "0.4s",
            "700": "0.5s",
            "800": "0.6s",
          },
          "opacity": {
            "0": 0,
            "100": 1,
            "11": 0.11,
            "15": 0.15,
            "16": 0.16,
            "2": 0.02,
            "3": 0.03,
            "38": 0.38,
            "5": 0.05,
            "50": 0.5,
            "60": 0.6,
            "87": 0.87,
          },
        },
        "type": "light",
        "typography": {
          "lineHeight": {
            "100": "1.8rem",
            "1000": "5.4rem",
            "200": "2.0rem",
            "300": "2.1rem",
            "400": "2.2rem",
            "500": "2.4rem",
            "600": "2.6rem",
            "700": "2.7rem",
            "800": "3.0rem",
            "850": "3.5rem",
            "900": "4.0rem",
            "910": "4.1rem",
          },
          "size": {
            "100": "1.2rem",
            "1000": "3.6rem",
            "1100": "4.8rem",
            "200": "1.4rem",
            "300": "1.6rem",
            "400": "1.8rem",
            "500": "2.0rem",
            "600": "2.1rem",
            "700": "2.4rem",
            "800": "2.8rem",
            "900": "3.2rem",
          },
          "weight": {
            "bold": ""Scotia Bold", "Arial", "Helvetica", "sans-serif"",
            "boldItalics": ""Scotia Bold Italic", "Arial", "Helvetica", "sans-serif"",
            "headline": ""Scotia Headline", "Arial", "Helvetica", "sans-serif"",
            "italic": ""Scotia Italic", "Arial", "Helvetica", "sans-serif"",
            "legal": ""Scotia Legal", "Arial", "Helvetica", "sans-serif"",
            "legalItalic": ""Scotia Legal Italic", "Arial", "Helvetica", "sans-serif"",
            "regular": ""Scotia Regular", "Arial", "Helvetica", "sans-serif"",
          },
        },
      },
      "transitions": {
        "duaration": {
          "sideSheetMainWrapper": "600ms",
        },
        "effect": {
          "easing": {
            "easeInOut": "ease-in-out",
          },
        },
      },
      "type": "light",
      "typography": {
        "fontBoldFamily": ""Scotia Bold", "Arial", "Helvetica", "sans-serif"",
        "fontBoldItalicFamily": ""Scotia Bold Italic", "Arial", "Helvetica", "sans-serif"",
        "fontHeadlineFamily": ""Scotia Headline", "Arial", "Helvetica", "sans-serif"",
        "fontItalicFamily": ""Scotia Italic", "Arial", "Helvetica", "sans-serif"",
        "fontLightFamily": ""Scotia Light", "Arial", "Helvetica", "sans-serif"",
        "fontRegularFamily": ""Scotia Regular", "Arial", "Helvetica", "sans-serif"",
      },
    }
  }
  weight="bold"
>
  <TextButtonstyle__StyledIcon
    as={[Function]}
    className="TextButton__icon--left"
    left={1}
    size={16}
  />
  <TextButtonstyle__StyledText>
    user
  </TextButtonstyle__StyledText>
</TextButtonstyle__StyleTextButton>
`;

exports[`UsersColumns name col 2`] = `
<TextButtonstyle__StyleTextButton
  className="TextButton__button users-list__text-button"
  color="blue"
  onClick={[Function]}
  theme={
    {
      "breakPoints": {
        "values": {
          "lg": 1025,
          "md": 768,
          "sm": 481,
          "xs": 0,
        },
      },
      "palette": {
        "actionMenuList": {
          "item": {
            "background": {
              "hover": "#F6F7FC",
            },
            "text": "#333",
          },
          "menuButton": {
            "background": {
              "hover": "#F6F7FC",
            },
            "border": {
              "focus": "#007EAB",
            },
            "text": "#333",
          },
        },
        "alertBanner": {
          "alert": {
            "background": "#FFF3EF",
            "border": "#FDB197",
          },
          "error": {
            "background": "#FEECED",
            "border": "#F5888C",
          },
          "info": {
            "background": "#EBF7FC",
            "border": "#7FCEEA",
          },
          "success": {
            "background": "#ECF5F3",
            "border": "#89C1B3",
          },
        },
        "backToTop": {
          "background": "#FFFFFF",
          "border": "#E2E8EE",
          "hover": "#949494",
        },
        "background": {
          "card": {
            "hover": "#949494",
            "keyline": "#E2E8EE",
            "primary": "#FFFFFF",
            "secondary": "#FFFFFF",
          },
          "modal": {
            "keyline": "#E2E8EE",
            "overlay": "rgba(0, 0, 0, 0.5)",
            "primary": "#FFFFFF",
          },
          "primary": "#FFFFFF",
        },
        "brandColors": {
          "dark": {
            "black": "#757575",
            "blue": "#91ddf8",
            "green": "#84d9c6",
            "orange": "#ffba8e",
            "pink": "#fda8de",
            "purple": "#aea9f4",
            "red": "#ff969c",
            "white": "#333333",
            "yellow": "#ffeaa5",
          },
          "light": {
            "black": "#333333",
            "blue": "#009dd6",
            "darkBlue": "#007EAB",
            "darkRed": "#BE061B",
            "green": "#138468",
            "orange": "#fb6330",
            "pink": "#f2609e",
            "purple": "#7849b8",
            "red": "#ec111a",
            "white": "#FFFFFF",
            "yellow": "#ffd42f",
          },
        },
        "button": {
          "close": {
            "color": "#333333",
            "hover": "#949494",
          },
          "disabled": {
            "background": "#F6F6F6",
            "border": "#D6D6D6",
            "color": "#757575",
          },
          "navigationButtons": {
            "backButtonColor": "#333333",
            "continueButtonColor": "#ec111a",
          },
          "pillButton": {
            "background": "transparent",
            "border": "#333333",
            "caution": {
              "color": "#ec111a",
            },
            "color": "#333333",
          },
          "primary": {
            "background": "#ec111a",
            "border": "#ec111a",
            "color": "#FFFFFF",
          },
          "secondary": {
            "background": "transparent",
            "border": "#ec111a",
            "color": "#ec111a",
          },
        },
        "canvasGrey": {
          "100": "#FAFBFD",
          "200": "#F6F7FC",
          "300": "#F6F6F6",
          "400": "#E2E8EE",
          "500": "#D6D6D6",
          "600": "#757575",
          "700": "#949494",
        },
        "canvasShadow": "rgba(0, 34, 91, 0.11)",
        "charts": {
          "lineChart": {
            "axisLineColor": "#757575",
            "gridLineColor": "#D6D6D6",
            "tooltip": {
              "borderColor": "#949494",
            },
          },
          "stackedColumn": {
            "axisLineColor": "#757575",
            "dataSetColors": {
              "limitedRetail": [
                "#333333",
                "#009DD6",
                "#7849B8",
                "#138468",
                "#FB6330",
              ],
              "retail": [
                "#009DD6",
                "#7849B8",
                "#138468",
                "#F2609E",
                "#F2609E",
              ],
            },
            "gridLineColor": "#D6D6D6",
            "tooltip": {
              "borderColor": "#383838",
            },
          },
        },
        "checkbox": {
          "border": "#757575",
          "checked": "#007EAB",
          "focus": {
            "boxShadowColor": {
              "a": "#FFFFFF",
              "b": "#007EAB",
            },
          },
          "indeterminate": "#949494",
        },
        "common": {
          "black": "#333333",
          "white": "#FFFFFF",
        },
        "error": "#be061b",
        "focusOutline": "#007EAB",
        "footer": {
          "base": {
            "background": "#F6F7FC",
          },
          "border": "#E2E8EE",
          "link": {
            "hover": "#333333",
          },
          "logo": {
            "fill": "#ec111a",
          },
        },
        "hamburger": {
          "menu": {
            "background": "#FFFFFF",
          },
        },
        "icons": {
          "category": {
            "circle": "#E2E8EE",
            "path": "#757575",
          },
          "functional": {
            "black": "#333333",
            "blue": "#007EAB",
            "darkBlue": "#007EAB",
            "darkRed": "#BE061B",
            "green": "#138468",
            "orange": "#fb6330",
            "pink": "#f2609e",
            "purple": "#7849b8",
            "red": "#ec111a",
            "white": "#FFFFFF",
            "yellow": "#ffd42f",
          },
        },
        "link": {
          "emphasis": {
            "color": "#007EAB",
            "hover": "#005E80",
          },
        },
        "loadingIndicator": {
          "background": "#949494",
        },
        "meter": {
          "borderColor": "#949494",
          "complete": "#138468",
          "incomplete": "#F6F6F6",
        },
        "pagination": {
          "disabled": "#949494",
          "selected": "#ec111a",
        },
        "pinTextField": {
          "pinItem": {
            "backgroundGray": "#F6F7FC",
          },
        },
        "primary": "#009dd6",
        "quickActionCard": {
          "chevron": {
            "black": "#333333",
          },
        },
        "radio": {
          "focus": {
            "boxShadowColor": {
              "a": "#FFFFFF",
              "b": "#007EAB",
            },
          },
        },
        "search": {
          "border": "#757575",
        },
        "snackBar": {
          "background": "#2c2c2e",
          "button": {
            "color": "#009DD6",
            "hover": "#05BCFF",
          },
          "color": "#FFFFFF",
        },
        "stepTracker": {
          "border": "#949494",
          "incompleteBackground": "#D6D6D6",
        },
        "tabs": {
          "border": "#383838",
          "hover": "#757575",
        },
        "text": {
          "disabled": "#949494",
          "highEmphasis": "#333333",
          "mediumEmphasis": "#757575",
          "placeholderText": "#949494",
        },
        "textArea": {
          "border": "#757575",
        },
        "toggleSwitch": {
          "iconOff": "#949494",
          "iconOn": "#007EAB",
        },
        "tooltip": {
          "color": {
            "blue": "#009dd6",
            "default": "#333333",
          },
        },
      },
      "tokens": {
        "action": {
          "focus": {
            "default": "#007EAB",
            "outline": "#009DD6",
          },
          "hover": {
            "background": {
              "actionMenuList": {
                "default": "rgba(226,232,238,0.5)",
              },
              "pillButton": {
                "caution": "#BE061B",
                "default": "#333333",
                "inverted": "#FFFFFF",
              },
              "pillButtonPrimary": {
                "default": "#BE061B",
                "inverted": "#333333",
              },
              "pillButtonSecondary": {
                "black": "#333333",
                "red": "#BE061B",
                "textOnly": "#333333",
              },
            },
            "border": {
              "pillButtonSecondary": {
                "black": "#333333",
                "red": "#BE061B",
                "textOnly": "transparent",
              },
            },
            "default": "#005E80",
            "text": {
              "pillButton": {
                "caution": "#FFFFFF",
                "default": "#FFFFFF",
              },
              "pillButtonPrimary": {
                "default": "#FFFFFF",
                "inverted": "#FFFFFF",
              },
              "pillButtonSecondary": {
                "black": "#FFFFFF",
                "red": "#FFFFFF",
                "textOnly": "#FFFFFF",
              },
              "snackbar": {
                "button": "#05BCFF",
              },
            },
          },
        },
        "background": {
          "alertBanner": {
            "alert": "rgba(251,99,48,0.08)",
            "error": "rgba(236,17,26,0.08)",
            "info": "rgba(0,157,214,0.08)",
            "new": "rgba(120,73,184,0.08)",
            "success": "rgba(19,132,104,0.08)",
          },
          "base": "#FFFFFF",
          "comboBox": {
            "default": "#FFFFFF",
            "listBoxItem": {
              "hover": "#F6F6F6",
            },
          },
          "footer": {
            "base": {
              "grey": "#F6F7FC",
              "white": "#FFFFFF",
            },
            "logo": {
              "default": "#EC111A",
            },
          },
          "header": {
            "black": "#333333",
            "red": "#EC111A",
            "white": "#FFFFFF",
          },
          "meter": {
            "default": "#138468",
            "incomplete": "#F6F6F6",
          },
          "modal": "#FFFFFF",
          "navigation": {
            "default": "#FFFFFF",
          },
          "overlay": "rgb(255,255,255,0.50)",
          "pillButtonPrimary": {
            "default": "#EC111A",
            "inverted": "#FFFFFF",
          },
          "pillButtonSecondary": {
            "black": "transparent",
            "red": "transparent",
            "textOnly": "transparent",
          },
          "primary": "#FFFFFF",
          "progressIndicator": {
            "bar": {
              "default": "#138468",
            },
            "container": {
              "default": "#F6F6F6",
            },
          },
          "secondary": "#FFFFFF",
          "skeleton": "#949494",
          "snackbar": "#2C2C2C",
          "statusBadge": {
            "default": "transparent",
            "emphasis": "transparent",
            "error": "transparent",
            "new": "#7849B8",
            "success": "#138468",
            "success-emphasis": "transparent",
          },
          "table": {
            "row": {
              "alternate": "#FAFBFD",
              "hover": "rgba(226, 232, 238, 0.4)",
            },
          },
        },
        "border": {
          "alertBanner": {
            "alert": "rgba(251,99,48,0.5)",
            "error": "rgba(236,17,26,0.5)",
            "info": "rgba(0,157,214,0.5)",
            "new": "rgba(120,73,184,0.5)",
            "success": "rgba(19,132,104,0.5)",
          },
          "card": "#E2E8EE",
          "default": "#707070",
          "header": {
            "white": "#E2E8EE",
          },
          "meter": {
            "default": "#949494",
          },
          "pillButton": {
            "caution": "#EC111A",
            "default": "#333333",
          },
          "pillButtonPrimary": {
            "default": "#EC111A",
            "inverted": "#FFFFFF",
          },
          "pillButtonSecondary": {
            "black": "#333333",
            "red": "#EC111A",
            "textOnly": "transparent",
          },
          "progressIndicator": {
            "container": {
              "default": "#949494",
            },
          },
          "skeleton": "#949494",
          "statusBadge": {
            "default": "#707070",
            "emphasis": "#007EAB",
            "error": "#BE061B",
            "new": "#7849B8",
            "success": "#138468",
            "success-emphasis": "#138468",
          },
        },
        "grid": {
          "breakPoints": {
            "lg": {
              "gutter": 3,
              "margin": "3.6rem",
              "maxCols": 12,
              "maxWidth": "4000px",
              "minWidth": "1025px",
              "rowMargin": "-1.8rem",
              "size": "lg",
            },
            "md": {
              "gutter": 3,
              "margin": [
                "3.6rem",
                "4.2rem",
                "4.8rem",
                "5.4rem",
              ],
              "maxCols": 12,
              "maxWidth": "1024px",
              "minWidth": "768px",
              "rowMargin": "-1.5rem",
              "size": "md",
            },
            "sm": {
              "gutter": 1.8,
              "margin": [
                "2.4rem",
                "3.0rem",
                "3.6rem",
                "4.2rem",
              ],
              "maxCols": 8,
              "maxWidth": "767px",
              "minWidth": "481px",
              "rowMargin": "-1.2rem",
              "size": "sm",
            },
            "xs": {
              "gutter": 1.8,
              "margin": [
                "1.8rem",
                "2.4rem",
                "3.0rem",
                "3.6rem",
              ],
              "maxCols": 4,
              "maxWidth": "480px",
              "minWidth": "0px",
              "rowMargin": "-0.6rem",
              "size": "xs",
            },
          },
          "containerMaxWidth": 1200,
          "containerMaxWidthMargins": 1272,
          "containerMinWidth": 320,
          "gridColumns": 12,
        },
        "icons": {
          "functional": {
            "black": "#333333",
            "blue": "#007EAB",
            "darkBlue": "#007EAB",
            "darkGray": "#707070",
            "darkRed": "#BE061B",
            "gray": "#949494",
            "green": "#138468",
            "orange": "#FB6330",
            "pink": "#F2609E",
            "purple": "#7849B8",
            "red": "#EC111A",
            "white": "#FFFFFF",
            "yellow": "#FFD42F",
          },
        },
        "loadingIndicator": {
          "darkGray": "#949494",
          "red": "#EC111A",
        },
        "mode": "light",
        "palette": {
          "color": {
            "base": "#FFFFFF",
            "black": "#333333",
            "blue": "#009DD6",
            "brandRed": "#EC111A",
            "darkBlue": "#007EAB",
            "darkBlue100": "#005E80",
            "darkGreen": "#117E63",
            "darkRed": "#BE061B",
            "darkRed100": "#BE061B",
            "green": "#138468",
            "green100": "rgb(132,217,198,0.15)",
            "orange": "#FB6330",
            "pink": "#F2609E",
            "primaryRed": "#EC111A",
            "purple": "#7849B8",
            "yellow": "#FFD42F",
          },
          "gray": {
            "0": "#FFFFFF",
            "100": "#FAFBFD",
            "200": "#F6F7FC",
            "300": "#F6F6F6",
            "400": "#E2E8EE",
            "500": "#D6D6D6",
            "550": "#949494",
            "600": "#707070",
            "700": "#333333",
            "800": "#2C2C2C",
          },
        },
        "pillButtonPrimary": {
          "default": {
            "backgroundColor": "#EC111A",
            "borderColor": "#EC111A",
            "textColor": "#FFFFFF",
          },
          "hover": {
            "default": {
              "backgroundColor": "#BE061B",
              "borderColor": "#BE061B",
              "textColor": "#FFFFFF",
            },
            "inverted": {
              "backgroundColor": "#333333",
              "borderColor": "#333333",
              "textColor": "#FFFFFF",
            },
          },
          "inverted": {
            "backgroundColor": "#FFFFFF",
            "borderColor": "#FFFFFF",
            "textColor": "#333333",
          },
        },
        "primary": {
          "dark": "#BE061B",
          "main": "#EC111A",
        },
        "secondary": {
          "dark": "#005E80",
          "main": "#007EAB",
        },
        "secondaryButton": {
          "default": {
            "backgroundColor": "#FFFFFF",
            "borderColor": "#EC111A",
            "textColor": "#EC111A",
          },
          "hover": {
            "default": {
              "backgroundColor": "#BE061B",
              "borderColor": "#BE061B",
              "textColor": "#FFFFFF",
            },
          },
        },
        "sidesheet": {
          "overlay": {
            "background": "#FFFFFF",
          },
          "persistent": {
            "background": "#FFFFFF",
          },
        },
        "size": {
          "borderRadius": {
            "0": "10rem",
            "100": "0.4rem",
            "200": "0.8rem",
            "300": "1.2rem",
            "50": "0.2rem",
          },
          "borderWidth": {
            "1": "0.1rem",
            "2": "0.2rem",
            "3": "0.3rem",
            "5": "0.5rem",
          },
          "breakPoints": {
            "lg": "1024px",
            "md": "768px",
            "sm": "375px",
            "xs": "0px",
          },
          "sizing": {
            "24": "2.4rem",
            "32": "3.2rem",
            "36": "3.6rem",
            "42": "4.2rem",
            "44": "4.4rem",
            "48": "4.8rem",
            "54": "5.4rem",
            "60": "6.0rem",
            "72": "7.2rem",
          },
          "spacing": {
            "1": "0.6rem",
            "10": "6.0rem",
            "11": "6.6rem",
            "12": "7.2rem",
            "13": "7.8rem",
            "14": "8.4rem",
            "15": "9.0rem",
            "16": "9.6rem",
            "17": "10.2rem",
            "18": "10.8rem",
            "19": "11.4rem",
            "2": "1.2rem",
            "20": "12.0rem",
            "3": "1.8rem",
            "4": "2.4rem",
            "5": "3.0rem",
            "6": "3.6rem",
            "7": "4.2rem",
            "8": "4.8rem",
            "9": "5.4rem",
          },
        },
        "state": {
          "disabled": {
            "background": "#F6F6F6",
            "border": "#D6D6D6",
            "text": "#949494",
          },
          "error": {
            "default": "#BE061B",
          },
        },
        "text": {
          "highEmphasis": "#333333",
          "medEmphasis": "#707070",
          "pillButton": {
            "caution": "#EC111A",
            "default": "#333333",
            "inverted": "#333333",
          },
          "pillButtonPrimary": {
            "default": "#FFFFFF",
            "inverted": "#333333",
          },
          "pillButtonSecondary": {
            "black": "#333333",
            "red": "#EC111A",
            "textOnly": "#333333",
          },
          "placeholder": "#707070",
          "secondaryButton": "#EC111A",
          "snackbar": {
            "button": "#009DD6",
            "content": "#E0E0E0",
          },
          "statusBadge": {
            "default": "#707070",
            "emphasis": "#007EAB",
            "error": "#BE061B",
            "new": "#FFFFFF",
            "success": "#FFFFFF",
            "success-emphasis": "#138468",
          },
        },
        "theme": "default",
        "transform": {
          "elevation": {
            "100": "0px 2px 10px rgba(0, 34, 91, 0.11)",
          },
          "motion": {
            "100": "200ms",
            "150": "350ms",
            "200": "500ms",
            "300": "0.1s",
            "400": "0.2s",
            "500": "0.3s",
            "600": "0.4s",
            "700": "0.5s",
            "800": "0.6s",
          },
          "opacity": {
            "0": 0,
            "100": 1,
            "11": 0.11,
            "15": 0.15,
            "16": 0.16,
            "2": 0.02,
            "3": 0.03,
            "38": 0.38,
            "5": 0.05,
            "50": 0.5,
            "60": 0.6,
            "87": 0.87,
          },
        },
        "type": "light",
        "typography": {
          "lineHeight": {
            "100": "1.8rem",
            "1000": "5.4rem",
            "200": "2.0rem",
            "300": "2.1rem",
            "400": "2.2rem",
            "500": "2.4rem",
            "600": "2.6rem",
            "700": "2.7rem",
            "800": "3.0rem",
            "850": "3.5rem",
            "900": "4.0rem",
            "910": "4.1rem",
          },
          "size": {
            "100": "1.2rem",
            "1000": "3.6rem",
            "1100": "4.8rem",
            "200": "1.4rem",
            "300": "1.6rem",
            "400": "1.8rem",
            "500": "2.0rem",
            "600": "2.1rem",
            "700": "2.4rem",
            "800": "2.8rem",
            "900": "3.2rem",
          },
          "weight": {
            "bold": ""Scotia Bold", "Arial", "Helvetica", "sans-serif"",
            "boldItalics": ""Scotia Bold Italic", "Arial", "Helvetica", "sans-serif"",
            "headline": ""Scotia Headline", "Arial", "Helvetica", "sans-serif"",
            "italic": ""Scotia Italic", "Arial", "Helvetica", "sans-serif"",
            "legal": ""Scotia Legal", "Arial", "Helvetica", "sans-serif"",
            "legalItalic": ""Scotia Legal Italic", "Arial", "Helvetica", "sans-serif"",
            "regular": ""Scotia Regular", "Arial", "Helvetica", "sans-serif"",
          },
        },
      },
      "transitions": {
        "duaration": {
          "sideSheetMainWrapper": "600ms",
        },
        "effect": {
          "easing": {
            "easeInOut": "ease-in-out",
          },
        },
      },
      "type": "light",
      "typography": {
        "fontBoldFamily": ""Scotia Bold", "Arial", "Helvetica", "sans-serif"",
        "fontBoldItalicFamily": ""Scotia Bold Italic", "Arial", "Helvetica", "sans-serif"",
        "fontHeadlineFamily": ""Scotia Headline", "Arial", "Helvetica", "sans-serif"",
        "fontItalicFamily": ""Scotia Italic", "Arial", "Helvetica", "sans-serif"",
        "fontLightFamily": ""Scotia Light", "Arial", "Helvetica", "sans-serif"",
        "fontRegularFamily": ""Scotia Regular", "Arial", "Helvetica", "sans-serif"",
      },
    }
  }
  weight="bold"
>
  <TextButtonstyle__StyledIcon
    as={[Function]}
    className="TextButton__icon--left"
    left={1}
    size={16}
  />
  <TextButtonstyle__StyledText>
    user
  </TextButtonstyle__StyledText>
</TextButtonstyle__StyleTextButton>
`;

exports[`UsersColumns team col 1`] = `
<TextCaptionstyle__Text
  as="p"
  bold={false}
  className="TextCaption__text"
  color="black"
  italic={false}
  numeric={false}
  theme={
    {
      "breakPoints": {
        "values": {
          "lg": 1025,
          "md": 768,
          "sm": 481,
          "xs": 0,
        },
      },
      "palette": {
        "actionMenuList": {
          "item": {
            "background": {
              "hover": "#F6F7FC",
            },
            "text": "#333",
          },
          "menuButton": {
            "background": {
              "hover": "#F6F7FC",
            },
            "border": {
              "focus": "#007EAB",
            },
            "text": "#333",
          },
        },
        "alertBanner": {
          "alert": {
            "background": "#FFF3EF",
            "border": "#FDB197",
          },
          "error": {
            "background": "#FEECED",
            "border": "#F5888C",
          },
          "info": {
            "background": "#EBF7FC",
            "border": "#7FCEEA",
          },
          "success": {
            "background": "#ECF5F3",
            "border": "#89C1B3",
          },
        },
        "backToTop": {
          "background": "#FFFFFF",
          "border": "#E2E8EE",
          "hover": "#949494",
        },
        "background": {
          "card": {
            "hover": "#949494",
            "keyline": "#E2E8EE",
            "primary": "#FFFFFF",
            "secondary": "#FFFFFF",
          },
          "modal": {
            "keyline": "#E2E8EE",
            "overlay": "rgba(0, 0, 0, 0.5)",
            "primary": "#FFFFFF",
          },
          "primary": "#FFFFFF",
        },
        "brandColors": {
          "dark": {
            "black": "#757575",
            "blue": "#91ddf8",
            "green": "#84d9c6",
            "orange": "#ffba8e",
            "pink": "#fda8de",
            "purple": "#aea9f4",
            "red": "#ff969c",
            "white": "#333333",
            "yellow": "#ffeaa5",
          },
          "light": {
            "black": "#333333",
            "blue": "#009dd6",
            "darkBlue": "#007EAB",
            "darkRed": "#BE061B",
            "green": "#138468",
            "orange": "#fb6330",
            "pink": "#f2609e",
            "purple": "#7849b8",
            "red": "#ec111a",
            "white": "#FFFFFF",
            "yellow": "#ffd42f",
          },
        },
        "button": {
          "close": {
            "color": "#333333",
            "hover": "#949494",
          },
          "disabled": {
            "background": "#F6F6F6",
            "border": "#D6D6D6",
            "color": "#757575",
          },
          "navigationButtons": {
            "backButtonColor": "#333333",
            "continueButtonColor": "#ec111a",
          },
          "pillButton": {
            "background": "transparent",
            "border": "#333333",
            "caution": {
              "color": "#ec111a",
            },
            "color": "#333333",
          },
          "primary": {
            "background": "#ec111a",
            "border": "#ec111a",
            "color": "#FFFFFF",
          },
          "secondary": {
            "background": "transparent",
            "border": "#ec111a",
            "color": "#ec111a",
          },
        },
        "canvasGrey": {
          "100": "#FAFBFD",
          "200": "#F6F7FC",
          "300": "#F6F6F6",
          "400": "#E2E8EE",
          "500": "#D6D6D6",
          "600": "#757575",
          "700": "#949494",
        },
        "canvasShadow": "rgba(0, 34, 91, 0.11)",
        "charts": {
          "lineChart": {
            "axisLineColor": "#757575",
            "gridLineColor": "#D6D6D6",
            "tooltip": {
              "borderColor": "#949494",
            },
          },
          "stackedColumn": {
            "axisLineColor": "#757575",
            "dataSetColors": {
              "limitedRetail": [
                "#333333",
                "#009DD6",
                "#7849B8",
                "#138468",
                "#FB6330",
              ],
              "retail": [
                "#009DD6",
                "#7849B8",
                "#138468",
                "#F2609E",
                "#F2609E",
              ],
            },
            "gridLineColor": "#D6D6D6",
            "tooltip": {
              "borderColor": "#383838",
            },
          },
        },
        "checkbox": {
          "border": "#757575",
          "checked": "#007EAB",
          "focus": {
            "boxShadowColor": {
              "a": "#FFFFFF",
              "b": "#007EAB",
            },
          },
          "indeterminate": "#949494",
        },
        "common": {
          "black": "#333333",
          "white": "#FFFFFF",
        },
        "error": "#be061b",
        "focusOutline": "#007EAB",
        "footer": {
          "base": {
            "background": "#F6F7FC",
          },
          "border": "#E2E8EE",
          "link": {
            "hover": "#333333",
          },
          "logo": {
            "fill": "#ec111a",
          },
        },
        "hamburger": {
          "menu": {
            "background": "#FFFFFF",
          },
        },
        "icons": {
          "category": {
            "circle": "#E2E8EE",
            "path": "#757575",
          },
          "functional": {
            "black": "#333333",
            "blue": "#007EAB",
            "darkBlue": "#007EAB",
            "darkRed": "#BE061B",
            "green": "#138468",
            "orange": "#fb6330",
            "pink": "#f2609e",
            "purple": "#7849b8",
            "red": "#ec111a",
            "white": "#FFFFFF",
            "yellow": "#ffd42f",
          },
        },
        "link": {
          "emphasis": {
            "color": "#007EAB",
            "hover": "#005E80",
          },
        },
        "loadingIndicator": {
          "background": "#949494",
        },
        "meter": {
          "borderColor": "#949494",
          "complete": "#138468",
          "incomplete": "#F6F6F6",
        },
        "pagination": {
          "disabled": "#949494",
          "selected": "#ec111a",
        },
        "pinTextField": {
          "pinItem": {
            "backgroundGray": "#F6F7FC",
          },
        },
        "primary": "#009dd6",
        "quickActionCard": {
          "chevron": {
            "black": "#333333",
          },
        },
        "radio": {
          "focus": {
            "boxShadowColor": {
              "a": "#FFFFFF",
              "b": "#007EAB",
            },
          },
        },
        "search": {
          "border": "#757575",
        },
        "snackBar": {
          "background": "#2c2c2e",
          "button": {
            "color": "#009DD6",
            "hover": "#05BCFF",
          },
          "color": "#FFFFFF",
        },
        "stepTracker": {
          "border": "#949494",
          "incompleteBackground": "#D6D6D6",
        },
        "tabs": {
          "border": "#383838",
          "hover": "#757575",
        },
        "text": {
          "disabled": "#949494",
          "highEmphasis": "#333333",
          "mediumEmphasis": "#757575",
          "placeholderText": "#949494",
        },
        "textArea": {
          "border": "#757575",
        },
        "toggleSwitch": {
          "iconOff": "#949494",
          "iconOn": "#007EAB",
        },
        "tooltip": {
          "color": {
            "blue": "#009dd6",
            "default": "#333333",
          },
        },
      },
      "tokens": {
        "action": {
          "focus": {
            "default": "#007EAB",
            "outline": "#009DD6",
          },
          "hover": {
            "background": {
              "actionMenuList": {
                "default": "rgba(226,232,238,0.5)",
              },
              "pillButton": {
                "caution": "#BE061B",
                "default": "#333333",
                "inverted": "#FFFFFF",
              },
              "pillButtonPrimary": {
                "default": "#BE061B",
                "inverted": "#333333",
              },
              "pillButtonSecondary": {
                "black": "#333333",
                "red": "#BE061B",
                "textOnly": "#333333",
              },
            },
            "border": {
              "pillButtonSecondary": {
                "black": "#333333",
                "red": "#BE061B",
                "textOnly": "transparent",
              },
            },
            "default": "#005E80",
            "text": {
              "pillButton": {
                "caution": "#FFFFFF",
                "default": "#FFFFFF",
              },
              "pillButtonPrimary": {
                "default": "#FFFFFF",
                "inverted": "#FFFFFF",
              },
              "pillButtonSecondary": {
                "black": "#FFFFFF",
                "red": "#FFFFFF",
                "textOnly": "#FFFFFF",
              },
              "snackbar": {
                "button": "#05BCFF",
              },
            },
          },
        },
        "background": {
          "alertBanner": {
            "alert": "rgba(251,99,48,0.08)",
            "error": "rgba(236,17,26,0.08)",
            "info": "rgba(0,157,214,0.08)",
            "new": "rgba(120,73,184,0.08)",
            "success": "rgba(19,132,104,0.08)",
          },
          "base": "#FFFFFF",
          "comboBox": {
            "default": "#FFFFFF",
            "listBoxItem": {
              "hover": "#F6F6F6",
            },
          },
          "footer": {
            "base": {
              "grey": "#F6F7FC",
              "white": "#FFFFFF",
            },
            "logo": {
              "default": "#EC111A",
            },
          },
          "header": {
            "black": "#333333",
            "red": "#EC111A",
            "white": "#FFFFFF",
          },
          "meter": {
            "default": "#138468",
            "incomplete": "#F6F6F6",
          },
          "modal": "#FFFFFF",
          "navigation": {
            "default": "#FFFFFF",
          },
          "overlay": "rgb(255,255,255,0.50)",
          "pillButtonPrimary": {
            "default": "#EC111A",
            "inverted": "#FFFFFF",
          },
          "pillButtonSecondary": {
            "black": "transparent",
            "red": "transparent",
            "textOnly": "transparent",
          },
          "primary": "#FFFFFF",
          "progressIndicator": {
            "bar": {
              "default": "#138468",
            },
            "container": {
              "default": "#F6F6F6",
            },
          },
          "secondary": "#FFFFFF",
          "skeleton": "#949494",
          "snackbar": "#2C2C2C",
          "statusBadge": {
            "default": "transparent",
            "emphasis": "transparent",
            "error": "transparent",
            "new": "#7849B8",
            "success": "#138468",
            "success-emphasis": "transparent",
          },
          "table": {
            "row": {
              "alternate": "#FAFBFD",
              "hover": "rgba(226, 232, 238, 0.4)",
            },
          },
        },
        "border": {
          "alertBanner": {
            "alert": "rgba(251,99,48,0.5)",
            "error": "rgba(236,17,26,0.5)",
            "info": "rgba(0,157,214,0.5)",
            "new": "rgba(120,73,184,0.5)",
            "success": "rgba(19,132,104,0.5)",
          },
          "card": "#E2E8EE",
          "default": "#707070",
          "header": {
            "white": "#E2E8EE",
          },
          "meter": {
            "default": "#949494",
          },
          "pillButton": {
            "caution": "#EC111A",
            "default": "#333333",
          },
          "pillButtonPrimary": {
            "default": "#EC111A",
            "inverted": "#FFFFFF",
          },
          "pillButtonSecondary": {
            "black": "#333333",
            "red": "#EC111A",
            "textOnly": "transparent",
          },
          "progressIndicator": {
            "container": {
              "default": "#949494",
            },
          },
          "skeleton": "#949494",
          "statusBadge": {
            "default": "#707070",
            "emphasis": "#007EAB",
            "error": "#BE061B",
            "new": "#7849B8",
            "success": "#138468",
            "success-emphasis": "#138468",
          },
        },
        "grid": {
          "breakPoints": {
            "lg": {
              "gutter": 3,
              "margin": "3.6rem",
              "maxCols": 12,
              "maxWidth": "4000px",
              "minWidth": "1025px",
              "rowMargin": "-1.8rem",
              "size": "lg",
            },
            "md": {
              "gutter": 3,
              "margin": [
                "3.6rem",
                "4.2rem",
                "4.8rem",
                "5.4rem",
              ],
              "maxCols": 12,
              "maxWidth": "1024px",
              "minWidth": "768px",
              "rowMargin": "-1.5rem",
              "size": "md",
            },
            "sm": {
              "gutter": 1.8,
              "margin": [
                "2.4rem",
                "3.0rem",
                "3.6rem",
                "4.2rem",
              ],
              "maxCols": 8,
              "maxWidth": "767px",
              "minWidth": "481px",
              "rowMargin": "-1.2rem",
              "size": "sm",
            },
            "xs": {
              "gutter": 1.8,
              "margin": [
                "1.8rem",
                "2.4rem",
                "3.0rem",
                "3.6rem",
              ],
              "maxCols": 4,
              "maxWidth": "480px",
              "minWidth": "0px",
              "rowMargin": "-0.6rem",
              "size": "xs",
            },
          },
          "containerMaxWidth": 1200,
          "containerMaxWidthMargins": 1272,
          "containerMinWidth": 320,
          "gridColumns": 12,
        },
        "icons": {
          "functional": {
            "black": "#333333",
            "blue": "#007EAB",
            "darkBlue": "#007EAB",
            "darkGray": "#707070",
            "darkRed": "#BE061B",
            "gray": "#949494",
            "green": "#138468",
            "orange": "#FB6330",
            "pink": "#F2609E",
            "purple": "#7849B8",
            "red": "#EC111A",
            "white": "#FFFFFF",
            "yellow": "#FFD42F",
          },
        },
        "loadingIndicator": {
          "darkGray": "#949494",
          "red": "#EC111A",
        },
        "mode": "light",
        "palette": {
          "color": {
            "base": "#FFFFFF",
            "black": "#333333",
            "blue": "#009DD6",
            "brandRed": "#EC111A",
            "darkBlue": "#007EAB",
            "darkBlue100": "#005E80",
            "darkGreen": "#117E63",
            "darkRed": "#BE061B",
            "darkRed100": "#BE061B",
            "green": "#138468",
            "green100": "rgb(132,217,198,0.15)",
            "orange": "#FB6330",
            "pink": "#F2609E",
            "primaryRed": "#EC111A",
            "purple": "#7849B8",
            "yellow": "#FFD42F",
          },
          "gray": {
            "0": "#FFFFFF",
            "100": "#FAFBFD",
            "200": "#F6F7FC",
            "300": "#F6F6F6",
            "400": "#E2E8EE",
            "500": "#D6D6D6",
            "550": "#949494",
            "600": "#707070",
            "700": "#333333",
            "800": "#2C2C2C",
          },
        },
        "pillButtonPrimary": {
          "default": {
            "backgroundColor": "#EC111A",
            "borderColor": "#EC111A",
            "textColor": "#FFFFFF",
          },
          "hover": {
            "default": {
              "backgroundColor": "#BE061B",
              "borderColor": "#BE061B",
              "textColor": "#FFFFFF",
            },
            "inverted": {
              "backgroundColor": "#333333",
              "borderColor": "#333333",
              "textColor": "#FFFFFF",
            },
          },
          "inverted": {
            "backgroundColor": "#FFFFFF",
            "borderColor": "#FFFFFF",
            "textColor": "#333333",
          },
        },
        "primary": {
          "dark": "#BE061B",
          "main": "#EC111A",
        },
        "secondary": {
          "dark": "#005E80",
          "main": "#007EAB",
        },
        "secondaryButton": {
          "default": {
            "backgroundColor": "#FFFFFF",
            "borderColor": "#EC111A",
            "textColor": "#EC111A",
          },
          "hover": {
            "default": {
              "backgroundColor": "#BE061B",
              "borderColor": "#BE061B",
              "textColor": "#FFFFFF",
            },
          },
        },
        "sidesheet": {
          "overlay": {
            "background": "#FFFFFF",
          },
          "persistent": {
            "background": "#FFFFFF",
          },
        },
        "size": {
          "borderRadius": {
            "0": "10rem",
            "100": "0.4rem",
            "200": "0.8rem",
            "300": "1.2rem",
            "50": "0.2rem",
          },
          "borderWidth": {
            "1": "0.1rem",
            "2": "0.2rem",
            "3": "0.3rem",
            "5": "0.5rem",
          },
          "breakPoints": {
            "lg": "1024px",
            "md": "768px",
            "sm": "375px",
            "xs": "0px",
          },
          "sizing": {
            "24": "2.4rem",
            "32": "3.2rem",
            "36": "3.6rem",
            "42": "4.2rem",
            "44": "4.4rem",
            "48": "4.8rem",
            "54": "5.4rem",
            "60": "6.0rem",
            "72": "7.2rem",
          },
          "spacing": {
            "1": "0.6rem",
            "10": "6.0rem",
            "11": "6.6rem",
            "12": "7.2rem",
            "13": "7.8rem",
            "14": "8.4rem",
            "15": "9.0rem",
            "16": "9.6rem",
            "17": "10.2rem",
            "18": "10.8rem",
            "19": "11.4rem",
            "2": "1.2rem",
            "20": "12.0rem",
            "3": "1.8rem",
            "4": "2.4rem",
            "5": "3.0rem",
            "6": "3.6rem",
            "7": "4.2rem",
            "8": "4.8rem",
            "9": "5.4rem",
          },
        },
        "state": {
          "disabled": {
            "background": "#F6F6F6",
            "border": "#D6D6D6",
            "text": "#949494",
          },
          "error": {
            "default": "#BE061B",
          },
        },
        "text": {
          "highEmphasis": "#333333",
          "medEmphasis": "#707070",
          "pillButton": {
            "caution": "#EC111A",
            "default": "#333333",
            "inverted": "#333333",
          },
          "pillButtonPrimary": {
            "default": "#FFFFFF",
            "inverted": "#333333",
          },
          "pillButtonSecondary": {
            "black": "#333333",
            "red": "#EC111A",
            "textOnly": "#333333",
          },
          "placeholder": "#707070",
          "secondaryButton": "#EC111A",
          "snackbar": {
            "button": "#009DD6",
            "content": "#E0E0E0",
          },
          "statusBadge": {
            "default": "#707070",
            "emphasis": "#007EAB",
            "error": "#BE061B",
            "new": "#FFFFFF",
            "success": "#FFFFFF",
            "success-emphasis": "#138468",
          },
        },
        "theme": "default",
        "transform": {
          "elevation": {
            "100": "0px 2px 10px rgba(0, 34, 91, 0.11)",
          },
          "motion": {
            "100": "200ms",
            "150": "350ms",
            "200": "500ms",
            "300": "0.1s",
            "400": "0.2s",
            "500": "0.3s",
            "600": "0.4s",
            "700": "0.5s",
            "800": "0.6s",
          },
          "opacity": {
            "0": 0,
            "100": 1,
            "11": 0.11,
            "15": 0.15,
            "16": 0.16,
            "2": 0.02,
            "3": 0.03,
            "38": 0.38,
            "5": 0.05,
            "50": 0.5,
            "60": 0.6,
            "87": 0.87,
          },
        },
        "type": "light",
        "typography": {
          "lineHeight": {
            "100": "1.8rem",
            "1000": "5.4rem",
            "200": "2.0rem",
            "300": "2.1rem",
            "400": "2.2rem",
            "500": "2.4rem",
            "600": "2.6rem",
            "700": "2.7rem",
            "800": "3.0rem",
            "850": "3.5rem",
            "900": "4.0rem",
            "910": "4.1rem",
          },
          "size": {
            "100": "1.2rem",
            "1000": "3.6rem",
            "1100": "4.8rem",
            "200": "1.4rem",
            "300": "1.6rem",
            "400": "1.8rem",
            "500": "2.0rem",
            "600": "2.1rem",
            "700": "2.4rem",
            "800": "2.8rem",
            "900": "3.2rem",
          },
          "weight": {
            "bold": ""Scotia Bold", "Arial", "Helvetica", "sans-serif"",
            "boldItalics": ""Scotia Bold Italic", "Arial", "Helvetica", "sans-serif"",
            "headline": ""Scotia Headline", "Arial", "Helvetica", "sans-serif"",
            "italic": ""Scotia Italic", "Arial", "Helvetica", "sans-serif"",
            "legal": ""Scotia Legal", "Arial", "Helvetica", "sans-serif"",
            "legalItalic": ""Scotia Legal Italic", "Arial", "Helvetica", "sans-serif"",
            "regular": ""Scotia Regular", "Arial", "Helvetica", "sans-serif"",
          },
        },
      },
      "transitions": {
        "duaration": {
          "sideSheetMainWrapper": "600ms",
        },
        "effect": {
          "easing": {
            "easeInOut": "ease-in-out",
          },
        },
      },
      "type": "light",
      "typography": {
        "fontBoldFamily": ""Scotia Bold", "Arial", "Helvetica", "sans-serif"",
        "fontBoldItalicFamily": ""Scotia Bold Italic", "Arial", "Helvetica", "sans-serif"",
        "fontHeadlineFamily": ""Scotia Headline", "Arial", "Helvetica", "sans-serif"",
        "fontItalicFamily": ""Scotia Italic", "Arial", "Helvetica", "sans-serif"",
        "fontLightFamily": ""Scotia Light", "Arial", "Helvetica", "sans-serif"",
        "fontRegularFamily": ""Scotia Regular", "Arial", "Helvetica", "sans-serif"",
      },
    }
  }
>
  Pigeon Team
</TextCaptionstyle__Text>
`;

exports[`UsersColumns team col 2`] = `
<TextCaptionstyle__Text
  as="p"
  bold={false}
  className="TextCaption__text"
  color="black"
  italic={false}
  numeric={false}
  theme={
    {
      "breakPoints": {
        "values": {
          "lg": 1025,
          "md": 768,
          "sm": 481,
          "xs": 0,
        },
      },
      "palette": {
        "actionMenuList": {
          "item": {
            "background": {
              "hover": "#F6F7FC",
            },
            "text": "#333",
          },
          "menuButton": {
            "background": {
              "hover": "#F6F7FC",
            },
            "border": {
              "focus": "#007EAB",
            },
            "text": "#333",
          },
        },
        "alertBanner": {
          "alert": {
            "background": "#FFF3EF",
            "border": "#FDB197",
          },
          "error": {
            "background": "#FEECED",
            "border": "#F5888C",
          },
          "info": {
            "background": "#EBF7FC",
            "border": "#7FCEEA",
          },
          "success": {
            "background": "#ECF5F3",
            "border": "#89C1B3",
          },
        },
        "backToTop": {
          "background": "#FFFFFF",
          "border": "#E2E8EE",
          "hover": "#949494",
        },
        "background": {
          "card": {
            "hover": "#949494",
            "keyline": "#E2E8EE",
            "primary": "#FFFFFF",
            "secondary": "#FFFFFF",
          },
          "modal": {
            "keyline": "#E2E8EE",
            "overlay": "rgba(0, 0, 0, 0.5)",
            "primary": "#FFFFFF",
          },
          "primary": "#FFFFFF",
        },
        "brandColors": {
          "dark": {
            "black": "#757575",
            "blue": "#91ddf8",
            "green": "#84d9c6",
            "orange": "#ffba8e",
            "pink": "#fda8de",
            "purple": "#aea9f4",
            "red": "#ff969c",
            "white": "#333333",
            "yellow": "#ffeaa5",
          },
          "light": {
            "black": "#333333",
            "blue": "#009dd6",
            "darkBlue": "#007EAB",
            "darkRed": "#BE061B",
            "green": "#138468",
            "orange": "#fb6330",
            "pink": "#f2609e",
            "purple": "#7849b8",
            "red": "#ec111a",
            "white": "#FFFFFF",
            "yellow": "#ffd42f",
          },
        },
        "button": {
          "close": {
            "color": "#333333",
            "hover": "#949494",
          },
          "disabled": {
            "background": "#F6F6F6",
            "border": "#D6D6D6",
            "color": "#757575",
          },
          "navigationButtons": {
            "backButtonColor": "#333333",
            "continueButtonColor": "#ec111a",
          },
          "pillButton": {
            "background": "transparent",
            "border": "#333333",
            "caution": {
              "color": "#ec111a",
            },
            "color": "#333333",
          },
          "primary": {
            "background": "#ec111a",
            "border": "#ec111a",
            "color": "#FFFFFF",
          },
          "secondary": {
            "background": "transparent",
            "border": "#ec111a",
            "color": "#ec111a",
          },
        },
        "canvasGrey": {
          "100": "#FAFBFD",
          "200": "#F6F7FC",
          "300": "#F6F6F6",
          "400": "#E2E8EE",
          "500": "#D6D6D6",
          "600": "#757575",
          "700": "#949494",
        },
        "canvasShadow": "rgba(0, 34, 91, 0.11)",
        "charts": {
          "lineChart": {
            "axisLineColor": "#757575",
            "gridLineColor": "#D6D6D6",
            "tooltip": {
              "borderColor": "#949494",
            },
          },
          "stackedColumn": {
            "axisLineColor": "#757575",
            "dataSetColors": {
              "limitedRetail": [
                "#333333",
                "#009DD6",
                "#7849B8",
                "#138468",
                "#FB6330",
              ],
              "retail": [
                "#009DD6",
                "#7849B8",
                "#138468",
                "#F2609E",
                "#F2609E",
              ],
            },
            "gridLineColor": "#D6D6D6",
            "tooltip": {
              "borderColor": "#383838",
            },
          },
        },
        "checkbox": {
          "border": "#757575",
          "checked": "#007EAB",
          "focus": {
            "boxShadowColor": {
              "a": "#FFFFFF",
              "b": "#007EAB",
            },
          },
          "indeterminate": "#949494",
        },
        "common": {
          "black": "#333333",
          "white": "#FFFFFF",
        },
        "error": "#be061b",
        "focusOutline": "#007EAB",
        "footer": {
          "base": {
            "background": "#F6F7FC",
          },
          "border": "#E2E8EE",
          "link": {
            "hover": "#333333",
          },
          "logo": {
            "fill": "#ec111a",
          },
        },
        "hamburger": {
          "menu": {
            "background": "#FFFFFF",
          },
        },
        "icons": {
          "category": {
            "circle": "#E2E8EE",
            "path": "#757575",
          },
          "functional": {
            "black": "#333333",
            "blue": "#007EAB",
            "darkBlue": "#007EAB",
            "darkRed": "#BE061B",
            "green": "#138468",
            "orange": "#fb6330",
            "pink": "#f2609e",
            "purple": "#7849b8",
            "red": "#ec111a",
            "white": "#FFFFFF",
            "yellow": "#ffd42f",
          },
        },
        "link": {
          "emphasis": {
            "color": "#007EAB",
            "hover": "#005E80",
          },
        },
        "loadingIndicator": {
          "background": "#949494",
        },
        "meter": {
          "borderColor": "#949494",
          "complete": "#138468",
          "incomplete": "#F6F6F6",
        },
        "pagination": {
          "disabled": "#949494",
          "selected": "#ec111a",
        },
        "pinTextField": {
          "pinItem": {
            "backgroundGray": "#F6F7FC",
          },
        },
        "primary": "#009dd6",
        "quickActionCard": {
          "chevron": {
            "black": "#333333",
          },
        },
        "radio": {
          "focus": {
            "boxShadowColor": {
              "a": "#FFFFFF",
              "b": "#007EAB",
            },
          },
        },
        "search": {
          "border": "#757575",
        },
        "snackBar": {
          "background": "#2c2c2e",
          "button": {
            "color": "#009DD6",
            "hover": "#05BCFF",
          },
          "color": "#FFFFFF",
        },
        "stepTracker": {
          "border": "#949494",
          "incompleteBackground": "#D6D6D6",
        },
        "tabs": {
          "border": "#383838",
          "hover": "#757575",
        },
        "text": {
          "disabled": "#949494",
          "highEmphasis": "#333333",
          "mediumEmphasis": "#757575",
          "placeholderText": "#949494",
        },
        "textArea": {
          "border": "#757575",
        },
        "toggleSwitch": {
          "iconOff": "#949494",
          "iconOn": "#007EAB",
        },
        "tooltip": {
          "color": {
            "blue": "#009dd6",
            "default": "#333333",
          },
        },
      },
      "tokens": {
        "action": {
          "focus": {
            "default": "#007EAB",
            "outline": "#009DD6",
          },
          "hover": {
            "background": {
              "actionMenuList": {
                "default": "rgba(226,232,238,0.5)",
              },
              "pillButton": {
                "caution": "#BE061B",
                "default": "#333333",
                "inverted": "#FFFFFF",
              },
              "pillButtonPrimary": {
                "default": "#BE061B",
                "inverted": "#333333",
              },
              "pillButtonSecondary": {
                "black": "#333333",
                "red": "#BE061B",
                "textOnly": "#333333",
              },
            },
            "border": {
              "pillButtonSecondary": {
                "black": "#333333",
                "red": "#BE061B",
                "textOnly": "transparent",
              },
            },
            "default": "#005E80",
            "text": {
              "pillButton": {
                "caution": "#FFFFFF",
                "default": "#FFFFFF",
              },
              "pillButtonPrimary": {
                "default": "#FFFFFF",
                "inverted": "#FFFFFF",
              },
              "pillButtonSecondary": {
                "black": "#FFFFFF",
                "red": "#FFFFFF",
                "textOnly": "#FFFFFF",
              },
              "snackbar": {
                "button": "#05BCFF",
              },
            },
          },
        },
        "background": {
          "alertBanner": {
            "alert": "rgba(251,99,48,0.08)",
            "error": "rgba(236,17,26,0.08)",
            "info": "rgba(0,157,214,0.08)",
            "new": "rgba(120,73,184,0.08)",
            "success": "rgba(19,132,104,0.08)",
          },
          "base": "#FFFFFF",
          "comboBox": {
            "default": "#FFFFFF",
            "listBoxItem": {
              "hover": "#F6F6F6",
            },
          },
          "footer": {
            "base": {
              "grey": "#F6F7FC",
              "white": "#FFFFFF",
            },
            "logo": {
              "default": "#EC111A",
            },
          },
          "header": {
            "black": "#333333",
            "red": "#EC111A",
            "white": "#FFFFFF",
          },
          "meter": {
            "default": "#138468",
            "incomplete": "#F6F6F6",
          },
          "modal": "#FFFFFF",
          "navigation": {
            "default": "#FFFFFF",
          },
          "overlay": "rgb(255,255,255,0.50)",
          "pillButtonPrimary": {
            "default": "#EC111A",
            "inverted": "#FFFFFF",
          },
          "pillButtonSecondary": {
            "black": "transparent",
            "red": "transparent",
            "textOnly": "transparent",
          },
          "primary": "#FFFFFF",
          "progressIndicator": {
            "bar": {
              "default": "#138468",
            },
            "container": {
              "default": "#F6F6F6",
            },
          },
          "secondary": "#FFFFFF",
          "skeleton": "#949494",
          "snackbar": "#2C2C2C",
          "statusBadge": {
            "default": "transparent",
            "emphasis": "transparent",
            "error": "transparent",
            "new": "#7849B8",
            "success": "#138468",
            "success-emphasis": "transparent",
          },
          "table": {
            "row": {
              "alternate": "#FAFBFD",
              "hover": "rgba(226, 232, 238, 0.4)",
            },
          },
        },
        "border": {
          "alertBanner": {
            "alert": "rgba(251,99,48,0.5)",
            "error": "rgba(236,17,26,0.5)",
            "info": "rgba(0,157,214,0.5)",
            "new": "rgba(120,73,184,0.5)",
            "success": "rgba(19,132,104,0.5)",
          },
          "card": "#E2E8EE",
          "default": "#707070",
          "header": {
            "white": "#E2E8EE",
          },
          "meter": {
            "default": "#949494",
          },
          "pillButton": {
            "caution": "#EC111A",
            "default": "#333333",
          },
          "pillButtonPrimary": {
            "default": "#EC111A",
            "inverted": "#FFFFFF",
          },
          "pillButtonSecondary": {
            "black": "#333333",
            "red": "#EC111A",
            "textOnly": "transparent",
          },
          "progressIndicator": {
            "container": {
              "default": "#949494",
            },
          },
          "skeleton": "#949494",
          "statusBadge": {
            "default": "#707070",
            "emphasis": "#007EAB",
            "error": "#BE061B",
            "new": "#7849B8",
            "success": "#138468",
            "success-emphasis": "#138468",
          },
        },
        "grid": {
          "breakPoints": {
            "lg": {
              "gutter": 3,
              "margin": "3.6rem",
              "maxCols": 12,
              "maxWidth": "4000px",
              "minWidth": "1025px",
              "rowMargin": "-1.8rem",
              "size": "lg",
            },
            "md": {
              "gutter": 3,
              "margin": [
                "3.6rem",
                "4.2rem",
                "4.8rem",
                "5.4rem",
              ],
              "maxCols": 12,
              "maxWidth": "1024px",
              "minWidth": "768px",
              "rowMargin": "-1.5rem",
              "size": "md",
            },
            "sm": {
              "gutter": 1.8,
              "margin": [
                "2.4rem",
                "3.0rem",
                "3.6rem",
                "4.2rem",
              ],
              "maxCols": 8,
              "maxWidth": "767px",
              "minWidth": "481px",
              "rowMargin": "-1.2rem",
              "size": "sm",
            },
            "xs": {
              "gutter": 1.8,
              "margin": [
                "1.8rem",
                "2.4rem",
                "3.0rem",
                "3.6rem",
              ],
              "maxCols": 4,
              "maxWidth": "480px",
              "minWidth": "0px",
              "rowMargin": "-0.6rem",
              "size": "xs",
            },
          },
          "containerMaxWidth": 1200,
          "containerMaxWidthMargins": 1272,
          "containerMinWidth": 320,
          "gridColumns": 12,
        },
        "icons": {
          "functional": {
            "black": "#333333",
            "blue": "#007EAB",
            "darkBlue": "#007EAB",
            "darkGray": "#707070",
            "darkRed": "#BE061B",
            "gray": "#949494",
            "green": "#138468",
            "orange": "#FB6330",
            "pink": "#F2609E",
            "purple": "#7849B8",
            "red": "#EC111A",
            "white": "#FFFFFF",
            "yellow": "#FFD42F",
          },
        },
        "loadingIndicator": {
          "darkGray": "#949494",
          "red": "#EC111A",
        },
        "mode": "light",
        "palette": {
          "color": {
            "base": "#FFFFFF",
            "black": "#333333",
            "blue": "#009DD6",
            "brandRed": "#EC111A",
            "darkBlue": "#007EAB",
            "darkBlue100": "#005E80",
            "darkGreen": "#117E63",
            "darkRed": "#BE061B",
            "darkRed100": "#BE061B",
            "green": "#138468",
            "green100": "rgb(132,217,198,0.15)",
            "orange": "#FB6330",
            "pink": "#F2609E",
            "primaryRed": "#EC111A",
            "purple": "#7849B8",
            "yellow": "#FFD42F",
          },
          "gray": {
            "0": "#FFFFFF",
            "100": "#FAFBFD",
            "200": "#F6F7FC",
            "300": "#F6F6F6",
            "400": "#E2E8EE",
            "500": "#D6D6D6",
            "550": "#949494",
            "600": "#707070",
            "700": "#333333",
            "800": "#2C2C2C",
          },
        },
        "pillButtonPrimary": {
          "default": {
            "backgroundColor": "#EC111A",
            "borderColor": "#EC111A",
            "textColor": "#FFFFFF",
          },
          "hover": {
            "default": {
              "backgroundColor": "#BE061B",
              "borderColor": "#BE061B",
              "textColor": "#FFFFFF",
            },
            "inverted": {
              "backgroundColor": "#333333",
              "borderColor": "#333333",
              "textColor": "#FFFFFF",
            },
          },
          "inverted": {
            "backgroundColor": "#FFFFFF",
            "borderColor": "#FFFFFF",
            "textColor": "#333333",
          },
        },
        "primary": {
          "dark": "#BE061B",
          "main": "#EC111A",
        },
        "secondary": {
          "dark": "#005E80",
          "main": "#007EAB",
        },
        "secondaryButton": {
          "default": {
            "backgroundColor": "#FFFFFF",
            "borderColor": "#EC111A",
            "textColor": "#EC111A",
          },
          "hover": {
            "default": {
              "backgroundColor": "#BE061B",
              "borderColor": "#BE061B",
              "textColor": "#FFFFFF",
            },
          },
        },
        "sidesheet": {
          "overlay": {
            "background": "#FFFFFF",
          },
          "persistent": {
            "background": "#FFFFFF",
          },
        },
        "size": {
          "borderRadius": {
            "0": "10rem",
            "100": "0.4rem",
            "200": "0.8rem",
            "300": "1.2rem",
            "50": "0.2rem",
          },
          "borderWidth": {
            "1": "0.1rem",
            "2": "0.2rem",
            "3": "0.3rem",
            "5": "0.5rem",
          },
          "breakPoints": {
            "lg": "1024px",
            "md": "768px",
            "sm": "375px",
            "xs": "0px",
          },
          "sizing": {
            "24": "2.4rem",
            "32": "3.2rem",
            "36": "3.6rem",
            "42": "4.2rem",
            "44": "4.4rem",
            "48": "4.8rem",
            "54": "5.4rem",
            "60": "6.0rem",
            "72": "7.2rem",
          },
          "spacing": {
            "1": "0.6rem",
            "10": "6.0rem",
            "11": "6.6rem",
            "12": "7.2rem",
            "13": "7.8rem",
            "14": "8.4rem",
            "15": "9.0rem",
            "16": "9.6rem",
            "17": "10.2rem",
            "18": "10.8rem",
            "19": "11.4rem",
            "2": "1.2rem",
            "20": "12.0rem",
            "3": "1.8rem",
            "4": "2.4rem",
            "5": "3.0rem",
            "6": "3.6rem",
            "7": "4.2rem",
            "8": "4.8rem",
            "9": "5.4rem",
          },
        },
        "state": {
          "disabled": {
            "background": "#F6F6F6",
            "border": "#D6D6D6",
            "text": "#949494",
          },
          "error": {
            "default": "#BE061B",
          },
        },
        "text": {
          "highEmphasis": "#333333",
          "medEmphasis": "#707070",
          "pillButton": {
            "caution": "#EC111A",
            "default": "#333333",
            "inverted": "#333333",
          },
          "pillButtonPrimary": {
            "default": "#FFFFFF",
            "inverted": "#333333",
          },
          "pillButtonSecondary": {
            "black": "#333333",
            "red": "#EC111A",
            "textOnly": "#333333",
          },
          "placeholder": "#707070",
          "secondaryButton": "#EC111A",
          "snackbar": {
            "button": "#009DD6",
            "content": "#E0E0E0",
          },
          "statusBadge": {
            "default": "#707070",
            "emphasis": "#007EAB",
            "error": "#BE061B",
            "new": "#FFFFFF",
            "success": "#FFFFFF",
            "success-emphasis": "#138468",
          },
        },
        "theme": "default",
        "transform": {
          "elevation": {
            "100": "0px 2px 10px rgba(0, 34, 91, 0.11)",
          },
          "motion": {
            "100": "200ms",
            "150": "350ms",
            "200": "500ms",
            "300": "0.1s",
            "400": "0.2s",
            "500": "0.3s",
            "600": "0.4s",
            "700": "0.5s",
            "800": "0.6s",
          },
          "opacity": {
            "0": 0,
            "100": 1,
            "11": 0.11,
            "15": 0.15,
            "16": 0.16,
            "2": 0.02,
            "3": 0.03,
            "38": 0.38,
            "5": 0.05,
            "50": 0.5,
            "60": 0.6,
            "87": 0.87,
          },
        },
        "type": "light",
        "typography": {
          "lineHeight": {
            "100": "1.8rem",
            "1000": "5.4rem",
            "200": "2.0rem",
            "300": "2.1rem",
            "400": "2.2rem",
            "500": "2.4rem",
            "600": "2.6rem",
            "700": "2.7rem",
            "800": "3.0rem",
            "850": "3.5rem",
            "900": "4.0rem",
            "910": "4.1rem",
          },
          "size": {
            "100": "1.2rem",
            "1000": "3.6rem",
            "1100": "4.8rem",
            "200": "1.4rem",
            "300": "1.6rem",
            "400": "1.8rem",
            "500": "2.0rem",
            "600": "2.1rem",
            "700": "2.4rem",
            "800": "2.8rem",
            "900": "3.2rem",
          },
          "weight": {
            "bold": ""Scotia Bold", "Arial", "Helvetica", "sans-serif"",
            "boldItalics": ""Scotia Bold Italic", "Arial", "Helvetica", "sans-serif"",
            "headline": ""Scotia Headline", "Arial", "Helvetica", "sans-serif"",
            "italic": ""Scotia Italic", "Arial", "Helvetica", "sans-serif"",
            "legal": ""Scotia Legal", "Arial", "Helvetica", "sans-serif"",
            "legalItalic": ""Scotia Legal Italic", "Arial", "Helvetica", "sans-serif"",
            "regular": ""Scotia Regular", "Arial", "Helvetica", "sans-serif"",
          },
        },
      },
      "transitions": {
        "duaration": {
          "sideSheetMainWrapper": "600ms",
        },
        "effect": {
          "easing": {
            "easeInOut": "ease-in-out",
          },
        },
      },
      "type": "light",
      "typography": {
        "fontBoldFamily": ""Scotia Bold", "Arial", "Helvetica", "sans-serif"",
        "fontBoldItalicFamily": ""Scotia Bold Italic", "Arial", "Helvetica", "sans-serif"",
        "fontHeadlineFamily": ""Scotia Headline", "Arial", "Helvetica", "sans-serif"",
        "fontItalicFamily": ""Scotia Italic", "Arial", "Helvetica", "sans-serif"",
        "fontLightFamily": ""Scotia Light", "Arial", "Helvetica", "sans-serif"",
        "fontRegularFamily": ""Scotia Regular", "Arial", "Helvetica", "sans-serif"",
      },
    }
  }
>
  Pigeon Team
</TextCaptionstyle__Text>
`;
