import React from 'react';
import { render, fireEvent } from '@testing-library/react';

import PermissionTable from './permissionsTable';
const options = [ 'campaigns_view', 'alerts_view', 'applications_view', 'applications_view_super', 'pages_view', 'pages_view_super', 'containers_view', 'containers_view_super', 'teams_view', 'teams_view_super', 'users_view', 'users_view_super', 'roles_view', 'roles_view_super', 'pega_variable_mapping_view', 'campaigns_manage', 'campaigns_review', 'pega_variable_mapping_view', 'pega_variable_mapping_manage', 'pega_variable_mapping_review' ];
describe('PermissionsTable', () => {
  test('Clicking on a checkbox triggers onChange', () => {
    const onChange = jest.fn();
    const { getByLabelText } = render(<PermissionTable input={{ onChange }} options={options} formValues={{ team_id: 1, id: 1 }} roleData={{ team_id: 1 }} />);
    expect(onChange).not.toHaveBeenCalled();
    fireEvent.click(getByLabelText('Toggle view permissions for campaigns'));
    expect(onChange).toHaveBeenCalledWith([ 'campaigns_view' ]);
    expect(getByLabelText('View')).not.toBeChecked();
    onChange.mockReset();
    fireEvent.click(getByLabelText('Toggle view permissions for campaigns'));
    expect(onChange).toHaveBeenCalledWith([]);
    expect(getByLabelText('View')).not.toBeChecked();
  });

  test('Clicking the top level view checkbox will trigger all checkboxes to turn on', () => {
    const onChange = jest.fn();
    const { getByLabelText } = render(<PermissionTable input={{ onChange }} options={options} />);
    fireEvent.click(getByLabelText('View'));
    expect(onChange).toHaveBeenCalledWith([ 'campaigns_view', 'alerts_view', 'applications_view', 'applications_view_super', 'pages_view', 'pages_view_super', 'containers_view', 'containers_view_super', 'teams_view', 'teams_view_super', 'users_view', 'users_view_super', 'roles_view', 'roles_view_super', 'pega_variable_mapping_view' ]);
    onChange.mockReset();
    fireEvent.click(getByLabelText('View'));
    expect(onChange).toHaveBeenCalledWith([]);
    onChange.mockReset();
    fireEvent.click(getByLabelText('Review'));
    expect(onChange).toHaveBeenCalledWith([ 'campaigns_view', 'campaigns_manage', 'campaigns_review', 'pega_variable_mapping_view', 'pega_variable_mapping_manage', 'pega_variable_mapping_review' ]);
  });

  test('Initially populated with values', () => {
    const { getByLabelText } = render(<PermissionTable
      options={options}
      meta={{ initial: [ 'campaigns_view', 'applications_view_super', 'applications_manage_super' ] }}
      roleData={{ permissions: [ 'campaigns_view', 'applications_view_super', 'applications_manage_super' ] }}
    />);
    expect(getByLabelText('Toggle view permissions for campaigns')).toBeChecked();
    expect(getByLabelText('Toggle manage permissions for applications_super')).toBeChecked();
    expect(getByLabelText('View')).not.toBeChecked();
    expect(getByLabelText('Manage')).not.toBeChecked();
  });
});
