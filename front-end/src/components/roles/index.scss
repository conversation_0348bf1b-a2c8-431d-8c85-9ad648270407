@import './details';
@import './list';

.permissions-table {
  display: grid;
  grid-template-columns: 1fr repeat(4, 11.2rem);

  .Checkbox__span {
    margin: 0;
  }

  &__cell {
    border-bottom: 0.1rem solid $canvas-gray-400;
    display: flex;
    align-items: center;

    &--label {
      padding-left: 3rem;
    }

    &--header {
      align-items: flex-end;
    }

    &--checkbox {
      justify-content: center;

      .Label__label {
        display: none;
      }
    }

    &--heading {
      justify-content: center;

      .Checkbox__label {
        display: flex;
        flex-direction: column-reverse;
        align-items: center;
      }

      .Checkbox__span {
        width: 2.4rem;
        margin-top: 1.6rem;
      }
    }

    &--view,
    &--review {
      background-color: $canvas-gray-100;
    }
  }

  &__error-container {
    margin-top: 0.6rem;
    display: flex;
  }

  &__error-message {
    color: $canvas-dark-red;
    margin-left: 1rem;
    font-size: 1.4rem;
  }
}
