// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`RolesList - loading  Snapshot 1`] = `
<div
  className="admin-list"
>
  <ForwardRef
    className="admin-list__table"
    columnFixed={false}
    columns={[]}
    data={[]}
    defaultSortOrder="desc"
    highlightOnHover={false}
    id="admin-list-table"
    resetSortOnDataChange={false}
    size="small"
    striped={false}
    title=""
    titleSize={21}
    translateHeaderLabels={[Function]}
  />
</div>
`;

exports[`RolesList Snapshot 1`] = `
<div
  className="admin-list"
>
  <ForwardRef
    className="admin-list__table"
    columnFixed={false}
    columns={[]}
    data={[]}
    defaultSortOrder="desc"
    highlightOnHover={false}
    id="admin-list-table"
    resetSortOnDataChange={false}
    size="small"
    striped={false}
    title=""
    titleSize={21}
    translateHeaderLabels={[Function]}
  />
  <f
    bold={false}
    color="black"
    component="p"
    italic={false}
    numeric={false}
  >
    No results available
  </f>
</div>
`;
