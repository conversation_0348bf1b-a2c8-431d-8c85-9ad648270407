import React from 'react';
import { fireEvent } from '@testing-library/react';
import { within } from '@testing-library/dom';
import mockAxios from 'axios';

import { defaultData, renderPage } from '../../utils/testing-library-utils';
import RolesListContainer from './rolesListContainer';

describe('RolesListContainer', () => {
  afterEach(() => {
    mockAxios.reset();
  });

  it('deactivate journey', async() => {
    const { baseElement, getByText, getAllByText, getByRole, getByLabelText } = await renderPage(<RolesListContainer />);
    const role = defaultData.roles[0];

    // search for target role to deactivate using the search bar and filters
    fireEvent.click(getByText('Filter'));
    fireEvent.change(getByLabelText('Status'), { target: { value: role.status } });
    fireEvent.change(getByLabelText('Team'), { target: { value: role.team_id } });
    expect(getByText(role.name)).toBeInTheDocument();
    fireEvent.click(getByRole('button', { name: 'Clear all' }));
    fireEvent.click(getByRole('button', { name: 'Cancel' }));
    fireEvent.change(baseElement.querySelector('#page-action-search-input'), { target: { value: role.name } });

    // trigger deactivation warning
    const targetRole = getAllByText(role.name)[0];
    const statusToggle = within(targetRole.closest('tr')).getByTestId('status-toggle-1');
    expect(statusToggle.checked).toBeTruthy();
    fireEvent.click(statusToggle);

    // due to known bug in canvas prior to 7.1.0, every time modal dialogue opens,
    // we must wait for focus animation to complete before closing the modal
    // https://bitbucket.agile.bns/projects/CANVAS/repos/core-react-develop/pull-requests/779/overview

    expect(getByText('Are you sure you want to deactivate this role?')).toBeInTheDocument();
    expect(getByText('Users assigned to this role will lose all permissions granted to this role.')).toBeInTheDocument();
    fireEvent.click(getByRole('button', { name: 'Deactivate Role' }));
    expect(mockAxios.post).toHaveBeenCalledWith(`/roles/${role.id}/deactivate`, undefined, undefined);
  });

  it('activation journey', async() => {
    const rolesItems = JSON.parse(JSON.stringify(defaultData.roles));
    rolesItems[0].status = 0;
    const role = rolesItems[0];
    const roles = { isLoading: false, items: rolesItems };
    const { getByText, getByLabelText, getByRole } = await renderPage(<RolesListContainer />, { initialState: { roles } });
    fireEvent.click(getByText('Filter'));
    fireEvent.change(getByLabelText('Status'), { target: { value: 'false' } });
    const targetRole = getByText(role.name);
    const statusToggle = within(targetRole.closest('tr')).getByTestId('status-toggle-1');
    expect(statusToggle.checked).toBeFalsy();
    fireEvent.click(statusToggle);

    expect(getByText('Are you sure you want to reactivate this role?')).toBeInTheDocument();
    expect(getByText('Users assigned to this role will regain access to all permissions granted to this role.')).toBeInTheDocument();
    fireEvent.click(getByRole('button', { name: 'Reactivate Role' }));
    expect(mockAxios.post).toHaveBeenCalledWith(`/roles/${role.id}/activate`, undefined, undefined);
  });

  it('block role activation if team is disabled', async() => {
    const teamsItems = JSON.parse(JSON.stringify(defaultData.teams));
    teamsItems[0].active = false;
    const rolesItems = JSON.parse(JSON.stringify(defaultData.roles));
    rolesItems[0].status = 0;
    const role = rolesItems[0];
    const roles = { isLoading: false, items: rolesItems };
    const teams = { isLoading: false, items: teamsItems };
    const { getAllByText } = await renderPage(
      <RolesListContainer />,
      { initialState: { roles, teams } }
    );
    const targetRole = getAllByText(role.name)[0];
    const statusToggle = within(targetRole.closest('tr')).getByTestId('status-toggle-1');
    expect(statusToggle.checked).toBeFalsy();
    expect(statusToggle).toBeDisabled();
  });

  it('create journey', async() => {
    const { getByRole, history } = await renderPage(<RolesListContainer />);
    fireEvent.click(getByRole('button', { name: 'Create Role' }));
    expect(history.location.pathname).toStrictEqual('/roles/create');
  });

  it('should allow deactivation modal to be dismissed by clicking cancel button', async() => {
    const { getAllByText, getByRole, queryByText } = await renderPage(<RolesListContainer />);
    const role = defaultData.roles[0];
    const targetUser = getAllByText(role.name)[0];
    fireEvent.click(within(targetUser.closest('tr')).getByTestId('status-toggle-1'));

    fireEvent.click(getByRole('button', { name: 'Cancel' }));
    expect(queryByText('Are you sure you want to deactivate this role')).not.toBeInTheDocument();
  });

  it('should allow deactivation modal to be dismissed by clicking modal back drop', async() => {
    const { getByText, getAllByText, queryByText } = await renderPage(<RolesListContainer />);
    const role = defaultData.roles[0];
    const targetRole = getAllByText(role.name)[0];
    fireEvent.click(within(targetRole.closest('tr')).getByTestId('status-toggle-1'));

    const modalMsg = getByText('Are you sure you want to deactivate this role?');
    fireEvent.click(modalMsg.closest('.Modal').querySelector('.InternalOverlay'));
    expect(queryByText('Are you sure you want to deactivate this role')).not.toBeInTheDocument();
  });

  it.todo('test pagination');
});
