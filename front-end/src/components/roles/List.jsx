import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import Table from 'canvas-core-react/lib/Table';
import TextCaption from 'canvas-core-react/lib/TextCaption';

const List = ({ isLoading, data, className, columns }) => {
  const renderListBody = () => {
    return (
      <Table
        className="admin-list__table"
        id="admin-list-table"
        title=""
        resetSortOnDataChange={false}
        columns={columns}
        data={data}
      />
    );
  };

  return (
    <div className={classnames('admin-list', className)}>
      { renderListBody() }
      { (!isLoading && !data.length) && <TextCaption component="p">No results available</TextCaption> }

    </div>
  );
};

List.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]),
  className: PropTypes.string,
  columns: PropTypes.array.isRequired,
  data: PropTypes.array,
  entityName: PropTypes.string.isRequired,
  isLoading: PropTypes.bool,
};

List.defaultProps = {
  data: [],
};

export default List;
