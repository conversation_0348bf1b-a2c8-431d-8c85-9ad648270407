import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { omit, difference } from 'lodash';
import qs from 'qs';

import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';

import PageInfo from '../pageInfo';
import PageActions from '../pageActions';
import AdminFilter from '../listing/AdminFilter';
import { useBrowserTitle } from '../../hooks/useBrowserTitle';
import { debounce, removeFalsyKeys } from '../../utils';

import { useRoles } from '../../hooks/useRoles';
import { setRoleActivation, getRoles } from '../../store/actions/roles';
import { tableColumns } from './rolesColumns';
import List from './List';

const PAGE_SIZE = 30;
const BASE_QUERY = {
  team_id: '',
  status: '',
  search: '',
  sort: '',
  limit: PAGE_SIZE,
  pageNumber: 1,
};

export const RolesListContainer = () => {
  useBrowserTitle({ title: 'Roles' });
  const history = useHistory();
  const dispatch = useDispatch();

  const queryParams = qs.parse(history.location.search, { ignoreQueryPrefix: true });
  const initialFilters = {
    ...BASE_QUERY,
    ...omit(queryParams, difference(Object.keys(queryParams), Object.keys(BASE_QUERY))),
  };
  const { roles, loading, teams, pagination, currentUser } = useRoles(removeFalsyKeys(initialFilters));
  const { canViewAllRoles, canEditAllRoles, canEditOwnTeamRoles } = useMemo(() => currentUser.permissionLevels || {}, [ currentUser ]);
  const [ filters, setFilters ] = useState(canViewAllRoles ? initialFilters : { ...initialFilters, team_id: currentUser.team_id });

  const [ roleToDeactivate, setRoleToDeactivate ] = useState(null);
  const [ roleToReactivate, setRoleToReactivate ] = useState(null);

  useEffect(() => {
    const queryParams = removeFalsyKeys(omit(filters, [
      'limit',
      ...(!canViewAllRoles ? [ 'team_id' ] : []),
      ...(filters.pageNumber === 1 ? [ 'pageNumber' ] : []),
    ]));
    history.push({ search: qs.stringify(queryParams, { addQueryPrefix: true }) });
  }, [ filters ]);

  const fetchRoles = (newFilters) => {
    dispatch(getRoles(removeFalsyKeys(newFilters)));
  };

  const debounceFetchRoles = useCallback(debounce(fetchRoles, 500), []);

  const handleOnChangeSearch = (e) => {
    const search = e.target.value;
    debounceFetchRoles({ ...filters, search, pageNumber: 1 });
    setFilters((f) => ({ ...f, search, pageNumber: 1 }));
  };

  const filtersChanged = (newFilters) => {
    setFilters(newFilters);
    dispatch(getRoles(removeFalsyKeys(newFilters)));
  };

  const handleClearFilters = () => {
    const newQuery = {
      ...BASE_QUERY,
      ...(!canViewAllRoles && {
        teamId: currentUser.team_id,
      }),
    };
    setFilters(newQuery);
    dispatch(getRoles(removeFalsyKeys(newQuery)));
  };

  const initialSortDirection = columnKey => {
    const { sort } = filters;
    if (!filters.sort) {
      return 0;
    }
    // check if the sorted key has a - in front of it which would indicate it's in descending order, ascending order otherwise
    if (sort.includes(columnKey)) {
      return (sort.charAt(0) === '-') ? 1 : -1;
    }
    return 0;
  };

  const onColumnSort = (columnKey, currentFilters) =>
    (row, direction) => {
      filtersChanged({ ...currentFilters, pageNumber: 1, sort: `${direction > 0 ? '-' : ''}${columnKey}` });
    };

  const sortableColumnProperties = useCallback((columnKey, currentFilters) => ({
    sortable: true,
    overrideSortBehaviour: onColumnSort(columnKey, currentFilters),
    initialSortDirection: initialSortDirection(columnKey),
  }), []);

  const handleChangeRoleStatus = (e, roleId, isRolesTeamActive) => {
    const { checked } = e.currentTarget;
    if (!checked) {
      e.preventDefault();
      setRoleToDeactivate(roleId);
      return;
    }
    if (isRolesTeamActive) {
      setRoleToReactivate(roleId);
    }
  };

  return (
    <>
      <PageInfo
        title="Roles"
        buttonText="Create Role"
        TitleComponent={TextHeadline}
        ButtonComponent={SecondaryButton}
        titleComponentPorps={{ component: 'h1' }}
        isLoading={loading}
        showActionButton={canEditOwnTeamRoles}
        onClick={() => history.push('/roles/create')}
      />

      <PageActions
        onChange={handleOnChangeSearch}
        value={filters.search}
        filterButtonText="Filter"
      >
        <AdminFilter
          className="admin-list__filter-options"
          onChange={filtersChanged}
          onClearClick={handleClearFilters}
          filterValues={canViewAllRoles ? filters : omit(filters, [ 'team' ])}
          fields={[
            ...canViewAllRoles ? [ {
              label: 'Team',
              key: 'team_id',
              defaultOptionLabel: `All`,
              options: teams.map(team => ({
                label: team.name,
                value: team.id,
              })).sort((a, b) => a.label.localeCompare(b.label)),
            } ] : [],
            {
              label: 'Status',
              key: 'status',
              defaultOptionLabel: `All`,
              options: [
                { label: 'Active', value: 'true' },
                { label: 'Inactive', value: 'false' },
              ],
            },
          ]}
          renderAsCard={false}
        />
      </PageActions>

      <List
        entityName="role"
        className="roles-list"
        columns={tableColumns({
          isLoading: loading,
          handleChangeRoleStatus,
          canEditOwnTeamRoles,
          canEditAllRoles,
          canViewAllRoles,
          teamId: currentUser.team_id,
          history,
          filters,
          sortableColumnProperties,
        })}
        data={roles}
        showMangePrefix={false}
      />

      { roles.length > 0 &&
        <DesktopPagination
          id="roles-pagination"
          totalResultCount={pagination?.total || 0}
          onChange={(pageNumber) => {
            filtersChanged({ ...filters, pageNumber });
            window.scrollTo(0, 0);
          }}
          firstButtonLabel="First"
          prevButtonLabel="Previous"
          nextButtonLabel="Next"
          lastButtonLabel="Last"
          navigationLabel="Pagination Navigation"
          pageSize={pagination?.limit || 0}
          currentPage={roles ? (pagination?.offset / pagination?.limit) + 1 : 1}
          containerType="card"
        />
      }

      { /* Modals */ }
      <ModalDialogue
        isModalVisible={roleToDeactivate !== null}
        headline="Are you sure you want to deactivate this role?"
        primaryButtonLabel="Deactivate Role"
        primaryAction={() => {
          dispatch(setRoleActivation(roleToDeactivate, false, filters));
          setRoleToDeactivate(null);
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setRoleToDeactivate(null)}
        setModalVisible={() => setRoleToDeactivate(null)}
      >
        Users assigned to this role will lose all permissions granted to this role.
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={roleToReactivate !== null}
        headline="Are you sure you want to reactivate this role?"
        primaryButtonLabel="Reactivate Role"
        primaryAction={() => {
          dispatch(setRoleActivation(roleToReactivate, true, filters));
          setRoleToReactivate(null);
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setRoleToReactivate(null)}
        setModalVisible={() => setRoleToReactivate(null)}
      >
        Users assigned to this role will regain access to all permissions granted to this role.
      </ModalDialogue>
    </>
  );
};

export default RolesListContainer;
