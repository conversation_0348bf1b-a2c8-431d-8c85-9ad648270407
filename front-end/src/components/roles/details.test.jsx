import React from 'react';
import { fireEvent, act, within } from '@testing-library/react';
import mockAxios from 'axios';

import RoleDetailsConnected from './details';
import { renderPage } from '../../utils/testing-library-utils';
import defaultData from '../../utils/testing-library-utils-data';
import { mapPropToKey } from '../../constants';
import { addSnackbar } from '../../store/actions/snackbar';

describe('Roles', () => {
  // common test data across all happy path test cases
  const mockRole = { ...defaultData.roles[0], status: false };
  const mockTeam = { ...defaultData.teams[0], active: false }; // mock role belongs to pigeon Team

  beforeEach(() => {
    mockAxios.reset();
    mockAxios.patch.mockImplementation(() => Promise.resolve({ data: { data: {} } }));
    mockAxios.get.mockImplementation(url => {
      switch (url) {
        case `/roles/${mockRole.id}`: return Promise.resolve({ data: { data: mockRole } });
        case `/teams?skipAccess=true`: return Promise.resolve({ data: [ mockTeam ] });
        default: return Promise.resolve({ data: { data: {} } });
      };
    });
  });

  afterAll(() => {
    mockAxios.reset();
  });

  const commonProps = {
    match: {
      params: {},
    },
    initializeRoleForm: jest.fn(),
    handleSubmit: jest.fn(),
    formValues: { id: 1, status: true },
    isLoading: false,
  };

  test('field validations', async() => {
    mockAxios.post.mockImplementation(() => Promise.resolve({ data: {} }));
    const handleSubmitMock = jest.fn().mockImplementation(fn => fn);
    const saveRoleValues = jest.fn().mockImplementation(() => Promise.resolve());
    const { getByRole, store } = await renderPage(
      <RoleDetailsConnected
        {...commonProps}
        handleSubmit={handleSubmitMock}
        saveRoleValues={saveRoleValues}
      />,
      { initialState: { form: { roleDetails: { values: { status: true } } } } },
    );
    fireEvent.click(getByRole('button', { name: 'Create Role' }));
    const actions = store.getActions();
    expect(actions.some(a => a.payload?.syncErrors?.team_id === 'Team is required')).toBeTruthy();
    expect(actions.some(a => a.payload?.syncErrors?.name === 'Role name is required')).toBeTruthy();
  });

  test('update journey, including role deactivation', async() => {
    const formInitialValue = { roleDetails: { initial: { status: true } } };
    // not fully realistic and comprehensive due to redux store not functioning in unit test
    // this exists here as litmus test for whether API call to backend was invoked or not
    const mockRoleEdited = { status: true, team_id: NaN };

    const { getByRole, getAllByRole, getByText, queryByText, store, history } = await renderPage(
      <RoleDetailsConnected match={{ params: { id: mockRole.id } }}/>,
      { initialState: { form: formInitialValue } },
    );

    // assert modal details
    await act(async() => fireEvent.click(getByRole('button', { name: 'Update Role' })));

    expect(getByText('Are you sure you want to deactivate this role?')).toBeInTheDocument();
    expect(getByText('Users assigned to this role will lose all permissions granted to this role.')).toBeInTheDocument();

    // test cancel button
    const modal = getAllByRole('dialog')[1];
    await act(async() => fireEvent.click(within(modal).getByRole('button', { name: 'Cancel' })));
    expect(queryByText('Are you sure you want to deactivate this role?')).not.toBeInTheDocument();
    await act(async() => fireEvent.click(getByRole('button', { name: 'Update Role' })));

    // follow through with role changes
    await act(async() => fireEvent.click(getByRole('button', { name: 'Deactivate Role' })));
    expect(mockAxios.patch).toHaveBeenCalledWith(`/roles/${mockRole.id}`, mockRoleEdited, undefined);
    const expectedSnackbar = addSnackbar({ bold: 'Role', message: ' was updated successfully' });
    expect(store.getActions()).toContainEqual(expectedSnackbar);
    expect(history.location.pathname).toBe('/roles');
  });

  test('update journey, including role reactivation', async() => {
     const mockRoleEdited = { status: true, team_id: NaN };
     const state = { form: { roleDetails: { initial: { status: false }, values: { status: true } } } };
    state.form.roleDetails.values.status = true;
    state.form.roleDetails.initial.status = false;
    const { getByRole, getAllByRole, getByText, queryByText, history, store } = await renderPage(
      <RoleDetailsConnected match={{ params: { id: mockRole.id } }}/>,
            { initialState: state },
    );

    // assert modal details
    await act(async() => fireEvent.click(getByRole('button', { name: 'Update Role' })));

    expect(getByText('Are you sure you want to reactivate this role?')).toBeInTheDocument();
   // test cancel button
    const modal = getAllByRole('dialog')[1];
    await act(async() => fireEvent.click(within(modal).getByRole('button', { name: 'Cancel' })));
    expect(queryByText('Are you sure you want to reactivate this role?')).not.toBeInTheDocument();
    await act(async() => fireEvent.click(getByRole('button', { name: 'Update Role' })));

    // follow through with role changes
    await act(async() => fireEvent.click(getByRole('button', { name: 'Reactivate Role' })));
    expect(mockAxios.patch).toHaveBeenCalledWith(`/roles/${mockRole.id}`, mockRoleEdited, undefined);
    const expectedSnackbar = addSnackbar({ bold: 'Role', message: ' was updated successfully' });
    expect(store.getActions()).toContainEqual(expectedSnackbar);
    expect(history.location.pathname).toBe('/roles');
  });

  it('should disable status toggle if team is disabled', async() => {
    const formInitialValue = { roleDetails: { values: { team_id: mockTeam.id } } };

    const { getByLabelText, getByText } = await renderPage(
      <RoleDetailsConnected match={{ params: { id: mockRole.id } }}/>,
      { initialState: {
        form: formInitialValue,
        teams: { isLoading: false, items: mapPropToKey([ mockTeam ], 'id') },
      } },
    );

    const statusToggle = getByLabelText('Inactive');
    expect(statusToggle).toBeDisabled();
    const tooltipButton = within(statusToggle.closest('.toggle-field')).getByRole('button');
    fireEvent.click(tooltipButton);
    expect(getByText('Cannot reactivate')).toBeInTheDocument();
    expect(getByText('The team that this role belongs to must be reactivated before this role can be reactivated.')).toBeInTheDocument();
  });

  test('history called when cancel clicked', async() => {
    const { history, getByText } = await renderPage(<RoleDetailsConnected match={{ params: { id: mockRole.id } }}/>);
    await act(async() => fireEvent.click(getByText('Cancel')));
    expect(history.entries[1].pathname).toBe('/roles');
  });

  test.todo('assign team drop down, and form depends on permission levels');
});

describe('Roles - page heading', () => {
  it('create', async() => {
    const { getByRole } = await renderPage(<RoleDetailsConnected match={{ params: { id: undefined } }}/>);
    expect(getByRole('heading', { level: 1, name: 'Create Role' })).toBeInTheDocument();
  });

  it('edit', async() => {
    const { getByRole } = await renderPage(<RoleDetailsConnected match={{ params: { id: 1 } }}/>);
    expect(getByRole('heading', { level: 1, name: 'Edit Role' })).toBeInTheDocument();
  });
});
