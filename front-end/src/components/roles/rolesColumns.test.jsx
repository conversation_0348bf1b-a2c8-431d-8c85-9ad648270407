import { tableColumns } from './rolesColumns';
import { shallow } from 'enzyme';

const role = {
  id: 1,
  status: true,
  name: 'Viewer 1',
  team_id: 1,
  teamName: 'Pigeon Team',
  teamActive: true,
};

describe('roles Columns', () => {
  const cols = {
    handleChangeRoleStatus: jest.fn(() => {}),
    canViewAllRoles: true,
    canEditOwnTeam: true,
    canEditAllRoles: true,
    teamId: role.team_id,
    teams: [ { id: 1 } ],
    sortableColumnProperties: jest.fn(() => ({ overrideSortBehaviour: jest.fn(), sortable: true })),
  };

  it('should retun 3 columns ', () => {
    const columns = tableColumns(cols);
    expect(columns).toHaveLength(3);
  });

  it('should retun 2 columns ', () => {
    const columns = tableColumns({ ...cols, canViewAllRoles: false, canEditAllRoles: false });
    expect(columns).toHaveLength(2);
  });

  // TODO: Review test case
  // it('name col', () => {
  //   const columns = tableColumns(cols);
  //   const nameCol = columns[0];
  //   const wrapper = shallow(
  //     nameCol.cellFormatter(role)
  //   );
  //   expect(wrapper).toMatchSnapshot();
  //   expect(nameCol.name).toStrictEqual('Role');
  //   expect(nameCol.sortable).toStrictEqual(true);
  //   expect(nameCol.selector).toStrictEqual('name');
  //   expect(typeof nameCol.sortCompare).toStrictEqual('function');
  //   expect(wrapper).toMatchSnapshot();
  // });

  it('team col', () => {
    const columns = tableColumns(cols);
    const teamCol = columns[1];
    const wrapper = shallow(
      teamCol.cellFormatter(role)
    );
    expect(wrapper).toMatchSnapshot();
    expect(teamCol.name).toStrictEqual('Team');
    expect(teamCol.sortable).toStrictEqual(true);
    expect(teamCol.selector).toStrictEqual('team');
    expect(typeof teamCol.overrideSortBehaviour).toStrictEqual('function');
    expect(wrapper).toMatchSnapshot();
  });
});
