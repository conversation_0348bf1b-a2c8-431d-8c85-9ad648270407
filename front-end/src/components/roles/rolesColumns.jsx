import React from 'react';
import TextButton from 'canvas-core-react/lib/TextButton';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import ToggleSwitch from 'canvas-core-react/lib/ToggleSwitch';
import { reject } from 'lodash';

import { sanitizeRoleName } from '../../constants';

export const tableColumns = ({
  isLoading,
  handleChangeRoleStatus,
  canViewAllRoles,
  canEditOwnTeamRoles,
  canEditAllRoles,
  teamId,
  history,
  filters,
  sortableColumnProperties,
}) => {
  const columns = [
    {
      name: 'Role',
      cellFormatter: (row) => (
        <>
          <TextButton
            Icon={() => <></>}
            className="roles-list__link-button"
            onClick={() => history.push(`/roles/${row.id}`)}
          >
            { sanitizeRoleName(row.name) }
          </TextButton>
          { (row.name.includes('Viewer') || row.name.includes('Team owner')) &&
            <span className="roles__required-copy">(Required)</span>
          }
        </>
      ),
      selector: '',
      style: { display: 'flex', flexFlow: 'wrap' },
      ...sortableColumnProperties('name', filters),
    },
    {
      name: 'Team',
      cellFormatter: (row) => (
        <TextCaption component="p">{ row.teamName }</TextCaption>
      ),
      selector: 'team',
      grow: 2,
      center: false,
      ...sortableColumnProperties('team', filters),
    },
    {
      name: 'Status',
      cellFormatter: (row) => (
        <ToggleSwitch
          id={`status-toggle-${row.id}`}
          data-testid={`status-toggle-${row.id}`}
          className="roles-list__toggle-switch"
          label=""
          name=""
          onChange={(e) => handleChangeRoleStatus(e, row.id, row.teamActive)}
          checked={!!row.status}
          disabled={isLoading ||
            row.name?.includes('Team owner') || // default role
            row.name?.includes('Viewer') || // default role
            !row.teamActive || // inactive team
            !(canEditAllRoles || (canEditOwnTeamRoles && row.team_id === teamId)) // insufficient access
          }
        />
      ),
      selector: 'status',
      grow: 0.25,
      right: false,
      // TODO: Migrate to headerFormatter with tooltip component when on Canvas 9.3.0+
      tooltip: 'An active, or activated, role has access to all permissions that has been assigned to it. An inactive, or deactivated, role does not have access to all permissions that has been assigned to it. When a team is deactivated, all roles in that team will also be deactivated. However, when a team is activated, only the Team Owner and Viewer roles are automatically activated, and those two roles cannot be deactivated while the team is active.',
    },
  ];
  return reject(columns, c => !canViewAllRoles && c.selector === 'team');
};
