import React, { useEffect, useState, useRef } from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import Checkbox from 'canvas-core-react/lib/Checkbox';
import IconErrorUniversal from 'canvas-core-react/lib/IconErrorUniversal';
import { mapValues } from 'lodash';
import { permissionSelectionsMappings } from '../../../../src/permissions/permission-constants';
import { capitalize } from '../../utils';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import Tooltip from 'canvas-core-react/lib/Tooltip';
import TextBody from 'canvas-core-react/lib/TextBody';

const initialState = Object.keys(permissionSelectionsMappings).reduce((acc, val) => {
  acc[val] = {};
  return acc;
}, {});

const actions = [ 'view', 'manage', 'review', 'approve' ];

const checkStates = {
  checked: true,
  indeterminate: 'indeterminate',
  unchecked: false,
};

const PermissionsTable = ({
  input: {
    onChange,
  },
  meta,
  disabled,
  options,
  formValues,
  roleData,
}) => {
  const firstRender = useRef(true);
  // permissionsSelected should represent each section + action
  const [ permissionsSelected, setPermissionsSelected ] = useState(initialState);
  const [ isInitialized, setIsInitialized ] = useState(false);

  // convert permission array to the state object
  const mapInitialPermissionsToState = selectedPermissions => (
    Object.keys(permissionSelectionsMappings).reduce((acc, section) => {
      acc[section] = actions.reduce((sectionActions, action) => {
        // if any of the initial permissions are found in the permissionSelectionMappings add those permissionSelectionMappings permissions here
        const permission = permissionSelectionsMappings[section].actions[action];
        if (!permission) {
          return sectionActions;
        }
        const permissionFoundInSelectedPermissions = selectedPermissions.includes(permission);
        if (permissionFoundInSelectedPermissions) {
          sectionActions[action] = permission;
        }
        return sectionActions;
      }, {});
      return acc;
    }, {})
  );

  // ensure our permissions selected state is up to date with the initial value
  useEffect(() => {
    if (roleData) {
      setIsInitialized(true);
      roleData.permissions && setPermissionsSelected(mapInitialPermissionsToState(roleData.permissions));
    }
  }, [ roleData ]);

  useEffect(() => {
    // onChange effect should not fire on first render
    if (firstRender.current) {
      firstRender.current = false;
      return;
    }
    // convert permissionsSelectedObject into flat permissions array
    if (isInitialized) {
      setIsInitialized(false);
      return;
    }

    const flattenedPermissions = Object.keys(permissionsSelected).reduce((permissions, section) => {
      for (const action of Object.keys(permissionsSelected[section])) {
        permissions.push(permissionsSelected[section][action] || []);
      }
      return permissions;
    }, []);

    onChange(flattenedPermissions);
  }, [ permissionsSelected ]);

  useEffect(() => {
    const initialState = Object.keys(permissionSelectionsMappings).reduce((acc, val) => {
      acc[val] = {};
      return acc;
    }, {});
   if (formValues && formValues?.team_id !== roleData?.team_id) {
    setPermissionsSelected(initialState);
   }
  }, [ formValues?.team_id ]);

  /**
   * Include/exclude a single permission for a particular section/action combination
   * @param section campaigns, alerts, etc.
   * @param action The action to check; one of view, manage, review or approve
   * @param shouldCheckPermission - whether to check/uncheck these permission checkboxes
   */
  const toggleSinglePermission = (section, action, shouldCheckPermission) => {
    if (!isValidCheckbox(section, action)) {
      return;
    }
    const currentPermissions = { ...permissionsSelected };
    if (!shouldCheckPermission) {
      // delete all permissions to the right of this as they depend on this one
      const actionsToUncheck = actions.slice(actions.indexOf(action));
      actionsToUncheck.forEach(a => {
        delete currentPermissions[section][a];
      });
      // delete currentPermissions[section][action];
    } else {
      // check all permissions to the left of this as they are dependencies for this permission
      const actionsToCheck = actions.slice(0, actions.indexOf(action) + 1);
      actionsToCheck.forEach(a => {
        if (!isValidCheckbox(section, a)) {
          return;
        }
        const permissionsForSectionAction = permissionSelectionsMappings[section].actions[a];
        currentPermissions[section][a] = permissionsForSectionAction;
      });
    }
    setIsInitialized(false);
    setPermissionsSelected(currentPermissions);
  };

  /**
   * Include/exclude all permissions for a particular action type (i.e. view, manage, etc)
   * @param action The action to check; one of view, manage, review or approve
   */
  const toggleAllPermissionsForAction = (action) => {
    const selectionsItems = mapValues(permissionsSelected, permission => permission[action]);
    const hasAnySelections = Object.values(selectionsItems).filter(p => !!p).length;

    Object.keys(permissionSelectionsMappings).forEach(section => {
      if (permissionSelectionsMappings[section].actions[action]) {
        toggleSinglePermission(section, action, !hasAnySelections);
      }
    });
  };

  const isValidCheckbox = (section, action) => {
    const permission = permissionSelectionsMappings[section].actions[action];
    return permission && options?.includes(permission);
  };

  const checkStatusesForAction = (
    // map action to a checked, indeterminate, unchecked state
    actions.reduce((acc, action) => {
      let numberOfSectionsWithActionSelected = 0;

      Object.keys(permissionSelectionsMappings).forEach(section => {
        if (permissionsSelected[section][action]) {
          numberOfSectionsWithActionSelected++;
        }
      });

      let checkStatus;
      if (numberOfSectionsWithActionSelected === Object.keys(permissionSelectionsMappings).filter(section => actions.some(action => isValidCheckbox(section, action))).length) {
        checkStatus = checkStates.checked;
      } else if (!numberOfSectionsWithActionSelected) {
        checkStatus = checkStates.unchecked;
      } else {
        checkStatus = checkStates.indeterminate;
      }

      acc[action] = checkStatus;
      return acc;
    }, {})
  );

  const TooltipBody = () => {
    return (
      <Tooltip
        id='campaign-enrollment-status-tooltip'
        heading='Functionality'
        infoButtonLabel='Info'
        closeButtonLabel='close'
      >
        <TextBody
          component='p'
          type='2'
          className='roles__role_tooltip_item'
        >
          Assign permissions to the functionalities this role should have access to
        </TextBody>
        <TextBody
          component='p'
          type='2'
          className='roles__role_tooltip_item'
        >
          <TextBody component='span' bold className='roles__role_tooltip_title_item'>Own team/application: </TextBody> This role is able to manage this functionality for their own team/application only.
        </TextBody>
        <TextBody
          component='p'
          type='2'
          className='roles__role_tooltip_item'
        >
         <TextBody component='span' bold className='roles__role_tooltip_title_item'>All teams/applications: </TextBody> This role is able to manage this functionality for all teams/applications in Pigeon.
        </TextBody>
      </Tooltip>
    );
  };

  const header = (
    <React.Fragment key="header">
      <div key={`heading-blank`} className="permissions-table__cell permissions-table__cell--label permissions-table__cell--header" >
      <TextSubtitle
      type="2"
      component="h3"
      className="advanced-targeting-section__subtitle"
    >
      Functionality
      <TooltipBody />
    </TextSubtitle>

      </div>
      { actions.map((action, index) => (
        <div key={`heading-${action}-${index}`} className="permissions-table__cell permissions-table__cell--heading">
          <Checkbox
            id={`heading-checkbox-${action}`}
            label={capitalize(action)}
            checked={checkStatusesForAction[action]}
            onChange={(e) => toggleAllPermissionsForAction(action)}
            disabled={disabled}
          />
        </div>)) }
    </React.Fragment>
  );

  const sectionRows = Object.keys(permissionSelectionsMappings).filter(section => actions.some(action => isValidCheckbox(section, action))).map(section => (
    <React.Fragment key={`section-${section}`}>
      <div
        className="permissions-table__cell permissions-table__cell--label"
      >
        { permissionSelectionsMappings[section].name }
      </div>
      { actions.map(action => (
        <Checkbox
          key={`${section}-${action}`}
          id={`${section}-${action}-checkbox`}
          className={classnames('permissions-table__cell', 'permissions-table__cell--checkbox', `permissions-table__cell--${action}`)}
          label={`Toggle ${action} permissions for ${section}`}
          checked={!!permissionsSelected[section][action]}
          onChange={(e) => toggleSinglePermission(section, action, e.target.checked)}
          disabled={!isValidCheckbox(section, action) || disabled}
        />
      )) }
    </React.Fragment>
  ));

  return (
    <div className="permissions-table">
      { header }
      { sectionRows }
      { meta.error && meta.touched &&
        <div className="permissions-table__error-container">
          <IconErrorUniversal />
          <div className="permissions-table__error-message">
            <span>{ meta.error }</span>
          </div>
        </div>
      }
    </div>
  );
};

PermissionsTable.defaultProps = {
  meta: {},
  input: { onChange: () => {} },
  disabled: false,
  options: [],
};

PermissionsTable.propTypes = {
  meta: PropTypes.object.isRequired,
  input: PropTypes.shape({
    onChange: PropTypes.func,
  }).isRequired,
  disabled: PropTypes.bool,
  options: PropTypes.array,
  formValues: PropTypes.object,
  roleData: PropTypes.object,
};

export default PermissionsTable;
