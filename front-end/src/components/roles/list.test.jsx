import React from 'react';
import { shallow } from 'enzyme';
import RolesList from './List';

const values = { isLoading: false, data: [], className: '', columns: [] };

describe('RolesList', () => {
  const wrapper = shallow(<RolesList {...values} entityName='' />);
  global.snapshot(wrapper);
});

describe('RolesList - loading ', () => {
  const wrapper = shallow(<RolesList {...values} isLoading entityName='' />);
  global.snapshot(wrapper);
});
