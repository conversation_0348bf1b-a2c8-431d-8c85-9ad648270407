import React, { useState, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { reduxForm, Field, getFormValues, getFormInitialValues, change } from 'redux-form';
import { useHistory } from 'react-router-dom';
import { bindActionCreators } from 'redux';
import { connect, useSelector, useDispatch } from 'react-redux';
import { isEqual } from 'lodash';

import Card from 'canvas-core-react/lib/Card';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';

import PermissionsTable from './permissionsTable';
import { InputSelectField, InputTextField, InputToggleField } from '../formFields';
import { initializeRoleForm, saveRoleValues } from '../../store/actions/roles';
import { addSnackbar } from '../../store/actions/snackbar';
import { mapObjectToArray, sanitizeRoleName } from '../../constants';
import { requiredSpecific, reservedRoleName, roleNameValidation, requireAtLeastOne } from '../../utils/validation';

const validations = {
  requireTeam: requiredSpecific('Team'),
  requireRole: requiredSpecific('Role name'),
};

const inactiveToolTip = {
  heading: 'Cannot reactivate',
  body: 'The team that this role belongs to must be reactivated before this role can be reactivated.',
};

const assignTeamTooltip = {
  heading: 'Assign Team',
  body: 'Select the team you would like to create a new role for.',
};

export const RoleDetails = ({
  dirty,
  isLoading,
  teams,
  saveRoleValues,
  formInitialValues,
  formValues,
  handleSubmit,
  initializeRoleForm,
  match: {
    params: {
      id: roleId,
    },
  },
  valid,
}) => {
  const authenticated = useSelector(state => state.authenticated);
  const { canEditAllRoles, canEditOwnTeamRoles } = authenticated.permissionLevels || {};
  const [ isFormDisabled, setIsFormDisabled ] = useState(true);
  const [ isCancelPopupOpen, setIsCancelPopupOpen ] = useState(false);
  const [ isDeactivatePromptOpen, setIsDeactivatePromptOpen ] = useState(false);
  const [ isReactivatePromptOpen, setIsReactivatePromptOpen ] = useState(false);
  const [ roleData, setRoleData ] = useState({});

  const history = useHistory();
  const dispatch = useDispatch();

  const disableStatusToggle = useMemo(() => {
    const assignedTeam = teams?.find(t => t.id === Number(formValues.team_id));
    return assignedTeam && !assignedTeam.active;
  }, [ formValues.team_id, teams ]);

  useEffect(() => {
    const init = async() => {
      const data = await initializeRoleForm(roleId, !canEditAllRoles && authenticated.team_id);
      setRoleData(data);
    };
    init();
  }, []);

  useEffect(() => {
    setIsFormDisabled(formValues.id
      ? !(canEditAllRoles || (canEditOwnTeamRoles && formValues.team_id === authenticated.team_id)) || formValues.name.includes('Team owner') || formValues.name.includes('Viewer')
      : !canEditOwnTeamRoles);
  }, [ formValues.id ]);

  const saveChanges = async(values) => {
    if (isEqual(roleData, formValues)) {
      return dispatch(addSnackbar({ message: 'No change to be saved' }));
    };
    const teamId = canEditAllRoles ? Number(values?.team_id) : authenticated.team_id;
    await saveRoleValues(roleId, { ...values, team_id: teamId });
    history.push('/roles');
  };

  const ensureNotReservedName = useMemo(() => reservedRoleName(), []);

  const handleTeamChange = (e, value) => {
    const isTeamActive = teams.find(t => t.id === Number(value)).active;
    if (!isTeamActive && formValues.status) {
      dispatch(change('roleDetails', 'status', isTeamActive));
    } else if (isTeamActive && !formValues.status) {
      dispatch(change('roleDetails', 'status', true));
    }
  };

  const handleChangeStatus = () => {
    if (!formValues.status) {
      setIsDeactivatePromptOpen(true);
    } else {
      setIsReactivatePromptOpen(true);
    }
  };

  if (isLoading) {
    return <IconSpinner size={32} />;
  }

  const checkSelectedTeamStatus = () => formValues.team_id ? teams.find(t => t.id === Number(formValues.team_id))?.active : true;

  return (
    <div className="roles">
      <TextHeadline className="roles__header" component="h1">{ `${roleId ? 'Edit' : 'Create'} Role` }</TextHeadline>
      <form>
        <Card className="roles__card">
          <TextHeadline className="roles__sub-header" component="h2" size={21}>Role Details</TextHeadline>
          <Field
            className="roles__field"
            name="status"
            label=""
            secondaryLabel={formValues.status ? 'Active' : 'Inactive'}
            component={InputToggleField}
            options={[
              { id: true, name: 'Active' },
              { id: false, name: 'Inactive' },
            ]}
            disabled={disableStatusToggle || isFormDisabled}
            tooltip={disableStatusToggle && inactiveToolTip}
          />
          { canEditAllRoles &&
            <Field
              className="roles__field"
              name="team_id"
              label="Assign Team"
              placeholder="Select a team"
              component={InputSelectField}
              defaultValue={undefined}
              options={teams.map(({ id, name, active }) => active ? ({ id, name }) : ({ id, name: name + ' (Inactive)' }))}
              validate={[ validations.requireTeam ]}
              disabled={isFormDisabled}
              onChange={handleTeamChange}
              tooltip={assignTeamTooltip}
              noBlur
            />
          }
          <Field
            className="roles__field"
            name="name"
            label="Role Name"
            placeholder="Enter role name"
            component={InputTextField}
            validate={[ validations.requireRole, ensureNotReservedName, roleNameValidation ]}
            disabled={isFormDisabled}
            format={(value) => isFormDisabled ? sanitizeRoleName(value) : value}
          />
        </Card>
        { (canEditAllRoles ? formValues.team_id : true) && (
          <Card className="roles__card">
            <TextHeadline className="roles__sub-header" component="h2" size={21}>Manage permissions</TextHeadline>
            <Field
              name="permissions"
              component={PermissionsTable}
              validate={[ requireAtLeastOne ]}
              disabled={isFormDisabled}
              options={canEditAllRoles
                ? teams.find(({ id }) => id === Number(formValues.team_id))?.permissions
                : Object.keys(authenticated.permissions)
              }
              formValues={formValues}
              roleData={roleData}
            />
          </Card>
        ) }
        <div className="roles__action-buttons">
          <SecondaryButton
            className="roles__action-button"
            type="button"
            onClick={() => dirty ? setIsCancelPopupOpen(true) : history.push('/roles')}
          >
            Cancel
          </SecondaryButton>
          <PrimaryButton
            className="roles__action-button"
            type="button"
            disabled={isFormDisabled}
            onClick={(valid && roleId && formInitialValues.status !== formValues.status && checkSelectedTeamStatus()) ? () => handleChangeStatus() : handleSubmit(saveChanges)}
          >
            { `${roleId ? 'Update' : 'Create' } Role` }
          </PrimaryButton>
        </div>
      </form>
      <ModalDialogue
        isModalVisible={isDeactivatePromptOpen}
        headline="Are you sure you want to deactivate this role?"
        primaryButtonLabel="Deactivate Role"
        primaryAction={handleSubmit(saveChanges)}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setIsDeactivatePromptOpen(false)}
        setModalVisible={() => setIsDeactivatePromptOpen(false)}
      >
        Users assigned to this role will lose all permissions granted to this role.
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={isReactivatePromptOpen}
        headline="Are you sure you want to reactivate this role?"
        primaryButtonLabel="Reactivate Role"
        primaryAction={handleSubmit(saveChanges)}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setIsReactivatePromptOpen(false)}
        setModalVisible={() => setIsReactivatePromptOpen(false)}
      >
        Users assigned to this role will regain access to all permissions granted to this role.
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={isCancelPopupOpen}
        headline="Are you sure?"
        primaryButtonLabel="Yes"
        primaryAction={() => history.push('/roles')}
        secondaryButtonLabel="No"
        secondaryAction={() => setIsCancelPopupOpen(false)}
        setModalVisible={() => setIsCancelPopupOpen(false)}
      >
        If you cancel, any unsaved changes will be lost.
      </ModalDialogue>
    </div>
  );
};

RoleDetails.propTypes = {
  dirty: PropTypes.bool.isRequired,
  teams: PropTypes.array,
  saveRoleValues: PropTypes.func.isRequired,
  formInitialValues: PropTypes.object,
  formValues: PropTypes.object,
  handleSubmit: PropTypes.func,
  match: PropTypes.object.isRequired,
  initializeRoleForm: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
  valid: PropTypes.bool.isRequired,
};

const mapStateToProps = state => ({
  isLoading: state.teams?.isLoading || state.roles?.isLoading,
  teams: mapObjectToArray(state.teams?.items || {}),
  formInitialValues: getFormInitialValues('roleDetails')(state) || {},
  formValues: getFormValues('roleDetails')(state) || {},
});

const mapDispatchToProps = dispatch => bindActionCreators({
  initializeRoleForm,
  saveRoleValues,
}, dispatch);

export default connect(mapStateToProps, mapDispatchToProps)(
  reduxForm({ form: 'roleDetails' })(RoleDetails)
);
