import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { reduxForm, formValueSelector } from 'redux-form';

import { closeAllModals } from '../../store/actions/modal';
import { getContentTypes, getContentItems } from '../../store/actions/content';
import ContentModal from './content';
import { modalTypes, ruleTypes } from '../../constants';
import { debounce } from '../../utils';

export class ContentModalContainer extends React.PureComponent {
  static propTypes = {
    type: PropTypes.oneOf([ ruleTypes.CAMPAIGN, ruleTypes.ALERT, ruleTypes.CCAU_CAMPAIGN ]),
    content: PropTypes.shape({
      types: PropTypes.objectOf(
        PropTypes.shape({
          id: PropTypes.string.isRequired,
          name: PropTypes.string.isRequired,
          display_field: PropTypes.string,
          items: PropTypes.arrayOf(
            PropTypes.shape({
              id: PropTypes.string.isRequired,
              updated_at: PropTypes.string.isRequired,
              content: PropTypes.object,
            })),
        })),
      isLoading: PropTypes.bool,
    }).isRequired,
    container: PropTypes.object,
    formValues: PropTypes.object.isRequired,
    pages: PropTypes.shape({
      items: PropTypes.array,
      isLoading: PropTypes.bool,
    }).isRequired,
    modal: PropTypes.string,
    closeAllModals: PropTypes.func.isRequired,
    getContentTypes: PropTypes.func.isRequired,
    getContentItems: PropTypes.func.isRequired,
    onSave: PropTypes.func.isRequired,
    handleSubmit: PropTypes.func.isRequired,
    change: PropTypes.func.isRequired,
    untouch: PropTypes.func.isRequired,
    contentfulSpace: PropTypes.string,
    isDismissible: PropTypes.bool,
  };

  state = {
    scrollPosition: null,
    searchTerm: '',
    sort: '',
  };

  getSnapshotBeforeUpdate(prevProps, prevState) {
    // if the modal was closed and its now being opened
    if (!this.isModalOpen(prevProps) && this.isModalOpen()) {
      document.body.classList.add('modal-open');
      this.setState({ scrollPosition: window.scrollY });
    }
    return null;
  }

  // React has a warning about using getSnapshotBeforeUpdate() without componentDidUpdate() but we are using the
  // above method to record scroll position before the modal opens
  componentDidUpdate(prevProps, prevState, snapshot) {
    if (
      !this.props.content.isLoading &&
      (!Object.keys(this.props.content).length ||
        !this.props.content.types ||
        prevProps.contentfulSpace !== this.props.contentfulSpace)
    ) {
      this.props.getContentTypes(this.props.contentfulSpace);
    }
  }

  componentWillUnmount() {
    this.onClose();
  }

  isModalOpen = (propsToUse) => {
    // for use with prevProps, otherwise use this.props
    if (propsToUse) {
      return propsToUse.modal === modalTypes.CONTENT;
    }
    return this.props.modal === modalTypes.CONTENT;
  };

  componentDidMount() {
    this.onSearch = debounce(this.onSearch, 500);
    this.props.getContentTypes(this.props.contentfulSpace);
  }

  contentTypeChanged = (e, value) => {
    this.setState({ searchTerm: '', sort: '' });
    this.props.getContentItems({ type: value, contentfulSpace: this.props.contentfulSpace });
  };

  pageClicked = (page) => {
    const { searchTerm, sort } = this.state;
    this.props.getContentItems({ type: this.props.formValues.content_type, name: searchTerm || undefined, page, sort: sort || undefined, contentfulSpace: this.props.contentfulSpace });
  };

  onClose = () => {
    this.props.closeAllModals();
    document.body.classList.remove('modal-open');
  };

  isSubmitDisabled = () => Object.keys(validate(this.props.formValues, this.props)).length !== 0;

  onSubmit = values => {
    const payload = {
      content_type: values.content_type,
      content_id: values.content_id,
    };
    this.props.onSave(payload);
    this.onClose();
    window.requestAnimationFrame(() => window.scrollTo(0, this.state.scrollPosition));
  };

  onSearch = value => {
    const term = value?.length >= 3 ? value : '';
    if (term || term !== this.state.searchTerm) {
      const { sort } = this.state;
      this.props.getContentItems({ type: this.props.formValues.content_type, name: term || undefined, page: 1, sort: sort || undefined, contentfulSpace: this.props.contentfulSpace });
    }
    this.setState({ searchTerm: term });
  };

  onClear = () => {
    this.setState({ searchTerm: '' });
    const { sort } = this.state;
    this.props.getContentItems({ type: this.props.formValues.content_type, page: 1, sort: sort || undefined, contentfulSpace: this.props.contentfulSpace });
  }

  onSort = (column, descending) => {
    const { searchTerm, sort } = this.state;
    if ((!descending && sort === column) || (descending && sort !== `-${column}`)) {
      this.setState({ sort: `-${column}` });
      this.props.getContentItems({ type: this.props.formValues.content_type, name: searchTerm || undefined, sort: `-${column}`, page: 1, contentfulSpace: this.props.contentfulSpace });
    } else {
      this.setState({ sort: column });
      this.props.getContentItems({ type: this.props.formValues.content_type, name: searchTerm || undefined, sort: column, page: 1, contentfulSpace: this.props.contentfulSpace });
    }
  };

  render() {
    if (this.props.modal !== modalTypes.CONTENT) {
      return null;
    }
    return this.props.container && this.props.pages.items && (
      <ContentModal
        type={this.props.type}
        isOpen={this.props.modal === modalTypes.CONTENT}
        onClose={this.onClose}
        onSubmit={this.props.handleSubmit(this.onSubmit)}
        container={this.props.container}
        contentTypeChanged={this.contentTypeChanged}
        pageClicked={this.pageClicked}
        onSearch={this.onSearch}
        onClear={this.onClear}
        onSort={this.onSort}
        content={this.props.content}
        formValues={this.props.formValues}
        isSubmitDisabled={this.isSubmitDisabled}
        isDismissible={this.props.isDismissible}
        contentfulSpace={this.props.contentfulSpace}
      />
    );
  }
}

export const validate = (values, props) => {
  const errors = {};
  // validation logic
  if (!values.content_type) {
    errors.content_type = 'You must select a Contentful model type.';
  }
  if (!values.content_id) {
    errors.content_id = 'You must select Contentful content.';
  }
  return errors;
};

const selector = formValueSelector('contentModal');

const mapStateToProps = state => ({
  content: state.content,
  modal: state.modal,
  pages: state.pages,
  formValues: selector(state, 'content_type', 'pages', 'content_id'),
});

const mapDispatchToProps = dispatch => bindActionCreators({
  closeAllModals,
  getContentTypes,
  getContentItems,
}, dispatch);

@connect(mapStateToProps, mapDispatchToProps)
@reduxForm({
  form: 'contentModal',
  validate,
  initialValues: {
    pages: {},
  },
})
export default class ContentModalConnected extends ContentModalContainer {}
