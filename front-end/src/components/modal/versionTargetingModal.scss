.version-targeting {
  &__button {
    &--add {
      &.TextButton__button {
        display: inline;
        padding: 1.1rem 1rem 1.4rem 1rem;
      }
    }

    &--edit {
      &.TextButton__button {
        display: inline;
        padding: 0 1rem 1.4rem 1rem;
      }
    }
  }
}

.vt-modal {
  .Modal__card {
    max-width: 100rem;
    max-height: 90%;
    overflow: auto;
  }

  &__button {
    &--add-row,
    &--delete-row {
      &.TextButton__button {
        padding: 0 1rem;
        margin-top: 2.5rem;
        display: block;
      }

      .TextButton__icon--left {
        margin: 0;
      }
    }

    &--save,
    &--cancel {
      @extend .admin-details__modal-button;
    }
  }

  &__delete-row-text {
    @include mq($from: desktop) {
      display: none;
    }
  }

  li {
    list-style: none;
  }

  &__row {
    margin-bottom: 2rem;

    @include mq($from: desktop) {
      display: flex;
    }
  }

  &__field {
    &--type {
      min-width: 16rem;
    }

    padding-bottom: 1rem;

    @include mq($from: desktop) {
      padding-bottom: 0;
      margin-right: 1rem;
    }
  }

  &__footer {
    @extend .admin-details__modal-footer;
  }
}
