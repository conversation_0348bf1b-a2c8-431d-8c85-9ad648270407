import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import moment from 'moment';
import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';
import IconExternal from 'canvas-core-react/lib/IconExternal';
import Modal from 'canvas-core-react/lib/internal/Modal';
import Table from 'canvas-core-react/lib/Table';
import Search from 'canvas-core-react/lib/Search';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import TextButton from 'canvas-core-react/lib/TextButton';

import InputRadioButtonField from '../formFields/inputRadioButtonField';
import InputGroupField from '../formFields/inputGroupField';
import { WEB_FRAGMENT_RESULTS_LIMIT } from '../../constants';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';

class Fragment extends React.Component {
  static propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onSearch: PropTypes.func.isRequired,
    searchResults: PropTypes.array, // @todo make a detailed shape
    onSubmit: PropTypes.func.isRequired,
    offset: PropTypes.number.isRequired,
    limit: PropTypes.number.isRequired,
    searchTerm: PropTypes.string.isRequired,
    totalSearchResults: PropTypes.number,
    isLoading: PropTypes.bool,
  };

  static defaultProps = {
    searchResults: 0,
    searchTerm: '',
  };

  state = {
    focusElementId: '',
  };

  componentDidUpdate() {
    // Since input components are rendered in a modal, reference is lost when state change/rerenders occur
    // - need to reapply focus to the input if it was the last element in focus (set via the handleChange function)
    if (this.state.focusElementId && document.getElementById(this.state.focusElementId)) {
      setTimeout(() => document.getElementById(this.state.focusElementId).focus(), 1);
    }
  }

  handleChange = (e) => {
    if (e.target.id === 'web-fragment-search-input') {
      this.props.onSearch({ name: e.target.value });
    }
    this.setState({ focusElementId: e.target.id });
  }

  handleClickPreviewLink = (type, fragmentId) => () => {
    const newUrl = `/web-fragment-preview/${type}/${fragmentId}/`;
    window.open(newUrl, 'fragmentPreview', 'height=600,width=600,top=0,left=0');
  };

  renderSearchResults = () => {
    const { searchResults } = this.props;
    if (!searchResults) {
      return;
    }

    if (searchResults.length === 0) {
      return (
        <TextCaption
          className="content-modal__no-results"
          component="p"
        >
          No results returned.
        </TextCaption>
      );
    }

    return (
      <div className="content-modal__content-items">
        <Field
          name="content_id"
          component={InputGroupField}
        >
          <Table
            id="fragment-search-table"
            title=""
            className="content-modal__content-table"
            columns={[
              {
                name: 'Content Name',
                selector: '',
                cellFormatter: row => (
                  <Field
                    id={`${row.web_fragment_id}`}
                    name="content_id"
                    className='content-modal__content-table__content-name'
                    label={row.name}
                    component={InputRadioButtonField}
                    onChange={(id) => this.setState({ focusElementId: id })}
                  />
                ),
                grow: 2,
                style: { textAlign: 'left', wordBreak: 'break-word' },
              },
              {
                name: 'Last Updated Date',
                selector: '',
                cellFormatter: row => (
                  <TextCaption component="p">
                    { moment(row.updated_ts).format('lll') }
                  </TextCaption>
                ),
              },
              {
                name: 'Preview Link',
                selector: '',
                cellFormatter: row => (
                  <TextButton
                    Icon={IconExternal}
                    iconPosition="right"
                    onClick={this.handleClickPreviewLink(row.space, row.web_fragment_id)}
                  >
                    Preview
                  </TextButton>
                ),
              },
            ]}
            data={searchResults}
          />
        </Field>
      </div>
    );
  };

  renderPagination = () => {
    const { onSearch, searchTerm, searchResults, totalSearchResults } = this.props;
    if (!searchResults || searchResults.length === 0) {
      return;
    }

    return (
      <DesktopPagination
        id="pagination"
        totalResultCount={totalSearchResults}
        onChange={(page) => onSearch({ name: searchTerm, offset: (page - 1) * WEB_FRAGMENT_RESULTS_LIMIT })}
        firstButtonLabel="First"
        prevButtonLabel="Previous"
        nextButtonLabel="Next"
        lastButtonLabel="Last"
        navigationLabel="Pagination Navigation"
        pageSize={WEB_FRAGMENT_RESULTS_LIMIT}
        currentPage={totalSearchResults ? (this.props.offset / this.props.limit) + 1 : 1}
      />
    );
  }

  render() {
    const { isOpen, onClose, onSubmit } = this.props;

    return (
      <div className="content-modal">
        <Modal
          className="content-modal__modal"
          headline="Add a new web fragment"
          setModalVisible={onClose}
          isModalVisible={isOpen}
        >
          <Search
            className="content-modal__fragment-search-input"
            id="web-fragment-search"
            showLabel={false}
            label="Search for a web fragment..."
            onChange={this.handleChange}
            value=""
            clearButtonLabel="Clear search"
            searchButtonLabel="Search"
          />
          { this.renderSearchResults() }
          { this.renderPagination() }
          <div className="content-modal__action-bar">
            <SecondaryButton
              className="content-modal__action-button"
              onClick={onClose}
            >
              Cancel
            </SecondaryButton>
            <PrimaryButton
              className="content-modal__action-button"
              onClick={onSubmit}
            >
              Save
            </PrimaryButton>
          </div>
        </Modal>
      </div>
    );
  }
}

export default Fragment;
