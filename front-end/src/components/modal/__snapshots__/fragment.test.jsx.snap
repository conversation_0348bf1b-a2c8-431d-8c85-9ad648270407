// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FragmentModal Container All Errors 1`] = `
{
  "content_id": "You must select a Web fragment.",
}
`;

exports[`FragmentModal Container Snapshot 1`] = `
<Fragment
  isLoading={false}
  isOpen={true}
  limit={10}
  offset={0}
  onClose={[Function]}
  onSearch={[Function]}
  onSubmit={[Function]}
  searchResults={[]}
  searchTerm="query"
  totalSearchResults={10}
/>
`;

exports[`FragmentModal matches a mocked open state 1`] = `
<div
  className="content-modal"
>
  <f
    className="content-modal__modal"
    headline="Add a new web fragment"
    isModalVisible={true}
    isOnlyClosedByButton={false}
    message=""
    setModalVisible={[MockFunction]}
  >
    <v
      className="content-modal__fragment-search-input"
      clearButtonLabel="Clear search"
      disabled={false}
      error={null}
      id="web-fragment-search"
      inputRef={null}
      label="Search for a web fragment..."
      onChange={[Function]}
      onClear={null}
      onSearchClick={null}
      searchButtonLabel="Search"
      secondaryLabel={null}
      showLabel={false}
      tooltip={null}
      value=""
      variant="default"
    />
    <div
      className="content-modal__content-items"
    >
      <Field
        component={[Function]}
        name="content_id"
      >
        <ForwardRef
          className="content-modal__content-table"
          columnFixed={false}
          columns={
            [
              {
                "cellFormatter": [Function],
                "grow": 2,
                "name": "Content Name",
                "selector": "",
                "style": {
                  "textAlign": "left",
                  "wordBreak": "break-word",
                },
              },
              {
                "cellFormatter": [Function],
                "name": "Last Updated Date",
                "selector": "",
              },
              {
                "cellFormatter": [Function],
                "name": "Preview Link",
                "selector": "",
              },
            ]
          }
          data={
            [
              {
                "id": "a",
                "updated_ts": "2019-09-30T04:00:00.000Z",
                "web_fragment_id": "a",
              },
              {
                "id": "b",
                "updated_ts": "2019-09-30T04:00:00.000Z",
                "web_fragment_id": "b",
              },
              {
                "id": "c",
                "updated_ts": "2019-09-30T04:00:00.000Z",
                "web_fragment_id": "c",
              },
            ]
          }
          defaultSortOrder="desc"
          highlightOnHover={false}
          id="fragment-search-table"
          resetSortOnDataChange={true}
          size="small"
          striped={false}
          title=""
          titleSize={21}
          translateHeaderLabels={[Function]}
        />
      </Field>
    </div>
    <g
      containerType="card"
      currentPage={1}
      firstButtonLabel="First"
      id="pagination"
      lastButtonLabel="Last"
      localizePaginationText={[Function]}
      navigationLabel="Pagination Navigation"
      nextButtonLabel="Next"
      onChange={[Function]}
      pageSize={10}
      prevButtonLabel="Previous"
      totalResultCount={10}
      visiblePages={5}
    />
    <div
      className="content-modal__action-bar"
    >
      <d
        className="content-modal__action-button"
        disabled={false}
        labelPadding={36}
        onClick={[MockFunction]}
        size="regular"
      >
        Cancel
      </d>
      <d
        className="content-modal__action-button"
        disabled={false}
        labelPadding={36}
        onClick={[MockFunction]}
        size="regular"
      >
        Save
      </d>
    </div>
  </f>
</div>
`;

exports[`FragmentModal matches the snapshot when results are empty array 1`] = `
<div
  className="content-modal"
>
  <f
    className="content-modal__modal"
    headline="Add a new web fragment"
    isModalVisible={true}
    isOnlyClosedByButton={false}
    message=""
    setModalVisible={[MockFunction]}
  >
    <v
      className="content-modal__fragment-search-input"
      clearButtonLabel="Clear search"
      disabled={false}
      error={null}
      id="web-fragment-search"
      inputRef={null}
      label="Search for a web fragment..."
      onChange={[Function]}
      onClear={null}
      onSearchClick={null}
      searchButtonLabel="Search"
      secondaryLabel={null}
      showLabel={false}
      tooltip={null}
      value=""
      variant="default"
    />
    <f
      bold={false}
      className="content-modal__no-results"
      color="black"
      component="p"
      italic={false}
      numeric={false}
    >
      No results returned.
    </f>
    <div
      className="content-modal__action-bar"
    >
      <d
        className="content-modal__action-button"
        disabled={false}
        labelPadding={36}
        onClick={[MockFunction]}
        size="regular"
      >
        Cancel
      </d>
      <d
        className="content-modal__action-button"
        disabled={false}
        labelPadding={36}
        onClick={[MockFunction]}
        size="regular"
      >
        Save
      </d>
    </div>
  </f>
</div>
`;

exports[`FragmentModal matches the snapshot when results are null 1`] = `
<div
  className="content-modal"
>
  <f
    className="content-modal__modal"
    headline="Add a new web fragment"
    isModalVisible={true}
    isOnlyClosedByButton={false}
    message=""
    setModalVisible={[MockFunction]}
  >
    <v
      className="content-modal__fragment-search-input"
      clearButtonLabel="Clear search"
      disabled={false}
      error={null}
      id="web-fragment-search"
      inputRef={null}
      label="Search for a web fragment..."
      onChange={[Function]}
      onClear={null}
      onSearchClick={null}
      searchButtonLabel="Search"
      secondaryLabel={null}
      showLabel={false}
      tooltip={null}
      value=""
      variant="default"
    />
    <div
      className="content-modal__action-bar"
    >
      <d
        className="content-modal__action-button"
        disabled={false}
        labelPadding={36}
        onClick={[MockFunction]}
        size="regular"
      >
        Cancel
      </d>
      <d
        className="content-modal__action-button"
        disabled={false}
        labelPadding={36}
        onClick={[MockFunction]}
        size="regular"
      >
        Save
      </d>
    </div>
  </f>
</div>
`;
