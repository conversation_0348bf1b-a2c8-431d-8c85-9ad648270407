// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ContentModal Container All Errors 1`] = `
{
  "content_id": "You must select Contentful content.",
  "content_type": "You must select a Contentful model type.",
}
`;

exports[`ContentModal Container Snapshot 1`] = `""`;

exports[`ContentModal Empty Snapshot 1`] = `
<div
  className="content-modal"
>
  <f
    className="content-modal__modal"
    headline="Add content"
    isModalVisible={true}
    isOnlyClosedByButton={false}
    message=""
    setModalVisible={[MockFunction]}
  >
    <div
      className="content-modal__input-container"
    >
      <div
        className="content-modal__dropdown-container"
      >
        <div
          className="content-modal__dropdown"
        >
          <Field
            component={[Function]}
            label="Contentful Type"
            name="content_type"
            onChange={[Function]}
            options={[]}
            placeholder="Select Contentful model type…"
          />
        </div>
      </div>
    </div>
    <div
      className="content-modal__action-bar"
    >
      <d
        disabled={false}
        labelPadding={36}
        onClick={[MockFunction]}
        size="regular"
      >
        Cancel
      </d>
      <d
        className="content-modal__action-button"
        disabled={false}
        labelPadding={36}
        onClick={[MockFunction]}
        size="regular"
      >
        Select
      </d>
    </div>
  </f>
</div>
`;

exports[`ContentModal Loaded Snapshot 1`] = `
<div
  className="content-modal"
>
  <f
    className="content-modal__modal"
    headline="Add content"
    isModalVisible={true}
    isOnlyClosedByButton={false}
    message=""
    setModalVisible={[MockFunction]}
  >
    <div
      className="content-modal__input-container"
    >
      <div
        className="content-modal__dropdown-container"
      >
        <div
          className="content-modal__dropdown"
        >
          <Field
            component={[Function]}
            label="Contentful Type"
            name="content_type"
            onChange={[Function]}
            options={[]}
            placeholder="Select Contentful model type…"
          />
        </div>
      </div>
    </div>
    <f
      className="content-modal__content-card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <div
        className="content-modal__content-search"
      >
        <v
          className="content-modal__content-search-input"
          clearButtonLabel="Clear search"
          disabled={false}
          error={null}
          id="content-search"
          inputRef={null}
          label="Search by content name"
          onChange={[Function]}
          onClear={[MockFunction]}
          onSearchClick={null}
          searchButtonLabel="Search"
          secondaryLabel={null}
          showLabel={false}
          tooltip={null}
          value=""
          variant="default"
        />
      </div>
      <Field
        className="content-modal__content-items"
        component={[Function]}
        name="content_id"
      >
        <ForwardRef
          className="content-modal__content-table"
          columnFixed={false}
          columns={
            [
              {
                "cellFormatter": [Function],
                "grow": 1,
                "name": "Select",
                "selector": "",
              },
              {
                "cellFormatter": [Function],
                "grow": 5,
                "name": "Content Name",
                "overrideSortBehaviour": [Function],
                "selector": "name",
                "sortable": true,
              },
              {
                "cellFormatter": [Function],
                "grow": 2,
                "name": "Last Updated",
                "overrideSortBehaviour": [Function],
                "selector": "updated_at",
                "sortable": true,
              },
              {
                "cellFormatter": [Function],
                "grow": 2,
                "name": "Preview",
                "selector": "",
              },
            ]
          }
          data={
            [
              {
                "id": "1",
                "name": "Demo Alert",
                "updated_at": "0",
              },
              {
                "id": "2",
                "name": "Sample Alert #1",
                "updated_at": "0",
              },
            ]
          }
          defaultSortOrder="asc"
          highlightOnHover={false}
          id="content-search-table"
          resetSortOnDataChange={false}
          size="small"
          striped={true}
          title=""
          titleSize={21}
          translateHeaderLabels={[Function]}
        />
      </Field>
    </f>
    <div
      className="content-modal__action-bar"
    >
      <d
        disabled={false}
        labelPadding={36}
        onClick={[MockFunction]}
        size="regular"
      >
        Cancel
      </d>
      <d
        className="content-modal__action-button"
        disabled={false}
        labelPadding={36}
        onClick={[MockFunction]}
        size="regular"
      >
        Select
      </d>
    </div>
  </f>
</div>
`;

exports[`ContentModal Loading Snapshot 1`] = `
<div
  className="content-modal"
>
  <f
    className="content-modal__modal"
    headline="Add content"
    isModalVisible={true}
    isOnlyClosedByButton={false}
    message=""
    setModalVisible={[MockFunction]}
  >
    <div
      className="content-modal__input-container"
    >
      <div
        className="content-modal__dropdown-container"
      >
        <div
          className="content-modal__dropdown"
        >
          <Field
            component={[Function]}
            label="Contentful Type"
            name="content_type"
            onChange={[Function]}
            options={[]}
            placeholder="Select Contentful model type…"
          />
        </div>
      </div>
    </div>
    <f
      className="content-modal__content-card"
      lgPadding={36}
      mdPadding={30}
      smPadding={24}
      type="flatSolid"
      xsPadding={18}
    >
      <div
        className="content-modal__content-search"
      >
        <v
          className="content-modal__content-search-input"
          clearButtonLabel="Clear search"
          disabled={false}
          error={null}
          id="content-search"
          inputRef={null}
          label="Search by content name"
          onChange={[Function]}
          onClear={[MockFunction]}
          onSearchClick={null}
          searchButtonLabel="Search"
          secondaryLabel={null}
          showLabel={false}
          tooltip={null}
          value=""
          variant="default"
        />
      </div>
      <Field
        className="content-modal__content-items"
        component={[Function]}
        name="content_id"
      >
        <ForwardRef
          className="content-modal__content-table"
          columnFixed={false}
          columns={
            [
              {
                "cellFormatter": [Function],
                "grow": 1,
                "name": "Select",
                "selector": "",
              },
              {
                "cellFormatter": [Function],
                "grow": 5,
                "name": "Content Name",
                "overrideSortBehaviour": [Function],
                "selector": "name",
                "sortable": true,
              },
              {
                "cellFormatter": [Function],
                "grow": 2,
                "name": "Last Updated",
                "overrideSortBehaviour": [Function],
                "selector": "updated_at",
                "sortable": true,
              },
              {
                "cellFormatter": [Function],
                "grow": 2,
                "name": "Preview",
                "selector": "",
              },
            ]
          }
          data={[]}
          defaultSortOrder="asc"
          highlightOnHover={false}
          id="content-search-table"
          resetSortOnDataChange={false}
          size="small"
          striped={true}
          title=""
          titleSize={21}
          translateHeaderLabels={[Function]}
        />
      </Field>
      <div
        className="content-modal__no-results"
      >
        No results found
      </div>
    </f>
    <div
      className="content-modal__action-bar"
    >
      <d
        disabled={false}
        labelPadding={36}
        onClick={[MockFunction]}
        size="regular"
      >
        Cancel
      </d>
      <d
        className="content-modal__action-button"
        disabled={false}
        labelPadding={36}
        onClick={[MockFunction]}
        size="regular"
      >
        Select
      </d>
    </div>
  </f>
</div>
`;

exports[`ContentModal Snapshot 1`] = `
<div
  className="content-modal"
>
  <f
    className="content-modal__modal"
    headline="Add content"
    isModalVisible={true}
    isOnlyClosedByButton={false}
    message=""
    setModalVisible={[MockFunction]}
  >
    <div
      className="content-modal__input-container"
    >
      <div
        className="content-modal__dropdown-container"
      >
        <div
          className="content-modal__dropdown"
        >
          <Field
            component={[Function]}
            label="Contentful Type"
            name="content_type"
            onChange={[Function]}
            options={[]}
            placeholder="Select Contentful model type…"
          />
        </div>
      </div>
    </div>
    <div
      className="content-modal__action-bar"
    >
      <d
        disabled={false}
        labelPadding={36}
        onClick={[MockFunction]}
        size="regular"
      >
        Cancel
      </d>
      <d
        className="content-modal__action-button"
        disabled={false}
        labelPadding={36}
        onClick={[MockFunction]}
        size="regular"
      >
        Select
      </d>
    </div>
  </f>
</div>
`;
