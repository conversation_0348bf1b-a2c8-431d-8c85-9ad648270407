// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Version Targeting form Adds a new row if the add row button is clicked and current os row is completed (os) 1`] = `
<div>
  <button
    class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button version-targeting__button--add"
    color="blue"
  >
    <svg
      aria-hidden="true"
      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
      color="currentColor"
      focusable="false"
      role="presentation"
      size="16"
      viewBox="0 0 30 30"
    >
      <path
        d="M15.0001 2.27197L15.0001 27.7278"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M27.728 14.9999H2.27213"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <span
      class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
    >
      Add Version Targeting
    </span>
  </button>
  <div
    aria-describedby="modal-dialog-description"
    aria-labelledby="modal-dialog-label"
    aria-modal="true"
    class="ModalCorestyle__Wrapper-canvas-core__sc-18rt7pb-1 blUJhf ModalCorestyle__Animation-canvas-core__sc-18rt7pb-0 hDpJAs Modalstyle__Wrapper-canvas-core__sc-c1xona-0 dqKKNS Modal vt-modal modal-transition-exit modal-transition-exit-active"
    role="dialog"
    tabindex="-1"
  >
    <div
      class="Overlaystyle__Wrapper-canvas-core__sc-kfecg2-0 iDMoqz InternalOverlay"
    />
    <div
      class="Modalstyle__Container-canvas-core__sc-c1xona-1 lmWGgX Modal__container"
    >
      <div
        class="Modalstyle__Card-canvas-core__sc-c1xona-2 dalgiQ Modal__card"
      >
        <h1
          class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 gJZgKV modalHeadline"
          color="black"
          id="modal-dialog-label"
          size="24"
        >
          iOS version targeting
        </h1>
        
        <form>
          <ul>
            <li>
              <div
                class="vt-modal__row"
              >
                <div
                  class="input-select-field vt-modal__field vt-modal__field--type"
                >
                  <div
                    class="custom-select-tooltip"
                  >
                    <span
                      class="custom-select-label-tooltip"
                    >
                      Version type
                    </span>
                  </div>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                    >
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                      >
                        <label
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                          for="platformsTargeting[0].versionType"
                          label="Version type"
                        >
                          Version type
                        </label>
                      </div>
                      <select
                        aria-describedby=""
                        class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                        id="platformsTargeting[0].versionType"
                        name="platformsTargeting[0].versionType"
                      >
                        <option
                          class="Selector__placeholder"
                          disabled=""
                          value=""
                        >
                          Select type
                        </option>
                        <option
                          value="app"
                        >
                          App
                        </option>
                        <option
                          value="device"
                        >
                          Device
                        </option>
                        <option
                          value="os"
                        >
                          OS
                        </option>
                      </select>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <div
                  class="input-select-field vt-modal__field"
                >
                  <div
                    class="custom-select-tooltip"
                  >
                    <span
                      class="custom-select-label-tooltip"
                    >
                      Targeting criteria
                    </span>
                  </div>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                    >
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                      >
                        <label
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                          for="platformsTargeting[0].targetingCriteria"
                          label="Targeting criteria"
                        >
                          Targeting criteria
                        </label>
                      </div>
                      <select
                        aria-describedby=""
                        class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                        id="platformsTargeting[0].targetingCriteria"
                        name="platformsTargeting[0].targetingCriteria"
                      >
                        <option
                          class="Selector__placeholder"
                          disabled=""
                          value=""
                        >
                          Select criteria
                        </option>
                        <option
                          value="equal"
                        >
                          Equal (=)
                        </option>
                        <option
                          value="less"
                        >
                          Less than (&lt;)
                        </option>
                        <option
                          value="lessEqual"
                        >
                          Less than or equal to (&lt;=)
                        </option>
                        <option
                          value="greater"
                        >
                          Greater than (&gt;)
                        </option>
                        <option
                          value="greaterEqual"
                        >
                          Greater than or equal to (&gt;=)
                        </option>
                        <option
                          value="range"
                        >
                          Range between
                        </option>
                      </select>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <div
                  class="input-text-field"
                >
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                    >
                      <label
                        class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                        for="platformsTargeting[0].version-input"
                        id="platformsTargeting[0].version-label"
                        label="Version"
                      >
                        Version
                      </label>
                      <div
                        class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                        id="tooltip-container-undefined"
                      >
                        <div
                          class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                        >
                          <button
                            aria-label="Info"
                            class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
                            id="desktop-icon-undefined"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btSiGf"
                              color="black"
                              focusable="false"
                              role="presentation"
                              size="18"
                              viewBox="0 0 30 30"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                                fill="none"
                                fill-rule="evenodd"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M15 20.9062V14.1562"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <circle
                                cx="14.9998"
                                cy="9.1309"
                                r="0.7"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                    <input
                      aria-describedby=""
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 ddelfF Input__input TextFieldstyle__StyledInput-canvas-core__sc-w9yen5-0 liAfho"
                      id="platformsTargeting[0].version-input"
                      placeholder="Ex: 12.5.5"
                      type="text"
                      value="4.3"
                    />
                  </div>
                </div>
                <button
                  aria-label="Delete"
                  class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button vt-modal__button--delete-row"
                  color="blue"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="16"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M18.8977 13.5471L17.3499 21.9875"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M11.5088 13.5471L13.0567 21.9837"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M9.52832 6.70208V3.37484C9.52832 2.33931 10.3678 1.49984 11.4033 1.49984L17.2502 1.49984H19.3921C20.4276 1.49984 21.2671 2.3393 21.2671 3.37484V6.70208"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M1.72485 6.90275H28.2754"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M26.4994 7.38814C25.4572 13.6422 24.4149 19.8962 23.3727 26.1503C23.1473 27.5031 21.9109 28.5 20.4584 28.5H9.62805C8.15415 28.5 6.90641 27.4743 6.70552 26.0976L3.91528 6.9758"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <span
                    class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                  >
                    <span
                      class="vt-modal__delete-row-text"
                    >
                      Delete
                    </span>
                  </span>
                </button>
              </div>
            </li>
            <li>
              <div
                class="vt-modal__row"
              >
                <div
                  class="input-select-field vt-modal__field vt-modal__field--type"
                >
                  <div
                    class="custom-select-tooltip"
                  >
                    <span
                      class="custom-select-label-tooltip"
                    >
                      Version type
                    </span>
                  </div>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                    >
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                      >
                        <label
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                          for="platformsTargeting[1].versionType"
                          label="Version type"
                        >
                          Version type
                        </label>
                      </div>
                      <select
                        aria-describedby=""
                        class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                        id="platformsTargeting[1].versionType"
                        name="platformsTargeting[1].versionType"
                      >
                        <option
                          class="Selector__placeholder"
                          disabled=""
                          value=""
                        >
                          Select type
                        </option>
                        <option
                          value="app"
                        >
                          App
                        </option>
                        <option
                          value="device"
                        >
                          Device
                        </option>
                      </select>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Delete"
                  class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button vt-modal__button--delete-row"
                  color="blue"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="16"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M18.8977 13.5471L17.3499 21.9875"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M11.5088 13.5471L13.0567 21.9837"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M9.52832 6.70208V3.37484C9.52832 2.33931 10.3678 1.49984 11.4033 1.49984L17.2502 1.49984H19.3921C20.4276 1.49984 21.2671 2.3393 21.2671 3.37484V6.70208"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M1.72485 6.90275H28.2754"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M26.4994 7.38814C25.4572 13.6422 24.4149 19.8962 23.3727 26.1503C23.1473 27.5031 21.9109 28.5 20.4584 28.5H9.62805C8.15415 28.5 6.90641 27.4743 6.70552 26.0976L3.91528 6.9758"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <span
                    class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                  >
                    <span
                      class="vt-modal__delete-row-text"
                    >
                      Delete
                    </span>
                  </span>
                </button>
              </div>
              <button
                class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button vt-modal__button--add-row"
                color="blue"
                disabled=""
              >
                <span
                  class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                >
                  Add row
                </span>
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 jvjhqp TextButton__icon--right"
                  color="currentColor"
                  focusable="false"
                  role="presentation"
                  size="16"
                  viewBox="0 0 30 30"
                >
                  <path
                    d="M15.0001 2.27197L15.0001 27.7278"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M27.728 14.9999H2.27213"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </li>
          </ul>
          <div
            class="vt-modal__footer"
          >
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary vt-modal__button--cancel"
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Cancel
                </span>
              </span>
            </button>
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button PrimaryButtonstyle__StyledPrimaryButtonCore-canvas-core__sc-11pddd2-0 jbDssn Button__button--primary vt-modal__button--save"
              type="submit"
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Save
                </span>
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
`;

exports[`Version Targeting form Adds a new row if the add row button is clicked and current row is completed (app range) 1`] = `
<div>
  <button
    class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button version-targeting__button--add"
    color="blue"
  >
    <svg
      aria-hidden="true"
      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
      color="currentColor"
      focusable="false"
      role="presentation"
      size="16"
      viewBox="0 0 30 30"
    >
      <path
        d="M15.0001 2.27197L15.0001 27.7278"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M27.728 14.9999H2.27213"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <span
      class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
    >
      Add Version Targeting
    </span>
  </button>
  <div
    aria-describedby="modal-dialog-description"
    aria-labelledby="modal-dialog-label"
    aria-modal="true"
    class="ModalCorestyle__Wrapper-canvas-core__sc-18rt7pb-1 blUJhf ModalCorestyle__Animation-canvas-core__sc-18rt7pb-0 hDpJAs Modalstyle__Wrapper-canvas-core__sc-c1xona-0 dqKKNS Modal vt-modal modal-transition-exit modal-transition-exit-active"
    role="dialog"
    tabindex="-1"
  >
    <div
      class="Overlaystyle__Wrapper-canvas-core__sc-kfecg2-0 iDMoqz InternalOverlay"
    />
    <div
      class="Modalstyle__Container-canvas-core__sc-c1xona-1 lmWGgX Modal__container"
    >
      <div
        class="Modalstyle__Card-canvas-core__sc-c1xona-2 dalgiQ Modal__card"
      >
        <h1
          class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 gJZgKV modalHeadline"
          color="black"
          id="modal-dialog-label"
          size="24"
        >
          iOS version targeting
        </h1>
        
        <form>
          <ul>
            <li>
              <div
                class="vt-modal__row"
              >
                <div
                  class="input-select-field vt-modal__field vt-modal__field--type"
                >
                  <div
                    class="custom-select-tooltip"
                  >
                    <span
                      class="custom-select-label-tooltip"
                    >
                      Version type
                    </span>
                  </div>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                    >
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                      >
                        <label
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                          for="platformsTargeting[0].versionType"
                          label="Version type"
                        >
                          Version type
                        </label>
                      </div>
                      <select
                        aria-describedby=""
                        class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                        id="platformsTargeting[0].versionType"
                        name="platformsTargeting[0].versionType"
                      >
                        <option
                          class="Selector__placeholder"
                          disabled=""
                          value=""
                        >
                          Select type
                        </option>
                        <option
                          value="app"
                        >
                          App
                        </option>
                        <option
                          value="device"
                        >
                          Device
                        </option>
                        <option
                          value="os"
                        >
                          OS
                        </option>
                      </select>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <div
                  class="input-select-field vt-modal__field"
                >
                  <div
                    class="custom-select-tooltip"
                  >
                    <span
                      class="custom-select-label-tooltip"
                    >
                      Targeting criteria
                    </span>
                  </div>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                    >
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                      >
                        <label
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                          for="platformsTargeting[0].targetingCriteria"
                          label="Targeting criteria"
                        >
                          Targeting criteria
                        </label>
                      </div>
                      <select
                        aria-describedby=""
                        class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                        id="platformsTargeting[0].targetingCriteria"
                        name="platformsTargeting[0].targetingCriteria"
                      >
                        <option
                          class="Selector__placeholder"
                          disabled=""
                          value=""
                        >
                          Select criteria
                        </option>
                        <option
                          value="equal"
                        >
                          Equal (=)
                        </option>
                        <option
                          value="less"
                        >
                          Less than (&lt;)
                        </option>
                        <option
                          value="lessEqual"
                        >
                          Less than or equal to (&lt;=)
                        </option>
                        <option
                          value="greater"
                        >
                          Greater than (&gt;)
                        </option>
                        <option
                          value="greaterEqual"
                        >
                          Greater than or equal to (&gt;=)
                        </option>
                        <option
                          value="range"
                        >
                          Range between
                        </option>
                      </select>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <div
                  class="input-text-field vt-modal__field"
                >
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                    >
                      <label
                        class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                        for="platformsTargeting[0].minVersion-input"
                        id="platformsTargeting[0].minVersion-label"
                        label="Minimum version"
                      >
                        Minimum version
                      </label>
                      <div
                        class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                        id="tooltip-container-undefined"
                      >
                        <div
                          class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                        >
                          <button
                            aria-label="Info"
                            class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
                            id="desktop-icon-undefined"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btSiGf"
                              color="black"
                              focusable="false"
                              role="presentation"
                              size="18"
                              viewBox="0 0 30 30"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                                fill="none"
                                fill-rule="evenodd"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M15 20.9062V14.1562"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <circle
                                cx="14.9998"
                                cy="9.1309"
                                r="0.7"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                    <input
                      aria-describedby=""
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 ddelfF Input__input TextFieldstyle__StyledInput-canvas-core__sc-w9yen5-0 liAfho"
                      id="platformsTargeting[0].minVersion-input"
                      placeholder="Ex: 12.5.5-1"
                      type="text"
                      value="12.11.22"
                    />
                  </div>
                </div>
                <div
                  class="input-text-field"
                >
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                    >
                      <label
                        class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                        for="platformsTargeting[0].maxVersion-input"
                        id="platformsTargeting[0].maxVersion-label"
                        label="Maximum version"
                      >
                        Maximum version
                      </label>
                      <div
                        class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                        id="tooltip-container-undefined"
                      >
                        <div
                          class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                        >
                          <button
                            aria-label="Info"
                            class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
                            id="desktop-icon-undefined"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btSiGf"
                              color="black"
                              focusable="false"
                              role="presentation"
                              size="18"
                              viewBox="0 0 30 30"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                                fill="none"
                                fill-rule="evenodd"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M15 20.9062V14.1562"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <circle
                                cx="14.9998"
                                cy="9.1309"
                                r="0.7"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                    <input
                      aria-describedby=""
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 ddelfF Input__input TextFieldstyle__StyledInput-canvas-core__sc-w9yen5-0 liAfho"
                      id="platformsTargeting[0].maxVersion-input"
                      placeholder="Ex: 13.5.5-1"
                      type="text"
                      value="12.11.34"
                    />
                  </div>
                </div>
                <button
                  aria-label="Delete"
                  class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button vt-modal__button--delete-row"
                  color="blue"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="16"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M18.8977 13.5471L17.3499 21.9875"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M11.5088 13.5471L13.0567 21.9837"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M9.52832 6.70208V3.37484C9.52832 2.33931 10.3678 1.49984 11.4033 1.49984L17.2502 1.49984H19.3921C20.4276 1.49984 21.2671 2.3393 21.2671 3.37484V6.70208"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M1.72485 6.90275H28.2754"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M26.4994 7.38814C25.4572 13.6422 24.4149 19.8962 23.3727 26.1503C23.1473 27.5031 21.9109 28.5 20.4584 28.5H9.62805C8.15415 28.5 6.90641 27.4743 6.70552 26.0976L3.91528 6.9758"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <span
                    class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                  >
                    <span
                      class="vt-modal__delete-row-text"
                    >
                      Delete
                    </span>
                  </span>
                </button>
              </div>
            </li>
            <li>
              <div
                class="vt-modal__row"
              >
                <div
                  class="input-select-field vt-modal__field vt-modal__field--type"
                >
                  <div
                    class="custom-select-tooltip"
                  >
                    <span
                      class="custom-select-label-tooltip"
                    >
                      Version type
                    </span>
                  </div>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                    >
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                      >
                        <label
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                          for="platformsTargeting[1].versionType"
                          label="Version type"
                        >
                          Version type
                        </label>
                      </div>
                      <select
                        aria-describedby=""
                        class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                        id="platformsTargeting[1].versionType"
                        name="platformsTargeting[1].versionType"
                      >
                        <option
                          class="Selector__placeholder"
                          disabled=""
                          value=""
                        >
                          Select type
                        </option>
                        <option
                          value="device"
                        >
                          Device
                        </option>
                        <option
                          value="os"
                        >
                          OS
                        </option>
                      </select>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Delete"
                  class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button vt-modal__button--delete-row"
                  color="blue"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="16"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M18.8977 13.5471L17.3499 21.9875"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M11.5088 13.5471L13.0567 21.9837"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M9.52832 6.70208V3.37484C9.52832 2.33931 10.3678 1.49984 11.4033 1.49984L17.2502 1.49984H19.3921C20.4276 1.49984 21.2671 2.3393 21.2671 3.37484V6.70208"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M1.72485 6.90275H28.2754"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M26.4994 7.38814C25.4572 13.6422 24.4149 19.8962 23.3727 26.1503C23.1473 27.5031 21.9109 28.5 20.4584 28.5H9.62805C8.15415 28.5 6.90641 27.4743 6.70552 26.0976L3.91528 6.9758"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <span
                    class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                  >
                    <span
                      class="vt-modal__delete-row-text"
                    >
                      Delete
                    </span>
                  </span>
                </button>
              </div>
              <button
                class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button vt-modal__button--add-row"
                color="blue"
                disabled=""
              >
                <span
                  class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                >
                  Add row
                </span>
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 jvjhqp TextButton__icon--right"
                  color="currentColor"
                  focusable="false"
                  role="presentation"
                  size="16"
                  viewBox="0 0 30 30"
                >
                  <path
                    d="M15.0001 2.27197L15.0001 27.7278"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M27.728 14.9999H2.27213"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </li>
          </ul>
          <div
            class="vt-modal__footer"
          >
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary vt-modal__button--cancel"
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Cancel
                </span>
              </span>
            </button>
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button PrimaryButtonstyle__StyledPrimaryButtonCore-canvas-core__sc-11pddd2-0 jbDssn Button__button--primary vt-modal__button--save"
              type="submit"
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Save
                </span>
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
`;

exports[`Version Targeting form Adds a new row if the add row button is clicked and current row is completed (app) 1`] = `
<div>
  <button
    class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button version-targeting__button--add"
    color="blue"
  >
    <svg
      aria-hidden="true"
      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
      color="currentColor"
      focusable="false"
      role="presentation"
      size="16"
      viewBox="0 0 30 30"
    >
      <path
        d="M15.0001 2.27197L15.0001 27.7278"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M27.728 14.9999H2.27213"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <span
      class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
    >
      Add Version Targeting
    </span>
  </button>
  <div
    aria-describedby="modal-dialog-description"
    aria-labelledby="modal-dialog-label"
    aria-modal="true"
    class="ModalCorestyle__Wrapper-canvas-core__sc-18rt7pb-1 blUJhf ModalCorestyle__Animation-canvas-core__sc-18rt7pb-0 hDpJAs Modalstyle__Wrapper-canvas-core__sc-c1xona-0 dqKKNS Modal vt-modal modal-transition-exit modal-transition-exit-active"
    role="dialog"
    tabindex="-1"
  >
    <div
      class="Overlaystyle__Wrapper-canvas-core__sc-kfecg2-0 iDMoqz InternalOverlay"
    />
    <div
      class="Modalstyle__Container-canvas-core__sc-c1xona-1 lmWGgX Modal__container"
    >
      <div
        class="Modalstyle__Card-canvas-core__sc-c1xona-2 dalgiQ Modal__card"
      >
        <h1
          class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 gJZgKV modalHeadline"
          color="black"
          id="modal-dialog-label"
          size="24"
        >
          iOS version targeting
        </h1>
        
        <form>
          <ul>
            <li>
              <div
                class="vt-modal__row"
              >
                <div
                  class="input-select-field vt-modal__field vt-modal__field--type"
                >
                  <div
                    class="custom-select-tooltip"
                  >
                    <span
                      class="custom-select-label-tooltip"
                    >
                      Version type
                    </span>
                  </div>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                    >
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                      >
                        <label
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                          for="platformsTargeting[0].versionType"
                          label="Version type"
                        >
                          Version type
                        </label>
                      </div>
                      <select
                        aria-describedby=""
                        class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                        id="platformsTargeting[0].versionType"
                        name="platformsTargeting[0].versionType"
                      >
                        <option
                          class="Selector__placeholder"
                          disabled=""
                          value=""
                        >
                          Select type
                        </option>
                        <option
                          value="app"
                        >
                          App
                        </option>
                        <option
                          value="device"
                        >
                          Device
                        </option>
                        <option
                          value="os"
                        >
                          OS
                        </option>
                      </select>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <div
                  class="input-select-field vt-modal__field"
                >
                  <div
                    class="custom-select-tooltip"
                  >
                    <span
                      class="custom-select-label-tooltip"
                    >
                      Targeting criteria
                    </span>
                  </div>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                    >
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                      >
                        <label
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                          for="platformsTargeting[0].targetingCriteria"
                          label="Targeting criteria"
                        >
                          Targeting criteria
                        </label>
                      </div>
                      <select
                        aria-describedby=""
                        class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                        id="platformsTargeting[0].targetingCriteria"
                        name="platformsTargeting[0].targetingCriteria"
                      >
                        <option
                          class="Selector__placeholder"
                          disabled=""
                          value=""
                        >
                          Select criteria
                        </option>
                        <option
                          value="equal"
                        >
                          Equal (=)
                        </option>
                        <option
                          value="less"
                        >
                          Less than (&lt;)
                        </option>
                        <option
                          value="lessEqual"
                        >
                          Less than or equal to (&lt;=)
                        </option>
                        <option
                          value="greater"
                        >
                          Greater than (&gt;)
                        </option>
                        <option
                          value="greaterEqual"
                        >
                          Greater than or equal to (&gt;=)
                        </option>
                        <option
                          value="range"
                        >
                          Range between
                        </option>
                      </select>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <div
                  class="input-text-field"
                >
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                    >
                      <label
                        class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                        for="platformsTargeting[0].version-input"
                        id="platformsTargeting[0].version-label"
                        label="Version"
                      >
                        Version
                      </label>
                      <div
                        class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                        id="tooltip-container-undefined"
                      >
                        <div
                          class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                        >
                          <button
                            aria-label="Info"
                            class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
                            id="desktop-icon-undefined"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btSiGf"
                              color="black"
                              focusable="false"
                              role="presentation"
                              size="18"
                              viewBox="0 0 30 30"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                                fill="none"
                                fill-rule="evenodd"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M15 20.9062V14.1562"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <circle
                                cx="14.9998"
                                cy="9.1309"
                                r="0.7"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                    <input
                      aria-describedby=""
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 ddelfF Input__input TextFieldstyle__StyledInput-canvas-core__sc-w9yen5-0 liAfho"
                      id="platformsTargeting[0].version-input"
                      placeholder="Ex: 12.5.5-1"
                      type="text"
                      value="12.11.22"
                    />
                  </div>
                </div>
                <button
                  aria-label="Delete"
                  class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button vt-modal__button--delete-row"
                  color="blue"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="16"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M18.8977 13.5471L17.3499 21.9875"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M11.5088 13.5471L13.0567 21.9837"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M9.52832 6.70208V3.37484C9.52832 2.33931 10.3678 1.49984 11.4033 1.49984L17.2502 1.49984H19.3921C20.4276 1.49984 21.2671 2.3393 21.2671 3.37484V6.70208"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M1.72485 6.90275H28.2754"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M26.4994 7.38814C25.4572 13.6422 24.4149 19.8962 23.3727 26.1503C23.1473 27.5031 21.9109 28.5 20.4584 28.5H9.62805C8.15415 28.5 6.90641 27.4743 6.70552 26.0976L3.91528 6.9758"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <span
                    class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                  >
                    <span
                      class="vt-modal__delete-row-text"
                    >
                      Delete
                    </span>
                  </span>
                </button>
              </div>
            </li>
            <li>
              <div
                class="vt-modal__row"
              >
                <div
                  class="input-select-field vt-modal__field vt-modal__field--type"
                >
                  <div
                    class="custom-select-tooltip"
                  >
                    <span
                      class="custom-select-label-tooltip"
                    >
                      Version type
                    </span>
                  </div>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                    >
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                      >
                        <label
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                          for="platformsTargeting[1].versionType"
                          label="Version type"
                        >
                          Version type
                        </label>
                      </div>
                      <select
                        aria-describedby=""
                        class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                        id="platformsTargeting[1].versionType"
                        name="platformsTargeting[1].versionType"
                      >
                        <option
                          class="Selector__placeholder"
                          disabled=""
                          value=""
                        >
                          Select type
                        </option>
                        <option
                          value="device"
                        >
                          Device
                        </option>
                        <option
                          value="os"
                        >
                          OS
                        </option>
                      </select>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Delete"
                  class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button vt-modal__button--delete-row"
                  color="blue"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="16"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M18.8977 13.5471L17.3499 21.9875"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M11.5088 13.5471L13.0567 21.9837"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M9.52832 6.70208V3.37484C9.52832 2.33931 10.3678 1.49984 11.4033 1.49984L17.2502 1.49984H19.3921C20.4276 1.49984 21.2671 2.3393 21.2671 3.37484V6.70208"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M1.72485 6.90275H28.2754"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M26.4994 7.38814C25.4572 13.6422 24.4149 19.8962 23.3727 26.1503C23.1473 27.5031 21.9109 28.5 20.4584 28.5H9.62805C8.15415 28.5 6.90641 27.4743 6.70552 26.0976L3.91528 6.9758"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <span
                    class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                  >
                    <span
                      class="vt-modal__delete-row-text"
                    >
                      Delete
                    </span>
                  </span>
                </button>
              </div>
              <button
                class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button vt-modal__button--add-row"
                color="blue"
                disabled=""
              >
                <span
                  class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                >
                  Add row
                </span>
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 jvjhqp TextButton__icon--right"
                  color="currentColor"
                  focusable="false"
                  role="presentation"
                  size="16"
                  viewBox="0 0 30 30"
                >
                  <path
                    d="M15.0001 2.27197L15.0001 27.7278"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M27.728 14.9999H2.27213"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </li>
          </ul>
          <div
            class="vt-modal__footer"
          >
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary vt-modal__button--cancel"
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Cancel
                </span>
              </span>
            </button>
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button PrimaryButtonstyle__StyledPrimaryButtonCore-canvas-core__sc-11pddd2-0 jbDssn Button__button--primary vt-modal__button--save"
              type="submit"
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Save
                </span>
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
`;

exports[`Version Targeting form Adds a new row if the add row button is clicked and current row is completed (os range) 1`] = `
<div>
  <button
    class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button version-targeting__button--add"
    color="blue"
  >
    <svg
      aria-hidden="true"
      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
      color="currentColor"
      focusable="false"
      role="presentation"
      size="16"
      viewBox="0 0 30 30"
    >
      <path
        d="M15.0001 2.27197L15.0001 27.7278"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M27.728 14.9999H2.27213"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <span
      class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
    >
      Add Version Targeting
    </span>
  </button>
  <div
    aria-describedby="modal-dialog-description"
    aria-labelledby="modal-dialog-label"
    aria-modal="true"
    class="ModalCorestyle__Wrapper-canvas-core__sc-18rt7pb-1 blUJhf ModalCorestyle__Animation-canvas-core__sc-18rt7pb-0 hDpJAs Modalstyle__Wrapper-canvas-core__sc-c1xona-0 dqKKNS Modal vt-modal modal-transition-exit modal-transition-exit-active"
    role="dialog"
    tabindex="-1"
  >
    <div
      class="Overlaystyle__Wrapper-canvas-core__sc-kfecg2-0 iDMoqz InternalOverlay"
    />
    <div
      class="Modalstyle__Container-canvas-core__sc-c1xona-1 lmWGgX Modal__container"
    >
      <div
        class="Modalstyle__Card-canvas-core__sc-c1xona-2 dalgiQ Modal__card"
      >
        <h1
          class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 gJZgKV modalHeadline"
          color="black"
          id="modal-dialog-label"
          size="24"
        >
          iOS version targeting
        </h1>
        
        <form>
          <ul>
            <li>
              <div
                class="vt-modal__row"
              >
                <div
                  class="input-select-field vt-modal__field vt-modal__field--type"
                >
                  <div
                    class="custom-select-tooltip"
                  >
                    <span
                      class="custom-select-label-tooltip"
                    >
                      Version type
                    </span>
                  </div>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                    >
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                      >
                        <label
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                          for="platformsTargeting[0].versionType"
                          label="Version type"
                        >
                          Version type
                        </label>
                      </div>
                      <select
                        aria-describedby=""
                        class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                        id="platformsTargeting[0].versionType"
                        name="platformsTargeting[0].versionType"
                      >
                        <option
                          class="Selector__placeholder"
                          disabled=""
                          value=""
                        >
                          Select type
                        </option>
                        <option
                          value="app"
                        >
                          App
                        </option>
                        <option
                          value="device"
                        >
                          Device
                        </option>
                        <option
                          value="os"
                        >
                          OS
                        </option>
                      </select>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <div
                  class="input-select-field vt-modal__field"
                >
                  <div
                    class="custom-select-tooltip"
                  >
                    <span
                      class="custom-select-label-tooltip"
                    >
                      Targeting criteria
                    </span>
                  </div>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                    >
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                      >
                        <label
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                          for="platformsTargeting[0].targetingCriteria"
                          label="Targeting criteria"
                        >
                          Targeting criteria
                        </label>
                      </div>
                      <select
                        aria-describedby=""
                        class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                        id="platformsTargeting[0].targetingCriteria"
                        name="platformsTargeting[0].targetingCriteria"
                      >
                        <option
                          class="Selector__placeholder"
                          disabled=""
                          value=""
                        >
                          Select criteria
                        </option>
                        <option
                          value="equal"
                        >
                          Equal (=)
                        </option>
                        <option
                          value="less"
                        >
                          Less than (&lt;)
                        </option>
                        <option
                          value="lessEqual"
                        >
                          Less than or equal to (&lt;=)
                        </option>
                        <option
                          value="greater"
                        >
                          Greater than (&gt;)
                        </option>
                        <option
                          value="greaterEqual"
                        >
                          Greater than or equal to (&gt;=)
                        </option>
                        <option
                          value="range"
                        >
                          Range between
                        </option>
                      </select>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <div
                  class="input-text-field vt-modal__field"
                >
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                    >
                      <label
                        class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                        for="platformsTargeting[0].minVersion-input"
                        id="platformsTargeting[0].minVersion-label"
                        label="Minimum version"
                      >
                        Minimum version
                      </label>
                      <div
                        class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                        id="tooltip-container-undefined"
                      >
                        <div
                          class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                        >
                          <button
                            aria-label="Info"
                            class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
                            id="desktop-icon-undefined"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btSiGf"
                              color="black"
                              focusable="false"
                              role="presentation"
                              size="18"
                              viewBox="0 0 30 30"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                                fill="none"
                                fill-rule="evenodd"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M15 20.9062V14.1562"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <circle
                                cx="14.9998"
                                cy="9.1309"
                                r="0.7"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                    <input
                      aria-describedby=""
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 ddelfF Input__input TextFieldstyle__StyledInput-canvas-core__sc-w9yen5-0 liAfho"
                      id="platformsTargeting[0].minVersion-input"
                      placeholder="Ex: 12.5.5"
                      type="text"
                      value="12.11.22"
                    />
                  </div>
                </div>
                <div
                  class="input-text-field"
                >
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                    >
                      <label
                        class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                        for="platformsTargeting[0].maxVersion-input"
                        id="platformsTargeting[0].maxVersion-label"
                        label="Maximum version"
                      >
                        Maximum version
                      </label>
                      <div
                        class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                        id="tooltip-container-undefined"
                      >
                        <div
                          class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                        >
                          <button
                            aria-label="Info"
                            class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
                            id="desktop-icon-undefined"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btSiGf"
                              color="black"
                              focusable="false"
                              role="presentation"
                              size="18"
                              viewBox="0 0 30 30"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                                fill="none"
                                fill-rule="evenodd"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M15 20.9062V14.1562"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <circle
                                cx="14.9998"
                                cy="9.1309"
                                r="0.7"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                    <input
                      aria-describedby=""
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 ddelfF Input__input TextFieldstyle__StyledInput-canvas-core__sc-w9yen5-0 liAfho"
                      id="platformsTargeting[0].maxVersion-input"
                      placeholder="Ex: 13.5.5"
                      type="text"
                      value="12.11.34"
                    />
                  </div>
                </div>
                <button
                  aria-label="Delete"
                  class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button vt-modal__button--delete-row"
                  color="blue"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="16"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M18.8977 13.5471L17.3499 21.9875"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M11.5088 13.5471L13.0567 21.9837"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M9.52832 6.70208V3.37484C9.52832 2.33931 10.3678 1.49984 11.4033 1.49984L17.2502 1.49984H19.3921C20.4276 1.49984 21.2671 2.3393 21.2671 3.37484V6.70208"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M1.72485 6.90275H28.2754"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M26.4994 7.38814C25.4572 13.6422 24.4149 19.8962 23.3727 26.1503C23.1473 27.5031 21.9109 28.5 20.4584 28.5H9.62805C8.15415 28.5 6.90641 27.4743 6.70552 26.0976L3.91528 6.9758"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <span
                    class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                  >
                    <span
                      class="vt-modal__delete-row-text"
                    >
                      Delete
                    </span>
                  </span>
                </button>
              </div>
            </li>
            <li>
              <div
                class="vt-modal__row"
              >
                <div
                  class="input-select-field vt-modal__field vt-modal__field--type"
                >
                  <div
                    class="custom-select-tooltip"
                  >
                    <span
                      class="custom-select-label-tooltip"
                    >
                      Version type
                    </span>
                  </div>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                    >
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                      >
                        <label
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                          for="platformsTargeting[1].versionType"
                          label="Version type"
                        >
                          Version type
                        </label>
                      </div>
                      <select
                        aria-describedby=""
                        class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                        id="platformsTargeting[1].versionType"
                        name="platformsTargeting[1].versionType"
                      >
                        <option
                          class="Selector__placeholder"
                          disabled=""
                          value=""
                        >
                          Select type
                        </option>
                        <option
                          value="app"
                        >
                          App
                        </option>
                        <option
                          value="device"
                        >
                          Device
                        </option>
                      </select>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Delete"
                  class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button vt-modal__button--delete-row"
                  color="blue"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="16"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M18.8977 13.5471L17.3499 21.9875"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M11.5088 13.5471L13.0567 21.9837"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M9.52832 6.70208V3.37484C9.52832 2.33931 10.3678 1.49984 11.4033 1.49984L17.2502 1.49984H19.3921C20.4276 1.49984 21.2671 2.3393 21.2671 3.37484V6.70208"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M1.72485 6.90275H28.2754"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M26.4994 7.38814C25.4572 13.6422 24.4149 19.8962 23.3727 26.1503C23.1473 27.5031 21.9109 28.5 20.4584 28.5H9.62805C8.15415 28.5 6.90641 27.4743 6.70552 26.0976L3.91528 6.9758"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <span
                    class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                  >
                    <span
                      class="vt-modal__delete-row-text"
                    >
                      Delete
                    </span>
                  </span>
                </button>
              </div>
              <button
                class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button vt-modal__button--add-row"
                color="blue"
                disabled=""
              >
                <span
                  class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                >
                  Add row
                </span>
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 jvjhqp TextButton__icon--right"
                  color="currentColor"
                  focusable="false"
                  role="presentation"
                  size="16"
                  viewBox="0 0 30 30"
                >
                  <path
                    d="M15.0001 2.27197L15.0001 27.7278"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M27.728 14.9999H2.27213"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </li>
          </ul>
          <div
            class="vt-modal__footer"
          >
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary vt-modal__button--cancel"
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Cancel
                </span>
              </span>
            </button>
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button PrimaryButtonstyle__StyledPrimaryButtonCore-canvas-core__sc-11pddd2-0 jbDssn Button__button--primary vt-modal__button--save"
              type="submit"
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Save
                </span>
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
`;

exports[`Version Targeting form Does not add a new row if the add row button is clicked but current row is not completed 1`] = `
<div>
  <button
    class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button version-targeting__button--add"
    color="blue"
  >
    <svg
      aria-hidden="true"
      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
      color="currentColor"
      focusable="false"
      role="presentation"
      size="16"
      viewBox="0 0 30 30"
    >
      <path
        d="M15.0001 2.27197L15.0001 27.7278"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M27.728 14.9999H2.27213"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <span
      class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
    >
      Add Version Targeting
    </span>
  </button>
  <div
    aria-describedby="modal-dialog-description"
    aria-labelledby="modal-dialog-label"
    aria-modal="true"
    class="ModalCorestyle__Wrapper-canvas-core__sc-18rt7pb-1 blUJhf ModalCorestyle__Animation-canvas-core__sc-18rt7pb-0 hDpJAs Modalstyle__Wrapper-canvas-core__sc-c1xona-0 dqKKNS Modal vt-modal Modal--open modal-transition-enter modal-transition-enter-active"
    role="dialog"
    tabindex="-1"
  >
    <div
      class="Overlaystyle__Wrapper-canvas-core__sc-kfecg2-0 iDMoqz InternalOverlay"
    />
    <div
      class="Modalstyle__Container-canvas-core__sc-c1xona-1 lmWGgX Modal__container"
    >
      <div
        class="Modalstyle__Card-canvas-core__sc-c1xona-2 dalgiQ Modal__card"
      >
        <h1
          class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 gJZgKV modalHeadline"
          color="black"
          id="modal-dialog-label"
          size="24"
        >
          iOS version targeting
        </h1>
        
        <form>
          <ul>
            <li>
              <div
                class="vt-modal__row"
              >
                <div
                  class="input-select-field vt-modal__field vt-modal__field--type"
                >
                  <div
                    class="custom-select-tooltip"
                  >
                    <span
                      class="custom-select-label-tooltip"
                    >
                      Version type
                    </span>
                  </div>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                  >
                    <div
                      class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                    >
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                      >
                        <label
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                          for="platformsTargeting[0].versionType"
                          label="Version type"
                        >
                          Version type
                        </label>
                      </div>
                      <select
                        aria-describedby=""
                        class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                        id="platformsTargeting[0].versionType"
                        name="platformsTargeting[0].versionType"
                      >
                        <option
                          class="Selector__placeholder"
                          disabled=""
                          value=""
                        >
                          Select type
                        </option>
                        <option
                          value="app"
                        >
                          App
                        </option>
                        <option
                          value="device"
                        >
                          Device
                        </option>
                        <option
                          value="os"
                        >
                          OS
                        </option>
                      </select>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
              <button
                class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button vt-modal__button--add-row"
                color="blue"
                disabled=""
              >
                <span
                  class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                >
                  Add row
                </span>
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 jvjhqp TextButton__icon--right"
                  color="currentColor"
                  focusable="false"
                  role="presentation"
                  size="16"
                  viewBox="0 0 30 30"
                >
                  <path
                    d="M15.0001 2.27197L15.0001 27.7278"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M27.728 14.9999H2.27213"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </li>
          </ul>
          <div
            class="vt-modal__footer"
          >
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary vt-modal__button--cancel"
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Cancel
                </span>
              </span>
            </button>
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button PrimaryButtonstyle__StyledPrimaryButtonCore-canvas-core__sc-11pddd2-0 jbDssn Button__button--primary vt-modal__button--save"
              type="submit"
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Save
                </span>
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
`;

exports[`Version Targeting form matches the snapshot when passed empty values 1`] = `
<div>
  <button
    class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button version-targeting__button--add"
    color="blue"
  >
    <svg
      aria-hidden="true"
      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
      color="currentColor"
      focusable="false"
      role="presentation"
      size="16"
      viewBox="0 0 30 30"
    >
      <path
        d="M15.0001 2.27197L15.0001 27.7278"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M27.728 14.9999H2.27213"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <span
      class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
    >
      Add Version Targeting
    </span>
  </button>
</div>
`;

exports[`Version Targeting form matches the snapshot when populated from campaignDetails 1`] = `
<div>
  <button
    class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button version-targeting__button--edit"
    color="blue"
  >
    <svg
      aria-hidden="true"
      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
      color="currentColor"
      focusable="false"
      role="presentation"
      size="16"
      viewBox="0 0 30 30"
    >
      <path
        clip-rule="evenodd"
        d="M28.5 8.99917L8.99965 28.4999H1.5V21.0007L21.0004 1.49992L28.5 8.99917Z"
        fill="none"
        fill-rule="evenodd"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M16.1143 6.47101L23.4159 13.7729"
        fill="none"
        stroke-linecap="square"
      />
    </svg>
    <span
      class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
    >
      Edit
    </span>
  </button>
</div>
`;
