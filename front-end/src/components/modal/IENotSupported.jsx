import React, { useState } from 'react';
import ModalAlert from 'canvas-core-react/lib/ModalAlert';

const IENotSupported = () => {
  const [ IENotSupportedVisible, setIENotSupportedVisible ] = useState(/Trident\/(\d+\.\d+);.*rv:11\.|MSIE (\d+\.\d+);/.test(navigator.userAgent));

  return (
    <ModalAlert
      headline="Unsupported Browser"
      primaryButtonLabel="OK"
      isModalVisible={IENotSupportedVisible}
      primaryAction={() => setIENotSupportedVisible(false)}
      setModalVisible={setIENotSupportedVisible}
    >Your browser is not supported, please use Google Chrome.</ModalAlert>
  );
};

export default IENotSupported;
