import React from 'react';
import { shallow } from 'enzyme';
import { Provider } from 'react-redux';
import { reduxForm } from 'redux-form';
import configureStore from 'redux-mock-store';
import { render, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import ContentModal from './content';
import { ContentModalContainer, validate } from './contentContainer';
import { modalTypes, ruleTypes as ruleTypesConstants, CONTENTFUL_SPACES } from '../../constants';

const testValues = {
  content_id: 'test-content-id',
  content_type: 'test-content-type',
  container: 1,
  pages: { 'page-1': true },
};

const transformedValues = {
  content_id: 'test-content-id',
  content_type: 'test-content-type',
};

const testContainers = {
  1: {
    'id': 1,
    'name': 'Alert',
    'containerId': 'alert',
    'description': 'This is a description for the Alert container',
    'rule_type': 'alert',
    'content_type': null,
    'pages': [ {
      id: 1,
    } ],
  },
  2: {
    'id': 2,
    'name': 'My Activity',
    'containerId': 'my-activity',
    'description': 'This is a description for the My Activity container',
    'rule_type': 'campaign',
    'content_type': 'standingCampaignTemplate\\d+$',
    'pages': [ {
      id: 1,
    } ],
  },
};

const testContentTypes = {
  standingCampaign: {
    id: 'standingCampaign',
    name: 'standingCampaign',
    items: [
      {
        id: '123',
        name: 'Some standing campaign',
        updated_at: '********',
      },
    ],
  },
};

const testContent = {
  types: {
    alerts: {
      id: 'alerts',
      name: 'Alerts',
      items: [
        { id: '1', name: 'Demo Alert', updated_at: '0' },
        { id: '2', name: 'Sample Alert #1', updated_at: '0' },
      ],
    },
  },
};

const testPages = [
  { id: 1, description: 'accounts', pageId: 'accounts', name: 'accounts' },
];

describe('ContentModal Empty', () => {
  const wrapper = shallow(
    <ContentModal
      containers={testContainers}
      formValues={{}}
      isOpen={true}
      pages={{ items: [ { name: 'page-1' } ] }}
      onClose={jest.fn()}
      onSubmit={jest.fn()}
      contentTypeChanged={jest.fn()}
      content={testContent}
      pageClicked={jest.fn()}
      isSubmitDisabled={() => false}
      container={testContainers[1]}
      onSearch={jest.fn()}
      onClear={jest.fn()}
      onSort={jest.fn()}
    />);

  global.snapshot(wrapper);
});

describe('ContentModal Loading', () => {
  const wrapper = shallow(
    <ContentModal
      containers={testContainers}
      formValues={{ container: 2, content_type: 'alerts' }}
      isOpen={true}
      pages={{ items: [ { name: 'page-1' } ] }}
      onClose={jest.fn()}
      onSubmit={jest.fn()}
      contentTypeChanged={jest.fn()}
      activeType={'alerts'}
      content={{
        isLoading: true,
        types: {
          alerts: {
            id: 'alerts',
            name: 'Alerts',
            isLoading: true,
            items: [],
          },
        },
      }}
      pageClicked={jest.fn()}
      isSubmitDisabled={() => false}
      container={testContainers[1]}
      onSearch={jest.fn()}
      onClear={jest.fn()}
      onSort={jest.fn()}
    />);

  global.snapshot(wrapper);
});

describe('ContentModal Loaded', () => {
  const wrapper = shallow(
    <ContentModal
      containers={testContainers}
      formValues={{ container: 2, content_type: 'alerts' }}
      isOpen={true}
      pages={{ items: [ { name: 'page-1' } ] }}
      onClose={jest.fn()}
      onSubmit={jest.fn()}
      contentTypeChanged={jest.fn()}
      activeType={'alerts'}
      content={testContent}
      pageClicked={jest.fn()}
      isSubmitDisabled={() => false}
      container={testContainers[1]}
      onSearch={jest.fn()}
      onClear={jest.fn()}
      onSort={jest.fn()}
    />);

  global.snapshot(wrapper);
});

describe('ContentModal', () => {
  const wrapper = shallow(
    <ContentModal
      containers={testContainers}
      formValues={{ container: 1 }}
      isOpen={true}
      pages={{ items: [ { name: 'page-1' } ] }}
      onClose={jest.fn()}
      onSubmit={jest.fn()}
      contentTypeChanged={jest.fn()}
      activeType={'alerts'}
      content={{
        types: {
          alerts: {
            id: 'alerts',
            name: 'Alerts',
            items: [
              { id: '1', name: 'Demo Alert', updated_at: '0' },
              { id: '2', name: 'Sample Alert #1', updated_at: '0' },
            ],
          },
        },
      }}
      pageClicked={jest.fn()}
      isSubmitDisabled={() => false}
      container={testContainers[1]}
      onSearch={jest.fn()}
      onClear={jest.fn()}
      onSort={jest.fn()}
    />);

  global.snapshot(wrapper);
});

describe('ContentModal Container', () => {
  it('All Errors', () => {
    expect(validate({ pages: { page: false } })).toMatchSnapshot();
  });

  it('No Errors', () => {
    expect(validate(testValues)).toStrictEqual({});
  });

  beforeEach(() => {
    getContentTypes.mockClear();
  });

  const closeAllModals = jest.fn();
  const getContentTypes = jest.fn();
  const getContentItems = jest.fn();
  const onSave = jest.fn();
  const wrapper = shallow(
    <ContentModalContainer
      change={jest.fn()}
      untouch={jest.fn()}
      containers={testContainers}
      containerId={testContainers['1'].containerId}
      reset={jest.fn()}
      getPages={jest.fn()}
      pageSelections={[ { name: 'page-1', pageId: 'page-1' } ]}
      pages={{ items: [ { name: 'page-1' } ] }}
      formValues={{}}
      closeAllModals={closeAllModals}
      getContentTypes={getContentTypes}
      contentfulSpace={CONTENTFUL_SPACES.pigeon.id}
      getContentItems={getContentItems}
      onSave={onSave}
      content={testContent}
      onSearch={jest.fn()}
      onClear={jest.fn()}
      onSort={jest.fn()}
      container={testContainers[1]}
      onSubmit={jest.fn()}
      handleSubmit={f => f} />);

  global.snapshot(wrapper);

  it('getContentTypes()', () => {
    instance.componentDidUpdate({ contentfulSpace: CONTENTFUL_SPACES.wealth.id });
    expect(getContentTypes).toHaveBeenCalledTimes(1);
  });

  const instance = wrapper.instance();

  it('contentTypeChanged()', () => {
    instance.contentTypeChanged(null, testValues);
    expect(getContentItems).toHaveBeenCalledTimes(1);
    expect(getContentItems).toHaveBeenCalledWith({ type: testValues, contentfulSpace: CONTENTFUL_SPACES.pigeon.id });
  });

  it('pagination should work', () => {
    instance.pageClicked(1);
  });

  it('no containers', () => {
    shallow(
      <ContentModalContainer
        change={jest.fn()}
        untouch={jest.fn()}
        containers={null}
        reset={jest.fn()}
        getPages={jest.fn()}
        pages={{ items: [ { name: 'page-1' } ] }}
        formValues={{ pages: [ 1 ] }}
        closeAllModals={closeAllModals}
        getContentTypes={getContentTypes}
        getContentItems={getContentItems}
        onSave={onSave}
        content={testContent}
        container={testContainers[1]}
        onSearch={jest.fn()}
        onClear={jest.fn()}
        onSort={jest.fn()}
        handleSubmit={f => f} />);
  });

  it('onSubmit()', () => {
    instance.onSubmit(testValues);
    expect(onSave).toHaveBeenCalledTimes(1);
    expect(onSave).toHaveBeenCalledWith(transformedValues);
    expect(closeAllModals).toHaveBeenCalledTimes(1);
  });
});

describe('Content Modal Container - Search & Sort', () => {
  const mockDispatchProps = {
    closeAllModals: jest.fn(),
    getContentTypes: jest.fn(),
    getContentItems: jest.fn(),
    getPages: jest.fn(),
    handleSubmit: jest.fn(),
    reset: jest.fn(),
  };

  const mockStateProps = {
    modal: modalTypes.CONTENT,
    content: { isLoading: false, types: testContentTypes },
    pages: { isLoading: false, items: testPages },
    formValues: {
      container: '1',
      content_type: 'standingCampaign',
      pages: { accounts: false, acivities: true },
    },
  };

  const mockParentProps = {
    type: ruleTypesConstants.CAMPAIGN,
    container: testContainers['1'],
    applicationId: 'nova',
    onSave: jest.fn(),
  };

  it('should search for content by name and clear search', () => {
    const store = configureStore([])();
    const ReduxifiedContentModal = reduxForm({ form: 'contentDetails' })(ContentModalContainer);
    const { getByPlaceholderText, getByText } = render(
      <Provider store={store}>
        <ReduxifiedContentModal
          {...mockDispatchProps}
          {...mockStateProps}
          container={testContainers[1]}
          onSearch={jest.fn()}
          onClear={jest.fn()}
          onSort={jest.fn()}
          onSubmit={jest.fn()}
          handleSubmit={f => f}
          {...mockParentProps}
        />
      </Provider>
    );

    const searchBar = getByPlaceholderText('Search by content name');
    expect(searchBar).toBeInTheDocument();

    mockDispatchProps.getContentItems.mockReset();
    searchBar.focus();
    fireEvent.mouseDown(document.activeElement || document.body);
    const searchString = 'Some standing campaign';
    userEvent.type(searchBar, searchString);
    const apiSearchString = 'Som'; // Content search api triggered after entering 3 characters
    expect(mockDispatchProps.getContentItems).toHaveBeenCalledWith({ type: 'standingCampaign', name: apiSearchString, page: 1 });

    const clearSearchBtn = getByText('Clear username');
    expect(clearSearchBtn).toBeInTheDocument();

    mockDispatchProps.getContentItems.mockReset();
    fireEvent.click(clearSearchBtn);
    expect(mockDispatchProps.getContentItems).toHaveBeenCalledWith({ type: 'standingCampaign', page: 1 });
  });

  it('should sort content by name and updated at', () => {
    const store = configureStore([])();
    const ReduxifiedContentModal = reduxForm({ form: 'contentDetails' })(ContentModalContainer);
    const { getByText } = render(
      <Provider store={store}>
        <ReduxifiedContentModal
          {...mockDispatchProps}
          {...mockStateProps}
          {...mockParentProps}
          container={testContainers[1]}
          onSearch={jest.fn()}
          onClear={jest.fn()}
          handleSubmit={f => f}
          onSort={jest.fn()}
        />
      </Provider>
    );

    mockDispatchProps.getContentItems.mockReset();
    fireEvent.click(getByText('Content Name'));
    expect(mockDispatchProps.getContentItems).toHaveBeenCalledWith({
      contentfulSpace: undefined,
      name: undefined,
      page: 1,
      sort: 'fields.name',
      type: 'standingCampaign',
    });

    mockDispatchProps.getContentItems.mockReset();
    fireEvent.click(getByText('Last Updated'));
    expect(mockDispatchProps.getContentItems).toHaveBeenCalledWith({
      contentfulSpace: undefined,
      name: undefined,
      page: 1,
      sort: 'sys.updatedAt',
      type: 'standingCampaign',
    });
  });
});

describe('Content modal (content testing)', () => {
  const contentTypes = {
    'dog': { id: 'dog', name: 'Dog Content Model' },
    'cat': { id: 'cat', name: 'Cat Content Model' },
    'car': { id: 'car', name: 'Car Content Model' },
  };

  const commonProps = {
    container: testContainers['1'],
    formValues: {},
    isOpen: true,
    pages: testPages,
    pageSelections: testPages[0].pageId,
    onClose: jest.fn(),
    onSubmit: jest.fn(),
    contentTypeChanged: jest.fn(),
    content: { isLoading: false, types: contentTypes },
    pageClicked: jest.fn(),
    onSearch: jest.fn(),
    isSubmitDisabled: jest.fn(),
    onClear: jest.fn(),
    onSort: jest.fn(),
  };

  const mockStore = configureStore([])({});
  const ContentModalComponent = (props) => {
    const ReduxifiedContentModal = reduxForm({ form: 'contentDetails' })(ContentModal);
    return (
      <Provider store={mockStore}>
        <ReduxifiedContentModal {...props} />
      </Provider>
    );
  };

  test('correct options are shown in dropdown based on the content type', () => {
    const containers = {
      1: {
        'id': 1,
        'name': 'Alert',
        'containerId': 'alert',
        'description': 'Alert container',
        'rule_type': 'alert',
        'content_type': [ 'dog', 'cat' ],
        'pages': [ {
          id: 1,
        } ],
      },
      2: {
        'id': 2,
        'name': 'Offers and Programs',
        'containerId': 'accounts',
        'description': 'O&P container',
        'rule_type': 'campaign',
        'content_type': 'car',
        'pages': [ {
          id: 1,
        } ],
      },
    };

    // render ContentModal with two containers that have both styles of content (one with regex, one with array)
    const { rerender, queryByText } = render(
      <ContentModalComponent
        {...commonProps}
        container={containers['1']}
      />
    );
    expect(queryByText('Cat Content Model')).toBeInTheDocument();
    expect(queryByText('Dog Content Model')).toBeInTheDocument();
    expect(queryByText('Car Content Model')).not.toBeInTheDocument();

    rerender(
      <ContentModalComponent
        {...commonProps}
        formValues={{ content_type: 'car' }}
        container={containers['2']}
      />
    );
    expect(queryByText('Car Content Model')).toBeInTheDocument();
  });
});
