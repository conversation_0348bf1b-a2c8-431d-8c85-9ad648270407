import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { Field } from 'redux-form';

import Card from 'canvas-core-react/lib/Card';
import IconExternal from 'canvas-core-react/lib/IconExternal';
import Modal from 'canvas-core-react/lib/internal/Modal';
import Search from 'canvas-core-react/lib/Search';
import Table from 'canvas-core-react/lib/Table';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import TextButton from 'canvas-core-react/lib/TextButton';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';

import InputGroupField from '../formFields/inputGroupField';
import InputSelectField from '../formFields/inputSelectField';
import InputRadioButtonField from '../formFields/inputRadioButtonField';
import { openPreviewWindow, ruleTypes } from '../../constants';

export default class Content extends React.Component {
  static propTypes = {
    type: PropTypes.oneOf([ ruleTypes.CAMPAIGN, ruleTypes.ALERT, ruleTypes.CCAU_CAMPAIGN ]),
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onSubmit: PropTypes.func.isRequired,
    contentTypeChanged: PropTypes.func.isRequired,
    pageClicked: PropTypes.func.isRequired,
    content: PropTypes.shape({
      types: PropTypes.objectOf(
        PropTypes.shape({
          id: PropTypes.string.isRequired,
          name: PropTypes.string.isRequired,
          display_field: PropTypes.string,
          total: PropTypes.number,
          items: PropTypes.arrayOf(
            PropTypes.shape({
              id: PropTypes.string.isRequired,
              updated_at: PropTypes.string.isRequired,
              content: PropTypes.object,
            })),
        })),
      isLoading: PropTypes.bool,
    }).isRequired,
    container: PropTypes.object.isRequired,
    formValues: PropTypes.object.isRequired,
    onSearch: PropTypes.func.isRequired,
    onClear: PropTypes.func.isRequired,
    onSort: PropTypes.func.isRequired,
    isSubmitDisabled: PropTypes.func.isRequired,
    isDismissible: PropTypes.bool,
    contentfulSpace: PropTypes.string,
  };

  state = {
    focusElementId: '',
  };

  componentDidUpdate() {
    // Since input components are rendered in a modal, reference is lost when state change/rerenders occur
    // - need to reapply focus to the input if it was the last element in focus (set via the handleChange function)
    if (this.state.focusElementId && document.getElementById(this.state.focusElementId)) {
      setTimeout(() => document.getElementById(this.state.focusElementId).focus(), 1);
    }
  }

  handleChange = (e, value) => {
    switch (e.target.id) {
      case 'content_type':
        this.props.contentTypeChanged(e, value);
        break;
      case 'content-search-input':
        this.props.onSearch(e.target.value);
        break;
      default:
        break;
    };
    this.setState({ focusElementId: e.target.id });
  }

  sortableColumnProperties = columnKey => {
    const onColumnSort = columnKey => (row, direction) => this.props.onSort(columnKey, direction === 1);

    return ({
      sortable: true,
      overrideSortBehaviour: onColumnSort(columnKey),
    });
  };

  render() {
    const { formValues, content: { types: contentTypes }, container } = this.props;
    const isDisabled = this.props.isSubmitDisabled();
    const activeContent = contentTypes && formValues.content_type && contentTypes[formValues.content_type];

    let filteredContentTypes;
    if (container) {
      const containerContentType = container.content_type;
      if (typeof containerContentType === 'string') {
        let regex = RegExp('.*');
        try {
          regex = RegExp(containerContentType);
        } catch (err) {
        }
        filteredContentTypes = Object.values(contentTypes || {}).filter(i => regex.test(i.id));
      } else if (containerContentType?.length > 0) {
        filteredContentTypes = Object.values(contentTypes || {}).filter(i => containerContentType.includes(i.id));
      } else {
        filteredContentTypes = [];
      }
    }

    return (
      <div className="content-modal">
        <Modal
          className="content-modal__modal"
          headline="Add content"
          isModalVisible={this.props.isOpen}
          setModalVisible={this.props.onClose}
        >
          <div className="content-modal__input-container">
            <div className="content-modal__dropdown-container">
              { container && (
                <div className="content-modal__dropdown">
                  <Field
                    name="content_type"
                    label="Contentful Type"
                    placeholder="Select Contentful model type&hellip;"
                    component={InputSelectField}
                    options={filteredContentTypes}
                    onChange={(e, value) => this.handleChange(e, value)}
                  />
                </div>
              ) }
            </div>
          </div>

          { activeContent && (
            <Card className="content-modal__content-card">
              <div className="content-modal__content-search">
                <Search
                  className="content-modal__content-search-input"
                  id="content-search"
                  showLabel={false}
                  label="Search by content name"
                  searchButtonLabel="Search"
                  clearButtonLabel="Clear search"
                  value={''}
                  onChange={e => this.handleChange(e)}
                  onClear={this.props.onClear}
                />
              </div>
              <Field
                className="content-modal__content-items"
                name="content_id"
                component={InputGroupField}
              >
                <Table
                  id="content-search-table"
                  title=""
                  className="content-modal__content-table"
                  resetSortOnDataChange={false}
                  columns={[
                    {
                      name: 'Select',
                      selector: '',
                      grow: 1,
                      cellFormatter: ({ id }) => (
                        <Field
                          id={id || 'content_id'}
                          name="content_id"
                          label=""
                          component={InputRadioButtonField}
                          onChange={id => this.setState({ focusElementId: id })}
                        />
                      ),
                    },
                    {
                      name: 'Content Name',
                      selector: 'name',
                      grow: 5,
                      cellFormatter: ({ name }) => (
                        <TextCaption component="p">{ name }</TextCaption>
                      ),
                      ...this.sortableColumnProperties('fields.name'),
                    },
                    {
                      name: 'Last Updated',
                      selector: 'updated_at',
                      grow: 2,
                      cellFormatter: row => (
                        <TextCaption component="p">
                          { moment(row['updated_at']).format('lll') }
                        </TextCaption>
                      ),
                      ...this.sortableColumnProperties('sys.updatedAt'),
                    },
                    {
                      name: 'Preview',
                      selector: '',
                      grow: 2,
                      cellFormatter: row => (
                        <TextButton
                          type="button"
                          Icon={IconExternal}
                          iconPosition="right"
                          onClick={openPreviewWindow(
                            row.type,
                            row.id,
                            container.containerId,
                            this.props.isDismissible,
                            this.props.contentfulSpace,
                            container.applicationId
                          )}
                        >
                          Preview
                        </TextButton>
                      ),
                    },
                  ]}
                  defaultSortOrder="asc"
                  data={activeContent.items || []}
                  striped={true}
                />
              </Field>
              { activeContent.items?.length === 0 && (
                <div className="content-modal__no-results">
                  No results found
                </div>
              ) }
              { activeContent.total > activeContent.limit && (
                <DesktopPagination
                  id="pagination"
                  totalResultCount={activeContent.total}
                  onChange={this.props.pageClicked}
                  firstButtonLabel="First"
                  prevButtonLabel="Previous"
                  nextButtonLabel="Next"
                  lastButtonLabel="Last"
                  visiblePages={3}
                  navigationLabel="Pagination Navigation"
                  pageSize={activeContent.limit}
                  currentPage={
                    activeContent
                      ? activeContent.offset / activeContent.limit + 1
                      : 1
                  }
                  containerType="border-top"
                />
              ) }
            </Card>
          ) }

          <div className="content-modal__action-bar">
            <SecondaryButton onClick={this.props.onClose}>
              Cancel
            </SecondaryButton>
            <PrimaryButton
              className="content-modal__action-button"
              disabled={isDisabled}
              onClick={this.props.onSubmit}
            >
              Select
            </PrimaryButton>
          </div>
        </Modal>
      </div>
    );
  }
}
