import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { reduxForm, change } from 'redux-form';

import FragmentModal from '../modal/fragment';
import { closeAllModals } from '../../store/actions/modal';
import { getWebFragments, selectWebFragment, clearSelectedWebFragment } from '../../store/actions/content';
import { modalTypes } from '../../constants';
import { debounce } from '../../utils';

export const validate = (values) => {
  const errors = {};

  if (!values.content_id) {
    errors.content_id = 'You must select a Web fragment.';
  }

  return errors;
};

const mapStateToProps = (state) => ({
  modal: state.modal,
  isLoading: state.content.webFragments && state.content.webFragments.isLoading,
  name: state.content.webFragments && state.content.webFragments.name,
  offset: state.content.webFragments?.offset || 0,
  limit: state.content.webFragments?.limit || 0,
  webFragments: state.content?.webFragments?.items || [],
  totalWebFragments: state.content.webFragments && state.content.webFragments.total,
});

const mapDispatchToProps = dispatch => ({
  clearSelectedWebFragment: () => dispatch(clearSelectedWebFragment()),
  selectWebFragment: (contentId) => dispatch(selectWebFragment(contentId)),
  getWebFragments: (props) => dispatch(getWebFragments(props)),
  closeAllModals: () => dispatch(closeAllModals()),
  formChange: (formName, formField, formValue) => dispatch(change(formName, formField, formValue)),
});

export class FragmentContainer extends React.Component {
  static propTypes = {
    name: PropTypes.string,
    webFragments: PropTypes.array,
    totalWebFragments: PropTypes.number,
    isLoading: PropTypes.bool,
    modal: PropTypes.string,
    handleSubmit: PropTypes.func,
    reset: PropTypes.func,
    clearSelectedWebFragment: PropTypes.func,
    closeAllModals: PropTypes.func,
    selectWebFragment: PropTypes.func,
    getWebFragments: PropTypes.func,
    formChange: PropTypes.func,
    type: PropTypes.string.isRequired,
    offset: PropTypes.number.isRequired,
    limit: PropTypes.number.isRequired,
  };

  state = {
    scrollPosition: null,
  };

  componentDidMount() {
    this.onSearch = debounce(this.onSearch, 500);
  }

  getSnapshotBeforeUpdate(prevProps, prevState) {
    // if the modal was closed and its now being opened
    if (!this.isModalOpen(prevProps) && this.isModalOpen()) {
      document.body.classList.add('modal-open');
      this.setState({ scrollPosition: window.scrollY });
    }
    return null;
  }

  // React has a warning about using getSnapshotBeforeUpdate() without componentDidUpdate() but we are using the
  // above method to record scroll position before the modal opens
  componentDidUpdate(prevProps, prevState, snapshot) {}

  componentWillUnmount() {
    this.onClose();
  }

  isModalOpen = (propsToUse) => {
    // for use with prevProps, otherwise use this.props
    if (propsToUse) {
      return propsToUse.modal === modalTypes.FRAGMENT;
    }
    return this.props.modal === modalTypes.FRAGMENT;
  };

  onClose = (clearWebFragment = true) => {
    this.props.reset();
    this.props.closeAllModals();
    document.body.classList.remove('modal-open');

    if (clearWebFragment) {
      this.props.clearSelectedWebFragment();
    }
  };

  onSearch = ({ name, offset, limit }) => {
    const searchTerm = name.length >= 3 ? name : '';
    this.props.getWebFragments({ name: searchTerm, offset, limit, type: this.props.type });
  };

  onSubmit = values => {
    const { formChange } = this.props;
    formChange('vignetteDetails', 'content_id', values['content_id']);
    this.props.selectWebFragment(this.props.webFragments.find(i => i.web_fragment_id === Number(values['content_id'])));
    this.onClose(false);
    window.requestAnimationFrame(() => window.scrollTo(0, this.state.scrollPosition));
  };

  render() {
    const { name, webFragments, totalWebFragments, isLoading } = this.props;
    return (
      <FragmentModal
        isLoading={isLoading}
        searchTerm={name}
        isOpen={this.props.modal === modalTypes.FRAGMENT}
        searchResults={webFragments}
        totalSearchResults={totalWebFragments}
        onClose={this.onClose}
        onSubmit={this.props.handleSubmit(this.onSubmit)}
        onSearch={this.onSearch}
        offset={this.props.offset}
        limit={this.props.limit}
      />
    );
  }
}
@connect(mapStateToProps, mapDispatchToProps)
@reduxForm({
  form: 'fragmentModal',
  validate,
  initialValues: {},
  shouldError: () => true,
})
export default class FragmentContainerConnected extends FragmentContainer {}
