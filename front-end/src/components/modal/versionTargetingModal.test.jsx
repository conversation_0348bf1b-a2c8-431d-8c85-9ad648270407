import React from 'react';
import { Provider } from 'react-redux';
import { fireEvent, render } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { VersionTargetingModal } from './versionTargetingModal';
import { populateCampaignDetails } from '../../store/actions/campaigns';

describe('Version Targeting form', () => {
  const emptyState = [
    {
      platform: 'ios',
      items: [],
      v: 1,
    },
    {
      platform: 'android',
      items: [],
      v: 1,
    },
  ];
  // converted from DB semver to FE redux in reducer
  const parentState = [
    {
      platform: 'ios',
      items: [
        { app_version: '>=1.2.3', device_model: 'iPhone11,2', os_version: '13.2.3 - 13.3.4' },
        { device_model: 'iPhone10,2' },
      ],
      v: 1,
    }, {
      platform: 'android',
      items: [
        { app_version: '10.11.12-xyz', device: 'Pixel+4' },
        { os_version: '1.2.3 - 2.3.4' },
      ],
      v: 1,
    },
  ];

  let store;

  const renderForm = (targetingState = emptyState, platform = 'ios', selected = undefined) => {
    jest.useFakeTimers(); // required for modal input fields' manual focus logic
    store = global.newStore();
    store.dispatch(populateCampaignDetails({
      application: 'nova',
      platforms_targeting: targetingState,
      platforms: [ 'ios', 'android' ],
      type: 'targeted',
    }));

    const renderedObject = render(
      <Provider store={store}>
        <VersionTargetingModal platform={platform} selected={selected} formName="campaignDetails"/>
      </Provider>
    );
    rerenderForm(renderedObject.rerender, platform, selected);
    return renderedObject;
  };

  const rerenderForm = (rerenderFunction, platform = 'ios', selected = undefined) => {
    rerenderFunction(
      <Provider store={store}>
        <VersionTargetingModal platform={platform} selected={selected} formName="campaignDetails"/>
      </Provider>
    );
  };

  it('matches the snapshot when passed empty values', () => {
    const { container } = renderForm();
    expect(container).toMatchSnapshot();
  });

  it('matches the snapshot when populated from campaignDetails', () => {
    const selected = 0;
    const { container } = renderForm(parentState, 'ios', selected);
    expect(container).toMatchSnapshot();
  });

  const addRow = (
    getByText,
    getByLabelText,
    rerender,
    versionType = 'app',
    platform = 'ios',
    selected = undefined
  ) => {
    if (versionType === 'device') {
      userEvent.selectOptions(getByLabelText('Version type'), 'device');
      userEvent.type(getByLabelText('Model'), 'iPhone 11');
      rerenderForm(rerender, platform, selected);
      expect(getByText('Equal (=)').selected).toBe(true);
      expect(getByLabelText('Model').value).toBe('iPhone 11');
    }

    fireEvent.click(getByText('Add row'));
    rerenderForm(rerender, platform, selected);
  };

  it('Does not add a new row if the add row button is clicked but current row is not completed', async() => {
    const { container, getByText, findAllByLabelText, rerender } = renderForm();
    userEvent.click(getByText('Add Version Targeting')); // open modal
    userEvent.click(getByText('Add row'));
    rerenderForm(rerender);
    const inputFields = await findAllByLabelText('Version type');
    expect(inputFields).toHaveLength(1);
    expect(container).toMatchSnapshot();
  });

  it('Adds a new row if the add row button is clicked and current row is completed (app)', async() => {
    const {
      container,
      findAllByLabelText,
      getByText,
      getByLabelText,
      rerender,
    } = renderForm();
    userEvent.click(getByText('Add Version Targeting')); // open modal

    userEvent.selectOptions(getByLabelText('Version type'), 'app');
    userEvent.selectOptions(getByLabelText('Targeting criteria'), 'less');
    userEvent.type(getByLabelText('Version'), '12.11.22');
    rerenderForm(rerender, 'ios', undefined);
    expect(getByText('App').selected).toBe(true);
    expect(getByText('Less than (<)').selected).toBe(true);
    expect(getByLabelText('Version').value).toBe('12.11.22');

    fireEvent.click(getByText('Add row'));
    rerenderForm(rerender, 'ios', undefined);
    const inputFields = await findAllByLabelText('Version type');
    expect(inputFields).toHaveLength(2);
    expect(container).toMatchSnapshot();
  });

  it('Adds a new row if the add row button is clicked and current row is completed (app range)', async() => {
    const {
      container,
      findAllByLabelText,
      getByText,
      getByLabelText,
      rerender,
    } = renderForm();
    userEvent.click(getByText('Add Version Targeting')); // open modal

    userEvent.selectOptions(getByLabelText('Version type'), 'app');
    userEvent.selectOptions(getByLabelText('Targeting criteria'), 'range');
    userEvent.type(getByLabelText('Minimum version'), '12.11.22');
    userEvent.type(getByLabelText('Maximum version'), '12.11.34');
    rerenderForm(rerender, 'ios', undefined);
    expect(getByText('App').selected).toBe(true);
    expect(getByText('Range between').selected).toBe(true);
    expect(getByLabelText('Minimum version').value).toBe('12.11.22');
    expect(getByLabelText('Maximum version').value).toBe('12.11.34');

    fireEvent.click(getByText('Add row'));
    rerenderForm(rerender, 'ios', undefined);
    const inputFields = await findAllByLabelText('Version type');
    expect(inputFields).toHaveLength(2);
    expect(container).toMatchSnapshot();
  });

  it('Adds a new row if the add row button is clicked and current os row is completed (os)', async() => {
    const {
      container,
      findAllByLabelText,
      getByText,
      getByLabelText,
      rerender,
    } = renderForm();
    userEvent.click(getByText('Add Version Targeting')); // open modal

    userEvent.selectOptions(getByLabelText('Version type'), 'os');
    userEvent.selectOptions(getByLabelText('Targeting criteria'), 'greaterEqual');
    userEvent.type(getByLabelText('Version'), '4.3');
    rerenderForm(rerender, 'ios', undefined);
    expect(getByText('OS').selected).toBe(true);
    expect(getByText('Greater than or equal to (>=)').selected).toBe(true);
    expect(getByLabelText('Version').value).toBe('4.3');

    fireEvent.click(getByText('Add row'));
    rerenderForm(rerender, 'ios', undefined);
    const inputFields = await findAllByLabelText('Version type');
    expect(inputFields).toHaveLength(2);
    expect(container).toMatchSnapshot();
  });

  it('Adds a new row if the add row button is clicked and current row is completed (os range)', async() => {
    const {
      container,
      findAllByLabelText,
      getByText,
      getByLabelText,
      rerender,
    } = renderForm();
    userEvent.click(getByText('Add Version Targeting')); // open modal

    userEvent.selectOptions(getByLabelText('Version type'), 'os');
    userEvent.selectOptions(getByLabelText('Targeting criteria'), 'range');
    userEvent.type(getByLabelText('Minimum version'), '12.11.22');
    userEvent.type(getByLabelText('Maximum version'), '12.11.34');
    rerenderForm(rerender, 'ios', undefined);
    expect(getByText('OS').selected).toBe(true);
    expect(getByText('Range between').selected).toBe(true);
    expect(getByLabelText('Minimum version').value).toBe('12.11.22');
    expect(getByLabelText('Maximum version').value).toBe('12.11.34');

    fireEvent.click(getByText('Add row'));
    rerenderForm(rerender, 'ios', undefined);
    const inputFields = await findAllByLabelText('Version type');
    expect(inputFields).toHaveLength(2);
    expect(container).toMatchSnapshot();
  });

  it('Deletes first row when first delete button is clicked', async() => {
    const {
      findAllByLabelText,
      getByText,
      getByLabelText,
      queryByLabelText,
      rerender,
    } = renderForm();
    userEvent.click(getByText('Add Version Targeting')); // open modal
    expect(queryByLabelText('Delete')).toBeNull();

    addRow(getByText, getByLabelText, rerender, 'device');
    const deleteButtons = await findAllByLabelText('Delete');
    expect(deleteButtons).toHaveLength(2);

    const firstDeleteButton = deleteButtons[0];
    userEvent.click(firstDeleteButton);
    rerenderForm(rerender);
    const firstInput = await findAllByLabelText('Version type');
    const secondInput = await queryByLabelText('Targeting criteria');
    expect(firstInput).toHaveLength(1);
    expect(secondInput).toBeNull();
  });

  it('fills in values from redux when editing existing values is true', async() => {
    const selected = 0;
    const { getByText, findAllByLabelText } = renderForm(parentState, 'ios', selected);
    userEvent.click(getByText('Edit')); // open modal
    const versionTypeColumn = await findAllByLabelText('Version type');
    const targetingColumn = await findAllByLabelText('Targeting criteria');
    const deviceModelColumn = await findAllByLabelText('Model');
    const versionColumn = await findAllByLabelText('Version');
    const minVersionColumn = await findAllByLabelText('Minimum version');
    const maxVersionColumn = await findAllByLabelText('Maximum version');
    expect(versionTypeColumn[0]).toHaveValue('app');
    expect(versionTypeColumn[1]).toHaveValue('os');
    expect(versionTypeColumn[2]).toHaveValue('device');
    expect(targetingColumn[0]).toHaveValue('greaterEqual');
    expect(targetingColumn[1]).toHaveValue('range');
    expect(targetingColumn[2]).toHaveValue('equal');
    expect(deviceModelColumn[0]).toHaveValue('iPhone11,2');
    expect(versionColumn[0]).toHaveValue('1.2.3');
    expect(minVersionColumn[0]).toHaveValue('13.2.3');
    expect(maxVersionColumn[0]).toHaveValue('13.3.4');
  });

  it('calls onSubmit function when save button is clicked', async() => {
    const selected = 0;
    const { container, getByText } = renderForm(parentState, 'ios', selected);
    userEvent.click(getByText('Edit')); // open modal
    userEvent.click(getByText('Save'));
    fireEvent.submit(container.querySelector('form')); // have to shim in this functionality
  });

  it('closes modal when cancel button is clicked', async() => {
    const selected = 0;
    const { getByText } = renderForm(parentState, 'ios', selected);
    userEvent.click(getByText('Edit')); // open modal
    expect(getByText('iOS version targeting')).toBeInTheDocument();
    userEvent.click(getByText('Cancel'));
  });
});
