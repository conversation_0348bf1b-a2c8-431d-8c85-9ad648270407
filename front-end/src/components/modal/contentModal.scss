.content-modal {
  &__modal {
    .Modal__card {
      max-height: 80%;
      min-width: 60%;
      overflow-y: auto;
    }

    #modal-dialog-description {
      padding: unset;
    }

    .Table__wrapper { // double scrollbar issue
      overflow-x: unset;
    }
  }

  &__input-container {
    display: flex;
    justify-content: space-between;
  }

  &__dropdown-container {
    margin-right: 3rem;
    width: 50%;
  }

  &__dropdown {
    &:last-child {
      margin-top: 2.4rem;
    }
  }

  &__content-card {
    margin-top: 4rem;
    overflow-x: auto;
  }

  &__content-search {
    max-width: 45.4rem;

    .Search__button--search {
      pointer-events: none;
    }
  }

  &__no-results {
    margin-top: 14rem;
    margin-bottom: 14rem;
    text-align: center;
    color: $canvas-gray-600;
    font-size: 1.4rem;
  }

  &__fragment-search-input {
    &.Search__input {
      margin-top: 4.8rem;
    }
  }

  &__content-search-input {
    &.Search__input {
      margin-top: 0;
    }
  }

  &__content-table {
    .TableBody__cell {
      align-items: center;
    }

    .TextCaption__text {
      word-break: break-word;
    }

    .TextButton__button {
      padding: 0;
      margin: 0;
    }

    &__content-name {
      word-break: break-all;
    }
  }

  &__action-bar {
    margin-top: 3.6rem;
    display: flex;
    justify-content: flex-end;
  }

  &__action-button {
    margin-left: 3rem;
  }

  &__content-items {
    margin-bottom: 1.8rem;
  }

  .RadioButton__circle {
    span {
      left: 0;
    }
  }
}

// to allow the modal to be scrollable when it is open
body.modal-open {
  height: 100vh;
  overflow: hidden;
}
