import React from 'react';
import { Provider } from 'react-redux';
import thunk from 'redux-thunk';
import { render, fireEvent, waitFor } from '@testing-library/react';
import { Router } from 'react-router-dom';
import { createMemoryHistory } from 'history';
import { ExportCampaignModal } from './exportModal';
import configureMockStore from 'redux-mock-store';
import { ruleTypes } from '../../constants';
import { reducer as formReducer } from 'redux-form';
import { createStore, combineReducers, applyMiddleware, compose } from 'redux';
import campaignReducer from '../../store/reducers/campaigns';

const configureStore = configureMockStore([ thunk ]);
describe('Export Modal', () => {
  const commonProps = {
    type: ruleTypes.CAMPAIGN,
    exportRules: jest.fn(),
    formValues: { exportFields: [] },
    invalid: false,
    totalRules: 100,
    initialize: jest.fn(),
    handleSubmit: jest.fn(),
  };

  test('when export is clicked, initialize is called', () => {
    const store = configureStore({
      campaigns: {
        pagination: {
          total: 100,
        },
      },
      form: {
        exportModal: {
          values: {
            exportFields: [ 'id', 'application' ],
          },
        },
      },
    });
    const { getByText } = render(
      <Provider store={store}>
        <Router history={createMemoryHistory()}>
          <ExportCampaignModal
            {...commonProps}
            formValues={{}}
          />
        </Router>
      </Provider>
    );
    expect(store.getActions()).not.toContainEqual(expect.objectContaining({ type: '@@redux-form/INITIALIZE' }));
    fireEvent.click(getByText('Export'));
    expect(store.getActions()).toContainEqual(expect.objectContaining({ type: '@@redux-form/INITIALIZE' }));
  });

  test('clicking export button shows modal', async() => {
    const exportCampaigns = jest.fn();
    window.open = jest.fn();
    const initialState = ({
      form: {
        exportModal: {
          values: {
            exportFields: [ 'id', 'application' ],
          },
        },
      },
      campaigns: {
        pagination: {
          total: 100,
        },
      },
    });

    // testing redux-form is not fun
    const store = createStore(combineReducers({ form: formReducer, campaigns: campaignReducer }), initialState, compose(applyMiddleware(thunk)));

    const { getByText, queryByText, container } = render(
      <Provider store={store}>
        <Router history={createMemoryHistory()}>
          <ExportCampaignModal
            {...commonProps}
            exportRules={exportCampaigns}
            handleSubmit={fn => fn}
          />
        </Router>
      </Provider>
    );

    const testModalIsOpen = () => expect(getByText('Export 100 Campaigns')).toBeInTheDocument();
    const testModalIsClosed = () => expect(queryByText('Export 100 Campaigns')).not.toBeInTheDocument();

    testModalIsClosed();
    fireEvent.click(getByText('Export'));
    testModalIsOpen();

    // click cancel to close the modal
    fireEvent.click(getByText('Cancel'));
    await waitFor(() => testModalIsClosed);

    // actually export now
    fireEvent.click(getByText('Export'));
    testModalIsOpen();
    fireEvent.click(getByText('Export CSV'));
    fireEvent.submit(container.querySelector('form')); // have to shim in this functionality
    expect(window.open).toHaveBeenCalledWith(`api/v1/export-campaign-rules?sort=-updated_at&offset=0&limit=100&export=${encodeURIComponent('external_ref,name,content_id,date,status,platforms,application,pages,container')}`);
  });
});
