import React from 'react';
import { shallow } from 'enzyme';
import FragmentModal from './fragment';
import { FragmentContainer, validate } from './fragmentContainer';
import Search from 'canvas-core-react/lib/Search';
import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';
import { modalTypes } from '../../constants';

import { render } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
describe('FragmentModal', () => {
  const fakeResults = [ 'a', 'b', 'c' ].map((key) => ({
    id: key,
    web_fragment_id: key,
    updated_ts: '2019-09-30T04:00:00.000Z',
  }));

  it('matches the snapshot when results are null', () => {
    const wrapper = shallow(
      <FragmentModal
        isOpen={true}
        onClose={jest.fn()}
        onSearch={jest.fn()}
        searchResults={null}
        onSave={jest.fn()}
        onSubmit={jest.fn()}
        offset={0}
        limit={10}
        totalSearchResults={10}
        isLoading={true}
      />
    );
    expect(wrapper).toMatchSnapshot();
  });

  it('matches the snapshot when results are empty array', () => {
    const wrapper = shallow(
      <FragmentModal
        isOpen={true}
        onClose={jest.fn()}
        onSearch={jest.fn()}
        searchResults={[]}
        onSave={jest.fn()}
        onSubmit={jest.fn()}
        offset={0}
        limit={10}
        totalSearchResults={10}
      />
    );
    expect(wrapper).toMatchSnapshot();
  });

  it('matches a mocked open state', () => {
    const wrapper = shallow(
      <FragmentModal
        isOpen={true}
        onClose={jest.fn()}
        onSearch={jest.fn()}
        searchResults={fakeResults}
        onSave={jest.fn()}
        totalSearchResults={10}
        onSubmit={jest.fn()}
        offset={0}
        limit={10}
      />
    );
    expect(wrapper).toMatchSnapshot();
  });

  it('calls onSearch when an input is selected', () => {
    const mockedFunction = jest.fn();
    const wrapper = shallow(
      <FragmentModal
        isOpen={true}
        onClose={jest.fn()}
        onSearch={mockedFunction}
        searchResults={fakeResults}
        onSave={jest.fn()}
        onRadioSelect={jest.fn()}
        onSubmit={jest.fn()}
        offset={0}
        limit={10}
        totalSearchResults={10}
      />
    );

    // ensure we have the correct number of inputs
    const inputs = wrapper.find(Search);
    expect(inputs).toHaveLength(1); // 3 for the inputs above, 1 for the input group

    inputs.first().simulate('change', { target: { id: 'web-fragment-search-input', value: 'abc' } });
    expect(mockedFunction).toHaveBeenCalledWith({ name: 'abc' });
  });

  it('onsearch is called when DesktopPagination has its page changed', () => {
    const mockedFunction = jest.fn();
    const wrapper = shallow(
      <FragmentModal
        isOpen={true}
        onClose={jest.fn()}
        onSearch={mockedFunction}
        searchResults={fakeResults}
        onSave={jest.fn()}
        onRadioSelect={jest.fn()}
        onSubmit={jest.fn()}
        offset={0}
        limit={10}
        totalSearchResults={10}
      />
    );

    // ensure we have the correct number of inputs
    const inputs = wrapper.find(DesktopPagination);
    expect(inputs).toHaveLength(1);

    inputs.props().onChange(1);
    expect(mockedFunction).toHaveBeenCalled();
  });
});

describe('FragmentModal Container', () => {
  const mockDispatchProps = {
    clearSelectedWebFragment: jest.fn(),
    selectWebFragment: jest.fn(),
    getWebFragments: jest.fn(),
    closeAllModals: jest.fn(),
    formChange: jest.fn(),
    handleSubmit: jest.fn(),
    reset: jest.fn(),
  };

  const mockStateProps = {
    modal: modalTypes.FRAGMENT,
    isLoading: false,
    name: 'query',
    webFragments: [],
    totalWebFragments: 10,
  };

  const wrapper = shallow(
    <FragmentContainer
      {...mockDispatchProps}
      {...mockStateProps}
      type=""
      handleSubmit={f => f}
      offset={0}
      limit={10}
    />
  );

  global.snapshot(wrapper);

  it('All Errors', () => {
    expect(validate({ content_id: null })).toMatchSnapshot();
  });

  it('No Errors', () => {
    expect(validate({ content_id: '3333' })).toStrictEqual({});
  });

  it('testing the onClose method', () => {
    wrapper.instance().onClose();
    expect(mockDispatchProps.reset).toHaveBeenCalled();
    expect(mockDispatchProps.closeAllModals).toHaveBeenCalled();
  });

  it('testing the onSubmit method', () => {
    wrapper.instance().onSubmit({ content_id: '7777' });
    expect(mockDispatchProps.formChange).toHaveBeenCalled();
  });

  it('testing the onSearch method', () => {
    const { getByPlaceholderText } = render(
      <FragmentContainer
        {...mockDispatchProps}
        {...mockStateProps}
        handleSubmit={f => f}
        offset={0}
        limit={10}
        type="estore"
      />
    );

    const searchBar = getByPlaceholderText('Search for a web fragment...');
    expect(searchBar).toBeInTheDocument();
    mockDispatchProps.getWebFragments.mockReset();
    expect(mockDispatchProps.getWebFragments).not.toHaveBeenCalled();

    const searchString = 'Web Fragment';
    userEvent.type(searchBar, searchString);

    expect(mockDispatchProps.getWebFragments).toHaveBeenCalledWith({ name: searchString, offset: undefined, limit: undefined, type: 'estore' });
  });
});
