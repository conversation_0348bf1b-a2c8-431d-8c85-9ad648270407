import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, connect } from 'react-redux';
import { Field, reduxForm, change as reduxFormChange, FieldArray } from 'redux-form';
import semverGt from 'semver/functions/gt';
import semverValid from 'semver/functions/valid';
import semverCoerce from 'semver/functions/coerce';

import IconAdd from 'canvas-core-react/lib/IconAdd';
import IconDelete from 'canvas-core-react/lib/IconDelete';
import IconEdit from 'canvas-core-react/lib/IconEdit';
import Modal from 'canvas-core-react/lib/internal/Modal';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import TextButton from 'canvas-core-react/lib/TextButton';

import { InputTextField, InputSelectField } from '../formFields';
import { versionTypeOptions, targetingCriteriaOptions, osVersionPattern } from '../versionTargeting/constants';
import {
  appVersionTooltip,
  appMinVersionTooltip,
  appMaxVersionTooltip,
  modelTooltipIos,
  modelTooltipAndroid,
  osVersionTooltip,
  osMinVersionTooltip,
  osMaxVersionTooltip,
} from '../versionTargeting/tooltips';

const getAvailableVersionTypeOptions = (fieldArrayValues, fieldArrayIndex) => {
  const rowVersionTypes = [];
  versionTypeOptions.forEach((option, index) => {
    const versionType = fieldArrayValues && fieldArrayValues[index] && fieldArrayValues[index].versionType;
    if (versionType) {
      rowVersionTypes.push(versionType);
    }
  });

  const remainingOptions = versionTypeOptions.filter(({ id }) => !rowVersionTypes.includes(id));
  const options = fieldArrayValues[fieldArrayIndex].versionType
    ? [ versionTypeOptions.find(({ id }) => id === fieldArrayValues[fieldArrayIndex].versionType), ...remainingOptions ]
    : [ ...remainingOptions ];
  return options;
};

const validate = (values) => {
  const errors = {};
  const platformsTargetingArrayErrors = [];
  // eslint-disable-next-line no-unused-expressions
  values.platformsTargeting?.forEach((row, rowIndex) => {
    const rowErrors = {};
    if (!row.versionType) {
      rowErrors.versionType = 'Version type is required';
      platformsTargetingArrayErrors[rowIndex] = rowErrors;
    }
    if (!row.minVersion) {
      rowErrors.minVersion = 'Minimum version is required';
      platformsTargetingArrayErrors[rowIndex] = rowErrors;
    } else if (row.versionType === 'app' && !semverValid(row.minVersion)) {
      rowErrors.minVersion = 'Version format is incorrect';
      platformsTargetingArrayErrors[rowIndex] = rowErrors;
    } else if (row.versionType === 'os' && !osVersionPattern.test(row.minVersion)) {
      rowErrors.minVersion = 'Version format is incorrect';
      platformsTargetingArrayErrors[rowIndex] = rowErrors;
    }
    if (!row.maxVersion) {
      rowErrors.maxVersion = 'Maximum version is required';
      platformsTargetingArrayErrors[rowIndex] = rowErrors;
    } else if (row.versionType === 'app' && !semverValid(row.maxVersion)) {
      rowErrors.maxVersion = 'Version format is incorrect';
      platformsTargetingArrayErrors[rowIndex] = rowErrors;
    } else if (row.versionType === 'os' && !osVersionPattern.test(row.maxVersion)) {
      rowErrors.maxVersion = 'Version format is incorrect';
      platformsTargetingArrayErrors[rowIndex] = rowErrors;
    } else if (row.minVersion && !rowErrors.minVersion && !semverGt(semverCoerce(row.maxVersion), semverCoerce(row.minVersion))) {
      rowErrors.maxVersion = 'Version must be greater than the minimum version';
      platformsTargetingArrayErrors[rowIndex] = rowErrors;
    }
    if (!row.version) {
      rowErrors.version = 'Version is required';
      platformsTargetingArrayErrors[rowIndex] = rowErrors;
    } else if (row.versionType === 'app' && !semverValid(row.version)) {
      rowErrors.version = 'Version format is incorrect';
      platformsTargetingArrayErrors[rowIndex] = rowErrors;
    } else if (row.versionType === 'os' && !osVersionPattern.test(row.version)) {
      rowErrors.version = 'Version format is incorrect';
      platformsTargetingArrayErrors[rowIndex] = rowErrors;
    }
    if (!row.model) {
      rowErrors.model = 'Model is required';
      platformsTargetingArrayErrors[rowIndex] = rowErrors;
    }
  });
  if (platformsTargetingArrayErrors.length) {
    errors.platformsTargeting = platformsTargetingArrayErrors;
  }
  return errors;
};

const renderPlatformsTargeting = ({ fields, handleChange, valid, platform }) => {
  const fieldArrayValues = fields.getAll();
  return (
    <ul>
      { fields.map((name, index) => (
        <li key={index}>
          <div className="vt-modal__row">
            <Field
              id={`${name}.versionType`}
              name={`${name}.versionType`}
              className="vt-modal__field vt-modal__field--type"
              label="Version type"
              placeholder="Select type"
              component={InputSelectField}
              onChange={(e) => handleChange(e, name)}
              options={getAvailableVersionTypeOptions(fieldArrayValues, index)}
            />
            { fieldArrayValues[index].versionType &&
              <>
                <Field
                  id={`${name}.targetingCriteria`}
                  name={`${name}.targetingCriteria`}
                  className="vt-modal__field"
                  label="Targeting criteria"
                  placeholder="Select criteria"
                  component={InputSelectField}
                  onChange={(e) => handleChange(e, name)}
                  disabled={fieldArrayValues[index].versionType === 'device'}
                  options={targetingCriteriaOptions}
                />
                { fieldArrayValues[index].versionType === 'device'
                  ? <Field
                    id={`${name}.model`}
                    name={`${name}.model`}
                    label="Model"
                    placeholder={`Enter model ${platform === 'ios' ? 'identifier' : 'name'}`}
                    component={InputTextField}
                    tooltip={platform === 'ios' ? modelTooltipIos : modelTooltipAndroid}
                    onChange={(e) => handleChange(e)}
                  />
                  : <>
                    { fieldArrayValues[index].targetingCriteria === 'range'
                      ? <>
                        <Field
                          id={`${name}.minVersion`}
                          name={`${name}.minVersion`}
                          className="vt-modal__field"
                          label="Minimum version"
                          placeholder={fieldArrayValues[index].versionType === 'app' ? 'Ex: 12.5.5-1' : 'Ex: 12.5.5'}
                          component={InputTextField}
                          tooltip={fieldArrayValues[index].versionType === 'app' ? appMinVersionTooltip : osMinVersionTooltip}
                          onChange={(e) => handleChange(e)}
                        />
                        <Field
                          id={`${name}.maxVersion`}
                          name={`${name}.maxVersion`}
                          label="Maximum version"
                          placeholder={fieldArrayValues[index].versionType === 'app' ? 'Ex: 13.5.5-1' : 'Ex: 13.5.5'}
                          component={InputTextField}
                          tooltip={fieldArrayValues[index].versionType === 'app' ? appMaxVersionTooltip : osMaxVersionTooltip}
                          onChange={(e) => handleChange(e)}
                        />
                      </>
                      : <Field
                        id={`${name}.version`}
                        name={`${name}.version`}
                        label="Version"
                        placeholder={fieldArrayValues[index].versionType === 'app' ? 'Ex: 12.5.5-1' : 'Ex: 12.5.5'}
                        component={InputTextField}
                        tooltip={fieldArrayValues[index].versionType === 'app' ? appVersionTooltip : osVersionTooltip}
                        onChange={(e) => handleChange(e)}
                      />
                    }
                  </>
                }
              </>
            }
            { fields.length > 1 && (
              <TextButton Icon={IconDelete} onClick={() => fields.remove(index)} className="vt-modal__button--delete-row" aria-label="Delete" type="button">
                <span className="vt-modal__delete-row-text">Delete</span>
              </TextButton>
            ) }
          </div>
          { (fields.length < versionTypeOptions.length && index === fields.length - 1) && (
            <TextButton
              Icon={IconAdd}
              onClick={() => fields.push({ targetingCriteria: 'equal' })}
              iconPosition="right"
              className="vt-modal__button--add-row"
              disabled={!valid}
            >
              Add row
            </TextButton>
          ) }
        </li>
      )) }
    </ul>
  );
};

renderPlatformsTargeting.propTypes = {
  fields: PropTypes.object,
  handleVersionTypeChange: PropTypes.func,
  handleChange: PropTypes.func,
  valid: PropTypes.bool,
  platform: PropTypes.string,
};

const VersionTargeting = ({
  isEditing,
  platform,
  platformIndex,
  parentPlatformsTargeting,
  parentFormName,
  valid,
  change,
  initialize,
  handleSubmit,
  selected,
  untouch,
}) => {
  const [ isModalOpen, setIsModalOpen ] = useState(false);
  const [ focusElementId, setFocusElementId ] = useState('');
  const dispatch = useDispatch();

  useEffect(() => {
    // Since input components are rendered in a modal, reference is lost when state change/rerenders occur
    // - need to reapply focus to the input if it was the last element in focus (set via the handleChange function)
    if (focusElementId && document.getElementById(focusElementId)) {
      setTimeout(() => document.getElementById(focusElementId).focus(), 1);
    }
  });

  const handleChange = (e, name) => {
    if (e.target.id === `${name}.versionType`) {
      // When version type changes, reset all row fields
      change(`${name}.targetingCriteria`, 'equal');
      change(`${name}.version`, null);
      change(`${name}.minVersion`, null);
      change(`${name}.maxVersion`, null);
      change(`${name}.model`, null);
      untouch(`${name}.version`, `${name}.minVersion`, `${name}.maxVersion`, `${name}.model`);
    } else if (e.target.id === `${name}.targetingCriteria` && e.target.value === 'range') {
      change(`${name}.version`, null);
      untouch(`${name}.version`,);
    } else if (e.target.id === `${name}.targetingCriteria`) {
      change(`${name}.minVersion`, null);
      change(`${name}.maxVersion`, null);
      untouch(`${name}.minVersion`, `${name}.maxVersion`);
    }
    setFocusElementId(e.target.id);
  };

  useEffect(() => {
    if (isModalOpen) {
      if (isEditing) {
        initialize({ platformsTargeting: parentPlatformsTargeting[platformIndex].items[selected] });
      } else {
        initialize({ platformsTargeting: [ { targetingCriteria: 'equal' } ] });
      }
    }
  }, [ isModalOpen ]);

  const closeModal = (e) => {
    e.preventDefault();
    setFocusElementId('');
    setIsModalOpen(false);
  };

  const openModal = (e) => {
    e.preventDefault();
    setIsModalOpen(true);
  };

  const onSubmit = (values) => {
    if (valid) {
      const targetingStateCopy = [ ...parentPlatformsTargeting ];
      if (platformIndex === -1) {
        targetingStateCopy.push({ platform, items: [ values.platformsTargeting ] });
      } else if (isEditing) {
        targetingStateCopy[platformIndex]['items'][selected] = values.platformsTargeting;
      } else {
        targetingStateCopy[platformIndex]['items'].push(values.platformsTargeting);
      }
      dispatch(reduxFormChange(parentFormName, 'platforms_targeting', targetingStateCopy));
      setFocusElementId('');
      setIsModalOpen(false);
    }
  };

  return (
    <>
      { isEditing
        ? <TextButton className="version-targeting__button--edit" onClick={(e) => openModal(e)} Icon={IconEdit}>
          Edit
        </TextButton>
        : <TextButton className="version-targeting__button--add" onClick={(e) => openModal(e)} Icon={IconAdd}>
          Add Version Targeting
        </TextButton>
      }
      <Modal
        className="vt-modal"
        headline={`${platform === 'ios' ? 'iOS' : 'Android'} version targeting`}
        setModalVisible={() => { }}
        isModalVisible={isModalOpen}
      >
        <form onSubmit={handleSubmit(onSubmit)}>
          <FieldArray name="platformsTargeting" component={renderPlatformsTargeting} props={{ handleChange, valid, platform }} rerenderOnEveryChange/>
          <div className="vt-modal__footer">
            <SecondaryButton onClick={(e) => closeModal(e)} className="vt-modal__button--cancel">
              Cancel
            </SecondaryButton>
            <PrimaryButton type="submit" className="vt-modal__button--save">
              Save
            </PrimaryButton>
          </div>
        </form>
      </Modal>
    </>
  );
};

VersionTargeting.propTypes = {
  parentFormName: PropTypes.oneOf([ 'campaignDetails', 'alertDetails' ]).isRequired,
  parentPlatformsTargeting: PropTypes.array,
  platform: PropTypes.oneOf([ 'ios', 'android' ]).isRequired,
  platformIndex: PropTypes.number,
  isEditing: PropTypes.bool,
  selected: PropTypes.number,
  change: PropTypes.func,
  initialize: PropTypes.func,
  handleSubmit: PropTypes.func,
  valid: PropTypes.bool,
  untouch: PropTypes.func,
};
VersionTargeting.defaultProps = {
  selected: undefined,
};

const VersionTargetingForm = reduxForm({
  form: 'platforms_targeting',
  validate,
})(VersionTargeting);

const VersionTargetingModal = connect(
  (state, props) => {
    const parentPlatformsTargeting = state.form[props.formName].values.platforms_targeting;
    const platformIndex = parentPlatformsTargeting.findIndex(x => x.platform === props.platform);
    const isEditing = props.selected !== undefined;

    return {
      parentFormName: props.formName,
      parentPlatformsTargeting: parentPlatformsTargeting,
      platform: props.platform,
      platformIndex: platformIndex,
      isEditing,
      selected: props.selected,
    };
  },
)(VersionTargetingForm);

export { VersionTargetingModal };
