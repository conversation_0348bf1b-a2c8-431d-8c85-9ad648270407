import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import qs from 'qs';
import { Field, reduxForm, getFormValues, initialize } from 'redux-form';

import IconDownload from 'canvas-core-react/lib/IconDownload';
import Modal from 'canvas-core-react/lib/internal/Modal';
import TextButton from 'canvas-core-react/lib/TextButton';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import {
  ruleTypes,
  STATUS,
  statusByType,
} from '../../constants';
import { exportCampaigns } from '../../store/actions/campaigns';
import { exportAlerts } from '../../store/actions/alerts';
import { InputCheckboxGroupField } from '../formFields';
import { useLocation } from 'react-router-dom';
import { formatWord } from '../../utils';
import { requireAtLeastOne } from '../../utils/validation';

export const ExportModal = ({
  type,
  totalRules,
  exportRules,
  formValues,
  initialize,
  handleSubmit,
  invalid,
}) => {
  const exportFieldOptions = {
    campaign: [
      { id: 'external_ref', name: 'Campaign ID' },
      { id: 'name', name: 'Campaign Name' },
      { id: 'content_id', name: 'Content Preview Name' },
      { id: 'date', name: 'Date Range' },
      { id: 'status', name: 'Status' },
      { id: 'platforms', name: 'Platform' },
      { id: 'application', name: 'Application' },
      { id: 'pages', name: 'Page' },
      { id: 'container', name: 'Container' },
    ],
    alert: [
      { id: 'name', name: 'Alert Name' },
      { id: 'date', name: 'Date Range' },
      { id: 'content_id', name: 'Content Preview Name' },
      { id: 'application', name: 'Application' },
      { id: 'platforms', name: 'Platform' },
      { id: 'created_by', name: 'Created By' },
      { id: 'updated_by', name: 'Last Updated By' },
      { id: 'updated_at', name: 'Last Updated At' },
      { id: 'status', name: 'Status' },
    ],
  };

  const location = useLocation();
  const [ exportModalIsOpen, setExportModalIsOpen ] = useState(false);

  useEffect(() => {
    if (exportModalIsOpen) {
      initialize({ exportFields: exportFieldOptions[type].map(option => option.id) });
    }
  }, [ exportModalIsOpen ]);

  const onSubmit = (values) => {
    const urlQueryParams = qs.parse(location.search, { ignoreQueryPrefix: true });
    const { status, start_date_gt: start, end_date_lt: end } = urlQueryParams;

    if (status && statusByType[type].published.includes(status)) {
      const date = new Date();
      const now = date.toISOString();
      if (status === STATUS.UPCOMING && (!start || start < now)) {
        urlQueryParams.start_date_gt = now;
        urlQueryParams.disabled = false;
      } else if (status === STATUS.EXPIRED && (!end || end > now)) {
        urlQueryParams.end_date_lt = now;
      } else if (status === STATUS.ACTIVE) {
        urlQueryParams.disabled = false;
        urlQueryParams.start_date_lt = now;
        urlQueryParams.end_date_gt = now;
      } else if (status === STATUS.INACTIVE) {
        urlQueryParams.disabled = true;
        urlQueryParams.end_date_gt = now;
      }
      urlQueryParams.status = STATUS.PUBLISHED;
    }

    const query = {
      ...urlQueryParams,
      sort: '-updated_at',
      offset: 0,
      limit: totalRules,
    };
    exportRules(query, formValues.exportFields);
    setExportModalIsOpen(false);
  };

  return (
    <>
      <TextButton
        Icon={IconDownload}
        onClick={() => setExportModalIsOpen(true)}
      >
        Export
      </TextButton>
      <Modal
        className="export-modal"
        headline={`Export ${totalRules} ${formatWord(type, { capitalize: true, plural: totalRules !== 1 })}`}
        primaryButtonLabel="Export CSV"
        setModalVisible={() => setExportModalIsOpen(false)}
        isModalVisible={exportModalIsOpen}
      >
        <form onSubmit={handleSubmit(onSubmit)}>
          <Field
            label="Select data to export"
            name="exportFields"
            component={InputCheckboxGroupField}
            options={exportFieldOptions[type]}
            validate={[ requireAtLeastOne ]}
            inline={false}
          />
          <div className="export-modal__footer">
            <SecondaryButton
              className="export-modal__button"
              type="button"
              onClick={() => setExportModalIsOpen(false)}
            >
              Cancel
            </SecondaryButton>
            <PrimaryButton
              className="export-modal__button"
              disabled={invalid}
              type="submit"
            >
              Export CSV
            </PrimaryButton>
          </div>
        </form>
      </Modal>
    </>
  );
};

ExportModal.propTypes = {
  type: PropTypes.oneOf([ ruleTypes.CAMPAIGN, ruleTypes.ALERT ]).isRequired,
  totalRules: PropTypes.number.isRequired,
  exportRules: PropTypes.func.isRequired,
  formValues: PropTypes.object.isRequired,
  initialize: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  invalid: PropTypes.bool.isRequired,
};

const ExportModalForm = reduxForm({ form: 'exportModal' })(ExportModal);

export const ExportCampaignModal = (
  connect(
    state => ({
      totalRules: state.campaigns.pagination?.total || 0,
      type: ruleTypes.CAMPAIGN,
      formValues: getFormValues('exportModal')(state) || {},
    }),
    dispatch => bindActionCreators({
      exportRules: exportCampaigns,
      initialize: () => initialize('exportModal'),
    }, dispatch),
  )(ExportModalForm)
);

export const ExportAlertModal = (
  connect(
    state => ({
      totalRules: state.alerts.pagination?.total || 0,
      type: ruleTypes.ALERT,
      formValues: getFormValues('exportModal')(state) || {},
    }),
    dispatch => bindActionCreators({
      exportRules: exportAlerts,
      initialize: () => initialize('exportModal'),
    }, dispatch),
  )(ExportModalForm)
);
