$autosuggest-v2-tag-height: 3rem;

.autosuggest-v2 {
  color: $brand-black;
  background-color: $brand-white;
  position: relative;
  border-bottom: 0.1rem $brand-black solid;

  &__border {
    &--disabled {
      border-bottom: 0.1rem $canvas-gray-500 solid;
    }
  }

  &__label {
    font-family: $font-bold-family;
    font-size: 1.6rem;

    &--error {
      color: $brand-red;
    }
  }

  &__input {
    display: block;
    height: $autosuggest-v2-tag-height + 0.7rem;
    border: none;
    outline: none;
    font-family: $font-regular-family;
    font-size: 2rem;
    width: 24rem;
    transition: width 0.2s ease;
    overflow: hidden;
    flex-grow: 1;
    padding: 0 0 0.7rem 0;

    &--hidden {
      opacity: 0;
      width: 0;
    }

    &--compact {
      max-width: 32rem;
    }

    &:disabled {
      background-color: transparent;
    }
  }

  &__input-wrapper {
    position: relative;
    padding-top: 1.5rem;
    min-height: $autosuggest-v2-tag-height + 1rem;
    cursor: default;
    flex-wrap: wrap;
    display: flex;
    align-items: center;

    &--editable {
      border-color: red;
      cursor: text;
    }
  }

  &__edit-btn {
    cursor: pointer;
    transition: all 0.2s ease;
    overflow: hidden;
    position: absolute;
    right: 0;
    bottom: 0.8rem;
    width: 3rem;
    padding-left: 1rem;
    margin-right: 1rem;
    background: $brand-white;

    &--read-only {
      color: #949494;
      pointer-events: none;
      position: absolute;
      bottom: 1.5rem;
    }
  }

  &__suggestions {
    position: absolute;
    margin-top: 0;
    max-height: 22rem;
    background-color: #292a2d;
    box-shadow: 0 0.4rem 0.5rem $canvas-gray-400;
    border-radius: 0.5rem;
    z-index: 1000;
    cursor: pointer;
    overflow-y: auto;
  }

  &__suggestion {
    overflow: hidden;
    outline: none;
    padding: 1rem;
    margin: 0.4rem;
    height: 4.1rem;
    border-radius: 0.4rem;
    border: 0.2rem solid transparent;
    display: flex;
    align-items: center;

    &:hover,
    &:focus {
      border: 0.2rem solid $brand-blue;
    }
  }

  &__avatar {
    float: left;
  }

  &__name {
    display: inline-block;
    color: $brand-white;
    font-size: 1.6rem;

    &:not(:only-child) {
      margin-left: 2rem;
    }
  }

  .tag {
    border-radius: 0.8rem;
    font-weight: normal;
    min-height: 3rem;
    padding: 0.3rem 1.2rem;
    background: $canvas-gray-100;
    margin: 0 0.6rem 0.8rem 0;

    &__value {
      margin: 0;
      line-height: 2.4rem;
    }

    &__delete {
      font-weight: normal;
      padding-left: 0.8rem;
      width: unset;
    }
  }
}
