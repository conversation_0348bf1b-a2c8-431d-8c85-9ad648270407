import React from 'react';
import { shallow } from 'enzyme';
import Autosuggest from './autosuggest';

const mockProps = {
  className: 'product-targeting__selector',
  editable: false,
  data: [],
  dataLegend: { keyName: 'id', valueName: 'label' },
  label: 'Target products',
  onChange: () => {},
  placeholder: 'Select products',
  secondaryLabel: '',
  showSuggestionsOnFocus: true,
  hideSuggestionsAfterSelection: true,
};

describe('Autosuggest component', () => {
  // Snapshot testing
  it('vanilla snapshot of the autosuggest component', () => {
    const wrapper = shallow(
      <Autosuggest
        {...mockProps}
      />
    );
    expect(wrapper).toMatchSnapshot();
  });
});
