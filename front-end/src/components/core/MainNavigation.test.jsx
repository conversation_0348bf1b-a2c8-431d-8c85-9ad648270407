import React from 'react';
import { render } from '@testing-library/react';
import HeadingConnected from './MainNavigation';
import { Provider } from 'react-redux';
import { Router } from 'react-router';
import { createMemoryHistory } from 'history';
import configureStore from 'redux-mock-store';

describe('Heading', () => {
  const mockStore = configureStore([]);

  it('renders the heading links', () => {
    const store = mockStore({
      authenticated: {
        permissions: {
          admin: true,
        },
      },
    });
    const history = createMemoryHistory();
    // with no permissions we should not see any header items
    const { queryByText, rerender } = render(
      <Router history={history}>
        <Provider store={mockStore({})}>
          <HeadingConnected />
        </Provider>
      </Router>
    );
    const headerItems = [
      'Campaigns',
      'Alerts',
      'Placement',
      'Access',
      'Mapping',
      'Message Centre',
      'Offer Management',
    ];

    headerItems.forEach(headerItem => expect(queryByText(headerItem)).not.toBeInTheDocument());

    // TODO tw rerender with non-super permissions for placement, access

    // TODO tw rerender with super permissions for placement, access

    // rerender with admin permissions
    rerender(
      <Router history={history}>
        <Provider store={store}>
          <HeadingConnected />
        </Provider>
      </Router>
    );

    // all header items are now present
    headerItems.forEach(headerItem => expect(queryByText(headerItem)).toBeInTheDocument());
  });
});
