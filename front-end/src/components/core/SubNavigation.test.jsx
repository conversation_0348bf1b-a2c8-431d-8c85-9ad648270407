import React from 'react';
import { render } from '@testing-library/react';
import SubNavigationConnected from './SubNavigation';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router-dom';
import configureStore from 'redux-mock-store';
import defaultData from '../../utils/testing-library-utils-data';

const storeData = {
  authenticated: {
    permissions: {
      admin: true,
    },
    access: {
      campaigns: {},
      ccau_campaigns: {},
      alerts: {},
    },
  },
  applications: {
    items: defaultData.applications,
    isLoading: false,
  },
  ruleTypes: {
    items: defaultData.ruleTypes,
    isLoading: false,
  },
};

describe('SubNavigation', () => {
  const mockStore = configureStore([]);

  test('renders for empty', () => {
    const { container } = render(
      <MemoryRouter initialEntries={[ { pathname: '' } ]}>
        <Provider store={mockStore({})}>
          <SubNavigationConnected />
        </Provider>
      </MemoryRouter>
    );

    expect(container.getElementsByClassName('core-sub-nav')).toHaveLength(1);
  });
  test('renders the heading links', () => {
    const store = mockStore(storeData);
    // with no permissions we should not see any header items
    const { queryByText } = render(
      <MemoryRouter initialEntries={[ { pathname: '/campaigns' } ]}>
        <Provider store={mockStore({})}>
          <SubNavigationConnected />
        </Provider>
      </MemoryRouter>
    );

    const campaignSubHeaderItems = [ 'Campaigns', 'SOL', 'Storefront' ];
    campaignSubHeaderItems.forEach(subheaderItem =>
      expect(queryByText(subheaderItem)).not.toBeInTheDocument()
    );

    render(
      <MemoryRouter initialEntries={[ { pathname: '/campaigns' } ]}>
        <Provider store={store}>
          <SubNavigationConnected />
        </Provider>
      </MemoryRouter>
    );

    campaignSubHeaderItems.forEach(subheaderItem =>
      expect(queryByText(subheaderItem)).toBeInTheDocument()
    );
  });

  test('No Admin permissions - only view campagins', async() => {
    const campaignSubHeaderItems = [ 'Campaigns' ];
    const store = mockStore({
      ...storeData,
      authenticated: {
        permissions: {
          campaigns_view: true,
        },
        access: {
          campaigns: {
            containers: {
              nova: {
                view: [ 'targeted', 'mass', 'message' ],
              },
            },
            pages: {
              nova: {
                view: [ 'targeted', 'mass', 'message' ],
              },
            },
            ruleSubTypes: {
              nova: {
                view: [ 'targeted', 'mass', 'message' ],
              },
            },
          },
          alerts: {},
          ccau_campaign: {},
        },
      },
    });

    const { queryByText } = render(
      <MemoryRouter initialEntries={[ { pathname: '/campaigns' } ]}>
        <Provider store={store}>
          <SubNavigationConnected />
        </Provider>
      </MemoryRouter>
    );
    campaignSubHeaderItems.forEach(subheaderItem =>
      expect(queryByText(subheaderItem)).toBeInTheDocument()
    );
  });

  test('Should render mapping draft', async() => {
    const store = mockStore({
      ...storeData,
      authenticated: {
        permissions: {
          campaigns_view: true,
          kt_variable_mapping_view: true,
        },
        access: {
          campaigns: {
            containers: {
              nova: {
                view: [ 'targeted', 'mass', 'message' ],
              },
            },
            pages: {
              nova: {
                view: [ 'targeted', 'mass', 'message' ],
              },
            },
            ruleSubTypes: {
              nova: {
                view: [ 'targeted', 'mass', 'message' ],
              },
            },
          },
          alerts: {},
          ccau_campaign: {},
        },
      },
      variableMapping: {
        isLoading: true,
        hasDraft: true,
        activeSet: {
          variable_set_id: 1,
          created_at: '2023-05-05T15:45:38.910Z',
          created_by: 's100',
          status: 'active',
          updated_at: '2023-05-05T18:37:55.264Z',
          updated_by: 's1838652',
          description: 'Test',
          approver_sid: 's100',
          variables: [
            {
              variable_campaign: 'current_account_maturity_date',
              variable_template: 'SOLUI_CURRENT_ACCOUNT_MATURITY_DATE_END',
              variable_type: 'date',
              variable_type_label: 'Date',
            },
            {
              variable_campaign: 'current_account_number',
              variable_template: 'SOLUI_CURRENT_ACCOUNT_NUMBER_END',
              variable_type: 'account-number-mask',
              variable_type_label: 'Masked Account Number',
            },
          ],
        },
      },
    });
    const { queryByText } = render(
      <MemoryRouter initialEntries={[ { pathname: '/variable-mapping/draft' } ]}>
        <Provider store={store}>
          <SubNavigationConnected />
        </Provider>
      </MemoryRouter>
    );

    expect(queryByText('Active')).toBeInTheDocument();

    expect(queryByText('Pending')).toBeInTheDocument();

    expect(queryByText('Draft*')).toBeInTheDocument();
  });

  test('Should render mapping pending', async() => {
    const store = mockStore({
      ...storeData,
      authenticated: {
        permissions: {
          campaigns_view: true,
          pega_variable_mapping_view: true,
        },
        access: {
          campaigns: {
            containers: {
              nova: {
                view: [ 'targeted', 'mass', 'message' ],
              },
            },
            pages: {
              nova: {
                view: [ 'targeted', 'mass', 'message' ],
              },
            },
            ruleSubTypes: {
              nova: {
                view: [ 'targeted', 'mass', 'message' ],
              },
            },
          },
          alerts: {},
          ccau_campaign: {},
        },
      },
      variableMapping: {
        isLoading: true,
        hasPending: true,
        activeSet: {
          variable_set_id: 1,
          created_at: '2023-05-05T15:45:38.910Z',
          created_by: 's100',
          status: 'active',
          updated_at: '2023-05-05T18:37:55.264Z',
          updated_by: 's1838652',
          description: 'Test',
          approver_sid: 's100',
          variables: [
            {
              variable_campaign: 'current_account_maturity_date',
              variable_template: 'SOLUI_CURRENT_ACCOUNT_MATURITY_DATE_END',
              variable_type: 'date',
              variable_type_label: 'Date',
            },
            {
              variable_campaign: 'current_account_number',
              variable_template: 'SOLUI_CURRENT_ACCOUNT_NUMBER_END',
              variable_type: 'account-number-mask',
              variable_type_label: 'Masked Account Number',
            },
          ],
        },
      },
    });
    const { queryByText } = render(
      <MemoryRouter initialEntries={[ { pathname: '/variable-mapping/pending' } ]}>
        <Provider store={store}>
          <SubNavigationConnected />
        </Provider>
      </MemoryRouter>
    );

    expect(queryByText('Active')).toBeInTheDocument();
    expect(queryByText('Draft')).toBeInTheDocument();
    expect(queryByText('Pending*')).toBeInTheDocument();
  });

  test('shoud render message centre heading links', () => {
    const store = mockStore(storeData);

    const { queryByText } = render(
      <MemoryRouter initialEntries={[ { pathname: '/message-centre/campaigns' } ]}>
        <Provider store={store}>
          <SubNavigationConnected />
        </Provider>
      </MemoryRouter>
    );

    expect(queryByText('Campaigns')).toBeInTheDocument();
    expect(queryByText('Messages')).toBeInTheDocument();
  });
});
