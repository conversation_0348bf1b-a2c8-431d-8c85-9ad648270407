import React from 'react';
import { shallow } from 'enzyme';

import SkipLink from './skipLink';

describe('SkipLink', () => {
  const wrapper = shallow(<SkipLink targetId="testId"/>);
  global.snapshot(wrapper);

  document.body.innerHTML = '<a id="hashlink"><div id="skip-link-card"></div></a>';
  it('onFocus', () => {
    wrapper.prop('onFocus')();
    expect(document.getElementById('skip-link-card').classList.contains('focus-within')).toBe(true);
  });
  it('onBlur', () => {
    wrapper.prop('onBlur')();
    expect(document.getElementById('skip-link-card').classList.contains('focus-within')).toBe(false);
  });
  it('onKeyDown', () => {
    wrapper.prop('onKeyDown')({ key: 'Enter' });
  });
  it('onKeyDown (Not Enter)', () => {
    wrapper.prop('onKeyDown')({ key: ' ' });
  });
  it('onClick', () => {
    wrapper.prop('onClick')();
  });
});
