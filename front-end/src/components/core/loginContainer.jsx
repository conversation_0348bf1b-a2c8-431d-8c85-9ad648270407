import React from 'react';
import PropTypes from 'prop-types';

import Login from './login';
import { setBrowserTitle } from '../../constants';

export default class LoginContainer extends React.PureComponent {
  static propTypes = {
    logout: PropTypes.func,
  };

  componentDidMount() {
    setBrowserTitle('Login');
  }

  onSubmit = () => {
    this.props.logout();
  };

  render() {
    return (
      <Login onSubmit={this.onSubmit} />
    );
  }
}
