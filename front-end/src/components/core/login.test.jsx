import React from 'react';

import { render, fireEvent } from '@testing-library/react';

import Login from './login';
import LoginContainer from './loginContainer';

const onSubmit = jest.fn();
const logout = jest.fn();

describe('Login', () => {
  test('Login button clicked', () => {
    const { getByText } = render(
      <Login onSubmit={onSubmit} />
    );
    fireEvent.click(getByText('Sign In'));
    expect(onSubmit).toHaveBeenCalledTimes(1);
  });

  test('onSubmit container redirects to the login url', () => {
    delete window.location;
    window.location = {
      assign: jest.fn(),
      pathname: '/',
      search: '',
    };
    const { getByText } = render(
      <LoginContainer logout={logout} />
    );

    fireEvent.click(getByText('Sign In'));
    expect(logout).toHaveBeenCalledTimes(1);
  });
});
