import React from 'react';
import PropTypes from 'prop-types';

import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import Card from 'canvas-core-react/lib/Card';

const Login = ({ onSubmit }) => (
  <form id="login">
    <Card>
      <PrimaryButton type="button" onClick={onSubmit}>Sign In</PrimaryButton>
    </Card>
  </form>
);

Login.propTypes = {
  onSubmit: PropTypes.func.isRequired,
};

export default Login;
