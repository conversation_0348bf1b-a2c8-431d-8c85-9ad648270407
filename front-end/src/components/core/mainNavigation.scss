.core-header {
  display: flex;
  justify-content: center;
  border-top: 0.4rem solid $brand-red;
  box-shadow: 0 0.6rem 0.7rem rgba(0, 40, 80, 0.11);
  height: 7.2rem;
  position: relative;
  z-index: 10000; // canvas side panel has z-index 9000

  &__inner {
    width: 144rem;
    display: flex;
    align-items: center;
    padding: 0 5rem;
    justify-content: space-between;
  }

  &__hamburger-menu {
    display: block;

    @include mq($from: desktop) {
      display: none !important;
    }
  }

  &__branding {
    display: flex;
    align-items: center;
  }

  &__name {
    white-space: nowrap;
  }

  &__logo {
    margin-right: 0.8rem;
  }

  &__navigation-links {
    display: none;

    @include mq($from: desktop) {
      display: flex;
    }
  }

  &__navigation-link {
    border-bottom: 0.2rem solid transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-family: $font-regular-family;
    color: $brand-black;
    text-decoration: none;
    font-size: 1.6rem;
    font-weight: normal;
    line-height: 1.8rem;
    padding: 2.4rem;
    text-align: center;
    height: 6.8rem;
    outline: none;
    margin-right: 0.2rem;

    &--active {
      font-family: $font-bold-family;
      border-color: $brand-red;
      padding: 1.2rem 2.35rem;
    }

    &:hover {
      border-color: $canvas-gray-700;
    }

    &:focus-visible {
      box-shadow: 0 0 0 0.2rem $brand-blue;
    }
  }

  &__user-avatar {
    margin-right: 0.8rem;
  }

  &__user-container {
    display: none;

    @include mq($from: desktop) {
      display: flex;
      align-items: center;
    }
  }

  &__user-full-name {
    font-family: $font-regular-family;
    font-size: 1.6rem;
  }

  .Hamburger__menu {
    z-index: 2;
  }
}
