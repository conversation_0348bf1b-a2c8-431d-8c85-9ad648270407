import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Link, NavLink, useLocation, useHistory } from 'react-router-dom';
import { bindActionCreators } from 'redux';
import LogoFlyingS from 'canvas-core-react/lib/LogoFlyingS';
import Avatar from 'canvas-core-react/lib/Avatar';
import TextIntroduction from 'canvas-core-react/lib/TextIntroduction';
import IconExit from 'canvas-core-react/lib/IconExit';
import TextButton from 'canvas-core-react/lib/TextButton';
import Hamburger from 'canvas-core-react/lib/Hamburger';
import HamburgerLink from 'canvas-core-react/lib/HamburgerLink';
import TextBody from 'canvas-core-react/lib/TextBody';
import { routes } from '../../constants/routes';
import { logout } from '../../store/actions/auth';
import { removeAlert } from '../../store/actions/alertBanner';
import { createInitialsFromName } from '../../utils';
import allPermissions from '../../constants/permissionsList';

export const MainNavigation = ({
  userFullName,
  permissions,
  loggedIn,
  logout,
  removeAlert,
  wamLogoutUrl,
}) => {
  const location = useLocation();
  const history = useHistory();
  const [ isMenuOpen, setIsMenuOpen ] = useState(false);

  const links = permissions ? Object.entries(routes).reduce((acc, [ headerNavName, tabLinks ]) => {
    const availableTabs = tabLinks
      .filter(({ section }) => {
        if (permissions.admin ||
            permissions[`${section.toLowerCase().replace(' ', '-')}_view_super`] ||
            permissions[`${section.toLowerCase().replace(' ', '-')}_view`]) {
          return true;
        }
        if (section === 'variable_mappings') {
          return permissions[allPermissions.KT_VARIABLE_MAPPING_VIEW] || permissions[allPermissions.PEGA_VARIABLE_MAPPING_VIEW];
        }
        return false;
      });
    if (availableTabs.length > 0) {
      acc.push(({
        name: headerNavName,
        to: availableTabs[0].to,
      }));
    }
    return acc;
  }, []) : [];

  const isHeaderLinkActive = (headerLink) => () => {
    const { pathname } = location;
    return routes[headerLink].filter(({ to }) => pathname.startsWith(to)).length > 0;
  };

  const renderBranding = () => (
    <div className="core-header__branding">
      <LogoFlyingS
        className="core-header__logo"
        id="heading-logo"
        size={30}
      />
      <TextIntroduction
        className="core-header__name"
        component="h2"
      >
        Pigeon
      </TextIntroduction>
    </div>
  );

  const renderHeaderNav = () => {
    if (!permissions) {
      return;
    }

    return (
      <div className="core-header__navigation-links">
        {
          links.map((link, index) => (
            <NavLink
              key={`nav-item-${index}`}
              to={link.to}
              className="core-header__navigation-link"
              activeClassName="core-header__navigation-link--active"
              isActive={isHeaderLinkActive(link.name)}
            >
              { link.name }
            </NavLink>
          ))
        }
      </div>
    );
  };

  const renderUserFullName = () => {
    if (!loggedIn) {
      return;
    }

    return (
      <div className="core-header__user-container">
        <Avatar
          className="core-header__user-avatar"
          ariaLabel={userFullName}
          text={createInitialsFromName(userFullName)}
          size={32}
        />
        <span className="core-header__user-full-name">{ userFullName }</span>
        <TextButton
          className="core-header__sign-out"
          Icon={IconExit} onClick={() => logout(wamLogoutUrl)}
        >
          Sign out
        </TextButton>
      </div>
    );
  };

  const renderMobileHamburgerMenu = () => {
    if (links.length === 0) {
      return;
    }

    return (
      <Hamburger
        className="core-header__hamburger-menu"
        menuButtonLabel="Menu"
        closedMenuButtonLabel="close menu"
        openMenuLabel="open menu"
        isMenuOpen={isMenuOpen}
        setIsMenuOpen={setIsMenuOpen}
      >
        {
          links.map(link => (
            <HamburgerLink
              key={`hamburger-link-${link.name}`}
              href=""
              component={Link}
              to={link.to}
              onClick={() => setIsMenuOpen(false)}
            >
              <TextBody component="p" type="2">{ link.name }</TextBody>
            </HamburgerLink>
          ))
        }
        <HamburgerLink
          href="/"
          onClick={e => {
            logout(wamLogoutUrl);
            setIsMenuOpen(false);
            removeAlert();
            history.push('/');
            e.preventDefault();
          }}
        >
          <TextBody component="p" type="2">Sign out</TextBody>
        </HamburgerLink>
      </Hamburger>
    );
  };

  return (
    <div className="core-header">
      <div className="core-header__inner">
        { renderBranding() }
        { renderHeaderNav() }
        { renderUserFullName() }
        { renderMobileHamburgerMenu() }
      </div>
    </div>
  );
};

MainNavigation.propTypes = {
  userFullName: PropTypes.string.isRequired,
  permissions: PropTypes.object,
  loggedIn: PropTypes.bool.isRequired,
  wamLogoutUrl: PropTypes.string,
  logout: PropTypes.func.isRequired,
  removeAlert: PropTypes.func,
};

const mapStateToProps = state => ({
  permissions: state.authenticated?.permissions,
  userFullName: state.authenticated?.name || '',
  loggedIn: !!state.authenticated?.permissions,
  wamLogoutUrl: state.authenticated?.auth_config?.wam_logout,
});

const mapDispatchToProps = dispatch => bindActionCreators({
  logout,
  removeAlert,
}, dispatch);

export default connect(mapStateToProps, mapDispatchToProps)(MainNavigation); ;
