import React from 'react';
import PropTypes from 'prop-types';

import CanvasBadge from 'canvas-core-react/lib/StatusBadge';

import { STATUS } from '../../constants';

export default class StatusBadge extends React.PureComponent {
  static propTypes = {
    status: PropTypes.string.isRequired,
    disabled: PropTypes.bool,
    deleted: PropTypes.bool,
    startTime: PropTypes.string,
    endTime: PropTypes.string,
    className: PropTypes.string,
  };

  renderStatusText(status) {
    if (this.props.deleted) {
      return STATUS.DELETED;
    }
    if (status === STATUS.PUBLISHED) {
      if (new Date(this.props.endTime) < Date.now()) {
        return STATUS.EXPIRED;
      }
      if (new Date(this.props.startTime) > Date.now() && !this.props.disabled) {
        return STATUS.UPCOMING;
      }
      if (this.props.disabled) {
        return STATUS.INACTIVE;
      }
      // For vignette - sol & sotrefront, published status is not broken down by upcoming/expired active/inactive
      if (!this.props.startTime && !this.props.endTime) {
        return STATUS.PUBLISHED;
      }
      return STATUS.ACTIVE;
    }
    return status;
  }

  renderVariant(statusText) {
    switch (statusText) {
      case STATUS.DRAFT:
      case STATUS.SUBMITTED:
      case STATUS.REVIEWED:
      case STATUS.UPDATED:
        return 'new';
      case STATUS.ACTIVE:
      case STATUS.UPCOMING:
      case STATUS.PUBLISHED:
        return 'success';
      case STATUS.EXPIRED:
      case STATUS.TERMINATED:
      case STATUS.DELETED:
        return 'error';
      case STATUS.INACTIVE:
      default:
        return 'default';
    }
  }

  render() {
    const status = this.props.status.toLowerCase();
    const statusText = this.renderStatusText(status);
    const variant = this.renderVariant(statusText);
    return (
      <CanvasBadge
        className={this.props.className}
        type={variant}
      >
        { statusText.toUpperCase() }
      </CanvasBadge>
    );
  }
}
