import React from 'react';
import PropTypes from 'prop-types';
import CanvasCard from 'canvas-core-react/lib/Card';
import IconArrowRight from 'canvas-core-react/lib/IconArrowRight';
import Link from 'canvas-core-react/lib/Link';
import { changeFocus } from '../../constants';

const toggleCard = (id) => {
  const card = document.getElementById(id);
  card.classList.toggle('focus-within');
};

const clickAnchor = (event, mainId) => {
  if (event.key === 'Enter') {
    document.getElementById('hashlink').click();
    changeFocus(mainId);
  }
};

const SkipLink = ({ targetId }) => (
  <Link
    id="hashlink"
    href=""
    tabIndex="0"
    onFocus={() => toggleCard('skip-link-card')}
    onKeyDown={event => clickAnchor(event, targetId)}
    onClick={() => changeFocus(targetId)}
    onBlur={() => toggleCard('skip-link-card')}
  >
    <CanvasCard className="card--skip-link" tabIndex="-1" id="skip-link-card">
      <div className="button block block--centered button--text button--skip-link" tabIndex="-1">
        <span className="link link--dotted--hover button__text link--skip-link">
          Skip to main content
        </span>
        <span className="button__icon--near block block-centered">
          <IconArrowRight size={18} />
        </span>
      </div>
    </CanvasCard>
  </Link>
);

SkipLink.propTypes = {
  targetId: PropTypes.string.isRequired,
};

export default SkipLink;
