// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SkipLink Snapshot 1`] = `
<f
  className={null}
  component="a"
  externalIconLabel={null}
  href=""
  id="hashlink"
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onKeyDown={[Function]}
  size={16}
  tabIndex="0"
  target={null}
  type="default"
  weight="bold"
>
  <f
    className="card--skip-link"
    id="skip-link-card"
    lgPadding={36}
    mdPadding={30}
    smPadding={24}
    tabIndex="-1"
    type="flatSolid"
    xsPadding={18}
  >
    <div
      className="button block block--centered button--text button--skip-link"
      tabIndex="-1"
    >
      <span
        className="link link--dotted--hover button__text link--skip-link"
      >
        Skip to main content
      </span>
      <span
        className="button__icon--near block block-centered"
      >
        <Component
          size={18}
        />
      </span>
    </div>
  </f>
</f>
`;
