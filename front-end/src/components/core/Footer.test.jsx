import React from 'react';
import Footer from './Footer';
import { render } from '@testing-library/react';

describe('Footer', () => {
  it('renders the current year (please update in code if this fails)', () => {
    const { queryByText } = render(<Footer />);
    const currentYear = new Date().getFullYear();
    expect(
      queryByText(`© ${currentYear} Scotiabank. All Rights Reserved.`),
    ).toBeInTheDocument();
  });
});
