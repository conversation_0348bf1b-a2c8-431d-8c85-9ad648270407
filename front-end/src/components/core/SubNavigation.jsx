import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { NavLink, useLocation } from 'react-router-dom';
import allPermissions from '../../constants/permissionsList';

import { routes } from '../../constants/routes';
import { canViewManageRuleType } from '../rules/utils';

export const SubNavigation = ({ access, permissions, variableMapping }) => {
  const location = useLocation();
  const renderTabs = () => {
    const { pathname } = location;
    if (!permissions || !pathname || pathname === '/') {
      return;
    }
    const { viewCampaigns, viewCCAUCampaigns, viewSOL, viewStoreFront } = canViewManageRuleType(access, permissions);

    const current = (Object.values(routes)
      // finds the routing section where current location belongs
      .find(tabs => tabs.filter(({ to }) => pathname.startsWith(to)).length > 0) || [])
      .filter(({ section }) => {
        if (permissions.admin) return true;
        if (section === 'Campaigns') return viewCampaigns;
        if (section === 'ccau_campaigns') return viewCCAUCampaigns;
        if (section === 'sol_campaigns') return viewSOL;
        if (section === 'storefront_campaigns') return viewStoreFront;
        if (section === 'variable_mappings') {
          return permissions[allPermissions.KT_VARIABLE_MAPPING_VIEW] || permissions[allPermissions.PEGA_VARIABLE_MAPPING_VIEW];
        }
        if (permissions[`${section.toLowerCase().replace(' ', '-')}_view_super`] ||
          permissions[`${section.toLowerCase().replace(' ', '-')}_view`]) {
          return true;
        }
        return false;
      }
      )
      .map(t => ({ ...t, active: false }));

    // sets active flag for the tab object
    const activeTab = current.reduce((acc, value, index) => acc > pathname.indexOf(value.to) ? acc : index, 0);
   /* istanbul ignore else */
    if (current[activeTab]) {
      current[activeTab].active = true;
    }

    /* istanbul ignore if */
    if (current.length === 0) {
      return;
    }

    return (
      <div className="core-sub-nav__navigation-links">
        {
          current.map(({ name, active, to }) => {
            if (pathname.startsWith('/variable-mapping')) {
              const { hasPending, hasDraft } = variableMapping;
              const markDraft = hasDraft && name === 'Draft';
              const markPending = hasPending && name === 'Pending';
              name = `${name}${(markDraft || markPending) ? '*' : ''}`;
            }
            return (
              <NavLink
                key={`nav-link-${name}`}
                className="core-sub-nav__navigation-link"
                activeClassName="core-sub-nav__navigation-link--active"
                isActive={() => active}
                to={to}
              >
                { name }
              </NavLink>
            );
          })
        }
      </div>
    );
  };

  return (
    <nav className="core-sub-nav" id="tabbed-nav">
      { renderTabs() }
    </nav>
  );
};

SubNavigation.propTypes = {
  access: PropTypes.object,
  permissions: PropTypes.object,
  variableMapping: PropTypes.object,
};

const mapStateToProps = state => ({
  access: state.authenticated?.access,
  permissions: state.authenticated?.permissions,
  variableMapping: state.variableMapping,
});

export default connect(mapStateToProps)(SubNavigation); ;
