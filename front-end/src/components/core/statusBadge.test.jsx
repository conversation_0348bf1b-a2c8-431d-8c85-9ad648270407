import React from 'react';
import { shallow } from 'enzyme';

import StatusBadge from './statusBadge';
import { STATUS } from '../../constants';

describe('StatusBadge', () => {
  for (const status in STATUS) {
    const wrapper = shallow(<StatusBadge status={STATUS[status]} />);
    global.snapshot(wrapper);
  }
});

describe('StatusBadge (Expired)', () => {
  const wrapper = shallow(<StatusBadge status="" endTime="0" />);
  global.snapshot(wrapper);
});
