import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import Checkbox from './Checkbox';
import IconChevronRight from 'canvas-core-react/lib/IconChevronRight';
import { checkStates } from './common';
import { capitalize, matchAll } from '../../utils';

const TargetingItem = ({
  label,
  expanded,
  onAccordionToggle,
  onCheckboxChecked,
  disabled,
  itemTag,
  children,
  searchValue,
  columns,
  ...rest // includes include/exclude
}) => {
  const highlightSearchedValue = value => {
    if (!searchValue || searchValue.length > value) {
      return value;
    }

    const matches = matchAll(searchValue, value);

    if (matches.length === 0) {
      return value;
    }

    let vi = matches[0]; // value pointer
    const output = vi === 0 ? [ ] : [ value.slice(0, vi) ];

    matches.forEach((matchObject, index) => {
      output.push(
        <span className="targeting-item__highlight">
          { value.slice(matchObject, matchObject + searchValue.length) }
        </span>
      );
      vi = matchObject + searchValue.length;

      // get the next chunk
      if (index !== matches.length - 1) {
        output.push(value.slice(vi, matches[index + 1]));
        vi = matches[index + 1];
      }
    });

    if (vi !== value.length) {
      output.push(value.slice(vi, value.length));
    }

    return output;
  };

  const isGroup = !!children;

  const itemContent = (
    <div className="targeting-item__content-container">
      <div className={classnames('targeting-item__content', { 'targeting-item__content--child': !isGroup })}>
        { isGroup &&
          <IconChevronRight className={classnames('targeting-item__chevron', { 'targeting-item__chevron--expanded': expanded })} />
        }
        <div className="targeting-item__item-information">
          { itemTag && <span className={classnames('targeting-item__tag', { 'targeting-item__tag--group': isGroup })}>
            { highlightSearchedValue(itemTag) }
          </span> }
          <label className={classnames('targeting-item__label', { 'targeting-item__label--group': isGroup })}>
            { highlightSearchedValue(label) }
          </label>
        </div>
      </div>
    </div>
  );

  const relationshipCheckboxes = (
    <div className="targeting-item__checkbox-container">
      {
        columns.map((relationship, index) => {
          const checkboxLabel = isGroup
            ? `${capitalize(relationship)} all ${label} items` // Include all Retail items
            : `${capitalize(relationship)} the ${label} item`; // Include the Scotiabank Amex Gold item
          return (
            <Checkbox
              key={`targetingitem-${relationship}-${index}`}
              className={classnames('targeting-item__checkbox', `targeting-item__checkbox--${relationship}`)}
              disabled={disabled}
              checked={rest[relationship]}
              onChange={(checked) => onCheckboxChecked(relationship, checked)}
              label={checkboxLabel}
            />
          );
        })
      }
    </div>
  );

  const listItem = isGroup ? (
    <button
      type="button"
      className={classnames('targeting-item__hover-item', 'targeting-item__hover-item--group')}
      onClick={onAccordionToggle}
      aria-label={`${label} ${expanded ? 'expanded' : 'collapsed'}`}
    >
      { itemContent }
    </button>
  ) : (
    <div className="targeting-item__hover-item">
      { itemContent }
    </div>
  );

  return (
    <li className="targeting-item">
      { listItem }
      { relationshipCheckboxes }
      { expanded && children }
    </li>
  );
};

TargetingItem.propTypes = {
  label: PropTypes.string,
  expanded: PropTypes.bool,
  onAccordionToggle: PropTypes.func.isRequired,
  onCheckboxChecked: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  itemTag: PropTypes.string,
  children: PropTypes.node,
  searchValue: PropTypes.string,
  include: PropTypes.oneOf(Object.values(checkStates)),
  exclude: PropTypes.oneOf(Object.values(checkStates)),
  columns: PropTypes.arrayOf(PropTypes.string).isRequired,
};

export default TargetingItem;
