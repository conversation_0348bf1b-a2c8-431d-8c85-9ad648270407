import React, { useMemo, useState, useEffect, useRef } from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';

import IconAdd from 'canvas-core-react/lib/IconAdd';
import IconMinus from 'canvas-core-react/lib/IconMinus';
import IconSearch from 'canvas-core-react/lib/IconSearch';
import IconCloseCircle from 'canvas-core-react/lib/IconCloseCircle';
import <PERSON><PERSON><PERSON> from 'canvas-core-react/lib/Tooltip';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import RadioButton from 'canvas-core-react/lib/RadioButton';

import TargetingItem from './TargetingItem';
import PreviewGroup from './PreviewGroup';
import PreviewCollection from './PreviewCollection';
import { capitalize, debounce } from '../../utils';
import { checkStates, includeOptions } from './common';

import {
  setDescendantsExpandedState,
  checkAllDescendants,
  updateAncestorsCheckState,
  expandAnySelections,
  getAllSelectedItemsByCategory,
  findSearchMatches,
  checkOnlyIncludeOrExclude,
} from './utils/treeUtils';

const minSearchLength = 2;

const AdvancedTargeting = ({
  data,
  labels,
  onChange,
  defaultIncludeMode,
  disabled,
  renderItemTag,
  onChangeFormatter,
  includeSearch,
  includeSelectionPreview,
  columns,
  disableOwnershipSelection,
  selectOnlyIncludeorExclude,
  productRelationShipCheckBoxes,
}) => {
  const [ , updater ] = useState(null);
  const [ isAllExpanded, setIsAllExpanded ] = useState(false);
  const [ isDirty, setIsDirty ] = useState(false); // determines if component has been used
  const [ searchResults, setSearchResults ] = useState(0);
  const [ searchValue, setSearchValue ] = useState(null);
  const [ gettingResults, setGettingResults ] = useState(false);
  const parentRef = useRef();
  const searchRef = useRef();

  const [ selections, setSelections ] = useState(null);
  const [ includeMode, setIncludeMode ] = useState(defaultIncludeMode);

  const isSearching = ![ null, '' ].includes(searchValue) && searchValue.length >= minSearchLength;

  const update = () => updater({});

  const setComponentDirty = () => {
    if (!isDirty) {
      setIsDirty(true);
    }
  };

  // the data prop but with additional setup data for tree traversal item presentation
  const tree = useMemo(() => {
    const defaultSelections = { include: [], exclude: [] };
    const addRelationships = (data, parentElement = null) => {
      data.forEach(item => {
        if (renderItemTag) {
          item.tag = renderItemTag(item);
        }

        item.parent = parentElement;

        if (item.include) {
          defaultSelections.include.push(item);
        } else if (item.exclude) {
          defaultSelections.exclude.push(item);
        }

        item.include = item.exclude = checkStates.unchecked;

        item.category = parentElement === null ? item.label : parentElement.category;
        if (item.children) {
          item.expanded = false;
          addRelationships(item.children, item);
        }
      });
      return data;
    };
    const tree = addRelationships([ ...data ]);

    Object.keys(defaultSelections).forEach(relationship => {
      defaultSelections[relationship].forEach(itemToCheck => {
        checkAllDescendants(itemToCheck, relationship, true);
        updateAncestorsCheckState(itemToCheck.parent);
      });
    });
    expandAnySelections(tree);

    const selectionsByCategory = getAllSelectedItemsByCategory(tree);
    if (Object.keys(selectionsByCategory).length > 0) {
      setSelections(selectionsByCategory);
    }

    return tree;
  }, [ data ]);

  // perform a search when the value changes
  useEffect(() => {
    if (searchValue === null) {
      return;
    }

    if (!isSearching) {
      setSearchResults(0);
      expandAnySelections(tree);
      setIsAllExpanded(false);
      update();
    } else {
      setGettingResults(true);
      searchTree.current(tree, searchValue);
    }

    setComponentDirty();
  }, [ searchValue ]);

  // fire `onChange` whenever selections or the include mode changes
  useEffect(() => {
    const changeObject = {};

    if (!selections || !isDirty) {
      return;
    }

    if (Object.keys(selections).length === 0) {
      onChange(changeObject);
      return;
    }
    // massage selections into an object in the form of { include: [...], exclude: [...], includeMode: 'any' }
    Object.keys(selections).forEach(category => {
      const selectionCategory = selections[category];
      columns.forEach(relationship => {
        if (selectionCategory[relationship].length === 0) {
          return;
        }

        // resolve any virtual groups since these should not be submitted
        const mappedSelections = selectionCategory[relationship]
          .reduce((acc, sel) => {
            return acc.concat(sel.virtualGroup ? sel.children : [ sel ]);
          }, [])
          .map(selection => {
            if (onChangeFormatter) {
              return onChangeFormatter(selection);
            }
            // by default remove all internal component variables from this item before submitting
            const {
              include, exclude, parent, hidden, expanded, tag, // private variables
              ...rest
            } = selection;
            return rest;
          });

        changeObject[relationship] = changeObject[relationship]
          ? changeObject[relationship].concat(mappedSelections)
          : mappedSelections;
      });
    });

    changeObject.includeMode = includeMode;

    onChange(changeObject);
  }, [ selections, includeMode ]);

  const toggleItemExpanded = item => {
    setComponentDirty();
    item.expanded = !item.expanded;
    update();
  };

  // augment data with ids and references to parents
  const renderDataDropdowns = listings => (
    <ul className="advanced-targeting__listing">
      {
        listings.map((item, index) => {
          const {
            label,
            children,
            expanded,
            hidden,
            tag,
          } = item;
          const childItems = children?.length > 0 ? renderDataDropdowns(children) : null;

          if (hidden) {
            return;
          }

          let isDisabled = false;
          if (disableOwnershipSelection && (item?.label === 'Retail' || item?.label === 'Business')) {
            isDisabled = true;
          }
          if (disabled) {
            isDisabled = true;
          }

          return (
            <TargetingItem
              key={`targeting-item-${label}-${tag}-${index}`}
              disabled={isDisabled}
              label={label}
              expanded={expanded}
              itemTag={tag}
              include={item.include}
              exclude={item.exclude}
              hideCheckboxes={item.hideCheckboxes}
              searchValue={isSearching ? searchValue : undefined}
              onAccordionToggle={() => toggleItemExpanded(item)}
              onCheckboxChecked={(relationship, checked) => checkboxChecked(checked, item, relationship)}
              columns={columns}
            >
              { childItems }
            </TargetingItem>
          );
        })
      }
    </ul>
  );

  const searchTree = useRef(debounce((tree, value) => {
    const results = findSearchMatches(tree, value);
    setSearchResults(results);
    setGettingResults(false);

    if (results > 0) {
      setIsAllExpanded(true);
    }
  }, 400));

  const checkboxChecked = (checked, obj, keyToAlter) => {
    setComponentDirty();
    checkAllDescendants(obj, keyToAlter, checked);
    updateAncestorsCheckState(obj.parent);

    if (selectOnlyIncludeorExclude) {
      checkOnlyIncludeOrExclude(tree, keyToAlter);
    }

    setSelections(getAllSelectedItemsByCategory(tree));
  };

  const renderSearch = () => {
    const resultsText = `${searchResults === 0 ? 'No' : searchResults} Result${searchResults === 1 ? '' : 's'}`;
    const resultsBar = isSearching && (
      <div className="advanced-targeting__search-results">
        <span>{ resultsText }</span>
        <button
          className="advanced-targeting__search-clear-button"
          aria-label="Clear search"
          onClick={() => {
            setSearchValue('');
            searchRef.current.focus();
          }}
        >
          <IconCloseCircle />
        </button>
      </div>
    );

    return (
      <div className="advanced-targeting__search-container">
        <IconSearch className="advanced-targeting__search-icon" />
        <input
          className="advanced-targeting__search-input"
          placeholder="Search"
          onChange={(e) => setSearchValue(e.target.value)}
          value={searchValue || ''}
          ref={searchRef}
        />
        { resultsBar }
      </div>
    );
  };

  const renderSelectionPreview = () => {
    const selectionsForRelationship = {
      include: false,
      exclude: false,
    };

    Object.values(selections || {}).forEach(relationships => {
      columns.forEach(relationshipKey => {
        if (!selectionsForRelationship[relationshipKey] && relationships[relationshipKey].length > 0) {
          selectionsForRelationship[relationshipKey] = true;
        }
      });
    });

    const previewSections = selections && Object.keys(selections).map(category => {
      const categoryItems = selections[category];

      const labelForItem = item => `${item.tag ? `(${item.tag}) ` : ''}${item.label}`; // item descriptions to include tag

      const previewItemsForRelationship = relationship => {
        const itemsForRelationship = categoryItems[relationship];
        const categoryItemPreviews = itemsForRelationship.length > 0
          ? (
            <PreviewCollection>
              {
                itemsForRelationship.map((sel, index) => (
                  <PreviewGroup
                    key={`preview-group-${sel.label}-${sel.tag}-${index}`}
                    className="advanced-targeting__preview-pill-group"
                    disabled={disabled}
                    label={labelForItem(sel)}
                    onDelete={() => checkboxChecked(false, sel, relationship)}
                  >
                    { sel.children?.length > 0 && sel.children.map(c => ({ ...c, label: labelForItem(c) })) }
                  </PreviewGroup>
                ))
              }
            </PreviewCollection>
          ) : (
            <div className={
              classnames('advanced-targeting__no-items',
                { 'advanced-targeting__no-items--exclude': relationship === 'exclude' })
            }
            >No items selected</div>
          );

        return (
          <div
            key={`relationship-preview-section-${relationship}`}
            className={
              classnames('advanced-targeting__preview-section',
                { 'advanced-targeting__preview-section--exclude': relationship === 'exclude' })
            }
          >
            <h3 className="advanced-targeting__preview-category-title">{ category }: </h3>
            <div className="advanced-targeting__pill-box">
              { categoryItemPreviews }
            </div>
          </div>
        );
      };

      const items = columns.map(previewItemsForRelationship);

      return items;
    });

    const renderedRadioButtons = Object.keys(includeOptions).map(
      option => (
        <RadioButton
          key={`relationship-radio-${option}`}
          disabled={disabled || !selectionsForRelationship.include}
          className="advanced-targeting__radio"
          id={`include-${option}`}
          label={capitalize(option)}
          name="include"
          checked={includeMode === option}
          onChange={() => {
            setComponentDirty();
            setIncludeMode(option);
          }}
        />
      )
    );

    const categoryPreviewContent = previewSections?.length > 0
      ? previewSections : (
        <>
          <div className="advanced-targeting__no-items">No items selected</div>
          <div className="advanced-targeting__no-items advanced-targeting__no-items--exclude">No items selected</div>
        </>
      );

    return (
      <div className="advanced-targeting__selection-container">
        <div className="advanced-targeting__preview-section">
          <div className="advanced-targeting__relationship-group">
            <TextSubtitle component="h3" className="advanced-targeting__relationship-label">
              Include:
              <Tooltip
                id="include-tooltip"
                heading="Include"
                infoButtonLabel="Info"
                closeButtonLabel="close"
                relativeParentRef={parentRef}
              >
                <TextCaption component="div">
                  <p>If &quot;Any&quot; is selected, customers that match any of the selected targeting criteria will be included in this campaign.</p>
                  <br />
                  <p>If &quot;All&quot; is selected, customers that match all of the selected targeting criteria will be included in this campaign.</p>
                </TextCaption>
              </Tooltip>
            </TextSubtitle>
            { renderedRadioButtons }
          </div>
        </div>
        <div className="advanced-targeting__preview-section advanced-targeting__preview-section--exclude">
          <div className="advanced-targeting__relationship-group">
            <TextSubtitle component="h3" className="advanced-targeting__relationship-label">
              Exclude:
              <Tooltip
                id="exclude-tooltip"
                heading="Exclude"
                infoButtonLabel="Info"
                closeButtonLabel="close"
              >
                <TextCaption component="p">
                  If &quot;Any&quot; is selected, customers that match any of the selected targeting criteria will be excluded in this campaign.
                </TextCaption>
              </Tooltip>
            </TextSubtitle>
            <RadioButton
              disabled={disabled || !selectionsForRelationship.exclude}
              className="advanced-targeting__radio"
              id="exclude-any"
              label="Any"
              name="exclude"
              checked
            />
          </div>
        </div>
        { categoryPreviewContent }
      </div>
    );
  };

  const renderLabelBar = () => {
    const labelClassName = classnames(
      'advanced-targeting__relationship-label',
      'advanced-targeting__relationship-label--label-bar'
    );

    const ToggleExpandIcon = isAllExpanded ? IconMinus : IconAdd;

    return (
      <div className="advanced-targeting__label-bar">
        <button
          className="advanced-targeting__expand-button"
          type="button"
          aria-label={`${!isAllExpanded ? 'Expand' : 'Collapse'} all ${labels.groupingTitle}`}
          onClick={() => {
            setIsAllExpanded(!isAllExpanded);
            setDescendantsExpandedState(tree, !isAllExpanded);
          }}
        >
          <ToggleExpandIcon size={14} />
        </button>
        <div className={labelClassName}>{ labels.groupingTitle }</div>
        <div className="advanced-targeting__relationship-labels">
          <div className={labelClassName}>{ labels.includeLabel }</div>
          { labels.excludeLabel && <div className={labelClassName}>{ labels.excludeLabel }</div> }
        </div>
      </div>
    );
  };

  const renderDropdownSection = () => {
    const sectionContent = (isSearching && searchResults === 0 && !gettingResults)
      ? <div className="advanced-targeting__no-results">No results found</div>
      : renderDataDropdowns(tree);

    return (
      <div
        className={classnames({
          'advanced-targeting__dropdown-container': true,
          'advanced-targeting__dropdown-container--expanded': isDirty,
        })}
      >
        { sectionContent }
      </div>
    );
  };

  return (
    <div className="advanced-targeting" ref={parentRef}>
      { includeSearch && renderSearch() }
      { renderLabelBar() }
      { renderDropdownSection() }
      { includeSelectionPreview && renderSelectionPreview() }
      { productRelationShipCheckBoxes && productRelationShipCheckBoxes }
    </div>
  );
};

AdvancedTargeting.propTypes = {
  data: PropTypes.array.isRequired,
  onChange: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  renderItemTag: PropTypes.func,
  onChangeFormatter: PropTypes.func,
  labels: PropTypes.shape({
    groupingTitle: PropTypes.string.isRequired,
    searchPlaceholder: PropTypes.string.isRequired,
    includeLabel: PropTypes.string.isRequired,
    excludeLabel: PropTypes.string.isRequired,
  }).isRequired,
  defaultIncludeMode: PropTypes.oneOf([ includeOptions.all, includeOptions.any ]),
  includeSearch: PropTypes.bool,
  includeSelectionPreview: PropTypes.bool,
  columns: PropTypes.arrayOf(PropTypes.string).isRequired,
  disableOwnershipSelection: PropTypes.bool,
  selectOnlyIncludeorExclude: PropTypes.bool,
  productRelationShipCheckBoxes: PropTypes.array,
};

AdvancedTargeting.defaultProps = {
  defaultIncludeMode: includeOptions.any,
  includeSearch: false,
  includeSelectionPreview: false,
  data: [],
  onChange: () => {},
  disableOwnershipSelection: false,
  selectOnlyIncludeorExclude: false,
  productRelationShipCheckBoxes: null,
};

export default AdvancedTargeting;
