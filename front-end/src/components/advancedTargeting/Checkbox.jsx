import React, { useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { checkStates } from './common';

import IconToggleCheck from 'canvas-core-react/lib/IconToggleCheck';
import IconIndeterminate from './IconIndeterminate';

const Checkbox = ({
  onChange,
  checked,
  className,
  disabled,
  label,
}) => {
  const r = useRef(null);

  useEffect(() => {
    r.current.indeterminate = checked === checkStates.indeterminate;
  }, [ checked ]);

  const renderIconForCheckState = () => {
    const iconColour = disabled ? 'gray' : 'white';

    switch (checked) {
      case checkStates.checked:
        return <IconToggleCheck color={iconColour} size={12} />;
      case checkStates.indeterminate:
        return <IconIndeterminate colour={iconColour} />;
      default:
        return null;
    }
  };

  return (
    <div className={classnames('targeting-checkbox', className)}>
      <input
        disabled={disabled}
        className="targeting-checkbox__input"
        ref={r}
        onClick={(e) => e.stopPropagation()}
        type="checkbox"
        checked={checked === checkStates.checked}
        onChange={(e) => onChange(e.target.checked)}
        aria-label={label}
      />
      <span className="targeting-checkbox__control">
        { renderIconForCheckState() }
      </span>
    </div>
  );
};

Checkbox.propTypes = {
  checked: PropTypes.oneOf(Object.values(checkStates)),
  onChange: PropTypes.func.isRequired,
  className: PropTypes.string,
  disabled: PropTypes.bool,
  label: PropTypes.string.isRequired,
};

export default Checkbox;
