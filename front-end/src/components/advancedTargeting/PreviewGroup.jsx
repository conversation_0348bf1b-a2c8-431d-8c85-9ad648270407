import React, { useState } from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import IconCloseCircle from 'canvas-core-react/lib/IconCloseCircle';
import IconChevronRight from 'canvas-core-react/lib/IconChevronRight';
import IconChevronLeft from 'canvas-core-react/lib/IconChevronLeft';

const PreviewGroup = ({
  className,
  label,
  children,
  onDelete,
  disabled,
}) => {
  const [ isExpanded, setIsExpanded ] = useState(false);
  const isGroup = !!children;
  const renderDeleteButton = () => (
    <button
      className="preview-group__delete-button"
      onClick={onDelete} type="button"
      aria-label={`Delete ${label} ${isGroup ? 'Group' : 'Item'}`}
    >
      <IconCloseCircle size={14} />
    </button>
  );

  const renderExpandButton = () => {
    if (!children) {
      return null;
    }

    const ChevronToUse = isExpanded ? IconChevronLeft : IconChevronRight;
    return (
      <button
        className={classnames('preview-group__expand-button', { 'preview-group__expand-button--expanded': isExpanded })}
        onClick={() => setIsExpanded(!isExpanded)}
        type="button"
      >
        <ChevronToUse size={14}/>
      </button>
    );
  };

  const renderChildren = () => {
    if (!children || !isExpanded) {
      return null;
    }

    return children.map(({ label }) => renderPreviewItem(label, true));
  };

  const renderPreviewItem = (previewLabel, isChild = false) => {
    return (
      <div
        className={classnames(className, 'preview-group__item', { 'preview-group__item--child': isChild })}
      >
        <div className={classnames('preview-group__item-inner', { 'preview-group__item-inner--has-children': !!children && !isChild })}>
          <span className={classnames('preview-group__label', {
            'preview-group__label--has-children': !!children,
            'preview-group__label--child': isChild })}
          >
            { previewLabel }
          </span>
          { !isChild && !disabled && renderDeleteButton() }
        </div>
        { !isChild && renderExpandButton() }
      </div>
    );
  };

  return (
    <>
      { renderPreviewItem(label) }
      { renderChildren() }
    </>
  );
};

PreviewGroup.propTypes = {
  className: PropTypes.string,
  label: PropTypes.string,
  children: PropTypes.node,
  onDelete: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
};

export default PreviewGroup;
