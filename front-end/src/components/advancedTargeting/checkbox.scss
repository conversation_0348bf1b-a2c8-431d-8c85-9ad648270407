.targeting-checkbox {
  $gray-bg: #c4c4c4;

  position: relative;

  &__label {
    display: none;
  }

  &__control {
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
    position: absolute;
    top: 0;
    left: 0;
    background-color: $brand-white;
    border: 0.1rem solid $canvas-gray-600;
    width: 2.4rem;
    height: 2.4rem;
    border-radius: 0.4rem;
  }

  &__input {
    opacity: 0;
    width: 2.4rem;
    height: 2.4rem;
    margin: 0;
    cursor: pointer;

    &:focus {
      + .targeting-checkbox__control {
        &::after {
          border-radius: 0.4rem;
          box-shadow:
            0 0 0 0.2rem $brand-white,
            0 0 0 0.4rem $canvas-dark-blue;
          content: ' ';
          display: inline-block;
          height: calc(100% + #{0.2rem});
          left: -0.1rem;
          opacity: 1;
          position: absolute;
          top: -0.1rem;
          width: calc(100% + #{0.2rem});
        }
      }
    }

    &:checked {
      + .targeting-checkbox__control {
        background-color: $canvas-dark-blue;
        border-color: $canvas-dark-blue;
      }
    }

    &:indeterminate {
      + .targeting-checkbox__control {
        background-color: $gray-bg;
        border-color: $gray-bg;
      }
    }

    &[disabled] {
      cursor: not-allowed;

      &:checked {
        + .targeting-checkbox__control {
          background-color: $gray-bg;
          border-color: $gray-bg;
        }
      }
    }
  }
}
