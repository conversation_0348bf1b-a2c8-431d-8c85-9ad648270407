import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import IconChevronUp from 'canvas-core-react/lib/IconChevronUp';
import IconChevronDown from 'canvas-core-react/lib/IconChevronDown';

const PreviewCollection = ({
  children,
  filterThreshold,
}) => {
  const [ showAllItems, setShowAllItems ] = useState(false);

  const exceedsFilterThreshold = children.length > filterThreshold;

  // reset the filter state if number of its no longer exceeds threshold
  useEffect(() => {
    if (!exceedsFilterThreshold) {
      setShowAllItems(false);
    }
  }, [ children ]);

  const renderFilterButton = () => {
    if (!exceedsFilterThreshold) {
      return;
    }

    const ChevronToUse = showAllItems ? IconChevronUp : IconChevronDown;
    return (
      <button
        className="preview-collection__filter-button"
        onClick={() => setShowAllItems(!showAllItems)}
        type="button"
      >
        { showAllItems ? 'See less' : `See ${children.length - filterThreshold} more` }
        { <ChevronToUse className="preview-collection__chevron" size={10} /> }
      </button>
    );
  };

  return (
    <div className="preview-collection">
      <div className="preview-collection__children">
        { children.slice(0, showAllItems ? children.length : filterThreshold) }
      </div>
      { renderFilterButton() }
    </div>
  );
};

PreviewCollection.propTypes = {
  children: PropTypes.node,
  filterThreshold: PropTypes.number.isRequired,
};

PreviewCollection.defaultProps = {
  filterThreshold: 7,
};

export default PreviewCollection;
