.advanced-targeting {
  /* Search */
  &__search-container {
    position: relative;
    height: 5.4rem;
  }

  &__search-icon {
    &.SvgIcon__icon {
      display: flex;
      height: 100%;
      align-items: center;
      position: absolute;
      left: 1.4rem;
    }
  }

  &__search-clear-button {
    display: flex;
    background: none;
    cursor: pointer;
    border: 0.1rem solid transparent;
    border-radius: 1.8rem;
    padding: 0.2rem;
    outline: none;

    &:focus {
      border: 0.1rem solid $brand-blue;
      box-shadow: 0 0 0 0.1rem $brand-blue;
    }
  }

  &__search-results {
    display: flex;
    position: absolute;
    height: 100%;
    top: 0;
    align-items: center;
    right: 2rem;
  }

  &__no-results,
  &__no-items {
    font-size: 1.6rem;
    color: $canvas-gray-600;

    &--exclude {
      @extend .advanced-targeting__preview-section--exclude;
    }
  }

  &__no-results {
    margin: 1.6rem 1.9rem;
  }

  &__no-items {
    margin-top: 0.6rem;
  }

  &__search-input {
    height: 100%;
    width: 100%;
    border: 0.1rem solid $canvas-gray-600;
    border-bottom: none;
    border-radius: 0.4rem 0.4rem 0 0;
    padding-left: 4rem;
    font-family: $font-regular;
    font-size: 1.6rem;
    outline: none;

    &:focus {
      border: 0.1rem solid $brand-blue;
      box-shadow: 0 0 0 0.1rem $brand-blue;
    }
  }

  /* Label bar */
  &__label-bar {
    height: 5.4rem;
    padding-right: 3.6rem;
    border: 0.1rem solid $canvas-gray-600;
    display: flex;
    align-items: center;
  }

  &__relationship-labels {
    display: flex;
    margin-left: auto;
  }

  &__expand-button {
    background: none;
    border: 0.1rem solid transparent;
    border-radius: 0.3rem;
    outline: none;
    padding: 0.1rem;
    margin: 0 1.6rem;
    height: 1.8rem;
    cursor: pointer;

    &:focus {
      border: 0.1rem solid $brand-blue;
      box-shadow: 0 0 0 0.1rem $brand-blue;
    }
  }

  /* Dropdown listings */
  &__dropdown-container {
    $rowHeight: 5.4rem;

    position: relative;
    border: 0.1rem solid $canvas-gray-600;
    border-top: none;
    border-radius: 0 0 0.4rem 0.4rem;
    min-height: $rowHeight * 3;
    max-height: $rowHeight * 6;
    overflow-y: scroll;

    @supports (overflow: overlay) {
      overflow-y: overlay;
    }

    &--expanded {
      min-height: $rowHeight * 6;
    }
  }

  &__listing {
    padding-left: 1.8rem;
  }

  /* Preview section */
  &__selection-container {
    margin-top: 2.4rem;
    display: grid;
    grid-gap: 1.8rem;
    grid-template-columns: 100%;

    @include mq($from: tablet) {
      grid-template-columns: calc(50% - 1.8rem / 2) calc(50% - 1.8rem / 2);
    }
  }

  &__preview-section {
    &--exclude {
      order: 2;

      @include mq($from: tablet) {
        order: unset;
      }
    }
  }

  &__relationship-group {
    display: flex;
    align-items: center;
    height: 2.4rem;
    margin-bottom: -0.6rem;
  }

  &__relationship-label {
    margin-right: 2rem;
    font-weight: bold;
    font-family: $font-regular-family;
    font-size: 1.6rem;

    &--label-bar {
      margin: 0;
      min-width: 6rem;

      &:last-child {
        margin-left: 2.4rem;
      }
    }
  }

  &__preview-category-title {
    font-family: $font-regular-family;
    font-weight: bold;
    font-size: 1.4rem;
    margin-bottom: 1.2rem - 0.6rem; // subtracting margin-top from preview-pill-group
  }

  &__preview-pill-group {
    margin-top: 0.6rem;
    margin-right: 0.6rem;
  }

  &__pill-box {
    display: inline-flex;
    flex-wrap: wrap;
    max-width: 100%;
  }

  &__radio {
    align-items: center;
    // inner canvas element, with styles for being displayed inline
    .RadioButton__label {
      padding: 0;
      align-items: center;
    }

    &:not(:last-child) {
      margin-right: 2rem;
    }
  }
}
