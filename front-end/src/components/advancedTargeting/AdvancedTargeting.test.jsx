import React from 'react';
import { render, fireEvent, act } from '@testing-library/react';
import AdvancedTargeting from './AdvancedTargeting';

const targetingData = () => ([
  {
    label: 'Retail',
    ownership: 'R',
    children: [
      {
        label: 'Banking',
        virtualGroup: true,
        children: [
          {
            label: 'AgriInvest',
            ownership: 'R',
            code: 'ABC',
            tag: '123',
            children: [
              {
                label: 'AgriInvest One',
                ownership: 'R',
                code: 'ABC',
                sub_code: 'AI1',
                tag: 'AI1',
              },
              {
                label: 'AgriInvest Two',
                ownership: 'R',
                code: 'ABC',
                sub_code: 'AI2',
                tag: 'AI2',
              },
              {
                label: 'AgriInvest Three',
                ownership: 'R',
                code: 'ABC',
                sub_code: 'AI3',
                tag: 'AI3',
              },
            ],
          },
          {
            label: 'Visa',
            ownership: 'R',
            code: 'BSA',
            sub_code: 'V',
            children: [],
          },
          {
            label: 'Mastercard World Elite',
            ownership: 'R',
            code: 'BSA',
            sub_code: 'MC',
            children: [],
          },
          {
            label: 'American Express Scotiabank Gold',
            ownership: 'R',
            code: 'BSA',
            sub_code: 'AMX',
            children: [],
          },
        ],
      },
      {
        label: 'Borrowing Products',
        virtualGroup: true,
        children: [
          {
            label: 'Retail VISA',
            ownership: 'R',
            code: 'SBC 1',
            sub_code: 'V',
            tag: 'V',
          },
          {
            label: 'Mastercard',
            ownership: 'R',
            code: 'SBC 2',
            sub_code: 'MC',
            tag: 'MC',
          },
          {
            label: 'American Express Scotiabank Gold',
            ownership: 'R',
            code: 'SBC 3',
            sub_code: 'AMX',
            tag: 'AMX',
          },
        ],
      },
    ],
  },
  {
    label: 'Business',
    ownership: 'B',
    children: [
      {
        label: 'Banking Products',
        virtualGroup: true,
        children: [
          {
            label: 'Banking Visa',
            ownership: 'B',
            code: 'BSA',
            sub_code: 'V',
          },
          {
            label: 'Banking Mastercard',
            ownership: 'B',
            code: 'BSA',
            sub_code: 'MC',
          },
          {
            label: 'Banking Amex',
            ownership: 'B',
            code: 'BSA',
            sub_code: 'AMX',
          },
        ],
      },
    ],
  },
]);

// eslint-disable-next-line camelcase
const onChangeFormatter = ({ code, label, ownership, sub_code }) => ({ code, label, ownership, sub_code });

const itemTagRenderer = obj => (
  [ 'ownership', 'code', 'sub_code' ]
    .map(key => obj[key])
    .filter(Boolean)
    .join(':')
);

const columns = [ 'include', 'exclude' ];

describe('AdvancedTargeting', () => {
  // onChange called when an item is selected
  test('onChange is called when items are selected', () => {
    const onChange = jest.fn();
    const { getByLabelText } = render(
      <AdvancedTargeting
        data={targetingData()}
        onChange={onChange}
        onChangeFormatter={onChangeFormatter}
        renderItemTag={itemTagRenderer}
        labels={{ excludeLabel: 'excludeLabel', groupingTitle: 'groupingTitle', searchPlaceholder: 'searchPlaceholder', includeLabel: 'includeLabel' }}
        includeSearch
        includeSelectionPreview
        columns={columns}
      />
    );

    expect(onChange).not.toHaveBeenCalled();

    // choose a visa product in the retail, banking category
    fireEvent.click(getByLabelText('Retail collapsed').closest('button'));
    fireEvent.click(getByLabelText('Banking collapsed').closest('button'));
    fireEvent.click(getByLabelText('Include the Visa item'));

    expect(onChange).toHaveBeenCalledWith({
      include: [
        {
          label: 'Visa',
          ownership: 'R',
          code: 'BSA',
          sub_code: 'V',
        },
      ],
      includeMode: 'any',
    });

    onChange.mockClear();

    // this is an interesting case where we are including a virtual group, we don't actually
    // include this item in the include/exclude field but rather it's children which is why we
    // will see all of Banking's children in the include property of `onChange`
    fireEvent.click(getByLabelText('Include all Banking items'));
    expect(onChange).toHaveBeenCalledWith({
      include: [
        {
          label: 'AgriInvest',
          ownership: 'R',
          code: 'ABC',
        },
        {
          label: 'Visa',
          ownership: 'R',
          code: 'BSA',
          sub_code: 'V',
        },
        {
          label: 'Mastercard World Elite',
          ownership: 'R',
          code: 'BSA',
          sub_code: 'MC',
        },
        {
          label: 'American Express Scotiabank Gold',
          ownership: 'R',
          code: 'BSA',
          sub_code: 'AMX',
        },
      ],
      includeMode: 'any',
    });

    // uncheck agriinvest
    fireEvent.click(getByLabelText('Include all Banking items'));
    onChange.mockClear();

    // exclude all business products
    fireEvent.click(getByLabelText('Exclude all Business items'));
    expect(onChange).toHaveBeenCalledWith({
      exclude: [
        {
          label: 'Business',
          ownership: 'B',
        },
      ],
      includeMode: 'any',
    });

    onChange.mockClear();
    // switching the include mode should trigger the onChange event
    fireEvent.click(getByLabelText('All'));
    expect(onChange).toHaveBeenCalledWith({
      exclude: [
        {
          label: 'Business',
          ownership: 'B',
        },
      ],
      includeMode: 'all', // <--- the difference
    });

    // wipe everything out
    // reset retail
    fireEvent.click(getByLabelText('Include all Retail items')); // checked
    expect(getByLabelText('Include all Retail items')).toBeChecked();
    fireEvent.click(getByLabelText('Include all Retail items')); // unchecked
    expect(getByLabelText('Include all Retail items')).not.toBeChecked();

    onChange.mockClear();
    // uncheck the exclude business we had before
    fireEvent.click(getByLabelText('Exclude all Business items'));
    // everything has been cleared out, onChange fires with an empty object
    expect(onChange).toHaveBeenCalledWith({});
  });

  test('initial selections', async() => {
    const data = targetingData();
    data[0].include = true; // all Retail included by default
    data[1].children[0].children[0].exclude = true; // a Banking Visa product is excluded

    const { getByLabelText } = render(
      <AdvancedTargeting
        data={data}
        labels={{ excludeLabel: 'excludeLabel', groupingTitle: 'groupingTitle', searchPlaceholder: 'searchPlaceholder', includeLabel: 'includeLabel' }}
        columns={columns}
        includeSearch
        includeSelectionPreview
      />
    );

    expect(getByLabelText('Include all Retail items')).toBeChecked();
    expect(getByLabelText('Exclude the Banking Visa item')).toBeChecked();
    expect(getByLabelText('Exclude all Business items')).toBePartiallyChecked(); // indeterminate!
  });

  // when a pill is deleted an item is also deleted
  test('disabled', () => {
    const { getByLabelText } = render(
      <AdvancedTargeting
        data={targetingData()}
        labels={{ excludeLabel: 'excludeLabel', groupingTitle: 'groupingTitle', searchPlaceholder: 'searchPlaceholder', includeLabel: 'includeLabel' }}
        columns={columns}
        includeSearch
        includeSelectionPreview
        disabled
      />
    );
    expect(getByLabelText('Include all Retail items')).toBeDisabled();
    expect(getByLabelText('Include all Business items')).toBeDisabled();
  });

  // searching for items
  test('searching', async() => {
    const { queryByLabelText, getByText, getByLabelText, getByPlaceholderText } = render(
      <AdvancedTargeting
        data={targetingData()}
        onChange={jest.fn()}
        labels={{ excludeLabel: 'excludeLabel', groupingTitle: 'groupingTitle', searchPlaceholder: 'searchPlaceholder', includeLabel: 'includeLabel' }}
        columns={columns}
        includeSearch
        includeSelectionPreview
      />
    );

    // by default everything is collapsed and agriinvest isn't shown
    expect(queryByLabelText('Include AgriInvest')).not.toBeInTheDocument();

    // let's find it!
    await act(async() => {
      fireEvent.change(getByPlaceholderText('Search'), { target: { value: 'AgriInvest' } });
      await new Promise((resolve) => setTimeout(resolve, 1000));
    });

    expect(queryByLabelText('Include all AgriInvest items')).toBeInTheDocument();
    expect(queryByLabelText('Include the AgriInvest One item')).toBeInTheDocument();

    expect(getByText('4 Results')).toBeInTheDocument();

    // select an item
    fireEvent.click(getByLabelText('Include all AgriInvest items'));

    // clear the search
    fireEvent.click(getByLabelText('Clear search'));
    expect(getByPlaceholderText('Search')).toHaveFocus();
    expect(queryByLabelText('Include all AgriInvest items')).toBeInTheDocument();
    expect(queryByLabelText('Include the AgriInvest One item')).not.toBeInTheDocument();
  });
});
