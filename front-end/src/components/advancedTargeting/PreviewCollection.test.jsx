import React from 'react';
import PreviewCollection from './PreviewCollection';
import { render, fireEvent } from '@testing-library/react';

const simpleElement = (index) => <div key={index}>Hi { index }!</div>;
describe('PreviewCollection', () => {
  test('it filters items past its threshold', () => {
    const items = Array.from({ length: 10 }).map((i, index) => simpleElement(index + 1));
    const { container, queryByText, getByText } = render(
      <PreviewCollection filterThreshold={5}>
        { items }
      </PreviewCollection>
    );

    expect(queryByText('Hi 5!')).toBeInTheDocument();
    expect(queryByText('Hi 6!')).not.toBeInTheDocument();

    // click the filter to show the other items
    fireEvent.click(getByText('See 5 more'));

    expect(queryByText('Hi 6!')).toBeInTheDocument();
    expect(container.querySelectorAll('button')).toHaveLength(1);
  });

  test('filter button not shown if below or equal to the threshold', () => {
    const items = Array.from({ length: 5 }).map((i, index) => simpleElement(index + 1));
    const { container, queryByText } = render(
      <PreviewCollection filterThreshold={5}>
        { items }
      </PreviewCollection>
    );

    expect(queryByText('Hi 5!')).toBeInTheDocument();

    expect(container.querySelectorAll('button')).toHaveLength(0);
  });
});
