.preview-group {
  &__item-inner {
    display: flex;
    align-items: center;
    padding: 0.6rem 0 0.6rem 1.2rem;
    max-width: 100%;

    &--has-children {
      max-width: calc(100% - 4.2rem); // 4.2rem is the width of the expand-button
    }
  }

  &__label {
    font-family: $font-regular;
    font-size: 1.6rem;

    &--has-children {
      font-weight: bold;
    }

    &:only-child {
      margin-right: 1.2rem;
    }

    &--child:only-child {
      margin-right: 0;
    }
  }

  &__item {
    display: flex;
    overflow: hidden;
    align-items: center;
    border: 0.1rem solid $canvas-gray-500;
    width: fit-content;
    border-radius: 5rem;
    min-height: 3.6rem;

    &--child {
      background-color: $canvas-gray-200;

      .preview-group__label {
        font-weight: normal;
      }

      .preview-group__item-inner {
        padding: 0.6rem 1.2rem;
      }
    }

    &:not(:last-child) {
      margin-right: 0.6rem;
    }
  }

  &__delete-button {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    height: 1.4rem;
    margin: 0 0.2rem;
    padding: 1.2rem;
  }

  &__expand-button {
    outline: none;
    background: none;
    height: 100%;
    border: none;
    border-left: 0.1rem solid $canvas-gray-500;
    width: 4.2rem;
    border-top-right-radius: 5rem;
    border-bottom-right-radius: 5rem;

    &--expanded {
      background-color: $canvas-gray-200;
    }

    &:focus {
      border: 0.1rem solid $brand-blue;
      box-shadow: 0 0 0 0.1rem $brand-blue;
    }
  }
}
