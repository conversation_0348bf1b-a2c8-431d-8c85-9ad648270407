import {
  setDescendantsExpandedState,
  checkAllDescendants,
  expandAnySelections,
  updateAncestorsCheckState,
  findSelectionsInTree,
  getAllSelectedItemsByCategory,
  findSearchMatches,
  checkOnlyIncludeOrExclude,
} from './treeUtils';
import { checkStates } from '../common';

const createSampleTree = () => {
  const t = [
    {
      label: 'Root',
      children: [
        {
          label: 'Branch A',
          children: [
            {
              label: 'Leaf A1',
            },
            {
              label: 'Leaf A2',
            },
          ],
        },
        {
          label: 'Branch B',
          children: [
            {
              label: 'Leaf B1',
            },
            {
              label: 'Leaf B2',
            },
          ],
        },
      ],
    },
  ];

  addParentRelationship(t[0]);
  return t;
};

const addParentRelationship = (node, parent = null) => {
  if (parent) {
    node.parent = parent;
  }
  if (node.children) {
    node.children.forEach(n => addParentRelationship(n, node));
  }
};

describe('Advanced Targeting Utils', () => {
  test('setDescendantsExpandedState', () => {
    const tree = createSampleTree();
    // the whole tree should be expanded
    setDescendantsExpandedState(tree, true);

    expect(tree[0].expanded).toStrictEqual(true);
    expect(tree[0].children[0].expanded).toStrictEqual(true);

    // the whole tree should be collapsed
    setDescendantsExpandedState(tree, false);

    expect(tree[0].expanded).toStrictEqual(false);
    expect(tree[0].children[0].expanded).toStrictEqual(false);
  });

  test('updateAncestorsCheckState', () => {
    const tree = createSampleTree();
    const rootNode = tree[0];

    // navigate to a leaf node and check it
    const leaf = rootNode.children[0].children[0];
    expect(leaf.label).toBe('Leaf A1');

    // check leaf
    checkAllDescendants(leaf);
    expect(leaf.include).toBe(checkStates.checked);

    // update ancestors starting with parent of this leaf node
    updateAncestorsCheckState(leaf.parent);

    expect(leaf.parent.include).toBe(checkStates.indeterminate);
    expect(leaf.parent.parent.include).toBe(checkStates.indeterminate);

    // individually check all of leaf's siblings
    leaf.parent.children.forEach(item => checkAllDescendants(item));
    updateAncestorsCheckState(leaf.parent);

    // because all of leaf's siblings are now checked, leaf's parent is considered checked as well.
    // however leaf's grandparent is not checked because 'Branch B' and it's children are unchecked
    expect(leaf.parent.include).toBe(checkStates.checked);
    expect(leaf.parent.parent.include).toBe(checkStates.indeterminate); // this is the rootNode

    // check 'Branch B' and update to have the entire tree as included
    checkAllDescendants(rootNode.children[1]);
    updateAncestorsCheckState(rootNode);
    expect(rootNode.include).toBe(checkStates.checked);

    // go down to 'Leaf B1' and exclude it
    const leafB1 = rootNode.children[1].children[0];
    expect(leafB1.label).toBe('Leaf B1');
    checkAllDescendants(leafB1, 'exclude');
    updateAncestorsCheckState(leafB1.parent);

    // have done this now leaf B1's parent has children that are both included and exclude therefore making both indeterminate

    expect(leafB1.parent.include).toBe(checkStates.indeterminate);
    expect(leafB1.parent.exclude).toBe(checkStates.indeterminate);

    expect(rootNode.include).toBe(checkStates.indeterminate);
    expect(rootNode.exclude).toBe(checkStates.indeterminate);
  });

  test('checkAllDescendants', () => {
    const tree = createSampleTree();
    const rootNode = tree[0];

    const checkItems = (item, keyToCheck, expectedValue) => {
      expect(item[keyToCheck]).toBe(expectedValue);
      if (item.children) {
        item.children.forEach(i => checkItems(i, keyToCheck, expectedValue));
      }
    };

    // let's check all the "include" boxes of this tree
    checkAllDescendants(rootNode);

    checkItems(rootNode, 'include', checkStates.checked);
    checkItems(rootNode, 'exclude', checkStates.unchecked);

    // on this same tree, let's set exclude to be checked
    checkAllDescendants(rootNode, 'exclude');

    checkItems(rootNode, 'include', checkStates.unchecked);
    checkItems(rootNode, 'exclude', checkStates.checked);

    // uncheck exclude meaning everything is now unchecked
    checkAllDescendants(rootNode, 'exclude', false);
    checkItems(rootNode, 'include', checkStates.unchecked);
    checkItems(rootNode, 'exclude', checkStates.unchecked);
  });

  test('expandAnySelections', () => {
    const tree = createSampleTree();
    const rootNode = tree[0];

    const includeItemAndExpandTree = item => {
      checkAllDescendants(item);
      updateAncestorsCheckState(item.parent);
      expandAnySelections(tree);
    };

    // include Leaf A1 and propagate the result change throughout the tree
    const leafA1 = rootNode.children[0].children[0];

    includeItemAndExpandTree(leafA1);

    // rootNode, branch A are both expanded to show leaf A1
    expect(rootNode.expanded).toBe(true);
    expect(rootNode.children[0].expanded).toBe(true);
    expect(rootNode.children[1].expanded).toBe(false);

    // demonstrate group collapse behaviour if group is selected
    includeItemAndExpandTree(leafA1.parent);
    expect(leafA1.parent.expanded).toBe(false);
  });

  test('findSelectionsInTree', () => {
    const tree = createSampleTree();
    const rootNode = tree[0];

    // select leaf A1, branch B
    const leafA1 = rootNode.children[0].children[0];
    const branchB = rootNode.children[1];
    checkAllDescendants(leafA1);
    checkAllDescendants(branchB);
    const results = findSelectionsInTree(tree);

    expect(results).toStrictEqual([ leafA1, branchB ]);
  });

  test('getAllSelectedItemsByCategory', () => {
    const addCategory = (node, category) => {
      node.category = category;

      if (node.children) {
        node.children.forEach(c => addCategory(c, category));
      }
    };

    const categoryA = createSampleTree();
    addCategory(categoryA[0], 'A');
    const categoryB = createSampleTree();
    addCategory(categoryB[0], 'B');
    const newTree = categoryA.concat(categoryB);

    // select leaf A1 in category A and leaf B2 in category B
    const categoryALeafA1 = newTree[0].children[0].children[0];
    const categoryBLeafB2 = newTree[1].children[1].children[1];
    checkAllDescendants(categoryALeafA1);
    checkAllDescendants(categoryBLeafB2, 'exclude');
    const results = getAllSelectedItemsByCategory(newTree);
    expect(results).toStrictEqual({
      A: {
        include: [ categoryALeafA1 ],
        exclude: [],
      },
      B: {
        include: [],
        exclude: [ categoryBLeafB2 ],
      },
    });
  });

  test('findSearchMatches', () => {
    const tree = createSampleTree();

    expect(findSearchMatches(tree, 'leaf')).toBe(4);
    expect(findSearchMatches(tree, 'oo')).toBe(1);
    expect(findSearchMatches(tree, 'gadzooks')).toBe(0);
  });
});

describe('checkOnlyIncludeOrExclude', () => {
  let testTree;

  beforeEach(() => {
    // Setup a test tree structure before each test
    testTree = [
      {
        include: checkStates.checked,
        exclude: checkStates.checked,
        children: [
          {
            include: checkStates.checked,
            exclude: checkStates.checked,
            children: [
              {
                include: checkStates.checked,
                exclude: checkStates.checked,
              },
            ],
          },
        ],
      },
      {
        include: checkStates.checked,
        exclude: checkStates.checked,
        children: [],
      },
    ];
  });

  it('should uncheck all exclude states when include is specified', () => {
    checkOnlyIncludeOrExclude(testTree, 'include');

    // Check root level nodes
    testTree.forEach(node => {
      expect(node.exclude).toBe(checkStates.unchecked);

      // Check children if they exist
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
          expect(child.exclude).toBe(checkStates.unchecked);

          // Check grandchildren if they exist
          if (child.children && child.children.length > 0) {
            child.children.forEach(grandChild => {
              expect(grandChild.exclude).toBe(checkStates.unchecked);
            });
          }
        });
      }
    });
  });

  it('should uncheck all include states when exclude is specified', () => {
    checkOnlyIncludeOrExclude(testTree, 'exclude');

    // Check root level nodes
    testTree.forEach(node => {
      expect(node.include).toBe(checkStates.unchecked);

      // Check children if they exist
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
          expect(child.include).toBe(checkStates.unchecked);

          // Check grandchildren if they exist
          if (child.children && child.children.length > 0) {
            child.children.forEach(grandChild => {
              expect(grandChild.include).toBe(checkStates.unchecked);
            });
          }
        });
      }
    });
  });

  it('should handle empty tree array', () => {
    const emptyTree = [];
    expect(() => checkOnlyIncludeOrExclude(emptyTree, 'include')).not.toThrow();
  });

  it('should handle nodes without children', () => {
    const treeWithoutChildren = [
      {
        include: checkStates.checked,
        exclude: checkStates.checked,
      },
    ];

    checkOnlyIncludeOrExclude(treeWithoutChildren, 'include');
    expect(treeWithoutChildren[0].exclude).toBe(checkStates.unchecked);
  });
});
