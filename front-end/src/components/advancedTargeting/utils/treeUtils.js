import { checkStates, mapCheckToState } from '../common';
import { normalizedStrIncludes } from '../../../utils';

/**
 * Modify the tree's expanded state
 * @param {array} tree
 * @param {boolean} expandState
 */
export const setDescendantsExpandedState = (tree, expandState) => {
  tree.forEach(item => {
    item.expanded = expandState;

    if (item.children) {
      setDescendantsExpandedState(item.children, expandState);
    }
  });
};

/**
 *  Update ancestors' include/exclude state based on the state of its children
 * @param child - the node to start updating from
 */
export const updateAncestorsCheckState = (child) => {
  if (!child || !child.children) {
    return;
  }

  let numIncluded = 0;
  let numExcluded = 0;
  let hasIncludeIndeterminate = false;
  let hasExcludeIndeterminate = false;
  // check all kids here to see if they are all included or excluded
  child.children.forEach(c => {
    if (c.include === checkStates.checked) {
      numIncluded++;
    } else if (c.include === checkStates.indeterminate) {
      hasIncludeIndeterminate = true;
    }

    if (c.exclude === checkStates.checked) {
      numExcluded++;
    } else if (c.exclude === checkStates.indeterminate) {
      hasExcludeIndeterminate = true;
    }
  });

  const numChildren = child.children.length;

  const getCheckStateFromNumChosen = num => {
    switch (num) {
      case 0:
        return checkStates.unchecked;
          case numChildren:
            return checkStates.checked;
      default:
        return checkStates.indeterminate;
    }
  };

  child.include = hasIncludeIndeterminate ? checkStates.indeterminate : getCheckStateFromNumChosen(numIncluded);
  child.exclude = hasExcludeIndeterminate ? checkStates.indeterminate : getCheckStateFromNumChosen(numExcluded);

  updateAncestorsCheckState(child.parent);
};

/**
 * Set the value of this items include/exclude value as well as all of its descendants
 * @param child - the node to start from
 * @param keyToAlter - the key whose value we should change ('include', 'exclude')
 * @param value - whether we should check this item or not
 */
export const checkAllDescendants = (child, keyToAlter = 'include', value = true) => {
  if (value) {
    const opposingValue = keyToAlter === 'include' ? 'exclude' : 'include';
    child[opposingValue] = mapCheckToState(!value);
  }

  child[keyToAlter] = mapCheckToState(value);

  if (!child.children) {
    return;
  }

  child.children.forEach(c => checkAllDescendants(c, keyToAlter, value));
};

/**
 * Go through the tree and expand items that contain selections so that they are visible
 * @param tree
 */
export const expandAnySelections = tree => {
  tree.forEach(item => {
    // if this is a group and it is either all included or excluded it should be collapsed to clean up the view
    if (item.children &&
      (item.include === checkStates.checked || item.exclude === checkStates.checked)
    ) {
      item.expanded = false;
    } else {
      // otherwise there is an item nested at some depth that we should show
      item.expanded = (
        [ checkStates.checked, checkStates.indeterminate ].includes(item.include) ||
        [ checkStates.checked, checkStates.indeterminate ].includes(item.exclude)
      );
    }

    item.hidden = false;

    if (item.children) {
      expandAnySelections(item.children);
    }
  });
};

/**
 * Returns all elements in a tree that are either included or excluded
 * @param tree
 * @returns {[]} - all the matched elements in the tree
 */
export const findSelectionsInTree = tree => {
  let selections = [];
  tree.forEach(item => {
    if (
      item.include === checkStates.checked ||
      item.exclude === checkStates.checked
    ) {
      selections.push(item);
    } else if (item.children) {
      selections = selections.concat(findSelectionsInTree(item.children));
    }
  });
  return selections;
};

/**
 * Organizes the inclusions and exclusions in a tree by category
 * @param tree
 * @returns {{}} A selection object in the form of { [category]: { include: [...inclusions], exclude: [...exclusions] }...}
 */
export const getAllSelectedItemsByCategory = tree => {
  const selections = findSelectionsInTree(tree);

  const selectionsByCategory = {};
  // organize the selections into their categories
  selections.forEach(selection => {
    const { category } = selection;
    const chosenRelationship = selection.include === checkStates.checked ? 'include' : 'exclude';
    if (!selectionsByCategory[category]) {
      selectionsByCategory[category] = { include: [], exclude: [] };
    }
    selectionsByCategory[category][chosenRelationship].push(selection);
  });

  return selectionsByCategory;
};

/**
 * Finds the number of elements in a tree that match the search
 * @param tree
 * @param value - value to search for
 * @returns {number} - number of results
 */
export const findSearchMatches = (tree, value) => {
  let matches = 0;

  // a match that is the closest to their root in the tree for a given branch
  const rootmostMatches = [];
  const allMatches = [];

  const flagMatchedItems = flagTree => {
    flagTree.forEach(item => {
      item.matches = [ item.label, item.tag ]
        .some(searchableValue => searchableValue && normalizedStrIncludes(searchableValue, value));
      item.hidden = !item.matches;
      item.expanded = false;

      if (item.matches) {
        matches++;
        allMatches.push(item);

        if (!item.parent || !item.parent.matches) {
          rootmostMatches.push(item);
        }
      }

      if (item.children) {
        flagMatchedItems(item.children);
      }
    });
  };

  // go through the tree and update values with hidden vs not
  const unhideDescendants = item => {
    item.hidden = false;

    if (item.children) {
      item.children.forEach(child => unhideDescendants(child));
    }
  };

  const unhideAncestors = item => {
    if (!item.parent) {
      return;
    }

    item.parent.hidden = false;
    item.parent.expanded = true;

    unhideAncestors(item.parent);
  };

  // go through the entire tree, giving each item a matched status
  flagMatchedItems(tree);

  // with the matches identified closest to their respective roots, unhide their descendants allowing them to be fully interactive
  rootmostMatches.forEach(rootmostMatch => unhideDescendants(rootmostMatch));

  // now unhide the ancestors of these matches
  allMatches.forEach(item => unhideAncestors(item));

  return matches;
};

export const checkOnlyIncludeOrExclude = (tree, keyToAlter) => {
  const opposingValue = keyToAlter === 'include' ? 'exclude' : 'include';
  const checkOthers = (children) => {
    if (!Array.isArray(children) || children.length === 0) {
      return;
    }

    children.forEach(child => {
      child[opposingValue] = checkStates.unchecked;

      if (child.children) {
        checkOthers(child.children);
      }
    });
  };
  tree.forEach(i => {
    i[opposingValue] = checkStates.unchecked;
     checkOthers(i.children);
  });
};
