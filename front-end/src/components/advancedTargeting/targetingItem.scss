.targeting-item {
  width: 100%;
  display: flex;
  flex-direction: column;

  &__highlight {
    background-color: #ffba8e;
  }

  &__hover-item {
    position: relative;
    display: flex;
    background: none;
    border: none;
    padding: 0;
    outline: none;

    &--group {
      cursor: pointer;
    }

    &:hover,
    &:focus-within, {
      &::before {
        position: absolute;
        content: '';
        width: calc(100% * 2);
        height: 100%;
        opacity: 1;
        left: -100%;
        background: $canvas-gray-200;
      }
    }
  }

  &__content-container {
    padding: 1.5rem 0;
    display: flex;
    max-width: calc(100% - 14.4rem - 3.6rem);
  }

  &__content {
    display: flex;
    position: relative;
    max-width: 100%;

    &--child {
      margin-left: 3.8rem;
      max-width: calc(100% - 3.8rem);
    }
  }

  &__chevron {
    background: none;
    border: none;
    padding: 0;
    margin-right: 1.8rem;
    min-width: 1.8rem;
    min-height: 1.8rem;
    transition: transform 120ms;

    &--expanded {
      transform: rotate(90deg);
    }
  }

  &__item-information {
    display: flex;
    text-align: left;
    flex-direction: column;
    max-width: 100%;

    @include mq($from: tablet) {
      flex-direction: row;
    }
  }

  &__label,
  &__tag {
    font-family: $font-regular;
    font-size: 1.6rem;
    pointer-events: none;

    &--group {
      font-weight: bold;
    }
  }

  &__label {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  &__tag {
    margin-right: 1.2rem;
  }

  &__checkbox-container {
    display: flex;
    position: absolute;
    right: 1.8rem;
    margin-top: 1.5rem;

    @supports (overflow: overlay) {
      right: 3.6rem;
    }
  }

  &__checkbox {
    margin: 0 1.8rem;

    &:first-child {
      margin-right: 1.8rem + 2.4rem;
    }
  }
}
