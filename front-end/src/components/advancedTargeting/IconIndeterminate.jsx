import React from 'react';
import PropTypes from 'prop-types';

const IconIndeterminate = ({ colour }) => {
  const pathColour = colour === 'white' ? 'white' : '#757575';
  return (
    <svg width="15" height="2" viewBox="0 0 15 2" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M1.5 1H13.5" stroke={pathColour} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
};

IconIndeterminate.propTypes = {
  colour: PropTypes.oneOf([ 'white', 'gray' ]),
};

export default IconIndeterminate;
