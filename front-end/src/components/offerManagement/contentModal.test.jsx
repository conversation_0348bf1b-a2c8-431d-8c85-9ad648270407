import React from 'react';
import {
  render,
  fireEvent,
  waitFor,
  screen,
  within,
} from '@testing-library/react';
import ContentModal from './contentModal';
import { getContentItems, getContentTypes } from '../../api/content';
import { HUBBLE_CONTENT_SPACE, HUBBLE_CONTENT_TYPE_OFFERS } from './constants';

// Mock the API calls
jest.mock('../../api/content');

// Mock debounce to execute immediately in tests
jest.mock('lodash/debounce', () => jest.fn(fn => fn));

const mockContentTypes = {
  items: [
    { id: 'type1', name: HUBBLE_CONTENT_TYPE_OFFERS },
    { id: 'type2', name: 'Content Type 2' },
  ],
};

const mockContentItems = {
  total: 25, // More than the default limit of 10
  offset: 0,
  limit: 10,
  items: Array.from({ length: 30 }, (_, index) => ({
    id: `content${index + 1}`,
    content: { id: `Content ${index + 1}` },
    updated_at: `2023-01-${(index + 1).toString().padStart(2, '0')}T12:00:00Z`,
  })),
};

describe('ContentModal', () => {
  const defaultProps = {
    isModalVisible: true,
    setModalVisible: jest.fn(),
    isDisabled: false,
    handleContentSelection: jest.fn(),
    onClose: jest.fn(),
  };

  beforeEach(() => {
    getContentTypes.mockResolvedValue(mockContentTypes);
    getContentItems.mockResolvedValue(mockContentItems);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the modal with correct title', () => {
    render(<ContentModal {...defaultProps} />);
    expect(screen.getByText('Add New Content')).toBeInTheDocument();
  });

  it('loads and displays content types in dropdown', async() => {
    render(<ContentModal {...defaultProps} />);

    await waitFor(() => {
      expect(getContentTypes).toHaveBeenCalled();
      expect(screen.getByText(HUBBLE_CONTENT_TYPE_OFFERS)).toBeInTheDocument();
    });
  });

  it('loads content items when content type is selected', async() => {
    render(<ContentModal {...defaultProps} />);

    // Wait for content types to load
    await waitFor(() => {
      expect(getContentTypes).toHaveBeenCalled();
    });

    // Find and select the content type using the label
    const contentTypeSelect = screen.getByTestId('content-type-select');
    fireEvent.change(contentTypeSelect, { target: { value: 'type1' } });

    // Verify the content items are fetched
    await waitFor(() => {
      expect(getContentItems).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'type1',
          contentfulSpace: expect.any(String),
        })
      );
    });

    // Verify content is displayed
    await waitFor(() => {
      expect(screen.getByText('Content 1')).toBeInTheDocument();
      expect(screen.getByText('Content 2')).toBeInTheDocument();
    });
  });

  it('handles search input with debounce', async() => {
    render(<ContentModal {...defaultProps} />);

    // Wait for content types to load
    await waitFor(() => {
      expect(getContentTypes).toHaveBeenCalled();
    });

    // Find and select the content type
    const contentTypeSelect = screen.getByTestId('content-type-select');
    fireEvent.change(contentTypeSelect, { target: { value: 'type1' } });

    // Verify the content items are fetched
    await waitFor(() => {
      expect(getContentItems).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'type1',
          contentfulSpace: expect.any(String),
        })
      );
    });

    const searchInput = screen.getByLabelText('Search by content name');
    fireEvent.change(searchInput, { target: { value: 'test' } });

    await waitFor(() => {
      expect(getContentItems).toHaveBeenCalledWith(
        expect.objectContaining({
          content_id: 'test',
        })
      );
    });
  });

  it('handles pagination', async() => {
    render(<ContentModal {...defaultProps} />);

    // Wait for content types to load
    await waitFor(() => {
      expect(getContentTypes).toHaveBeenCalled();
    });

    // Find and select the content type
    const contentTypeSelect = screen.getByTestId('content-type-select');
    fireEvent.change(contentTypeSelect, { target: { value: 'type1' } });

    // Verify the content items are fetched
    await waitFor(() => {
      expect(getContentItems).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'type1',
          contentfulSpace: expect.any(String),
        })
      );
    });

    // Verify content is displayed
    await waitFor(() => {
      expect(screen.getByText('Content 1')).toBeInTheDocument();
      expect(screen.getByText('Content 2')).toBeInTheDocument();
    });

    const nextPageButton = screen.getByLabelText('Next');
    fireEvent.click(nextPageButton);

    await waitFor(() => {
      expect(getContentItems).toHaveBeenCalledWith(
        expect.objectContaining({
          offset: 10,
        })
      );
    });
  });

  it('handles radio button selection', async() => {
    render(<ContentModal {...defaultProps} />);

    // Wait for content types to load
    await waitFor(() => {
      expect(getContentTypes).toHaveBeenCalled();
    });

    // Find and select the content type
    const contentTypeSelect = screen.getByTestId('content-type-select');
    fireEvent.change(contentTypeSelect, { target: { value: 'type1' } });

    // Verify the content items are fetched
    await waitFor(() => {
      expect(getContentItems).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'type1',
          contentfulSpace: expect.any(String),
        })
      );
    });

    // Find the row containing Content 1
    const rows = screen.getAllByRole('row');
    const contentRow = Array.from(rows).find(row =>
      row.textContent.includes('Content 1') && row.textContent.includes('Contentful')
    );

    const radioButtonElement = within(contentRow).getByRole('radio');
    fireEvent.click(radioButtonElement);

    expect(radioButtonElement).toBeChecked();
  });

  it('calls onClose when Cancel button is clicked', async() => {
    render(<ContentModal {...defaultProps} />);

    // Wait for content types to load
    await waitFor(() => {
      expect(getContentTypes).toHaveBeenCalled();
    });

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);

    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('calls handleContentSelection when Select button is clicked', async() => {
    render(<ContentModal {...defaultProps} />);

    // Wait for content types to load
    await waitFor(() => {
      expect(getContentTypes).toHaveBeenCalled();
    });

    const selectButton = screen.getByRole('button', { name: /select/i });
    fireEvent.click(selectButton);

    expect(defaultProps.handleContentSelection).toHaveBeenCalled();
  });

  describe('Contentful link environment handling', () => {
    const envTests = [
      {
        url: 'http://localhost:3000/offers/create',
        expectedEnv: 'ist',
        desc: 'localhost',
      },
      {
        url: 'https://pigeon-admin-ist.apps.cloud.bns/offers/create',
        expectedEnv: 'ist',
        desc: 'ist environment',
      },
      {
        url: 'https://pigeon-admin-uat.apps.cloud.bns/offers/create',
        expectedEnv: 'uat',
        desc: 'uat environment',
      },
      {
        url: 'https://pigeon-admin.apps.cloud.bns/offers/create',
        expectedEnv: 'master',
        desc: 'production environment',
      },
    ];

    envTests.forEach(({ url, expectedEnv, desc }) => {
      it(`opens Contentful link with correct environment for ${desc}`, async() => {
        // Mock window.open
        const mockOpen = jest.fn();
        window.open = mockOpen;

        // Mock window.location
        const originalLocation = window.location;
        delete window.location;
        window.location = { href: url };

        render(<ContentModal {...defaultProps} />);

        // Wait for content types to load
        await waitFor(() => {
          expect(getContentTypes).toHaveBeenCalled();
        });

        // Find and select content type
        const contentTypeSelect = screen.getByTestId('content-type-select');
        fireEvent.change(contentTypeSelect, {
          target: { value: 'type1' },
        });

        // Wait for content items to be fetched
        await waitFor(() => {
          expect(getContentItems).toHaveBeenCalledWith(
            expect.objectContaining({
              type: 'type1',
              contentfulSpace: expect.any(String),
            })
          );
        });

        // Find the row containing Content 1
        const rows = screen.getAllByRole('row');
        const contentRow = Array.from(rows).find(row =>
          row.textContent.includes('Content 1') && row.textContent.includes('Contentful')
        );

        // Find and click the Contentful button within the row
        const contentfulButton = within(contentRow).getByRole('button', {
          name: /contentful/i,
        });
        fireEvent.click(contentfulButton);

        // Verify the correct Contentful URL is opened with the expected environment
        expect(mockOpen).toHaveBeenCalledWith(
          `https://app.contentful.com/spaces/${HUBBLE_CONTENT_SPACE}/environments/${expectedEnv}/entries/content1`,
          '_blank'
        );

        // Cleanup
        window.location = originalLocation;
      });
    });
  });
});
