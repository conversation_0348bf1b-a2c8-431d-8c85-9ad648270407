import React, { useRef, useState, useCallback } from 'react';
import qs from 'qs';
import PropTypes from 'prop-types';

import Card from 'canvas-core-react/lib/Card';
import DatePicker from 'canvas-core-react/lib/DatePicker';
import Filter from 'canvas-core-react/lib/Filter';
import TextButton from 'canvas-core-react/lib/TextButton';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import LoadingIndicator from 'canvas-core-react/lib/LoadingIndicator';
import Search from 'canvas-core-react/lib/Search';
import IconFilter from 'canvas-core-react/lib/IconFilter';
import IconClose from 'canvas-core-react/lib/IconClose';
import IconDownload from 'canvas-core-react/lib/IconDownload';

import { SHORT_MONTH_FORMAT } from '../../constants';
import { createIdFromName, debounce, removeFalsyKeys } from '../../utils';

import classnames from 'classnames';
import moment from 'moment';
import { omit } from 'lodash';
import { useHistory } from 'react-router-dom';
import TextField from 'canvas-core-react/lib/TextField';

const FilterLoader = () => {
  return (
    <div className="admin-filter__loader">
      <LoadingIndicator color="grey" />
    </div>
  );
};

const OfferManagementFilter = ({
  title,
  filters,
  includeDateTime,
  className,
  onFiltersChange,
  filterFields,
  handleExport,
}) => {
  const history = useHistory();

  const startDatePickerRef = useRef();
  const endDatePickerRef = useRef();
  const [ filtersCollapsed, setFiltersCollapsed ] = useState(true);

  const handleChangingFilters = (newValues) => {
    const queryParams = removeFalsyKeys(
      omit(newValues, [
        'limit',
        ...(newValues.pageNumber === 1 ? [ 'pageNumber' ] : []),
      ]),
    );
    history.push({
      search: qs.stringify(queryParams, { addQueryPrefix: true }),
    });
    onFiltersChange(newValues);
  };

  const handleSearch = (search) => {
    handleChangingFilters({ ...filters, search, pageNumber: 1 });
  };

  const debouncedHandleSearch = useCallback(debounce(handleSearch, 500), []);

  const renderedFilters = filterFields.map(
    ({ label, key, options, defaultOptionLabel, isLoading }) => {
      const filterKey = key || createIdFromName(label.toLowerCase());
      return (
        <div key={filterKey} className="admin-filter__item">
          <Filter
            name={`admin-filter-${label}`}
            id={`admin-filter-${key}`}
            label={label}
            isLabelVisible
            onChange={(e) =>
              handleChangingFilters({
                ...filters,
                [filterKey]: e.target.value,
              })
            }
            value={filters[filterKey] || ''}
            tooltip={isLoading ? <FilterLoader /> : false}
          >
            <option key={`${filterKey}-all`} value={''}>
              { defaultOptionLabel || 'All' }
            </option>
            { options.map(({ value, label: optionLabel, disabled }) => (
              <option
                key={`${filterKey}-${value}`}
                value={value}
                disabled={disabled}
              >
                { optionLabel }
              </option>
            )) }
          </Filter>
        </div>
      );
    },
  );

  const onDateTimeChange = (value, forField) => {
    // handles cancel on date picker, if value is undefined, we want to clear the filter
    if (value === undefined) {
      handleChangingFilters({
        ...filters,
        [forField]: undefined,
      });
      return;
    }
    // Only validate moment format for non-undefined values
    if (!moment(value, SHORT_MONTH_FORMAT, true).isValid()) return;
    const date = moment(value).toISOString();
    handleChangingFilters({
      ...filters,
      [forField]: date,
    });
  };

  const renderDateTimeFilters = () => (
    <>
      <DatePicker
        className="admin-filter__item"
        id="datepicker-start-date"
        locale="en-CA"
        inputDateFormat={SHORT_MONTH_FORMAT}
        overrides={{ 'en-CA': { startDateLabel: 'Start Date' } }}
        onChange={(value) => onDateTimeChange(value, 'start_date')}
        selected={filters.start_date ? new Date(filters.start_date) : null}
        onDatePickerClear={() => onDateTimeChange(undefined, 'start_date')}
        datePickerComponentRef={startDatePickerRef}
      />
      <DatePicker
        className="admin-filter__item"
        id="datepicker-end-date"
        locale="en-CA"
        inputDateFormat={SHORT_MONTH_FORMAT}
        overrides={{ 'en-CA': { startDateLabel: 'End Date' } }}
        errorStrings={{
          label: 'error',
          start: {
            range: 'The end date selection is outside of the allowed range',
            disallowedDate: 'The end date selection is invalid',
            invalidRange: '',
          },
          end: {
            range: '',
            disallowedDate: '',
            invalidRange: '',
          },
        }}
        onChange={(value) => onDateTimeChange(value, 'end_date')}
        onDatePickerClear={() => onDateTimeChange(undefined, 'end_date')}
        datePickerComponentRef={endDatePickerRef}
        selected={filters.end_date ? new Date(filters.end_date) : null}
      />
    </>
  );

  const renderOfferIdFilter = () => (
    <>
    <div
      key={'offer_id'}
      className="admin-filter__item">
        <TextField
          name='admin-filter-OfferId'
          id='admin-filter-offer_id'
          label='Offer ID'
          placeholder="Enter Offer ID"
          onChange={(e) => handleChangingFilters({
            ...filters,
            offer_id: e.target.value,
          })}
          value={filters['offer_id'] || ''}
        />
    </div>
    </>
  );

  const renderClearBtn = () => {
    return (
      <div className="admin-filter__item-inline">
        <TextButton
          className="admin-filter__item-clear-inline"
          onClick={() => {
            startDatePickerRef.current.clearDateField();
            endDatePickerRef.current.clearDateField();
            handleChangingFilters({});
          }}
        >
          Clear all
        </TextButton>
      </div>
    );
  };

  return (
    <>
      <div className="admin-list__filter-accordion">
        <div className="admin-list__search">
          <Search
            id="name"
            placeholder="Search"
            onChange={(e) => {
              debouncedHandleSearch(e.target.value);
            }}
            showLabel={false}
            value={filters?.search || ''}
            clearButtonLabel="Clear search"
            searchButtonLabel="Search"
            label="Search"
          />
        </div>
        <div className="admin-list__action-button-container">
          <TextButton Icon={IconDownload} onClick={handleExport}>
            Export
          </TextButton>
          <TextButton
            Icon={filtersCollapsed ? IconFilter : IconClose}
            onClick={() => setFiltersCollapsed(!filtersCollapsed)}
          >
            { filtersCollapsed ? 'Filters' : 'Cancel' }
          </TextButton>
        </div>
      </div>

      { !filtersCollapsed && (
        <Card className={classnames('admin-filter', className)}>
          <div className="admin-filter__header-container">
            <TextSubtitle component='legend' className="admin-filter__header-title">{ title }</TextSubtitle>
            { renderClearBtn() }
          </div>

          <div className="admin-filter__filter-container">
              { renderOfferIdFilter() }
              { includeDateTime && renderDateTimeFilters() }
              { renderedFilters }
          </div>
        </Card>
      ) }
    </>
  );
};

OfferManagementFilter.defaultProps = {
  title: 'Filters',
  filters: {},
  includeDateTime: false,
  className: 'admin-list__filter-options',
  onFiltersChange: () => {},
  handleExport: () => {},
  filterFields: [],
};

OfferManagementFilter.propTypes = {
  title: PropTypes.string,
  filters: PropTypes.object,
  includeDateTime: PropTypes.bool,
  className: PropTypes.string,
  filterFields: PropTypes.array,
  onFiltersChange: PropTypes.func,
  handleExport: PropTypes.func,
};

export default OfferManagementFilter;
