// MultiProvinceStateSelection.test.js
import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { renderPage } from '../../utils/testing-library-utils';
import MultiProvinceStateSelection from './multiProvinceStateSelection';

describe('MultiProvinceStateSelection', () => {
  const mockSelectOptions = [
    { value: 'ON', label: 'Ontario', checked: true },
    { value: 'BC', label: 'British Columbia', checked: false },
    { value: 'QC', label: 'Quebec', checked: true },
  ];

  const mockProps = {
    className: 'test-class',
    label: 'Select Provinces',
    name: 'province-select',
    placeholder: 'Choose provinces',
    onChange: jest.fn(),
    onClickSelectAll: jest.fn(),
    onClickClearAll: jest.fn(),
    onClickTagDeletion: jest.fn(),
    selectOptions: mockSelectOptions,
    disabled: false,
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders component with label and placeholder', async() => {
    await renderPage(<MultiProvinceStateSelection {...mockProps} />);
    expect(screen.getByLabelText('Select Provinces')).toBeInTheDocument();
    expect(screen.getByText('2 of 3 items selected')).toBeInTheDocument();
  });

  test('renders selected tags', async() => {
    const { container } = await renderPage(<MultiProvinceStateSelection {...mockProps} />);
    const tagSpans = container.querySelectorAll('span.tag__value');

    const labels = Array.from(tagSpans).map((span) => span.textContent);
    expect(labels).toContain('Ontario');
    expect(labels).toContain('Quebec');
  });

  test('calls onClickSelectAll and renders all tags after clicking "Select all"', async() => {
    const updatedProps = {
      ...mockProps,
      selectOptions: mockProps.selectOptions.map((option) => ({
        ...option,
        checked: true,
      })),
    };

    const { container, rerender } = await renderPage(
      <MultiProvinceStateSelection {...mockProps} />,
    );

    const selectAllButton = screen.getByTestId('offers-select-all-province-button');
    fireEvent.click(selectAllButton);

    expect(mockProps.onClickSelectAll).toHaveBeenCalled();

    // Simulate prop update after clearing
    await rerender(<MultiProvinceStateSelection {...updatedProps} />);

    // Wait for all tags to appear
    await waitFor(() => {
      const tagSpans = container.querySelectorAll('span.tag__value');
      const labels = Array.from(tagSpans).map((span) => span.textContent);
      expect(labels).toContain('Ontario');
      expect(labels).toContain('British Columbia');
      expect(labels).toContain('Quebec');

      expect(screen.getByText('3 of 3 items selected')).toBeInTheDocument();
    });
  });

  test('calls onClickClearAll and shows placeholder with no tags after clearing', async() => {
    const updatedProps = {
      ...mockProps,
      selectOptions: mockProps.selectOptions.map((option) => ({
        ...option,
        checked: false,
      })),
    };

    const { container, rerender } = await renderPage(
      <MultiProvinceStateSelection {...mockProps} />,
    );

    const clearAllButton = screen.getByTestId('offers-clear-all-province-button');
    fireEvent.click(clearAllButton);

    expect(mockProps.onClickClearAll).toHaveBeenCalled();

    // Simulate prop update after clearing
    await rerender(<MultiProvinceStateSelection {...updatedProps} />);

    await waitFor(() => {
      const tagSpans = container.querySelectorAll('span.tag__value');
      const labels = Array.from(tagSpans).map((span) => span.textContent);

      // Ensure no tags are rendered
      expect(labels).toHaveLength(0);

      // Ensure placeholder is visible
      expect(screen.getByText('Choose provinces')).toBeInTheDocument();
    });
  });

  test('calls onClickTagDeletion and updates visible tags after deletion', async() => {
    const { container, rerender } = await renderPage(
      <MultiProvinceStateSelection {...mockProps} />,
    );

    // Initially, Ontario and Quebec tags should be visible
    let tagSpans = container.querySelectorAll('span.tag__value');
    let labels = Array.from(tagSpans).map((span) => span.textContent);
    expect(labels).toContain('Ontario');
    expect(labels).toContain('Quebec');
    expect(labels).toHaveLength(2);

    // Find the tag element that contains 'Ontario' and click its delete button
    const tagElements = container.querySelectorAll('.tag');
    const ontarioTag = Array.from(tagElements).find((tag) => tag.textContent.includes('Ontario'));
    const deleteButton = ontarioTag?.querySelector('button');
    fireEvent.click(deleteButton);

    expect(mockProps.onClickTagDeletion).toHaveBeenCalled();

    // Simulate updated props: only Quebec remains checked
    const updatedProps = {
      ...mockProps,
      selectOptions: [
        { value: 'ON', label: 'Ontario', checked: false },
        { value: 'BC', label: 'British Columbia', checked: false },
        { value: 'QC', label: 'Quebec', checked: true },
      ],
    };

    await rerender(<MultiProvinceStateSelection {...updatedProps} />);

    // Wait for the UI to reflect the updated state
    await waitFor(() => {
      const updatedTagSpans = container.querySelectorAll('span.tag__value');
      const updatedLabels = Array.from(updatedTagSpans).map((span) => span.textContent);
      expect(updatedLabels).toContain('Quebec');
      expect(updatedLabels).not.toContain('Ontario');
      expect(updatedLabels).toHaveLength(1);
    });
  });
});
