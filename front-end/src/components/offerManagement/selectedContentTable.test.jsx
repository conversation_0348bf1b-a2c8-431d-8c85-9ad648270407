import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react';
import SelectedContentTable from './selectedContentTable';
import { getContentById } from '../../api/content';

// Mock the content API
jest.mock('../../api/content');

// Mock window.open
const mockOpen = jest.fn();
window.open = mockOpen;

const mockContentData = {
  id: 'test-content-id',
  content: {
    id: 'test-name',
  },
  type: 'cmOffer',
};

describe('SelectedContentTable', () => {
  const defaultProps = {
    contentfulId: 'test-id',
    contentfulType: 'cmOffer',
    setModalVisible: jest.fn(),
    deleteContent: jest.fn(),
    formDisabled: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    getContentById.mockResolvedValue(mockContentData);
  });

  it('renders correctly with content data', async() => {
    render(<SelectedContentTable {...defaultProps} />);

    // Wait for content to load
    await waitFor(() => {
      expect(getContentById).toHaveBeenCalledWith(
        'cmOffer',
        'test-id',
        expect.any(String)
      );
    });

    // Check if table columns are rendered
    expect(screen.getByText('Contentful Name')).toBeInTheDocument();
    expect(screen.getByText('Content Type')).toBeInTheDocument();
    expect(screen.getByText('Preview Link')).toBeInTheDocument();
    expect(screen.getByText('Action')).toBeInTheDocument();
  });

  it('opens preview link in new tab when clicked', async() => {
    render(<SelectedContentTable {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('Preview')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Preview'));
    expect(window.open).toHaveBeenCalledWith(
      expect.stringContaining('https://app.contentful.com/spaces/'),
      '_blank'
    );
  });

  it('shows action menu with Edit and Delete options', async() => {
    render(<SelectedContentTable {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /menu/i })).toBeInTheDocument();
    });

    // Open the menu
    fireEvent.click(screen.getByRole('button', { name: /menu/i }));

    // Check if menu items are present
    expect(screen.getByText('Edit')).toBeInTheDocument();
    expect(screen.getByText('Delete')).toBeInTheDocument();
  });

  it('calls appropriate functions when menu items are clicked', async() => {
    render(<SelectedContentTable {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /menu/i })).toBeInTheDocument();
    });

    // Open the menu
    fireEvent.click(screen.getByRole('button', { name: /menu/i }));

    // Click Edit
    fireEvent.click(screen.getByText('Edit'));
    expect(defaultProps.setModalVisible).toHaveBeenCalled();

    // Click Delete
    fireEvent.click(screen.getByText('Delete'));
    expect(defaultProps.deleteContent).toHaveBeenCalled();
  });

  it('does not render action column when form is disabled', async() => {
    render(<SelectedContentTable {...defaultProps} formDisabled={true} />);

    await waitFor(() => {
      expect(getContentById).toHaveBeenCalled();
    });

    expect(screen.queryByText('Action')).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /menu/i })).not.toBeInTheDocument();
  });

  it('does not render table when contentfulId is not provided', () => {
    render(<SelectedContentTable {...defaultProps} contentfulId={null} />);
    expect(screen.queryByText('Contentful Name')).not.toBeInTheDocument();
  });
});
