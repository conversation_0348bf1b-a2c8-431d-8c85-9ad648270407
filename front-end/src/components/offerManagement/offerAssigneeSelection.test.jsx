import React from 'react';
import { screen, fireEvent, waitFor, within } from '@testing-library/react';
import { renderPage } from '../../utils/testing-library-utils';
import OfferAssigneeSelection from './offerAssigneeSelection';
import { INVALID_EMPTY_ASSIGNEE_LIST_ERROR_MSG } from './constants';

describe('OfferAsignneeSelection', () => {
  const mockPotentialAssignees = [
    { sid: 'user1', full_name: '<PERSON>' },
    { sid: 'user2', full_name: '<PERSON>' },
    { sid: 'user3', full_name: '<PERSON>' },
  ];

  const mockAssignees = [ 'user1' ];

  const mockProps = {
    isOpen: true,
    onCancel: jest.fn(),
    onSubmit: jest.fn(),
    handleAssigneeChange: jest.fn(),
    potentialAssignees: mockPotentialAssignees,
    assignees: mockAssignees,
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should render the component correctly with initial assignee', async() => {
    await renderPage(<OfferAssigneeSelection {...mockProps} />);

    const modalHeader = await screen.findByText('This offer needs to be assigned');
    const initialOption = await screen.findByText('John Doe');
    const addButton = await screen.findByText('Add an assignee');
    expect(modalHeader).toBeInTheDocument();
    expect(initialOption).toBeInTheDocument();
    expect(addButton).toBeInTheDocument();
  });

  test('should handle adding a new assignee selector', async() => {
    await renderPage(<OfferAssigneeSelection {...mockProps} />);

    const addButton = await screen.findByText('Add an assignee');
    fireEvent.click(addButton);

    const selectors = await screen.findAllByTestId('approver-selector');
    expect(selectors).toHaveLength(3);
  });

  test('should handle removing an assignee selector', async() => {
    await renderPage(<OfferAssigneeSelection {...mockProps} />);

    const addButton = await screen.findByTestId('add-assignee-button');
    fireEvent.click(addButton);

    const removeButtons = await screen.findAllByText('Remove');
    expect(removeButtons.length).toBeGreaterThan(0);
    fireEvent.click(removeButtons[0]);

    await waitFor(() => {
      const selectors = screen.getAllByTestId('approver-selector');
      expect(selectors).toHaveLength(2);
    });
  });

  test('should handle selecting an assignee', async() => {
    await renderPage(<OfferAssigneeSelection {...mockProps} />);
    await screen.findByText('This offer needs to be assigned');
    const selectors = await screen.findAllByTestId('approver-selector');
    const firstSelector = selectors[0];
    expect(within(firstSelector).getByText('John Doe')).toBeInTheDocument();

    fireEvent.change(firstSelector, { target: { value: 'user2' } });

    expect(mockProps.handleAssigneeChange).toHaveBeenCalledWith([ 'user2' ]);
  });

  test('should show error when submitting with no assignees selected', async() => {
    await renderPage(<OfferAssigneeSelection {...mockProps} assignees={[]} />);
    // Wait for the submit button to be available
    const submitButton = await screen.findByText('Submit');
    fireEvent.click(submitButton);
    await waitFor(() => {
      expect(screen.getByText(INVALID_EMPTY_ASSIGNEE_LIST_ERROR_MSG)).toBeInTheDocument();
    });
    expect(mockProps.onSubmit).not.toHaveBeenCalled();
  });

  test('should call onSubmit with selected assignees when submitting', async() => {
    await renderPage(<OfferAssigneeSelection {...mockProps} />);
    const submitButton = await screen.findByText('Submit');
    fireEvent.click(submitButton);

    expect(mockProps.onSubmit).toHaveBeenCalledWith([ 'user1' ]);
  });

  test('should call onCancel when cancel button is clicked', async() => {
    await renderPage(<OfferAssigneeSelection {...mockProps} />);
    const cancelButton = await screen.findByText('Cancel');
    fireEvent.click(cancelButton);
    expect(mockProps.onCancel).toHaveBeenCalled();
  });

  test('should filter out already selected assignees from dropdown options', async() => {
    const propsWithMultipleAssignees = {
      ...mockProps,
      assignees: [ 'user1', 'user2' ],
    };

    await renderPage(<OfferAssigneeSelection {...propsWithMultipleAssignees} />);

    // Wait for the "Add an assignee" button to be available
    const addButton = await screen.findByTestId('add-assignee-button');
    fireEvent.click(addButton);
    fireEvent.click(addButton);

    // Wait for all selectors to be available
    await waitFor(() => {
      // 3 assignees from the original list and 2 added assignees
      expect(screen.getAllByTestId('approver-selector')).toHaveLength(5);
    });

    const selectors = screen.getAllByTestId('approver-selector');
    const thirdSelector = selectors[2];

    // Check that user3 option is available but user1 and user2 are not
    expect(thirdSelector).toBeInTheDocument();
    expect(within(thirdSelector).queryByText('John Doe')).not.toBeInTheDocument();
    expect(within(thirdSelector).queryByText('Jane Smith')).not.toBeInTheDocument();
    expect(within(thirdSelector).getByText('Bob Johnson')).toBeInTheDocument();
  });

  test('should reset state when cancel is called', async() => {
    await renderPage(<OfferAssigneeSelection {...mockProps} />);

    // Wait for the "Add an assignee" button to be available
    const addButton = await screen.findByTestId('add-assignee-button');
    fireEvent.click(addButton);

    const cancelButton = await screen.findByText('Cancel');
    fireEvent.click(cancelButton);

    const newProps = { ...mockProps, isOpen: true };
    renderPage(<OfferAssigneeSelection {...newProps} />);

    await waitFor(() => {
      const selectors = screen.getAllByTestId('approver-selector');
      expect(selectors).toHaveLength(1);
    });
  });
});
