.multi-province-selection {
  &__province-fields {
    display: flex;
    margin-bottom: 1rem;
    flex-direction: column;
    gap: 2rem;
    height: auto;

    @include mq($from: desktop) {
      flex-direction: row;
      gap: 3rem;
      margin-bottom: 2rem;
    }

    #multi-province-selection {
      width: 40rem;
      height: 2rem;
    }

    .province-utility-button {
      width: fit-content;
      display: flex;
    }
  }

  &__province-tags {
    display: flex;
    flex-wrap: wrap;
  }
}
