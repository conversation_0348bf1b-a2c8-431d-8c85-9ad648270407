import React, { useEffect, useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import debounce from 'lodash/debounce';

import Card from 'canvas-core-react/lib/Card';
import IconExternal from 'canvas-core-react/lib/IconExternal';
import Modal from 'canvas-core-react/lib/internal/Modal';
import Search from 'canvas-core-react/lib/Search';
import Table from 'canvas-core-react/lib/Table';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import TextButton from 'canvas-core-react/lib/TextButton';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import RadioButton from 'canvas-core-react/lib/RadioButton';
import Selector from 'canvas-core-react/lib/Selector';
import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';

import { getContentItems, getContentTypes } from '../../api/content';
import { HUBBLE_CONTENT_SPACE, HUBBLE_CONTENT_TYPE_OFFERS, getContentfulEnvironment } from './constants';
import moment from 'moment';

function ContentModal({
  isModalVisible,
  setModalVisible,
  isDisabled,
  handleContentSelection,
  onClose,
}) {
  const [ contentItems, setContentItems ] = useState({
    total: 0,
    offset: 0,
    limit: 5, // Default limit
    items: [],
    contentfulSpace: HUBBLE_CONTENT_SPACE,
    searchTerm: '',
    sort: '-sys.updatedAt', // Default sort by last updated descending
  });
  const [ contentTypes, setContentTypes ] = useState([]);
  const [ activeContentType, setActiveContentType ] = useState('');
  const [ activeContentId, setActiveContentId ] = useState(null);

  const fetchContentItems = async(params = {}) => {
    /* istanbul ignore else */
    if (activeContentType) {
      const data = await getContentItems({
        contentfulSpace: HUBBLE_CONTENT_SPACE,
        type: activeContentType,
        content_id: contentItems.searchTerm || undefined,
        offset: contentItems.offset,
        limit: contentItems.limit,
        sort: contentItems.sort,
        ...params,
      });
      setContentItems(prevState => ({
        ...prevState,
        ...data,
      }));
    }
  };

  useEffect(() => {
    const fetchContentTypes = async() => {
      const contentTypesData = await getContentTypes(HUBBLE_CONTENT_SPACE);
      setContentTypes(contentTypesData.items.filter(item => item.name === HUBBLE_CONTENT_TYPE_OFFERS));
    };
    fetchContentTypes();
  }, []);

  useEffect(() => {
    if (activeContentType) {
      fetchContentItems();
    }
  }, [ activeContentType, contentItems.offset, contentItems.searchTerm ]);

  const debouncedFetchContentItems = useCallback(
    debounce(searchTerm => {
      setContentItems(prevState => ({
        ...prevState,
        searchTerm,
        offset: 0,
      }));
    }, 500),
    []
  );

  const handleSearch = e => {
    const term = e.target.value;
    /* istanbul ignore next */
    if (term.length >= 3 || term.length === 0) {
      debouncedFetchContentItems(term);
    }
  };

  const handleClearSearch = () => {
    setContentItems(prevState => ({
      ...prevState,
      searchTerm: '',
      offset: 0,
    }));
  };

  const handlePageClick = pageNumber => {
    const newOffset = (pageNumber - 1) * contentItems.limit;
    setContentItems(prevState => ({
      ...prevState,
      offset: newOffset,
    }));
  };

  const handleSort = (column, descending) => {
    const newSort = `${descending ? '-' : ''}${column}`;
    setContentItems(prevState => ({
      ...prevState,
      sort: newSort,
      offset: 0,
    }));

    fetchContentItems({
      sort: newSort,
      offset: 0,
    });
  };

  const sortableColumnProperties = columnKey => ({
    sortable: true,
    overrideSortBehaviour: (row, direction) =>
      handleSort(columnKey, direction === 1),
  });

  return (
    <div className="content-modal">
      <Modal
        className="content-modal__modal"
        headline="Add New Content"
        isModalVisible={isModalVisible}
        setModalVisible={setModalVisible}
      >
        <div className="content-modal__input-container">
          <div className="content-modal__dropdown-container">
            <div className="content-modal__dropdown">
              <Selector
                data-testid="content-type-select"
                name="content_type"
                label="Contentful Type"
                placeholder="Select Contentful model type&hellip;"
                onChange={e => {
                  setActiveContentId(null);
                  setActiveContentType(e.target.value);
                  setContentItems(prevState => ({
                    ...prevState,
                    offset: 0,
                    searchTerm: '',
                  }));
                }}
                value={activeContentType}
              >
                { contentTypes &&
                  contentTypes.map((option, index) => {
                    return (
                      <option key={index} id={index} value={option.id}>
                        { option.name }
                      </option>
                    );
                  }) }
              </Selector>
            </div>
          </div>
        </div>

        <Card className="content-modal__content-card">
          <div className="content-modal__content-search">
            <Search
              className="content-modal__content-search-input"
              id="content-search"
              showLabel={false}
              label="Search by content name"
              searchButtonLabel="Search"
              clearButtonLabel="Clear search"
              value={contentItems.searchTerm}
              onChange={handleSearch}
              onClear={handleClearSearch}
            />
          </div>
          <div className="content-modal__content-items" name="content_id">
            <Table
              id="content-search-table"
              title=""
              className="content-modal__content-table"
              resetSortOnDataChange={false}
              columns={[
                {
                  name: 'Select',
                  selector: '',
                  grow: 1,
                  cellFormatter: row => (
                    <RadioButton
                      id={row.id}
                      onChange={() => setActiveContentId(row.id)}
                      checked={row.id === activeContentId}
                      data-testid={`content-radio-button-${row.id}`}
                    />
                  ),
                },
                {
                  name: 'Content Name',
                  selector: 'id',
                  grow: 5,
                  cellFormatter: data => (
                    <TextCaption component="p">{ data.content.id }</TextCaption>
                  ),
                  ...sortableColumnProperties('fields.id'),
                },
                {
                  name: 'Last Updated',
                  selector: 'updated_at',
                  grow: 2,
                  cellFormatter: row => (
                    <TextCaption component="p">
                      { moment(row['updated_at']).format('lll') }
                    </TextCaption>
                  ),
                  ...sortableColumnProperties('sys.updatedAt'),
                },
                {
                  name: 'Contentful',
                  selector: '',
                  grow: 2,
                  cellFormatter: row => (
                    <TextButton
                      type="button"
                      Icon={IconExternal}
                      iconPosition="right"
                      onClick={() => {
                        const url = window.location.href;
                        const env = getContentfulEnvironment(url);
                        window.open(
                          `https://app.contentful.com/spaces/${HUBBLE_CONTENT_SPACE}/environments/${env}/entries/${row.id}`,
                          '_blank'
                        );
                      }}
                    >
                      Contentful
                    </TextButton>
                  ),
                },
              ]}
              defaultSortOrder="asc"
              data={contentItems.items}
              striped={true}
            />
          </div>
          { contentItems.items.length === 0 && (
            <div className="content-modal__no-results">No results found</div>
          ) }
          { contentItems.total > contentItems.limit && (
            <DesktopPagination
              id="pagination"
              totalResultCount={contentItems.total}
              onChange={handlePageClick}
              firstButtonLabel="First"
              prevButtonLabel="Previous"
              nextButtonLabel="Next"
              lastButtonLabel="Last"
              visiblePages={3}
              navigationLabel="Pagination Navigation"
              pageSize={contentItems.limit}
              currentPage={
                contentItems.items
                  ? contentItems.offset / contentItems.limit + 1
                  : 1
              }
              containerType="border-top"
            />
          ) }
        </Card>

        <div className="content-modal__action-bar">
          <SecondaryButton type="button" onClick={onClose}>
            Cancel
          </SecondaryButton>
          <PrimaryButton
            className="content-modal__action-button"
            disabled={isDisabled}
            onClick={() => {
              setModalVisible(false);
              handleContentSelection({ activeContentId, activeContentType });
            }}
            type="button"
            data-testid="content-modal-select-button"
          >
            Select
          </PrimaryButton>
        </div>
      </Modal>
    </div>
  );
}

ContentModal.propTypes = {
  isModalVisible: PropTypes.bool.isRequired,
  setModalVisible: PropTypes.func.isRequired,
  isDisabled: PropTypes.bool,
  handleContentSelection: PropTypes.func,
  onClose: PropTypes.func,
};

export default ContentModal;
