export const LANGUAGES_OPTIONS = [
  { id: 'ENGLISH', label: 'English', value: 'ENGLISH', name: 'English' },
  { id: 'FRENCH', label: 'French', value: 'FRENCH', name: 'French' },
];

export const CATEGORY_OPTIONS = [
  { id: 'Offers', value: 'OFFERS', name: 'Offers', label: 'Offers' },
  { id: 'Benefits', value: 'BENEFITS', name: 'Benefits', label: 'Benefits' },
  { id: 'Perks', value: 'PERKS', name: 'Perks', label: 'Perks' },
];

export const PRIMARY_RELATIONSHIP_RADIOBOXES = [
  {
    label: 'Product Relationship',
    name: 'product_relationship',
    options: [
      { label: 'Primary', value: 'PRIMARY', name: 'product_relationship' },
      { label: 'Everyone', value: 'EVERYONE', name: 'product_relationship' },
    ],
  },
];

export const OFFERS_FIELDS = {
  title: '',
  category: '',
  priority: 0,
  start_date: '',
  expiry_date: '',
  target_language: [ 'ENGLISH', 'FRENCH' ],
  product_relationship: '',
  products: {
    any_of: [],
  },
};

export const ACTION = {
  REJECT: 'REJECT',
  APPROVE: 'APPROVE',
  PUBLISH: 'PUBLISH',
};

export const INVALID_DATE_COMPARISON_ERROR_MSG =
  'You must enter a publication Expiry date later than the Start date.';

export const HUBBLE_CONTENT_SPACE = 'iy4ddvu7jd2s';

export const HUBBLE_CONTENT_TYPE_OFFERS = 'OffersHubOffer';

export const getContentfulEnvironment = (url) => {
  if (url.includes('localhost') || url.includes('-ist.')) {
    return 'ist';
  }
  if (url.includes('-uat.')) {
    return 'uat';
  }
  return 'master';
};

export const OFFERS_STATUS = {
  DRAFT: 'DRAFT',
  SUBMITTED: 'SUBMITTED',
  REVIEWED: 'REVIEWED',
  PUBLISHED: 'PUBLISHED',
  EXPIRED: 'EXPIRED',
  UPCOMING: 'UPCOMING',
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  DELETED: 'DELETED',
  UPDATED: 'UPDATED',
};
export const INVALID_EMPTY_ASSIGNEE_LIST_ERROR_MSG = 'At least one assignee is required';

export const OFFERS_UPDATE_STATUS = {
  PAUSE: 'PAUSE',
  RESUME: 'RESUME',
};

export const OFFERS_TAB_STATUS = {
  PUBLISHED: 'PUBLISHED',
  PENDING: 'PENDING',
};

export const statusByTab = {
  pending: [ OFFERS_STATUS.DRAFT, OFFERS_STATUS.SUBMITTED, OFFERS_STATUS.REVIEWED ],
  published: [ OFFERS_STATUS.UPCOMING, OFFERS_STATUS.EXPIRED, OFFERS_STATUS.ACTIVE, OFFERS_STATUS.INACTIVE, OFFERS_STATUS.UPDATED ],
};

export const CANADIAN_PROVINCES = [
  { name: 'alberta', label: 'Alberta', value: 'AB' },
  { name: 'britishcolumbia', label: 'British Columbia', value: 'BC' },
  { name: 'manitoba', label: 'Manitoba', value: 'MB' },
  { name: 'newbrunswick', label: 'New Brunswick', value: 'NB' },
  { name: 'newfoundlandandlabrador', label: 'Newfoundland and Labrador', value: 'NL' },
  { name: 'novascotia', label: 'Nova Scotia', value: 'NS' },
  { name: 'ontario', label: 'Ontario', value: 'ON' },
  { name: 'princeedwardisland', label: 'Prince Edward Island', value: 'PE' },
  { name: 'quebec', label: 'Quebec', value: 'QC' },
  { name: 'northwestterritories', label: 'Northwest Territories', value: 'NT' },
  { name: 'nunavut', label: 'Nunavut', value: 'NU' },
  { name: 'saskatchewan', label: 'Saskatchewan', value: 'SK' },
  { name: 'yukon', label: 'Yukon', value: 'YT' },
];
