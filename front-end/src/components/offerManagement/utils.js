import { OFFERS_STATUS } from './constants';

const getBadgeType = (statusText) => {
  switch (statusText) {
    case OFFERS_STATUS.DRAFT:
    case OFFERS_STATUS.SUBMITTED:
    case OFFERS_STATUS.REVIEWED:
    case OFFERS_STATUS.UPDATED:
      return 'new';
    case OFFERS_STATUS.ACTIVE:
    case OFFERS_STATUS.PUBLISHED:
      return 'success-emphasis';
    case OFFERS_STATUS.EXPIRED:
    case OFFERS_STATUS.DELETED:
      return 'error';
    case OFFERS_STATUS.INACTIVE:
    default:
      return 'default';
  }
};

/**
 * @typedef {Object} InputItem
 * @property {string} ownership
 * @property {string} code
 * @property {string} sub_code
 */

/**
 * @typedef {Object} OutputItem
 * @property {string} ownership_type
 * @property {string} product_code
 * @property {string} product_sub_code
 */

/**
 * Maps the keys of each object in the input's any_of, all_of, or none_of arrays to new keys.
 *
 * @param {InputObject} input - The input object with any_of, all_of, or none_of arrays.
 * @returns {OutputObject} The output object with mapped keys inside the group array.
 */
const mapProductTargetingField = (input) => {
  const keyMap = {
    ownership: 'ownership_type',
    code: 'product_code',
    sub_code: 'product_sub_code',
  };

  // Find the top-level key (any_of, all_of, or none_of)
  const groupKey = Object.keys(input)[0];
  const items = input[groupKey];

  // Map each object in the array
  const mappedItems = items.map((obj) =>
    Object.fromEntries(Object.entries(obj).map(([ k, v ]) => [ keyMap[k] || k, v ])),
  );

  // Return the mapped structure
  return { [groupKey]: mappedItems };
};

/**
 * Extracts all 'details' messages from error.response.data.notifications array.
 * Returns an array of strings (may be empty if no details found).
 */
function extractErrorDetailsArray(error) {
  const notifications = error?.response?.data?.notifications;
  if (!Array.isArray(notifications)) return [];

  return notifications
    .map(n => n?.metadata?.details)
    .filter(detail => typeof detail === 'string' && detail.trim() !== '');
}

const formatProducts = i => {
  return {
    ...(i.ownership_type && { ownership: i.ownership_type }),
    ...(i.product_code && { code: i.product_code }),
    ...(i.product_sub_code && { sub_code: i.product_sub_code }),
  };
};

export { getBadgeType, mapProductTargetingField, extractErrorDetailsArray, formatProducts };
