import React, { useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import moment from 'moment';

import SideSheet from 'canvas-core-react/lib/SideSheet';
import CanvasBadge from 'canvas-core-react/lib/StatusBadge';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import TextBody from 'canvas-core-react/lib/TextBody';
import TextLegal from 'canvas-core-react/lib/TextLegal';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';
import IconClose from 'canvas-core-react/lib/IconClose';
import CanvasLink from 'canvas-core-react/lib/Link';

import { getBadgeType } from './utils';
import { getContentById } from '../../store/actions/content';

const headerHeight = 72;

const SidePanel = ({
  isSidePanelOpen,
  setIsSidePanelOpen,
  selectedOffer,
  actionList,
  offersLoading,
}) => {
  const dispatch = useDispatch();
  const [ panelOffset, setPanelOffset ] = useState(headerHeight);
  const {
    content: {
      contentItem: content,
      isLoading: contentLoading,
    },
    teams: { items: teams, isLoading: teamsLoading },
    users: { items: users, isLoading: usersLoading },
  } = useSelector((state) => state);

  useEffect(() => {
    const handleScroll = () => {
      setPanelOffset(window.scrollY < headerHeight ? headerHeight - scrollY : 0);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (selectedOffer) {
      dispatch(getContentById(selectedOffer.contentful_type, selectedOffer.contentful_id, selectedOffer.contentful_space_id));
    }
  }, [ selectedOffer ]);

  const sidMapping = useMemo(() => {
    if (usersLoading || !users) return {};
    return Object.values(users).reduce((o, i) => ({ ...o, [i.sid]: { name: i.name, teamId: i.team_id } }), {});
  }, [ users, usersLoading ]);

  const teamMapping = useMemo(
    () => teams ? Object.values(teams).reduce((o, team) => ({ ...o, [team.id]: team.name }), {}) : {},
    [ teams, teamsLoading ]
  );

  return (
    <SideSheet
      headline=""
      isSheetVisible={isSidePanelOpen}
      setSheetVisible={setIsSidePanelOpen}
      className={classnames('admin-list__sidepanel', { 'admin-list__panel-offset': panelOffset })}
      style={{ top: `${panelOffset}px` }}
      type="persistent"
      hideHeading
    >
      <div className="admin-list__sidepanel__container">
        <div className="admin-list__panel-section">
          { offersLoading ? (
            <IconSpinner size={32} className="admin-list__panel-badge" />
          ) : (
            <CanvasBadge className="admin-list__panel-badge" type={getBadgeType(selectedOffer.offer_status)}>
              { selectedOffer.offer_status }
            </CanvasBadge>
          ) }
          <button className="admin-list__panel-icon" onClick={() => setIsSidePanelOpen(false)}>
            <IconClose />
          </button>
        </div>
        <TextHeadline className="admin-list__panel-heading" component="h2" size={21}>
          { selectedOffer.offer_id }
        </TextHeadline>
        <TextBody className="admin-list__panel-name" component="p">
          { selectedOffer.title }
        </TextBody>
        <div className="admin-list__panel-section admin-list__panel-icons">
          { actionList.map(action => {
            const isDisabled =
            offersLoading && (action.menuName === 'Activate' || action.menuName === 'Suspend');
            return (
              <div
                key={`action-${action.menuName}`}
                className={`admin-list__panel-icon-container ${isDisabled ? 'admin-list__panel-icon-container--disabled' : ''}`}
              >
                <button
                  className={`admin-list__panel-icon ${isDisabled ? 'admin-list__panel-icon--disabled' : ''}`}
                  onClick={action.onClick}
                  disabled={isDisabled}
                >
                  { action.iconType({ color: `${isDisabled ? 'gray' : 'blue'}` }) }
                </button>
                <TextLegal component="p" bold={true}>
                  { action.menuName }
                </TextLegal>
              </div>
            );
          }) }
        </div>
        <ul>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Category
            </TextSubtitle>
            <TextCaption component="p">{ selectedOffer.category }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Content Type
            </TextSubtitle>
            <TextCaption component="p">{ selectedOffer.contentful_type }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
              <TextSubtitle type="3" component="p">Content Name</TextSubtitle>
              { contentLoading
                ? <IconSpinner size={24} />
                : <TextCaption component="p" className="admin-list__panel-content-name">{ content?.content.title }</TextCaption>
              }
            </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Contentful ID
            </TextSubtitle>
            <div className="admin-list__panel-content-id">
              <TextCaption component="p">{ selectedOffer.contentful_id }</TextCaption>
              <CanvasLink
                href={`/api/v1/contents/spaces/${selectedOffer.contentful_space_id}/entries/contents/${selectedOffer.contentful_id}`}
                target="_blank"
                type="emphasis"
                size={14}
                externalIconLabel="External link"
              >
                Contentful Link
              </CanvasLink>
            </div>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Created by(Team)
            </TextSubtitle>
            <TextCaption component="p">{ teamMapping[sidMapping[selectedOffer.change_history?.created_by]?.teamId] || selectedOffer.change_history?.created_by }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Created by(User)
            </TextSubtitle>
            <TextCaption component="p">{ sidMapping[selectedOffer.change_history?.created_by]?.name || selectedOffer.change_history?.created_by }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Created On
            </TextSubtitle>
            <TextCaption component="p">
            { moment(selectedOffer.change_history?.created_timestamp).format('lll') }
            </TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Last updated by(Team)
            </TextSubtitle>
            <TextCaption component="p">{ teamMapping[sidMapping[selectedOffer.change_history?.last_updated_by]?.teamId] || selectedOffer.change_history?.last_updated_by }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Last updated by(User)
            </TextSubtitle>
            <TextCaption component="p">{ sidMapping[selectedOffer.change_history?.last_updated_by]?.name || selectedOffer.change_history?.last_updated_by }</TextCaption>
          </li>
          <li className="admin-list__panel-section admin-list__panel-list-item">
            <TextSubtitle type="3" component="p">
              Last Update Date
            </TextSubtitle>
            <TextCaption component="p">
            { moment(selectedOffer.change_history?.last_updated_timestamp).format('lll') }
            </TextCaption>
          </li>
        </ul>
      </div>
    </SideSheet>
  );
};

SidePanel.propTypes = {
  isSidePanelOpen: PropTypes.bool,
  setIsSidePanelOpen: PropTypes.func,
  selectedOffer: PropTypes.object,
  actionList: PropTypes.array,
  offersLoading: PropTypes.bool,
};

export default SidePanel;
