.selected-offers-content {
  width: 100%;

  &__content-action-menu {
    ul {
      box-shadow: 0 0.2rem 1rem rgba(0, 34, 91, 0.11);
    }

    button.ActionMenu__KebabButton {
      height: 2.4rem;
    }

    .ActionMenu__dialog--top-left {
      left: 1.2rem;
    }
  }

  &__content-table {
    thead tr {
      border-bottom: 0.1rem solid $canvas-gray-600;
    }

    tbody tr {
      border-bottom: 0.1rem solid $canvas-gray-400;
    }

    thead th {
      min-width: 20rem;
    }

    tbody th {
      min-width: 20rem;
    }

    thead {
      padding-top: unset;
    }

    &-link:hover {
      color: #005e80;
      border: none;
    }
  }
}

a.selected-offers-content__content-table-link,
button.selected-offers-content__content-table-link {
  font-size: 1.4rem;
  height: 2.1rem;
  background: none !important;
  border: none;
  padding: 0 !important;
  font-family: $font-bold-family;
  color: $canvas-dark-blue;
  text-decoration: underline;
  cursor: pointer;
  min-width: auto;
}

// hide canvas default onHover ux
a.selected-offers-content__content-table-link > span,
a.selected-offers-content__content-table-link:hover.Link__link > span,
a.selected-offers-content__content-table-link:hover > span:hover {
  border: none;
}
