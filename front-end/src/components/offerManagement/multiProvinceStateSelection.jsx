import React, { useState } from 'react';
import PropTypes from 'prop-types';
import MultiSelect from 'canvas-core-react/lib/MultiSelect';
import TextButton from 'canvas-core-react/lib/TextButton';
import IconToggleCheck from 'canvas-core-react/lib/IconToggleCheck';
import IconClose from 'canvas-core-react/lib/IconClose';
import Tag from '../autosuggest/tag';

const MultiProvinceStateSelection = ({
  className,
  label,
  name,
  placeholder,
  onChange,
  selectOptions,
  onClickSelectAll,
  onClickClearAll,
  onClickTagDeletion,
  disabled,
}) => {
  const [ isOpen, setIsOpen ] = useState(false);

  return (
    <>
      <div className="multi-province-selection__province-fields">
        <MultiSelect
          id={name}
          className={className}
          label={label}
          placeholder={placeholder}
          options={selectOptions}
          isMutliSelectOpen={isOpen}
          setIsMultiSelectOpen={setIsOpen}
          onChange={onChange}
          disabled={disabled}
        />
        <div className="multi-province-selection__button">
          <TextButton
            type="button"
            iconPosition="left"
            Icon={IconToggleCheck}
            onClick={() => onClickSelectAll(true)}
            data-testid="offers-select-all-province-button"
            className="province-utility-button"
            disabled={disabled}
          >
            Select all { selectOptions.length }
          </TextButton>
        </div>
        <div className="multi-province-selection__button">
          <TextButton
            type="button"
            iconPosition="left"
            Icon={IconClose}
            onClick={onClickClearAll}
            data-testid="offers-clear-all-province-button"
            className="province-utility-button"
            disabled={disabled}
          >
            Clear all
          </TextButton>
        </div>
      </div>
      <div className="multi-province-selection__province-tags">
        { selectOptions &&
          selectOptions
            .filter((province) => province.checked)
            .map((province, idx) => (
              <Tag
                key={idx}
                index={province.value}
                value={province.label}
                onDelete={onClickTagDeletion}
                disabled={disabled}
                type="button"
              />
            )) }
      </div>
    </>
  );
};

MultiProvinceStateSelection.propTypes = {
  className: PropTypes.string,
  label: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  placeholder: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  searchInputLabel: PropTypes.string,
  selectOptions: PropTypes.arrayOf(PropTypes.string),
  disabled: PropTypes.oneOfType([ PropTypes.bool, PropTypes.func ]),
  onClickClearAll: PropTypes.func,
  onClickSelectAll: PropTypes.func,
  onClickTagDeletion: PropTypes.func,
};

export default MultiProvinceStateSelection;
