import React from 'react';
import moment from 'moment';

import TextCaption from 'canvas-core-react/lib/TextCaption';
import CanvasBadge from 'canvas-core-react/lib/StatusBadge';
import TextButton from 'canvas-core-react/lib/TextButton';
import IconInfo from 'canvas-core-react/lib/IconInfo';
import ActionMenuList from 'canvas-core-react/lib/ActionMenuList';

import { OFFERS_STATUS } from './constants';
import { getBadgeType } from './utils';

export const tableColumns = ({
  offers,
  getActionItems,
  isActionListOpen,
  setIsActionListOpen,
  setIsSidePanelOpen,
  setSelectedOffer,
  sortableColumnProperties,
  filters,
  // history,
}) => {
  const columns = [
    {
      name: 'ID',
      cellFormatter: (row) => (
        <>
          <TextCaption component="p">{ row.offer_id || 'N/A' }</TextCaption>
        </>
      ),
      selector: '',
      bodyStyle: { display: 'flex', flexFlow: 'wrap' },
      minWidth: 'auto',
      grow: 0.8,
      ...sortableColumnProperties('offer_id', filters),
    },
    {
      name: `Offer Name`,
      cellFormatter: (row) => (
        <TextButton
          Icon={IconInfo}
          iconPosition="right"
          className="admin-list__name-link"
          onClick={() => {
            setSelectedOffer(row);
            setIsSidePanelOpen(true);
          }}
        >
          { row.title }
        </TextButton>
      ),
      selector: '',
      bodyStyle: { textAlign: 'left' },
      ...sortableColumnProperties('title', filters),
    },
    {
      name: 'Start Date',
      cellFormatter: (row) => (
        <TextCaption component="p">{ moment(row.start_date).format('ll') }</TextCaption>
      ),
      selector: '',
      minWidth: 'auto',
      ...sortableColumnProperties('start_date', filters),
    },
    {
      name: 'Expiry Date',
      cellFormatter: (row) => (
        <TextCaption component="p">{ moment(row.expiry_date).format('ll') }</TextCaption>
      ),
      selector: '',
      minWidth: 'auto',
      ...sortableColumnProperties('expiry_date', filters),
    },
    {
      name: 'Category',
      cellFormatter: (row) => (
        <>
          <TextCaption component="p">{ row.category || 'N/A' }</TextCaption>
        </>
      ),
      selector: '',
      minWidth: 'auto',
      ...sortableColumnProperties('category', filters),
    },
    {
      name: 'Approver',
      cellFormatter: (row) => (
        <>
          <TextCaption component="p">{ row.approvers?.approvers?.join(', ') || 'N/A' }</TextCaption>
        </>
      ),
      selector: '',
      minWidth: 'auto',
    },
    {
      name: 'Status',
      cellFormatter: (row) => {
        let statusText = row.offer_status;
        if (statusText === OFFERS_STATUS.ACTIVE && new Date(row.start_date) > Date.now()) {
          statusText = OFFERS_STATUS.UPCOMING;
        }
        const badgeType = getBadgeType(statusText);
        return (
          <CanvasBadge type={badgeType}>
            { statusText }
          </CanvasBadge>
        );
      },
      selector: '',
      minWidth: 'auto',
      bodyStyle: { height: '100%' },
      ...sortableColumnProperties('status', filters),
    },
    {
      name: 'Priority',
      cellFormatter: (row) => (
        <>
          <TextCaption component="p">
            { row.priority || 'N/A' }
          </TextCaption>
        </>
      ),
      selector: '',
      minWidth: 'auto',
      ...sortableColumnProperties('priority', filters),
    },
    {
      name: 'Action',
      cellFormatter: (row) => {
        const index = offers?.findIndex((_) => _.offer_id === row.offer_id);
        const isBottom = offers?.length > 3 && offers.length - index > 3;
        return (
          <ActionMenuList
            isMenuOpen={row.offer_id === isActionListOpen}
            setIsMenuOpen={(v) => setIsActionListOpen(v ? row.offer_id : false)}
            bottomSheet={{ heading: '' }}
            iconType="horizontal-small"
            dialogPosition={isBottom ? 'top-center' : 'bottom-center'}
          >
            { getActionItems(row).actionItems }
          </ActionMenuList>
        );
      },
      selector: '',
      minWidth: 'auto',
    },
  ];
  return columns;
};
