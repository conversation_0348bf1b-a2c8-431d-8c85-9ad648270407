.offers-details {
  &__header {
    &.TextIntroduction__text {
      font-size: 3.2rem;
    }
  }

  &__sub-header {
    margin-bottom: 3.6rem;

    &.TextIntroduction__text {
      font-size: 2.4rem;
    }
  }

  &__card {
    margin: 3.6rem 0;
  }

  &__field {
    margin-bottom: 2.5rem;
    max-width: 60rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__date-fields {
    display: flex;
    margin-bottom: 1.8rem;
    flex-direction: column;

    @include mq($from: desktop) {
      flex-direction: row;
    }
  }

  &__date-field {
    &:not(:last-child) {
      margin-bottom: 1.8rem;

      @include mq($from: desktop) {
        margin-right: 2.9rem;
      }
    }
  }

  &__content-field {
    margin-bottom: 1.8rem;
  }

  &__information_fields {
    display: flex;
    margin-bottom: 3.6rem;
    flex-direction: column;
    gap: 3.6rem;
    align-self: stretch;

    @include mq($from: tablet) {
      flex-direction: row;
    }
  }

  &__information_field {
    flex: 0 0 50%;
  }

  &__information_category {
    flex: 0 0 40%;

    &__tooltip {
      font-family: 'Scotia Regular', sans-serif;
      font-size: 1.4rem;
    }
  }

  &__information_priority {
    flex: 1;
  }

  &__input-group-language {
    display: flex;
    gap: 2.4rem;
    flex-direction: column;
    margin-top: 2.4rem;
  }

  &__action-buttons {
    display: flex;
    align-content: center;
    justify-content: space-between;
  }

  &__action-button {
    margin-left: 3rem;

    &:first-child {
      margin-left: 0;
    }

    &__submit-buttons {
      display: flex;
    }
  }

  &__product-relationship {
    display: flex;
    align-items: center;
    margin-top: 1.8rem;
    gap: 2.4rem;
  }
}
