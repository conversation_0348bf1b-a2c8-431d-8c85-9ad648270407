import React, { useState, useCallback, useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import qs from 'qs';
import { omit, difference } from 'lodash';
import classNames from 'classnames';
import { useDispatch, useSelector } from 'react-redux';

import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';
import Tabs from 'canvas-core-react/lib/Tabs';
import Tab from 'canvas-core-react/lib/Tab';
import AlertBanner from 'canvas-core-react/lib/AlertBanner';
import IconShow from 'canvas-core-react/lib/IconShow';
import IconCheck from 'canvas-core-react/lib/IconCheck';
import IconClose from 'canvas-core-react/lib/IconClose';
import IconDuplicate from '../../assets/svgs/IconDuplicate';
import IconDelete from 'canvas-core-react/lib/IconDelete';
import IconEdit from 'canvas-core-react/lib/IconEdit';
import ActionMenuListItem from 'canvas-core-react/lib/ActionMenuListItem';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';

import PageInfo from '../pageInfo';
import List from '../listing/list';
import { useOffers } from '../../hooks/useOffers';
import { tableColumns } from './offerColumns';
import { formatWord, removeFalsyKeys } from '../../utils';
import permissionsList from '../../constants/permissionsList';
import { CATEGORY_OPTIONS, OFFERS_STATUS, OFFERS_TAB_STATUS, statusByTab } from './constants';
import SidePanel from './sidePanel';
import { deleteOffer, setOfferActive } from '../../store/actions/offers';
import Filters from './filters';
import { exportOffers } from '../../api/offers';

const PAGE_SIZE = 10;
const BASE_QUERY = {
  limit: PAGE_SIZE,
  pageNumber: 1,
  search: '',
  status: '',
  priority: '',
  category: '',
  offer_id: '',
  sort: '',
  start_date: '',
  end_date: '',
};

const OffersList = () => {
  const dispatch = useDispatch();
  const history = useHistory();

  const queryParams = qs.parse(history.location.search, { ignoreQueryPrefix: true });
  const initialFilters = {
    ...BASE_QUERY,
    ...omit(queryParams, difference(Object.keys(queryParams), Object.keys(BASE_QUERY))),
  };

  const [ filters, setFilters ] = useState(initialFilters);
  const [ isActionListOpen, setIsActionListOpen ] = useState(false);
  const [ isSidePanelOpen, setIsSidePanelOpen ] = useState(false);
  const [ idToDelete, setIdToDelete ] = useState(null);
  const [ selectedOffer, setSelectedOffer ] = useState(null);
  const [ activeTab, setActiveTab ] = useState(null);
  const {
    users: { items: users, isLoading: usersLoading },
  } = useSelector((state) => state);

  const { offersLoading, offers, pagination, currentUser, isUpdateOfferStatusLoading } = useOffers(removeFalsyKeys(filters), true);
  const { permissions } = currentUser;

  const canCreate = permissions && (
    permissions['admin'] ||
    permissions[permissionsList.OFFERS_MANAGE]
  );

  const canView = permissions && (
    permissions['admin'] ||
    permissions[permissionsList.OFFERS_VIEW]
  );

  if (!canView) {
    return (<AlertBanner type="error">You do not have permissions to view this page</AlertBanner>);
  };

  const filtersChanged = (newFilters) => {
    setFilters(newFilters);
    const queryParams = removeFalsyKeys(
      omit(newFilters, [
        'limit',
        ...(newFilters.pageNumber === 1 ? [ 'pageNumber' ] : []),
      ])
    );
    history.push({
      search: qs.stringify(queryParams, { addQueryPrefix: true }),
    });
  };

  const onColumnSort = (columnKey, currentFilters) =>
    (row, direction) => filtersChanged({ ...currentFilters, pageNumber: 1, sort: `${direction > 0 ? '-' : ''}${columnKey}` });

  const initialSortDirection = columnKey => {
    const { sort } = filters;
    if (!filters.sort) {
      return 0;
    }

    // check if the sorted key has a - in front of it which would indicate it's in descending order, ascending order otherwise
    if (sort.includes(columnKey)) {
      return (sort.charAt(0) === '-') ? 1 : -1;
    }

    return 0;
  };

  const sortableColumnProperties = useCallback((columnKey, currentFilters) => ({
    sortable: true,
    overrideSortBehaviour: onColumnSort(columnKey, currentFilters),
    initialSortDirection: initialSortDirection(columnKey),
  }), []);

  const sidMapping = useMemo(() => {
    if (usersLoading || !users) return {};
    return Object.values(users).reduce((o, i) => ({ ...o, [i.sid]: { name: i.name, teamId: i.team_id } }), {});
  }, [ users, usersLoading ]);

  const tableData = useMemo(() => {
    if (!offers || !offers.length) return [];
    return offers && offers.length
    ? offers.map(offer => {
        if (offer.approvers && offer.approvers.approvers) {
          offer.approvers.approvers = offer.approvers.approvers.map(approver => sidMapping[approver] ? sidMapping[approver].name : approver);
        }
        return offer;
      })
    : [];
  }, [ sidMapping, offers ]);

  const getActionItems = (row) => {
    if (!row) {
      return;
    }

    const {
      offer_status: status,
      offer_id: id,
    } = row;

    const actionList = [ {
      iconType: status === OFFERS_STATUS.DRAFT && canCreate ? IconEdit : IconShow,
      onClick: () => {
        setIsActionListOpen(false);
        history.push(`/offers/${id}/${status === OFFERS_STATUS.DRAFT && canCreate ? 'edit' : 'view'}`);
      },
      menuName: status === OFFERS_STATUS.DRAFT && canCreate ? 'Edit' : 'View',
    } ];

    if (status === OFFERS_STATUS.ACTIVE && canCreate) {
      actionList.push({
        iconType: IconEdit,
        onClick: () => {
          setIsActionListOpen(false);
          history.push(`/offers/${id}/edit`);
        },
        menuName: 'Edit',
      }, {
        iconType: IconClose,
        onClick: () => {
          setIsActionListOpen(false);
          dispatch(setOfferActive(id, false));
        },
        menuName: 'Deactivate',
      }
      );
    };
    if (canCreate) {
      actionList.push({
        iconType: IconDuplicate,
        onClick: () => history.push(`/offers/${id}/duplicate`),
        menuName: 'Duplicate',
      });
    };

    if (status === OFFERS_STATUS.INACTIVE) {
      actionList.push({
        iconType: IconCheck,
        onClick: () => {
          setIsActionListOpen(false);
          dispatch(setOfferActive(id));
        },
        menuName: 'Activate',
      });
    }

    if ((status === OFFERS_STATUS.DRAFT || status === OFFERS_STATUS.SUBMITTED || status === OFFERS_STATUS.REVIEWED) && canCreate) {
      actionList.push({
        iconType: IconDelete,
        onClick: () => {
          setIsActionListOpen(false);
          setIdToDelete(id);
        },
        menuName: 'Delete',
      });
    }
    return { actionList, actionItems: actionList.map(action => <ActionMenuListItem key={action.menuName} icon={action.iconType} onClick={action.onClick}>{ action.menuName }</ActionMenuListItem>) };
  };

  const renderDeleteModal = () => {
    if (idToDelete && isSidePanelOpen) {
      setIsSidePanelOpen(false);
    }
    const closeDeleteModal = () => setIdToDelete(null);
    return (
      <ModalDialogue
        headline="Are you sure?"
        primaryButtonLabel="Delete"
        primaryAction={() => {
          dispatch(deleteOffer(idToDelete));
          closeDeleteModal();
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={closeDeleteModal}
        isModalVisible={!!idToDelete}
        setModalVisible={closeDeleteModal}
      >
        This offer will be deleted.
      </ModalDialogue>
    );
  };

  const getStatusOptions = () => {
    let statusOptions = [];
    const date = new Date();
    const now = date.toISOString();
    if (!activeTab) {
      if (filters.start_date_gt > now) {
        // can't be expired
        statusOptions = [ ...statusByTab.pending, OFFERS_STATUS.UPCOMING, OFFERS_STATUS.INACTIVE ];
      } else if (filters.end_date_lt < now) {
        // can't be upcoming
        statusOptions = [ ...statusByTab.pending, OFFERS_STATUS.EXPIRED ];
      } else {
        statusOptions = [ ...statusByTab.pending, ...statusByTab.published ];
      }
    } else if (activeTab === OFFERS_STATUS.PUBLISHED) {
      if (filters.start_date_gt > now) {
        statusOptions = [ OFFERS_STATUS.UPCOMING ];
      } else if (filters.end_date_lt < now) {
        statusOptions = [ OFFERS_STATUS.EXPIRED ];
      } else {
        statusOptions = statusByTab.published;
      }
    } else if (activeTab === OFFERS_TAB_STATUS.PENDING) {
      statusOptions = statusByTab.pending;
    }

    const sortOrder = [ OFFERS_STATUS.ACTIVE, OFFERS_STATUS.INACTIVE, OFFERS_STATUS.UPCOMING, OFFERS_STATUS.EXPIRED, OFFERS_STATUS.DRAFT, OFFERS_STATUS.REVIEWED, OFFERS_STATUS.SUBMITTED, OFFERS_STATUS.UPDATED ];
    const res = sortOrder.filter(status => {
      if (statusOptions.includes(status)) {
        return status;
      }
    });
    return res;
  };

  const tabChanged = (tabIndex) => {
    const newFilters = { ...filters };
    switch (tabIndex) {
      case 0:
        if (activeTab) {
          setActiveTab(null);
          delete newFilters.pageNumber;
          delete newFilters.filter;
          setFilters(newFilters);
        }
        break;
      case 1:
        if (activeTab !== OFFERS_TAB_STATUS.PUBLISHED) {
          setActiveTab(OFFERS_TAB_STATUS.PUBLISHED);
          delete newFilters.pageNumber;
          setFilters({ ...newFilters, filter: OFFERS_TAB_STATUS.PUBLISHED });
        }
        break;
      case 2:
        if (activeTab !== OFFERS_TAB_STATUS.PENDING) {
          setActiveTab(OFFERS_TAB_STATUS.PENDING);
          delete newFilters.pageNumber;
          setFilters({ ...newFilters, filter: OFFERS_TAB_STATUS.PENDING });
        }
        break;
      default:
        break;
    }
  };

  const handleExport = (e) => {
    exportOffers(filters);
  };

  return (
    <>
    <div className={classNames('admin-list')}>
      <PageInfo
        title="Offers"
        buttonText="Create Offer"
        TitleComponent={TextHeadline}
        ButtonComponent={SecondaryButton}
        titleComponentPorps={{ component: 'h1' }}
        isLoading={false}
        showActionButton={!!canCreate}
        onClick={() => history.push('/offers/create')}
      />

      <Tabs className="admin-list__status-tabs" onClick={(tabIndex) => tabChanged(tabIndex)}>
        <Tab label="All" />
        <Tab label="Published" />
        <Tab label="Pending" />
      </Tabs>

      <Filters
        filters={filters}
        onFiltersChange={setFilters}
        filterFields={[
          {
            label: 'Status',
            key: 'status',
            options: getStatusOptions().map(status => ({
              label: formatWord(status, { capitalize: true }),
              value: status,
            })),
            defaultOptionLabel: 'Select status',
          },
          {
            label: 'Category',
            key: 'category',
            options: CATEGORY_OPTIONS,
            defaultOptionLabel: 'Select Category',
          },
          {
            label: 'Priority',
            key: 'priority',
            options: Array.from({ length: 50 }, (_, i) => ({
              id: i + 1,
              name: i + 1,
              value: i + 1,
              label: i + 1,
            })),
            defaultOptionLabel: 'Select priority',
          },
        ]}
        handleExport={handleExport}
        includeDateTime
      />

      <List
        isLoading={offersLoading}
        entityName=""
        className="admin-list__listings-table"
        columnFixed={false}
        columns={tableColumns({
          tableData,
          getActionItems,
          isActionListOpen,
          setIsActionListOpen,
          setIsSidePanelOpen,
          setSelectedOffer,
          history,
          sortableColumnProperties,
          filters,
        })}
        data={offers}
        showMangePrefix={false}
        resetSortOnDataChange={false}
      />

      { !offersLoading && offers?.length > 0 && (
        <DesktopPagination
          id="offers-pagination"
          totalResultCount={pagination?.total || 0}
          onChange={(pageNumber) => {
            filtersChanged({ ...filters, pageNumber });
            window.scrollTo(0, 0);
          }}
          firstButtonLabel="First"
          prevButtonLabel="Previous"
          nextButtonLabel="Next"
          lastButtonLabel="Last"
          visiblePages={5}
          navigationLabel="Pagination Navigation"
          pageSize={pagination?.limit || 0}
          currentPage={offers ? pagination?.offset / pagination?.limit + 1 : 1}
          containerType="card"
        />
      ) }

      { selectedOffer && (
        <SidePanel
          isSidePanelOpen={isSidePanelOpen}
          setIsSidePanelOpen={setIsSidePanelOpen}
          selectedOffer={selectedOffer}
          setSelectedOffer={setSelectedOffer}
          actionList={getActionItems(selectedOffer).actionList}
          offersLoading={isUpdateOfferStatusLoading}
        />
      ) }

      { renderDeleteModal() }
    </div>
    </>
  );
};

export default OffersList;
