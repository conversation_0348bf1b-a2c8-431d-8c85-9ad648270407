import React from 'react';
import mockAxios from 'axios';
import { fireEvent, waitFor } from '@testing-library/react';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';

import { renderPage } from '../../utils/testing-library-utils';
import OffersList from './listContainer';
import permissionsList from '../../constants/permissionsList';

const mockStore = configureMockStore([ thunk ]);
const mockContent = {
  isLoading: false,
  contentItem: {
    content: {
      name: 'test contentful name',
    },
  },
};

describe('Offers list', () => {
  afterEach(() => {
    mockAxios.reset();
  });

  test('Hide create button if no access', async() => {
    const { queryByText } = await renderPage(
      <OffersList />,
      {
        store: mockStore({
          authenticated: { permissions: { [permissionsList.OFFERS_VIEW]: true, [permissionsList.OFFERS_MANAGE]: false } },
          offers: { isLoading: false, items: [] },
          users: { isLoading: false, items: [] },
        }
      ) }
    );
    await waitFor(() => {
      expect(queryByText('Create New')).not.toBeInTheDocument();
    });
  });

  test('No permissions - check for alert banner', async() => {
    const { getByText } = await renderPage(
      <OffersList />,
      {
        store: mockStore({
          authenticated: { permissions: { [permissionsList.OFFERS_VIEW]: false } },
          offers: { isLoading: false, items: [] },
          users: { isLoading: false, items: [] },
        }
      ) }
    );
    expect(getByText('You do not have permissions to view this page')).toBeInTheDocument();
  });

  test('Side panel open', async() => {
    const { container, getAllByRole } = await renderPage(
      <OffersList />, {
        initialState: {
          content: mockContent,
        },
      },
    );
    const allButtons = getAllByRole((role, element) => role === 'button' && element.className.includes('admin-list__name-link'));
    fireEvent.click((allButtons[0]));
    const sidePanel = container.querySelector('.admin-list__sidepanel__container');
    expect(sidePanel).toBeInTheDocument();
  });
});
