import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { renderPage } from '../../utils/testing-library-utils';

import Details from './details';
import { createMemoryHistory } from 'history';
import { MemoryRouter, Route } from 'react-router-dom';
import {
  HUBBLE_CONTENT_SPACE,
  INVALID_DATE_COMPARISON_ERROR_MSG,
  OFFERS_STATUS,
  ACTION,
  HUBBLE_CONTENT_TYPE_OFFERS,
} from './constants';

import mockOffer from '../../../../src/__mocks__/mockOffer';
import {
  createOffer,
  updateOffer,
  getOffer,
  getProductBook,
  approveOffer,
  getOfferAssignees,
} from '../../api/offers';
import {
  getContentById,
  getContentItems,
  getContentTypes,
} from '../../api/content';
import { addSnackbar } from '../../store/actions/snackbar';
import { addAlert } from '../../store/actions/alertBanner';

jest.mock('../../api/offers');
jest.mock('../../api/content');

const productMockBook = [
  {
    category: 'banking',
    code: 'DDA',
    description: 'DDA',
    id: '100020',
    ownership: 'B',
    include: false,
    exclude: false,
  },
  {
    category: 'banking',
    code: 'DDA',
    description: 'DDA',
    id: '100031',
    ownership: 'R',
    include: false,
    exclude: false,
  },
  {
    category: 'banking',
    code: 'DDA',
    description: 'Current Account',
    id: '100060',
    ownership: 'B',
    sub_code: 'CA',
    include: false,
    exclude: false,
  },
  {
    category: 'banking',
    code: 'DDA',
    description: 'Current Account',
    id: '100071',
    ownership: 'R',
    sub_code: 'CA',
    include: true,
    exclude: false,
  },
  {
    category: 'banking',
    code: 'DDA',
    description: 'Scotia Chequing',
    id: '100100',
    ownership: 'B',
    sub_code: 'SC',
    include: false,
    exclude: false,
  },
  {
    category: 'banking',
    code: 'DDA',
    description: 'Scotia Chequing',
    id: '100111',
    ownership: 'R',
    sub_code: 'SC',
    include: true,
    exclude: false,
  },
];

const mockContentData = {
  id: 'test-content-id',
  content: {
    id: 'test-name',
  },
  type: 'cmOffer',
};

const mockContentTypes = {
  items: [
    { id: 'type1', name: HUBBLE_CONTENT_TYPE_OFFERS },
    { id: 'type2', name: 'Content Type 2' },
  ],
};

const mockContentItems = {
  total: 25, // More than the default limit of 10
  offset: 0,
  limit: 10,
  items: Array.from({ length: 30 }, (_, index) => ({
    id: `content${index + 1}`,
    content: { id: `Content ${index + 1}` },
    updated_at: `2023-01-${(index + 1).toString().padStart(2, '0')}T12:00:00Z`,
  })),
};

const mockAssignees = [
  { sid: 's1234567', full_name: 'John Doe' },
  { sid: 's1234568', full_name: 'Jane Smith' },
];

describe('OffersDetails', () => {
  beforeEach(() => {
    getProductBook.mockResolvedValue(productMockBook);
    getContentById.mockResolvedValue(mockContentData);
    getContentTypes.mockResolvedValue(mockContentTypes);
    getContentItems.mockResolvedValue(mockContentItems);
    getOfferAssignees.mockResolvedValue({ data: mockAssignees });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('should render in view action', async() => {
    getOffer.mockResolvedValue({ data: mockOffer });
    await renderPage(
      <MemoryRouter initialEntries={[ '/offers/1/view' ]}>
        <Route path="/offers/:id/:action" component={Details} />
      </MemoryRouter>
    );
    expect(screen.getByText('View Offer')).toBeInTheDocument();
  });

  test('invalid same date campaign and priority weight', async() => {
       // Mock scrollIntoView, focus before rendering
       window.HTMLElement.prototype.scrollIntoView = jest.fn();
       window.HTMLElement.prototype.focus = jest.fn();

    await renderPage(<Details />, {
      history: createMemoryHistory({
        initialEntries: [ '/offers/create' ],
      }),
    });
    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: '05/01/2024 12:00 AM' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /expiry date/i }), {
      target: { value: '05/01/2024 12:00 AM' },
    });

    fireEvent.change(screen.getByRole('slider', { name: /priority weight/i }), {
      target: { value: '0' },
    });

    await waitFor(() => {
      expect(
        screen.getByText(INVALID_DATE_COMPARISON_ERROR_MSG)
      ).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('offers-save-draft-button'));

    await waitFor(() => {
      expect(
        screen.getByText('Please enter an integer between 1 to 50')
      ).toBeInTheDocument();
    });
  });

  test('handle offer create action submit', async() => {
    window.HTMLElement.prototype.scrollIntoView = jest.fn();
    window.HTMLElement.prototype.focus = jest.fn();
    createOffer.mockResolvedValue({ data: mockOffer });
    const { store } = await renderPage(
      <MemoryRouter initialEntries={[ '/offers/create' ]}>
        <Route path="/offers/create" component={Details} />
      </MemoryRouter>
    );

    expect(await screen.findByText('Create a New Offer')).toBeInTheDocument();

    fireEvent.change(screen.getByTestId('input-offer-title'), {
      target: { value: 'Test Offer' },
    });

    fireEvent.change(screen.getByTestId('select-offer-category'), {
      target: { value: 'OFFERS' },
    });

    fireEvent.change(
      await screen.findByRole('textbox', { name: /start date/i }),
      {
        target: { value: '01/25/2025 12:00 AM' },
      }
    );
    fireEvent.change(
      await screen.findByRole('textbox', { name: /expiry date/i }),
      {
        target: { value: '02/25/2025 12:00 AM' },
      }
    );

    fireEvent.change(
      await screen.findByRole('textbox', { name: /priority weight/i }),
      {
        target: { value: '10' },
      }
    );

    fireEvent.click(
      await screen.findByTestId('radio-product_relationship-EVERYONE')
    );

    /// assert content modal is  not open
    await waitFor(() => {
      expect(screen.queryByText('Add New Content')).not.toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('offers-add-content-button'));

    /// assert content modal is open
    await waitFor(() => {
      expect(screen.queryByText('Add New Content')).toBeInTheDocument();
    });

    // Find and select the content type
    const contentTypeSelect = await screen.findByTestId('content-type-select');
    fireEvent.change(contentTypeSelect, { target: { value: 'type1' } });

    // Verify the content items are fetched
    await waitFor(() => {
      expect(getContentItems).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'type1',
          contentfulSpace: HUBBLE_CONTENT_SPACE,
        })
      );
    });

    const radioButtonElement = screen.getByTestId(
      'content-radio-button-content1'
    );
    fireEvent.click(radioButtonElement);
    expect(radioButtonElement).toBeChecked();

    const selectButton = await screen.findByTestId(
      'content-modal-select-button'
    );
    fireEvent.click(selectButton);

    await waitFor(() => {
      expect(screen.queryByText('Add New Content')).not.toBeInTheDocument();
    });

    await waitFor(() => {
      expect(getContentById).toHaveBeenCalled();
    });

    fireEvent.click(screen.getByTestId('offers-save-draft-button'));

    await waitFor(() => {
      expect(createOffer).toHaveBeenCalled();
      const expectedSnackbar = addSnackbar({
        message: 'Offer "Test Offer" has been created successfully',
      });
      expect(store.getActions()).toContainEqual(expectedSnackbar);
    });
  });

  it('handle offer edit action submit', async() => {
    getOffer.mockResolvedValue({ data: mockOffer });
    updateOffer.mockResolvedValue({ updatedOffer: { data: { mockOffer } } });

    const { store } = await renderPage(
      <MemoryRouter initialEntries={[ '/offers/OFF-1/edit' ]}>
        <Route path="/offers/:id/:action" component={Details} />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('Edit Offer')).toBeInTheDocument();
      expect(screen.getByText('Target by demographic')).toBeInTheDocument();
    });

    fireEvent.change(screen.getByTestId('input-offer-title'), {
      target: { value: 'Test Offer' },
    });

    fireEvent.change(screen.getByTestId('select-offer-category'), {
      target: { value: 'OFFERS' },
    });

    fireEvent.click(screen.getByTestId('offers-select-all-province-button'));

    fireEvent.change(
      screen.getByRole('textbox', { name: /priority weight/i }),
      {
        target: { value: 10 },
      }
    );

    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: '01/25/2025 12:00 AM' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /expiry date/i }), {
      target: { value: '02/25/2025 12:00 AM' },
    });

    fireEvent.click(screen.getByTestId('offers-save-draft-button'));

    await waitFor(() => {
      expect(updateOffer).toHaveBeenCalled();
      const expectedSnackbar = addSnackbar({
        message: 'Offer "Test Offer" has been edited successfully',
      });
      expect(store.getActions()).toContainEqual(expectedSnackbar);
    });
  });

  it('handle submit error', async() => {
    getOffer.mockResolvedValue({ data: mockOffer });
    updateOffer.mockRejectedValue({
      response: { data: { errorMessage: 'Failed to save offer' } },
    });

    const { store } = await renderPage(
      <MemoryRouter initialEntries={[ '/offers/OFF-1/edit' ]}>
        <Route path="/offers/:id/:action" component={Details} />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('Edit Offer')).toBeInTheDocument();
    });

    fireEvent.change(screen.getByTestId('input-offer-title'), {
      target: { value: 'Test Offer' },
    });

    fireEvent.change(screen.getByTestId('select-offer-category'), {
      target: { value: 'OFFERS' },
    });

    fireEvent.change(
      screen.getByRole('textbox', { name: /priority weight/i }),
      {
        target: { value: 10 },
      }
    );

    fireEvent.change(screen.getByRole('textbox', { name: /start date/i }), {
      target: { value: '01/25/2025 12:00 AM' },
    });
    fireEvent.change(screen.getByRole('textbox', { name: /expiry date/i }), {
      target: { value: '02/25/2025 12:00 AM' },
    });

    fireEvent.click(screen.getByTestId('offers-save-draft-button'));

    await waitFor(() => {
      const expectedSnackbar = addAlert({ message: 'Failed to save offer' });
      expect(store.getActions()).toContainEqual(expectedSnackbar);
    });
  });

  test('should handle submitting an offer for review', async() => {
    getOffer.mockResolvedValue({ data: { ...mockOffer, offer_status: OFFERS_STATUS.DRAFT } });

    await renderPage(
      <MemoryRouter initialEntries={[ '/offers/OFF-1/edit' ]}>
        <Route path="/offers/:id/:action" component={Details} />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('Edit Offer')).toBeInTheDocument();
    });

    const submitButton = await screen.findByText('Submit for Review');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('This offer needs to be assigned')).toBeInTheDocument();
    });

    const selector = await screen.findByTestId('approver-selector');
    fireEvent.change(selector, { target: { value: 's1234567' } });

    const modalSubmitButton = await screen.findByText('Submit');
    fireEvent.click(modalSubmitButton);

    await waitFor(() => {
      expect(updateOffer).toHaveBeenCalled();
    });
  });

  test('should handle approving an offer', async() => {
    approveOffer.mockResolvedValue({ data: { ...mockOffer, offer_status: OFFERS_STATUS.REVIEWED } });
    getOffer.mockResolvedValue({ data: { ...mockOffer, offer_status: OFFERS_STATUS.SUBMITTED } });

    await renderPage(
      <MemoryRouter initialEntries={[ '/offers/OFF-1/edit' ]}>
        <Route path="/offers/:id/:action" component={Details} />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('Edit Offer')).toBeInTheDocument();
    });

    const approveButton = await screen.findByText('Approve');
    fireEvent.click(approveButton);

    await waitFor(() => {
      expect(screen.getByText('This offer needs to be assigned')).toBeInTheDocument();
    });

    // Select an assignee
    const selector = await screen.findByTestId('approver-selector');
    fireEvent.change(selector, { target: { value: 's1234567' } });

    const modalSubmitButton = await screen.findByText('Submit');
    fireEvent.click(modalSubmitButton);

    await waitFor(() => {
      expect(approveOffer).toHaveBeenCalledWith(
        mockOffer.offer_id,
        expect.objectContaining({
          approvers: [ 's1234567' ],
          action: ACTION.APPROVE,
        })
      );
    });
  });

  test('should handle publishing an offer', async() => {
    approveOffer.mockResolvedValue({ data: { ...mockOffer, offer_status: OFFERS_STATUS.PUBLISHED } });
    getOffer.mockResolvedValue({ data: { ...mockOffer, offer_status: OFFERS_STATUS.REVIEWED } });

    await renderPage(
      <MemoryRouter initialEntries={[ '/offers/OFF-1/edit' ]}>
        <Route path="/offers/:id/:action" component={Details} />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('Edit Offer')).toBeInTheDocument();
    });

    const publishButton = await screen.findByText('Publish');
    fireEvent.click(publishButton);

    await waitFor(() => {
      expect(approveOffer).toHaveBeenCalledWith(
        mockOffer.offer_id,
        expect.objectContaining({
          action: ACTION.PUBLISH,
        })
      );
    });
  });

  test('should handle rejecting an offer', async() => {
    approveOffer.mockResolvedValue({ data: { ...mockOffer, offer_status: OFFERS_STATUS.DRAFT } });
    getOffer.mockResolvedValue({ data: { ...mockOffer, offer_status: OFFERS_STATUS.SUBMITTED } });

    await renderPage(
      <MemoryRouter initialEntries={[ '/offers/OFF-1/edit' ]}>
        <Route path="/offers/:id/:action" component={Details} />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('Edit Offer')).toBeInTheDocument();
    });

    const rejectButton = await screen.findByText('Reject');
    fireEvent.click(rejectButton);

    await waitFor(() => {
      expect(approveOffer).toHaveBeenCalledWith(
        mockOffer.offer_id,
        expect.objectContaining({
          action: ACTION.REJECT,
        })
      );
    });
  });

  test('should handle form validation errors when submitting for review', async() => {
    getOffer.mockResolvedValue({ data: { ...mockOffer, title: '', category: '' } });

    await renderPage(
      <MemoryRouter initialEntries={[ '/offers/OFF-1/edit' ]}>
        <Route path="/offers/:id/:action" component={Details} />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('Edit Offer')).toBeInTheDocument();
    });

    const submitButton = await screen.findByText('Submit for Review');
    fireEvent.click(submitButton);

    await waitFor(() => {
      const errors = screen.getAllByText('Required');
      // Title and category are empty
      expect(errors.length).toBeGreaterThan(1);
    });

    expect(screen.queryByText('This offer needs to be assigned')).not.toBeInTheDocument();
  });

  test('should handle error during approval process', async() => {
    approveOffer.mockRejectedValue({
      response: { data: { errorMessage: 'Failed to approve offer' } },
    });
    getOffer.mockResolvedValue({ data: { ...mockOffer, offer_status: OFFERS_STATUS.SUBMITTED } });

    const { store } = await renderPage(
      <MemoryRouter initialEntries={[ '/offers/OFF-1/edit' ]}>
        <Route path="/offers/:id/:action" component={Details} />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('Edit Offer')).toBeInTheDocument();
    });

    const approveButton = await screen.findByText('Approve');
    fireEvent.click(approveButton);

    await waitFor(() => {
      expect(screen.getByText('This offer needs to be assigned')).toBeInTheDocument();
    });

    // Select an assignee
    const selector = await screen.findByTestId('approver-selector');
    fireEvent.change(selector, { target: { value: 's1234567' } });

    const modalSubmitButton = await screen.findByText('Submit');
    fireEvent.click(modalSubmitButton);

    // Verify the error alert was dispatched
    await waitFor(() => {
      const expectedAlert = addAlert({ message: 'Failed to approve offer' });
      expect(store.getActions()).toContainEqual(expectedAlert);
    });
  });

  test('should handle error during publishing process', async() => {
    approveOffer.mockRejectedValue({
      response: { data: { errorMessage: 'Failed to publish offer' } },
    });
    getOffer.mockResolvedValue({ data: { ...mockOffer, offer_status: OFFERS_STATUS.REVIEWED } });

    const { store } = await renderPage(
      <MemoryRouter initialEntries={[ '/offers/OFF-1/edit' ]}>
        <Route path="/offers/:id/:action" component={Details} />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('Edit Offer')).toBeInTheDocument();
    });

    const publishButton = await screen.findByText('Publish');
    fireEvent.click(publishButton);

    await waitFor(() => {
      const expectedAlert = addAlert({ message: 'Failed to publish offer' });
      expect(store.getActions()).toContainEqual(expectedAlert);
    });
  });
});
