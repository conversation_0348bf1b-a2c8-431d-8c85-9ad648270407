import React, { useState, useEffect } from 'react';
import ActionMenuList from 'canvas-core-react/lib/ActionMenuList';
import IconDelete from 'canvas-core-react/lib/IconDelete';
import IconEdit from 'canvas-core-react/lib/IconEdit';
import Table from 'canvas-core-react/lib/Table';
import { HUBBLE_CONTENT_SPACE, getContentfulEnvironment } from './constants';
import { getContentById } from '../../api/content';
import PropTypes from 'prop-types';
import ActionMenuListItem from 'canvas-core-react/lib/ActionMenuListItem';

function SelectedContentTable({
  contentfulId,
  contentfulType,
  setModalVisible,
  formDisabled,
  deleteContent,
}) {
  const [ contentData, setContentData ] = useState(null);
  const [ isMenuOpen, setIsMenuOpen ] = useState(false);

  useEffect(() => {
    const fetchContent = async() => {
      const data = await getContentById(
        contentfulType,
        contentfulId,
        HUBBLE_CONTENT_SPACE
      );
      setContentData(data);
    };

    fetchContent();
  }, [ contentfulId, contentfulType ]);

  /** add missing button attribute for canvas 6 action menu component bug */
  const fixCanvasButtons = () => {
    document
      .querySelector('.selected-offers-content__content-action-menu')
      .querySelectorAll('button')
      .forEach(el => {
        el.type = 'button';
      });
  };

  const contentTableColumns = [
    {
      name: 'Contentful Name',
      grow: 1,
      selector: 'content.id',
    },
    {
      name: 'Content Type',
      selector: 'type',
    },
    {
      name: 'Preview Link',
      cellFormatter: row => {
        return (
          <button
            className="selected-offers-content__content-table-link"
            type="button"
            onClick={() => {
              const url = window.location.href;
              const env = getContentfulEnvironment(url);
              window.open(
                `https://app.contentful.com/spaces/${HUBBLE_CONTENT_SPACE}/environments/${env}/entries/${row.id}`,
                '_blank'
              );
            }}
          >
            Preview
          </button>
        );
      },
      selector: '',
    },
    ...(!formDisabled
      ? [
          {
            name: 'Action',
            cellFormatter: row => {
              const menuItems = [
                {
                  menuName: 'Edit',
                  iconType: IconEdit,
                  onClick: () => {
                    fixCanvasButtons();
                    setModalVisible();
                  },
                },
                {
                  menuName: 'Delete',
                  iconType: IconDelete,
                  onClick: () => {
                    fixCanvasButtons();
                    deleteContent();
                  },
                },
              ];
              return (
                <ActionMenuList
                  className="selected-offers-content__content-action-menu"
                  icon="horizontal-small"
                  isMenuOpen={isMenuOpen}
                  setIsMenuOpen={() => {
                    fixCanvasButtons();
                    setIsMenuOpen(!isMenuOpen);
                  }}
                  bottomSheet={{ heading: '' }}
                >
                  { menuItems.map(action => (
                    <ActionMenuListItem
                      key={action.menuName}
                      icon={action.iconType}
                      onClick={action.onClick}
                    >
                      { action.menuName }
                    </ActionMenuListItem>
                  )) }
                </ActionMenuList>
              );
            },
            selector: '',
          },
        ]
      : /* istanbul ignore next */ []),
  ];
  return (
    contentfulId &&
    contentData && (
      <div className={'selected-offers-content'}>
        <Table
          id="selected-offers-content-table"
          className="selected-offers-content__content-table"
          title=""
          columns={contentTableColumns}
          data={[ contentData ]}
        />
      </div>
    )
  );
}

SelectedContentTable.propTypes = {
  contentfulId: PropTypes.string.isRequired,
  contentfulType: PropTypes.string.isRequired,
  setModalVisible: PropTypes.func.isRequired,
  formDisabled: PropTypes.bool,
  deleteContent: PropTypes.func.isRequired,
};

SelectedContentTable.defaultProps = {
  formDisabled: false,
};

export default SelectedContentTable;
