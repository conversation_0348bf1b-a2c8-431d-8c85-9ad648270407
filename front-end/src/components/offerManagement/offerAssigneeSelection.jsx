import PropTypes from 'prop-types';
import React, { useState } from 'react';

import Selector from 'canvas-core-react/lib/Selector';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import TextButton from 'canvas-core-react/lib/TextButton';
import IconAdd from 'canvas-core-react/lib/IconAdd';
import IconDelete from 'canvas-core-react/lib/IconDelete';

import { INVALID_EMPTY_ASSIGNEE_LIST_ERROR_MSG } from './constants';

const OfferAsignneeSelection = ({
  isOpen,
  onCancel,
  onSubmit,
  handleAssigneeChange,
  potentialAssignees,
  assignees,
}) => {
  const [ selectedAssignees, setSelectedAssignees ] = useState(assignees || []);
  const [ showError, setShowError ] = useState(false);
  const [ selectorRendererCount, setSelectorRendererCount ] = useState((assignees?.length || 0) + 1);

  const submit = () => {
    if (selectedAssignees && selectedAssignees.length !== 0) {
      onSubmit(selectedAssignees);
    } else {
      setShowError(true);
    }
  };

  const cancel = () => {
    setSelectedAssignees([]);
    setSelectorRendererCount(1);
    onCancel();
  };

  const handleSelectorChange = (index, sid) => {
    const selectedAssignee = potentialAssignees.find(a => a.sid === sid);

    if (selectedAssignee) {
      if (showError) setShowError(false);

      // Create a new array with the updated selection at the specific index
      const newSelectedAssignees = [ ...selectedAssignees ];
      newSelectedAssignees[index] = selectedAssignee.sid;

      setSelectedAssignees(newSelectedAssignees);
      handleAssigneeChange(newSelectedAssignees);
    }
  };

  const removeSelector = (index) => {
    const newSelectedAssignees = [ ...selectedAssignees ];
    newSelectedAssignees.splice(index, 1);
    setSelectedAssignees(newSelectedAssignees);
    handleAssigneeChange(newSelectedAssignees);
    setSelectorRendererCount(count => count - 1);
  };

  return (
    <ModalDialogue
      headline="This offer needs to be assigned"
      isModalVisible={isOpen}
      primaryAction={submit}
      primaryButtonLabel="Submit"
      secondaryAction={cancel}
      onOverlayClick={cancel}
      secondaryButtonLabel="Cancel"
      setModalVisible={cancel}
      data-testid="offer-assignee-selector-modal"
    >
      { Array(selectorRendererCount)
        .fill(null)
        .map((_, index) => {
          // Filter out already selected assignees for this selector
          const availableAssignees = potentialAssignees.filter(
            assignee => !selectedAssignees.includes(assignee.sid) ||
                        selectedAssignees[index] === assignee.sid
          );

          return (
            <div
              key={`assigneeSelectorContainer-${index}`}
              className="assignee-selector__container"
            >
              <div className="assignee-selector__input-remove-row input-remove-row">
              <Selector
                id={`assigneeSelect-${index}`}
                name="assigneeSelect"
                label="Assignee"
                placeholder="Select a person"
                className="assignee-selector__selector"
                onChange={(e) => handleSelectorChange(index, e.target.value)}
                value={selectedAssignees[index] || ''}
                data-testid="approver-selector"
                {
                  ...(showError ? {
                    error: { label: 'Error', messages: [ INVALID_EMPTY_ASSIGNEE_LIST_ERROR_MSG ] },
                  } : {})
                }
              >
                  { availableAssignees.map((assignee) => (
                    <option
                      key={assignee.sid}
                      value={assignee.sid}
                      data-testid={`approver-selector-option_${assignee.sid}`}
                    >
                      { ' ' }
                      { assignee.full_name }{ ' ' }
                    </option>
                  )) }
                </Selector>
                { index > 0 ? (
                  <TextButton
                    className="offer-assignee-selection__button--remove"
                    onClick={() => removeSelector(index)}
                    Icon={IconDelete}
                    iconPosition="right"
                  >
                    Remove
                  </TextButton>
                ) : null }
              </div>
            </div>
          );
      }) }
      { /* Add a single "Add an assignee" button after all selectors */ }
      <div className="assignee-selector__add-button-container">
        <TextButton
          className="offer-assignee-selection__button--add"
          onClick={() => setSelectorRendererCount(count => count + 1)}
          Icon={IconAdd}
          iconPosition="right"
          data-testid="add-assignee-button"
        >
          Add an assignee
        </TextButton>
      </div>
    </ModalDialogue>
  );
};

OfferAsignneeSelection.propTypes = {
  isOpen: PropTypes.bool,
  isSubmitButtonDisabled: PropTypes.bool,
  onCancel: PropTypes.func,
  onSubmit: PropTypes.func,
  assignees: PropTypes.array,
  potentialAssignees: PropTypes.array,
  handleAssigneeChange: PropTypes.func,
  setPotentialAssignees: PropTypes.func,
};

export default OfferAsignneeSelection;
