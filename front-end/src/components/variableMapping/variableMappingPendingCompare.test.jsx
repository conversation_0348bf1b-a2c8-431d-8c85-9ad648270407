import React from 'react';
import { Provider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { render } from '@testing-library/react';
import { Router } from 'react-router-dom';
import { createBrowserHistory } from 'history';
import VariableMappingPendingCompare from './variableMappingPendingCompare';

const mockStore = configureMockStore([ thunk ]);
const mockUsers = {
  items: {
    '1': {
      id: 1,
      name: 'Test Admin',
      email: '<EMAIL>',
      sid: 's1990028',
      active: true,
      roles: [ 1 ],
    },
    '2': {
      id: 2,
      name: 'Test Approver2',
      email: '<EMAIL>',
      sid: 's1990029',
      active: true,
      roles: [ 1 ],
    },
  },
  isLoading: false,
};
const mockVars = {
  isLoading: false,
  activeSet: {
    variable_set_id: 107,
    created_at: '2021-12-08T20:36:27.337Z',
    created_by: 's1990028',
    status: 'active',
    updated_at: '2021-12-09T21:29:50.130Z',
    updated_by: 's1990028',
    description: 'desc',
    approver_sid: 's1990028',
    variables: [
      {
        variable_campaign: 'testmod',
        variable_template: 'SOLUI_TESTMOD_END',
        variable_type: 'text',
        variable_type_label: 'Text',
      },
      {
        variable_campaign: 'testdelete',
        variable_template: 'SOLUI_TESTDELETED_END',
        variable_type: 'text',
        variable_type_label: 'Text',
      },
    ],
  },
  pendingSet: {
    variable_set_id: 124,
    created_at: '2021-12-11T21:19:17.548Z',
    created_by: 's1990028',
    status: 'pending',
    updated_at: '2021-12-09T21:29:50.130Z',
    updated_by: 's1990028',
    description: 'desc',
    approver_sid: 's1990028',
    variables: [
      {
        variable_campaign: 'testmod',
        variable_template: 'SOLUI_TESTMOD_END',
        variable_type: 'date',
        variable_type_label: 'Date',
      },
      {
        variable_campaign: 'testadd',
        variable_template: 'SOLUI_TESTADD_END',
        variable_type: 'date',
        variable_type_label: 'Date',
      },
    ],
  },
  hasPending: true,
  types: [
    { id: 1, name: 'date', description: 'Date (MMDDYYY)' },
    { id: 2, name: 'text', description: 'Text' },
    { id: 3, name: 'currency', description: 'Currency' },
    { id: 4, name: 'account-number-mask', description: 'Masked Account Number' },
    { id: 5, name: 'number', description: 'Number' },
    { id: 6, name: 'account', description: 'Account Number' },
    { id: 7, name: 'gic-special-term', description: 'GIC Special Term' },
    { id: 8, name: 'gic-special-rate', description: 'GIC Special Rate' },
  ],
  approvers: [ mockUsers.items['1'] ],
};

const renderPage = (options = {}) => {
  const store = mockStore({
    authenticated: {
      permissions: { variable_mappings_update: true, variable_mappings_view: true },
      sid: mockUsers.items['1'].sid,
    },
    users: mockUsers,
    variableMapping: mockVars,
    // redux-mock-store does not support reducers
    // so state changes due to UI interaction need to be loaded here
    ...options.initialState,
  });
  const history = options.history ? options.history : createBrowserHistory();
  !options.history && history.push('./active') && history.push('./pending');
  return render(
    <Provider store={store}>
      <Router history={history}>
        <VariableMappingPendingCompare/>
      </Router>
    </Provider>
  );
};

describe('VariableMappingPending', () => {
  it('should render page with default data', () => {
    const page = renderPage();
    expect(page).toMatchSnapshot();
  });

  it.todo('calls onApprove');
  // it('calls onApprove', async() => {
  //   const history = createBrowserHistory();
  //   history.push('./active');
  //   history.push('./pending');
  //   const { getByTestId } = renderPage({ history });
  //   const button = await findByTestId('approve-button');
  //   fireEvent.click(button);
  //   const modal = await findByTestId('approve-confirm-modal');
  //   fireEvent.click(within(modal).getByText('Approve'));
  //   await wait(() => expect(history.location.pathname).toStrictEqual('/active'));
  // });

  it.todo('calls onReject');
  // it('calls onReject', async() => {
  //   const history = createBrowserHistory();
  //   history.push('./draft');
  //   history.push('./pending');
  //   const { getByTestId } = renderPage({ history });
  //   const button = await findByTestId('reject-button');
  //   fireEvent.click(button);
  //   const modal = await findByTestId('reject-confirm-modal');
  //   fireEvent.click(within(modal).getByText('Reject'));
  //   await wait(() => expect(history.location.pathname).toStrictEqual('/draft'));
  // });

  it.todo('calls onUnsubmit');
  // it('calls onUnsubmit', async() => {
  //   const history = createBrowserHistory();
  //   history.push('./draft');
  //   history.push('./pending');
  //   const { findByText, getByTestId } = renderPage({ history });
  //   const button = await findByTestId('unsubmit-button');
  //   fireEvent.click(button);
  //   const modal = await findByTestId('unsubmit-confirm-modal');
  //   fireEvent.click(within(modal).getByText('Unsubmit'));
  //   await wait(() => expect(history.location.pathname).toStrictEqual('/draft'));
  // });

  // it('should allow approver to be edited', async() => {
  //   const { findByText, getByTestId } = renderPage();
  //   fireEvent.click(await findByText('Edit'));

  //   // select an assignee
  //   const { sid, name: newApproverName } = mockUsers.items['2'];
  //   expect(await getByTestId(`approver-selector-option_${sid}`).selected).toBeFalsy();
  //   fireEvent.change(getByTestId('approver-selector').querySelector('select'), { target: { value: sid } });
  //   expect(await getByTestId(`approver-selector-option_${sid}`).selected).toBeTruthy();
  //   fireEvent.click(await findByText('Submit'));
  //   await (await findByText('Assigned to:')).closest('div');
  //   expect(await findByText(newApproverName)).toBeInTheDocument();
  // });
});
