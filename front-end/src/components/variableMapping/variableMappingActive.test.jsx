import React from 'react';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { Provider } from 'react-redux';
import { render, fireEvent } from '@testing-library/react'; // import pretty DOM for ease of visualizing html tree
import { Router } from 'react-router-dom';
import { createBrowserHistory } from 'history';
import mockAxios from 'axios';
import VariableMappingActive from './variableMappingActive';
import permissionsList from '../../constants/permissionsList';
const mockStore = configureMockStore([ thunk ]);
const mockUsers = {
  items: {
    '1': {
      id: 1,
      name: 'Test Admin',
      email: '<EMAIL>',
      sid: 's1990028',
      active: true,
      roles: [ 1 ],
    },
  },
  isLoading: false,
};
const mockVars = {
  isLoading: false,
  activeSet: {
    variable_set_id: 107,
    created_at: '2021-12-08T20:36:27.337Z',
    created_by: 's1990028',
    status: 'active',
    updated_at: '2021-12-09T21:29:50.130Z',
    updated_by: 's1990028',
    description: 'desc',
    approver_sid: 's1990028',
    variables: [
      {
        variable_campaign: 'test',
        variable_template: 'SOLUI_TEST_END',
        variable_type: 'text',
        variable_type_label: 'Text',
      },
    ],
  },
  types: [
    { id: 1, name: 'date', description: 'Date (MMDDYYY)' },
    { id: 2, name: 'text', description: 'Text' },
    { id: 3, name: 'currency', description: 'Currency' },
    { id: 4, name: 'account-number-mask', description: 'Masked Account Number' },
    { id: 5, name: 'number', description: 'Number' },
    { id: 6, name: 'account', description: 'Account Number' },
    { id: 7, name: 'gic-special-term', description: 'GIC Special Term' },
    { id: 8, name: 'gic-special-rate', description: 'GIC Special Rate' },
  ],
  approvers: [ mockUsers.items['1'] ],
  pendingSet: {},
  hasPending: false,
  draftSet: {},
  hasDraft: false,
  editedSet: {},
};
const mockDeltaSet = {
  ...mockVars.activeSet,
  variables: [ {
    ...mockVars.activeSet.variables[0],
    variable_type: mockVars.types[0].name,
    variable_type_label: mockVars.types[0].description,
  } ],
};

const renderPage = (options = {}) => {
  const store = mockStore({
    authenticated: { permissions: { [permissionsList.PEGA_VARIABLE_MAPPING_MANAGE]: true,
      [permissionsList.PEGA_VARIABLE_MAPPING_REVIEW]: true,
      [permissionsList.PEGA_VARIABLE_MAPPING_VIEW]: true,
      [permissionsList.PEGA_VARIABLE_MAPPING_APPROVE]: true,

    },
    sid: '123' },
    users: mockUsers,
    variableMapping: mockVars,
    // redux-mock-store does not support reducers
    // so state changes due to UI interaction need to be loaded here
    ...options.initialState,
  });
  const history = options.history ? options.history : createBrowserHistory();
  !options.history && history.push('./active');
  return render(
    <Provider store={store}>
      <Router history={history}>
        <VariableMappingActive/>
      </Router>
    </Provider>
  );
};

describe('VariableMappingActive', () => {
  beforeEach(() => {
    mockAxios.reset();
  });

  afterAll(() => {
    mockAxios.reset();
  });

  it('should render page correctly', async() => {
    const page = renderPage();
    expect(page).toMatchSnapshot();
  });

  // all test cases involving quickly closing a modal after opening cannot be tested right now
  // test case cannot be executed due to canvas-core-react bug involving modal dialogues
  // when modal opens by clicking cancel button, it requires a moment to complete opening animation
  // if modal is closed immediately before this happens, an exception will be throw, breaking the test
  // this test case cannot be executed until either this bug is fixed in canvas or
  // until there is a way, only inside unit tests, to be able to wait between opening the cancel modal
  // and dismissing it by clicking on continue button
  it.todo('should allow changes to be cancelled');
  // it('should allow changes to be cancelled', async() => {
  //   const initialState = { variableMapping: { ...mockVars, editedSet: mockVars.activeSet } };
  //   const { findByText } = renderPage({ initialState });

  //   // open edit view
  //   fireEvent.click(await findByText('Edit'));
  //   expect(await findByText('Edit Variables')).toBeInTheDocument();

  //   // cancel and re-enter edit view
  //   fireEvent.click(await findByText('Cancel'));
  //   expect(await findByText('Are you sure you want to Cancel?')).toBeInTheDocument();
  //   fireEvent.click(await findByText('Continue'));
  //   await Promise.resolve(() => setTimeout(() => {}, 5000));
  //   expect(await findByText('Active')).toBeInTheDocument();
  //   fireEvent.click(await findByText('Edit'));
  //   expect(await findByText('Edit Variables')).toBeInTheDocument();;
  // });

  it('should allow variable set to be edited, validated, and reviewed', async() => {
    const mockRuleRes = {
      variable_set_id: 123,
      created_at: '2021-12-10T19:31:45.321Z',
      created_by: 's1990028',
      status: 'draft',
      updated_at: null,
      updated_by: null,
      approver_sid: 's1990028',
      variables: [
        {
          variable_template: 'SOLUI_TEST_END',
          variable_campaign: 'test',
          variable_type: 'date',
        },
      ],
    };

    // mock submit 1 of 2: save draft
    mockAxios.put.mockImplementationOnce(() => Promise.resolve({ data: { data: {
      ...mockRuleRes,
      updated_at: '2021-12-10T19:31:45.377Z',
      updated_by: 's1990028',
    } } }));

    // mock submit 2 of 2: update status to pending
    mockAxios.put.mockImplementationOnce(() => Promise.resolve({ data: { data: {
      ...mockRuleRes,
      updated_at: '2021-12-10T19:31:45.377Z',
      updated_by: 's1990028',
      status: 'pending',
    } } }));

    const initialState = { variableMapping: { ...mockVars, editedSet: mockVars.activeSet } };
    const { queryByText, getByText, getByTestId, getAllByTestId } = renderPage({ initialState });

    // click edit button to open edit view
    fireEvent.click(getByText('Edit'));
    expect(queryByText('Edit Variables')).toBeInTheDocument();

    // test validation for no change made
    fireEvent.click(getByText('Review'));
    expect(queryByText('No change to save or review')).toBeInTheDocument();

    // change variable format from text to date
    const getVarTypeSelected = async type => {
      const dropDownMenuItem = getByTestId(`format-select-option_${type}`);
      return !!dropDownMenuItem?.selected;
    };
    expect(await getVarTypeSelected('date')).toBeFalsy();
    expect(await getVarTypeSelected('text')).toBeTruthy();
    fireEvent.change(getByTestId('format-select'), { target: { value: 'date' } });
    expect(await getVarTypeSelected('date')).toBeTruthy();
    expect(await getVarTypeSelected('text')).toBeFalsy();

    // test validation on empty row
    fireEvent.click(getByText('Add row'));
    fireEvent.click(getByText('Review'));
    expect(queryByText('Variable name is required')).toBeInTheDocument();
    fireEvent.click(await getAllByTestId('delete-row-button')[1]);

    // test validation on description box
    fireEvent.change(getByTestId('draftCommentInput'), { target: { value: null } });
    fireEvent.click(getByText('Review'));
    expect(getByText('Description is required')).toBeInTheDocument();
    fireEvent.change(getByTestId('draftCommentInput'), { target: { value: 'desc123' } });

    // proceed to review
    fireEvent.click(getByText('Review'));
    expect(queryByText('Review Changes')).toBeInTheDocument();

    // click edit button to see if we can go back to edit page and come back
    fireEvent.click(getByText('Edit'));
    expect(queryByText('Edit Variables')).toBeInTheDocument();

    // setup test data again since state store isn't updated due to redux-mock-store limitations
    fireEvent.change(getByTestId('format-select'), { target: { value: 'date' } });

    // return to review
    fireEvent.click(getByText('Review'));
    expect(queryByText('Review Changes')).toBeInTheDocument();
  });

  it('should allow changes to be saved as draft', async() => {
    const history = createBrowserHistory();
    history.push('./active');
    const initialState = { variableMapping: { ...mockVars, editedSet: mockDeltaSet } };
    const { getByText, findByText, queryByText } = renderPage({ initialState, history });

    // open edit view
    fireEvent.click(getByText('Edit'));
    expect(queryByText('Edit Variables')).toBeInTheDocument();

    // changes already populated in state

    // click submit
    fireEvent.click(getByText('Save as Draft'));
    await findByText('Save as Draft'); // use testing library to wait for UI interaction to complete, act() doesn't work
    expect(history.location.pathname).toStrictEqual('/draft');
  });

  it('should allow changes to be submitted for approval', async() => {
    const history = createBrowserHistory();
    history.push('./active');
    const initialState = { variableMapping: { ...mockVars, editedSet: mockDeltaSet } };
    const { queryByText, getByText } = renderPage({ initialState, history });

    // open edit view
    fireEvent.click(getByText('Edit'));
    expect(queryByText('Edit Variables')).toBeInTheDocument();

    // changes already populated in state, can immediately reivew and submit for approval
    fireEvent.click(getByText('Review'));
    expect(queryByText('Review Changes')).toBeInTheDocument();
    fireEvent.click(getByText('Submit for Approval'));
    expect(queryByText('Select a person to approve these changes')).toBeInTheDocument();

    // react warning may appear here sugguesting .toLowerCase is not a function
    // this is due to canvas modal component only officially suports text content
    // however legacy requirement and code for admin requires drop down menu for assignee selection
    // this drop down can be moved out onto the review page in the future

    // // select an assignee
    // const mockApproverSid = mockUsers.items['1'].sid;
    // expect(getByTestId(`approver-selector-option_${mockApproverSid}`).selected).toBeFalsy();
    // fireEvent.change(getByTestId('approver-selector').querySelector('select'), { target: { value: mockApproverSid } });
    // expect(getByTestId(`approver-selector-option_${mockApproverSid}`).selected).toBeTruthy();

    // // click submit
    // fireEvent.click(getByText('Submit'));
    // // use testing library to wait for UI interaction to complete, act() doesn't work
    // getByText('Submit for Approval');
    // expect(history.location.pathname).toStrictEqual('/pending');
  });

  it('should handle existing draft', async() => {
    const history = createBrowserHistory();
    history.push('./active');
    const initialState = { variableMapping: { ...mockVars, draftSet: mockDeltaSet, hasDraft: true } };
    // const { container, getByText, queryByText } = renderPage({ initialState, history });
    const { container } = renderPage({ initialState, history });
    expect(container).toMatchSnapshot();

    // // open draft warning modal by attempting to edit vars
    // fireEvent.click(getByText('Edit'));
    // expect(queryByText('You can only edit one draft set at a time')).toBeInTheDocument();

    // // accept warning
    // fireEvent.click(getByText('View Draft Set'));
    // expect(history.location.pathname).toStrictEqual('/draft');
  });

  it('should handle existing pending set', async() => {
    const history = createBrowserHistory();
    history.push('./active');
    const initialState = { variableMapping: { ...mockVars, pendingSet: mockDeltaSet, hasPending: true } };
    const { getByText, queryByText } = renderPage({ initialState, history });

    // open pending warning modal by attempting to edit vars
    fireEvent.click(getByText('Edit'));
    setTimeout(() => {
      expect(queryByText('You can only edit one variable set at a time')).toBeInTheDocument();
      // accept warning
      fireEvent.click(getByText('View Pending Set'));
      expect(history.location.pathname).toStrictEqual('/pending');
    }, 1000);
  });
});
