import { isEmpty } from 'ramda';

export const calculateDiff = (oldSet, newSet) => {
  let _changes = [];
  if (isEmpty(newSet)) return _changes; // new set is not empty, it doesn't exists, so don't diff

  const oldVars = oldSet.variables || [];
  const newVars = newSet.variables || [];
  // find deleted
  oldVars.forEach((v) => {
    let found = newVars.find((f) => f.variable_campaign === v.variable_campaign);
    if (!found) {
      _changes.push({ name: v.variable_campaign, template: v.variable_template, format: v.variable_type, type: 'd' });
    }
  });

  // find new and modified
  newVars.forEach((v) => {
    let found = oldVars.find((f) => f.variable_campaign === v.variable_campaign);
    if (!found) {
      _changes.push({ name: v.variable_campaign, template: v.variable_template, format: v.variable_type, type: 'n' });
    } else if (v.variable_type !== found.variable_type) {
      _changes.push({ name: v.variable_campaign, template: v.variable_template, format: v.variable_type, oldFormat: found.variable_type, type: 'm' });
    }
  });
  return _changes;
};
