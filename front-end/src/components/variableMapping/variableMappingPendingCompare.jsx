import React, { useState, useEffect } from 'react';
import { useSelector, connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import moment from 'moment';
import { useHistory } from 'react-router-dom';

import Table from 'canvas-core-react/lib/Table';
import CanvasCard from 'canvas-core-react/lib/Card';
import TextButton from 'canvas-core-react/lib/TextButton';
import IconEdit from 'canvas-core-react/lib/IconEdit';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';

import { calculateDiff } from './calculate-diff';
import typeMap from './typeMap';
import {
  getVariableMapping,
  rejectPendingSet,
  approvePendingSet,
  updateApprover,
} from '../../store/actions/variable-mapping';
import { getUsers } from '../../store/actions/users';
import VariableMappingApproverPopup from './variableMappingApproverPopup';
import permissionsList from '../../constants/permissionsList';

const actionMap = {
  d: 'Deleted',
  m: 'Modified',
  n: 'Added',
};

const VariableMappingPending = ({
  getVariableMapping,
  rejectPendingSet,
  approvePendingSet,
  isLoading,
  hasPending,
  activeSet,
  pendingSet,
  users,
  getUsers,
  updateApprover,
}) => {
  const [ changes, setChanges ] = useState([]);
  const [ approvePrompt, setApprovePrompt ] = useState(false);
  const [ rejectPrompt, setRejectPrompt ] = useState(false);
  const [ isSubmitter, setIsSubmitter ] = useState(false);
  const [ assignedBy, setAssignedBy ] = useState();
  const [ approver, setApprover ] = useState();
  const [ assignedDate, setAssignedDate ] = useState();
  const [ approverPrompt, setApproverPrompt ] = useState(false);

  const sid = useSelector(state => state.authenticated.sid);
  const permissions = useSelector(state => state.authenticated.permissions);
  const isAdmin = permissions && permissions.admin;
  const canUpdate = permissions && (permissions[permissionsList.PEGA_VARIABLE_MAPPING_MANAGE] || permissions[permissionsList.KT_VARIABLE_MAPPING_MANAGE] || permissions.admin);
  const canApprove = permissions && (permissions[permissionsList.PEGA_VARIABLE_MAPPING_APPROVE] || permissions[permissionsList.KT_VARIABLE_MAPPING_APPROVE] || permissions.admin);
  const history = useHistory();

  useEffect(() => {
    const init = async() => {
      getVariableMapping({ status: 'pending' });
      await getUsers();
    };
    init();
  }, []);

  useEffect(() => {
    if (users.isLoading === false && hasPending && users.items) {
      const diff = calculateDiff(activeSet, pendingSet);
      setChanges(diff);
      setIsSubmitter(pendingSet.updated_by === sid);
      setAssignedDate(moment(pendingSet.updated_at).format('LLL'));
      const userList = Object.values(users.items);
      const userBy = userList.find((u) => u.sid === pendingSet.updated_by);
      setAssignedBy(userBy);
      const userTo = userList.find((u) => u.sid === pendingSet.approver_sid);
      setApprover(userTo);
    }
  }, [ activeSet, pendingSet, users ]);

  const onApproveAbort = () => {
    setApprovePrompt(false);
  };

  const onApproveContinue = async() => {
    setApprovePrompt(false);
    await approvePendingSet(pendingSet);
    history.push('./active');
  };

  const onRejectAbort = () => {
    setRejectPrompt(false);
  };

  const onRejectContinue = async() => {
    setRejectPrompt(false);
    await rejectPendingSet(pendingSet);
    history.push('./draft');
  };

  const onEdit = () => {
    setApproverPrompt(true);
  };

  const editApprover = async(approver) => {
    await updateApprover(approver.sid);
    setApprover(approver);
    setApproverPrompt(false);
  };

  const formatCellFormatter = (rowData) => {
    return (
      <>
        { rowData.oldFormat &&
          <span className="old-mapping-format">
            { typeMap[rowData.oldFormat] }
          </span>
        }
        { typeMap[rowData.format] }
      </>
    );
  };

  const statusCellFormatter = (rowData) => {
    return (
      <div className={`text-${rowData.type}`}>{ actionMap[rowData.type] }</div>
    );
  };

  const columnDefinition = [
    {
      name: 'Source Name',
      selector: 'name',
      sortable: true,
      grow: 2,
    },
    {
      name: 'Presentation Name',
      selector: 'template',
      sortable: true,
      grow: 2.5,
    },
    {
      name: 'Format',
      selector: 'format',
      sortable: true,
      cellFormatter: formatCellFormatter,
      grow: 1.5,
    },
    {
      name: 'Status',
      selector: 'status',
      cellFormatter: statusCellFormatter,
    },
  ];
  return (
    <div id='variable-mapping'>
      <div className="details__action-bar">
        <TextHeadline component='h1' data-testid="pending-set-title">Pending</TextHeadline>
      </div>
      { assignedBy && <CanvasCard className="pending-metadata-card">
        <span><span className="assigned-to">Assigned to:</span> { approver?.name }</span>
        { (isSubmitter || isAdmin) && <TextButton Icon={IconEdit} onClick={onEdit}>
          Edit
        </TextButton>
        }
        <span><span className="assigned-by">Assigned by:</span> { assignedBy?.name }</span>
        <span><span className="assigned-at">Date & Time:</span> { assignedDate }</span>
      </CanvasCard> }
      <CanvasCard>
        { isLoading && <div>Loading mapping set...</div> }
        { !isLoading && changes.length > 0 &&
          <Table id="pending_variable_set_table" title="" columns={columnDefinition} data={changes} striped />
        }
        { !isLoading && changes.length === 0 &&
          <div className="centered">There are no pending variable sets</div>
        }
      </CanvasCard>

      { !isLoading && pendingSet?.description && <CanvasCard>
        <h5>Comments</h5>
        <div>{ pendingSet?.description }</div>
      </CanvasCard> }

      { !isLoading && changes.length > 0 &&
        <div className="details__action-bar right">
          <div className="details__action-buttons">
            { ((approver?.sid === sid)) &&
              <>
                <SecondaryButton
                  className="details__action-button"
                  onClick={() => setRejectPrompt(true)}
                  data-testid="reject-button"
                  disabled={isSubmitter ? !canUpdate : !canApprove}
                >
                  { isSubmitter ? 'Unsubmit' : 'Reject' }
                </SecondaryButton>
                <PrimaryButton
                  className="details__action-button"
                  onClick={() => setApprovePrompt(true)}
                  data-testid="approve-button"
                  disabled={!canApprove}
                >
                    Approve
                </PrimaryButton>
              </>
            }
          </div>
        </div>
      }

      <ModalDialogue
        headline={`Are you sure you want to ${isSubmitter ? 'Unsubmit' : 'Reject'}?`}
        isModalVisible={rejectPrompt}
        primaryAction={onRejectContinue}
        primaryButtonLabel={isSubmitter ? 'Unsubmit' : 'Reject'}
        secondaryAction={onRejectAbort}
        secondaryButtonLabel="Cancel"
        setModalVisible={setRejectPrompt}
        data-testid="unsubmit-confirm-modal"
      >
        This set will be saved as a draft
      </ModalDialogue>

      <ModalDialogue
        headline="Are you sure you want to Approve?"
        isModalVisible={approvePrompt}
        primaryAction={onApproveContinue}
        primaryButtonLabel="Approve"
        secondaryAction={onApproveAbort}
        secondaryButtonLabel="Cancel"
        setModalVisible={setApprovePrompt}
        data-testid="approve-confirm-modal"
      >
        This will update the set in production
      </ModalDialogue>

      { users.isLoading === false &&
        <VariableMappingApproverPopup
          isOpen={approverPrompt}
          approver={approver}
          onCancel={() => setApproverPrompt(false)}
          onConfirm={editApprover}
        />
      }
    </div>
  );
};

VariableMappingPending.propTypes = {
  getVariableMapping: PropTypes.func.isRequired,
  rejectPendingSet: PropTypes.func.isRequired,
  approvePendingSet: PropTypes.func.isRequired,
  isLoading: PropTypes.bool,
  hasPending: PropTypes.bool,
  activeSet: PropTypes.object,
  pendingSet: PropTypes.object,
  users: PropTypes.object,
  getUsers: PropTypes.func.isRequired,
  updateApprover: PropTypes.func,
};

const mapStateToProps = state => ({
  isLoading: state.variableMapping.isLoading,
  hasPending: state.variableMapping.hasPending,
  activeSet: state.variableMapping.activeSet,
  pendingSet: state.variableMapping.pendingSet,
  users: state.users,
});

const mapDispatchToProps = dispatch => bindActionCreators({
  getVariableMapping,
  rejectPendingSet,
  approvePendingSet,
  getUsers,
  updateApprover,
}, dispatch);

export default connect(mapStateToProps, mapDispatchToProps)(VariableMappingPending);
