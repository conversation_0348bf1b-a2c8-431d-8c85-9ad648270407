import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import _ from 'lodash';

import CanvasCard from 'canvas-core-react/lib/Card';
import TextButton from 'canvas-core-react/lib/TextButton';
import BackButton from 'canvas-core-react/lib/BackButton';
import TextField from 'canvas-core-react/lib/TextField';
import Selector from 'canvas-core-react/lib/Selector';
import IconAddOutline from 'canvas-core-react/lib/IconAddOutline';
import IconDelete from 'canvas-core-react/lib/IconDelete';
import IconRefresh from 'canvas-core-react/lib/IconRefresh';
import SnackBar from 'canvas-core-react/lib/SnackBar';
import AlertBanner from 'canvas-core-react/lib/AlertBanner';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import <PERSON><PERSON>rea from 'canvas-core-react/lib/TextArea';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import Table from 'canvas-core-react/lib/Table';

import { getTypes } from '../../store/actions/variable-mapping';
import typeMap from './typeMap';

// allows for making changes to an arbitrary set (i.e draft, active set or a new set)
// three actions allowed:
// cancel -> noop, go back where you came from
// save as draft -> save set as a draft
// review -> proceed to review screen without saving as draft

const VariableMappingEdit = ({
  onCancel,
  onReview,
  onSaveAsDraft,
  types = [],
  getTypes, // to map variable type name to readable description
  activeSet,
  editedSet,
  canReview,
}) => {
  // Mapping set must be cloned with indexes b/c currently
  // Canvas Table cell formatters do not receive row index
  const cloneWithIndexes = (arr) => {
    return arr.map((item, index) => {
      return {
        ...item,
        index,
      };
    });
  };
  const [ newMappingSet, setNewMappingSet ] = useState(editedSet);
  const [ cancelPrompt, setCancelPrompt ] = useState(false);
  const [ deleteAlertOpen, setDeleteAlertOpen ] = useState(false);
  const [ error, setError ] = useState(false);
  const [ descriptionError, setDescriptionError ] = useState(false);

  useEffect(() => {
    setNewMappingSet(editedSet);
    types.length === 0 && getTypes();
  }, []);

  const onCommentChange = (value) => {
    setNewMappingSet({
      ...newMappingSet,
      description: value || undefined,
    });
  };

  const onNameChange = (value, index) => {
    const newList = newMappingSet.variables.map((item, i) => {
      if (index === i) {
        const updatedItem = {
          ...item,
          variable_campaign: value,
          variable_template: `SOLUI_${value}_END`.toUpperCase(),
        };
        return updatedItem;
      }
      return item;
    });
    setNewMappingSet({
      ...newMappingSet,
      variables: newList,
    });
  };

  const onFormatChange = (value, index) => {
    const newList = newMappingSet.variables.map((item, i) => {
      if (index === i) {
        const updatedItem = {
          ...item,
          variable_type: value,
          variable_type_label: typeMap[value],
        };
        return updatedItem;
      }
      return item;
    });
    setNewMappingSet({
      ...newMappingSet,
      variables: newList,
    });
  };

  const onAdd = () => {
    const newList = [ ...newMappingSet.variables ];
    newList.push({ variable_campaign: '', variable_template: '' });
    setNewMappingSet({
      ...newMappingSet,
      variables: newList,
    });
  };

  const onDelete = (index) => {
    const newList = [ ...newMappingSet.variables ];
    const removed = newList.splice(index, 1);
    setNewMappingSet({
      ...newMappingSet,
      variables: newList,
    });
    setDeleteAlertOpen(removed[0]);
    setTimeout(() => setDeleteAlertOpen(false), 3000);
  };

  const onUndoDelete = () => {
    setDeleteAlertOpen(false);
    const newList = [ ...newMappingSet.variables ];
    newList.push(deleteAlertOpen);
    setNewMappingSet({
      ...newMappingSet,
      variables: newList,
    });
  };

  const regex = new RegExp('^[a-z0-9\\-_]{1,50}$');

  const validate = () => {
    const err = {};
    if (_.isEqual(newMappingSet.variables, activeSet.variables)) {
      err.text = `No change to save or review`;
    }
    newMappingSet.variables.forEach((v, i) => {
      if (v.variable_campaign.length === 0) {
        err.text = `Variable name is required`;
        err.type = 'name';
        return false;
      }
      if (!regex.test(v.variable_campaign)) {
        err.text = `${v.variable_campaign} is not a valid name`;
        err.type = 'name';
        return false;
      }
      const firstChar = v.variable_campaign[0];
      const lastChar = v.variable_campaign[v.variable_campaign.length - 1];
      if (firstChar === '_' || firstChar === '-' || lastChar === '_' || lastChar === '-') {
        err.text = `${v.variable_campaign} must begin and end with a letter`;
        err.type = 'name';
        return false;
      }
      if (v.variable_type === undefined) {
        err.text = `${v.variable_campaign} must have a type`;
        err.type = 'type';
        return false;
      }

      newMappingSet.variables.forEach((vc, ic) => {
        if ((vc.variable_campaign === v.variable_campaign) && (i !== ic)) {
          err.text = `${v.variable_campaign} has duplicates. Names must be unique.`;
          err.type = 'name';
          return false;
        }
      });
    });
    if (err.text) {
      setError(err);
      if (!newMappingSet.description) setDescriptionError(true);
      return err;
    } else {
      setError(false);
    }
    if (!newMappingSet.description) {
      setDescriptionError(true);
      return true;
    }
  };

  const saveAsDraft = () => {
    const err = validate();
    if (err) return;
    onSaveAsDraft(newMappingSet);
  };

  const review = () => {
    const err = validate();
    if (err) return;
    onReview(newMappingSet);
  };

  const nameCellFormatter = (rowData) => {
    return (
      <TextField
        label=""
        placeholder="Enter source name"
        id={'text-field-name' + rowData.index}
        value={rowData.variable_campaign}
        onChange={e => onNameChange(e.target.value, rowData.index)}
      />
    );
  };

  const presentationCellFormatter = (rowData) => {
    return (
      <TextField
        label=''
        className="Input__input"
        placeholder="Presentation name"
        id={'text-field-presentation' + rowData.index}
        value={rowData.variable_template}
        disabled
      />
    );
  };

  const formatCellFormatter = (rowData) => {
    return (
      <Selector
        id={'type' + rowData.index}
        name={'type' + rowData.index}
        label=""
        placeholder="Select format"
        value={rowData.variable_type}
        onChange={e => onFormatChange(e.target.value, rowData.index)}
        data-testid='format-select'
      >
        { types.map((v) => (
          <option value={v.name} key={v.id} data-testid={`format-select-option_${v.name}`}> { v.description } </option>
        )) }
      </Selector>
    );
  };

  const deleteCellFormatter = (rowData) => {
    return (
      <TextButton
        name='delete'
        Icon={IconDelete}
        onClick={() => onDelete(rowData.index)}
        data-testid='delete-row-button'
      >&nbsp;</TextButton>
    );
  };

  const columnDefinition = [
    {
      name: 'Source Name',
      selector: 'variable_campaign',
      sortable: true,
      cellFormatter: nameCellFormatter,
      grow: 2,
    },
    {
      name: 'Presentation Name',
      selector: 'variable_template',
      sortable: true,
      cellFormatter: presentationCellFormatter,
      grow: 2.5,
    },
    {
      name: 'Format',
      selector: 'variable_type_label',
      sortable: true,
      cellFormatter: formatCellFormatter,
      grow: 1.5,
    },
    {
      name: 'Delete',
      selector: 'delete',
      cellFormatter: deleteCellFormatter,
    },
  ];

  return (
    <div id='variable-mapping'>
      <div className="details__action-bar">
        <TextHeadline component='h1'>Edit Variables</TextHeadline>
      </div>
      <CanvasCard>
        { types && newMappingSet && newMappingSet.variables && newMappingSet.variables.length > 0 &&
          <Table
            id="edit_draft_variable_set_table"
            title=""
            columns={columnDefinition}
            data={cloneWithIndexes(newMappingSet.variables)}
            striped
          />
        }
        <TextButton Icon={IconAddOutline} onClick={onAdd}>
          Add row
        </TextButton>
        { error && <AlertBanner type="error">
          <TextSubtitle component="div" type="2">{ error.text }</TextSubtitle>
          { error && error.type === 'name' &&
          <>
            <div>1 to 50 characters</div>
            <div>only lowercase letters, - and _</div>
            <div>must begin and end with a letter</div>
          </>
          }
        </AlertBanner> }
      </CanvasCard>

      <CanvasCard>
        <TextArea
          id="draftCommentInput"
          data-testid="draftCommentInput"
          label="Description"
          placeholder="Describe the changes that you made"
          rows={3}
          limit={300}
          maxLength={300}
          value={newMappingSet?.description}
          onChange={(e) => onCommentChange(e.target.value)}
        />
        { descriptionError &&
          <AlertBanner type="error" className="details__description-error">
            <TextSubtitle component="div" type="2">Description is required</TextSubtitle>
          </AlertBanner>
        }
      </CanvasCard>

      <div className="details__action-bar">
        <BackButton
          onClick={() => setCancelPrompt(true)}
          data-testid="cancel-button">
            Cancel
        </BackButton>
        <div className="details__action-buttons">
          <SecondaryButton
            className="details__action-button"
            data-testid="save-button"
            onClick={saveAsDraft}>
              Save as Draft
          </SecondaryButton>
          <PrimaryButton
            className="details__action-button"
            data-testid="review-button"
            disabled={!canReview}
            onClick={review}>
              Review
          </PrimaryButton>
        </div>
      </div>

      <ModalDialogue
        headline="Are you sure you want to Cancel?"
        isModalVisible={cancelPrompt}
        primaryAction={onCancel}
        primaryButtonLabel="Continue"
        secondaryAction={() => setCancelPrompt(false)}
        secondaryButtonLabel="Cancel"
        setModalVisible={setCancelPrompt}
        data-testid="cancel-confirm-modal"
      >
        Unsaved changes will be lost.
      </ModalDialogue>

      <SnackBar
        open={!!deleteAlertOpen}
        onClose={() => setDeleteAlertOpen(false)}
        button={{
          text: 'Undo',
          onClick: onUndoDelete,
          Icon: IconRefresh,
        }}
      >{ deleteAlertOpen.variable_campaign } was deleted.</SnackBar>

    </div>
  );
};

VariableMappingEdit.propTypes = {
  activeSet: PropTypes.object.isRequired,
  editedSet: PropTypes.object.isRequired,
  onCancel: PropTypes.func,
  onReview: PropTypes.func,
  onSaveAsDraft: PropTypes.func.isRequired,
  types: PropTypes.array,
  getTypes: PropTypes.func,
  canReview: PropTypes.bool.isRequired,
};

const mapStateToProps = state => ({
  types: state.variableMapping.types,
  activeSet: { ...state.variableMapping.activeSet, variable_set_id: undefined },
  editedSet: state.variableMapping.editedSet,
});

const mapDispatchToProps = dispatch => bindActionCreators({
  getTypes,
}, dispatch);

export default connect(mapStateToProps, mapDispatchToProps)(VariableMappingEdit);
