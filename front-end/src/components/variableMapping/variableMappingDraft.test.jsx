import React from 'react';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { Provider } from 'react-redux';
import VariableMappingDraft from './variableMappingDraft';
import { render, fireEvent, waitFor, within } from '@testing-library/react';
import { Router } from 'react-router-dom';
import { createBrowserHistory } from 'history';
import mockAxios from 'axios';

const mockStore = configureMockStore([ thunk ]);
const mockUsers = {
  items: {
    '1': {
      id: 1,
      name: 'Test Admin',
      email: '<EMAIL>',
      sid: 's1990028',
      active: true,
      roles: [ 1 ],
    },
  },
  isLoading: false,
};
const mockVars = {
  isLoading: false,
  activeSet: {
    variable_set_id: 107,
    created_at: '2021-12-08T20:36:27.337Z',
    created_by: 's1990028',
    status: 'active',
    updated_at: '2021-12-09T21:29:50.130Z',
    updated_by: 's1990028',
    description: 'desc',
    approver_sid: 's1990028',
    variables: [
      {
        variable_campaign: 'testmod',
        variable_template: 'SOLUI_TESTMOD_END',
        variable_type: 'text',
        variable_type_label: 'Text',
      },
      {
        variable_campaign: 'testdelete',
        variable_template: 'SOLUI_TESTDELETED_END',
        variable_type: 'text',
        variable_type_label: 'Text',
      },
    ],
  },
  draftSet: {
    variable_set_id: 124,
    created_at: '2021-12-11T21:19:17.548Z',
    created_by: 's1990028',
    status: 'draft',
    updated_at: null,
    updated_by: null,
    description: 'test',
    approver_sid: 's1990028',
    variables: [
      {
        variable_campaign: 'testmod',
        variable_template: 'SOLUI_TESTMOD_END',
        variable_type: 'date',
        variable_type_label: 'Date',
      },
      {
        variable_campaign: 'testadd',
        variable_template: 'SOLUI_TESTADD_END',
        variable_type: 'date',
        variable_type_label: 'Date',
      },
    ],
  },
  hasDraft: true,
  types: [
    { id: 1, name: 'date', description: 'Date (MMDDYYY)' },
    { id: 2, name: 'text', description: 'Text' },
    { id: 3, name: 'currency', description: 'Currency' },
    { id: 4, name: 'account-number-mask', description: 'Masked Account Number' },
    { id: 5, name: 'number', description: 'Number' },
    { id: 6, name: 'account', description: 'Account Number' },
    { id: 7, name: 'gic-special-term', description: 'GIC Special Term' },
    { id: 8, name: 'gic-special-rate', description: 'GIC Special Rate' },
  ],
  approvers: [ mockUsers.items['1'] ],
  pendingSet: {},
  hasPending: false,
};
mockVars.editedSet = { ...mockVars.draftSet, variables: [ ...mockVars.draftSet.variables ] };

const renderPage = (options = {}) => {
  const store = mockStore({
    authenticated: {
      permissions: {
        pega_variable_mapping_view: true,
        pega_variable_mapping_manage: true,
        pega_variable_mapping_review: true,
        pega_variable_mapping_approve: true,
      },
      sid: '123',
    },
    users: mockUsers,
    variableMapping: mockVars,
    // redux-mock-store does not support reducers
    // so state changes due to UI interaction need to be loaded here
    ...options.initialState,
  });
  const history = options.history ? options.history : createBrowserHistory();
  !options.history && history.push('./active') && history.push('./draft');
  return render(
    <Provider store={store}>
      <Router history={history}>
        <VariableMappingDraft/>
      </Router>
    </Provider>
  );
};

describe('VariableMappingDraft', () => {
  beforeEach(() => {
    mockAxios.reset();
  });

  afterAll(() => {
    mockAxios.reset();
  });

  it('should render page with default data', async() => {
    const rendered = renderPage();

    // shows added/deleted/updated changes from active set
    // explicitly test these in case someone changes snapshot without verifying
    expect(await rendered.findAllByText('testadd')).toHaveLength(1);
    expect(await rendered.findAllByText('testmod')).toHaveLength(1);
    expect(await rendered.findAllByText('testdelete')).toHaveLength(1);
    expect(await rendered.findAllByText('Added')).toHaveLength(1);
    expect(await rendered.findAllByText('Modified')).toHaveLength(1);
    expect(await rendered.findAllByText('Deleted')).toHaveLength(1);

    // expect(rendered).toMatchSnapshot();
  });

  it('go to active tab onDelete', async() => {
    const history = createBrowserHistory();
    const { findByTestId } = renderPage({ history });
    fireEvent.click(await findByTestId('delete-button'));
    const modal = await findByTestId('delete-confirm-modal');
    expect(modal).toBeInTheDocument();
    // modal test cases cannot be tested due to canvas modal bug
    fireEvent.click(await within(modal).getByText('Delete'));
    await waitFor(() => expect(history.location.pathname).toStrictEqual('/active'));
  });

  it.todo('go to pending tab on approve');
  // it('go to pending tab on approve', async() => {
  //   const mockRuleRes = {
  //     variable_set_id: 123,
  //     created_at: '2021-12-10T19:31:45.321Z',
  //     created_by: 's1990028',
  //     status: 'draft',
  //     updated_at: null,
  //     updated_by: null,
  //     approver_sid: 's1990028',
  //     variables: [
  //       {
  //         variable_template: 'SOLUI_TEST_END',
  //         variable_campaign: 'test',
  //         variable_type: 'date',
  //       },
  //     ],
  //   };

  //   // mock submit 1 of 2: save draft
  //   mockAxios.put.mockImplementationOnce(() => Promise.resolve({ data: { data: {
  //     ...mockRuleRes,
  //     updated_at: '2021-12-10T19:31:45.377Z',
  //     updated_by: 's1990028',
  //   } } }));

  //   // mock submit 2 of 2: update status to pending
  //   mockAxios.put.mockImplementationOnce(() => Promise.resolve({ data: { data: {
  //     ...mockRuleRes,
  //     updated_at: '2021-12-10T19:31:45.377Z',
  //     updated_by: 's1990028',
  //     status: 'pending',
  //   } } }));

  //   const history = createBrowserHistory();
  //   history.push('./active');
  //   history.push('./draft');
  //   const { findByText, getByTestId } = renderPage({ history });

  //   // open edit view
  //   fireEvent.click(await findByText('Edit'));
  //   expect(await findByText('Edit Variables')).toBeInTheDocument();
  //   fireEvent.click(await findByText('Review'));
  //   expect(await findByText('Review Changes')).toBeInTheDocument();
  //   fireEvent.click(await findByText('Edit')); // test ability to re-enter edit mode
  //   expect(await findByText('Edit Variables')).toBeInTheDocument();

  //   // changes already populated in state, can immediately reivew and submit for approval
  //   fireEvent.click(await findByText('Review'));
  //   expect(await findByText('Review Changes')).toBeInTheDocument();
  //   fireEvent.click(await findByText('Submit for Approval'));
  //   expect(await findByText('Select a person to approve these changes')).toBeInTheDocument();

  //   // react warning may appear here sugguesting .toLowerCase is not a function
  //   // this is due to canvas modal component only officially suports text content
  //   // however legacy requirement and code for admin requires drop down menu for assignee selection
  //   // this drop down can be moved out onto the review page in the future

  //   // select an assignee
  //   const mockApproverSid = mockUsers.items['1'].sid;
  //   expect(getByTestId(`approver-selector-option_${mockApproverSid}`).selected).toBeFalsy();
  //   fireEvent.change(getByTestId('approver-selector').querySelector('select'), { target: { value: mockApproverSid } });
  //   expect(getByTestId(`approver-selector-option_${mockApproverSid}`).selected).toBeTruthy();

  //   // click submit
  //   fireEvent.click(await findByText('Submit'));
  //   // use testing library to wait for UI interaction to complete, act() doesn't work
  //   await findByText('Submit for Approval');
  //   expect(history.location.pathname).toStrictEqual('/pending');
  // });
});
