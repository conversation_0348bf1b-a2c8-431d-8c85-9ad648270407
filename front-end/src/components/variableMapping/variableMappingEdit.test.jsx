// import React from 'react';
// import configureMockStore from 'redux-mock-store';
// import thunk from 'redux-thunk';
// import { Provider } from 'react-redux';
// import VariableMappingEdit from './variableMappingEdit';
// import { render, fireEvent } from '@testing-library/react';
// import { MockApi } from './mockApi';

// const mockStore = configureMockStore([ thunk ], { authenticated: { variableMapping: {} } });
// // let mockApi;

describe('VariableMappingEdit', () => {
  document.getElementById = () => ({
    focus: jest.fn(),
  });

  // const testSet = {
  //   variables: [
  //     {
  //       variable_campaign: 'test',
  //       variable_template: 'test',
  //       variable_type: 'text',
  //     },
  //   ],
  // };

  // const store = mockStore({
  //   authenticated: { permissions: {}, sid: '123' },
  //   variableMapping: { activeSet: { mappingSet: { variables: testSet.variables }, variables: [] } },
  // });

  // beforeEach(() => {
  //   mockApi = MockApi();
  // });

  it.todo('should remove deleted row when delete button is clicked');
  // it('should remove deleted row when delete button is clicked', async() => {
  //   const { findAllByRole } = render(
  //     <Provider store={store}>
  //       <VariableMappingEdit
  //         mappingSet={testSet}
  //         onSaved={jest.fn()}
  //         onCancel={jest.fn()}
  //         onReview={jest.fn()}
  //         api={mockApi}
  //       />
  //     </Provider>
  //   );
  //   // cannot query by name, possible cause is testing lib version too old
  //   // https://github.com/testing-library/react-testing-library/issues/636
  //   let deleteBtn = (await findAllByRole('button')).find(b => b.name === 'delete');
  //   fireEvent.click(deleteBtn);
  //   deleteBtn = (await findAllByRole('button')).find(b => b.name === 'delete');
  //   expect(deleteBtn).toBeUndefined();
  // });

  it.todo('validates errors');
  // it('validates errors', async() => {
  //   const invalidTestSet = {
  //     variables: [
  //       {
  //         variable_campaign: 'test*&',
  //         variable_template: 'test*&',
  //         variable_type: 'text',
  //       },
  //     ],
  //   };
  //   const store = mockStore({
  //     authenticated: { permissions: {}, sid: '123' },
  //     variableMapping: { activeSet: { mappingSet: { variables: invalidTestSet.variables }, variables: [] } },
  //   });
  //   const onSave = jest.fn();
  //   const { findByTestId, findByText } = render(
  //     <Provider store={store}>
  //       <VariableMappingEdit
  //         mappingSet={invalidTestSet}
  //         onSaveAsDraft={onSave}
  //         onCancel={jest.fn()}
  //         onReview={jest.fn()}
  //         api={mockApi}
  //       />
  //     </Provider>
  //   );

  //   const button = await findByTestId('save-button');
  //   fireEvent.click(button);
  //   await findByText('test*& is not a valid name');
  //   expect(onSave).toHaveBeenCalledTimes(0);
  // });
});
