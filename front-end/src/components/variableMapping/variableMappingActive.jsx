import React, { useState, useEffect } from 'react';
import { useSelector, connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { useHistory } from 'react-router-dom';
import PropTypes from 'prop-types';

import CanvasCard from 'canvas-core-react/lib/Card';
import Table from 'canvas-core-react/lib/Table';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';

import {
  getVariableMapping,
  createMappingSet,
  submitForApproval,
  editedSetUpdated,
} from '../../store/actions/variable-mapping';
import VariableMappingEdit from './variableMappingEdit';
import VariableMappingReview from './variableMappingReview';
import defaultApi from '../../api/variable-mapping';
import permissionsList from '../../constants/permissionsList';

const VariableMappingActive = ({
  getVariableMapping,
  createMappingSet,
  submitForApproval,
  editedSetUpdated,
  isLoading,
  hasDraft,
  hasPending,
  activeSet,
}) => {
  const [ mode, setMode ] = useState('view');
  const [ showPendingAlert, setShowPendingAlert ] = useState(false);
  const [ showDraftAlert, setShowDraftAlert ] = useState(false);
  const history = useHistory();
  const permissions = useSelector(state => state.authenticated.permissions) || {};
  const canView = permissions[permissionsList.PEGA_VARIABLE_MAPPING_VIEW] ||
    permissions[permissionsList.KT_VARIABLE_MAPPING_VIEW] ||
    permissions.admin;
  const canUpdate = permissions[permissionsList.PEGA_VARIABLE_MAPPING_MANAGE] ||
    permissions[permissionsList.KT_VARIABLE_MAPPING_MANAGE] ||
    permissions.admin;
  const canReview = permissions[permissionsList.PEGA_VARIABLE_MAPPING_REVIEW] ||
    permissions[permissionsList.KT_VARIABLE_MAPPING_REVIEW] ||
    permissions.admin;
  const canApprove = permissions[permissionsList.PEGA_VARIABLE_MAPPING_APPROVE] ||
    permissions[permissionsList.KT_VARIABLE_MAPPING_APPROVE] ||
    permissions.admin;
  const variables = activeSet?.variables || [];

  useEffect(() => {
    setMode('view');
    getVariableMapping({ status: 'active' });
  }, []);

  const edit = () => {
    if (hasPending) return setShowPendingAlert(true);
    if (hasDraft) return setShowDraftAlert(true);
    editedSetUpdated({ ...activeSet, variables, variable_set_id: undefined });
    setMode('edit');
  };

  const onSubmittedForApproval = async(editedSet, approver) => {
    await submitForApproval(editedSet, approver.sid);
    history.push('./pending');
  };

  const columnDefinition = [
    {
      name: 'Source Name',
      selector: 'variable_campaign',
      sortable: true,
      grow: 2,
    },
    {
      name: 'Presentation Name',
      selector: 'variable_template',
      sortable: true,
      grow: 2.5,
    },
    {
      name: 'Format',
      selector: 'variable_type_label',
      sortable: true,
      grow: 1.5,
    },
  ];

  const renderView = () => {
    return (
      <>
        <div className="details__action-bar">
          <TextHeadline data-testid="active-set-title" component='h1'>Active</TextHeadline>
          <div className="details__action-buttons">
            { !isLoading && canUpdate &&
              <SecondaryButton className="details__action-button" onClick={edit}>
                { variables.length === 0 ? 'Create' : 'Edit' }
              </SecondaryButton>
            }
          </div>
        </div>
        <CanvasCard>
          { isLoading && <div>Loading mapping set...</div> }
          { !isLoading && variables.length === 0 && <div>There are no active mapping sets</div> }
          { !isLoading && activeSet && variables.length > 0 &&
            <Table id="active_variable_set_table" title="" columns={columnDefinition} data={variables} striped />
          }
        </CanvasCard>

        <ModalDialogue
          headline="You can only edit one variable set at a time"
          isModalVisible={showPendingAlert}
          primaryAction={() => { history.push('./pending'); }}
          primaryButtonLabel="View Pending Set"
          secondaryAction={() => setShowPendingAlert(false)}
          secondaryButtonLabel="Cancel"
          setModalVisible={() => setShowPendingAlert(false)}
          data-testid="pending-set-alert-modal"
        >
          Please approve or reject the pending set before editing a new set
        </ModalDialogue>

        <ModalDialogue
          headline="You can only edit one draft set at a time"
          isModalVisible={showDraftAlert}
          primaryAction={() => history.push('./draft')}
          primaryButtonLabel="View Draft Set"
          secondaryAction={() => setShowDraftAlert(false)}
          secondaryButtonLabel="Cancel"
          setModalVisible={() => setShowDraftAlert(false)}
          data-testid="draft-set-alert-modal"
        >
          Please continue editing or delete the draft set before editing a new set
        </ModalDialogue>
      </>
    );
  };

  const renderEdit = () =>
    <VariableMappingEdit
      onCancel={() => setMode('view')}
      onReview={editedSet => { setMode('review'); editedSetUpdated(editedSet); }}
      onSaveAsDraft={async newSet => {
        await createMappingSet(newSet);
        history.push('./draft');
      }}
      canReview={canReview}
    />;

  const renderReview = () =>
    <VariableMappingReview
      onEdit={() => setMode('edit')}
      onSubmittedForApproval={onSubmittedForApproval}
      canApprove={canApprove}
      canUpdate={canUpdate}
    />;

  return (
    <div id='variable-mapping'>
      { !canView && <div>You are not authorized to view this content</div> }
      { canView && mode === 'view' && renderView() }
      { canView && mode === 'edit' && renderEdit() }
      { canView && mode === 'review' && renderReview() }
    </div>
  );
};

VariableMappingActive.propTypes = {
  getVariableMapping: PropTypes.func.isRequired,
  createMappingSet: PropTypes.func.isRequired,
  submitForApproval: PropTypes.func.isRequired,
  editedSetUpdated: PropTypes.func.isRequired,
  isLoading: PropTypes.bool,
  hasDraft: PropTypes.bool,
  hasPending: PropTypes.bool,
  activeSet: PropTypes.object,
  api: PropTypes.object,
};

VariableMappingActive.defaultProps = {
  api: defaultApi,
};

const mapStateToProps = state => ({
  isLoading: state.variableMapping.isLoading,
  hasDraft: state.variableMapping.hasDraft,
  hasPending: state.variableMapping.hasPending,
  activeSet: state.variableMapping.activeSet,
});

const mapDispatchToProps = dispatch => bindActionCreators({
  getVariableMapping,
  createMappingSet,
  submitForApproval,
  editedSetUpdated,
}, dispatch);

export default connect(mapStateToProps, mapDispatchToProps)(VariableMappingActive);
