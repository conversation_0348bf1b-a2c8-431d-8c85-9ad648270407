const mockPermissions = [
  { id: 1, name: 'Admin', description: 'admin' },
  { id: 2, name: 'variable_mappings_view', description: 'View Variable Mappings Tab' },
  { id: 3, name: 'variable_mappings_update', description: 'Update Variable Mappings draft' },
  { id: 4, name: 'variable_mappings_approve', description: 'Approve Variable Mappings draft' },
];
const mockRoles = [ { id: 1, permissions: [ 1, 2, 3, 4 ] } ];
const mockUsers = [
  { id: 1, sid: '123', name: '<PERSON>', roles: [ 1 ] },
  { id: 1, sid: 's1001000', name: 'Manager', roles: [ 3 ] },
];
const mockApprovers = [ { 'full_name': 'Manager', sid: 's1001000' } ];

const activeSets = [ {
  description: 'test   <div>super hot</div> testtest',
  updated_by: '123',
  variable_set_id: '437BeQbV0seae2QkusuOCa',
  variables: [
    {
      variable_campaign: 'test3',
      variable_template: 'SOLUI_TEST3_END',
      variable_type: 'text',
    },
    {
      variable_campaign: 'test2',
      variable_template: 'SOLUI_TEST2_END',
      variable_type: 'currency',
    },
  ],
} ];

const mockTypes = [
  { 'id': 1, 'name': 'date', 'description': 'Date (MMDDYYY)' },
  { 'id': 2, 'name': 'text', 'description': 'Text' },
  { 'id': 3, 'name': 'currency', 'description': 'Currency' },
  { 'id': 4, 'name': 'account-number-mask', 'description': 'Masked Account Number' },
  { 'id': 5, 'name': 'number', 'description': 'Number' },
  { 'id': 7, 'name': 'gic-special-term', 'description': 'GIC Special Term' },
  { 'id': 8, 'name': 'gic-special-rate', 'description': 'GIC Special Rate' },
];

export const MockApi = () => {
  return {
    getTypes: jest.fn().mockReturnValue(Promise.resolve(mockTypes)),
    getPermissions: jest.fn().mockReturnValue(Promise.resolve(mockPermissions)),
    updateMappings: jest.fn().mockReturnValue(Promise.resolve()),
    getUsers: jest.fn().mockReturnValue(Promise.resolve(mockUsers)),
    getRoles: jest.fn().mockReturnValue(Promise.resolve(mockRoles)),
    getMappings: jest.fn().mockReturnValue(Promise.resolve(activeSets)),
    createMappingSet: jest.fn().mockReturnValue(Promise.resolve(activeSets[0])),
    editDraft: jest.fn().mockReturnValue(Promise.resolve(activeSets[0])),
    getApprovers: jest.fn().mockReturnValue(Promise.resolve(mockApprovers)),
  };
};
