import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { useSelector, connect } from 'react-redux';
import { useHistory } from 'react-router-dom';
import moment from 'moment';

import Table from 'canvas-core-react/lib/Table';
import CanvasCard from 'canvas-core-react/lib/Card';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';

import { calculateDiff } from './calculate-diff';
import typeMap from './typeMap';
import VariableMappingEdit from './variableMappingEdit';
import VariableMappingReview from './variableMappingReview';
import {
  getVariableMapping,
  createMappingSet,
  editedSetUpdated,
  deleteDraftSet,
  submitForApproval,
} from '../../store/actions/variable-mapping';
import { getUsers } from '../../store/actions/users';
import permissionsList from '../../constants/permissionsList';

const actionMap = {
  d: 'Deleted',
  m: 'Modified',
  n: 'Added',
};

const VariableMappingDraft = ({
  getVariableMapping,
  createMappingSet,
  editedSetUpdated,
  deleteDraftSet,
  submitForApproval,
  activeSet,
  draftSet,
  isLoading,
  hasDraft,
  users,
  getUsers,
}) => {
  const [ changes, setChanges ] = useState([]);
  const [ mode, setMode ] = useState('view');
  const [ deletePrompt, setDeletePrompt ] = useState(false);
  const [ assignedBy, setAssignedBy ] = useState();
  const [ assignedDate, setAssignedDate ] = useState();
  const permissions = useSelector(state => state.authenticated.permissions) || {};
  const canView = permissions[permissionsList.PEGA_VARIABLE_MAPPING_VIEW] ||
    permissions[permissionsList.KT_VARIABLE_MAPPING_VIEW] ||
    permissions.admin;
  const canUpdate = permissions[permissionsList.PEGA_VARIABLE_MAPPING_MANAGE] ||
    permissions[permissionsList.KT_VARIABLE_MAPPING_MANAGE] ||
    permissions.admin;
  const canReview = permissions[permissionsList.PEGA_VARIABLE_MAPPING_REVIEW] ||
    permissions[permissionsList.KT_VARIABLE_MAPPING_REVIEW] ||
    permissions.admin;
  const canApprove = permissions[permissionsList.PEGA_VARIABLE_MAPPING_APPROVE] ||
    permissions[permissionsList.KT_VARIABLE_MAPPING_APPROVE] ||
    permissions.admin;
  const history = useHistory();

  useEffect(() => {
    getUsers();
    getVariableMapping({ status: 'draft' });
  }, []);

  useEffect(() => {
    if (hasDraft && users.isLoading === false && users.items) {
      const diff = calculateDiff(activeSet, draftSet);
      setChanges(diff);
      const isNewSet = !draftSet.updated_at && !draftSet.updated_by;
      const lastModified = isNewSet ? draftSet.created_at : draftSet.updated_at;
      setAssignedDate(moment(lastModified).format('LLL'));
      const user = Object.values(users.items).find((u) => {
        return u.sid === (isNewSet ? draftSet.created_by : draftSet.updated_by);
      });
      setAssignedBy(user);
      if (mode !== 'review') setMode('view');
    }
  }, [ draftSet, users ]);

  const onDelete = async() => {
    await deleteDraftSet(draftSet);
    history.push('./active');
  };

  const save = async(newSet) => {
    await createMappingSet(newSet);
    setMode('view');
  };

  const onSubmittedForApproval = async(editedSet, approver) => {
    await submitForApproval(editedSet, approver.sid);
    history.push('./pending');
  };

  const formatCellFormatter = (rowData) => {
    return (
      <>
        { rowData.oldFormat &&
          <span className="old-mapping-format">
            { typeMap[rowData.oldFormat] }
          </span>
        }
        { typeMap[rowData.format] }
      </>
    );
  };

  const statusCellFormatter = (rowData) => {
    return (
      <div className={`text-${rowData.type}`}>{ actionMap[rowData.type] }</div>
    );
  };

  const columnDefinition = [
    {
      name: 'Source Name',
      selector: 'name',
      sortable: true,
      grow: 2,
    },
    {
      name: 'Presentation Name',
      selector: 'template',
      sortable: true,
      grow: 2.5,
    },
    {
      name: 'Format',
      selector: 'format',
      sortable: true,
      cellFormatter: formatCellFormatter,
      grow: 1.5,
    },
    {
      name: 'Status',
      selector: 'status',
      cellFormatter: statusCellFormatter,
    },
  ];

  const renderView = () => {
    return (
      <div id='variable-mapping'>
        <div className="details__action-bar">
          <TextHeadline component='h1' data-testid="draft-set-title">Draft</TextHeadline>
        </div>
        { assignedBy && <CanvasCard>
          <span className="assigned-by">Last edited by:</span> { assignedBy?.name } on { assignedDate }
        </CanvasCard> }
        <CanvasCard>
          { isLoading && <div>Loading mapping set...</div> }
          { !isLoading && !hasDraft &&
            <div className="centered">There are no draft variable sets</div>
          }

          { !isLoading && hasDraft && changes.length === 0 &&
            <div className="centered">There are currently no differences between active and draft sets</div>
          }

          { !isLoading && changes.length > 0 &&
            <Table id="draft_variable_set_table" title="" columns={columnDefinition} data={changes} striped />
          }

        </CanvasCard>

        { !isLoading && hasDraft && changes.length > 0 && draftSet?.description?.length > 0 &&
          <CanvasCard>
            <h5>Comments</h5>
            <div>{ draftSet?.description }</div>
          </CanvasCard>
        }

        { !isLoading && hasDraft &&
          <div className="details__action-bar right">
            <div className="details__action-buttons">
              { canUpdate && <SecondaryButton
                className="details__action-button"
                data-testid="delete-button"
                onClick={() => setDeletePrompt(true)}>
                  Delete
              </SecondaryButton>
              }
              { canUpdate && <PrimaryButton
                className="details__action-button"
                onClick={() => setMode('edit')}>
                  Edit
              </PrimaryButton>
              }
            </div>
          </div>
        }

        <ModalDialogue
          headline="Are you sure you want to Delete?"
          isModalVisible={deletePrompt}
          primaryAction={onDelete}
          primaryButtonLabel="Delete"
          secondaryAction={() => setDeletePrompt(false)}
          secondaryButtonLabel="Cancel"
          setModalVisible={setDeletePrompt}
          data-testid="delete-confirm-modal"
        >
          Your changes will not be saved if you delete
        </ModalDialogue>
      </div>
    );
  };
  const renderEdit = () =>
    <VariableMappingEdit
      mode={mode}
      canReview={canReview}
      onReview={editedSet => { editedSetUpdated(editedSet); setMode('review'); }}
      onCancel={() => setMode('view')}
      onSaveAsDraft={save}
    />;

  const renderReview = () =>
    <VariableMappingReview
      canApprove={canApprove}
      canUpdate={canUpdate}
      onEdit={() => setMode('edit')}
      onSubmittedForApproval={onSubmittedForApproval}
    />;

  return (
    <div>
      { !canView && <div>You are not authorized to view this content</div> }
      { canView && mode === 'view' && renderView() }
      { canView && mode === 'edit' && renderEdit() }
      { canView && mode === 'review' && renderReview() }
    </div>
  );
};

VariableMappingDraft.propTypes = {
  getVariableMapping: PropTypes.func.isRequired,
  createMappingSet: PropTypes.func.isRequired,
  editedSetUpdated: PropTypes.func.isRequired,
  deleteDraftSet: PropTypes.func.isRequired,
  submitForApproval: PropTypes.func.isRequired,
  activeSet: PropTypes.object,
  draftSet: PropTypes.object,
  isLoading: PropTypes.bool,
  hasDraft: PropTypes.bool,
  users: PropTypes.object,
  getUsers: PropTypes.func.isRequired,
};

const mapStateToProps = state => ({
  isLoading: state.variableMapping.isLoading,
  hasDraft: state.variableMapping.hasDraft,
  activeSet: state.variableMapping.activeSet,
  draftSet: state.variableMapping.draftSet,
  users: state.users,
});

const mapDispatchToProps = dispatch => bindActionCreators({
  getVariableMapping,
  createMappingSet,
  editedSetUpdated,
  deleteDraftSet,
  submitForApproval,
  getUsers,
}, dispatch);

export default connect(mapStateToProps, mapDispatchToProps)(VariableMappingDraft);
