import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';

import Selector from 'canvas-core-react/lib/Selector';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import AlertBanner from 'canvas-core-react/lib/AlertBanner';

import { getUsers } from '../../store/actions/users';
import { getApprovers } from '../../store/actions/variable-mapping';

const VariableMappingApproverPopup = ({
  isOpen,
  onConfirm,
  onCancel,
  approver,
  users,
  getUsers,
  approvers,
  getApprovers,
}) => {
  const [ newApprover, setNewApprover ] = useState(approver);
  const [ error, setError ] = useState(false);
  const isLoading = users.isLoading !== false || !approvers || approvers?.length === 0;
  useEffect(() => {
    !users.items && getUsers();
  }, []);

  useEffect(() => {
    users.isLoading === false && !approvers && users.items && getApprovers(Object.values(users.items));
  }, [ users ]);

  useEffect(() => {
    setNewApprover(approver); // if approver already exists, ie it's a pending set
  }, [ approvers, approver ]);

  const confirm = (newApprover) => {
    if (newApprover) {
      onConfirm(newApprover);
    } else {
      setError('Please select the approver from the list');
    }
  };

  return (
    <ModalDialogue
      headline="Submit for Approval"
      isModalVisible={isOpen}
      primaryAction={() => confirm(newApprover)}
      primaryButtonLabel="Submit"
      secondaryAction={onCancel}
      secondaryButtonLabel="Cancel"
      setModalVisible={onCancel}
      data-testid="edit-approver-modal"
    >
      Select a person to approve these changes
      { isLoading && <div>Loading...</div> }
      { !isLoading &&
        <>
          <Selector
            id="assigneeSelect"
            name="assigneeSelect"
            label="Assignee"
            placeholder="Select one"
            onChange={e => setNewApprover(Object.values(users.items).find(u => u.sid === e.target.value))}
            value={newApprover?.sid}
            data-testid='approver-selector'
          >
            { Object.values(approvers).map(approver => (
              <option
                key={approver.id}
                value={approver.sid}
                data-testid={`approver-selector-option_${approver.sid}`}
              > { approver.name } </option>
            )) }
          </Selector>
          { error && <AlertBanner className="margin" type="error">{ error }</AlertBanner> }
        </>
      }
    </ModalDialogue>
  );
};

VariableMappingApproverPopup.propTypes = {
  isOpen: PropTypes.bool,
  onCancel: PropTypes.func,
  onConfirm: PropTypes.func,
  users: PropTypes.object,
  approver: PropTypes.object,
  approvers: PropTypes.array,
  getUsers: PropTypes.func.isRequired,
  getApprovers: PropTypes.func.isRequired,
};

const mapStateToProps = state => ({
  users: state.users,
  approvers: state.variableMapping.approvers,
});

const mapDispatchToProps = dispatch => bindActionCreators({
  getUsers,
  getApprovers,
}, dispatch);

export default connect(mapStateToProps, mapDispatchToProps)(VariableMappingApproverPopup);
