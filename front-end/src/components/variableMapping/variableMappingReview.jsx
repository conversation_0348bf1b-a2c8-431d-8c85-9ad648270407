import React, { useState, useEffect } from 'react';
import { connect, useSelector } from 'react-redux';
import { bindActionCreators } from 'redux';
import CanvasCard from 'canvas-core-react/lib/Card';
import PropTypes from 'prop-types';

import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import Table from 'canvas-core-react/lib/Table';

import { calculateDiff } from './calculate-diff';
import typeMap from './typeMap';
import VariableMappingApproverPopup from './variableMappingApproverPopup';

const actionMap = {
  d: 'Deleted',
  m: 'Modified',
  n: 'Added',
};

// screen that compares arbitrary set to the active set
// two actions allowed:
// edit -> noop aka cancel, go back to edit screen
// submit for approval -> saves arbitrary set as 'pending'

const VariableMappingReviewChanges = ({
  onEdit,
  onSubmittedForApproval,
  activeSet,
  isLoading,
  editedSet,
  canApprove,
  canUpdate,
}) => {
  const alertBanner = useSelector(state => state.alertBanner);
  const [ changes, setChanges ] = useState([]);
  const [ approverPrompt, setApproverPrompt ] = useState(false);
  useEffect(() => {
    const diff = calculateDiff(activeSet, editedSet);
    setChanges(diff);
  }, []);

  const formatCellFormatter = (rowData) => {
    return (
      <>
        { rowData.oldFormat &&
          <span className="old-mapping-format">
            { typeMap[rowData.oldFormat] }
          </span>
        }
        { typeMap[rowData.format] }
      </>
    );
  };

  const statusCellFormatter = (rowData) => {
    return (
      <div className={`text-${rowData.type}`}>{ actionMap[rowData.type] }</div>
    );
  };

  const columnDefinition = [
    {
      name: 'Source Name',
      selector: 'name',
      sortable: true,
      grow: 2,
    },
    {
      name: 'Presentation Name',
      selector: 'template',
      sortable: true,
      grow: 2.5,
    },
    {
      name: 'Format',
      selector: 'format',
      sortable: true,
      cellFormatter: formatCellFormatter,
      grow: 1.5,
    },
    {
      name: 'Status',
      selector: 'status',
      cellFormatter: statusCellFormatter,
    },
  ];

  const pageContent =
    <>
      { changes.length === 0 && <CanvasCard><div>No changes have been made to variables</div></CanvasCard> }
      { changes.length > 0 &&
        <CanvasCard>
          <Table id="pending_variable_set_table" title="" columns={columnDefinition} data={changes} striped />
        </CanvasCard>
      }
      { changes.length > 0 && <CanvasCard><h5>Comments</h5><div>{ editedSet?.description }</div></CanvasCard> }
      <div className="details__action-bar right">
        <div className="details__action-buttons">
          <SecondaryButton
            className="details__action-button"
            disabled={!canUpdate}
            onClick={onEdit}>
              Edit
          </SecondaryButton>
          <PrimaryButton
            className="details__action-button"
            disabled={changes.length === 0 || alertBanner?.message || !canApprove}
            onClick={() => setApproverPrompt(true)}>
              Submit for Approval
          </PrimaryButton>
        </div>
      </div>
    </>;

  return (
    <div id='variable-mapping'>
      <div className="details__action-bar"><TextHeadline component='h1'>Review Changes</TextHeadline></div>
      { isLoading && <CanvasCard><div>Loading mapping set...</div></CanvasCard> }
      { !isLoading && pageContent }
      <VariableMappingApproverPopup
        isOpen={approverPrompt}
        onCancel={() => setApproverPrompt(false)}
        onConfirm={approverSid => onSubmittedForApproval(editedSet, approverSid)}
      />
    </div>
  );
};

VariableMappingReviewChanges.propTypes = {
  onEdit: PropTypes.func,
  onSubmittedForApproval: PropTypes.func,
  activeSet: PropTypes.object.isRequired,
  editedSet: PropTypes.object,
  isLoading: PropTypes.bool,
  canApprove: PropTypes.bool,
  canUpdate: PropTypes.bool,
};

const mapStateToProps = state => ({
  activeSet: state.variableMapping?.activeSet,
  editedSet: state.variableMapping?.editedSet,
  isLoading: state.variableMapping?.isLoading,
});

const mapDispatchToProps = dispatch => bindActionCreators({
}, dispatch);

export default connect(mapStateToProps, mapDispatchToProps)(VariableMappingReviewChanges);
