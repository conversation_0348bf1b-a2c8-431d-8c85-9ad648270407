import { calculateDiff } from './calculate-diff';

describe('calculate diff function', () => {
  it('should return the diff', () => {
    const newSet = {
      variable_set_id: 1,
      created_at: '2021-01-20T23:16:21.705Z',
      created_by: 's6983282',
      status: 'active',
      updated_at: null,
      updated_by: 's7646571',
      variables: [
        {
          variable_template: 'SOLUI_CUST_FULL_NAME_END',
          variable_campaign: 'cust_full_name',
          variable_type: 'text',
        },
        {
          variable_template: 'SOLUI_EXPIRY_DATE_END',
          variable_campaign: 'expiry_date',
          variable_type: 'date',
        },
        {
          variable_template: 'SOLUI_CURRENT_ACCOUNT_NUMBER_END',
          variable_campaign: 'current_account_number',
          variable_type: 'account-number-mask',
        },
      ],
    };
    const oldSet = {
      variable_set_id: 3,
      created_at: '2021-01-20T23:16:21.724Z',
      created_by: 's6983282',
      status: 'active',
      updated_at: null,
      updated_by: 's7646571',
      variables: [
        {
          variable_template: 'SOLUI_CURRENT_ACCOUNT_NUMBER_END',
          variable_campaign: 'current_account_number',
          variable_type: 'account-number-mask',
        },
      ],
    };

    const diff = [
      {
        name: 'cust_full_name',
        template: 'SOLUI_CUST_FULL_NAME_END',
        format: 'text',
        type: 'n',
      },
      {
        name: 'expiry_date',
        template: 'SOLUI_EXPIRY_DATE_END',
        format: 'date',
        type: 'n',
      } ];
    const result = calculateDiff(oldSet, newSet);
    expect(result).toStrictEqual(diff);
  });
});
