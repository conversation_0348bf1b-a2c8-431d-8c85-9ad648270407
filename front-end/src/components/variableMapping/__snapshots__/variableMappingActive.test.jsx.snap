// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VariableMappingActive should handle existing draft 1`] = `
<div>
  <div
    id="variable-mapping"
  >
    <div
      class="details__action-bar"
    >
      <h1
        class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iejpSD"
        color="black"
        data-testid="active-set-title"
      >
        Active
      </h1>
      <div
        class="details__action-buttons"
      >
        <button
          class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary details__action-button"
        >
          <span
            class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
            tabindex="-1"
          >
            <span
              class="ButtonCore__text"
            >
              Edit
            </span>
          </span>
        </button>
      </div>
    </div>
    <div
      class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container"
      type="flatSolid"
    >
      <div
        class="Tablestyle__StyledTable-canvas-core__sc-mfsabp-0 dvntiZ Table"
      >
        <div
          class="Tablestyle__StyledTableWrapper-canvas-core__sc-mfsabp-1 ikRFBm Table__wrapper"
          id="table-active_variable_set_table"
        >
          <table
            class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable"
          >
            <caption
              aria-live="polite"
              class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP TableHeadingstyle__StyledHeading-canvas-core__sc-2qo8vm-0 bPEdEe Table__title"
              color="black"
              size="21"
            />
            <thead
              class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
              role="rowgroup"
            >
              <tr
                class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
                role="row"
              >
                <th
                  aria-sort="none"
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 kQJGxh"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <button
                    class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                    data="[object Object]"
                  >
                    <p
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                      color="black"
                    >
                      Source Name
                    </p>
                    <div
                      class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="8"
                        viewBox="4 0 10 10"
                      >
                        <path
                          d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="8"
                        viewBox="4 8 10 10"
                      >
                        <path
                          d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                        />
                      </svg>
                    </div>
                    <span
                      class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                    >
                      unsorted
                    </span>
                  </button>
                </th>
                <th
                  aria-sort="none"
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMomoq"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <button
                    class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                    data="[object Object]"
                  >
                    <p
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                      color="black"
                    >
                      Presentation Name
                    </p>
                    <div
                      class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="8"
                        viewBox="4 0 10 10"
                      >
                        <path
                          d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="8"
                        viewBox="4 8 10 10"
                      >
                        <path
                          d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                        />
                      </svg>
                    </div>
                    <span
                      class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                    >
                      unsorted
                    </span>
                  </button>
                </th>
                <th
                  aria-sort="none"
                  class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 joOFzN"
                  data="[object Object]"
                  role="columnheader"
                  scope="col"
                >
                  <button
                    class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                    data="[object Object]"
                  >
                    <p
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                      color="black"
                    >
                      Format
                    </p>
                    <div
                      class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="8"
                        viewBox="4 0 10 10"
                      >
                        <path
                          d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="8"
                        viewBox="4 8 10 10"
                      >
                        <path
                          d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                        />
                      </svg>
                    </div>
                    <span
                      class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                    >
                      unsorted
                    </span>
                  </button>
                </th>
              </tr>
            </thead>
            <tbody
              class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
              role="rowgroup"
            >
              <tr
                class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jjizpO"
                role="row"
              >
                <th
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                  role="rowheader"
                  scope="row"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                    color="black"
                  >
                    test
                  </p>
                </th>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bwbimY"
                  role="cell"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                    color="black"
                  >
                    SOLUI_TEST_END
                  </p>
                </td>
                <td
                  class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 grJjpf"
                  role="cell"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                    color="black"
                  >
                    Text
                  </p>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VariableMappingActive should render page correctly 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        id="variable-mapping"
      >
        <div
          class="details__action-bar"
        >
          <h1
            class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iejpSD"
            color="black"
            data-testid="active-set-title"
          >
            Active
          </h1>
          <div
            class="details__action-buttons"
          >
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary details__action-button"
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Edit
                </span>
              </span>
            </button>
          </div>
        </div>
        <div
          class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container"
          type="flatSolid"
        >
          <div
            class="Tablestyle__StyledTable-canvas-core__sc-mfsabp-0 dvntiZ Table"
          >
            <div
              class="Tablestyle__StyledTableWrapper-canvas-core__sc-mfsabp-1 ikRFBm Table__wrapper"
              id="table-active_variable_set_table"
            >
              <table
                class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable"
              >
                <caption
                  aria-live="polite"
                  class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP TableHeadingstyle__StyledHeading-canvas-core__sc-2qo8vm-0 bPEdEe Table__title"
                  color="black"
                  size="21"
                />
                <thead
                  class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
                  role="rowgroup"
                >
                  <tr
                    class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
                    role="row"
                  >
                    <th
                      aria-sort="none"
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 kQJGxh"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <button
                        class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                        data="[object Object]"
                      >
                        <p
                          class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                          color="black"
                        >
                          Source Name
                        </p>
                        <div
                          class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                        >
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="8"
                            viewBox="4 0 10 10"
                          >
                            <path
                              d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                            />
                          </svg>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="8"
                            viewBox="4 8 10 10"
                          >
                            <path
                              d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                            />
                          </svg>
                        </div>
                        <span
                          class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                        >
                          unsorted
                        </span>
                      </button>
                    </th>
                    <th
                      aria-sort="none"
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMomoq"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <button
                        class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                        data="[object Object]"
                      >
                        <p
                          class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                          color="black"
                        >
                          Presentation Name
                        </p>
                        <div
                          class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                        >
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="8"
                            viewBox="4 0 10 10"
                          >
                            <path
                              d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                            />
                          </svg>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="8"
                            viewBox="4 8 10 10"
                          >
                            <path
                              d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                            />
                          </svg>
                        </div>
                        <span
                          class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                        >
                          unsorted
                        </span>
                      </button>
                    </th>
                    <th
                      aria-sort="none"
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 joOFzN"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <button
                        class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                        data="[object Object]"
                      >
                        <p
                          class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                          color="black"
                        >
                          Format
                        </p>
                        <div
                          class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                        >
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="8"
                            viewBox="4 0 10 10"
                          >
                            <path
                              d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                            />
                          </svg>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="8"
                            viewBox="4 8 10 10"
                          >
                            <path
                              d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                            />
                          </svg>
                        </div>
                        <span
                          class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                        >
                          unsorted
                        </span>
                      </button>
                    </th>
                  </tr>
                </thead>
                <tbody
                  class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
                  role="rowgroup"
                >
                  <tr
                    class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jjizpO"
                    role="row"
                  >
                    <th
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                      role="rowheader"
                      scope="row"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        test
                      </p>
                    </th>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bwbimY"
                      role="cell"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        SOLUI_TEST_END
                      </p>
                    </td>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 grJjpf"
                      role="cell"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        Text
                      </p>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      id="variable-mapping"
    >
      <div
        class="details__action-bar"
      >
        <h1
          class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iejpSD"
          color="black"
          data-testid="active-set-title"
        >
          Active
        </h1>
        <div
          class="details__action-buttons"
        >
          <button
            class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary details__action-button"
          >
            <span
              class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
              tabindex="-1"
            >
              <span
                class="ButtonCore__text"
              >
                Edit
              </span>
            </span>
          </button>
        </div>
      </div>
      <div
        class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container"
        type="flatSolid"
      >
        <div
          class="Tablestyle__StyledTable-canvas-core__sc-mfsabp-0 dvntiZ Table"
        >
          <div
            class="Tablestyle__StyledTableWrapper-canvas-core__sc-mfsabp-1 ikRFBm Table__wrapper"
            id="table-active_variable_set_table"
          >
            <table
              class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable"
            >
              <caption
                aria-live="polite"
                class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP TableHeadingstyle__StyledHeading-canvas-core__sc-2qo8vm-0 bPEdEe Table__title"
                color="black"
                size="21"
              />
              <thead
                class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
                role="rowgroup"
              >
                <tr
                  class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
                  role="row"
                >
                  <th
                    aria-sort="none"
                    class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 kQJGxh"
                    data="[object Object]"
                    role="columnheader"
                    scope="col"
                  >
                    <button
                      class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                      data="[object Object]"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        Source Name
                      </p>
                      <div
                        class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                      >
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="8"
                          viewBox="4 0 10 10"
                        >
                          <path
                            d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                          />
                        </svg>
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="8"
                          viewBox="4 8 10 10"
                        >
                          <path
                            d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                          />
                        </svg>
                      </div>
                      <span
                        class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                      >
                        unsorted
                      </span>
                    </button>
                  </th>
                  <th
                    aria-sort="none"
                    class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMomoq"
                    data="[object Object]"
                    role="columnheader"
                    scope="col"
                  >
                    <button
                      class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                      data="[object Object]"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        Presentation Name
                      </p>
                      <div
                        class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                      >
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="8"
                          viewBox="4 0 10 10"
                        >
                          <path
                            d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                          />
                        </svg>
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="8"
                          viewBox="4 8 10 10"
                        >
                          <path
                            d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                          />
                        </svg>
                      </div>
                      <span
                        class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                      >
                        unsorted
                      </span>
                    </button>
                  </th>
                  <th
                    aria-sort="none"
                    class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 joOFzN"
                    data="[object Object]"
                    role="columnheader"
                    scope="col"
                  >
                    <button
                      class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                      data="[object Object]"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        Format
                      </p>
                      <div
                        class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                      >
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="8"
                          viewBox="4 0 10 10"
                        >
                          <path
                            d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                          />
                        </svg>
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="8"
                          viewBox="4 8 10 10"
                        >
                          <path
                            d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                          />
                        </svg>
                      </div>
                      <span
                        class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                      >
                        unsorted
                      </span>
                    </button>
                  </th>
                </tr>
              </thead>
              <tbody
                class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
                role="rowgroup"
              >
                <tr
                  class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jjizpO"
                  role="row"
                >
                  <th
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                    role="rowheader"
                    scope="row"
                  >
                    <p
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                      color="black"
                    >
                      test
                    </p>
                  </th>
                  <td
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bwbimY"
                    role="cell"
                  >
                    <p
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                      color="black"
                    >
                      SOLUI_TEST_END
                    </p>
                  </td>
                  <td
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 grJjpf"
                    role="cell"
                  >
                    <p
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                      color="black"
                    >
                      Text
                    </p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
