// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VariableMappingPending should render page with default data 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        id="variable-mapping"
      >
        <div
          class="details__action-bar"
        >
          <h1
            class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iejpSD"
            color="black"
            data-testid="pending-set-title"
          >
            Pending
          </h1>
        </div>
        <div
          class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container pending-metadata-card"
          type="flatSolid"
        >
          <span>
            <span
              class="assigned-to"
            >
              Assigned to:
            </span>
             
            Test Admin
          </span>
          <button
            class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button"
            color="blue"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="16"
              viewBox="0 0 30 30"
            >
              <path
                clip-rule="evenodd"
                d="M28.5 8.99917L8.99965 28.4999H1.5V21.0007L21.0004 1.49992L28.5 8.99917Z"
                fill="none"
                fill-rule="evenodd"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M16.1143 6.47101L23.4159 13.7729"
                fill="none"
                stroke-linecap="square"
              />
            </svg>
            <span
              class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
            >
              Edit
            </span>
          </button>
          <span>
            <span
              class="assigned-by"
            >
              Assigned by:
            </span>
             
            Test Admin
          </span>
          <span>
            <span
              class="assigned-at"
            >
              Date & Time:
            </span>
             
            December 9, 2021 9:29 PM
          </span>
        </div>
        <div
          class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container"
          type="flatSolid"
        >
          <div
            class="Tablestyle__StyledTable-canvas-core__sc-mfsabp-0 dvntiZ Table"
          >
            <div
              class="Tablestyle__StyledTableWrapper-canvas-core__sc-mfsabp-1 ikRFBm Table__wrapper"
              id="table-pending_variable_set_table"
            >
              <table
                class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable"
              >
                <caption
                  aria-live="polite"
                  class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP TableHeadingstyle__StyledHeading-canvas-core__sc-2qo8vm-0 bPEdEe Table__title"
                  color="black"
                  size="21"
                />
                <thead
                  class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
                  role="rowgroup"
                >
                  <tr
                    class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
                    role="row"
                  >
                    <th
                      aria-sort="none"
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 kQJGxh"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <button
                        class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                        data="[object Object]"
                      >
                        <p
                          class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                          color="black"
                        >
                          Source Name
                        </p>
                        <div
                          class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                        >
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="8"
                            viewBox="4 0 10 10"
                          >
                            <path
                              d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                            />
                          </svg>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="8"
                            viewBox="4 8 10 10"
                          >
                            <path
                              d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                            />
                          </svg>
                        </div>
                        <span
                          class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                        >
                          unsorted
                        </span>
                      </button>
                    </th>
                    <th
                      aria-sort="none"
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMomoq"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <button
                        class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                        data="[object Object]"
                      >
                        <p
                          class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                          color="black"
                        >
                          Presentation Name
                        </p>
                        <div
                          class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                        >
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="8"
                            viewBox="4 0 10 10"
                          >
                            <path
                              d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                            />
                          </svg>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="8"
                            viewBox="4 8 10 10"
                          >
                            <path
                              d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                            />
                          </svg>
                        </div>
                        <span
                          class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                        >
                          unsorted
                        </span>
                      </button>
                    </th>
                    <th
                      aria-sort="none"
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 joOFzN"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <button
                        class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                        data="[object Object]"
                      >
                        <p
                          class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                          color="black"
                        >
                          Format
                        </p>
                        <div
                          class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                        >
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="8"
                            viewBox="4 0 10 10"
                          >
                            <path
                              d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                            />
                          </svg>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                            color="currentColor"
                            focusable="false"
                            role="presentation"
                            size="8"
                            viewBox="4 8 10 10"
                          >
                            <path
                              d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                            />
                          </svg>
                        </div>
                        <span
                          class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                        >
                          unsorted
                        </span>
                      </button>
                    </th>
                    <th
                      class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                      data="[object Object]"
                      role="columnheader"
                      scope="col"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        Status
                      </p>
                    </th>
                  </tr>
                </thead>
                <tbody
                  class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
                  role="rowgroup"
                >
                  <tr
                    class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jjizpO"
                    role="row"
                  >
                    <th
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                      role="rowheader"
                      scope="row"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        testdelete
                      </p>
                    </th>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bwbimY"
                      role="cell"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        SOLUI_TESTDELETED_END
                      </p>
                    </td>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 grJjpf"
                      role="cell"
                    >
                      Text
                    </td>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                      role="cell"
                    >
                      <div
                        class="text-d"
                      >
                        Deleted
                      </div>
                    </td>
                  </tr>
                  <tr
                    class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jjizpO"
                    role="row"
                  >
                    <th
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                      role="rowheader"
                      scope="row"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        testmod
                      </p>
                    </th>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bwbimY"
                      role="cell"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        SOLUI_TESTMOD_END
                      </p>
                    </td>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 grJjpf"
                      role="cell"
                    >
                      <span
                        class="old-mapping-format"
                      >
                        Text
                      </span>
                      Date
                    </td>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                      role="cell"
                    >
                      <div
                        class="text-m"
                      >
                        Modified
                      </div>
                    </td>
                  </tr>
                  <tr
                    class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jjizpO"
                    role="row"
                  >
                    <th
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                      role="rowheader"
                      scope="row"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        testadd
                      </p>
                    </th>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bwbimY"
                      role="cell"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                        color="black"
                      >
                        SOLUI_TESTADD_END
                      </p>
                    </td>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 grJjpf"
                      role="cell"
                    >
                      Date
                    </td>
                    <td
                      class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                      role="cell"
                    >
                      <div
                        class="text-n"
                      >
                        Added
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div
          class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container"
          type="flatSolid"
        >
          <h5>
            Comments
          </h5>
          <div>
            desc
          </div>
        </div>
        <div
          class="details__action-bar right"
        >
          <div
            class="details__action-buttons"
          >
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary details__action-button"
              data-testid="reject-button"
              disabled=""
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Unsubmit
                </span>
              </span>
            </button>
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button PrimaryButtonstyle__StyledPrimaryButtonCore-canvas-core__sc-11pddd2-0 jbDssn Button__button--primary details__action-button"
              data-testid="approve-button"
              disabled=""
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Approve
                </span>
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      id="variable-mapping"
    >
      <div
        class="details__action-bar"
      >
        <h1
          class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iejpSD"
          color="black"
          data-testid="pending-set-title"
        >
          Pending
        </h1>
      </div>
      <div
        class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container pending-metadata-card"
        type="flatSolid"
      >
        <span>
          <span
            class="assigned-to"
          >
            Assigned to:
          </span>
           
          Test Admin
        </span>
        <button
          class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button"
          color="blue"
        >
          <svg
            aria-hidden="true"
            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
            color="currentColor"
            focusable="false"
            role="presentation"
            size="16"
            viewBox="0 0 30 30"
          >
            <path
              clip-rule="evenodd"
              d="M28.5 8.99917L8.99965 28.4999H1.5V21.0007L21.0004 1.49992L28.5 8.99917Z"
              fill="none"
              fill-rule="evenodd"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M16.1143 6.47101L23.4159 13.7729"
              fill="none"
              stroke-linecap="square"
            />
          </svg>
          <span
            class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
          >
            Edit
          </span>
        </button>
        <span>
          <span
            class="assigned-by"
          >
            Assigned by:
          </span>
           
          Test Admin
        </span>
        <span>
          <span
            class="assigned-at"
          >
            Date & Time:
          </span>
           
          December 9, 2021 9:29 PM
        </span>
      </div>
      <div
        class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container"
        type="flatSolid"
      >
        <div
          class="Tablestyle__StyledTable-canvas-core__sc-mfsabp-0 dvntiZ Table"
        >
          <div
            class="Tablestyle__StyledTableWrapper-canvas-core__sc-mfsabp-1 ikRFBm Table__wrapper"
            id="table-pending_variable_set_table"
          >
            <table
              class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable"
            >
              <caption
                aria-live="polite"
                class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP TableHeadingstyle__StyledHeading-canvas-core__sc-2qo8vm-0 bPEdEe Table__title"
                color="black"
                size="21"
              />
              <thead
                class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
                role="rowgroup"
              >
                <tr
                  class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
                  role="row"
                >
                  <th
                    aria-sort="none"
                    class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 kQJGxh"
                    data="[object Object]"
                    role="columnheader"
                    scope="col"
                  >
                    <button
                      class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                      data="[object Object]"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        Source Name
                      </p>
                      <div
                        class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                      >
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="8"
                          viewBox="4 0 10 10"
                        >
                          <path
                            d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                          />
                        </svg>
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="8"
                          viewBox="4 8 10 10"
                        >
                          <path
                            d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                          />
                        </svg>
                      </div>
                      <span
                        class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                      >
                        unsorted
                      </span>
                    </button>
                  </th>
                  <th
                    aria-sort="none"
                    class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMomoq"
                    data="[object Object]"
                    role="columnheader"
                    scope="col"
                  >
                    <button
                      class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                      data="[object Object]"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        Presentation Name
                      </p>
                      <div
                        class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                      >
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="8"
                          viewBox="4 0 10 10"
                        >
                          <path
                            d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                          />
                        </svg>
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="8"
                          viewBox="4 8 10 10"
                        >
                          <path
                            d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                          />
                        </svg>
                      </div>
                      <span
                        class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                      >
                        unsorted
                      </span>
                    </button>
                  </th>
                  <th
                    aria-sort="none"
                    class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 joOFzN"
                    data="[object Object]"
                    role="columnheader"
                    scope="col"
                  >
                    <button
                      class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                      data="[object Object]"
                    >
                      <p
                        class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                        color="black"
                      >
                        Format
                      </p>
                      <div
                        class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                      >
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="8"
                          viewBox="4 0 10 10"
                        >
                          <path
                            d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                          />
                        </svg>
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="8"
                          viewBox="4 8 10 10"
                        >
                          <path
                            d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                          />
                        </svg>
                      </div>
                      <span
                        class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                      >
                        unsorted
                      </span>
                    </button>
                  </th>
                  <th
                    class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                    data="[object Object]"
                    role="columnheader"
                    scope="col"
                  >
                    <p
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                      color="black"
                    >
                      Status
                    </p>
                  </th>
                </tr>
              </thead>
              <tbody
                class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
                role="rowgroup"
              >
                <tr
                  class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jjizpO"
                  role="row"
                >
                  <th
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                    role="rowheader"
                    scope="row"
                  >
                    <p
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                      color="black"
                    >
                      testdelete
                    </p>
                  </th>
                  <td
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bwbimY"
                    role="cell"
                  >
                    <p
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                      color="black"
                    >
                      SOLUI_TESTDELETED_END
                    </p>
                  </td>
                  <td
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 grJjpf"
                    role="cell"
                  >
                    Text
                  </td>
                  <td
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                    role="cell"
                  >
                    <div
                      class="text-d"
                    >
                      Deleted
                    </div>
                  </td>
                </tr>
                <tr
                  class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jjizpO"
                  role="row"
                >
                  <th
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                    role="rowheader"
                    scope="row"
                  >
                    <p
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                      color="black"
                    >
                      testmod
                    </p>
                  </th>
                  <td
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bwbimY"
                    role="cell"
                  >
                    <p
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                      color="black"
                    >
                      SOLUI_TESTMOD_END
                    </p>
                  </td>
                  <td
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 grJjpf"
                    role="cell"
                  >
                    <span
                      class="old-mapping-format"
                    >
                      Text
                    </span>
                    Date
                  </td>
                  <td
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                    role="cell"
                  >
                    <div
                      class="text-m"
                    >
                      Modified
                    </div>
                  </td>
                </tr>
                <tr
                  class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jjizpO"
                  role="row"
                >
                  <th
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                    role="rowheader"
                    scope="row"
                  >
                    <p
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                      color="black"
                    >
                      testadd
                    </p>
                  </th>
                  <td
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 bwbimY"
                    role="cell"
                  >
                    <p
                      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                      color="black"
                    >
                      SOLUI_TESTADD_END
                    </p>
                  </td>
                  <td
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 grJjpf"
                    role="cell"
                  >
                    Date
                  </td>
                  <td
                    class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                    role="cell"
                  >
                    <div
                      class="text-n"
                    >
                      Added
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div
        class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 kbHRQF Card__container"
        type="flatSolid"
      >
        <h5>
          Comments
        </h5>
        <div>
          desc
        </div>
      </div>
      <div
        class="details__action-bar right"
      >
        <div
          class="details__action-buttons"
        >
          <button
            class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary details__action-button"
            data-testid="reject-button"
            disabled=""
          >
            <span
              class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
              tabindex="-1"
            >
              <span
                class="ButtonCore__text"
              >
                Unsubmit
              </span>
            </span>
          </button>
          <button
            class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button PrimaryButtonstyle__StyledPrimaryButtonCore-canvas-core__sc-11pddd2-0 jbDssn Button__button--primary details__action-button"
            data-testid="approve-button"
            disabled=""
          >
            <span
              class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
              tabindex="-1"
            >
              <span
                class="ButtonCore__text"
              >
                Approve
              </span>
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
