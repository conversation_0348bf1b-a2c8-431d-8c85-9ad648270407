import React from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router';
import PropTypes from 'prop-types';
import qs from 'qs';
import AlertBanner from 'canvas-core-react/lib/AlertBanner';
import SnackBar from 'canvas-core-react/lib/SnackBar';
import { urlErrorMessages, scrollToTop } from '../constants';
import SkipLink from './core/skipLink';
import Login from '../components/core/loginContainer';
import { getUserData, userUnauth, logout } from '../store/actions/auth';
import { bindActionCreators } from 'redux';
import { addAlert, removeAlert } from '../store/actions/alertBanner';
import { removeSnackbar } from '../store/actions/snackbar';

import IENotSupported from './modal/IENotSupported';
import Heading from './core/MainNavigation';
import SubNavigation from './core/SubNavigation';
import Footer from './core/Footer';

export class App extends React.PureComponent {
  static propTypes = {
    getUserData: PropTypes.func.isRequired,
    userUnauth: PropTypes.func.isRequired,
    logout: PropTypes.func.isRequired,
    authenticated: PropTypes.object.isRequired,
    children: PropTypes.node.isRequired,
    toasts: PropTypes.array.isRequired,
    alertBanner: PropTypes.shape({
      message: PropTypes.string,
      type: PropTypes.string,
    }),
    snackbar: PropTypes.shape({
      message: PropTypes.string,
      isOpen: PropTypes.bool,
      bold: PropTypes.string,
    }),
    location: PropTypes.shape({
      search: PropTypes.string,
      pathname: PropTypes.string,
    }).isRequired,
    addAlert: PropTypes.func.isRequired,
    removeAlert: PropTypes.func.isRequired,
    removeSnackbar: PropTypes.func.isRequired,
    history: PropTypes.shape({
      push: PropTypes.func.isRequired,
      listen: PropTypes.func.isRequired,
    }),
  };

  componentDidMount() {
    this.unsubscribe = this.props.history.listen(() => {
      this.props.removeAlert();
    });

    // load user data
    this.props.getUserData();

    const { location } = this.props;
    if (location && location.search) {
      const { error } = qs.parse(location.search.replace('?', ''));
      const errorMessage = urlErrorMessages[error];
      if (errorMessage) {
        this.props.addAlert({
          message: errorMessage,
          type: 'alert',
        });
      }
    }
  }

  componentWillUnmount() {
    this.unsubscribe();
  }

  render() {
    if (this.props.alertBanner.message) {
      scrollToTop();
    }

    return (
      <React.Fragment>
        <SkipLink targetId="content-main" />
        <Heading />
        { this.props.alertBanner.message &&
          <nav className="padding-side margin content__alert-banner" style={{ maxWidth: '1440px', marginLeft: 'auto', marginRight: 'auto' }}>
            <AlertBanner type={this.props.alertBanner.type} closeButton ={{ onClick: this.props.removeAlert, label: 'close' }}>
              { this.props.alertBanner.message }
            </AlertBanner>
          </nav>
        }
        <SubNavigation />
        <main className="margin padding-side" id="content-main">
          { this.props.authenticated.permissions && this.props.children }
          { this.props.authenticated.loggedOut && <Login logout={this.props.logout}/> }
        </main>
        { this.props.snackbar.isOpen && (
          <SnackBar
            duration={1500}
            open={this.props.snackbar.isOpen}
            onClose={() => this.props.removeSnackbar()}
          >
            { this.props.snackbar.bold && (
              <strong style={{ paddingRight: 5 }}>
                { this.props.snackbar.bold }
              </strong>
            ) }
            { this.props.snackbar.message }
          </SnackBar>
        ) }
        <IENotSupported />
        <Footer />
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => ({
  authenticated: state.authenticated,
  toasts: state.toasts,
  alertBanner: state.alertBanner,
  snackbar: state.snackbar,
});

const mapDispatchToProps = dispatch => bindActionCreators({
  logout,
  getUserData,
  userUnauth,
  addAlert,
  removeAlert,
  removeSnackbar,
}, dispatch);

@withRouter
@connect(mapStateToProps, mapDispatchToProps)
export default class AppWithRouter extends App { }
