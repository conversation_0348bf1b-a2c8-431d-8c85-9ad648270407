import React from 'react';
import PropTypes from 'prop-types';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';

export const PageInfo = ({
  title = '',
  TitleComponent = TextHeadline,
  titleComponentPorps = {},
  ButtonComponent = SecondaryButton,
  onClick = () => {},
  buttonText = '',
  buttonProps = {},
  isLoading = false,
  showActionButton = true,
  loaderSpinner = <IconSpinner size={24} />,
}) => {
  return (
    <div className="page-info__title-action">
      <TitleComponent
        data-testid="page-info-title"
        component="h1"
        {...titleComponentPorps}
      >
        { title } { isLoading && loaderSpinner }
      </TitleComponent>
      { showActionButton && (
        <ButtonComponent
          data-testid="page-info-action"
          {...buttonProps}
          onClick={onClick}
        >
          { buttonText }
        </ButtonComponent>
      ) }
    </div>
  );
};
PageInfo.propTypes = {
  title: PropTypes.string,
  TitleComponent: PropTypes.oneOfType([ PropTypes.func, PropTypes.element ]),
  titleComponentPorps: PropTypes.object,
  ButtonComponent: PropTypes.oneOfType([ PropTypes.func, PropTypes.element ]),
  onClick: PropTypes.func,
  buttonText: PropTypes.string,
  buttonProps: PropTypes.object,
  isLoading: PropTypes.bool,
  showActionButton: PropTypes.bool,
  loaderSpinner: PropTypes.element,
};

export default PageInfo;
