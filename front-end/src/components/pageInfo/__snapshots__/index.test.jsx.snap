// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PageInfo - match snapshot loading - false Snapshot 1`] = `
<div
  className="page-info__title-action"
>
  <u
    color="black"
    component="h1"
    data-testid="page-info-title"
  >
    User
     
  </u>
  <d
    data-testid="page-info-action"
    disabled={false}
    labelPadding={36}
    onClick={[Function]}
    size="regular"
  >
    Create User
  </d>
</div>
`;

exports[`PageInfo - match snapshot loading - true Snapshot 1`] = `
<div
  className="page-info__title-action"
>
  <u
    color="black"
    component="h1"
    data-testid="page-info-title"
  >
    User
     
    <s
      color="inherit"
      size={24}
    />
  </u>
  <d
    data-testid="page-info-action"
    disabled={false}
    labelPadding={36}
    onClick={[Function]}
    size="regular"
  >
    Create User
  </d>
</div>
`;
