import React from 'react';
import { shallow } from 'enzyme';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import PageInfo from './index';

describe('PageInfo - match snapshot loading - true', () => {
  const wrapper = shallow(
    <PageInfo
      title="User"
      buttonText="Create User"
      TitleComponent={TextHeadline}
      ButtonComponent={SecondaryButton}
      titleComponentPorps={{ component: 'h1' }}
      isLoading
    />
  );
  global.snapshot(wrapper);
});

describe('PageInfo - match snapshot loading - false', () => {
  const wrapper = shallow(
    <PageInfo
      title="User"
      buttonText="Create User"
      TitleComponent={TextHeadline}
      ButtonComponent={SecondaryButton}
      titleComponentPorps={{ component: 'h1' }}
      isLoading={false}
    />
  );
  global.snapshot(wrapper);
});

describe('PageInfo', () => {
  it('renders the correct elemnts', () => {
    const wrapper = shallow(<PageInfo title="User" buttonText="Create User" />);
    expect(wrapper.find({ 'data-testid': 'page-info-title' })).toHaveLength(1);
    expect(wrapper.find({ 'data-testid': 'page-info-action' })).toHaveLength(1);
  });

  it('renders the correct test action', () => {
    const wrapper = shallow(
      <PageInfo title="User" onc buttonText="Create User" />
    );
    expect(wrapper.find({ 'data-testid': 'page-info-title' })).toHaveLength(1);
    expect(wrapper.find({ 'data-testid': 'page-info-action' })).toHaveLength(1);
  });
});
