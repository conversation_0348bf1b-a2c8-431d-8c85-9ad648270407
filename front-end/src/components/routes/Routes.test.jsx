import React from 'react';
import { render } from '@testing-library/react';
import { Router } from 'react-router-dom';
import { createMemoryHistory } from 'history';
import Routes from './Routes';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';
const mockStore = configureStore([ thunk ]);
const createUser = (id, name, active = true, role = 1) => ({
  id,
  name,
  email: `${name.toLowerCase().replace(' ', '')}@scotiabank.com`,
  sid: 's999999',
  updated_at: '2021-03-033:24:30.770Z',
  roles: [ role ],
  active,
});
describe('Routes', () => {
  test('normal route to 404 page', () => {
    const history = createMemoryHistory();
    history.push('/campaigns');
    const store = mockStore({
      authenticated: {
        permissions: { admin: true },
        access: { campaigns: { ruleSubTypes: {} } },
      },
      users: {
        items: [ createUser(1, '<PERSON> Potter'), createUser(4, 'Albus Dumbledore', false, 4) ],

        isLoading: false,
      },
      campaigns: {
        items: [],
      },
      applications: {},
      pages: {},
      containers: {},
      form: {
        exportModal: {
          values: {
            exportFields: [ 'id', 'application' ],
          },
        },
      },
    });
    const { queryByText, rerender } = render(
      <Provider store={store}>
        <Router history={history}>
          <Routes />
        </Router>
      </Provider>
    );

    expect(queryByText('Campaigns')).toBeInTheDocument();

    history.push('/mars-perserverance-advertising-campaigns');
    rerender(
      <Provider store={store}>
        <Router history={history}>
          <Routes />
        </Router>
      </Provider>
    );

    expect(queryByText('Manage Martian Campaigns')).not.toBeInTheDocument();
    expect(queryByText('Page not found')).toBeInTheDocument();
  });
});
