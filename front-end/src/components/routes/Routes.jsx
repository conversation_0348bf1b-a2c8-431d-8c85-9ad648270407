import React from 'react';
import { Route, Switch } from 'react-router-dom';

import Redirect from './Redirect';

// teams
import TeamsDetails from '../teams/details';
import TeamsList from '../teams/list';

// campaign/alerts screens
import { AlertList, CampaignList } from '../../components/rules/listContainer';
import { AlertDetails, CampaignDetails } from '../../components/rules/detailsContainer';

// application placement
import ApplicationList from '../onboarding/applications/listContainer';
import ApplicationDetails from '../onboarding/applications/details';
import ContainerList from '../onboarding/containers/listContainer';
import ContainerDetails from '../onboarding/containers/detailsContainer';
import PageList from '../onboarding/pages/listContainer';
import PageDetails from '../onboarding/pages/detailsContainer';

// listing
import UserList from '../users/usersListContainer';
import UserDetails from '../../components/users/details';
import RoleList from '../../components/roles/rolesListContainer';
import RoleDetails from '../roles/details';

// legacy
import { VignetteList } from '../../components/vignette/listContainer';
import { VignetteDetails } from '../../components/vignette/detailsContainer';

import WebFragmentPreview from '../../components/fragmentPreview/WebFragmentPreview.jsx';

// variable mapping
import VariableMappingActive from '../variableMapping/variableMappingActive';
import VariableMappingPendingCompare from '../variableMapping/variableMappingPendingCompare';
import VariableMappingDraft from '../variableMapping/variableMappingDraft';

// Message Centre
import MessageCentreCampaignList from '../messageCentre/campaigns/listContainer.jsx';
import MessageCentreCampaignDetails from '../messageCentre/campaigns/details.jsx';
import MessageCentreMessageList from '../messageCentre/messages/listContainer.jsx';
import MessageDetails from '../messageCentre/messages/details.jsx';

// Offer Management
import OffersList from '../offerManagement/listContainer.jsx';
import OffersDetails from '../offerManagement/details.jsx';

// misc error screens
import NotFound from './NotFound';

const Routes = () => (
  <Switch>
    <Route path="/" component={Redirect} exact/>

    <Route path="/teams" component={TeamsList} exact/>
    <Route path="/teams/create" component={TeamsDetails} exact/>
    <Route path="/teams/:id" component={TeamsDetails}/>

    <Route path="/campaigns/:type(estore|sol)" component={VignetteList} exact/>
    <Route path="/campaigns/:type(estore|sol)/create" component={VignetteDetails} exact/>
    <Route path="/campaigns/:type(estore|sol)/:id/:action?" component={VignetteDetails}/>

    <Route path="/campaigns" component={CampaignList} exact/>
    <Route path="/campaigns/create" component={CampaignDetails} exact/>
    <Route path="/campaigns/:id/:action?" component={CampaignDetails}/>
    <Route path="/ccau_campaigns" component={CampaignList} exact/>
    <Route path="/ccau_campaigns/create" component={CampaignDetails} exact/>
    <Route path="/ccau_campaigns/:id/:action?" component={CampaignDetails}/>

    <Route path="/alerts" component={AlertList} exact/>
    <Route path="/alerts/create" component={AlertDetails} exact/>
    <Route path="/alerts/:id/:action?" component={AlertDetails}/>

    <Route path="/users" component={UserList} exact/>
    <Route path="/users/create" component={UserDetails} exact/>
    <Route path="/users/:id" component={UserDetails}/>

    <Route path="/roles" component={RoleList} exact/>
    <Route path="/roles/create" component={RoleDetails} exact/>
    <Route path="/roles/:id" component={RoleDetails}/>

    <Route path="/applications" component={ApplicationList} exact/>
    <Route path="/applications/create" component={ApplicationDetails} exact/>
    <Route path="/applications/:id" component={ApplicationDetails}/>

    <Route path="/containers" component={ContainerList} exact/>
    <Route path="/containers/create" component={ContainerDetails} exact/>
    <Route path="/containers/:id" component={ContainerDetails}/>

    <Route path="/pages" component={PageList} exact/>
    <Route path="/pages/create" component={PageDetails} exact/>
    <Route path="/pages/:id" component={PageDetails}/>

    <Route path="/web-fragment-preview/:type(sol|estore)/:webfragmentId" component={WebFragmentPreview}/>

    <Route path="/variable-mapping/active" component={VariableMappingActive} exact/>
    <Route path="/variable-mapping/pending" component={VariableMappingPendingCompare} exact/>
    <Route path="/variable-mapping/draft" component={VariableMappingDraft} exact/>

    <Route path="/message-centre/campaigns" component={MessageCentreCampaignList} exact/>
    <Route path="/message-centre/campaigns/create" component={MessageCentreCampaignDetails} exact/>
    <Route path="/message-centre/campaigns/:id/:action" component={MessageCentreCampaignDetails} exact/>
    <Route path="/message-centre/messages" component={MessageCentreMessageList} exact/>
    <Route path="/message-centre/messages/:id/:action" component={MessageDetails} exact/>

    <Route path="/offers" component={OffersList} exact />
    <Route path="/offers/create" component={OffersDetails} exact />
    <Route path="/offers/:id/:action" component={OffersDetails} exact />

    <Route component={NotFound}/>
  </Switch>
);

export default Routes;
