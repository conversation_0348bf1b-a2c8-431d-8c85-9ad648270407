import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';

const Tag = ({
  index, // the index/key that this tag has
  value, // the value that is displayed in the Tag
  onDelete, // callback when the delete button has been clicked
  disabled, // whether the tag should be editable or not
  type, // Passing type as an optional prop to use it inside a react hook form
}) => {
  const handleDeleteClick = () => {
    return onDelete(index);
  };

  return (
    <div
      className={classnames({
        'tag': true,
        'tag--editing': !disabled,
      })}
      title={value}
    >
      <span className="tag__value">
        { value }
      </span>
      { !disabled &&
        <button type={type} className={classnames({ 'tag__delete': true, 'tag__delete--hidden': disabled })}
          onClick={handleDeleteClick}
          aria-label="Delete"
        >✕</button>
      }
    </div>
  );
};

Tag.propTypes = {
  index: PropTypes.oneOfType([ PropTypes.string, PropTypes.number ]),
  value: PropTypes.string,
  disabled: PropTypes.bool,
  onDelete: PropTypes.func.isRequired,
  type: PropTypes.string,
};

export default Tag;
