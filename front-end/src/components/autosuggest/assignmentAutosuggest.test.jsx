import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import AssignmentAutosuggest, {
  AssignmentSuggestionRenderer,
  AssignmentTagRenderer,
} from './assignmentAutosuggest';

const mockData = [
  {
    key: '1',
    value: 'Dog',
  },
  {
    key: '2',
    value: 'beaver',
  },
  {
    key: '3',
    value: 'cat',
  },
  {
    key: '4',
    value: 'bat',
  },
  {
    key: '5',
    value: 'horse',
  },
];

describe('AssignmentAutosuggest', () => {
  const data = [
    { key: 's0001', value: 'A11yeg Berman' },
    { key: 's0002' },
  ];
  const { container } = render(
    <AssignmentAutosuggest
      data={data}
      onChange={jest.fn()}
    />
  );

  expect(container).toMatchSnapshot();

  it('Assignment suggestions close after a suggestion is made', () => {
    const onChange = jest.fn();
    const { getByText, getByTestId, container, getByPlaceholderText } = render(
      <AssignmentAutosuggest
        data={mockData}
        onChange={onChange}
        editable
      />
    );

    const selectionsShown = () => container.getElementsByClassName('autosuggest__suggestion').length;

    // before we've selected anything selections shouldn't be shown
    expect(selectionsShown()).toBe(0);

    // focus on autoSuggest to bring up the selections
    const autosuggestInput = getByPlaceholderText('Please enter a value');

    fireEvent.click(getByTestId('edit'));
    expect(autosuggestInput).toHaveFocus();

    expect(selectionsShown()).toBe(5);

    // select the 'beaver'
    fireEvent.click(getByText('beaver'));
    expect(onChange).toHaveBeenCalled();

    // selections should no longer be shown
    expect(selectionsShown()).toBe(0);
  });

  it('AssignmentSuggestionRenderer with a normal value', () => {
    const wrapper = render(<AssignmentSuggestionRenderer value="Mike Haas" />);
    expect(wrapper.asFragment()).toMatchSnapshot();
  });

  it('AssignmentSuggestionRenderer with no value', () => {
    const wrapper = render(<AssignmentSuggestionRenderer />);
    expect(wrapper.asFragment()).toMatchSnapshot();
  });

  it('AssignmentSuggestionRenderer a single word', () => {
    const wrapper = render(<AssignmentSuggestionRenderer value="Mike" />);
    expect(wrapper.asFragment()).toMatchSnapshot();
  });

  it('AssignmentSuggestionRenderer a single letter', () => {
    const wrapper = render(<AssignmentSuggestionRenderer value="M" />);
    expect(wrapper.asFragment()).toMatchSnapshot();
  });

  it('AssignmentTagRenderer', () => {
    const mockFn = jest.fn();
    const wrapper = render(<AssignmentTagRenderer value="Mike Haas" onDelete={mockFn} index="0"/>);
    fireEvent.click(wrapper.getByText('✕'));
    expect(mockFn).toHaveBeenCalled();
  });
});
