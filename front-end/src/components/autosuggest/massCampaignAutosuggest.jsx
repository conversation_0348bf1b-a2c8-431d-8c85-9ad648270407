import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';

import Autosuggest from './autosuggest';

const MassCampaignAutosuggest = ({
  className,
  data,
  dataLegend,
  initialSelection,
  excludes,
  ...restProps
}) => {
  // convert initialSelection and excludes from arrays with keys to arrays with OG selections from data
  const initial = data.filter((item) => initialSelection && initialSelection.includes(item[dataLegend.keyName]));
  const inputData = data.filter((item) => !excludes || !excludes.includes(item[dataLegend.keyName]));

  return (
    <Autosuggest
      className={classnames('details__styled-autosuggest', className)}
      data={inputData}
      dataLegend={dataLegend}
      initialSelection={initial}
      {...restProps}
    />
  );
};

MassCampaignAutosuggest.defaultProps = {
  dataLegend: {
    keyName: 'code',
    valueName: 'value',
  },
};

MassCampaignAutosuggest.propTypes = {
  ...Autosuggest.propTypes,
  excludes: PropTypes.array,
};

export default MassCampaignAutosuggest;
