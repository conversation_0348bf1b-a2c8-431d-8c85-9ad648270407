import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import Avatar from 'canvas-core-react/lib/Avatar';
import IconUsername from 'canvas-core-react/lib/IconUsername';

import { uniqueId, createInitialsFromName } from '../../utils';
import Autosuggest from './autosuggest';

const AssignmentAutosuggest = (props) => {
  const id = props.id || useRef(uniqueId()).current.toString();
  return (
    <div className="assignment">
      <label
        className={classnames('assignment__label', 'label')}
        htmlFor={id}
      >
        Assignee
      </label>
      <Autosuggest
        id={id}
        className="assignment__autosuggest"
        hideSuggestionsAfterSelection
        {...props}
        renderers={{
          tagRenderer: AssignmentTagRenderer,
          suggestionRenderer: AssignmentSuggestionRenderer,
          emptyViewRenderer: AssignmentEmptyRenderer,
        }}
      />
    </div>
  );
};
AssignmentAutosuggest.propTypes = {
  classname: PropTypes.string,
  id: PropTypes.string,
  onChange: PropTypes.func.isRequired, // fired on every change to the input field
  data: PropTypes.array.isRequired, // the data from which to make selections
  initialSelection: PropTypes.array, //
  editable: PropTypes.bool,
};

export const AssignmentSuggestionRenderer = ({ value }) => {
  return (
      <>
        <div className="autosuggest__avatar">
          <Avatar size={32} text={createInitialsFromName(value)} ariaLabel={value || ''} />
        </div>
        <span className="autosuggest__name">
          { value }
        </span>
    </>
  );
};
AssignmentSuggestionRenderer.propTypes = {
  value: PropTypes.string,
};

export const AssignmentTagRenderer = ({
  index,
  value,
  onDelete,
  disabled,
}) => {
  const handleDeleteClick = () => {
    return onDelete(index);
  };

  return (
    <div className="tag" tabIndex="0" title={value}>
      <div className="tag__avatar" aria-hidden>
        <Avatar size={32} text={createInitialsFromName(value)} ariaLabel={`${value}`} />
      </div>
      <span className="tag__value">
        { value && value.split(' ').reverse().join(', ') }
      </span>
      <button className={classnames({
        'tag__delete': true,
        'tag__delete--hidden': disabled,
      })}
      onClick={handleDeleteClick}
      disabled={disabled}
      aria-disabled={disabled}
      >
        ✕
      </button>
    </div>
  );
};
AssignmentTagRenderer.propTypes = {
  index: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onDelete: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
};

export const AssignmentEmptyRenderer = () => (
  <div className="assignment__no-assignees">
    <IconUsername size={24} />
    <span>Unassigned</span>
  </div>
);
export default AssignmentAutosuggest;
