import React from 'react';
import { mount, shallow } from 'enzyme';
import { fireEvent, render } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import Autosuggest, { Tag } from './';

const mockData = [
  {
    key: '1',
    value: 'Dog',
  },
  {
    key: '2',
    value: 'beaver',
  },
  {
    key: '3',
    value: 'cat',
  },
  {
    key: '4',
    value: 'bat',
  },
  {
    key: '5',
    value: 'horse',
  },
];

describe('Autosuggest component', () => {
  // Snapshot testing
  it('vanilla snapshot of the autosuggest component', () => {
    const wrapper = shallow(
      <Autosuggest
        data={mockData}
        initialSelection={[]}
        onChange={jest.fn()}
        editable
      />
    );
    expect(wrapper).toMatchSnapshot();
  });

  it('snapshot of the autosuggest component with items selected', () => {
    const onChangeFunc = jest.fn();
    const wrapper = mount(
      <Autosuggest
        data={mockData}
        initialSelection={mockData}
        onChange={onChangeFunc}
        editable
      />
    );
    expect(wrapper).toMatchSnapshot();

    expect(wrapper.find(Tag)).toHaveLength(mockData.length);

    // click the edit button to make it editable
    wrapper.find('.autosuggest__edit-btn').simulate('click');

    // click the first delete tag
    const deleteButton = wrapper.find('.tag__delete').at(1);
    expect(deleteButton).toHaveLength(1);
    deleteButton.simulate('click');

    // we should have reduced our selections by 1
    expect(wrapper.find(Tag)).toHaveLength(mockData.length - 1);
  });

  it('focus on input tab is first suggestion', () => {
    const { container } = render(
      <>
        <input name="before" className="before"/>
        <Autosuggest
          data={mockData}
          initialSelection={[]}
          onChange={jest.fn()}
          editable
        />
        <input name="after" className="after"/>
      </>
    );

    const beforeElem = container.getElementsByClassName('before')[0];
    const AutoElemInput = container.getElementsByClassName('autosuggest__input')[0];
    const afterElem = container.getElementsByClassName('after')[0];

    // Focus on before div
    beforeElem.focus();
    // Verify focus on before div
    expect(beforeElem).toHaveFocus();

    // Press 'Tab'
    userEvent.tab();
    // Verify focus on Autosuggest
    expect(AutoElemInput).toHaveFocus();

    // Press 'Tab'
    userEvent.tab();
    // Verify focus on after div
    expect(afterElem).toHaveFocus();

    // Press 'Shift + Tab'
    userEvent.tab({ shift: true });
    // Verify focus on Autosuggest
    expect(AutoElemInput).toHaveFocus();

    // Press 'ArrowDown'
    fireEvent.keyDown(document.activeElement, { key: 'ArrowDown' });
    // Verify focus on first Suggestion
    expect(container.getElementsByClassName('autosuggest__suggestion')[0]).toHaveFocus();

    // Press 'ArrowUp'
    fireEvent.keyDown(document.activeElement, { key: 'ArrowUp' });
    // Verify focus is still on first Suggestion
    expect(container.getElementsByClassName('autosuggest__suggestion')[0]).toHaveFocus();

    // Press 'ArrowDown'
    fireEvent.keyDown(document.activeElement, { key: 'ArrowDown' });
    // Verify focus on second Suggestion
    expect(container.getElementsByClassName('autosuggest__suggestion')[1]).toHaveFocus();

    // Press 'ArrowDown'
    fireEvent.keyDown(document.activeElement, { key: 'ArrowDown' });
    // Verify focus on third Suggestion
    expect(container.getElementsByClassName('autosuggest__suggestion')[2]).toHaveFocus();
  });

  it('snapshot of the autosuggest component when id, label and error is passed', () => {
    const wrapper = shallow(
      <Autosuggest
        data={mockData}
        initialSelection={[]}
        onChange={jest.fn()}
        id='platforms'
        label='platforms'
        className='details__styled-autosuggest'
        error='Required'
      />
    );
    expect(wrapper).toMatchSnapshot();
    expect(wrapper.find('.autosuggest__error')).toHaveLength(1);
  });
});
