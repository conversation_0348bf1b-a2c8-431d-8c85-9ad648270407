import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';

import IconEdit from 'canvas-core-react/lib/IconEdit';
import Error from 'canvas-core-react/lib/internal/Error';

import Tag from './tag';

const Autosuggest = forwardRef(({
  className,
  hideSuggestionsAfterSelection,
  id,
  onChange = () => {}, // fired on every change to the input field
  nonEditableIcon, // the icon to be shown when autosuggest can't be edited
  data, // the data from which to make selections
  limit,
  input,
  dataLegend: {
    keyName,
    valueName,
  },
  initialSelection, // the items to be selections initially
  showSuggestionsOnFocus,
  editable,
  renderers: {
    emptyViewRenderer: EmptyViewRenderer, // a view to be renderered when autosuggest is empty and not editable
    tagRenderer: TagRenderer, // an alternate component to be rendered in place of the default Tag
    suggestionRenderer: SuggestionRenderer, // an alternate component for the dropdown suggestion items
  },
  placeholder, // { empty, filled } || ''
  noLock,
  error,
  label,
}, ref) => {
  const [ suggestions, setSuggestions ] = useState([]);
  const [ showSuggestions, setShowSuggestions ] = useState(false);
  const [ searchValue, setSearchValue ] = useState('');
  const [ editMode, setEditMode ] = useState(false);
  const [ selections, setSelections ] = useState(initialSelection || []);
  // allow initial selections to be set once, otherwise whenever it changes selections will change
  const [ lockInitialSelections, setLockInitialSelections ] = useState(false);
  const [ suggestionBoxOffset, setSuggestionBoxOffset ] = useState(0);
  const [ inputRef, setInputRef ] = useState(null);
  const [ firstSuggestionRef, setFirstSuggestionRef ] = useState(null);
  const [ editRef, setEditRef ] = useState(null);

  const noSelections = selections && selections.length === 0;
  const limitReached = limit && selections.length >= limit;

  useImperativeHandle(ref, () => ({
    resetSelections: () => setSelections([]),
  }));

  useEffect(() => {
    if (initialSelection && initialSelection.length > 0 && !lockInitialSelections) {
      setSelections(initialSelection);
      if (!noLock) {
        setLockInitialSelections(true);
      }
    }
  }, [ initialSelection ]);

  // registering intervals so that the suggestion box dropdown animates with the input box
  useEffect(() => {
    if (editRef) {
      let interval;
      editRef.addEventListener('transitionstart', (e) => {
        // this ensures that this setInterval is only run for 1 property, since it is fired for
        // every property being transitioned (in my case it was width, opacity and margin-right
        if (e.propertyName !== 'width') {
          return;
        }
        interval = setInterval(updateSuggestionOffset, 1 / 60 * 1000);
      });

      editRef.addEventListener('transitionend', () => {
        clearInterval(interval);
      });
      // Clear interval so that component does not attempt to update state after it has been unmounted
      return () => clearInterval(interval);
    }
  }, [ editRef ]);

  const getSuggestions = (searchVal) => {
    if (searchVal.trim() === '' && !showSuggestionsOnFocus) {
      return [];
    }
    const s = selections.map((dataObject) => dataObject[keyName]);
    return data.filter(
      (dataObject) => (
        String(dataObject[valueName])
          .toLowerCase()
          .includes(searchVal.toLowerCase())) && !s.includes(dataObject[keyName])
    ).sort((a, b) => `${a[valueName]}`.localeCompare(`${b[valueName]}`));
  };

  // update the offset left of the autosuggestion dropdown on every update
  useEffect(() => {
    updateSuggestionOffset();
  });

  useEffect(() => {
    if (editMode) {
      setSuggestions(getSuggestions(searchValue));
    }

    if (limitReached && editMode) {
      setEditMode(false);
    }
  }, [ selections ]);

  const updateSuggestionOffset = () => inputRef && setSuggestionBoxOffset(inputRef.offsetLeft);

  // helpful function to determine if the child is a descendant of the parent
  const isDescendant = (parent, child) => {
    if (child === null || parent === null) {
      return false;
    }

    let node = child.parentNode;

    while (node !== null) {
      if (node === parent) {
        return true;
      }
      node = node.parentNode;
    }
    return false;
  };

  // actions to take when we blur from the input
  const blur = (e) => {
    // we are doing this check in the event that the reason we lost focus was to click on the suggestion box
    // dropdown, in which case we don't want to blur so that we can allow the suggestion box to stay open long
    // enough for the click to register
    if (isDescendant(e.currentTarget, e.relatedTarget)) {
      return;
    }
    blurField();
  };

  const blurField = () => {
    setEditMode(false);
    setShowSuggestions(false);
    setSearchValue('');
  };

  const handleTagDelete = (deletedKey) => {
    const filteredSelections = selections.filter((selectionObject) => selectionObject[keyName] !== deletedKey);
    setSelections(filteredSelections);
    setSearchValue('');
    onChange(filteredSelections);
    if (input && input.onChange) {
      input.onChange(filteredSelections);
    }
    focusInput();
  };

  const handleInputChange = (e) => {
    const { value } = e.target;
    setSuggestions(getSuggestions(value));
    setSearchValue(value);
  };

  const handleInputKeyDown = (e) => {
    handleEscKey(e);

    if (e.key === 'Enter') { // disable hitting enter from the input, it focuses and deletes
      e.preventDefault();
      return;
    }

    if (
      searchValue === '' && // the input is empty
      selections.length > 0 && // there are selections to delete
      e.key === 'Backspace') { // the key pressed is the delete key
      const selectionsCopy = [ ...selections ];
      const deleteKey = selectionsCopy.pop()[keyName];
      handleTagDelete(deleteKey);
    }

    if (e.key === 'ArrowDown') {
      e.preventDefault();
      firstSuggestionRef && firstSuggestionRef.focus();
    }
  };

  const handleInputFocus = (e) => {
    const value = e.target.value;
    setEditMode(true);
    if (showSuggestionsOnFocus) {
      setSuggestions(getSuggestions(value));
      setShowSuggestions(true);
    }
  };

  const handleSuggestionSelect = selectionsKey => {
    const selection = data.find((selectionObject) => selectionObject[keyName] === selectionsKey);

    const newSelections = Object.assign([], selections);
    newSelections.push(selection);
    setSelections(newSelections);

    onChange(newSelections);
    if (input && input.onChange) {
      input.onChange(newSelections);
    }
    focusInput();

    if (hideSuggestionsAfterSelection) {
      blurField();
    }
  };

  const handleEscKey = (e) => {
    if (e.key === 'Escape') {
      setShowSuggestions(false);
      setEditMode(false);
    }
  };

  const handleSuggestionKeydown = (e, suggestionKey) => {
    handleEscKey(e);

    if (e.key === 'ArrowDown') {
      e.preventDefault();
      e.target.nextElementSibling && e.target.nextElementSibling.focus(); // focus on input below
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      e.target.previousElementSibling && e.target.previousElementSibling.focus(); // focus on input above
    } else if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault(); // prevents a bug where the input is focused and triggers the tagDelete handler
      return handleSuggestionSelect(suggestionKey);
    }
  };

  const focusInput = () => {
    setEditMode(true);
    editable && inputRef && inputRef.focus();
  };

  const renderPlaceholder = () => {
    if (typeof placeholder === 'string') {
      return placeholder;
    }

    if (!editable && noSelections) {
      return placeholder.empty;
    }

    if (editable && noSelections) {
      return placeholder.filled;
    }
  };

  const renderInputField = () => {
    return (
      <input
        autoComplete="off"
        type="search"
        id={id}
        onChange={handleInputChange}
        onKeyDown={handleInputKeyDown}
        value={searchValue}
        onFocus={handleInputFocus}
        placeholder={renderPlaceholder()}
        disabled={!editable}
        readOnly={!editable}
        className={
          classnames({
            'autosuggest__input': true,
            'autosuggest__input--compact': editMode && !noSelections,
            'autosuggest__input--hidden': (!editMode && !noSelections) || limitReached,
          })
        }
        ref={setInputRef}
      />
    );
  };

  const renderSuggestionBox = () => {
    if (!showSuggestions || // shouldn't show
      suggestions.length === 0 || // there are no suggestions
      limitReached) { // we are at the limit
      return null;
    }

    return (
      <ul
        className="autosuggest__suggestions"
        style={{ left: suggestionBoxOffset, width: '100%' }}
      >
        {
          suggestions.map((suggestion, index) => {
            return (
              <li
                key={`${suggestion[keyName]}${index}`}
                className="autosuggest__suggestion"
                onClick={() => handleSuggestionSelect(suggestion[keyName])}
                onKeyDown={(e) => handleSuggestionKeydown(e, suggestion[keyName])}
                tabIndex={-1}
                role="button"
                ref={index === 0 ? setFirstSuggestionRef : null}
              >
                { SuggestionRenderer
                  ? <SuggestionRenderer value={suggestion[valueName]} />
                  : <span className="autosuggest__name">{ suggestion[valueName] }</span>
                }
              </li>
            );
          })
        }
      </ul>
    );
  };

  const renderSelectionTags = () => {
    return selections.map((selection, index) => {
      const tagProps = {
        index: selection[keyName],
        value: selection[valueName],
        onDelete: handleTagDelete,
        disabled: !(editable && editMode),
      };
      const TagComponent = TagRenderer || Tag;
      return <TagComponent key={`${selection[keyName]}${index}`} {...tagProps} />;
    });
  };

  const renderEmptyView = () => noSelections && !editable && EmptyViewRenderer && <EmptyViewRenderer />;

  const renderInputIcon = () => {
    if (editable) {
      return (
        <div
          className={classnames(
            'autosuggest__edit-btn',
            { 'autosuggest__edit-btn--focused': editMode }
          )}
          ref={setEditRef}
          data-testid="edit"
          onClick={focusInput}
        >
          <IconEdit size={24} />
        </div>
      );
    } else if (noSelections && nonEditableIcon) {
      return (
        <div className="autosuggest__non-editable-icon">
          { nonEditableIcon }
        </div>
      );
    }
  };

  if (renderEmptyView()) {
    return (
      <div
        className={
          classnames({
            'autosuggest': true,
            [className]: !!className,
          })
        }
      >
        { renderEmptyView() }
      </div>
    );
  }

  return (
    <div
      className={
        classnames({
          'autosuggest': true,
          [className]: !!className,
        })
      }
      onBlur={blur}
    >
      { label && (
        <label
          htmlFor={id}
          className={classnames('autosuggest__label', { 'autosuggest__label--error': error })}
        >
          { label }
        </label>
      ) }
      <div
        className={
          classnames({
            'autosuggest__input-wrapper': true,
            'autosuggest__input-wrapper--editable': editable && editMode,
          })
        }
      >
        { renderSelectionTags() }
        { renderInputIcon() }
        { renderInputField() }
      </div>
      { error && <Error className="autosuggest__error" errorLabel='Error' errorMsg={error} /> }
      { renderSuggestionBox() }
    </div>
  );
});

Autosuggest.displayName = 'Autosuggest';

Autosuggest.defaultProps = {
  renderers: {},
  showSuggestionsOnFocus: true,
  hideSuggestionsAfterSelection: false,
  dataLegend: {
    keyName: 'key',
    valueName: 'value',
  },
  placeholder: 'Please enter a value',
};

Autosuggest.propTypes = {
  className: PropTypes.string,
  id: PropTypes.string,
  onChange: PropTypes.func, // fired on every change to the input field (in case of uncontrolled component)
  hideSuggestionsAfterSelection: PropTypes.bool, // if true, will hide suggestion dropdown when a selection is made
  nonEditableIcon: PropTypes.node, // the icon to be shown when autosuggest can't be edited
  data: PropTypes.array.isRequired, // the data from which to make selections
  dataLegend: PropTypes.shape({
    keyName: PropTypes.string, // the data key that will be used as the value of the data key: defaults to `key`
    valueName: PropTypes.string, // the data key that will be used as the value of the data value: defaults to `value`
  }),
  initialSelection: PropTypes.array, // the data that is initially selected
  editable: PropTypes.bool,
  showSuggestionsOnFocus: PropTypes.bool,
  limit: PropTypes.number, // the number of items that by default can be selected
  renderers: PropTypes.shape({
    emptyViewRenderer: PropTypes.func, // a view to be renderered when autosuggest is empty and not editable
    tagRenderer: PropTypes.func, // an alternate component to be rendered in place of the default Tag
    suggestionRenderer: PropTypes.func, // an alternate component for the dropdown suggestion items
  }),
  placeholder: PropTypes.oneOfType([
    PropTypes.string, // when its a constant placeholder,
    PropTypes.shape({
      empty: PropTypes.string, // placeholder for when there are no selections
      filled: PropTypes.string, // placeholder for when selections have been chosen
    }),
  ]),
  input: PropTypes.object, // in case the component is controlled by redux-form
  noLock: PropTypes.bool,
  error: PropTypes.string,
  label: PropTypes.string,
};

export default Autosuggest;
