.tag {
  display: flex;
  border: 0.1rem solid $canvas-gray-500;
  border-radius: 2.5rem;
  outline: none;
  padding: 0.6rem;
  margin: 0 0.8rem 0.5rem 0;
  min-height: $autosuggest-tag-height;
  font-size: 1.4rem;
  font-weight: 600;
  transition: all 0.2s ease;

  &--editing {
    padding-right: 0.4rem; // - 2
    padding-left: 0; // - 5
  }

  &__value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0.3rem 0.7rem;
  }

  &__delete {
    padding: 0;
    font-weight: bold;
    font-size: 1.2rem;
    background: none;
    border: none;
    outline: none;
    cursor: pointer;
    // for the purposes of animation
    transition: all 0.2s ease;
    width: 1rem;
    overflow: hidden;

    &--hidden {
      opacity: 0;
      width: 0;
      pointer-events: none;
    }

    &:hover {
      color: #000;
    }
  }
}
