// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[` 1`] = `
<div>
  <div
    class="assignment"
  >
    <label
      class="assignment__label label"
      for="0"
    >
      Assignee
    </label>
    <div
      class="autosuggest assignment__autosuggest"
    >
      <div
        class="assignment__no-assignees"
      >
        <svg
          aria-hidden="true"
          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 krCJZm SvgIcon__icon"
          color="currentColor"
          focusable="false"
          role="presentation"
          size="24"
          viewBox="0 0 30 30"
        >
          <path
            clip-rule="evenodd"
            d="M16.4655 15.5344C20.0649 15.5344 22.9827 12.6166 22.9827 9.01719C22.9827 5.41781 20.0649 2.49994 16.4655 2.49994C12.8661 2.49994 9.94824 5.41781 9.94824 9.01719C9.94824 12.6166 12.8661 15.5344 16.4655 15.5344Z"
            fill="none"
            fill-rule="evenodd"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M29.5 29.4999V26.3964C29.5 22.9684 26.4779 20.1895 22.75 20.1895H9.25001C5.52208 20.1895 2.5 22.9684 2.5 26.3964V29.4999"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
        <span>
          Unassigned
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`AssignmentAutosuggest AssignmentSuggestionRenderer a single letter 1`] = `
<DocumentFragment>
  <div
    class="autosuggest__avatar"
  >
    <div
      class="Avatarstyle__AvatarBlock-canvas-core__sc-1xypltv-0 jbVYQc"
    >
      <span
        class="Avatarstyle__AvatarWrap-canvas-core__sc-1xypltv-1 iXZXhN Avatar__wrap Avatar--32 Avatar--green"
      >
        M
      </span>
    </div>
  </div>
  <span
    class="autosuggest__name"
  >
    M
  </span>
</DocumentFragment>
`;

exports[`AssignmentAutosuggest AssignmentSuggestionRenderer a single word 1`] = `
<DocumentFragment>
  <div
    class="autosuggest__avatar"
  >
    <div
      class="Avatarstyle__AvatarBlock-canvas-core__sc-1xypltv-0 jbVYQc"
    >
      <span
        class="Avatarstyle__AvatarWrap-canvas-core__sc-1xypltv-1 iXZXhN Avatar__wrap Avatar--32 Avatar--green"
      >
        MI
      </span>
    </div>
  </div>
  <span
    class="autosuggest__name"
  >
    Mike
  </span>
</DocumentFragment>
`;

exports[`AssignmentAutosuggest AssignmentSuggestionRenderer with a normal value 1`] = `
<DocumentFragment>
  <div
    class="autosuggest__avatar"
  >
    <div
      class="Avatarstyle__AvatarBlock-canvas-core__sc-1xypltv-0 jbVYQc"
    >
      <span
        class="Avatarstyle__AvatarWrap-canvas-core__sc-1xypltv-1 iXZXhN Avatar__wrap Avatar--32 Avatar--green"
      >
        MH
      </span>
    </div>
  </div>
  <span
    class="autosuggest__name"
  >
    Mike Haas
  </span>
</DocumentFragment>
`;

exports[`AssignmentAutosuggest AssignmentSuggestionRenderer with no value 1`] = `
<DocumentFragment>
  <div
    class="autosuggest__avatar"
  >
    <div
      class="Avatarstyle__AvatarBlock-canvas-core__sc-1xypltv-0 jbVYQc"
    >
      <span
        class="Avatarstyle__AvatarWrap-canvas-core__sc-1xypltv-1 cksffR Avatar__wrap Avatar--32 Avatar--null"
      />
    </div>
  </div>
  <span
    class="autosuggest__name"
  />
</DocumentFragment>
`;
