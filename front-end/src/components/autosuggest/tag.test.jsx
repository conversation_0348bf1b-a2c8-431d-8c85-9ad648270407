import { shallow } from 'enzyme';
import { Tag } from './index';
import React from 'react';

const mockData = [
  {
    key: '1',
    value: 'Dog',
  },
  {
    key: '2',
    value: 'beaver',
  },
  {
    key: '3',
    value: 'cat',
  },
  {
    key: '4',
    value: 'bat',
  },
  {
    key: '5',
    value: 'horse',
  },
];

describe('Tag component', () => {
  it('should render tag component, showing delete icon', () => {
    const mockDeleteEvent = jest.fn();
    const wrapper = shallow(
      <Tag
        index={mockData[0].key}
        value={mockData[0].value}
        onDelete={mockDeleteEvent}
      />
    );
    expect(wrapper).toMatchSnapshot();

    // click the delete icon
    const deleteButton = wrapper.find('.tag__delete');
    deleteButton.simulate('click');
    expect(mockDeleteEvent).toHaveBeenCalled();
  });

  it('should render tag component, without showing delete icon', () => {
    const deleteEventMock = jest.fn();
    const wrapper = shallow(
      <Tag
        {...mockData[1]}
        disabled
        onDelete={deleteEventMock}
      />
    );
    expect(wrapper).toMatchSnapshot();
  });
});
