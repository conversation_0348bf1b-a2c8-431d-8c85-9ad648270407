$autosuggest-tag-height: 3.8rem;

.autosuggest {
  background-color: $brand-white;
  position: relative;

  &__input-wrapper {
    position: relative;
    padding-top: 0.5rem;
    min-height: $autosuggest-tag-height + 1rem;
    cursor: default;
    flex-wrap: wrap;
    display: flex;
    align-items: center;

    &--editable {
      border-color: red;
      cursor: text;

      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 0;
        height: 0.2rem;
        width: 100%;
        background-color: $brand-red;
      }
    }
  }

  &__label {
    font-family: $font-bold-family;
    font-size: 1.6rem;

    &--error {
      color: $brand-red;
    }

    + .autosuggest__input-wrapper {
      margin-top: 1.2rem;
    }
  }

  &__input {
    display: block;
    height: $autosuggest-tag-height;
    border: none;
    outline: none;
    font-family: $font-regular-family;
    font-size: 2rem;
    width: 24rem;
    transition: width 0.2s ease;
    overflow: hidden;
    flex-grow: 1;

    &--hidden {
      opacity: 0;
      width: 0;
    }

    &--compact {
      width: 16rem;
    }

    &:disabled {
      background-color: transparent;
    }
  }

  &__edit-btn {
    margin-right: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 2.4rem;
    overflow: hidden;

    &--focused {
      opacity: 0;
      width: 0;
      margin: 0;
      pointer-events: none;
    }
  }

  &__non-editable-icon {
    @extend .autosuggest__edit-btn;
  }

  &__suggestions {
    position: absolute;
    margin-top: 0;
    padding: 1.5rem;
    max-height: 22rem;
    background-color: #fff;
    box-shadow: 0 0.2rem 1rem $canvas-gray-400;
    border-radius: 0.2rem;
    z-index: 1000;
    cursor: pointer;
    overflow-y: auto;
  }

  &__suggestion {
    overflow: hidden;
    outline: none;
    padding: 0.6rem;
    height: 4.1rem;
    margin: 0.3rem;
    border-radius: 0.3rem;
    border: 0.2rem solid transparent;
    display: flex;
    align-items: center;

    &:hover,
    &:focus {
      background: #f6f3fe;
      border: 0.2rem solid #5d24c0;
    }
  }

  &__avatar {
    float: left;
  }

  &__name {
    display: inline-block;
    font-size: 1.3rem;
    font-weight: bold;

    &:not(:only-child) {
      margin-left: 2rem;
    }
  }
}

.assignment {
  padding: 1.2rem 0;
  display: flex;
  align-items: center;

  &__label {
    margin-right: 3rem;
    font-size: 1.6rem;
    color: $brand-black;
    font-family: $font-bold-family;
  }

  &__autosuggest {
    .autosuggest__input-wrapper {
      padding-top: 0;
    }
  }

  &__no-assignees {
    font-size: 1.8rem;
    display: flex;
    align-items: flex-end;

    .svg-icon {
      margin-right: 0.8rem;
    }
  }
}
