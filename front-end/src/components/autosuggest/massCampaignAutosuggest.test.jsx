import { render } from '@testing-library/react';
import MassCampaignAutosuggest from './massCampaignAutosuggest';
import React from 'react';

describe('MassCampaignAutosuggest', () => {
  test('matches snapshot', () => {
    const data = [ 0, 1, 2, 3, 4, 5 ].map((i) => ({ code: i, value: `Text ${i}` }));
    const initialSelection = data.slice(2);
    const exclusions = data.slice(4);

    const wrapper = render(
      <MassCampaignAutosuggest
        data={data}
        initialSelection={initialSelection}
        excludes={exclusions}
        onChange={jest.fn()}
      />
    );

    expect(wrapper.asFragment()).toMatchSnapshot();
  });
});
