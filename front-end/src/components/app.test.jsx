import React from 'react';
import { shallow } from 'enzyme';

import { App } from './app';

describe('App', () => {
  const wrapper = shallow(
    <App
      userUnauth={jest.fn()}
      logout={jest.fn()}
      getUserData={jest.fn()}
      permissions={{}}
      location={{
        search: '?error=USER_DEACTIVATED',
        pathname: '/campaigns',
      }}
      snackbar={{}}
      alertBanner={{}}
      addAlert={jest.fn()}
      removeAlert={jest.fn()}
      removeSnackbar={jest.fn()}
      authenticated={{
        name: 'Oleg Berman',
      }}
      toasts={[]}
      history={{
        push: jest.fn(),
        listen: jest.fn(),
      }}
    >
      App
    </App>,
  );

  global.snapshot(wrapper);
});

describe('App (Other Error)', () => {
  const wrapper = shallow(
    <App
      userUnauth={jest.fn()}
      logout={jest.fn()}
      getUserData={jest.fn()}
      permissions={{}}
      location={{
        search: '?error=OTHER_ERROR',
        pathname: '/campaigns',
      }}
      snackbar={{}}
      alertBanner={{}}
      addAlert={jest.fn()}
      removeAlert={jest.fn()}
      removeSnackbar={jest.fn()}
      authenticated={{
        name: 'Oleg Berman',
      }}
      toasts={[]}
      history={{
        push: jest.fn(),
        listen: jest.fn(),
      }}
    >
      App
    </App>,
  );

  global.snapshot(wrapper);
});

describe('App (logged in)', () => {
  document.cookie = 'auth=test-token;';
  const wrapper = shallow(
    <App
      userUnauth={jest.fn()}
      logout={jest.fn()}
      getUserData={jest.fn()}
      permissions={{ admin: true }}
      location={{ pathname: '/alerts' }}
      snackbar={{}}
      alertBanner={{}}
      addAlert={jest.fn()}
      removeAlert={jest.fn()}
      removeSnackbar={jest.fn()}
      authenticated={{
        name: 'Oleg Berman',
      }}
      toasts={[]}
      history={{
        push: jest.fn(),
        listen: jest.fn(),
      }}
    >
      App
    </App>,
  );

  global.snapshot(wrapper);
});

describe('App (logged out) and snackbar', () => {
  document.cookie = 'auth=test-token;';
  const wrapper = shallow(
    <App
      userUnauth={jest.fn()}
      logout={jest.fn()}
      getUserData={jest.fn()}
      permissions={{ admin: true }}
      location={{ pathname: '/alerts' }}
      snackbar={{
        isOpen: true,
        bold: true,
      }}
      alertBanner={{}}
      addAlert={jest.fn()}
      removeAlert={jest.fn()}
      removeSnackbar={jest.fn()}
      authenticated={{
        name: 'Admin',
        loggedOut: true,
        permissions: [ 'admin' ],
      }}
      toasts={[]}
      history={{
        push: jest.fn(),
        listen: jest.fn(),
      }}
    >
      App
    </App>,
  );

  global.snapshot(wrapper);
});

describe('App (logged in) - alert banner message', () => {
  document.cookie = 'auth=test-token;';
  const wrapper = shallow(
    <App
      userUnauth={jest.fn()}
      logout={jest.fn()}
      getUserData={jest.fn()}
      permissions={{ admin: true }}
      location={{ pathname: '/alerts' }}
      snackbar={{}}
      alertBanner={{
        message: 'Test message',
      }}
      addAlert={jest.fn()}
      removeAlert={jest.fn()}
      removeSnackbar={jest.fn()}
      authenticated={{
        name: 'Oleg Berman',
      }}
      toasts={[]}
      history={{
        push: jest.fn(),
        listen: jest.fn(),
      }}
    >
      App
    </App>,
  );

  global.snapshot(wrapper);
});
