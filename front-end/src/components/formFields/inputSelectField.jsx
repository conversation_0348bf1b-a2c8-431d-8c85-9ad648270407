import React from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import { fieldPropTypes } from 'redux-form';

import Selector from 'canvas-core-react/lib/Selector';
import Tooltip from 'canvas-core-react/lib/Tooltip';
import TextCaption from 'canvas-core-react/lib/TextCaption';

const InputSelectField = ({
  id,
  className,
  input: {
    name,
    value,
    onChange,
    onBlur,
  },
  meta,
  options,
  optionName,
  optionValue,
  placeholder,
  disabledIds = [],
  label,
  tooltip,
  noBlur,
  ...rest
}) => {
  const CustomLabelTooltip = () => {
    return (
      <div className='custom-select-tooltip'>
        <span className='custom-select-label-tooltip'>{ label }</span>
        { tooltip &&
          <Tooltip
            id="toggle-field-toolip"
            heading={tooltip.heading}
            infoButtonLabel="Info"
            closeButtonLabel="close"
          >
            <TextCaption component='p'>{ tooltip.body }</TextCaption>
          </Tooltip>
        }
      </div>
    );
  };

  return (
    <div className={classnames('input-select-field', className)}>
      <CustomLabelTooltip />
      <Selector
        label={label}
        name={name}
        id={id || name}
        onChange={onChange}
        onBlur={noBlur ? undefined : onBlur}
        defaultValue="default" // @todo Canvas has a bug regarding the default appearance of this selector, remove when bug has been fixed
        value={`${value}`} // expects a string
        placeholder={placeholder}
        error={meta.error && meta.touched ? { label: 'Error', messages: [ meta.error ] } : null}
        {...rest}
      >
        { options && options
          .sort((a, b) => (a.sortOrder && b.sortOrder)
            ? (a.sortOrder < b.sortOrder ? -1 : 1)
            : (a[optionName || 'name'].toLowerCase() < b[optionName || 'name'].toLowerCase() ? -1 : 1))
          .map((option, index) =>
            <option
              key={`${name}-${index}`}
              disabled={disabledIds.includes(option.id)}
              value={option[optionValue || 'id']}>
              { option[optionName || 'name'] }
            </option>
          ) }
      </Selector>
    </div>
  );
};

InputSelectField.propTypes = {
  id: PropTypes.string,
  label: PropTypes.string,
  tooltip: PropTypes.object,
  ...fieldPropTypes,
};

export default InputSelectField;
