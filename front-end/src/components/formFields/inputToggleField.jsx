import React from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import { fieldPropTypes } from 'redux-form';

import ToggleSwitch from 'canvas-core-react/lib/ToggleSwitch';
import Tooltip from 'canvas-core-react/lib/Tooltip';
import TextCaption from 'canvas-core-react/lib/TextCaption';

const InputToggleField = ({
  id,
  className,
  input: {
    name,
    onChange,
    value,
  },
  meta,
  label,
  secondaryLabel,
  disabled,
  tooltip,
  ...rest
}) => {
  const toggleId = id || name;
  const displayError = meta.error && meta.touched;
  return (
    <div className={classnames('toggle-field', className)}>
      <label
        className="toggle-field__label"
        htmlFor={toggleId}
      >
        { label }
      </label>
      <ToggleSwitch
        name={name}
        className="toggle-field__toggle"
        id={toggleId}
        label={secondaryLabel || ''}
        checked={!!value || false}
        onChange={onChange}
        disabled={disabled}
        error={displayError ? { label: 'Error', messages: [ meta.error ] } : undefined}
      />
      {
        tooltip &&
        <span className="toggle-field__tooltip">
          <Tooltip
          id="toggle-field-toolip"
          heading={tooltip.heading}
          infoButtonLabel="Info"
          closeButtonLabel="close"
          >
          <TextCaption component="p">{ tooltip.body }</TextCaption>
          </Tooltip>
        </span>
      }
    </div>
  );
};

InputToggleField.propTypes = {
  id: PropTypes.string,
  classname: PropTypes.string,
  label: PropTypes.string.isRequired,
  secondaryLabel: PropTypes.string,
  ...fieldPropTypes,
};

export default InputToggleField;
