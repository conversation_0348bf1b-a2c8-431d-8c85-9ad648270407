import React from 'react';
import Checkbox from 'canvas-core-react/lib/Checkbox';

const InputCheckboxField = (field) => (
  <Checkbox
    className={field.className}
    id={field.input.name}
    disabled={field.disabled}
    label={field.label}
    onChange={field.input.onChange}
    checked={Boolean(field.input.value)}
    error={field.meta.touched && field.meta.error ? { label: 'Error', message: field.meta.error } : null}
    tooltip={field.tooltip}
  />
);

export default InputCheckboxField;
