import React from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import { fieldPropTypes } from 'redux-form';

import Autosuggest from '../autosuggest-v2';

const InputSelectsField = ({
  id,
  className,
  input,
  meta,
  options,
  optionName,
  optionValue,
  labelSecondary,
  ...rest
}) => {
  return (
    <div className={classnames('input-select-field', className)}>
      <Autosuggest
        id={id || input.name}
        input={input}
        data={options && options
          .sort((a, b) => a[optionName || 'name'].toLowerCase() < b[optionName || 'name'].toLowerCase() ? -1 : 1)}
        dataLegend={{ keyName: 'id', valueName: 'name' }}
        limit={options.length}
        ref={rest.childRef}
        {...rest}
      />
    </div>
  );
};

InputSelectsField.propTypes = {
  id: PropTypes.string,
  ...fieldPropTypes,
};

export default InputSelectsField;
