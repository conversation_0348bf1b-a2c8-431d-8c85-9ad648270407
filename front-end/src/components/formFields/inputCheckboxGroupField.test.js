import React from 'react';
import PropTypes from 'prop-types';
import { render, fireEvent } from '@testing-library/react';
import InputCheckboxGroupField from './inputCheckboxGroupField';

describe('checkboxGroupField', () => {
  const options = Array.from({ length: 5 }, (item, i) => ({
    id: i + 1,
    name: `Option ${i + 1}`,
  }));
  const inputProps = {
    onChange: jest.fn(),
    onBlur: jest.fn(),
    onDragStart: jest.fn(),
    onDrop: jest.fn(),
    onFocus: jest.fn(),
    name: '',
  };
  const metaProps = {
    active: true,
    autofilled: true,
    asyncValidating: true,
    dirty: true,
    form: '',
    invalid: true,
    pristine: true,
    submitting: true,
    submitFailed: true,
    touched: true,
    valid: true,
    visited: true,
    dispatch: jest.fn(),
  };

  test('it sends correct values onChange', () => {
    const onChange = jest.fn();
    const CheckboxGroup = ({ value }) => (
      <InputCheckboxGroupField
        options={options}
        input={{
          ...inputProps,
          value,
          onChange,
        }}
        meta={metaProps}
        label=""
      />
    );

    CheckboxGroup.propTypes = { value: PropTypes.array };
    const { getByLabelText, rerender } = render(<CheckboxGroup value={[]} />);
    // check option 3
    fireEvent.click(getByLabelText('Option 3'));
    expect(onChange).toHaveBeenCalledWith([ 3 ]);

    // select option 5
    rerender(<CheckboxGroup value={[ 3 ]} />);
    onChange.mockClear();
    fireEvent.click(getByLabelText('Option 5'));
    expect(onChange).toHaveBeenCalledWith([ 3, 5 ]);

    rerender(<CheckboxGroup value={[ 3, 5 ]} />);

    // uncheck option 3
    fireEvent.click(getByLabelText('Option 3'));
    expect(onChange).toHaveBeenCalledWith([ 5 ]);
  });
});
