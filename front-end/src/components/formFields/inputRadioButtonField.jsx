import React from 'react';
import RadioButton from 'canvas-core-react/lib/RadioButton';

const InputRadioButtonField = (field) => (
  <RadioButton
    {...field.input}
    {...field}
    checked={(field.input.value === field.id) || field.checked}
    error={<PERSON><PERSON>an(field.meta.touched && field.meta.error)}
    onChange={() => field.input.onChange(field.id)} />
);

export default InputRadioButtonField;
