import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import ReactDateTime from 'react-datetime';
import TextField from 'canvas-core-react/lib/TextField';
import { Controller } from 'react-hook-form';

import { formatReactDateTimeValue } from '../../../utils';
import { DATE_PRESENTATION_FORMAT } from '../../../constants';

function InputDateField({
  autoComplete = 'off',
  control,
  disabled,
  label,
  placeholder,
  rules,
  name,
  className,
}) {
  const id = name || label.toLowerCase().replace(/ /g, '-');

  return (
    <Controller
      name={id}
      control={control}
      rules={rules}
      render={({ field, fieldState: { error } }) => {
        return (
          <ReactDateTime
            className={classnames(className)}
            name={field.name}
            onChange={field.onChange}
            onBlur={field.onBlur}
            dateFormat={DATE_PRESENTATION_FORMAT}
            timeFormat={false}
            value={formatReactDateTimeValue(field.value)}
            renderInput={(props, openCalendar) => {
              return (
                <TextField
                  id={id}
                  {...field}
                  {...props}
                  label={label}
                  placeholder={placeholder}
                  error={
                    error?.message && {
                      label: 'Error',
                      messages: [ error.message ],
                    }
                  }
                  onClick={openCalendar}
                  disabled={disabled}
                  autoComplete={autoComplete}
                />
              );
            }}
          />
        );
      }}
    />
  );
}

InputDateField.propTypes = {
  autoComplete: PropTypes.bool,
  className: PropTypes.string,
  control: PropTypes.object.isRequired,
  disabled: PropTypes.bool,
  label: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
  rules: PropTypes.object,
  name: PropTypes.string,
};

export default InputDateField;
