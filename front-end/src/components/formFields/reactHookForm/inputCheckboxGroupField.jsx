import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { Controller } from 'react-hook-form';

import Error from 'canvas-core-react/lib/internal/Error';
import Checkbox from 'canvas-core-react/lib/Checkbox';
import Tooltip from 'canvas-core-react/lib/Tooltip';
import TextCaption from 'canvas-core-react/lib/TextCaption';

const InputCheckboxGroupField = ({
  className,
  control,
  defaultValue = [],
  disabled,
  disabledIds = [],
  inline,
  label,
  name,
  options,
  rules,
  tooltip,
  checkBoxContainerClassName,
}) => {
  const id = (name || label).toLowerCase().replace(/ /g, '-');
  const checkboxGroupdId = id;

  const onChange = (id, isChecked, field) => {
    const output = [ ...field.value ];
    const idx = output.indexOf(id);
    isChecked && idx === -1 && output.push(id);
    !isChecked && idx !== -1 && output.splice(idx, 1);
    field.onChange(output);
  };

  return (
    <Controller
      name={id}
      control={control}
      defaultValue={defaultValue}
      rules={rules}
      render={({ field, fieldState: { error } }) =>
        <div className={classnames('checkbox-group-field', className)}>
          <label
            className={classnames('checkbox-group-field__label', { 'checkbox-group-field__label--error': error?.message })}
            htmlFor={checkboxGroupdId}
          >
            { label }
            { !!tooltip &&
            <Tooltip
              id="include-tooltip"
              heading={tooltip?.heading}
              infoButtonLabel="Info"
              closeButtonLabel="close"
            >
              <TextCaption component="div" className='checkbox-group-field__label__tooltip'>
                { tooltip?.text }
              </TextCaption>
            </Tooltip>
            }
          </label>
          <div className={classnames('checkbox-group-field__checkbox-container',
            { 'checkbox-group-field__checkbox-container--inline': inline },
            checkBoxContainerClassName,
          )}
          >
            { options.map(option => (
              <Checkbox
                id={`checkbox-${checkboxGroupdId}-${option.name}`}
                className={classnames('checkbox-group-field__checkbox', { 'checkbox-group-field__checkbox--inline': inline })}
                key={`icgf-${option.id}-${option.name}`}
                checked={field.value.includes(option.id)}
                label={option.name}
                value={`${option.id}`}
                onChange={e => onChange(option.id, e.target.checked, field)}
                disabled={disabled || disabledIds.includes(option.id)}
              />
            )) }
          </div>
          { error?.message && <Error errorLabel="Error" errorMsg={error.message} /> }
        </div>
      }
    />
  );
};

InputCheckboxGroupField.propTypes = {
  className: PropTypes.string,
  control: PropTypes.object.isRequired,
  defaultValue: PropTypes.array,
  disabled: PropTypes.bool,
  disabledIds: PropTypes.array,
  label: PropTypes.string.isRequired,
  name: PropTypes.string,
  inline: PropTypes.bool,
  options: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.oneOfType([ PropTypes.string, PropTypes.number ]).isRequired,
    name: PropTypes.string.isRequired,
  })).isRequired,
  rules: PropTypes.object,
  tooltip: PropTypes.object,
  checkBoxContainerClassName: PropTypes.string,
};

InputCheckboxGroupField.defaultProps = {
  inline: true,
};

export default InputCheckboxGroupField;
