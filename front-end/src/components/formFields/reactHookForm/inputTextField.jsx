import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { Controller } from 'react-hook-form';

import TextField from 'canvas-core-react/lib/TextField';

const InputTextField = ({
  autoComplete = false,
  control,
  disabled,
  label,
  placeholder,
  rules,
  name,
  type,
  className,
  ...props
}) => {
  const id = name || label.toLowerCase().replace(/ /g, '-');
  return (
    <Controller
      name={id}
      control={control}
      rules={rules}
      render={({ field, fieldState: { error } }) => (
        <div className={classnames(className)}>
          <TextField
            {...field}
            id={id}
            placeholder={placeholder}
            label={label}
            disabled={disabled}
            autoComplete={autoComplete || 'off'}
            defaultValue={field.value}
            type={type}
            error={
              error?.message && { label: 'Error', messages: [ error.message ] }
            }
            inputRef={field.ref}
            onChange={field.onChange}
            onBlur={field.onBlur}
            {...props}
          />
        </div>
      )}
    />
  );
};

InputTextField.propTypes = {
  autoComplete: PropTypes.bool,
  className: PropTypes.string,
  control: PropTypes.object.isRequired,
  disabled: PropTypes.bool,
  label: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
  rules: PropTypes.object,
  name: PropTypes.string,
  type: PropTypes.string,
};

export default InputTextField;
