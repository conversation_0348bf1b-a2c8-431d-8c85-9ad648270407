import React from 'react';
import PropTypes from 'prop-types';
import { Controller } from 'react-hook-form';

import RadioButton from 'canvas-core-react/lib/RadioButton';
import InputGroup from 'canvas-core-react/lib/InputGroup';

function InputRadioGroupButtonField({
  control,
  disabled,
  label,
  rules,
  name,
  className,
  id,
  inputGroupClassName,
  options,
  inline,
  labelLeft,
}) {
  return (
    <Controller
      name={name}
      control={control}
      key={id}
      rules={rules}
      render={({ field, fieldState: { error } }) => (
        <InputGroup
          legend={label}
          className={inputGroupClassName}
          error={error?.message && { label: 'Error', message: [ error.message ] }}
          inline={inline}
          labelLeft={labelLeft}
        >
          { options.map((item) => (
            <RadioButton
              {...field}
              id={`radio-${name}-${item.value}`}
              key={`radio-${name}-${item.value}`}
              data-testid={`radio-${name}-${item.value}`}
              label={item.label}
              disabled={disabled}
              name={item.name}
              value={item.value}
              inputRef={field.ref}
              onChange={field.onChange}
              onBlur={field.onBlur}
              checked={item.value === field.value}
              className={className}
            />
          )) }
        </InputGroup>
      )}
    />
  );
}

export default InputRadioGroupButtonField;

InputRadioGroupButtonField.propTypes = {
  classname: PropTypes.string,
  control: PropTypes.object.isRequired,
  disabled: PropTypes.bool,
  label: PropTypes.string.isRequired,
  rules: PropTypes.object,
  name: PropTypes.string,
  className: PropTypes.string,
  value: PropTypes.string,
  id: PropTypes.string,
  inputGroupClassName: PropTypes.string,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string,
      label: PropTypes.string.isRequired,
      value: PropTypes.string.isRequired,
    })
  ).isRequired,
  inline: PropTypes.bool,
  labelLeft: PropTypes.bool,
};
