import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { Controller } from 'react-hook-form';

import ToggleSwitch from 'canvas-core-react/lib/ToggleSwitch';
import Tooltip from 'canvas-core-react/lib/Tooltip';
import TextCaption from 'canvas-core-react/lib/TextCaption';

const InputToggleField = ({
  className,
  control,
  disabled,
  label,
  secondaryLabel,
  tooltip,
}) => {
  const id = label.toLowerCase().replace(/ /g, '-');
  return (
    <Controller
      name={id}
      control={control}
      render={({ field }) =>
        <div className={classnames('toggle-field', className)}>
          <ToggleSwitch
            name={label}
            className="toggle-field__toggle"
            id={id}
            label={secondaryLabel || ''}
            disabled={disabled}
            onChange={field.onChange}
            onBlur={field.onBlur}
            checked={field.value}
            value={String(field.value)}
          />
          {
            tooltip &&
              <Tooltip
              id="toggle-field-toolip"
              heading={tooltip.heading}
              infoButtonLabel="Info"
              closeButtonLabel="close"
              >
              <TextCaption component="p">{ tooltip.body }</TextCaption>
              </Tooltip>
          }
        </div>
      }
    />
  );
};

InputToggleField.propTypes = {
  className: PropTypes.string,
  control: PropTypes.object.isRequired,
  disabled: PropTypes.bool,
  label: PropTypes.string.isRequired,
  secondaryLabel: PropTypes.string,
  tooltip: PropTypes.shape({
    heading: PropTypes.string,
    body: PropTypes.string,
  }),
};

export default InputToggleField;
