import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { Controller } from 'react-hook-form';

import InputSlider from 'canvas-core-react/lib/InputSlider';

const InputSliderField = ({
  className,
  control,
  label,
  name,
  onChange,
  placeholder,
  rules,
  showError,
  disabled,
  ...rest
}) => {
  const id = (name || label).toLowerCase().replace(/ /g, '-');

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState: { error } }) => {
        return (
          <div className={classnames('input-slider-field', className)}>
            <InputSlider
              name={name}
              id={id}
              error={
                showError && error?.message && { label: 'Error', messages: [ error.message ] }
              }
              label={label}
              placeholder={placeholder}
              defaultValue={field.value}
              disabled={disabled}
              onBlur={field.onBlur}
              onChange={(value) => field.onChange(value)}
              {...rest}
              ref={field.ref}
            />
          </div>
        );
      }}
    />
  );
};

InputSliderField.propTypes = {
  className: PropTypes.string,
  control: PropTypes.object.isRequired,
  label: PropTypes.string.isRequired,
  name: PropTypes.string,
  onChange: PropTypes.func,
  optionName: PropTypes.string,
  optionValue: PropTypes.string,
  placeholder: PropTypes.string,
  rules: PropTypes.object,
  showError: PropTypes.bool,
  disabled: PropTypes.bool,
};

export default InputSliderField;
