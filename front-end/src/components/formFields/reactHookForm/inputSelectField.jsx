import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { Controller } from 'react-hook-form';

import Selector from 'canvas-core-react/lib/Selector';

const InputSelectField = ({
  className,
  control,
  disabledIds = [],
  label,
  name,
  onChange,
  options,
  optionName,
  optionValue,
  placeholder,
  rules,
  ...rest
}) => {
  const id = (name || label).toLowerCase().replace(/ /g, '-');
  return (
    <Controller
      name={id}
      control={control}
      rules={rules}
      render={({ field, fieldState: { error } }) =>
        <div className={classnames('input-select-field', className)}>
          <Selector
            name={id}
            id={id}
            error={error?.message && { label: 'Error', messages: [ error.message ] }}
            label={label}
            value={field?.value} // expects a string
            placeholder={placeholder}
            onBlur={field.onBlur}
            onChange={(e, val) => {
              onChange && onChange(e, val);
              field.onChange(e, val);
            }}
            {...rest}
          >
            { options && options
              .sort((a, b) => (a.sortOrder && b.sortOrder)
                ? (a.sortOrder < b.sortOrder ? -1 : 1)
                : (a[optionName || 'name'].toLowerCase() < b[optionName || 'name'].toLowerCase() ? -1 : 1))
              .map((option, index) =>
                <option
                  key={`${id}-${index}`}
                  data-testid={`${id}-option`}
                  disabled={disabledIds.includes(option.id)}
                  value={option[optionValue || 'id']}>
                  { option[optionName || 'name'] }
                </option>
              ) }
          </Selector>
        </div>
      }
    />
  );
};

InputSelectField.propTypes = {
  className: PropTypes.string,
  control: PropTypes.object.isRequired,
  disabledIds: PropTypes.array,
  label: PropTypes.oneOfType([ PropTypes.string, PropTypes.element ]),
  name: PropTypes.string,
  onChange: PropTypes.func,
  options: PropTypes.array,
  optionName: PropTypes.string,
  optionValue: PropTypes.string,
  placeholder: PropTypes.string,
  rules: PropTypes.object,
};

export default InputSelectField;
