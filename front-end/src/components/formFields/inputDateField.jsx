import React from 'react';
import classnames from 'classnames';
import ReactDateTime from 'react-datetime';
import TextField from 'canvas-core-react/lib/TextField';

import { formatReactDateTimeValue } from '../../utils';
import { DATE_PRESENTATION_FORMAT, TIME_PRESENTATION_FORMAT } from '../../constants';

const InputDateField = (field) => {
  return (
    <ReactDateTime
      className={classnames('input-date-field', field.className)}
      {...field.input}
      dateFormat={DATE_PRESENTATION_FORMAT}
      timeFormat={TIME_PRESENTATION_FORMAT}
      value={formatReactDateTimeValue(field.input.value)}
      renderInput={(props, openCalendar) => (
        <TextField
          id={field.input.name}
          {...field}
          {...props}
          error={field.meta.error && field.meta.touched ? { label: 'Error', messages: [ field.meta.error ] } : null}
          onClick={openCalendar} />
      )} />
  );
};

export default InputDateField;
