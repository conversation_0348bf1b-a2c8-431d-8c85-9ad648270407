import React from 'react';
import { fieldPropTypes } from 'redux-form';

import AutoSuggest from '../autosuggest/autosuggest';

const AutoSuggestField = ({
  input,
  meta,
  ...rest
}) => {
  return (
    <AutoSuggest
      {...input}
      {...rest}
      error={meta.error && meta.touched ? meta.error : ''}
    />
  );
};

AutoSuggestField.propTypes = {
  ...fieldPropTypes,
};

export default AutoSuggestField;
