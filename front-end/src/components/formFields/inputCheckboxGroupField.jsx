import React from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import { fieldPropTypes } from 'redux-form';
import Error from 'canvas-core-react/lib/internal/Error';
import Checkbox from 'canvas-core-react/lib/Checkbox';
const InputCheckboxGroupField = ({
  id,
  className,
  input,
  meta,
  options,
  label,
  inline,
  disabledIds = [],
  disabled,
}) => {
  const checkboxGroupdId = id || input.name;
  const showError = meta.error && meta.touched;

  const onChange = (idOnChange, isChecked) => {
    const output = [ ...input.value ];

    if (isChecked && !output.includes(idOnChange)) {
      output.push(idOnChange);
    } else if (!isChecked) {
      const indexToDelete = output.indexOf(idOnChange);
      if (indexToDelete !== -1) {
        output.splice(indexToDelete, 1);
      }
    }
    input.onChange(output);
  };

  const checkboxes = options.map(option => (
    <Checkbox
      id={`checkbox-${checkboxGroupdId}-${option.name}`}
      className={classnames('checkbox-group-field__checkbox', { 'checkbox-group-field__checkbox--inline': inline })}
      key={`icgf-${option.id}-${option.name}`}
      checked={input.value.includes(option.id)}
      label={option.name}
      value={`${option.id}`}
      onChange={e => onChange(option.id, e.target.checked)}
      disabled={disabledIds.includes(option.id) || disabled}
    />
  ));

  return (
    <div className={classnames('checkbox-group-field', className)}>
      <label
        className={classnames('checkbox-group-field__label', { 'checkbox-group-field__label--error': showError })}
        htmlFor={checkboxGroupdId}
      >
        { label }
      </label>
      <div
        className={
          classnames('checkbox-group-field__checkbox-container', { 'checkbox-group-field__checkbox-container--inline': inline })}
      >
        { checkboxes }
      </div>
      { showError && (
        <Error
          errorLabel="Error"
          errorMsg={meta.error}
        />)
      }
    </div>
  );
};

InputCheckboxGroupField.defaultProps = {
  inline: true,
};

InputCheckboxGroupField.propTypes = {
  id: PropTypes.string,
  classname: PropTypes.string,
  options: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.oneOfType([ PropTypes.string, PropTypes.number ]).isRequired,
    name: PropTypes.string.isRequired,
  })).isRequired,
  label: PropTypes.string.isRequired,
  inline: PropTypes.bool,
  ...fieldPropTypes,
};

export default InputCheckboxGroupField;
