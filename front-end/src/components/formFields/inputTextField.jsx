import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { fieldPropTypes } from 'redux-form';

import CanvasTextField from 'canvas-core-react/lib/TextField';
import Tooltip from 'canvas-core-react/lib/Tooltip';
import TextCaption from 'canvas-core-react/lib/TextCaption';

const InputTextField = ({
  id,
  className,
  input: {
    name,
    value,
    onChange,
    onBlur,
  },
  meta,
  placeholder,
  tooltip,
  ...rest
}) => (
  <div className={classnames('input-text-field', className)}>
    <CanvasTextField
      id={id || name}
      onChange={onChange}
      onBlur={onBlur}
      value={value}
      placeholder={placeholder}
      error={meta.error && meta.touched ? { label: 'Error', messages: [ meta.error ] } : undefined}
      tooltip={!!tooltip && <Tooltip
        heading={tooltip.heading}
        infoButtonLabel="Info"
        closeButtonLabel="close"
      >
        <TextCaption component='p'>{ tooltip.body }</TextCaption>
      </Tooltip>}
      {...rest}
    />
  </div>
);

InputTextField.propTypes = {
  id: PropTypes.string,
  classname: PropTypes.string,
  ...fieldPropTypes,
};

export default InputTextField;
