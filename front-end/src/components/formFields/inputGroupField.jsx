import React, { useMemo } from 'react';
import InputGroup from 'canvas-core-react/lib/InputGroup';
import { uniqueId } from '../../utils';

const InputGroupField = (field) => {
  const id = useMemo(uniqueId, []);

  return (
    <InputGroup
      id={field.id || `${id}`}
      legend={field.legend || ''}
      {...field}
      error={field.meta.touched && field.meta.error ? { label: 'Error', message: field.meta.error } : null}
    >
      { field.children }
    </InputGroup>
  );
};

export default InputGroupField;
