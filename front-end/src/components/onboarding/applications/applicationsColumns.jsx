
import React from 'react';
import { Link } from 'react-router-dom';

import CanvasLink from 'canvas-core-react/lib/Link';
import Tooltip from 'canvas-core-react/lib/Tooltip';
import TextBody from 'canvas-core-react/lib/TextBody';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import ToggleSwitch from 'canvas-core-react/lib/ToggleSwitch';

const renderLink = (id, name, description) => (
  <>
    <CanvasLink
      href=""
      component={Link}
      type="emphasis"
      to={`/applications/${id}`}
    >
      { name }
    </CanvasLink>
    {
      description && (
        <Tooltip
          position={id === 1 ? 'bottom' : 'top'}
          id={`tooltip-${id}-${description}`}
          closeButtonLabel="Close Tooltip"
          infoButtonLabel="Info"
          heading={name}
          iconColor="blue"
        >
          <TextBody component="p">{ description }</TextBody>
        </Tooltip>
      )
    }
  </>
);

export const tableColumns = ({
  canEditAllApplications,
  canEditOwnTeamApplications,
  handleChangeApplicationStatus,
  currentUser,
  filters,
  sortableColumnProperties,
}) => {
  const columns = [
    {
      name: 'Application Name',
      cellFormatter: ({ id, name, description }) => renderLink(id, name, description),
      style: { textAlign: 'left' },
      selector: 'name',
      ...sortableColumnProperties('name', filters),
    },
    {
      name: 'ID',
      selector: 'applicationId',
    },
    {
      name: 'Application Owner',
      cellFormatter: row => <TextCaption component="p">{ row.team?.length ? row.team[0] : '' }</TextCaption>,
      selector: '',
    },
    {
      name: 'Platforms',
      cellFormatter: row => (
        <div>
          { (row.platforms || []).map((platform) => (
            <TextCaption
              key={`platform-${platform}`}
              component="p"
            >
              { platform }
            </TextCaption>
          ))
          }
        </div>
      ),
      selector: '',
    },
    {
      name: 'Rule Types',
      cellFormatter: row => (
        <div>
          { (row.ruleTypes || []).map(ruleType => (
            <TextCaption
              key={`ruleType-${ruleType}`}
              component="p"
            >
              { ruleType }
            </TextCaption>
          ))
          }
        </div>
      ),
      selector: '',
    },
    {
      name: 'Status',
      cellFormatter: (row) => (
        <ToggleSwitch
          label=""
          id={`status-toggle-${row.id}`}
          data-testid={`status-toggle-${row.id}`}
          name={`status-toggle-${row.id}`}
          className="teams-list__toggle-switch"
          checked={row.status}
          onChange={(e) => handleChangeApplicationStatus(e, row.id)}
          disabled={!(canEditAllApplications || (canEditOwnTeamApplications && (row.team_id === currentUser?.team_id)))}
        />
      ),
      selector: 'status',
      tooltip: 'An active application can have active pages and containers. An inactive application can only have inactive pages and containers.',
    },
  ];
  return columns;
};
