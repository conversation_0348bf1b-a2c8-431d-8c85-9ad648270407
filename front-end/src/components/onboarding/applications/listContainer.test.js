import React from 'react';
import { Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import { createMemoryHistory } from 'history';
import mockAxios from 'axios';
import { render, fireEvent, act } from '@testing-library/react';
import { within } from '@testing-library/dom';

import ApplicationListConnected from './listContainer';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';

const history = createMemoryHistory();
const mockStore = configureStore([ thunk ]);
const applications = [
  {
    id: 1,
    name: 'SOL',
    description: null,
    applicationId: 'sol',
    status: false,
    rule_version: 2,
    ruleTypeIds: [ 3 ],
  },
  {
    id: 2,
    name: 'eExperience Storefront',
    description: null,
    applicationId: 'storefront',
    status: true,
    rule_version: 2,
    platformIds: [ 1 ],
    platforms: [ 'Web' ],
    ruleTypeIds: [ 3 ],
  },
  {
    id: 5,
    name: 'Nova Mobile',
    description: 'This is a fun description',
    applicationId: 'nova',
    status: true,
    rule_version: 1,
    ruleTypeIds: [ 1, 2 ],
  },
];

const ruleTypes = [
  { id: 1, rule_type: 'alert', slug: 'alert' },
  { id: 2, rule_type: 'campaign', slug: 'campaign' },
  { id: 3, rule_type: 'vignette', slug: 'vignette' },
];

const platforms = [
  { 'id': 1, 'slug': 'web', 'name': 'Web' },
];

const teams = [
  { id: 1, name: 'Pigeon Team', active: true },
  { id: 2, name: 'Nova Mobile', active: true },
];

const storeLoaded = mockStore({
  applications: {
    items: applications,
    isLoading: false,
  },
  platforms: {
    items: platforms,
    isLoading: false,
  },
  ruleTypes: {
    items: ruleTypes,
    isLoading: false,
  },
  authenticated: {
    permissionLevels: { canViewAllApplications: true, canViewOwnTeamApplications: true },
  },
  teams: {
    items: teams,
    isLoading: false,
  },
});

const storeLoading = mockStore({
  applications: {
    items: [],
    isLoading: false,
  },
  platforms: {
    items: [],
    isLoading: false,
  },
  ruleTypes: {
    items: [],
    isLoading: false,
  },
  teams: {
    items: [],
    isLoading: false,
  },
  authenticated: {
    permissions: {},
  },
});

const storeLoadingWithPermission = mockStore({
  applications: {
    items: [],
    isLoading: false,
  },
  platforms: {
    items: [],
    isLoading: false,
  },
  ruleTypes: {
    items: [],
    isLoading: false,
  },
  authenticated: {
    permissionLevels: { canViewAllApplications: true, canViewOwnTeamApplications: true },
  },
  teams: {
    items: [],
    isLoading: false,
  },
});

describe('ApplicationListContainer', () => {
  afterEach(() => {
    mockAxios.reset();
  });

  test('Applications displays correctly', () => {
    const { queryByText, rerender, getAllByLabelText, queryAllByText } = render(
      <Router history={history}>
        <Provider store={storeLoading}>
          <ApplicationListConnected />
        </Provider>
      </Router>
    );

    expect(queryByText('Nova Mobile')).not.toBeInTheDocument();
    expect(queryByText('SOL')).not.toBeInTheDocument();

    rerender(
      <Router history={history}>
        <Provider store={storeLoaded}>
          <ApplicationListConnected />
        </Provider>
      </Router>
    );

    expect(queryAllByText('Nova Mobile')).toHaveLength(1);
    expect(queryAllByText('SOL')).toHaveLength(1);
    expect(queryAllByText('Web')).toHaveLength(1);

    // click the tooltip that is next to 'Nova Mobile'
    fireEvent.click(getAllByLabelText('Info, Nova Mobile')[0]);
    expect(queryAllByText('This is a fun description')).toHaveLength(1);
  });

  test('status toggles', async() => {
    mockAxios.post.mockImplementation(() => Promise.resolve({ data: {} }));
    mockAxios.get.mockImplementation(() => Promise.resolve({ data: {} }));

    const { getAllByTestId, getByRole, queryByRole } = render(
      <Router history={history}>
        <Provider store={storeLoaded}>
          <ApplicationListConnected />
        </Provider>
      </Router>
    );

    // test activate
    const statusToggle1 = getAllByTestId('status-toggle-1')[0];
    await act(async() => fireEvent.click(statusToggle1));
    await act(async() => fireEvent.click(getByRole('button', { name: 'Reactivate Application' })));
    expect(queryByRole('button', { name: 'Reactivate Application' })).not.toBeInTheDocument();
    const postUrl1 = '/applications/1/activate?activateChildren=false';
    expect(mockAxios.post).toHaveBeenCalledWith(postUrl1, undefined, undefined);

    // test deactivate
    const statusToggle2 = getAllByTestId('status-toggle-2')[0];
    const postUrl2 = '/applications/2/deactivate';
    await act(async() => fireEvent.click(statusToggle2));
    await act(async() => fireEvent.click(getByRole('button', { name: 'Deactivate Application' })));
    expect(queryByRole('button', { name: 'Deactivate Application' })).not.toBeInTheDocument();
    expect(mockAxios.post).toHaveBeenCalledWith(postUrl2, undefined, undefined);
  });

  test.todo('scenarios where user lack necessary permission to view, edit or flip status toggles');

  test('Filters - initial load', async() => {
    const { getByText } = render(
      <Router history={history}>
        <Provider store={storeLoadingWithPermission}>
          <ApplicationListConnected />
        </Provider>
      </Router>
    );
    const filterToggle = getByText('Filter');
    await act(async() => fireEvent.click(filterToggle));
    const getTeamsUrl = '/teams?flag=skipAll';
    const getPlatformsUrl = '/platforms';
    const getApplicationsUrl = '/applications?limit=30&pageNumber=1';
    expect(mockAxios.get).toHaveBeenCalledWith(getApplicationsUrl, undefined);
    expect(mockAxios.get).toHaveBeenCalledWith(getTeamsUrl, undefined);
    expect(mockAxios.get).toHaveBeenCalledWith(getPlatformsUrl, undefined);
  });

  test('Filters - Application Owner selected', async() => {
    const { getByText } = render(
      <Router history={history}>
        <Provider store={storeLoaded}>
          <ApplicationListConnected />
        </Provider>
      </Router>
    );
    const filterToggle = getByText('Filter');
    await act(async() => fireEvent.click(filterToggle));

    const filterSection = document.getElementsByClassName('admin-filter')[0];
    // select values
    const teamIdSelected = teams.filter(a => a.name === 'Nova Mobile')[0].id;
    // console.log(applicationSelected);
    await act(async() => fireEvent.change(within(filterSection).getByLabelText('Application Owner'), { target: { value: teamIdSelected } }));
    // fireEvent.click(within(filterSection).getByText('Page'));
    const getApplicationsWithFilter = `/applications?limit=30&pageNumber=1&team_id=${teamIdSelected}`;
    expect(mockAxios.get).toHaveBeenCalledWith(getApplicationsWithFilter, undefined);
  });
});
