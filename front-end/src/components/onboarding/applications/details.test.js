import React from 'react';
import mockAxios from 'axios';
import { fireEvent, within } from '@testing-library/react';
import { act } from 'react-dom/test-utils';

import ApplicationDetails from './details';
import { renderPage, defaultData } from '../../../utils/testing-library-utils';
import { addSnackbar } from '../../../store/actions/snackbar';

const { applications, teams } = defaultData;
const application = applications[0];

describe('invalid/loading states', () => {
    test('Should redirect to /applications if invalid id', async() => {
        const { queryByText } = await renderPage(
            <ApplicationDetails
                match={{
                    params: {
                        id: 'non-number-id',
                    },
                    isExact: true,
                }}
            />
        );
        expect(queryByText('Create Application')).not.toBeInTheDocument();
        expect(queryByText('Edit Application')).not.toBeInTheDocument();
    });
});

describe('Application details', () => {
    mockAxios.get.mockImplementation((url) => {
        if (url.includes(`/applications/${application.id}`)) {
            return Promise.resolve({ data: application });
        } else if (url.includes(`/applications/network-error`)) {
            throw new Error('Network Error');
        }
    });

    test('update application', async() => {
        const {
            getByRole, getByLabelText, history, store,
        } = await renderPage(<ApplicationDetails match={{ params: { id: application.id } }} />);

        // warning for submit without change
        await act(async() => fireEvent.click(getByRole('button', { name: 'Update Application' })));
        const expectedSnackbar = addSnackbar({ message: 'No change to be saved' });
        expect(store.getActions()).toContainEqual(expectedSnackbar);
        // make changes
        await act(async() => { fireEvent.change(getByLabelText('Application Owner'), { target: { value: teams[1].id } }); });
        // submit changes
        expect(history.location.pathname).toStrictEqual('/');
        await act(async() => fireEvent.click(getByRole('button', { name: 'Update Application' })));
        expect(history.location.pathname).toBe('/applications');
    });

    test('cancel journey', async() => {
        const { getByText, getByRole, getByLabelText, history } = await renderPage(<ApplicationDetails />);

        // cancel directly
        fireEvent.click(getByRole('button', { name: 'Cancel' }));
        expect(history.location.pathname).toStrictEqual('/applications');
        history.goBack();
        expect(history.location.pathname).toStrictEqual('/');

        // make changes
        await act(async() => { fireEvent.change(getByLabelText('Application Owner'), { target: { value: teams[1].id } }); });
        fireEvent.click(getByRole('button', { name: 'Cancel' }));

        expect(getByText('Are you sure?')).toBeInTheDocument();
        expect(getByText('If you cancel, any unsaved changes will be lost.')).toBeInTheDocument();
        fireEvent.click(getByRole('button', { name: 'Yes' }));
        expect(history.location.pathname).toStrictEqual('/applications');
    });

    test('deactivate journey', async() => {
        mockAxios.patch.mockImplementationOnce(() => Promise.resolve({}));
        const {
            getByRole, getAllByRole, getByLabelText, getByText, queryByText, history,
        } = await renderPage(
            <ApplicationDetails match={{
                params: {
                    id: application.id,
                },
                isExact: true,
            }} />,
        );

        // warning for submit without change
        fireEvent.click(getByLabelText('Active'));
        expect(getByLabelText('Inactive')).toBeInTheDocument();
        await act(async() => fireEvent.click(getByRole('button', { name: 'Update Application' })));
        expect(getByText('Are you sure you want to deactivate this application?')).toBeInTheDocument();
        expect(getByText('Users will no longer be able to create, review, or approve campaigns or alerts for this application. All pages and containers that belong to this application will also be deactivated.')).toBeInTheDocument();
        // test cancel button on warning modal
        const modalCancelBtn = within(getAllByRole('dialog')[1]).getByRole('button', { name: 'Cancel' });
        fireEvent.click(modalCancelBtn);
        expect(queryByText('Are you sure you want to deactivate this application?')).not.toBeInTheDocument();
        await act(async() => fireEvent.click(getByRole('button', { name: 'Update Application' })));
        expect(getByText('Are you sure you want to deactivate this application?')).toBeInTheDocument();
        // deactivate user
        await act(async() => fireEvent.click(getByRole('button', { name: 'Deactivate Application' })));
        const deactivatedApplication = { ...application, status: false };
        delete deactivatedApplication.id;
        delete deactivatedApplication.platforms;
        delete deactivatedApplication.ruleSubTypes;
        delete deactivatedApplication.ruleTypes;
        delete deactivatedApplication.rule_version;
        delete deactivatedApplication.team;
        expect(mockAxios.patch).toHaveBeenCalledWith(`/applications/${application.id}`, deactivatedApplication, undefined);
        expect(history.location.pathname).toBe('/applications');
    });

    test('reactivate application', async() => {
        const applicationDisabled = { ...application, status: false };
        mockAxios.get.mockImplementation((url) => {
            if (url.includes(`/applications/${applicationDisabled.id}`)) {
                return Promise.resolve({ data: applicationDisabled });
            }
        });
        const { getByRole, getByLabelText } = await renderPage(
            <ApplicationDetails match={{
                params: {
                    id: applicationDisabled.id,
                },
                isExact: true,
            }} />,
        );
        fireEvent.click(getByLabelText('Inactive'));
        expect(getByLabelText('Active')).toBeInTheDocument();
        // bring up modal
        await act(async() => fireEvent.click(getByRole('button', { name: 'Update Application' })));

        // submit
        await act(async() => fireEvent.click(getByRole('button', { name: 'Reactivate Application' })));
        const patchData = { ...applicationDisabled, status: true };
        delete patchData.id;
        delete patchData.rule_version;
        delete patchData.team;
        delete patchData.platforms;
        delete patchData.ruleSubTypes;
        delete patchData.ruleTypes;
        expect(mockAxios.patch).toHaveBeenCalledWith(`/applications/${applicationDisabled.id}`, patchData, undefined);
    });

    test('should allow dismissal of cancel modal by clicking no button', async() => {
        const { getByRole, getByText, queryByText, getByLabelText } = await renderPage(<ApplicationDetails />);

        // modify form data to trigger warning modal on cancel
        const nameField = getByLabelText('Application Name'); // await findBy... doesn't seem to wait until element appears as documented
        expect(nameField).toBeInTheDocument();
        await act(async() => { fireEvent.change(nameField, { target: { value: 'New Application' } }); });
        fireEvent.click(getByRole('button', { name: 'Cancel' }));

        expect(getByText('If you cancel, any unsaved changes will be lost.')).toBeInTheDocument();
        fireEvent.click(getByRole('button', { name: 'No' }));
        expect(queryByText('If you cancel, any unsaved changes will be lost.')).not.toBeInTheDocument();
    });

    test('should allow dismissal of cancel modal by clicking modal backdrop', async() => {
        const { getByRole, queryByText, getByLabelText } = await renderPage(<ApplicationDetails />);

        // modify form data to trigger warning modal on cancel
        await act(async() => { fireEvent.change(getByLabelText('Application Name'), { target: { value: 'New Application' } }); });
        fireEvent.click(getByRole('button', { name: 'Cancel' }));

        expect(queryByText('If you cancel, any unsaved changes will be lost.')).toBeInTheDocument();
        fireEvent.click(getByRole('button', { name: 'No' }).closest('.Modal').querySelector('.InternalOverlay'));
        expect(queryByText('If you cancel, any unsaved changes will be lost.')).not.toBeInTheDocument();
    });

    test('should render spinner if loading', async() => {
        const { queryByText } = await renderPage(
            <ApplicationDetails />,
            { initialState: { teams: { isLoading: true }, platforms: { isLoading: true }, ruleTypes: { isLoading: true } } },
        );
        expect(queryByText('Create Application')).not.toBeInTheDocument();
    });
});
