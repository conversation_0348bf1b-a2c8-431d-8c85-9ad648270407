import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Redirect, useHistory } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import PropTypes from 'prop-types';

import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import CanvasCard from 'canvas-core-react/lib/Card';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import Margin from 'canvas-core-react/lib/Margin';
import TextField from 'canvas-core-react/lib/TextField';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import Checkbox from 'canvas-core-react/lib/Checkbox';

import { saveApplicationValues, setApplicationActivation } from '../../../store/actions/applications';
import { addSnackbar } from '../../../store/actions/snackbar';
import { InputToggleField, InputTextField, InputSelectField, InputCheckboxGroupField } from '../../formFields/reactHookForm';
import { useApplication } from '../../../hooks/useApplication';
import {
    min,
    max,
    requiredSpecific,
    campaignName,
    requiredPlatform,
    requiredRuleType,
    requiredCampaignType,
    ensureUnique,
} from '../../../utils/validation';
import { capitalize } from '../../../utils';
import { CONTENTFUL_SPACES } from '../../../constants';

const validations = {
    requiredApplicationOwner: requiredSpecific('Application owner'),
    requiredName: requiredSpecific('Application name'),
    requiredDescription: requiredSpecific('Application description'),
    requiredContentfulSpace: requiredSpecific('Contentful space'),
};

export const ApplicationDetails = ({ match }) => {
    const history = useHistory();
    const dispatch = useDispatch();
    const appId = match?.params?.id;
    const isExact = match?.params?.isExact;
    // Re-route to main application page if not an exact match (i.e. /applications/create) or invalid id format
    if (isExact || (appId && isNaN(appId))) {
        return <Redirect to="/applications" />;
    }
    const { handleSubmit, control, getValues, reset, setValue, formState: { isDirty, dirtyFields }, watch } = useForm({
        mode: 'onChange',
        defaultValues: {
            status: true,
        },
    });
    const {
        isLoading,
        isFormDisabled,
        teams,
        applicationIdValue,
        platforms,
        ruleTypes,
        validateRuleTypeSelection,
        isCampaignRuleTypeSelected,
        ruleSubTypes,
        validateRuleSubTypeSelection,
    } = useApplication({ id: appId, reset, watch, getValues, setValue });
    const [ cancelPopupOpened, setCancelPopupOpened ] = useState(false);
    const [ isReactivationModalOpen, setIsReactivationModalOpen ] = useState(false);
    const [ reactivateChildren, setReactivateChildren ] = useState(false);
    const [ deactivationModalOpen, setDeactivationModalOpen ] = useState(false);
    const onSubmit = () => {
        if (!isDirty) {
            return dispatch(addSnackbar({ message: 'No change to be saved' }));
        }
        const activating = appId && getValues('status') && dirtyFields.status;
        const deactivating = appId && !getValues('status') && dirtyFields.status;
        if (activating) return setIsReactivationModalOpen(true);
        if (deactivating) return setDeactivationModalOpen(true);
        submitApplicationsForm(getValues(), false);
    };

    const submitApplicationsForm = async(values, reactivateChildren) => {
        const updatedValues = {
            ...values,
            platformIds: values.platformids,
            ruleTypeIds: values.ruletypeids,
            ruleSubTypeIds: values.rulesubtypeids,
        };
        const errorReturned = await saveApplicationValues(appId, updatedValues)(dispatch);
        if (!errorReturned) {
            if (reactivateChildren) {
                await setApplicationActivation(appId, true, true)(dispatch);
            }
            setIsReactivationModalOpen(false);
            history.push('/applications');
        }
    };
    if (isLoading) {
        return <IconSpinner size={32} />;
    }

    const modals = () => {
        return (
            <>
                { /* Modals */ }
                <ModalDialogue
                    isModalVisible={cancelPopupOpened}
                    headline="Are you sure?"
                    primaryButtonLabel="Yes"
                    primaryAction={() => history.push('/applications')}
                    secondaryButtonLabel="No"
                    secondaryAction={() => setCancelPopupOpened(false)}
                    setModalVisible={() => setCancelPopupOpened(false)}
                >
                    If you cancel, any unsaved changes will be lost.
                </ModalDialogue>

                <ModalDialogue
                    isModalVisible={isReactivationModalOpen}
                    headline="Reactivate Application"
                    primaryButtonLabel="Reactivate Application"
                    primaryAction={() => submitApplicationsForm({
                        ...getValues(),
                        platformIds: getValues('platformids'),
                        ruleTypeIds: getValues('ruletypeids'),
                        ruleSubTypeIds: getValues('rulesubtypeids'),
                    }, reactivateChildren)}
                    secondaryButtonLabel="Cancel"
                    secondaryAction={() => setIsReactivationModalOpen(false)}
                    setModalVisible={() => setIsReactivationModalOpen(false)}
                >
                    <p>Select checkbox if you would also like to reactivate all of this application&apos;s pages and containers. If this checkbox is not selected, only the application will be reactivated.</p>
                    <Checkbox
                        id="applications-list__modal-checkbox"
                        checked={reactivateChildren}
                        onChange={(e) => setReactivateChildren(e.target.checked)}
                        label="Reactivate application pages and containers"
                    />
                </ModalDialogue>

                <ModalDialogue
                    isModalVisible={deactivationModalOpen}
                    headline="Are you sure you want to deactivate this application?"
                    primaryButtonLabel="Deactivate Application"
                    primaryAction={() => submitApplicationsForm({
                        ...getValues(),
                        platformIds: getValues('platformids'),
                        ruleTypeIds: getValues('ruletypeids'),
                        ruleSubTypeIds: getValues('rulesubtypeids'),
                    }, false)}
                    secondaryButtonLabel="Cancel"
                    secondaryAction={() => setDeactivationModalOpen(false)}
                    setModalVisible={() => setDeactivationModalOpen(false)}
                >
                    <p>Users will no longer be able to create, review, or approve campaigns or alerts for this application. All pages and containers that belong to this application will also be deactivated.</p>
                </ModalDialogue>
            </>
        );
    };
    return (
        <div className="admin-details">
            <form onSubmit={handleSubmit(onSubmit)}>
                <TextHeadline
                    className="admin-details__header"
                    component="h1"
                >
                    { appId ? 'Edit Application' : 'Create Application' }
                </TextHeadline>
                <CanvasCard className="admin-details__card">
                    <TextHeadline className="admin-details__sub-header" component="h2" size={32}>
                        Application Details
                    </TextHeadline>
                    <InputToggleField
                        control={control}
                        className="admin-details__field"
                        disabled={isFormDisabled}
                        name="status"
                        label="status"
                        secondaryLabel={`${watch('status') ? 'Active' : 'Inactive'}`}
                    />
                    <InputSelectField
                        control={control}
                        className="admin-details__field"
                        disabled={isFormDisabled}
                        name="team_id"
                        label="Application Owner"
                        placeholder="Select a team"
                        options={Object.values(teams || {}).map(team => ({ id: team.id, name: team.name }))}
                        rules={{
                            validate: {
                                requiredSpecific: validations.requiredApplicationOwner,
                            },
                        }}
                    />
                    <InputTextField
                        control={control}
                        disabled={isFormDisabled}
                        label='Application Name'
                        name="name"
                        placeholder='Enter application name'
                        className="input-text-field admin-details__field"
                        rules={{
                            validate: {
                                required: validations.requiredName,
                                min: min(3),
                                max: max(40),
                                campaignName,
                                ensureUnique: ensureUnique('applicationName', 'name', dirtyFields),
                            },
                        }}
                    />
                    <div style={{ display: 'none' }}>
                        <InputTextField
                            control={control}
                            className="input-text-field admin-details__field"
                            disabled
                            name="applicationId"
                            label="Application ID"
                            placeholder="Enter application ID"
                            component={InputTextField}
                        />
                    </div>
                    <TextField
                        id={`applicationId`}
                        className="admin-details__field"
                        disabled
                        name="applicationId"
                        label="Application ID"
                        placeholder="Enter application ID"
                        component={InputTextField}
                        value={applicationIdValue}
                        readOnly={!!appId}
                    />
                    <InputTextField
                        control={control}
                        className="input-text-field admin-details__field"
                        disabled={isFormDisabled}
                        name="description"
                        label="Application Description"
                        placeholder="Enter application description"
                        rules={{
                            validate: {
                                required: validations.requiredDescription,
                            },
                        }}
                    />
                </CanvasCard>
                <CanvasCard>
                    <Margin side="bottom" xs={48}>
                        <TextHeadline component="h2" size={32}>Configuration</TextHeadline>
                    </Margin>
                    <InputSelectField
                        control={control}
                        className="admin-details__field"
                        disabled={isFormDisabled}
                        label="Contentful Space"
                        name="contentful_space"
                        options={Object.values(CONTENTFUL_SPACES)}
                        rules={{
                            validate: {
                                required: validations.requiredContentfulSpace,
                            },
                        }}
                    />
                    <InputCheckboxGroupField
                        control={control}
                        className="admin-details__field"
                        disabled={isFormDisabled}
                        label="Platforms"
                        name="platformIds"
                        options={Object.values(platforms || {}).map(({ id, name }) => ({ // eslint-disable-line
                            id,
                            name,
                        })).reverse()}
                        rules={{
                            validate: {
                                requiredPlatform,
                            },
                        }}
                    />
                    <InputCheckboxGroupField
                        control={control}
                        className="admin-details__field"
                        disabled={isFormDisabled}
                        label="Rule Types"
                        name="ruleTypeIds"
                        options={Object.values(ruleTypes || {})
                            .filter(({ slug }) => [ 'campaign', 'alert', 'ccau_campaign' ].includes(slug))
                            .map(({ id, rule_type: ruleType }) => {
                                if (ruleType === 'ccau_campaign') {
                                    return { id, name: 'CCAU Campaign' };
                                } else {
                                    return { id, name: capitalize(ruleType) };
                                }
                            })
                            .sort((a, b) => a.name.localeCompare(b.name))}
                        rules={{
                            validate: {
                                requiredRuleType,
                                validateRuleTypeSelection,
                            },
                        }}
                    />
                    {
                        isCampaignRuleTypeSelected && (
                            <InputCheckboxGroupField
                                control={control}
                                className="admin-details__field"
                                disabled={isFormDisabled}
                                component={InputCheckboxGroupField}
                                label="Campaign Types"
                                name="ruleSubTypeIds"
                                options={ruleSubTypes.map(({ id, description }) => ({
                                    id,
                                    name: description,
                                }))}
                                rules={{
                                    validate: {
                                        requiredCampaignType,
                                        validateRuleSubTypeSelection,
                                    },
                                }}
                            />
                        )
                    }
                </CanvasCard>
                <div className="admin-details__action-right-bar">
                    <div className="details__action-buttons">
                        <SecondaryButton
                            type="button"
                            onClick={() => isDirty ? setCancelPopupOpened(true) : history.push('/applications')}
                        >
                            Cancel
                        </SecondaryButton>
                        <PrimaryButton
                            type="submit"
                            className="details__action-button"
                            disabled={isFormDisabled}
                        >
                            { appId ? 'Update Application' : 'Create Application' }
                        </PrimaryButton>
                    </div>
                </div>
            </form>
            { modals() }
        </div>
    );
};

ApplicationDetails.propTypes = {
    match: PropTypes.object,
};

export default ApplicationDetails;
