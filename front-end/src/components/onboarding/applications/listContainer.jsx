import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { omit, difference } from 'lodash';
import qs from 'qs';

import AlertBanner from 'canvas-core-react/lib/AlertBanner';
import Checkbox from 'canvas-core-react/lib/Checkbox';
import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';

import List from '../../listing/list';
import PageActions from '../../pageActions';
import AdminFilter from '../../listing/AdminFilter';
import { debounce, removeFalsyKeys } from '../../../utils';
import { TEAMS_FLAGS } from '../../../constants/permissionsList';

import { getApplications, setApplicationActivation } from '../../../store/actions/applications';
import { getPlatforms } from '../../../store/actions/platforms';
import { getRuleTypes } from '../../../store/actions/rule-types';
import { getTeams } from '../../../store/actions/teams';
import { useApplications } from '../../../hooks/useApplications';
import { tableColumns } from './applicationsColumns';

const PAGE_SIZE = 30;
const BASE_QUERY = {
  team_id: '',
  platform_id: '',
  rule_type_id: '',
  status: '',
  search: '',
  sort: '',
  limit: PAGE_SIZE,
  pageNumber: 1,
};

const ApplicationList = () => {
  const history = useHistory();
  const dispatch = useDispatch();

  const queryParams = qs.parse(history.location.search, { ignoreQueryPrefix: true });
  const initialFilters = {
    ...BASE_QUERY,
    ...omit(queryParams, difference(Object.keys(queryParams), Object.keys(BASE_QUERY))),
  };
  const [ filters, setFilters ] = useState(initialFilters);
  const { loading, teamsLoading, applications, teams, platforms, ruleTypes, pagination, currentUser } = useApplications(removeFalsyKeys(initialFilters));

  const { canEditAllApplications, canEditOwnTeamApplications, canViewOwnTeamApplications, canViewAllApplications } =
    useMemo(() => currentUser.permissionLevels || {}, [ currentUser ]);

  const [ isFilterOpen, setIsFilterOpen ] = useState(false);
  const [ applicationToDeactivate, setApplicationToDeactivate ] = useState(null);
  const [ applicationToReactivate, setApplicationToReactivate ] = useState(null);
  const [ reactivateChildren, setReactivateChildren ] = useState(false);

  useEffect(() => {
    const queryParams = removeFalsyKeys(omit(filters, [ 'limit', 'flag', ...(filters.pageNumber === 1 ? [ 'pageNumber' ] : []) ]));
    history.push({ search: qs.stringify(queryParams, { addQueryPrefix: true }) });
  }, [ filters ]);

  useEffect(() => {
    if (isFilterOpen) {
      dispatch(getTeams({ flag: TEAMS_FLAGS.SKIP_ALL }));
      if (!platforms || !platforms.length) {
        dispatch(getPlatforms());
      }
      if (!ruleTypes || !ruleTypes.length) {
        dispatch(getRuleTypes());
      }
    }
  }, [ isFilterOpen ]);

  const fetchApplications = (newFilters) => {
    dispatch(getApplications(removeFalsyKeys(newFilters)));
  };

  const debounceFetchApplications = useCallback(debounce(fetchApplications, 500), []);

  const handleOnChangeSearch = (e) => {
    const search = e.target.value;
    debounceFetchApplications({ ...filters, search, pageNumber: 1 });
    setFilters((f) => ({ ...f, search, pageNumber: 1 }));
  };

  const filtersChanged = (newFilters) => {
    setFilters(newFilters);
    dispatch(getApplications(removeFalsyKeys(newFilters)));
  };

  const handleClearFilters = () => {
    const newQuery = {
      ...BASE_QUERY,
      ...(!canViewAllApplications && {
        team_id: currentUser.team_id,
      }),
    };
    setFilters(newQuery);
    dispatch(getApplications(removeFalsyKeys(newQuery)));
  };

  const initialSortDirection = columnKey => {
    const { sort } = filters;
    if (!filters.sort) {
      return 0;
    }
    // check if the sorted key has a - in front of it which would indicate it's in descending order, ascending order otherwise
    if (sort.includes(columnKey)) {
      return (sort.charAt(0) === '-') ? 1 : -1;
    }
    return 0;
  };

  const onColumnSort = (columnKey, currentFilters) =>
    (row, direction) => {
      filtersChanged({ ...currentFilters, pageNumber: 1, sort: `${direction > 0 ? '-' : ''}${columnKey}` });
  };

  const sortableColumnProperties = useCallback((columnKey, currentFilters) => ({
    sortable: true,
    overrideSortBehaviour: onColumnSort(columnKey, currentFilters),
    initialSortDirection: initialSortDirection(columnKey),
  }), []);

  const handleChangeApplicationStatus = (e, applicationId) => {
    const { checked } = e.currentTarget;
    if (checked) {
      setApplicationToReactivate(applicationId);
    } else {
      setApplicationToDeactivate(applicationId);
    }
  };

  if (!canViewOwnTeamApplications) {
    return (<AlertBanner type="error">You do not have permissions to view this page</AlertBanner>);
  };

  return (
    <>
      <List
        entityName="application"
        className="application-list"
        columns={tableColumns({
          canEditAllApplications,
          canEditOwnTeamApplications,
          handleChangeApplicationStatus,
          currentUser,
          filters,
          sortableColumnProperties,
        })}
        isLoading={loading}
        data={applications}
        permissions={currentUser?.permissions}
        columnFixed={false}
        resetSortOnDataChange={false}
      >
        <PageActions
          onChange={handleOnChangeSearch}
          value={filters.search}
          filterButtonText="Filter"
          setParentFilteringOptionsCollapsed={setIsFilterOpen}
        >
          <AdminFilter
            onChange={filtersChanged}
            onClearClick={handleClearFilters}
            filterValues={filters}
            fields={[
              {
                label: 'Application Owner',
                key: 'team_id',
                options: teams.map(team => ({
                  label: team.name,
                  value: team.id,
                })).sort((a, b) => a.label.localeCompare(b.label)),
                isLoading: teamsLoading,
              },
              {
                label: 'Platform',
                key: 'platform_id',
                options: platforms.map(platform => ({
                  label: platform.name,
                  value: platform.id,
                })).sort((a, b) => a.label.localeCompare(b.label)),
              },
              {
                label: 'Rule Type',
                key: 'rule_type_id',
                options: ruleTypes.map(ruleType => ({
                  label: ruleType.rule_type,
                  value: ruleType.id,
                })).sort((a, b) => a.label.localeCompare(b.label)),
              },
              {
                label: 'Status',
                key: 'status',
                options: [
                  { value: true, label: 'Active' },
                  { value: false, label: 'Inactive' },
                ],
              },
            ]}
          />
        </PageActions>
      </List>

      { !loading && applications?.length > 0 &&
        <DesktopPagination
          id="teams-pagination"
          totalResultCount={pagination?.total || 0}
          onChange={(pageNumber) => {
            filtersChanged({ ...filters, pageNumber });
            window.scrollTo(0, 0);
          }}
          firstButtonLabel="First"
          prevButtonLabel="Previous"
          nextButtonLabel="Next"
          lastButtonLabel="Last"
          visiblePages={5}
          navigationLabel="Pagination Navigation"
          pageSize={pagination?.limit || 0}
          currentPage={applications ? (pagination?.offset / pagination?.limit) + 1 : 1}
          containerType="card"
        />
      }

      { /* Modals */ }
      <ModalDialogue
        isModalVisible={applicationToDeactivate !== null}
        headline="Are you sure you want to deactivate this application?"
        primaryButtonLabel="Deactivate Application"
        primaryAction={() => {
          dispatch(setApplicationActivation(applicationToDeactivate, false, false, filters));
          setApplicationToDeactivate(null);
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setApplicationToDeactivate(null)}
        setModalVisible={() => setApplicationToDeactivate(null)}
      >
        Users will no longer be able to create, review, or approve campaigns or alerts for this application. All pages and containers that belong to this application will also be deactivated.
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={applicationToReactivate !== null}
        headline="Reactivate Application"
        primaryButtonLabel="Reactivate Application"
        primaryAction={() => {
          dispatch(setApplicationActivation(applicationToReactivate, true, reactivateChildren, filters));
          setApplicationToReactivate(null);
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setApplicationToReactivate(null)}
        setModalVisible={() => setApplicationToReactivate(null)}
      >
        Select checkbox if you would also like to reactivate all of this application&apos;s pages and containers. If this checkbox is not selected, only the application will be reactivated.
        <Checkbox
          id="applications-list__modal-checkbox"
          checked={reactivateChildren}
          onChange={(e) => setReactivateChildren(e.target.checked)}
          label="Reactivate application's pages and containers"
        />
      </ModalDialogue>
    </>
  );
};

export default ApplicationList;
