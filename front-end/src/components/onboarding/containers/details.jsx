import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { isEmpty } from 'lodash';

import Card from 'canvas-core-react/lib/Card';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';

import { InputToggleField, InputTextField, InputSelectField, InputRadioButtonField, InputGroupField } from '../../formFields';
import { capitalize, normalizedStrCompare } from '../../../utils';
import Autosuggest from '../../autosuggest/autosuggest';
import { max, requiredSpecific, campaignName, id as validId, required, maxValue } from '../../../utils/validation';
import { mapPropToKey, mapObjectToArray, addThousandSeparator, removeThousandSeparator } from '../../../constants';

const reactivateTooltip = {
  heading: 'Cannot reactivate',
  body: 'The application that this container belongs to must be reactivated before this container can be reactivated.',
};

const limitTooltip = {
  heading: 'Container campaign limit',
  body: 'The maximum number of campaigns to display in this container.',
};

const dataLegend = { keyName: 'id', valueName: 'name' };

const validations = {
  requiredApplication: requiredSpecific('Application'),
  requiredName: requiredSpecific('Container name'),
  requiredID: requiredSpecific('Container ID'),
  requiredDescription: requiredSpecific('Container description'),
  max64: max(64),
  max200: max(200),
  maxValueOneHundred: maxValue(100),
};

const Container = ({
  isLoading,
  id,
  title,
  applications,
  containers,
  pages,
  ruleTypes,
  contentTypes,
  // callback functions in detailsContainer
  createContainerId,
  onCancel,
  onSubmit,
  // redux form properties
  isDirty,
  untouch,
  changeField,
  formValues,
  authenticated,
  valid,
}) => {
  const canEditAll = useMemo(() => !!authenticated.permissions['admin'] || !!authenticated.permissions['containers_manage_super'], [ authenticated ]);
  const canEdit = useMemo(() => canEditAll || !!authenticated.permissions['containers_manage'], [ authenticated ]);
  const ruleLookup = useMemo(() => mapPropToKey(ruleTypes, 'rule_type'), [ ruleTypes ]);

  const [ isFormDisabled, setIsFormDisabled ] = useState(true);
  const [ isCancelPopupOpen, setCancelPopupOpen ] = useState(false);
  const [ deactivationModalVisible, setDeactivationModalVisible ] = useState(false);
  const [ reactivationModalVisible, setReactivationModalVisible ] = useState(false);
  const [ previousStatus, setPreviousStatus ] = useState(false);
  const [ isInitialized, setIsInitialized ] = useState(false);
  const [ errors, setErrors ] = useState({
    pages: '',
    contentType: '',
  });

  const pageAutosuggestRef = useRef();
  const contentTypeAutosuggestRef = useRef();

  useEffect(() => {
    if (!isInitialized && !isEmpty(formValues) && id) {
      setPreviousStatus(formValues.status);
      setIsInitialized(true);
    }
    // Checking for pages and content-type errors
    const err = { ...errors };
    if (!isEmpty(formValues) && formValues.rule_type && formValues.rule_type !== 'alert') {
      if (formValues.pages && !formValues.pages.length) {
        err.pages = 'Pages are required';
      } else {
        err.pages = '';
      }
    } else {
      err.pages = '';
    }
    if (!isEmpty(formValues) && formValues.content_type && !formValues.content_type.length) {
      err.contentType = 'Content type is required';
    } else {
      err.contentType = '';
    }
    setErrors(err);
  }, [ formValues ]);

  const pageList = useMemo(() => {
    if (!formValues.application || !pages) {
      return [];
    }
    return mapObjectToArray(pages).filter(page => page.application === parseInt(formValues.application));
  }, [ formValues, pages ]);

  const selectedPages = useMemo(() => {
    if (!formValues.application || Object.keys(formValues.pages || {}).length === 0) {
      return [];
    }
    return pageList.filter(page => formValues.pages.includes(page.id));
  }, [ formValues, pageList ]);

  const contentTypeList = useMemo(() => {
    if (!formValues.application || !contentTypes) {
      return [];
    }
    return mapObjectToArray(contentTypes || {});
  }, [ formValues, contentTypes ]);

  const selectedContentTypes = useMemo(() => {
    if (!formValues.application || (formValues.content_type || []).length === 0) {
      return [];
    }
    let initialSelections;
    if (typeof formValues.content_type === 'string') {
      let regex = RegExp('.*');
      try {
        regex = RegExp(formValues.content_type);
      } catch (err) {
      }
      initialSelections = contentTypeList.filter(i => regex.test(i.id));
    } else if (contentTypeList.length > 0) {
      initialSelections = (formValues.content_type || []).map(id => contentTypes[id]).filter(x => x);
    }
    return initialSelections;
  }, [ formValues, contentTypeList ]);

  const isApplicationDisabled = useMemo(() => {
    const app = applications.find(({ id }) => id === Number(formValues.application));
    return app && !app.status;
  }, [ applications, formValues.application ]);

  const filteredRuleType = useMemo(() => ruleTypes
    .filter(({ slug }) => applications.find(a => a.id === parseInt(formValues.application))?.ruleTypes?.includes(slug)), [ ruleTypes, formValues, applications ]);

  useEffect(() => {
    if (formValues.id) {
      // enabled if user has containers_manage_super permission, or if
      // user has containers_manage permission and their team owns the container's parent application
      const isOwnTeamsApp = Object.values(applications)
        .find(({ applicationId }) => applicationId === formValues.applicationId)?.team_id === authenticated.team_id;
      setIsFormDisabled(!(canEditAll || (canEdit && isOwnTeamsApp)));
    } else {
      // enabled if user has any containers_manage permission - other fields will be filtered accordingly
      setIsFormDisabled(!canEdit);
    }
  }, [ formValues, authenticated.team_id, applications ]);

  const ensureUnique = useCallback((label, key) => value => {
    const itemIsNotUnique = Object.values(containers)
      .filter(container => {
        if (key === 'containerId') {
          return container.application && container.application.toString() === formValues.application;
        } else {
          return true;
        }
      })
      .some(container => container.id !== id && normalizedStrCompare(container[key], value));

    if (itemIsNotUnique) {
      return `${label} needs to be unique.`;
    }
  }, [ containers, id, formValues ]);
  const ensureUniqueContainerId = useCallback(ensureUnique('Container ID', 'containerId'), [ containers, id, formValues ]);
  const ensureUniqueContainerName = useCallback(ensureUnique('Container Name', 'name'), [ containers, id, formValues ]);

  const applicationSelectChange = (event, newValue, previousValue) => {
    if (newValue !== previousValue) {
      // if new parent application is disabled, the container must be disabled as well
      const newApplication = Object.values(applications || []).find(a => a.id === Number(newValue));
      if (!newApplication.status && formValues.status) {
        changeField('status', false);
      } else if (newApplication.status && !formValues.status) {
        changeField('status', true);
      }

      changeField('rule_type', null);
      changeField('pages', []);

      untouch('rule_type');
      // when the application select changes, we should reset the selections in the autosuggest field
      if (pageAutosuggestRef.current) {
        pageAutosuggestRef.current.resetSelections();
      }

      // if new application has a different contentful space reset content type selections
      const oldContentfulSpace = Object.values(applications).find(app => app.id === Number(previousValue))?.contentful_space;
      const newContentfulSpace = Object.values(applications).find(app => app.id === Number(newValue))?.contentful_space;
      if (oldContentfulSpace !== newContentfulSpace) {
        changeField('content_type', []);
        if (contentTypeAutosuggestRef.current) {
          contentTypeAutosuggestRef.current.resetSelections();
        }
      }
    }
  };

  const multiSelectChange = formKey => selections => {
    const itemToUpdateWith = selections.map(selection => selection.id);
    changeField(formKey, itemToUpdateWith);
  };

  const renderContentField = () => {
    const chosenRuleType = ruleLookup[formValues.rule_type]?.slug || '';
    if (chosenRuleType.startsWith('vignette')) {
      return null;
    }

    return (
      <Autosuggest
        className="admin-details__autosuggest"
        label="Content Type"
        ref={contentTypeAutosuggestRef}
        placeholder="Select content type"
        data={contentTypeList}
        dataLegend={dataLegend}
        editable={!isFormDisabled}
        onChange={multiSelectChange('content_type')}
        initialSelection={selectedContentTypes}
        error={errors && errors.contentType}
      />
    );
  };

  const renderPages = () => {
    if (!formValues.application) {
      return null;
    }

    const isRequired = formValues.rule_type && formValues.rule_type !== 'alert';

    return (
      <Autosuggest
        label="Pages"
        ref={pageAutosuggestRef}
        className="admin-details__autosuggest"
        data={pageList}
        placeholder="Select pages"
        dataLegend={dataLegend}
        editable={!isFormDisabled}
        onChange={multiSelectChange('pages')}
        initialSelection={selectedPages}
        error={errors && errors.pages}
        required={isRequired}
      />
    );
  };

  const renderTitleSection = () => (
    <div className="admin-details__action-bar">
      <TextHeadline
        className="admin-details__header"
        component="h1"
      >
        { title } { isLoading && <IconSpinner /> }
      </TextHeadline>
    </div>
  );

  const renderPopups = () => (
    <>
      <ModalDialogue
        headline="Are you sure?"
        primaryButtonLabel="Yes"
        primaryAction={onCancel}
        secondaryButtonLabel="No"
        secondaryAction={() => setCancelPopupOpen(false)}
        isModalVisible={isCancelPopupOpen}
        setModalVisible={() => setCancelPopupOpen(false)}
      >
        If you cancel, any unsaved changes will be lost.
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={deactivationModalVisible}
        headline="Are you sure you want to deactivate this container?"
        primaryButtonLabel="Deactivate Container"
        primaryAction={() => { onSubmit(); setDeactivationModalVisible(false); }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setDeactivationModalVisible(false)}
        setModalVisible={() => { }}
      >
        Users will no longer be able to create new campaigns or alerts for this container
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={reactivationModalVisible}
        headline="Are you sure you want to reactivate this container?"
        primaryButtonLabel="Reactivate Container"
        primaryAction={() => { onSubmit(); setReactivationModalVisible(false); }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setReactivationModalVisible(false)}
        setModalVisible={() => setReactivationModalVisible(false)}
      >
        User will be able to create new campaigns or alerts again for this container.
      </ModalDialogue>
    </>
  );

  const checkSelectedApplicationStatus = () => formValues.application && applications.find(a => a.id === parseInt(formValues.application)).status;

  const modalActivation = () => {
    if ((!formValues.status && previousStatus && checkSelectedApplicationStatus()) && valid && id) {
      setDeactivationModalVisible(true);
    } else if ((formValues.status && !previousStatus && checkSelectedApplicationStatus()) && valid && id) {
      setReactivationModalVisible(true);
    } else {
      onSubmit();
    }
  };

  const renderFormFields = () => {
    if (isLoading) {
      return null;
    }

    return (
      <form>
        <Card className="admin-details__card">
          <TextHeadline className="admin-details__sub-header" component="h2" size={21}>
            Container Details
          </TextHeadline>
          <Field
            className="admin-details__field"
            disabled={isFormDisabled || isApplicationDisabled}
            name="status"
            label=""
            secondaryLabel={`${formValues.status ? 'Active' : 'Inactive'}`}
            component={InputToggleField}
            options={[
              { id: true, name: 'Active' },
              { id: false, name: 'Inactive' },
            ]}
            tooltip={isApplicationDisabled && reactivateTooltip}
          />
          <Field
            className="admin-details__field"
            disabled={isFormDisabled}
            name="application"
            label="Application"
            placeholder="Select application"
            component={InputSelectField}
            options={Object.values(applications || [])
              .filter(({ team_id: teamId }) => (isFormDisabled || canEditAll) || (teamId === authenticated.team_id))
              .map(({ id, name, status }) => status ? ({ id, name }) : ({ id, name: `${name} (Inactive)` }))
            }
            onChange={applicationSelectChange}
            validate={validations.requiredApplication}
          />
          <Field
            className="admin-details__field"
            disabled={isFormDisabled}
            name="name"
            label="Container Name"
            placeholder="Enter container name"
            component={InputTextField}
            validate={[
              validations.requiredName,
              validations.max64,
              campaignName,
              ensureUniqueContainerName,
            ]}
            autoComplete="off"
            onChange={createContainerId}
          />
          <Field
            className="admin-details__field"
            disabled
            name="containerId"
            label="Container ID"
            placeholder="Enter container ID"
            readOnly={!!id}
            component={InputTextField}
            validate={[
              validations.requiredID,
              validations.max64,
              validId,
              ensureUniqueContainerId,
            ]}
            autoComplete="off"
          />
          <Field
            className="admin-details__field"
            disabled={isFormDisabled}
            name="description"
            label="Container Description"
            placeholder="Enter container description"
            component={InputTextField}
            validate={[
              validations.requiredDescription,
              validations.max200,
            ]}
            autoComplete="off"
          />
        </Card>
        { formValues.application &&
          <Card>
            <TextHeadline className="admin-details__sub-header" component="h2" size={21}>
              Configuration
            </TextHeadline>
            <TextSubtitle component="label" type="2">Rule Type</TextSubtitle>
            <Field
              className="admin-details__field"
              name="rule_type"
              component={InputGroupField}
              validate={required}
            >
              <div className="container-list__radio-container">
                { filteredRuleType
                  .map(({ slug: id, rule_type: name }) => (
                    <Field
                      className="container-list__radio-btn"
                      disabled={isFormDisabled}
                      id={name}
                      key={id}
                      checked={!formValues.rule_type === null}
                      name="rule_type"
                      label={name === 'alert' ? 'Alerts' : (name === 'ccau_campaign' ? 'CCAU Campaign' : capitalize(name))}
                      component={InputRadioButtonField}
                      validate={required}
                    />
                  ))
                }
              </div>
            </Field>
            {
              formValues.rule_type === 'campaign' &&
              <Field
                className="admin-details__field"
                disabled={isFormDisabled}
                name="campaign_limit"
                label="Limit"
                placeholder="Enter container campaign limit"
                component={InputTextField}
                format={addThousandSeparator}
                normalize={removeThousandSeparator}
                validate={[ validations.maxValueOneHundred ]}
                tooltip={limitTooltip}
                autoComplete="off"
              />
            }
            { formValues.rule_type &&
              <>
                { renderContentField() }
                { renderPages() }
              </>
            }
          </Card>
        }

        <div className="admin-details__action-right-bar">
          <div className="details__action-buttons">
            <SecondaryButton
              type="button"
              onClick={() => isDirty ? setCancelPopupOpen(true) : onCancel()}
            >
              Cancel
            </SecondaryButton>
            <PrimaryButton
              type="button"
              className="details__action-button"
              disabled={isFormDisabled}
              onClick={() => modalActivation()}
            >
              { id ? 'Update Container' : 'Create Container' }
            </PrimaryButton>
          </div>
        </div>
      </form>
    );
  };

  return (
    <div className="details">
      { renderTitleSection() }
      { renderFormFields() }
      { renderPopups() }
    </div>
  );
};

Container.propTypes = {
  id: PropTypes.number,
  isLoading: PropTypes.bool,
  title: PropTypes.string.isRequired,
  onSubmit: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  isDirty: PropTypes.bool.isRequired,
  createContainerId: PropTypes.func,
  applications: PropTypes.array.isRequired,
  containers: PropTypes.object.isRequired,
  pages: PropTypes.object.isRequired,
  formValues: PropTypes.object.isRequired,
  untouch: PropTypes.func.isRequired,
  ruleTypes: PropTypes.array.isRequired,
  changeField: PropTypes.func.isRequired,
  contentTypes: PropTypes.object,
  authenticated: PropTypes.object,
  valid: PropTypes.bool,
};

export default Container;
