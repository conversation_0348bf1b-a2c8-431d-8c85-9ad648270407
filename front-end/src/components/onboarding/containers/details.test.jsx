import React from 'react';
import PropTypes from 'prop-types';

import { Provider } from 'react-redux';
import { reduxForm } from 'redux-form';
import { render, fireEvent } from '@testing-library/react';
import configureStore from 'redux-mock-store';
import ContainerDetails from './details';

const applications = [
  {
    'id': 1,
    'name': 'SOL',
    'applicationId': 'sol',
    'rule_version': 2,
    'description': null,
    'status': true,
    'ruleTypes': [ 1 ],
  },
  {
    'id': 2,
    'name': 'Nova Mobile',
    'applicationId': 'nova',
    'rule_version': 2,
    'description': null,
    'status': true,
    'ruleTypes': [ 1, 2 ],
  },
];

const containers = {
  1: {
    'id': 1,
    'name': 'My Activity',
    'containerId': 'my-activity',
    'application': 1,
    'description': null,
  },
  2: {
    'id': 2,
    'name': 'Offers and Programs',
    'containerId': 'offers-and-programs',
    'application': 1,
    'description': null,
  },
};

const pages = {
  1: {
    'id': 1,
    'name': 'Accounts',
    'pageId': 'accounts',
    'application': 1,
    'containers': [ 1, 2 ],
    'description': 'Accounts Page Description',
  },
};

const ruleTypes = [
  { 'id': 1, 'rule_type': 'alert', 'slug': 'alert' },
  { 'id': 2, 'rule_type': 'campaign', 'slug': 'campaign' },
  { 'id': 3, 'rule_type': 'vignette', 'slug': 'vignette' },
];

const contentTypes = {
  'dog': { id: 'dog', name: 'Dog Content Model', updated_at: '2020-11-06T18:20:26.704Z' },
  'cat': { id: 'cat', name: 'Cat Content Model', updated_at: '2020-11-06T18:20:26.704Z' },
  'car': { id: 'car', name: 'Car Content Model', updated_at: '2020-11-06T18:20:26.704Z' },
};

const values = {
  name: 'Alerts',
  containerId: 'alert',
  application: 1,
  rule_type: 'alert',
  content_type: '^alert$',
  status: true,
  description: 'Alerts',
  pages: {},
};

describe('ContainerDetails', () => {
  const commonProps = {
    title: 'String title',
    onCancel: jest.fn(),
    onSubmit: jest.fn(),
    onDelete: jest.fn(),
    checkboxOnClick: jest.fn(),
    createContainerId: jest.fn(),
    updateDetails: jest.fn(),
    pages,
    applications,
    ruleTypes,
    containers,
    isDirty: false,
    formValues: values,
    isLoading: false,
    contentTypes,
    changeField: jest.fn(),
    authenticated: {
      permissions: { 'containers_manage_super': true },
    },
  };

  const mockStore = configureStore([])({});
  const Container = (props) => {
    const ReduxifiedContainer = reduxForm({ form: 'containerDetails' })(ContainerDetails);
    return (
      <Provider store={mockStore}>
        <ReduxifiedContainer {...props} />
      </Provider>
    );
  };

  test('form fields are not shown if the component is loading', () => {
    const { queryByText, rerender } = render(<Container {...commonProps} isLoading={true} />);
    expect(queryByText('Container Details')).not.toBeInTheDocument();
    rerender(<Container {...commonProps} isLoading={false} />);
    expect(queryByText('Container Details')).toBeInTheDocument();
  });

  test('pages shown when rule type is selected, ', () => {
    const { rerender, queryByText } = render(
      <Container {...commonProps} formValues={{}} />
    );
    expect(queryByText('Pages')).not.toBeInTheDocument();

    rerender(<Container {...commonProps} formValues={{ application: 1, rule_type: 'campaign', pages: [ 1 ] }} />);
    expect(queryByText('Pages')).toBeInTheDocument();

    rerender(<Container {...commonProps} formValues={{ application: 1, rule_type: 'alert', pages: [ 1 ] }} />);
    expect(queryByText('Pages')).toBeInTheDocument();
  });

  test('application change causes pages to be cleared', () => {
    const change = jest.fn();
    const { getByText, getByPlaceholderText } = render(
      <Container {...commonProps} formValues={{ application: 1, rule_type: 'campaign' }} changeField={change} />
    );
    expect(change).not.toHaveBeenCalled();
    fireEvent.focus(getByPlaceholderText('Select pages'));
    fireEvent.click(getByText('Accounts'));
    expect(change).toHaveBeenCalledWith('pages', [ 1 ]);

    // change the application field to trigger pages
    change.mockClear();
  });

  test('application change causes pages to be cleared 2', () => {
    const change = jest.fn();
    const { getByText, getByLabelText, getByPlaceholderText } = render(
      <Container {...commonProps} formValues={{ application: 1, rule_type: 'campaign' }} changeField={change} />
    );
    expect(change).not.toHaveBeenCalled();
    fireEvent.focus(getByPlaceholderText('Select pages'));
    fireEvent.click(getByText('Accounts'));
    fireEvent.change(getByLabelText('Application'), { target: { value: '1' } });

    expect(change).toHaveBeenCalledTimes(4);
        // pages cleared
    expect(change.mock.calls[3][0]).toStrictEqual('pages');
    expect(change.mock.calls[3][1]).toStrictEqual([]);
    // rule type cleared
    expect(change.mock.calls[2][0]).toStrictEqual('rule_type');
    expect(change.mock.calls[2][1]).toStrictEqual(null);
  });

  describe('Content Type testing', () => {
    it('initial selections populate correctly when content_type is a string or an array of values', () => {
      const ContainerWithContentType = ({ type }) => (
        <Container
          {...commonProps}
          formValues={{
            ...values,
            content_type: type,
          }}
        />
      );
      ContainerWithContentType.propTypes = {
        type: PropTypes.oneOfType([
          PropTypes.string,
          PropTypes.array,
        ]),
      };

      const { rerender, queryByText } = render(<ContainerWithContentType type="cat" />);

      // we are testing the existence of Autosuggest pills in the 'Content Type' autosuggest field
      expect(queryByText('Cat Content Model')).toBeInTheDocument();
      expect(queryByText('Car Content Model')).not.toBeInTheDocument();
      expect(queryByText('Dog Content Model')).not.toBeInTheDocument();

      rerender(<ContainerWithContentType type="ca" />);

      expect(queryByText('Cat Content Model')).toBeInTheDocument();
      expect(queryByText('Car Content Model')).toBeInTheDocument();
      expect(queryByText('Dog Content Model')).not.toBeInTheDocument();

      rerender(<ContainerWithContentType type={[ 'cat', 'dog' ]} />);

      expect(queryByText('Cat Content Model')).toBeInTheDocument();
      expect(queryByText('Car Content Model')).not.toBeInTheDocument();
      expect(queryByText('Dog Content Model')).toBeInTheDocument();

      rerender(<ContainerWithContentType type={[]} />);

      expect(queryByText('Cat Content Model')).not.toBeInTheDocument();
      expect(queryByText('Car Content Model')).not.toBeInTheDocument();
      expect(queryByText('Dog Content Model')).not.toBeInTheDocument();

      rerender(<ContainerWithContentType type={null} />);

      expect(queryByText('Cat Content Model')).not.toBeInTheDocument();
      expect(queryByText('Car Content Model')).not.toBeInTheDocument();
      expect(queryByText('Dog Content Model')).not.toBeInTheDocument();
    });

    test('`change` is called when content Autosuggest items are selected ', () => {
      const onChange = jest.fn();

      const { getByText, getByPlaceholderText } = render(
        <Container
          {...commonProps}
          changeField={onChange}
        />
      );
      expect(onChange).not.toHaveBeenCalled();
      // select an item from the Autosuggest
      // expect(getAllByPlaceholderText('Please choose a content type')).toBe([]);
      expect(getByPlaceholderText('Select content type')).toBeInTheDocument();
      fireEvent.focus(getByPlaceholderText('Select content type'));

      fireEvent.click(getByText('Car Content Model'));
      expect(onChange).toHaveBeenCalledWith('content_type', [ 'car' ]);
      fireEvent.click(getByText('Dog Content Model'));
      expect(onChange).toHaveBeenCalledWith('content_type', [ 'car', 'dog' ]);
    });

    test('content field is not rendered when rule type is `vignette`', () => {
      // `vignette` rule_type
      const { queryByText, rerender } = render(
        <Container {...commonProps} formValues={{ ...values, rule_type: 'vignette' }} />
      );
      expect(queryByText('Content type')).not.toBeInTheDocument();

      // `campaign` rule_type
      rerender(<Container {...commonProps} formValues={{ ...values, rule_type: 'campaign' }} />);
      // expect(queryAllByText('Content type')).toBe('bla');
      expect(queryByText('Content Type')).toBeInTheDocument();
    });
  });

  describe('cancelling', () => {
    test('onCancel called if form is not dirty', () => {
      const onCancel = jest.fn();
      const { getByText } = render(
        <Container
          {...commonProps}
          isDirty={false}
          onCancel={onCancel}
        />
      );
      fireEvent.click(getByText('Cancel'));
      expect(onCancel).toHaveBeenCalled();
    });

    test('popup appears during cancel if form is dirty', () => {
      const onCancel = jest.fn();
      const { getByText } = render(
        <Container
          {...commonProps}
          isDirty={true}
          onCancel={onCancel}
        />
      );
      fireEvent.click(getByText('Cancel'));
      expect(getByText('Are you sure?')).toBeInTheDocument();
      expect(onCancel).not.toHaveBeenCalled(); // cancel popup is now open

      fireEvent.click(getByText('No')); // cancel the cancel window
      expect(onCancel).not.toHaveBeenCalled();
      fireEvent.click(getByText('Cancel'));
      fireEvent.click(getByText('Yes')); // confirm that we want to cancel

      expect(onCancel).toHaveBeenCalled();
    });
  });
});
