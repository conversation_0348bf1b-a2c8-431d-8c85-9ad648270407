import React from 'react';
import { Router } from 'react-router-dom';
import { createMemoryHistory } from 'history';
import { defaultData, renderPage } from '../../../utils/testing-library-utils';
import { Provider } from 'react-redux';
import { render, fireEvent } from '@testing-library/react';
import { within } from '@testing-library/dom';
import { ContainerList } from './listContainer';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';

const mockApplications = [
  {
    id: 1,
    name: 'SOL',
    team_id: 1,
  },
  {
    id: 2,
    name: 'eExperience Storefront',
    team_id: 1,
  },
  {
    id: 3,
    name: 'Nova Mobile',
    team_id: 1,
  },
  {
    id: 4,
    name: 'Test App',
    team_id: 1,
  },
];

const mockPages = [
  {
    'id': 1,
    'application': 3,
    'name': 'activities',
    'pageId': 'activities',
    'status': true,
    'description': 'NOVA mobile - My Activity tab',
    'containers': [ 2 ],
  },
  {
    'id': 2,
    'application': 3,
    'name': 'accounts',
    'pageId': 'accounts',
    'status': true,
    'description': 'NOVA mobile - My Accounts tab',
    'containers': [ 2, 3, 4 ],
  },
  {
    'id': 3,
    'application': 4,
    'name': 'Test Page',
    'pageId': 'test-page',
    'status': true,
    'description': 'Just a lil test page',
    'containers': [],
  },
];

const mockContainers = [
  {
    'id': 1,
    'name': 'alert',
    'containerId': 'alert',
    'application': 'Nova Mobile',
    'status': true,
    'description': 'Alert',
    'content_type': '^alert$',
    'rule_type': 'alert',
    'pages': [],
  },
  {
    'id': 2,
    'name': 'my-activity',
    'containerId': 'my-activity',
    'application': 'Nova Mobile',
    'status': true,
    'description': 'My Activity',
    'content_type': '^standingCampaignTemplate\\d+$',
    'rule_type': 'campaign',
    'pages': [ 'NOVA mobile - My Activity tab', 'NOVA mobile - My Accounts tab' ],
  },
  {
    'id': 3,
    'name': 'offers-and-programs',
    'containerId': 'offers-and-programs',
    'application': 'Nova Mobile',
    'status': true,
    'description': 'Offers and Programs',
    'content_type': '^targetedCampaignTemplate\\d+$',
    'rule_type': 'campaign',
    'pages': [ 'NOVA mobile - My Accounts tab' ],
  },
  {
    'id': 4,
    'name': 'priority-box',
    'containerId': 'priority-box',
    'application': 'Nova Mobile',
    'status': true,
    'description': 'Priority Box',
    'content_type': null,
    'rule_type': 'campaign',
    'pages': [ 'NOVA mobile - My Accounts tab' ],
  },
  {
    'id': 5,
    'name': 'storefront-container',
    'containerId': 'storefront-container',
    'application': 'eExperience Storefront',
    'status': false,
    'description': 'Storefront container',
    'content_type': null,
    'rule_type': 'storefront',
    'pages': [],
  },
];

const mockAuthenticated = {
  permissions: {
    containers_view_super: true,
    containers_manage_super: true,
  },
  permissionLevels: {
    canViewAllContainers: true,
    canViewOwnTeamContainers: true,
  },
};

describe('ContainerList', () => {
  const loadContainerList = jest.fn();
  const mockSetContainerActivation = jest.fn();
  const store = configureMockStore([ thunk ])({});

  const containerComponent = (
    <Router history={createMemoryHistory()}>
      <Provider store={store}>
        <ContainerList
          isLoading={false}
          loadContainerList={loadContainerList}
          applications={mockApplications}
          containers={mockContainers}
          pages={mockPages}
          authenticated={mockAuthenticated}
          setContainerActivation={mockSetContainerActivation}
        />
      </Provider>
    </Router>
  );

  beforeEach(() => {
    loadContainerList.mockReset();
  });

  it('only makes calls for pages that belong to the application', () => {
    const { getByLabelText, getByText } = render(containerComponent);
    // open filter
    const filterBtn = getByText('Filter');
    fireEvent.click(filterBtn);
    const filterSection = document.getElementsByClassName('admin-filter')[0];
    // application and page chosen that are valid
    fireEvent.change(within(filterSection).getByLabelText('Application'), { target: { value: '3' } });
    loadContainerList.mockReset();
    fireEvent.change(within(filterSection).getByLabelText('Page'), { target: { value: '1' } });
    loadContainerList.mockReset();
    // application changed to one that the current page no longer applies to
    fireEvent.change(within(filterSection).getByLabelText('Application'), { target: { value: '2' } });
    expect(getByLabelText('Page')).toHaveValue('all');
  });

  it('renders containers', () => {
    const { container } = render(containerComponent);
    expect(container).toMatchSnapshot();
  });

  it('deactivate journey', async() => {
    const { getByText, getAllByText, getByRole } = await renderPage(containerComponent);
    const container = defaultData.containers[0];
    // trigger deactivation warning
    const targetContainer = getAllByText(container.name)[0];
    const statusToggle = within(targetContainer.closest('tr')).getByTestId(`status-toggle-${container.id}`);
    expect(statusToggle.checked).toBeTruthy();
    fireEvent.click(statusToggle);

    // due to known bug in canvas prior to 7.1.0, every time modal dialogue opens,
    // we must wait for focus animation to complete before closing the modal
    // https://bitbucket.agile.bns/projects/CANVAS/repos/core-react-develop/pull-requests/779/overview
    expect(getByText('Are you sure you want to deactivate this container?')).toBeInTheDocument();
    expect(getByText('Users will no longer be able to create new campaigns or alerts for this container.')).toBeInTheDocument();
    fireEvent.click(getByRole('button', { name: 'Deactivate Container' }));
    expect(mockSetContainerActivation).toHaveBeenCalledWith(container.id, false, { application_id: '2', limit: 30, pageNumber: 1 });
  });

  it('should render only selected application`s pages', async() => {
    let mockApplication = mockApplications[2];
    const { getByText, queryByRole } = await renderPage(containerComponent);
    // open filter section
    const filterBtn = getByText('Filter');
    fireEvent.click(filterBtn);
    const filterSection = document.getElementsByClassName('admin-filter')[0];
    // select values
    fireEvent.change(within(filterSection).getByLabelText('Application'), { target: { value: mockApplication.id } });
    fireEvent.click(within(filterSection).getByText('Page'));
    expect(queryByRole('option', { name: 'accounts' })).toBeInTheDocument(); // belong to Applications 3
    expect(queryByRole('option', { name: 'Test Page' })).not.toBeInTheDocument(); // not belong to to Applications 3

    mockApplication = mockApplications[3];
    fireEvent.change(within(filterSection).getByLabelText('Application'), { target: { value: mockApplication.id } });
    fireEvent.click(within(filterSection).getByText('Page'));
    expect(queryByRole('option', { name: 'Test Page' })).toBeInTheDocument(); // belong to Applications 3
    expect(queryByRole('option', { name: 'accounts' })).not.toBeInTheDocument(); // not belong to to Applications 3
  });
});
