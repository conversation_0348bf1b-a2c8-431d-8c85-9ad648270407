// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ContainerList renders containers 1`] = `
<div>
  <div
    class="admin-list container-list"
  >
    <div
      class="admin-list__action-bar"
    >
      <h1
        class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iejpSD admin-list__header"
        color="black"
      >
        Containers
         
      </h1>
      <button
        class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary"
        type="caution"
      >
        <span
          class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
          tabindex="-1"
        >
          <span
            class="ButtonCore__text"
          >
            Create 
            Container
          </span>
        </span>
      </button>
    </div>
    <div
      class="page-actions__actions-container"
    >
      <div
        class="page-actions__search"
      >
        <div
          class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline"
        >
          <label
            class="Searchstyle__SearchLabelDesktop-canvas-core__sc-xli5u-3 kreROw Search__label--desktop"
            for="page-action-search-input"
            id="page-action-search-label-desktop"
          >
            <span
              class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
            >
              Search
            </span>
          </label>
          <div
            class="Searchstyle__InputWrapper-canvas-core__sc-xli5u-0 faizXP"
            role="search"
          >
            <label
              class="Searchstyle__SearchLabelMobile-canvas-core__sc-xli5u-2 gTlGrz Search__label--mobile"
              for="page-action-search-input"
              id="page-action-search-label-mobile"
            >
              <button
                class="Searchstyle__SearchButtonMobile-canvas-core__sc-xli5u-5 jHXkhr Search__button Search__button--search"
                type="submit"
              >
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon Search__Icon"
                  color="black"
                  focusable="false"
                  role="presentation"
                  size="18"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M15.6248 15.6249L21.6227 21.6228"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    clip-rule="evenodd"
                    d="M17.7135 9.85775C17.7135 5.51885 14.1957 2 9.85677 2C5.51787 2 2 5.51885 2 9.85775C2 14.1957 5.51787 17.7135 9.85677 17.7135C14.1957 17.7135 17.7135 14.1957 17.7135 9.85775Z"
                    fill="none"
                    fill-rule="evenodd"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                >
                  Search
                </span>
              </button>
            </label>
            <input
              aria-describedby="Search__errors-page-action-search"
              class="Searchstyle__SearchInput-canvas-core__sc-xli5u-1 jtIJGo Search__input"
              id="page-action-search-input"
              placeholder="Search"
              value=""
            />
            <button
              aria-label=""
              class="Searchstyle__SearchButton-canvas-core__sc-xli5u-4 kmxzup Search__button Search__button--search"
              type="submit"
            >
              <svg
                aria-hidden="true"
                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon Search__buttonIcon"
                color="black"
                focusable="false"
                role="presentation"
                size="18"
                viewBox="0 0 24 24"
              >
                <path
                  d="M15.6248 15.6249L21.6227 21.6228"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  clip-rule="evenodd"
                  d="M17.7135 9.85775C17.7135 5.51885 14.1957 2 9.85677 2C5.51787 2 2 5.51885 2 9.85775C2 14.1957 5.51787 17.7135 9.85677 17.7135C14.1957 17.7135 17.7135 14.1957 17.7135 9.85775Z"
                  fill="none"
                  fill-rule="evenodd"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
            
          </div>
          <div
            id="Search__errors-page-action-search"
          />
        </div>
      </div>
      <div>
        <button
          class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button"
          color="blue"
        >
          <svg
            aria-hidden="true"
            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
            color="currentColor"
            focusable="false"
            role="presentation"
            size="16"
            viewBox="0 0 30 30"
          >
            <path
              clip-rule="evenodd"
              d="M15.0911 7.80238C13.8895 7.80238 12.9182 6.83003 12.9182 5.62953C12.9182 4.42903 13.8895 3.45667 15.0911 3.45667C16.2916 3.45667 17.2639 4.42903 17.2639 5.62953C17.2639 6.83003 16.2916 7.80238 15.0911 7.80238Z"
              fill="none"
              fill-rule="evenodd"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              clip-rule="evenodd"
              d="M9.96851 17.1728C8.76801 17.1728 7.79565 16.2004 7.79565 14.9999C7.79565 13.7994 8.76801 12.8271 9.96851 12.8271C11.169 12.8271 12.1414 13.7994 12.1414 14.9999C12.1414 16.2004 11.169 17.1728 9.96851 17.1728Z"
              fill="none"
              fill-rule="evenodd"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              clip-rule="evenodd"
              d="M19.9636 26.5432C18.762 26.5432 17.7908 25.5708 17.7908 24.3703C17.7908 23.1698 18.762 22.1975 19.9636 22.1975C21.1641 22.1975 22.1365 23.1698 22.1365 24.3703C22.1365 25.5708 21.1641 26.5432 19.9636 26.5432Z"
              fill="none"
              fill-rule="evenodd"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M12.7056 15.1696H28.2969"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M1.33056 5.53895H12.7049"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M17.4854 5.79923H28.3174"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M1.31748 24.2797H17.4852"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M22.7002 24.54H28.2944"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M1.34448 14.9093H6.83822"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          <span
            class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
          >
            Filter
          </span>
        </button>
      </div>
    </div>
    <div
      class="Tablestyle__StyledTable-canvas-core__sc-mfsabp-0 dvntiZ Table"
    >
      <div
        class="Tablestyle__StyledTableWrapper-canvas-core__sc-mfsabp-1 ikRFBm Table__wrapper"
        id="table-admin-list-table"
      >
        <table
          class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable admin-list__table"
        >
          <caption
            aria-live="polite"
            class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP TableHeadingstyle__StyledHeading-canvas-core__sc-2qo8vm-0 bPEdEe Table__title"
            color="black"
            size="21"
          />
          <thead
            class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
            role="rowgroup"
          >
            <tr
              class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
              role="row"
            >
              <th
                aria-sort="none"
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 kQJGxh"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <button
                  class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                  data="[object Object]"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Container Name
                  </p>
                  <div
                    class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="8"
                      viewBox="4 0 10 10"
                    >
                      <path
                        d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                      />
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="8"
                      viewBox="4 8 10 10"
                    >
                      <path
                        d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                      />
                    </svg>
                  </div>
                  <span
                    class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                  >
                    unsorted
                  </span>
                </button>
              </th>
              <th
                aria-sort="none"
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 kQJGxh"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <button
                  class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                  data="[object Object]"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    ID
                  </p>
                  <div
                    class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="8"
                      viewBox="4 0 10 10"
                    >
                      <path
                        d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                      />
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="8"
                      viewBox="4 8 10 10"
                    >
                      <path
                        d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                      />
                    </svg>
                  </div>
                  <span
                    class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                  >
                    unsorted
                  </span>
                </button>
              </th>
              <th
                aria-sort="none"
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <button
                  class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                  data="[object Object]"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Rule Type
                  </p>
                  <div
                    class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="8"
                      viewBox="4 0 10 10"
                    >
                      <path
                        d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                      />
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="8"
                      viewBox="4 8 10 10"
                    >
                      <path
                        d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                      />
                    </svg>
                  </div>
                  <span
                    class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                  >
                    unsorted
                  </span>
                </button>
              </th>
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 kQJGxh"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  Associated Pages
                </p>
              </th>
              <th
                aria-sort="none"
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <button
                  class="TableHeadstyle__StyledHeaderButton-canvas-core__sc-avmfdv-2 cIUXPG"
                  data="[object Object]"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                    color="black"
                  >
                    Application
                  </p>
                  <div
                    class="TableHeadstyle__StyledSortableWrapper-canvas-core__sc-avmfdv-6 bpuaEs"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableAsc-canvas-core__sc-avmfdv-7 hsrBOY"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="8"
                      viewBox="4 0 10 10"
                    >
                      <path
                        d="M5.46448 8L9.00001 4.46447L12.5355 8H9.00001H5.46448Z"
                      />
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 wjeXK SvgIcon__icon TableHeadstyle__StyledSortableDesc-canvas-core__sc-avmfdv-8 eCKTfz"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="8"
                      viewBox="4 8 10 10"
                    >
                      <path
                        d="M5.46448 10L9.00001 13.5355L12.5355 10H9.00001H5.46448Z"
                      />
                    </svg>
                  </div>
                  <span
                    class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                  >
                    unsorted
                  </span>
                </button>
              </th>
              <th
                class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                data="[object Object]"
                role="columnheader"
                scope="col"
              >
                <span
                  class="TableHeadstyle__StyledTooltipWrapper-canvas-core__sc-avmfdv-4 lawWnN"
                >
                  <span
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableHeadstyle__StyledTooltip-canvas-core__sc-avmfdv-5 jTTNmu"
                    color="black"
                    data="[object Object]"
                  >
                    An active container can be used by campaigns and alerts. An inactive container cannot be used by campaigns and alerts. By default, if a container belongs to an inactive application, the container will also be inactive.
                  </span>
                </span>
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                  color="black"
                >
                  Status
                </p>
              </th>
            </tr>
          </thead>
          <tbody
            class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
            role="rowgroup"
          >
            <tr
              class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jGMNii"
              role="row"
            >
              <th
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="rowheader"
                scope="row"
              >
                <a
                  class="Linkstyle__Wrapper-canvas-core__sc-nzeldc-1 hqbNYC admin-list__name-link"
                  href="/containers/1"
                  size="16"
                  theme="[object Object]"
                  type="emphasis"
                  weight="bold"
                >
                  <span
                    class="Linkstyle__Text-canvas-core__sc-nzeldc-0 ictHEk"
                    type="emphasis"
                  >
                    alert
                  </span>
                </a>
                <div
                  class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                  id="tooltip-container-tooltip-1-Alert"
                >
                  <div
                    class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                  >
                    <button
                      aria-label="Info, alert"
                      class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
                      id="desktop-icon-tooltip-1-Alert"
                      type="button"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 cpIiDu SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btYqFz"
                        color="blue"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                          fill="none"
                          fill-rule="evenodd"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M15 20.9062V14.1562"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <circle
                          cx="14.9998"
                          cy="9.1309"
                          r="0.7"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </th>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                  color="black"
                >
                  alert
                </p>
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                  color="black"
                >
                  alert
                </p>
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="cell"
              >
                <div
                  class="admin-list__list-cell"
                />
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                  color="black"
                />
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <div
                  class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline admin-list__toggle-switch"
                >
                  <label
                    class="ToggleSwitchstyle__Wrapper-canvas-core__sc-168jcna-0 bMCkrB ToggleSwitch__label"
                    disabled=""
                    for="status-toggle-1"
                  >
                    <input
                      checked=""
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 kgbyfK Input__input Input__input--disabled ToggleInput"
                      data-testid="status-toggle-1"
                      disabled=""
                      id="status-toggle-1"
                      name="status-toggle-1"
                      type="checkbox"
                      value=""
                    />
                    <div
                      class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label labelSpacing"
                    >
                      <div
                        class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW labelSpacing"
                      >
                        
                      </div>
                    </div>
                    <span
                      class="ToggleSwitchstyle__StyledSlider-canvas-core__sc-168jcna-1 ktXjYj ToggleSwitch__slider"
                      disabled=""
                    >
                      <span
                        class="ToggleSwitchstyle__StyledSwitch-canvas-core__sc-168jcna-2 kWOVUq ToggleSwitch__switch"
                      >
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-off"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="10"
                          viewBox="0 0 10 10"
                        >
                          <g
                            class="IconToggleSwitchOffstyle__StyledG-canvas-core__sc-r3kqbd-0 ezyJRW"
                            fill="none"
                            fill-rule="evenodd"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1"
                          >
                            <g
                              stroke-width="2"
                              transform="translate(-496.000000, -153.000000)"
                            >
                              <g
                                transform="translate(439.000000, 143.000000)"
                              >
                                <g
                                  transform="translate(47.000000, 0.000000)"
                                >
                                  <g
                                    id="Group"
                                    transform="translate(11.000000, 11.000000)"
                                  >
                                    <line
                                      id="Stroke-184"
                                      x1="0"
                                      x2="8"
                                      y1="0"
                                      y2="8"
                                    />
                                    <line
                                      id="Stroke-185"
                                      x1="0"
                                      x2="8"
                                      y1="8"
                                      y2="0"
                                    />
                                  </g>
                                </g>
                              </g>
                            </g>
                          </g>
                        </svg>
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-on"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="10"
                          viewBox="0 0 11 9"
                        >
                          <g
                            class="IconToggleSwitchOnstyle__StyledG-canvas-core__sc-16ds4pu-0 fVdUxI"
                            fill="none"
                            fill-rule="evenodd"
                            id="Page-1"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1"
                          >
                            <g
                              stroke-width="2"
                              transform="translate(-522.000000, -111.000000)"
                            >
                              <g
                                transform="translate(439.000000, 100.000000)"
                              >
                                <g
                                  transform="translate(84.000000, 12.000000)"
                                >
                                  <polyline
                                    points="0 3.73333333 3.15 7 9 0"
                                  />
                                </g>
                              </g>
                            </g>
                          </g>
                        </svg>
                      </span>
                    </span>
                  </label>
                </div>
              </td>
            </tr>
            <tr
              class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jGMNii"
              role="row"
            >
              <th
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="rowheader"
                scope="row"
              >
                <a
                  class="Linkstyle__Wrapper-canvas-core__sc-nzeldc-1 hqbNYC admin-list__name-link"
                  href="/containers/2"
                  size="16"
                  theme="[object Object]"
                  type="emphasis"
                  weight="bold"
                >
                  <span
                    class="Linkstyle__Text-canvas-core__sc-nzeldc-0 ictHEk"
                    type="emphasis"
                  >
                    my-activity
                  </span>
                </a>
                <div
                  class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                  id="tooltip-container-tooltip-2-My Activity"
                >
                  <div
                    class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                  >
                    <button
                      aria-label="Info, my-activity"
                      class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
                      id="desktop-icon-tooltip-2-My Activity"
                      type="button"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 cpIiDu SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btYqFz"
                        color="blue"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                          fill="none"
                          fill-rule="evenodd"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M15 20.9062V14.1562"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <circle
                          cx="14.9998"
                          cy="9.1309"
                          r="0.7"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </th>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                  color="black"
                >
                  my-activity
                </p>
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                  color="black"
                >
                  campaign
                </p>
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="cell"
              >
                <div
                  class="admin-list__list-cell"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text container-list__cell-item"
                    color="black"
                  />
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text container-list__cell-item"
                    color="black"
                  />
                </div>
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                  color="black"
                />
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <div
                  class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline admin-list__toggle-switch"
                >
                  <label
                    class="ToggleSwitchstyle__Wrapper-canvas-core__sc-168jcna-0 bMCkrB ToggleSwitch__label"
                    disabled=""
                    for="status-toggle-2"
                  >
                    <input
                      checked=""
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 kgbyfK Input__input Input__input--disabled ToggleInput"
                      data-testid="status-toggle-2"
                      disabled=""
                      id="status-toggle-2"
                      name="status-toggle-2"
                      type="checkbox"
                      value=""
                    />
                    <div
                      class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label labelSpacing"
                    >
                      <div
                        class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW labelSpacing"
                      >
                        
                      </div>
                    </div>
                    <span
                      class="ToggleSwitchstyle__StyledSlider-canvas-core__sc-168jcna-1 ktXjYj ToggleSwitch__slider"
                      disabled=""
                    >
                      <span
                        class="ToggleSwitchstyle__StyledSwitch-canvas-core__sc-168jcna-2 kWOVUq ToggleSwitch__switch"
                      >
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-off"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="10"
                          viewBox="0 0 10 10"
                        >
                          <g
                            class="IconToggleSwitchOffstyle__StyledG-canvas-core__sc-r3kqbd-0 ezyJRW"
                            fill="none"
                            fill-rule="evenodd"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1"
                          >
                            <g
                              stroke-width="2"
                              transform="translate(-496.000000, -153.000000)"
                            >
                              <g
                                transform="translate(439.000000, 143.000000)"
                              >
                                <g
                                  transform="translate(47.000000, 0.000000)"
                                >
                                  <g
                                    id="Group"
                                    transform="translate(11.000000, 11.000000)"
                                  >
                                    <line
                                      id="Stroke-184"
                                      x1="0"
                                      x2="8"
                                      y1="0"
                                      y2="8"
                                    />
                                    <line
                                      id="Stroke-185"
                                      x1="0"
                                      x2="8"
                                      y1="8"
                                      y2="0"
                                    />
                                  </g>
                                </g>
                              </g>
                            </g>
                          </g>
                        </svg>
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-on"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="10"
                          viewBox="0 0 11 9"
                        >
                          <g
                            class="IconToggleSwitchOnstyle__StyledG-canvas-core__sc-16ds4pu-0 fVdUxI"
                            fill="none"
                            fill-rule="evenodd"
                            id="Page-1"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1"
                          >
                            <g
                              stroke-width="2"
                              transform="translate(-522.000000, -111.000000)"
                            >
                              <g
                                transform="translate(439.000000, 100.000000)"
                              >
                                <g
                                  transform="translate(84.000000, 12.000000)"
                                >
                                  <polyline
                                    points="0 3.73333333 3.15 7 9 0"
                                  />
                                </g>
                              </g>
                            </g>
                          </g>
                        </svg>
                      </span>
                    </span>
                  </label>
                </div>
              </td>
            </tr>
            <tr
              class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jGMNii"
              role="row"
            >
              <th
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="rowheader"
                scope="row"
              >
                <a
                  class="Linkstyle__Wrapper-canvas-core__sc-nzeldc-1 hqbNYC admin-list__name-link"
                  href="/containers/3"
                  size="16"
                  theme="[object Object]"
                  type="emphasis"
                  weight="bold"
                >
                  <span
                    class="Linkstyle__Text-canvas-core__sc-nzeldc-0 ictHEk"
                    type="emphasis"
                  >
                    offers-and-programs
                  </span>
                </a>
                <div
                  class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                  id="tooltip-container-tooltip-3-Offers and Programs"
                >
                  <div
                    class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                  >
                    <button
                      aria-label="Info, offers-and-programs"
                      class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--top"
                      id="desktop-icon-tooltip-3-Offers and Programs"
                      type="button"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 cpIiDu SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btYqFz"
                        color="blue"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                          fill="none"
                          fill-rule="evenodd"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M15 20.9062V14.1562"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <circle
                          cx="14.9998"
                          cy="9.1309"
                          r="0.7"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </th>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                  color="black"
                >
                  offers-and-programs
                </p>
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                  color="black"
                >
                  campaign
                </p>
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="cell"
              >
                <div
                  class="admin-list__list-cell"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text container-list__cell-item"
                    color="black"
                  />
                </div>
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                  color="black"
                />
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <div
                  class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline admin-list__toggle-switch"
                >
                  <label
                    class="ToggleSwitchstyle__Wrapper-canvas-core__sc-168jcna-0 bMCkrB ToggleSwitch__label"
                    disabled=""
                    for="status-toggle-3"
                  >
                    <input
                      checked=""
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 kgbyfK Input__input Input__input--disabled ToggleInput"
                      data-testid="status-toggle-3"
                      disabled=""
                      id="status-toggle-3"
                      name="status-toggle-3"
                      type="checkbox"
                      value=""
                    />
                    <div
                      class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label labelSpacing"
                    >
                      <div
                        class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW labelSpacing"
                      >
                        
                      </div>
                    </div>
                    <span
                      class="ToggleSwitchstyle__StyledSlider-canvas-core__sc-168jcna-1 ktXjYj ToggleSwitch__slider"
                      disabled=""
                    >
                      <span
                        class="ToggleSwitchstyle__StyledSwitch-canvas-core__sc-168jcna-2 kWOVUq ToggleSwitch__switch"
                      >
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-off"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="10"
                          viewBox="0 0 10 10"
                        >
                          <g
                            class="IconToggleSwitchOffstyle__StyledG-canvas-core__sc-r3kqbd-0 ezyJRW"
                            fill="none"
                            fill-rule="evenodd"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1"
                          >
                            <g
                              stroke-width="2"
                              transform="translate(-496.000000, -153.000000)"
                            >
                              <g
                                transform="translate(439.000000, 143.000000)"
                              >
                                <g
                                  transform="translate(47.000000, 0.000000)"
                                >
                                  <g
                                    id="Group"
                                    transform="translate(11.000000, 11.000000)"
                                  >
                                    <line
                                      id="Stroke-184"
                                      x1="0"
                                      x2="8"
                                      y1="0"
                                      y2="8"
                                    />
                                    <line
                                      id="Stroke-185"
                                      x1="0"
                                      x2="8"
                                      y1="8"
                                      y2="0"
                                    />
                                  </g>
                                </g>
                              </g>
                            </g>
                          </g>
                        </svg>
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-on"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="10"
                          viewBox="0 0 11 9"
                        >
                          <g
                            class="IconToggleSwitchOnstyle__StyledG-canvas-core__sc-16ds4pu-0 fVdUxI"
                            fill="none"
                            fill-rule="evenodd"
                            id="Page-1"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1"
                          >
                            <g
                              stroke-width="2"
                              transform="translate(-522.000000, -111.000000)"
                            >
                              <g
                                transform="translate(439.000000, 100.000000)"
                              >
                                <g
                                  transform="translate(84.000000, 12.000000)"
                                >
                                  <polyline
                                    points="0 3.73333333 3.15 7 9 0"
                                  />
                                </g>
                              </g>
                            </g>
                          </g>
                        </svg>
                      </span>
                    </span>
                  </label>
                </div>
              </td>
            </tr>
            <tr
              class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jGMNii"
              role="row"
            >
              <th
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="rowheader"
                scope="row"
              >
                <a
                  class="Linkstyle__Wrapper-canvas-core__sc-nzeldc-1 hqbNYC admin-list__name-link"
                  href="/containers/4"
                  size="16"
                  theme="[object Object]"
                  type="emphasis"
                  weight="bold"
                >
                  <span
                    class="Linkstyle__Text-canvas-core__sc-nzeldc-0 ictHEk"
                    type="emphasis"
                  >
                    priority-box
                  </span>
                </a>
                <div
                  class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                  id="tooltip-container-tooltip-4-Priority Box"
                >
                  <div
                    class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                  >
                    <button
                      aria-label="Info, priority-box"
                      class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--top"
                      id="desktop-icon-tooltip-4-Priority Box"
                      type="button"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 cpIiDu SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btYqFz"
                        color="blue"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                          fill="none"
                          fill-rule="evenodd"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M15 20.9062V14.1562"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <circle
                          cx="14.9998"
                          cy="9.1309"
                          r="0.7"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </th>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                  color="black"
                >
                  priority-box
                </p>
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                  color="black"
                >
                  campaign
                </p>
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="cell"
              >
                <div
                  class="admin-list__list-cell"
                >
                  <p
                    class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text container-list__cell-item"
                    color="black"
                  />
                </div>
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text"
                  color="black"
                />
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <div
                  class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline admin-list__toggle-switch"
                >
                  <label
                    class="ToggleSwitchstyle__Wrapper-canvas-core__sc-168jcna-0 bMCkrB ToggleSwitch__label"
                    disabled=""
                    for="status-toggle-4"
                  >
                    <input
                      checked=""
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 kgbyfK Input__input Input__input--disabled ToggleInput"
                      data-testid="status-toggle-4"
                      disabled=""
                      id="status-toggle-4"
                      name="status-toggle-4"
                      type="checkbox"
                      value=""
                    />
                    <div
                      class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label labelSpacing"
                    >
                      <div
                        class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW labelSpacing"
                      >
                        
                      </div>
                    </div>
                    <span
                      class="ToggleSwitchstyle__StyledSlider-canvas-core__sc-168jcna-1 ktXjYj ToggleSwitch__slider"
                      disabled=""
                    >
                      <span
                        class="ToggleSwitchstyle__StyledSwitch-canvas-core__sc-168jcna-2 kWOVUq ToggleSwitch__switch"
                      >
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-off"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="10"
                          viewBox="0 0 10 10"
                        >
                          <g
                            class="IconToggleSwitchOffstyle__StyledG-canvas-core__sc-r3kqbd-0 ezyJRW"
                            fill="none"
                            fill-rule="evenodd"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1"
                          >
                            <g
                              stroke-width="2"
                              transform="translate(-496.000000, -153.000000)"
                            >
                              <g
                                transform="translate(439.000000, 143.000000)"
                              >
                                <g
                                  transform="translate(47.000000, 0.000000)"
                                >
                                  <g
                                    id="Group"
                                    transform="translate(11.000000, 11.000000)"
                                  >
                                    <line
                                      id="Stroke-184"
                                      x1="0"
                                      x2="8"
                                      y1="0"
                                      y2="8"
                                    />
                                    <line
                                      id="Stroke-185"
                                      x1="0"
                                      x2="8"
                                      y1="8"
                                      y2="0"
                                    />
                                  </g>
                                </g>
                              </g>
                            </g>
                          </g>
                        </svg>
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-on"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="10"
                          viewBox="0 0 11 9"
                        >
                          <g
                            class="IconToggleSwitchOnstyle__StyledG-canvas-core__sc-16ds4pu-0 fVdUxI"
                            fill="none"
                            fill-rule="evenodd"
                            id="Page-1"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1"
                          >
                            <g
                              stroke-width="2"
                              transform="translate(-522.000000, -111.000000)"
                            >
                              <g
                                transform="translate(439.000000, 100.000000)"
                              >
                                <g
                                  transform="translate(84.000000, 12.000000)"
                                >
                                  <polyline
                                    points="0 3.73333333 3.15 7 9 0"
                                  />
                                </g>
                              </g>
                            </g>
                          </g>
                        </svg>
                      </span>
                    </span>
                  </label>
                </div>
              </td>
            </tr>
            <tr
              class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jGMNii"
              role="row"
            >
              <th
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="rowheader"
                scope="row"
              >
                <a
                  class="Linkstyle__Wrapper-canvas-core__sc-nzeldc-1 hqbNYC admin-list__name-link admin-list__name-link--disabled"
                  href="/containers/5"
                  size="16"
                  theme="[object Object]"
                  type="emphasis"
                  weight="bold"
                >
                  <span
                    class="Linkstyle__Text-canvas-core__sc-nzeldc-0 ictHEk"
                    type="emphasis"
                  >
                    storefront-container
                  </span>
                </a>
                <div
                  class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                  id="tooltip-container-tooltip-5-Storefront container"
                >
                  <div
                    class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                  >
                    <button
                      aria-label="Info, storefront-container"
                      class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--top"
                      id="desktop-icon-tooltip-5-Storefront container"
                      type="button"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 cpIiDu SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btYqFz"
                        color="blue"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                          fill="none"
                          fill-rule="evenodd"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M15 20.9062V14.1562"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <circle
                          cx="14.9998"
                          cy="9.1309"
                          r="0.7"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </th>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 bhzbbw TextCaption__text"
                  color="gray"
                >
                  storefront-container
                </p>
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 bhzbbw TextCaption__text"
                  color="gray"
                >
                  storefront
                </p>
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 jzZhtT"
                role="cell"
              >
                <div
                  class="admin-list__list-cell"
                />
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <p
                  class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 bhzbbw TextCaption__text"
                  color="gray"
                />
              </td>
              <td
                class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                role="cell"
              >
                <div
                  class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline admin-list__toggle-switch"
                >
                  <label
                    class="ToggleSwitchstyle__Wrapper-canvas-core__sc-168jcna-0 bMCkrB ToggleSwitch__label"
                    disabled=""
                    for="status-toggle-5"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 kgbyfK Input__input Input__input--disabled ToggleInput"
                      data-testid="status-toggle-5"
                      disabled=""
                      id="status-toggle-5"
                      name="status-toggle-5"
                      type="checkbox"
                      value=""
                    />
                    <div
                      class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label labelSpacing"
                    >
                      <div
                        class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW labelSpacing"
                      >
                        
                      </div>
                    </div>
                    <span
                      class="ToggleSwitchstyle__StyledSlider-canvas-core__sc-168jcna-1 ktXjYj ToggleSwitch__slider"
                      disabled=""
                    >
                      <span
                        class="ToggleSwitchstyle__StyledSwitch-canvas-core__sc-168jcna-2 kWOVUq ToggleSwitch__switch"
                      >
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-off"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="10"
                          viewBox="0 0 10 10"
                        >
                          <g
                            class="IconToggleSwitchOffstyle__StyledG-canvas-core__sc-r3kqbd-0 ezyJRW"
                            fill="none"
                            fill-rule="evenodd"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1"
                          >
                            <g
                              stroke-width="2"
                              transform="translate(-496.000000, -153.000000)"
                            >
                              <g
                                transform="translate(439.000000, 143.000000)"
                              >
                                <g
                                  transform="translate(47.000000, 0.000000)"
                                >
                                  <g
                                    id="Group"
                                    transform="translate(11.000000, 11.000000)"
                                  >
                                    <line
                                      id="Stroke-184"
                                      x1="0"
                                      x2="8"
                                      y1="0"
                                      y2="8"
                                    />
                                    <line
                                      id="Stroke-185"
                                      x1="0"
                                      x2="8"
                                      y1="8"
                                      y2="0"
                                    />
                                  </g>
                                </g>
                              </g>
                            </g>
                          </g>
                        </svg>
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-on"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="10"
                          viewBox="0 0 11 9"
                        >
                          <g
                            class="IconToggleSwitchOnstyle__StyledG-canvas-core__sc-16ds4pu-0 fVdUxI"
                            fill="none"
                            fill-rule="evenodd"
                            id="Page-1"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1"
                          >
                            <g
                              stroke-width="2"
                              transform="translate(-522.000000, -111.000000)"
                            >
                              <g
                                transform="translate(439.000000, 100.000000)"
                              >
                                <g
                                  transform="translate(84.000000, 12.000000)"
                                >
                                  <polyline
                                    points="0 3.73333333 3.15 7 9 0"
                                  />
                                </g>
                              </g>
                            </g>
                          </g>
                        </svg>
                      </span>
                    </span>
                  </label>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <nav
    aria-label="Pagination Navigation"
    class="DesktopPaginationstyle__Wrapper-canvas-core__sc-6osluo-0 dEkrda DesktopPagination__container DesktopPagination__container--card"
    id="containers-pagination"
  >
    <span
      aria-live="polite"
      class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 bhzbbw TextCaption__text"
      color="gray"
    >
      NaN-NaN of 0 results
    </span>
    <ul
      class="DesktopPagination__list"
    >
      <li
        class="PaginationItemstyle__Wrapper-canvas-core__sc-1gx7xda-0 ctDMuy PaginationItem"
      >
        <button
          aria-current="false"
          aria-label="Previous"
          class="PaginationItemstyle__Button-canvas-core__sc-1gx7xda-1 hnTLVo PaginationItem__btn"
        >
          <div
            class="PaginationItemstyle__ContentBox-canvas-core__sc-1gx7xda-2 jQIOPj PaginationItem__contentBox"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="18"
              viewBox="0 0 30 30"
            >
              <path
                d="M21.75 28.4999L8.25 14.9999L21.75 1.49991"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </button>
      </li>
      <li
        class="PaginationItemstyle__Wrapper-canvas-core__sc-1gx7xda-0 ctDMuy PaginationItem"
      >
        <button
          aria-current="false"
          aria-label="Next"
          class="PaginationItemstyle__Button-canvas-core__sc-1gx7xda-1 hnTLVo PaginationItem__btn"
        >
          <div
            class="PaginationItemstyle__ContentBox-canvas-core__sc-1gx7xda-2 jQIOPj PaginationItem__contentBox"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="18"
              viewBox="0 0 30 30"
            >
              <path
                d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </button>
      </li>
    </ul>
  </nav>
</div>
`;
