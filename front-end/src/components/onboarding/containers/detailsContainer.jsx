import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { reduxForm, getFormValues } from 'redux-form';
import React from 'react';
import PropTypes from 'prop-types';

import {
  initializeContainerForm,
  populateContainerDetails,
  saveContainerValues,
} from '../../../store/actions/containers';
import { getContentTypes } from '../../../store/actions/content';

import ContainerDetails from './details';
import { setBrowserTitle, scrollToTop, mapObjectToArray } from '../../../constants';
import { createIdFromName } from '../../../utils';

export class ContainerDetailsContainer extends React.PureComponent {
  static propTypes = {
    isLoading: PropTypes.bool.isRequired,
    applications: PropTypes.array.isRequired,
    change: PropTypes.func.isRequired,
    containers: PropTypes.object,
    pages: PropTypes.object,
    match: PropTypes.shape({
      params: PropTypes.shape({
        id: PropTypes.string,
      }).isRequired,
    }).isRequired,
    history: PropTypes.shape({
      goBack: PropTypes.func.isRequired,
      push: PropTypes.func.isRequired,
    }).isRequired,
    initializeContainerForm: PropTypes.func.isRequired,
    saveContainerValues: PropTypes.func.isRequired,
    ruleTypes: PropTypes.array.isRequired,
    contentTypes: PropTypes.object.isRequired,
    populateContainerDetails: PropTypes.func.isRequired,
    handleSubmit: PropTypes.func.isRequired,
    touch: PropTypes.func.isRequired,
    untouch: PropTypes.func.isRequired,
    dirty: PropTypes.bool,
    formValues: PropTypes.object.isRequired,
    getContentTypes: PropTypes.func,
    authenticated: PropTypes.object,
    valid: PropTypes.bool,
  };

  componentDidMount() {
    this.props.initializeContainerForm(this.containerID);
    setBrowserTitle(this.title);
  }

  componentDidUpdate(prevProps) {
    if ((prevProps.formValues?.application !== this.props.formValues?.application) && this.props.applications) {
      const app = this.props.applications.find(a => a.id === +this.props.formValues.application);
      const space = app?.contentful_space;
      this.props.getContentTypes(space);
    }
  }

  get containerID() {
    return this.props.match.params.id;
  }

  get title() {
    return this.containerID ? 'Edit Container' : 'Create Container';
  }

  goBack = () => {
    this.props.history.push('/containers');
  };

  formatValues = values => ({
    name: (values?.name || '').trim(),
    containerId: (values?.containerId || '').trim(),
    application: parseInt(values.application),
    rule_type: values.rule_type,
    content_type: JSON.stringify(values?.content_type?.length > 0 ? values.content_type : []),
    status: values.status,
    description: (values?.description || '').trim(),
    pages: values.pages,
    campaign_limit: (values.campaign_limit && !isNaN(values.campaign_limit)) ? parseInt(values.campaign_limit, 10) : null,
  });

  createContainerId = (e) => {
    if (!parseInt(this.containerID)) {
      const containerId = createIdFromName(e.target.value);
      this.props.change('containerId', containerId);
    }
  }

  submit = this.props.handleSubmit(async values => {
    if (!values.content_type.length || (!values.pages.length && values.rule_type !== 'alert')) {
      return;
    }
    const container = mapObjectToArray(this.props.containers || {}).find(_ => _.id === parseInt(this.containerID));
    let shouldDisplayActivateSnackBar = false;
    if (container) {
      shouldDisplayActivateSnackBar = container && container.status !== values.status;
    }
    await this.props.saveContainerValues(this.containerID, this.formatValues(values), shouldDisplayActivateSnackBar);
    scrollToTop();
    this.props.history.push('/containers');
  });

  render() {
    return (
      <ContainerDetails
        isLoading={this.props.isLoading}
        title={this.title}
        onSubmit={this.submit}
        onCancel={this.goBack}
        pages={this.props.pages}
        applications={this.props.applications}
        ruleTypes={this.props.ruleTypes}
        containers={this.props.containers}
        contentTypes={this.props.contentTypes}
        formValues={this.props.formValues}
        isDirty={this.props.dirty}
        createContainerId={this.createContainerId}
        changeField={this.props.change}
        untouch={this.props.untouch}
        id={this.containerID ? parseInt(this.containerID) : null}
        authenticated={this.props.authenticated}
        valid={this.props.valid}
      />
    );
  }
}

const mapDispatchToProps = dispatch => bindActionCreators({
  saveContainerValues,
  populateContainerDetails,
  initializeContainerForm,
  getContentTypes,
}, dispatch);

const mapContainerStateToProps = state => ({
  pages: state.pages?.items || {},
  applications: state.applications?.items || {},
  contentTypes: state.content?.types || {},
  containers: state.containers?.items || {},
  ruleTypes: state.ruleTypes?.items || [],
  isLoading: [ 'pages', 'applications', 'contentTypes', 'containers', 'ruleTypes' ].some(key => state[key]?.isLoading),
  formValues: getFormValues('containerDetails')(state) || {},
  authenticated: state.authenticated || {},
});

@connect(mapContainerStateToProps, mapDispatchToProps)
@reduxForm({
  form: 'containerDetails',
})
export default class ContainerDetailsConnected extends ContainerDetailsContainer { }
