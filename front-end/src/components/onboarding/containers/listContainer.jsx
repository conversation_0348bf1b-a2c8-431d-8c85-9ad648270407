import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { connect, useDispatch, useSelector } from 'react-redux';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import { useHistory } from 'react-router-dom';
import { omit, difference } from 'lodash';
import qs from 'qs';

import AlertBanner from 'canvas-core-react/lib/AlertBanner';
import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';
import ModalAlert from 'canvas-core-react/lib/ModalAlert';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';

import List from '../../listing/list';
import PageActions from '../../pageActions';
import AdminFilter from '../../listing/AdminFilter';
import { debounce, removeFalsyKeys } from '../../../utils';
import { getApplications } from '../../../store/actions/applications';
import { getContainers, setContainerActivation } from '../../../store/actions/containers';
import { getPages } from '../../../store/actions/pages';
import { tableColumns } from './containersColumns';

const PAGE_SIZE = 30;
const BASE_QUERY = {
  application_id: '',
  page_id: '',
  status: '',
  search: '',
  sort: '',
  limit: PAGE_SIZE,
  pageNumber: 1,
};

const ContainerList = ({
  authenticated,
  isLoading,
  containers,
  applications,
  pages,
  setContainerActivation,
}) => {
  const history = useHistory();
  const dispatch = useDispatch();
  const perms = authenticated.permissions;
  const isPagesLoading = useSelector(state => state.pages?.isLoading);
  const isApplicationsLoading = useSelector(state => state.applications?.isLoading);
  const pagination = useSelector(state => state.containers?.pagination);
  const defaultFilterValues = qs.parse(history.location.search, { ignoreQueryPrefix: true });

  const [ filters, setFilters ] = useState({
    ...BASE_QUERY,
    ...omit(defaultFilterValues, difference(Object.keys(defaultFilterValues), Object.keys(BASE_QUERY))),
  });
  const [ isModalVisible, setIsModalVisible ] = useState(false);
  const [ deactivationModalVisible, setDeactivationModalVisible ] = useState(false);
  const [ reactivationModalVisible, setReactivationModalVisible ] = useState(false);
  const [ containerForAction, setContainerForAction ] = useState(null);

  const { canEditAllContainers, canEditOwnTeamContainers, canViewOwnTeamContainers, canViewAllContainers } =
    useMemo(() => authenticated.permissionLevels || {}, [ authenticated ]);

  // limit results to current team if lacking view super permissions
  const teamFilter = permRequired => perms[permRequired] ? {} : { team_id: authenticated.team_id };

  useEffect(() => {
    dispatch(getContainers(({ ...removeFalsyKeys(filters), ...teamFilter('containers_view_super') })));
    dispatch(getApplications({ ...teamFilter('containers_view_super'), flag: 'baseFields' }));
    dispatch(getPages(teamFilter('containers_view_super')));
  }, []);

  useEffect(() => {
    const queryParams = removeFalsyKeys(omit(filters, [ 'limit', ...(filters.pageNumber === 1 ? [ 'pageNumber' ] : []) ]));
    history.push({ search: qs.stringify(queryParams, { addQueryPrefix: true }) });
  }, [ filters ]);

  const fetchContainers = (newFilters) => {
    dispatch(getContainers(removeFalsyKeys(newFilters)));
  };

  const debounceFetchContainers = useCallback(debounce(fetchContainers, 500), []);

  const handleOnChangeSearch = (e) => {
    const search = e.target.value;
    debounceFetchContainers({ ...filters, search, pageNumber: 1 });
    setFilters((f) => ({ ...f, search, pageNumber: 1 }));
  };

  const filtersChanged = (newFilters) => {
    const applicationFilterChanged = filters.application_id && newFilters.application_id !== filters.application_id;
    const newQuery = {
      ...newFilters,
      ...(applicationFilterChanged && { page_id: '' }),
    };
    setFilters(newQuery);
    dispatch(getContainers(removeFalsyKeys(newQuery)));
  };

  const handleClearFilters = () => {
    const newQuery = {
      ...BASE_QUERY,
      ...(!canViewAllContainers && {
        teamId: authenticated.team_id,
      }),
    };
    setFilters(newQuery);
    dispatch(getContainers(removeFalsyKeys(newQuery)));
  };

  const initialSortDirection = (columnKey) => {
    const { sort } = filters;
    if (!filters.sort) {
      return 0;
    }
    // check if the sorted key has a - in front of it which would indicate it's in descending order, ascending order otherwise
    if (sort.includes(columnKey)) {
      return (sort.charAt(0) === '-') ? 1 : -1;
    }
    return 0;
  };

  const onColumnSort = (columnKey, currentFilters) =>
    (row, direction) => {
      filtersChanged({ ...currentFilters, pageNumber: 1, sort: `${direction > 0 ? '-' : ''}${columnKey}` });
  };

  const sortableColumnProperties = useCallback((columnKey, currentFilters) => ({
    sortable: true,
    overrideSortBehaviour: onColumnSort(columnKey, currentFilters),
    initialSortDirection: initialSortDirection(columnKey),
  }), []);

  const handleChangeContainerStatus = (e, row) => {
    const parentApplication = applications.find(({ id }) => id === row.application);
    if (e.target.checked && !parentApplication.status) {
      setIsModalVisible(true);
    } else {
      setContainerForAction(row.id);
      if (!e.target.checked) {
        setDeactivationModalVisible(true);
      } else {
        setReactivationModalVisible(true);
        // setContainerActivation(row.id, e.target.checked, canViewAllContainers ? filters : { ...filters, team_id: authenticated.team_id });
      }
    }
  };

  if (!canViewOwnTeamContainers) {
    return (<AlertBanner type="error">You do not have permissions to view this page</AlertBanner>);
  };

  return (
    <>
      <List
        isLoading={isLoading}
        className="container-list"
        entityName="container"
        columns={tableColumns({
          canEditAllContainers,
          canEditOwnTeamContainers,
          containers,
          applications,
          handleChangeContainerStatus,
          authenticated,
          pages,
          filters,
          sortableColumnProperties,
        })}
        data={containers}
        permissions={perms}
        columnFixed={false}
        resetSortOnDataChange={false}
      >
        <PageActions
          onChange={handleOnChangeSearch}
          value={filters.search}
          filterButtonText="Filter"
        >
          <AdminFilter
            defaultFilterValues={defaultFilterValues}
            onChange={filtersChanged}
            onClearClick={handleClearFilters}
            filterValues={filters}
            fields={[
              {
                label: 'Application',
                key: 'application_id',
                options: applications.map((app) => ({
                  value: app.id,
                  label: app.name,
                })).sort((a, b) => a.label.localeCompare(b.label)),
                isLoading: isApplicationsLoading,
              },
              {
                label: 'Page',
                key: 'page_id',
                options: filters.application_id ? pages.filter(page => page.application === parseInt(filters.application_id)).map(page => ({
                  label: page.name,
                  value: page.id,
                })).sort((a, b) => a.label.localeCompare(b.label)) : [],
                isLoading: isPagesLoading,
              },
              {
                label: 'Status',
                key: 'status',
                options: [
                  { value: true, label: 'Active' },
                  { value: false, label: 'Inactive' },
                ],
              },
            ]}
          />
        </PageActions>
      </List>

      { !isLoading && containers?.length > 0 &&
        <DesktopPagination
          id="containers-pagination"
          totalResultCount={pagination?.total || 0}
          onChange={(pageNumber) => {
            filtersChanged({ ...filters, pageNumber });
            window.scrollTo(0, 0);
          }}
          firstButtonLabel="First"
          prevButtonLabel="Previous"
          nextButtonLabel="Next"
          lastButtonLabel="Last"
          navigationLabel="Pagination Navigation"
          pageSize={pagination?.limit || 0}
          currentPage={containers ? (pagination?.offset / pagination?.limit) + 1 : 1}
          containerType="card"
        />
      }

      { /* Modals */ }
      <ModalAlert
        isModalVisible={isModalVisible}
        headline="Cannot activate this container"
        primaryButtonLabel="Close"
        primaryAction={() => setIsModalVisible(false)}
        setModalVisible={() => setIsModalVisible(false)}
      >
        Application must be active in order to activate this container
      </ModalAlert>
      <ModalDialogue
        isModalVisible={deactivationModalVisible}
        headline="Are you sure you want to deactivate this container?"
        primaryButtonLabel="Deactivate Container"
        primaryAction={() => {
          setContainerActivation(containerForAction, false, canViewAllContainers ? filters : { ...filters, team_id: authenticated.team_id });
          setDeactivationModalVisible(false);
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setDeactivationModalVisible(false)}
        setModalVisible={() => setDeactivationModalVisible(false)}
      >
        Users will no longer be able to create new campaigns or alerts for this container.
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={reactivationModalVisible}
        headline="Are you sure you want to reactivate this container?"
        primaryButtonLabel="Reactivate Container"
        primaryAction={() => {
          setContainerActivation(containerForAction, true, canViewAllContainers ? filters : { ...filters, team_id: authenticated.team_id });
          setReactivationModalVisible(false);
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setReactivationModalVisible(false)}
        setModalVisible={() => setReactivationModalVisible(false)}
      >
        User will be able to create new campaigns or alerts again for this container.
      </ModalDialogue>
    </>
  );
};

ContainerList.propTypes = {
  isLoading: PropTypes.bool.isRequired,
  authenticated: PropTypes.object,
  containers: PropTypes.array.isRequired,
  pages: PropTypes.array.isRequired,
  applications: PropTypes.array.isRequired,
  setContainerActivation: PropTypes.func.isRequired,
};

ContainerList.defaultProps = {
  isLoading: true,
  applications: [],
  pages: [],
  containers: [],
};

const mapStateToProps = state => {
  return {
    isLoading: (
      state.containers?.isLoading ||
      state.applications?.isLoading ||
      state.pages?.isLoading ||
      !state.containers?.items
    ),
    applications: state.applications?.items,
    authenticated: state.authenticated,
    containers: state.containers?.items,
    pages: state.pages?.items,
  };
};

const mapDispatchToProps = dispatch => bindActionCreators({
  setContainerActivation,
}, dispatch);

export { ContainerList };
export default connect(mapStateToProps, mapDispatchToProps)(ContainerList);
