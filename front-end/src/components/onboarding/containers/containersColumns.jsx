
import React from 'react';
import { Link } from 'react-router-dom';
import classnames from 'classnames';

import CanvasLink from 'canvas-core-react/lib/Link';
import Tooltip from 'canvas-core-react/lib/Tooltip';
import TextBody from 'canvas-core-react/lib/TextBody';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import ToggleSwitch from 'canvas-core-react/lib/ToggleSwitch';

const renderLink = (row, index) => (
  <>
    <CanvasLink
      className={
        classnames(
          'admin-list__name-link',
          { 'admin-list__name-link--disabled': !row.status }
        )
      }
      href=""
      component={Link}
      type="emphasis"
      to={`/containers/${row.id}`}
    >
      { row.name }
    </CanvasLink>
    {
      row?.description && (
        <Tooltip
          position={index < 2 ? 'bottom' : 'top'}
          id={`tooltip-${row?.id}-${row?.description}`}
          closeButtonLabel="Close Tooltip"
          infoButtonLabel="Info"
          heading={row?.name}
          iconColor="blue"
        >
          <TextBody component="p">{ row?.description }</TextBody>
        </Tooltip>
      )
    }
  </>
);

export const tableColumns = ({
  canEditAllContainers,
  canEditOwnTeamContainers,
  containers,
  applications,
  handleChangeContainerStatus,
  authenticated,
  pages,
  filters,
  sortableColumnProperties,
}) => {
  const columns = [
    {
      name: 'Container Name',
      cellFormatter: row => {
        const index = containers.findIndex(el => el.id === row.id);
        return renderLink(row, index);
      },
      selector: 'name',
      grow: 2,
      style: { textAlign: 'left' },
      ...sortableColumnProperties('name', filters),
    },
    {
      name: 'ID',
      cellFormatter: row => (
        <TextCaption color={row.status ? 'black' : 'gray'} component="p">
          { row.containerId }
        </TextCaption>
      ),
      selector: 'containerId',
      grow: 2,
      ...sortableColumnProperties('containerId', filters),
    },
    {
      name: 'Rule Type',
      cellFormatter: row => (
        <TextCaption color={row.status ? 'black' : 'gray'} component="p">{ row['rule_type'] }</TextCaption>
      ),
      selector: 'rule_type',
      ...sortableColumnProperties('rule_type', filters),
    },
    {
      name: 'Associated Pages',
      grow: 2,
      cellFormatter: row => (
        <div className="admin-list__list-cell">
          {
            row.pages.map((page, index) => (
              <TextCaption
                color={row.status ? 'black' : 'gray'}
                className="container-list__cell-item"
                key={index}
                component="p"
              >
                { pages?.find(({ id }) => id === page)?.name }
              </TextCaption>
            ))
          }
        </div>
      ),
      selector: '',
    },
    {
      name: 'Application',
      cellFormatter: row => (
        <TextCaption color={row.status ? 'black' : 'gray'} component="p">
          { applications.find(({ id }) => id === row.application)?.name }
        </TextCaption>
      ),
      selector: '',
      ...sortableColumnProperties('applicationId', filters),
    },
    {
      name: 'Status',
      cellFormatter: row => {
        const isOwnTeamsApp = applications.find(({ id }) => id === row.application)?.team_id === authenticated.team_id;
        return (<ToggleSwitch
          id={`status-toggle-${row.id}`}
          data-testid={`status-toggle-${row.id}`}
          name={`status-toggle-${row.id}`}
          className="admin-list__toggle-switch"
          label=""
          checked={row.status}
          onChange={e => handleChangeContainerStatus(e, row)}
          disabled={!(canEditAllContainers || (canEditOwnTeamContainers && isOwnTeamsApp))}
        />);
      },
      selector: 'status',
      tooltip: 'An active container can be used by campaigns and alerts. An inactive container cannot be used by campaigns and alerts. By default, if a container belongs to an inactive application, the container will also be inactive.',
    },
  ];
  return columns;
};
