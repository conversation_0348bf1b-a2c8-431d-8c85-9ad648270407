import React from 'react';
import PropTypes from 'prop-types';
import { combineReducers, createStore } from 'redux';
import { reducer as formReducer, reduxForm } from 'redux-form';
import { Provider } from 'react-redux';
import { fireEvent, render } from '@testing-library/react';

import { ContainerDetailsContainer } from './detailsContainer';

const applications = [
  {
    'id': 1,
    'name': 'SOL',
    'applicationId': 'sol',
    'rule_version': 2,
    'description': null,
    'status': true,
    'ruleTypes': [ 1 ],
    'team_id': 1,
  },
  {
    'id': 2,
    'name': 'Nova Mobile',
    'applicationId': 'nova',
    'rule_version': 2,
    'description': null,
    'status': true,
    'ruleTypes': [ 1, 2 ],
    'team_id': 1,
  },
];

const containers = {
  1: {
    'id': 1,
    'name': 'My Activity',
    'containerId': 'my-activity',
    'application': 1,
    'description': null,
  },
  2: {
    'id': 2,
    'name': 'Offers and Programs',
    'containerId': 'offers-and-programs',
    'application': 1,
    'description': null,
  },
};

const pages = {
  1: {
    'id': 1,
    'name': 'Accounts',
    'pageId': 'accounts',
    'application': 1,
    'containers': [ 1, 2 ],
    'description': 'Accounts Page Description',
  },
};

const ruleTypes = [
  { 'id': 1, 'rule_type': 'alert', 'slug': 'alert' },
  { 'id': 2, 'rule_type': 'campaign', 'slug': 'campaign' },
  { 'id': 3, 'rule_type': 'vignette', 'slug': 'vignette' },
];

const contentTypes = {
  'dog': { id: 'dog', name: 'Dog Content Model', updated_at: '2020-11-06T18:20:26.704Z' },
  'cat': { id: 'cat', name: 'Cat Content Model', updated_at: '2020-11-06T18:20:26.704Z' },
  'car': { id: 'car', name: 'Car Content Model', updated_at: '2020-11-06T18:20:26.704Z' },
};

const values = {
  name: 'Alerts',
  containerId: 'alert',
  application: 1,
  rule_type: 'alert',
  content_type: '^alert$',
  status: true,
  description: 'Alerts',
  pages: {},
};

describe('ContainerDetailsContainer', () => {
  const handleSubmit = jest.fn().mockImplementation(() => jest.fn());
  const containerProps = {
    handleSubmit,
    onCancel: jest.fn(),
    onSubmit: jest.fn(),
    onDelete: jest.fn(),
    checkboxOnClick: jest.fn(),
    createContainerId: jest.fn(),
    updateDetails: jest.fn(),
    pages,
    applications,
    ruleTypes,
    containers,
    isDirty: false,
    formValues: values,
    isLoading: false,
    contentTypes,
    changeField: jest.fn(),
    history: {
      goBack: jest.fn(),
      push: jest.fn(),
    },
    initializeContainerForm: jest.fn(),
    populateContainerDetails: jest.fn(),
    deleteContainer: jest.fn(),
    saveContainerValues: jest.fn(),
    change: jest.fn(),
    match: {
      params: {},
    },
    authenticated: {
      permissions: { 'containers_manage_super': true },
    },
  };
  const Container = ({ initialState = {}, ...props }) => {
    const store = createStore(combineReducers({ form: formReducer }), initialState);
    const ReduxContainerDetails = reduxForm({ form: 'containerDetails' })(ContainerDetailsContainer);

    return (
      <Provider store={store}>
        <ReduxContainerDetails {...containerProps} {...props} />
      </Provider>
    );
  };
  Container.propTypes = {
    initialState: PropTypes.object,
  };

  test('Fields only rendered when isLoading=false', () => {
    const { queryByText, queryAllByText, rerender } = render(<Container isLoading={true} />);
    expect(queryByText('Container Details')).not.toBeInTheDocument();
    rerender(<Container isLoading={false} />);
    expect(queryByText('Container Details')).toBeInTheDocument();
    expect(queryAllByText('Create Container')).toHaveLength(2); // 1 - Page title, 2 - button
  });

  test('Should display correct title when editing', () => {
    containerProps.initializeContainerForm.mockClear();
    const { queryByText } = render(<Container match={{ params: { id: '1' } }} />);
    expect(queryByText('Edit Container')).toBeInTheDocument();
    expect(containerProps.initializeContainerForm).toHaveBeenCalled();
  });

  test('container name generates a relevant container-id when name is blurred', () => {
    const { getByPlaceholderText } = render(
      <Container
        formValues={{

        }}
      />
    );
    fireEvent.focus(getByPlaceholderText('Enter container name'));
    fireEvent.change(getByPlaceholderText('Enter container name'), { target: { value: 'sample container name' } });
    expect(getByPlaceholderText('Enter container ID')).toHaveValue('sample-container-name');
  });

  test('container submission', () => {
    const containerValues = {
      name: 'Fancy container',
      containerId: '1',
      application: 1,
      rule_type: 'campaign',
      content_type: [ 'alert' ],
      status: 'true',
      description: 'Super cool fancy container',
      pages: [ 1, 2 ],
    };

    const handleSubmit = jest.fn().mockImplementation(() => saveContainerValues);
    const saveContainerValues = jest.fn();
    const { getAllByText } = render(
      <Container
        handleSubmit={handleSubmit}
        saveContainerValues={saveContainerValues}
        initialState={{
          form: {
            containerDetails: {
              values: containerValues,
            },
          },
        }}
      />
    );
    fireEvent.click(getAllByText('Create Container')[1].closest('button'));
    const savedValues = { ...containerValues };
    delete savedValues.id;
    // check that saved values are what we expect
    expect(saveContainerValues).toHaveBeenCalled();
  });
});
