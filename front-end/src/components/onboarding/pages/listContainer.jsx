import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { connect, useDispatch, useSelector } from 'react-redux';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import { useHistory } from 'react-router-dom';
import { omit, difference } from 'lodash';
import qs from 'qs';

import AlertBanner from 'canvas-core-react/lib/AlertBanner';
import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';
import ModalAlert from 'canvas-core-react/lib/ModalAlert';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';

import List from '../../listing/list';
import PageActions from '../../pageActions';
import AdminFilter from '../../listing/AdminFilter';
import { mapObjectToArray } from '../../../constants';
import { debounce, removeFalsyKeys } from '../../../utils';
import { getPages, setPageActivation } from '../../../store/actions/pages';
import { getApplications } from '../../../store/actions/applications';
import { getContainers } from '../../../store/actions/containers';
import { tableColumns } from './pagesColumns';

const PAGE_SIZE = 30;
const BASE_QUERY = {
  application_id: '',
  container_id: '',
  status: '',
  search: '',
  sort: '',
  limit: PAGE_SIZE,
  pageNumber: 1,
};

export const PageList = ({
  authenticated,
  isLoading,
  containers,
  applicationMap,
  setPageActivation,
  pages,
}) => {
  const history = useHistory();
  const dispatch = useDispatch();
  const perms = authenticated.permissions;
  const isContainersLoading = useSelector(state => state.containers?.isLoading);
  const isApplicationsLoading = useSelector(state => state.applications?.isLoading);
  const pagination = useSelector(state => state.pages?.pagination);
  const defaultFilterValues = qs.parse(history.location.search, { ignoreQueryPrefix: true });

  const [ filters, setFilters ] = useState({
    ...BASE_QUERY,
    ...omit(defaultFilterValues, difference(Object.keys(defaultFilterValues), Object.keys(BASE_QUERY))),
  });
  const [ isModalVisible, setIsModalVisible ] = useState(false);
  const [ deactivationModalVisible, setDeactivationModalVisible ] = useState(false);
  const [ reactivationModalVisible, setReactivationModalVisible ] = useState(false);
  const [ pageForAction, setPageForAction ] = useState(null);

  const { canEditAllPages, canEditOwnTeamPages, canViewOwnTeamPages, canViewAllPages } =
    useMemo(() => authenticated.permissionLevels || {}, [ authenticated ]);

  const applications = useMemo(() => mapObjectToArray(applicationMap), [ applicationMap ]);

  // limit results to current team if lacking view super permissions
  const teamFilter = permRequired => perms[permRequired] ? {} : { team_id: authenticated.team_id };

  useEffect(() => {
    dispatch(getPages({ ...removeFalsyKeys(filters), ...teamFilter('pages_view_super') }));
    dispatch(getApplications({ ...teamFilter('pages_view_super'), flag: 'baseFields' }));
    dispatch(getContainers(teamFilter('pages_view_super')));
  }, []);

  useEffect(() => {
    const queryParams = removeFalsyKeys(omit(filters, [ 'limit', ...(filters.pageNumber === 1 ? [ 'pageNumber' ] : []) ]));
    history.push({ search: qs.stringify(queryParams, { addQueryPrefix: true }) });
  }, [ filters ]);

  const fetchPages = (newFilters) => {
    dispatch(getPages(removeFalsyKeys(newFilters)));
  };

  const debounceFetchPages = useCallback(debounce(fetchPages, 500), []);

  const handleOnChangeSearch = (e) => {
    const search = e.target.value;
    debounceFetchPages({ ...filters, search, pageNumber: 1 });
    setFilters((f) => ({ ...f, search, pageNumber: 1 }));
  };

  const filtersChanged = newFilters => {
    const applicationFilterChanged = filters.application_id && newFilters.application_id !== filters.application_id;
    const newQuery = {
      ...newFilters,
      ...(applicationFilterChanged && { container_id: '' }),
    };
    setFilters(newQuery);
    dispatch(getPages(removeFalsyKeys(newQuery)));
  };

  const handleClearFilters = () => {
    const newQuery = {
      ...BASE_QUERY,
      ...(!canViewAllPages && {
        team_id: authenticated.team_id,
      }),
    };
    setFilters(newQuery);
    dispatch(getPages(removeFalsyKeys(newQuery)));
  };

  const initialSortDirection = (columnKey) => {
    const { sort } = filters;
    if (!filters.sort) {
      return 0;
    }
    // check if the sorted key has a - in front of it which would indicate it's in descending order, ascending order otherwise
    if (sort.includes(columnKey)) {
      return (sort.charAt(0) === '-') ? 1 : -1;
    }
    return 0;
  };

  const onColumnSort = (columnKey, currentFilters) =>
    (row, direction) => {
      filtersChanged({ ...currentFilters, pageNumber: 1, sort: `${direction > 0 ? '-' : ''}${columnKey}` });
  };

  const sortableColumnProperties = useCallback((columnKey, currentFilters) => ({
    sortable: true,
    overrideSortBehaviour: onColumnSort(columnKey, currentFilters),
    initialSortDirection: initialSortDirection(columnKey),
  }), []);

  const handleChangePageStatus = (e, row) => {
    const parentApplication = applications.find(({ id }) => id === row.application);
    if (e.target.checked && !parentApplication.status) {
      setIsModalVisible(true);
    } else {
      setPageForAction(row.id);
      if (!e.target.checked) {
        setDeactivationModalVisible(true);
      } else {
        setReactivationModalVisible(true);
      }
    }
  };

  if (!canViewOwnTeamPages) {
    return (<AlertBanner type="error">You do not have permissions to view this page</AlertBanner>);
  };

  return (
    <>
      <List
        isLoading={isLoading}
        className="page-list"
        entityName="page"
        columns={tableColumns({
          canEditAllPages,
          canEditOwnTeamPages,
          applications,
          handleChangePageStatus,
          authenticated,
          containers,
          pages,
          filters,
          sortableColumnProperties,
        })}
        data={pages}
        permissions={perms}
        columnFixed={false}
        resetSortOnDataChange={false}
      >
        <PageActions
          onChange={handleOnChangeSearch}
          value={filters.search}
          filterButtonText="Filter"
        >
          <AdminFilter
            defaultFilterValues={defaultFilterValues}
            onChange={filtersChanged}
            onClearClick={handleClearFilters}
            filterValues={filters}
            fields={[
              {
                label: 'Application',
                key: 'application_id',
                options: applications.map((app) => ({
                  value: app.id,
                  label: app.name,
                })).sort((a, b) => a.label.localeCompare(b.label)),
                isLoading: isApplicationsLoading,
              },
              {
                label: 'Container',
                key: 'container_id',
                options: filters.application_id ? containers.filter(container => container.application === parseInt(filters.application_id)).map(container => ({
                  label: container.name,
                  value: container.id,
                })).sort((a, b) => a.label.localeCompare(b.label)) : [],
                isLoading: isContainersLoading,
              },
              {
                label: 'Status',
                key: 'status',
                options: [
                  { value: true, label: 'Active' },
                  { value: false, label: 'Inactive' },
                ],
              },
            ]}
          />
        </PageActions>
      </List>

      { pages?.length > 0 &&
        <DesktopPagination
          id="pages-pagination"
          totalResultCount={pagination?.total || 0}
          onChange={(pageNumber) => {
            filtersChanged({ ...filters, pageNumber });
            window.scrollTo(0, 0);
          }}
          firstButtonLabel="First"
          prevButtonLabel="Previous"
          nextButtonLabel="Next"
          lastButtonLabel="Last"
          navigationLabel="Pagination Navigation"
          pageSize={pagination?.limit || 0}
          currentPage={pages ? (pagination?.offset / pagination?.limit) + 1 : 1}
          containerType="card"
        />
      }

      { /* Modals */ }
      <ModalAlert
        isModalVisible={isModalVisible}
        headline="Cannot activate this page"
        primaryButtonLabel="Close"
        primaryAction={() => setIsModalVisible(false)}
        setModalVisible={() => setIsModalVisible(false)}
      >
        Application must be active in order to activate this page
      </ModalAlert>
      <ModalDialogue
        isModalVisible={deactivationModalVisible}
        headline="Are you sure you want to deactivate this page?"
        primaryButtonLabel="Deactivate Page"
        primaryAction={() => { setPageActivation(pageForAction, canViewAllPages ? filters : { ...filters, team_id: authenticated.team_id }, false); setDeactivationModalVisible(false); }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setDeactivationModalVisible(false)}
        setModalVisible={() => setDeactivationModalVisible(false)}
      >
        User will not be able to create new campaigns or alerts for this page.
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={reactivationModalVisible}
        headline="Are you sure you want to reactivate this page?"
        primaryButtonLabel="Reactivate Page"
        primaryAction={() => { setPageActivation(pageForAction, canViewAllPages ? filters : { ...filters, team_id: authenticated.team_id }, true); setReactivationModalVisible(false); }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setReactivationModalVisible(false)}
        setModalVisible={() => setReactivationModalVisible(false)}
      >
        User will be able to create new campaigns or alerts again for this page.
      </ModalDialogue>
    </>
  );
};

PageList.propTypes = {
  authenticated: PropTypes.object,
  isLoading: PropTypes.bool.isRequired,
  containerMap: PropTypes.object.isRequired,
  containers: PropTypes.array.isRequired,
  setPageActivation: PropTypes.func.isRequired,
  pages: PropTypes.array.isRequired,
  applicationMap: PropTypes.object.isRequired,
};

PageList.defaultProps = {
  isLoading: true,
  applicationMap: {},
  pages: [],
  containers: [],
};

const mapStateToProps = state => {
  return {
    isLoading: (
      state.containers?.isLoading ||
      state.applications?.isLoading ||
      state.pages?.isLoading ||
      !state.containers?.items
    ),
    applicationMap: state.applications?.items,
    authenticated: state.authenticated,
    containers: mapObjectToArray(state.containers?.items),
    pages: mapObjectToArray(state.pages?.items),
  };
};

const mapDispatchToProps = dispatch => bindActionCreators({
  setPageActivation,
}, dispatch);

export default connect(mapStateToProps, mapDispatchToProps)(PageList);
