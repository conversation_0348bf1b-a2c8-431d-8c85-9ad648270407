import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { getFormValues, reduxForm } from 'redux-form';
import React from 'react';
import PropTypes from 'prop-types';

import { addSnackbar } from '../../../store/actions/snackbar';
import { initializePageForm, savePageValues } from '../../../store/actions/pages';
import { setBrowserTitle, scrollToTop } from '../../../constants';
import PageDetails from './details';
import { createIdFromName } from '../../../utils';

export class PageDetailsContainer extends React.PureComponent {
  static propTypes = {
    applications: PropTypes.object.isRequired,
    pages: PropTypes.object.isRequired,
    match: PropTypes.shape({
      params: PropTypes.shape({
        id: PropTypes.string,
      }).isRequired,
    }).isRequired,
    history: PropTypes.shape({
      goBack: PropTypes.func.isRequired,
      push: PropTypes.func.isRequired,
    }).isRequired,
    handleSubmit: PropTypes.func.isRequired,
    initializePageForm: PropTypes.func.isRequired,
    change: PropTypes.func.isRequired,
    savePageValues: PropTypes.func.isRequired,
    isLoading: PropTypes.bool.isRequired,
    dirty: PropTypes.bool.isRequired,
    formValues: PropTypes.object,
    addSnackbar: PropTypes.func.isRequired,
    authenticated: PropTypes.object,
    valid: PropTypes.bool,
  };

  async componentDidMount() {
    this.props.initializePageForm(this.pageID);
    setBrowserTitle(this.title);
  }

  get pageID() {
    return this.props.match.params.id;
  }

  get title() {
    return this.pageID ? 'Edit Page' : 'Create Page';
  }

  goBack = () => {
    this.props.history.push('/pages');
  };

  formatValues = values => {
    const payload = {
      application: parseInt(values.application),
      name: (values?.name || '').trim(),
      pageId: (values?.pageId || '').trim(),
      status: values.status,
      description: (values?.description || '').trim(),
    };
    return payload;
  };

  createPageId = () => {
    const vals = this.props.formValues;
    if (!vals.pageId && vals.name) {
      const pageId = createIdFromName(vals.name);
      this.props.change('pageId', pageId);
    }
  }

  submit = this.props.handleSubmit(async values => {
    await this.props.savePageValues(this.pageID, this.formatValues(values));
    scrollToTop();
    this.props.history.push('/pages');
  });

  render() {
    if (this.props.isLoading) {
      return null;
    }
    return (
      <PageDetails
        title={this.title}
        onSubmit={this.submit}
        onCancel={this.goBack}
        isDirty={this.props.dirty}
        createPageId={this.createPageId}
        applications={this.props.applications}
        pages={this.props.pages}
        id={this.pageID ? parseInt(this.pageID) : null}
        formValues={this.props.formValues}
        authenticated={this.props.authenticated}
        changeField={this.props.change}
        valid={this.props.valid}
      />
    );
  }
}

const mapStateToProps = state => {
  return {
    applications: state.applications?.items,
    pages: state.pages?.items || {},
    formValues: getFormValues('pageDetails')(state) || {},
    isLoading: state.pages?.isLoading || state.applications?.isLoading,
    authenticated: state.authenticated || {},
  };
};

const mapDispatchToProps = dispatch => bindActionCreators({
  initializePageForm,
  addSnackbar,
  savePageValues,
}, dispatch);

@connect(mapStateToProps, mapDispatchToProps)
@reduxForm({
  form: 'pageDetails',
})

export default class PageDetailsConnected extends PageDetailsContainer { }
