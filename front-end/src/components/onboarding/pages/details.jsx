import React, { useState, useCallback, useMemo, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { isEmpty } from 'lodash';

import Card from 'canvas-core-react/lib/Card';
import InputSelectField from '../../formFields/inputSelectField';
import InputTextField from '../../formFields/inputTextField';
import InputToggleField from '../../formFields/inputToggleField';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';

import { createIdFromName, normalizedStrCompare } from '../../../utils';
import { max, required, requiredSpecific, campaignName, id as validId } from '../../../utils/validation';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';

const reactivateTooltip = {
  heading: 'Cannot reactivate',
  body: 'The application that this page belongs to must be reactivated before this page can be reactivated.',
};

const validations = {
  requiredApplication: requiredSpecific('Application'),
  requiredName: requiredSpecific('Page name'),
  requiredID: requiredSpecific('Page ID'),
  requiredDescription: requiredSpecific('Page description'),
  max64: max(64),
  max200: max(200),
};

const PageDetails = ({
  id,
  pages,
  title,
  createPageId,
  authenticated,
  applications,
  // callback functions in detailsContainers
  onSubmit,
  onCancel,
  // redux form properties
  formValues,
  isDirty,
  changeField,
  valid,
}) => {
  const canEditAll = useMemo(() => !!authenticated.permissions['admin'] || !!authenticated.permissions['pages_manage_super'], [ authenticated ]);
  const canEdit = useMemo(() => canEditAll || !!authenticated.permissions['pages_manage'], [ authenticated ]);

  const [ isFormDisabled, setIsFormDisabled ] = useState(true);
  const [ openPopup, setOpenPopup ] = useState(null);
  const [ deactivationModalVisible, setDeactivationModalVisible ] = useState(false);
  const [ reactivationModalVisible, setReactivationModalVisible ] = useState(false);
  const [ previousStatus, setPreviousStatus ] = useState(false);
  const [ isInitialized, setIsInitialized ] = useState(false);

  useEffect(() => {
    if (!isInitialized && !isEmpty(formValues) && id) {
      setPreviousStatus(formValues.status);
      setIsInitialized(true);
    }
  }, [ formValues ]);

  const isApplicationDisabled = useMemo(() => {
    const app = Object.values(applications || {}).find(({ id }) => id === Number(formValues.application));
    return app && !app.status;
  }, [ applications, formValues.application ]);

  useEffect(() => {
    if (formValues.id) {
      // enabled if user has pages_manage_super permission, or if
      // user has pages_manage permission and their team owns the page's parent application
      const isOwnTeamsApp = Object.values(applications || {}).find(({ applicationId }) => applicationId === formValues.applicationId)?.team_id === authenticated.team_id;
      setIsFormDisabled(!(canEditAll || (canEdit && isOwnTeamsApp)));
    } else {
      // enabled if user has any pages_manage permission - other fields will be filtered accordingly
      setIsFormDisabled(!canEdit);
    }
  }, [ formValues, authenticated ]);

  const setPopupVisible = (popupKey, isVisible = true) => setOpenPopup(isVisible ? popupKey : null);

  const ensureUnique = useCallback((label, key) => value => {
    const itemIsNotUnique = Object.values(pages).some(c => {
      return c.id !== id && normalizedStrCompare(c[key], value);
    });

    if (itemIsNotUnique) {
      return `${label} needs to be unique.`;
    }
  }, [ pages, id ]);

  const ensureUniquePageId = useCallback(ensureUnique('Page ID', 'pageId'), [ pages ]);
  const ensureUniquePageName = useCallback(ensureUnique('Page Name', 'name'), [ pages ]);

  const applicationSelectChange = (event, newValue, previousValue) => {
    if (newValue !== previousValue) {
      // if new parent application is disabled, the page must be disabled as well
      const newApplication = Object.values(applications || []).find(a => a.id === Number(newValue));
      if (!newApplication.status && formValues.status) {
        changeField('status', false);
      } else if (newApplication.status && !formValues.status) {
        changeField('status', true);
      }
    }
  };

  const checkSelectedApplicationStatus = () => formValues.application && applications.find(app => Number(formValues.application) === app.id)?.status;

  const modalActivation = () => {
    if ((!formValues.status && previousStatus && checkSelectedApplicationStatus()) && valid && id) {
      setDeactivationModalVisible(true);
    } else if ((formValues.status && !previousStatus && checkSelectedApplicationStatus() && valid && id)) {
      setReactivationModalVisible(true);
    } else {
      onSubmit();
    }
  };

  return (
    <div className="admin-details">
      <form>
        <div className="admin-details__action-bar">
          <TextHeadline component="h1" className="admin-details__header">{ title }</TextHeadline>
        </div>
        <Card className="admin-details__card">
          <TextHeadline className="admin-details__sub-header" component="h2" size={21}>
            Page Information
          </TextHeadline>
          <Field
            className="admin-details__field"
            disabled={isFormDisabled || isApplicationDisabled}
            name="status"
            label=""
            secondaryLabel={`${formValues.status ? 'Active' : 'Inactive'}`}
            placeholder="Status&hellip;"
            component={InputToggleField}
            options={[
              { id: true, name: 'Active' },
              { id: false, name: 'Inactive' },
            ]}
            validate={required}
            tooltip={isApplicationDisabled && reactivateTooltip}
          />
          <Field
            className="admin-details__field"
            disabled={isFormDisabled}
            name="application"
            label="Application"
            placeholder="Select application"
            component={InputSelectField}
            options={Object.values(applications || [])
              .filter(({ team_id: teamId }) => (isFormDisabled || canEditAll) || (teamId === authenticated.team_id))
              .map(({ id, name, status }) => status ? ({ id, name }) : ({ id, name: `${name} (Inactive)` }))
            }
            onChange={applicationSelectChange}
            validate={[ validations.requiredApplication ]}
          />
          <Field
            className="admin-details__field"
            disabled={isFormDisabled}
            name="name"
            label="Page Name"
            placeholder="Enter page name"
            component={InputTextField}
            validate={[
              validations.max64,
              validations.requiredName,
              campaignName,
              ensureUniquePageName,
            ]}
            autoComplete="off"
            onBlur={createPageId}
            onChange={e => !id && changeField('pageId', createIdFromName(e.target.value))}
          />
          <Field
            className="admin-details__field"
            disabled
            name="pageId"
            label="Page ID"
            placeholder="Enter page ID"
            component={InputTextField}
            validate={[
              validations.requiredID,
              validations.max64,
              validId,
              ensureUniquePageId,
            ]}
            readOnly={!!id}
            autoComplete="off"
          />
          <Field
            className="admin-details__field"
            disabled={isFormDisabled}
            name="description"
            label="Page Description"
            placeholder="Enter page description"
            component={InputTextField}
            validate={[ validations.requiredDescription, validations.max200 ]}
            autoComplete="off"
          />
        </Card>

        <div className="admin-details__action-right-bar">
          <div className="admin-details__action-buttons">
            <SecondaryButton
              type="button"
              onClick={() => {
                isDirty ? setPopupVisible('cancel') : onCancel();
              }}
            >
              Cancel
            </SecondaryButton>
            <PrimaryButton
              type="button"
              className="admin-details__action-button"
              onClick={() => modalActivation()}
              disabled={isFormDisabled}
            >
              { id ? 'Update Page' : 'Create Page' }
            </PrimaryButton>
          </div>
        </div>
      </form>
      <ModalDialogue
        headline="Are you sure?"
        primaryButtonLabel="Yes"
        primaryAction={onCancel}
        secondaryButtonLabel="No"
        secondaryAction={() => setPopupVisible('cancel', false)}
        isModalVisible={openPopup === 'cancel'}
        setModalVisible={() => setPopupVisible('cancel', false)}
      >
        If you cancel, any unsaved changes will be lost
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={deactivationModalVisible}
        headline="Are you sure you want to deactivate this page?"
        primaryButtonLabel="Deactivate Page"
        primaryAction={() => { onSubmit(); setDeactivationModalVisible(false); }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setDeactivationModalVisible(false)}
        setModalVisible={() => {}}
      >
        User will not be able to create new campaigns or alerts for this page.
      </ModalDialogue>
      <ModalDialogue
        isModalVisible={reactivationModalVisible}
        headline="Are you sure you want to reactivate this page?"
        primaryButtonLabel="Reactivate Page"
        primaryAction={() => { onSubmit(); setReactivationModalVisible(false); }}
        secondaryButtonLabel="Cancel"
        secondaryAction={() => setReactivationModalVisible(false)}
        setModalVisible={() => setReactivationModalVisible(false)}
      >
        User will be able to create new campaigns or alerts again for this page.
      </ModalDialogue>
    </div>
  );
};

PageDetails.propTypes = {
  id: PropTypes.oneOfType([ PropTypes.string, PropTypes.number ]),
  pages: PropTypes.object.isRequired,
  title: PropTypes.string.isRequired,
  onSubmit: PropTypes.shape({
    then: PropTypes.func.isRequired,
    catch: PropTypes.func.isRequired,
 }).isRequired,
  onCancel: PropTypes.func.isRequired,
  isDirty: PropTypes.bool.isRequired,
  createPageId: PropTypes.func,
  applications: PropTypes.object.isRequired,
  authenticated: PropTypes.object,
  formValues: PropTypes.object,
  changeField: PropTypes.func.isRequired,
  valid: PropTypes.bool,
};

export default PageDetails;
