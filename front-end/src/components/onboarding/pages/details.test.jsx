import React from 'react';
import PropTypes from 'prop-types';
import { Provider } from 'react-redux';

import { reduxForm, reducer as formReducer } from 'redux-form';
import { render, fireEvent, screen } from '@testing-library/react';
import { PageDetailsContainer } from './detailsContainer';

import { createStore, combineReducers } from 'redux';

const applications = {
  1: {
    'id': 1,
    'name': 'SOL',
    'applicationId': 'sol',
    'rule_version': 2,
    'description': null,
    'status': true,
  },
  2: {
    'id': 2,
    'name': 'Nova Mobile',
    'applicationId': 'nova',
    'rule_version': 2,
    'description': null,
    'status': true,
  },
};

const pages = {
  1: {
    'id': 1,
    'name': 'activities',
    'pageId': 'activities',
    'description': null,
    'status': true,
  },
  2: {
    'id': 2,
    'name': 'accounts',
    'pageId': 'accounts',
    'description': null,
    'status': true,
  },
};

describe('PageDetails', () => {
  const commonProps = {
    history: {
      goBack: jest.fn(),
      push: jest.fn(),
    },
    match: { params: {} },
    location: { pathname: '' },
    getApplications: jest.fn(),
    initializePageForm: jest.fn(),
    handleSubmit: jest.fn(),
    change: jest.fn(),
    deletePage: jest.fn(),
    savePageValues: jest.fn(),
    isLoading: false,
    dirty: false,
    formValues: { name: 'pageDetails' },
    addSnackbar: jest.fn(),
    applications,
    pages,
    authenticated: {
      permissions: { 'pages_manage_super': true },
    },
    onSubmit: jest.fn(),
  };

  afterEach(() => {
    commonProps.deletePage.mockClear();
    commonProps.history.push.mockClear();
  });

  const Page = ({ initialState = {}, ...props }) => {
    const store = createStore(combineReducers({ form: formReducer }), initialState);
    const ReduxPageDetails = reduxForm({ form: 'pageDetails' })(PageDetailsContainer);

    return (
      <Provider store={store}>
        <ReduxPageDetails {...commonProps} {...props} />
      </Provider>
    );
  };

  Page.propTypes = {
    initialState: PropTypes.object,
  };

  test('nothing is returned if the component is loading', () => {
  render(<Page isLoading={true} handleSubmit={fn => fn({})}
    />);
    expect(screen.queryByText('Create Page')).not.toBeInTheDocument();
  });

  test('should render for empty applications', () => {
    const pageValues = {
      id: 1,
      application: 2,
      name: 'Activities Page',
      pageId: 'activities',
      status: true,
      description: 'An important page to be seen on Nova,\'s My Updates',
    };

    const handleSubmit = fn => fn(pageValues);
    const savePageValues = jest.fn();
    const { getByText } = render(
      <Page
        handleSubmit={handleSubmit}
        savePageValues={savePageValues}
        initialState={{
          form: {
            pageDetails: {
              values: pageValues,
            },
          },
          applications: null,
        }}
        formValues={
          { id: pageValues.id, status: 'Active' }
        }
        authenticated={{ permissions: [ 'pages_manag' ] }}
        applications={undefined}
        isDirty={true}
      />
    );

    expect(getByText('Page Information')).toBeInTheDocument();
  });

  describe('cancelling', () => {
    test('history.push called if form is not dirty', () => {
      const push = jest.fn();
      const { getByText } = render(
        <Page
          history={{
            push,
            goBack: jest.fn(),
          }}
          initializePageForm={jest.fn()}
          handleSubmit={fn => fn({})}
        />
      );
      fireEvent.click(getByText('Cancel'));
      expect(push).toHaveBeenCalledWith('/pages');
    });
  });

  describe('page title', () => {
    test('correct title when creating a new page', () => {
      const { queryAllByText } = render(<Page handleSubmit={fn => fn({})}
      />
      );
      expect(queryAllByText('Create Page')).toHaveLength(2); // 1 - Page title, 2 - button
    });

    test('correct title when editing a page', () => {
      const { queryByText } = render(
        <Page
          match={{
            params: {
              id: '1',
            },
          }}
          handleSubmit={fn => fn({})}
        />
      );
      expect(queryByText('Edit Page')).toBeInTheDocument();
    });
  });

  describe('page submission', () => {
    test('submission', () => {
      const pageValues = {
        id: 1,
        application: 2,
        name: 'Activities Page',
        pageId: 'activities',
        status: true,
        description: 'An important page to be seen on Nova,\'s My Updates',
      };

      const handleSubmit = fn => fn(pageValues);
      const savePageValues = jest.fn();
      const { getAllByText } = render(
        <Page
          handleSubmit={handleSubmit}
          savePageValues={savePageValues}
          initialState={{
            form: {
              pageDetails: {
                values: pageValues,
              },
            },
          }}
        />
      );
      fireEvent.submit(getAllByText('Create Page')[1].closest('button'));
      const savedValues = { ...pageValues };
      delete savedValues.id;
      // check that saved values are what we expect
      expect(savePageValues.mock.calls[0][1]).toStrictEqual(savedValues);
    });
  });

  test('page name generates a relevant page-id when name is blurred', () => {
    const { getByPlaceholderText } = render(
      <Page
        formValues={{
          name: 'sample page name',
        }}
        handleSubmit={fn => fn({})}
      />
    );
    fireEvent.focus(getByPlaceholderText('Enter page name'));
    fireEvent.blur(getByPlaceholderText('Enter page name'));
    expect(getByPlaceholderText('Enter page ID')).toHaveValue('sample-page-name');
  });

  describe('page edit submission', () => {
    test('edit', () => {
      const pageValues = {
        id: 1,
        application: 2,
        name: 'Activities Page',
        pageId: 'activities',
        status: true,
        description: 'An important page to be seen on Nova,\'s My Updates',
      };

      const handleSubmit = fn => fn(pageValues);
      const savePageValues = jest.fn();
      const { getAllByText } = render(
        <Page
          handleSubmit={handleSubmit}
          savePageValues={savePageValues}
          initialState={{
            form: {
              pageDetails: {
                values: pageValues,
              },
            },
          }}
          formValues={
            { id: pageValues.id, application: 1 }
          }
          authenticated={{ permissions: [ 'pages_manag' ] }}
          applications={{ 1: {
            id: 1,
            'status': false,
          } }}
        />
      );
      fireEvent.submit(getAllByText('Create Page')[1].closest('button'));
      const savedValues = { ...pageValues };
      delete savedValues.id;
      // check that saved values are what we expect
      expect(savePageValues.mock.calls[0][1]).toStrictEqual(savedValues);
    });
  });
});
