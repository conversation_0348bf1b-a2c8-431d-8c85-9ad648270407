import React from 'react';
import { Router } from 'react-router-dom';
import { createMemoryHistory } from 'history';
import { Provider } from 'react-redux';

import { render, fireEvent } from '@testing-library/react';
import { within } from '@testing-library/dom';
import { PageList } from './listContainer';
import { mapObjectToArray } from '../../../constants';

import { renderPage } from '../../../utils/testing-library-utils';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';

const mockApplications = [
  {
    id: 1,
    name: 'SOL',
    team_id: 1,
  },
  {
    id: 2,
    name: 'eExperience Storefront',
    team_id: 1,
  },
  {
    id: 3,
    name: 'Nova Mobile',
    team_id: 1,
    status: true,
  },
  {
    id: 4,
    name: 'Inactive application',
    team_id: 1,
    status: false,
  },
];

const mockPages = {
  1: {
    'id': 1,
    'application': 3,
    'name': 'activities',
    'pageId': 'activities',
    'status': true,
    'description': 'NOVA mobile - My Activity tab',
    'containers': [ 2 ],
  },
  2: {
    'id': 2,
    'application': 3,
    'name': 'accounts',
    'pageId': 'accounts',
    'status': false,
    'description': 'NOVA mobile - My Accounts tab',
    'containers': [ 2, 3, 4 ],
  },
  3: {
    'id': 3,
    'application': 4,
    'name': 'Test Page',
    'pageId': 'test-page',
    'status': false,
    'description': 'Just a lil test page',
    'containers': [],
  },
};

const mockContainers = {
  1: {
    'id': 1,
    'name': 'alert',
    'containerId': 'alert',
    'application': 3,
    'status': true,
    'description': 'Alert',
    'content_type': '^alert$',
    'rule_type': 'alert',
    'pages': [],
  },
  2: {
    'id': 2,
    'name': 'my-activity',
    'containerId': 'my-activity',
    'application': 3,
    'status': true,
    'description': 'My Activity',
    'content_type': '^standingCampaignTemplate\\d+$',
    'rule_type': 'campaign',
    'pages': [ 1, 2 ],
  },
  3: {
    'id': 3,
    'name': 'offers-and-programs',
    'containerId': 'offers-and-programs',
    'application': 3,
    'status': true,
    'description': 'Offers and Programs',
    'content_type': '^targetedCampaignTemplate\\d+$',
    'rule_type': 'campaign',
    'pages': [ 2 ],
  },
  4: {
    'id': 4,
    'name': 'priority-box',
    'containerId': 'priority-box',
    'application': 3,
    'status': true,
    'description': 'Priority Box',
    'content_type': null,
    'rule_type': 'campaign',
    'pages': [ 2 ],
  },
  5: {
    'id': 5,
    'name': 'storefront-container',
    'containerId': 'storefront-container',
    'application': 2,
    'status': false,
    'description': 'Storefront container',
    'content_type': null,
    'rule_type': 'storefront',
    'pages': [],
  },
};

const mockAuthenticated = {
  permissions: {
    pages_view_super: true,
    pages_manage_super: true,
  },
  permissionLevels: {
    canViewAllPages: true,
    canViewOwnTeamPages: true,
  },
  access: {
    campaigns: {
      applications: {
        view: [],
        manage: [],
      },
      pages: {
        view: [],
        manage: [],
      },
    },
    alerts: {
      applications: {
        view: [],
        manage: [],
      },
      pages: {
        view: [],
        manage: [],
      },
    },
  },
};

describe('PageList', () => {
  const loadPageList = jest.fn();
  const getPages = jest.fn();
  const mockSetPageActivation = jest.fn();
  const store = configureMockStore([ thunk ])({});

  const containerComponent = (
    <Router history={createMemoryHistory()}>
      <Provider store={store}>
        <PageList
          isLoading={false}
          loadPageList={loadPageList}
          getPages={getPages}
          applicationMap={mockApplications}
          containerMap={mockContainers}
          containers={mapObjectToArray(mockContainers)}
          pages={mapObjectToArray(mockPages)}
          authenticated={mockAuthenticated}
          setPageActivation={mockSetPageActivation}
        />
      </Provider>
    </Router>
  );

  beforeEach(() => {
    loadPageList.mockReset();
    getPages.mockReset();
  });

  it('renders containers', () => {
    const { container } = render(containerComponent);

    expect(container).toMatchSnapshot();
  });

  it('deactivate journey', async() => {
    const { getByText, getAllByText, getByRole } = await renderPage(containerComponent);
    const page = mockPages[1];
    // trigger deactivation warning
    const targetPage = getAllByText(page.name)[0];
    const statusToggle = within(targetPage.closest('tr')).getByTestId(`status-toggle-${page.id}`);
    expect(statusToggle.checked).toBeTruthy();
    fireEvent.click(statusToggle);
    fireEvent.click(getByRole('button', { name: 'Cancel' }));
    fireEvent.click(statusToggle);
    // due to known bug in canvas prior to 7.1.0, every time modal dialogue opens,
    // we must wait for focus animation to complete before closing the modal
    // https://bitbucket.agile.bns/projects/CANVAS/repos/core-react-develop/pull-requests/779/overview

    expect(getByText('Are you sure you want to deactivate this page?')).toBeInTheDocument();
    expect(getByText('User will not be able to create new campaigns or alerts for this page.')).toBeInTheDocument();
    fireEvent.click(getByRole('button', { name: 'Deactivate Page' }));
    expect(mockSetPageActivation).toHaveBeenCalledWith(page.id, { limit: 30, pageNumber: 1 }, false);
  });

  it('reactivate journey', async() => {
    const { getByText, getAllByText, getByRole } = await renderPage(containerComponent);
    const page = mockPages[2];
    // trigger deactivation warning
    const targetPage = getAllByText(page.name)[0];
    const statusToggle = within(targetPage.closest('tr')).getByTestId(`status-toggle-${page.id}`);
    expect(statusToggle.checked).not.toBeTruthy();
    fireEvent.click(statusToggle);
    fireEvent.click(getByRole('button', { name: 'Cancel' }));
    fireEvent.click(statusToggle);
    // due to known bug in canvas prior to 7.1.0, every time modal dialogue opens,
    // we must wait for focus animation to complete before closing the modal
    // https://bitbucket.agile.bns/projects/CANVAS/repos/core-react-develop/pull-requests/779/overview

    expect(getByText('Are you sure you want to reactivate this page?')).toBeInTheDocument();
    fireEvent.click(getByRole('button', { name: 'Reactivate Page' }));
    expect(mockSetPageActivation).toHaveBeenCalledWith(page.id, { limit: 30, pageNumber: 1 }, true);
  });

  it('cannot reactivate', async() => {
    const { getByText, getAllByText, getByRole } = await renderPage(containerComponent);
    const page = mockPages[3];
    // trigger deactivation warning
    const targetPage = getAllByText(page.name)[0];
    const statusToggle = within(targetPage.closest('tr')).getByTestId(`status-toggle-${page.id}`);
    expect(statusToggle.checked).not.toBeTruthy();
    fireEvent.click(statusToggle);

    // due to known bug in canvas prior to 7.1.0, every time modal dialogue opens,
    // we must wait for focus animation to complete before closing the modal
    // https://bitbucket.agile.bns/projects/CANVAS/repos/core-react-develop/pull-requests/779/overview
    expect(getByText('Cannot activate this page')).toBeInTheDocument();
    fireEvent.click(getByRole('button', { name: 'Close' }));
  });

  it('should render only selected application`s container', async() => {
    let mockApplication = mockApplications[2];
    const { getByText, queryByRole } = await renderPage(containerComponent);
    // open filter section
    const filterBtn = getByText('Filter');
    fireEvent.click(filterBtn);
    const filterSection = document.getElementsByClassName('admin-filter')[0];
    // select values
    fireEvent.change(within(filterSection).getByLabelText('Application'), { target: { value: mockApplication.id } });
    fireEvent.click(within(filterSection).getByText('Container'));
    expect(queryByRole('option', { name: 'my-activity' })).toBeInTheDocument(); // belong to Applications 3
    expect(queryByRole('option', { name: 'storefront-container' })).not.toBeInTheDocument(); // not belong to to Applications 3

    mockApplication = mockApplications[1];
    fireEvent.change(within(filterSection).getByLabelText('Application'), { target: { value: mockApplication.id } });
    fireEvent.click(within(filterSection).getByText('Container'));
    expect(queryByRole('option', { name: 'my-activity' })).not.toBeInTheDocument(); // belong to Applications 2
    expect(queryByRole('option', { name: 'storefront-container' })).toBeInTheDocument(); // not belong to to Applications 2
  });
});
