
import React from 'react';
import { Link } from 'react-router-dom';
import classnames from 'classnames';

import CanvasLink from 'canvas-core-react/lib/Link';
import Tooltip from 'canvas-core-react/lib/Tooltip';
import TextBody from 'canvas-core-react/lib/TextBody';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import ToggleSwitch from 'canvas-core-react/lib/ToggleSwitch';

const renderLink = (row, index) => (
  <>
    <CanvasLink
      className={
        classnames(
          'admin-list__name-link',
          { 'admin-list__name-link--disabled': !row.status }
        )
      }
      href=""
      component={Link}
      type="emphasis"
      to={`/pages/${row.id}`}
    >
      { row.name }
    </CanvasLink>
    {
      row?.description && (
        <Tooltip
          position={index < 2 ? 'bottom' : 'top'}
          id={`tooltip-${row?.id}-${row?.description}`}
          closeButtonLabel="Close Tooltip"
          infoButtonLabel="Info"
          heading={row?.name}
          iconColor="blue"
        >
          <TextBody component="p">{ row?.description }</TextBody>
        </Tooltip>
      )
    }
  </>
);

export const tableColumns = ({
  canEditAllPages,
  canEditOwnTeamPages,
  applications,
  handleChangePageStatus,
  authenticated,
  containers,
  pages,
  filters,
  sortableColumnProperties,
}) => {
  const columns = [
    {
      name: 'Page Name',
      cellFormatter: row => {
        const index = pages.findIndex(el => el.id === row.id);
        return renderLink(row, index);
      },
      selector: 'name',
      grow: 2,
      style: { textAlign: 'left' },
      ...sortableColumnProperties('name', filters),
    },
    {
      name: 'ID',
      cellFormatter: row => (
        <TextCaption color={row.status ? 'black' : 'gray'} component="p">
          { row.pageId }
        </TextCaption>
      ),
      selector: 'pageId',
      grow: 2,
      ...sortableColumnProperties('pageId', filters),
    },
    {
      name: 'Associated Containers',
      grow: 2,
      cellFormatter: row => (
        <div className="admin-list__list-cell">
          {
            row.containers.map(container => (
              <TextCaption
                color={row.status ? 'black' : 'gray'}
                className="page-list__cell-item"
                key={`container-${container}`}
                component="p"
              >
                { containers.find(({ id }) => id === container)?.name }
              </TextCaption>
            ))
          }
        </div>
      ),
      selector: '',
    },
    {
      name: 'Application',
      cellFormatter: row => (
        <TextCaption color={row.status ? 'black' : 'gray'} component="p">
            { applications.find(({ id }) => id === row.application)?.name }
        </TextCaption>
      ),
      selector: '',
      ...sortableColumnProperties('applicationId', filters),
    },
    {
      name: 'Status',
      cellFormatter: row => {
        const isOwnTeamsApp = applications.find(({ applicationId }) => applicationId === row.applicationId)?.team_id === authenticated.team_id;
        return (<ToggleSwitch
          id={`status-toggle-${row.id}`}
          data-testid={`status-toggle-${row.id}`}
          name={`status-toggle-${row.id}`}
          className="admin-list__toggle-switch"
          label=""
          checked={row.status}
          onChange={e => handleChangePageStatus(e, row)}
          disabled={!(canEditAllPages || (canEditOwnTeamPages && isOwnTeamsApp))}
        />);
      },
      selector: 'status',
      tooltip: 'An active page can be used by campaigns and alerts. An inactive page cannot be used by campaigns and alerts. By default, if a page belongs to an inactive application, the page will also be inactive.',
    },
  ];
  return columns;
};
