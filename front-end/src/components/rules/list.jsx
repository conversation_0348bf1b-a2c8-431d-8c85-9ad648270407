import React, { useEffect, useState, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import qs from 'qs';
import { cloneDeep, uniq, isEmpty, omit } from 'lodash';
import { useHistory, useLocation } from 'react-router-dom';
import classnames from 'classnames';

import ActionMenuListItem from 'canvas-core-react/lib/ActionMenuListItem';
import SideSheet from 'canvas-core-react/lib/SideSheet';
import CanvasLink from 'canvas-core-react/lib/Link';
import DesktopPagination from 'canvas-core-react/lib/DesktopPagination';
import IconCheck from 'canvas-core-react/lib/IconCheck';
import IconClose from 'canvas-core-react/lib/IconClose';
import IconDuplicate from '../../assets/svgs/IconDuplicate';
import IconDelete from 'canvas-core-react/lib/IconDelete';
import IconEdit from 'canvas-core-react/lib/IconEdit';
import IconFilter from 'canvas-core-react/lib/IconFilter';
import IconShow from 'canvas-core-react/lib/IconShow';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import Search from 'canvas-core-react/lib/Search';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import Table from 'canvas-core-react/lib/Table';
import Tabs from 'canvas-core-react/lib/Tabs';
import Tab from 'canvas-core-react/lib/Tab';
import TextBody from 'canvas-core-react/lib/TextBody';
import TextButton from 'canvas-core-react/lib/TextButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import TextLegal from 'canvas-core-react/lib/TextLegal';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';

import { tableColumns } from './ruleColumns';
import { ExportCampaignModal, ExportAlertModal } from '../modal/exportModal';
import { useRedirect } from '../../hooks/useRedirect';
import { canEditRule } from './utils';

import {
  DATE_ISO_FORMAT,
  formatWord,
  mapObjectPropertyToArray,
  openPreviewWindow,
  ruleSubTypesDisplayName,
  platformsUI,
  ruleTypes,
  ruleTypesDisplayName,
  STATUS,
  statusByType,
} from '../../constants';
import { TEAMS_FLAGS } from '../../constants/permissionsList';
import { debounce } from '../../utils';
import StatusBadge from '../core/statusBadge';
import AdminFilter from '../listing/AdminFilter';

const defaultFilter = {
  sort: '-updated_at',
};
const headerHeight = 72;

const RulesList = ({
  rules,
  pagination,
  type,
  content,
  access,
  permissions,
  isLoading,
  isSetActiveLoading,
  applications,
  containers,
  pages,
  users,
  ruleSubTypes,
  teams,
  getRules,
  getContentById,
  getRuleSubTypes,
  getApplications,
  getPages,
  getContainers,
  getTeams,
  setRuleActive,
  deleteRule,
}) => {
  const history = useHistory();
  const location = useLocation();

  const [ filteringOptionsCollapsed, setFilteringOptionsCollapsed ] = useState(true);
  const [ filters, setFilters ] = useState({ ...defaultFilter, ...qs.parse(location.search, { ignoreQueryPrefix: true }) });
  const [ activeTab, setActiveTab ] = useState(null);
  const [ startDateLowerLimit, setStartDateLowerLimit ] = useState(null);
  const [ startDateUpperLimit, setStartDateUpperLimit ] = useState(null);
  const [ endDateLowerLimit, setEndDateLowerLimit ] = useState(null);
  const [ endDateUpperLimit, setEndDateUpperLimit ] = useState(null);
  const [ idToDelete, setIdToDelete ] = useState(null); // if not null, the popup to delete this id will be shown
  const [ isSidePanelOpen, setIsSidePanelOpen ] = useState(false);
  const [ selectedRuleId, setSelectedRuleId ] = useState(null);
  const [ panelOffset, setPanelOffset ] = useState(headerHeight);
  const [ isActionListOpen, setIsActionListOpen ] = useState(false);

  const { redirectTo } = useRedirect(location.pathname.replace('/', ''));

  const sidMapping = useMemo(() => {
    if (!users?.items || !type) return {};
    if (type === ruleTypes.CAMPAIGN || type === ruleTypes.CCAU_CAMPAIGN) {
      return Object.values(users.items).reduce((o, i) => ({ ...o, [i.sid]: { name: i.name, teamId: i.team_id } }), {});
    }
    if (type === ruleTypes.ALERT) {
      return Object.values(users.items).reduce((o, i) => ({ ...o, [i.name]: { teamId: i.team_id } }), {});
    }
  }, [ users, type ]);
  const teamMapping = useMemo(
    () => teams?.items ? Object.values(teams.items).reduce((o, team) => ({ ...o, [team.id]: team.name }), {}) : {},
    [ teams ]
  );
  const tableData = useMemo(() => {
    if (!rules || !rules.length || !type) return [];
    if (type === ruleTypes.CAMPAIGN || type === ruleTypes.CCAU_CAMPAIGN) return rules;
    if (type === ruleTypes.ALERT) {
      return rules && rules.length
      ? rules.map(rule => {
          rule.created_by = `${rule.created_by} ${sidMapping[rule.created_by] ? '(' + teamMapping[sidMapping[rule.created_by].teamId] + ')' : ''}`;
          rule.updated_by = `${rule.updated_by} ${sidMapping[rule.updated_by] ? '(' + teamMapping[sidMapping[rule.updated_by].teamId] + ')' : ''}`;
          return rule;
        })
      : [];
    }
  }, [ sidMapping, teamMapping, rules, type ]);
  const canCreate = useMemo(() => canEditRule(access, permissions, type), [ access, permissions, type ]);

  // Converts front-end filters to respective rule-api request parameters
  const fetchRules = (newFilters, selectedTab = null, initialize = false) => {
    const ruleParams = { ...newFilters };
    const { status, start_date_gt: start, end_date_lt: end } = newFilters;
    if (((start && !moment(start, DATE_ISO_FORMAT, true).isValid()) || start === null) || ((end && !moment(end, DATE_ISO_FORMAT, true).isValid()) || end === null)) {
      return;
    }

    if (status && statusByType[type].published.includes(status)) {
      const date = new Date();
      const now = date.toISOString();
      if (status === STATUS.UPCOMING && (!start || start < now)) {
        ruleParams.start_date_gt = now;
        ruleParams.disabled = false;
      } else if (status === STATUS.EXPIRED && (!end || end > now)) {
        ruleParams.end_date_lt = now;
      } else if (status === STATUS.ACTIVE) {
        ruleParams.disabled = false;
        ruleParams.start_date_lt = now;
        ruleParams.end_date_gt = now;
      } else if (status === STATUS.INACTIVE) {
        ruleParams.disabled = true;
        ruleParams.end_date_gt = now;
      }
      ruleParams.status = STATUS.PUBLISHED;
    } else if (!status && selectedTab) {
      ruleParams.status = selectedTab;
    }
    if (!newFilters.sort) {
      ruleParams.sort = defaultFilter.sort;
    }

    getRules({ ...ruleParams, type }, initialize);
  };

  const debouncedFetchRules = useCallback(debounce(fetchRules, 500), [ type ]);

  // update rules whenever a filter changes
  useEffect(() => {
    if (redirectTo === '') return; // redirect logic hasn't completed
    // If user doesn't have view permissions to the route, redirect before fetching rules
    if (redirectTo && redirectTo !== location.pathname.replace('/', '')) {
      history.push(redirectTo);
      return;
    }

    runDateRangeValidations(filters);

    fetchRules(filters, activeTab, true);
    checkApplicationsContainersPages();

    if (type === ruleTypes.ALERT) {
      getTeams({ flag: TEAMS_FLAGS.SKIP_ALL });
    }

    const handleScroll = () => {
      setPanelOffset(window.scrollY < headerHeight ? headerHeight - scrollY : 0);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [ type, redirectTo ]);

  useEffect(() => {
    runDateRangeValidations(filters);
    const redirectedFrom = history.location.state?.from;
    if (redirectedFrom && redirectedFrom === 'message-centre') {
      toggleFilterSection();
    }
  }, [ history.location.state ]);

  const clearAllClicked = () => {
    setFilters(defaultFilter);
    runDateRangeValidations(defaultFilter, true);
    fetchRules({ ...defaultFilter, ...(activeTab && { status: activeTab }) });
    history.push({ search: '' });
  };

  // Determines selectable start/end dates in calendar based on start/end date & status
  const runDateRangeValidations = (newFilters, reset = false) => {
    const { status, start_date_gt: start, end_date_lt: end } = newFilters;

    // Initialize selectable date range - follows Canvas' default date range
    const minDate = moment().dayOfYear(1).year(moment().year() - 6);
    const maxDate = moment().dayOfYear(366).year(moment().year() + 5);
    if ((!startDateLowerLimit && !endDateLowerLimit && !startDateUpperLimit && !endDateUpperLimit) || reset) {
      setStartDateLowerLimit(minDate);
      setEndDateLowerLimit(minDate);
      setStartDateUpperLimit(maxDate);
      setEndDateUpperLimit(maxDate);
    }

    if (status && statusByType[type].published.includes(status)) {
      switch (status) {
        case STATUS.UPCOMING:
          setStartDateLowerLimit(moment());
          setStartDateUpperLimit(end ? moment(end) : maxDate);
          setEndDateLowerLimit(start ? moment(start) : moment());
          setEndDateUpperLimit(maxDate);
          break;
        case STATUS.EXPIRED:
          setStartDateLowerLimit(minDate);
          setStartDateUpperLimit(end ? moment(end) : moment().subtract(1, 'day'));
          setEndDateLowerLimit(start ? moment(start) : minDate);
          setEndDateUpperLimit(moment().subtract(1, 'day'));
          break;
        case STATUS.ACTIVE:
          setStartDateLowerLimit(minDate);
          setStartDateUpperLimit(moment());
          setEndDateLowerLimit(moment().subtract(1, 'day'));
          setEndDateUpperLimit(maxDate);
          break;
        case STATUS.INACTIVE:
          setStartDateLowerLimit(minDate);
          setStartDateUpperLimit(end ? moment(end) : maxDate);
          setEndDateLowerLimit(start ? moment(start) : moment().subtract(1, 'day'));
          setEndDateUpperLimit(maxDate);
          break;
      }
    } else if (!status || statusByType[type].pending.includes(status)) {
      setStartDateLowerLimit(minDate);
      setStartDateUpperLimit(end ? moment(end) : maxDate);
      setEndDateLowerLimit(start ? moment(start) : minDate);
      setEndDateUpperLimit(maxDate);
    }
  };

  // Determines selectable status' based on start/end date & tab
  const getStatusOptions = () => {
    let statusOptions = [];
    const date = new Date();
    const now = date.toISOString();
    if (!activeTab) {
      if (filters.start_date_gt > now) {
        // can't be expired
        statusOptions = [ ...statusByType[type].pending, STATUS.UPCOMING, STATUS.INACTIVE ];
      } else if (filters.end_date_lt < now) {
        // can't be upcoming
        statusOptions = [ ...statusByType[type].pending, STATUS.EXPIRED ];
      } else {
        statusOptions = [ ...statusByType[type].pending, ...statusByType[type].published ];
      }
    } else if (activeTab === STATUS.PUBLISHED) {
      if (filters.start_date_gt > now) {
        statusOptions = [ STATUS.UPCOMING ];
      } else if (filters.end_date_lt < now) {
        statusOptions = [ STATUS.EXPIRED ];
      } else {
        statusOptions = statusByType[type].published;
      }
    } else if (activeTab === STATUS.PENDING) {
      statusOptions = statusByType[type].pending;
    }

    const sortOrder = [ STATUS.ACTIVE, STATUS.INACTIVE, STATUS.UPCOMING, STATUS.EXPIRED, STATUS.DRAFT, STATUS.REVIEWED, STATUS.SUBMITTED ];
    const res = sortOrder.map(status => {
      if (statusOptions.includes(status)) {
        return status;
      }
    }).filter(status => status);
    return res;
  };

  const filtersChanged = (newFilters, debounce = false) => {
    let finalFilter = Object.keys(newFilters).reduce((acc, val) => {
      if (
        (val === 'pageNumber' && newFilters.pageNumber === 1) ||
        newFilters[val] === ''
      ) {
        delete acc[val];
        return acc;
      }

      acc[val] = newFilters[val];
      return acc;
    }, { ...filters });

    if (newFilters.application && filters.application !== newFilters.application) { // in case the user change the application filter so we should reset the page and pageNumber filter
      finalFilter = omit(finalFilter, [ 'container', 'page', 'status', 'start_date_gt', 'end_date_lt' ]);
    }
    // if a container is already selected and then a page is selected:
    //    a.if the container belongs to that page, nothing would happen in the drop-downs
    //    b.if the container doesn't belong to that page, the container drop-down will be cleared to select a container
    //    more details : https://jira.agile.bns/browse/PIGEON-4439
    if (newFilters.page && filters.page !== newFilters.page && filters.container) {
      const selectedPage = Object.values(cloneDeep(pages.items) || {}).find(p => p.pageId === newFilters.page);
      const selectedContainer = Object.values(cloneDeep(containers.items) || {}).find(p => p.containerId === filters.container && p.applicationId === filters.application);
      if (!selectedPage.containers.includes(selectedContainer.id)) {
        finalFilter = omit(finalFilter, [ 'container' ]);
      }
    }

    const { sort, ...qsParams } = finalFilter;
    runDateRangeValidations(qsParams);
    history.push({ search: qs.stringify({ sort, ...qsParams }, { addQueryPrefix: true }) });
    setFilters(finalFilter);
    if (debounce) {
      debouncedFetchRules(finalFilter, activeTab);
    } else {
      fetchRules(finalFilter, activeTab);
    }
  };

  const tabChanged = (tabIndex) => {
    const newFilters = { ...filters };
    switch (tabIndex) {
      case 0:
        if (activeTab) {
          setActiveTab(null);
          if (!filters.status) {
            delete newFilters.pageNumber;
            setFilters(newFilters);
            fetchRules(newFilters);
            history.push({ search: qs.stringify(newFilters, { addQueryPrefix: true }) });
          }
        }
        break;
      case 1:
        if (activeTab !== STATUS.PUBLISHED) {
          setActiveTab(STATUS.PUBLISHED);
          if (!filters.status || (filters.status && !statusByType[type].published.includes(filters.status))) {
            delete newFilters.status;
            delete newFilters.pageNumber;
            setFilters(newFilters);
            fetchRules({ ...newFilters, status: STATUS.PUBLISHED }, STATUS.PUBLISHED);
            history.push({ search: qs.stringify(newFilters, { addQueryPrefix: true }) });
          }
        }
        break;
      case 2:
        if (activeTab !== STATUS.PENDING) {
          setActiveTab(STATUS.PENDING);
          if (!filters.status || (filters.status && !statusByType[type].pending.includes(filters.status))) {
            delete newFilters.status;
            delete newFilters.pageNumber;
            runDateRangeValidations(newFilters);
            setFilters(newFilters);
            fetchRules({ ...newFilters, status: STATUS.PENDING }, STATUS.PENDING);
            history.push({ search: qs.stringify(newFilters, { addQueryPrefix: true }) });
          }
        }
        break;
      default:
        break;
    }
  };

  const onColumnSort = (columnKey, currentFilters) =>
    (row, direction) => filtersChanged({ ...currentFilters, pageNumber: 1, sort: `${direction > 0 ? '-' : ''}${columnKey}` });

  const initialSortDirection = columnKey => {
    const { sort } = filters;
    if (!filters.sort) {
      return 0;
    }

    // check if the sorted key has a - in front of it which would indicate it's in descending order, ascending order otherwise
    if (sort.includes(columnKey)) {
      return (sort.charAt(0) === '-') ? 1 : -1;
    }

    return 0;
  };

  const sortableColumnProperties = useCallback((columnKey, currentFilters) => ({
    sortable: true,
    overrideSortBehaviour: onColumnSort(columnKey, currentFilters),
    initialSortDirection: initialSortDirection(columnKey),
  }), [ type ]);

  const toggleFilterSection = () => {
    // Fetch applications, pages, & containers to populate dropdown options
    if (filteringOptionsCollapsed && (type === ruleTypes.CAMPAIGN || type === ruleTypes.CCAU_CAMPAIGN)) {
      checkApplicationsContainersPages();
      if (!ruleSubTypes.isLoading && isEmpty(ruleSubTypes.items)) {
        getRuleSubTypes();
      }
    }
    setFilteringOptionsCollapsed(!filteringOptionsCollapsed);
  };

  const renderFilterSection = () => {
    if (filteringOptionsCollapsed) {
      return;
    }

    // General
    const statusOptions = getStatusOptions();
    const statusFilter = {
      label: 'Status',
      options: statusOptions.map(status => ({
        label: formatWord(status, { capitalize: true }),
        value: status,
      })),
      defaultOptionLabel: 'Select Status',
    };

    let filterFields;

    if (type === ruleTypes.CAMPAIGN || type === ruleTypes.CCAU_CAMPAIGN) {
      // Campaigns - Only allow user to select a valid combination of application/container/page
      const accessApplications = uniq([ ...Object.keys(access.pages), ...Object.keys(access.containers), ...Object.keys(access.ruleSubTypes) ]);
      let applicationOptions = Object.values(applications.items || {})
        .filter(application => application.status && application.ruleTypes.includes(type) && accessApplications.includes(application.applicationId));
      const validApplicationIds = mapObjectPropertyToArray(applicationOptions, 'applicationId');

      const accessPages = [];
      Object.keys(access.pages).filter(applicationId => validApplicationIds.includes(applicationId))
        .forEach(applicationId => accessPages.push(...[ ...(access.pages[applicationId].view || []), ...(access.pages[applicationId].manage || []) ]));
      let pageOptions = Object.values(cloneDeep(pages.items) || {})
        .filter(p => p.status && validApplicationIds.includes(p.applicationId) && accessPages.includes(p.pageId) && p.applicationId === filters.application);

      const accessContainers = [];
      Object.keys(access.containers).filter(applicationId => validApplicationIds.includes(applicationId))
        .forEach(applicationId => accessContainers.push(...[ ...(access.containers[applicationId].view || []), ...(access.containers[applicationId].manage || []) ]));
      let containerOptions = Object.values(cloneDeep(containers.items) || {})
        .filter(c => c.status && c.rule_type === type && validApplicationIds.includes(c.applicationId) && accessContainers.includes(c.containerId) &&
        c.applicationId === filters.application);

      // Display all campaign types as active (i.e. not listed as deprecated) until an application is selected
      let activeRuleSubTypeIds = mapObjectPropertyToArray(ruleSubTypes.items, 'id');

      if (filters.application && applicationOptions.length) {
        activeRuleSubTypeIds = applicationOptions.find(application => application.applicationId === filters.application).ruleSubTypeIds;
      }

      if (filters.page && pageOptions.length) {
        const page = pageOptions.find(page => page.pageId === filters.page);
        containerOptions = containerOptions.filter(c => c.pages?.includes(page.id));
      };

      filterFields = [ {
        label: 'Campaign Types',
        key: 'external_ref',
        options: Object.values(ruleSubTypes.items || {})
          .map(subType => ({
            label: `${ruleSubTypesDisplayName[subType.type]} ${!activeRuleSubTypeIds.includes(subType.id) ? '(Deprecated)' : ''}`, // CCAU-TODO
            value: subType.type.toUpperCase(),
          })),
        defaultOptionLabel: 'Select Campaign Type',
        isLoading: ruleSubTypes.isLoading,
      },
      {
        label: 'Applications',
        key: 'application',
        options: applicationOptions.map(application => ({
          label: formatWord(application.name, { capitalize: true }),
          value: application.applicationId,
          disabled: application.disabled,
        })),
        defaultOptionLabel: 'Select Application',
        isLoading: applications.isLoading,
      },
      {
        label: 'Pages',
        key: 'page',
        options: pageOptions.map(page => ({
          label: formatWord(page.name, { capitalize: true }),
          value: page.pageId,
        })),
        defaultOptionLabel: 'Select Page',
        isLoading: pages.isLoading,
      },
      {
        label: 'Containers',
        key: 'container',
        options: containerOptions.map(container => ({
          label: formatWord(container.name, { capitalize: true }),
          value: container.containerId,
        })),
        defaultOptionLabel: 'Select Container',
        isLoading: containers.isLoading,
      },
      statusFilter,
      ];
    } else {
      // Alerts
      const accessApplications = uniq([ ...Object.keys(access.pages), ...Object.keys(access.containers) ]);
      let applicationOptions = Object.values(applications.items || {})
        .filter(application => application.status && application.ruleTypes.includes(type) && accessApplications.includes(application.applicationId));
      filterFields = [
        {
          label: 'Applications',
          key: 'application',
          options: applicationOptions.map(application => ({
            label: formatWord(application.name, { capitalize: true }),
            value: application.applicationId,
          })),
          defaultOptionLabel: 'Select Application',
        },
        {
          label: 'Created By',
          key: 'created_by',
          options: Object.values(users.items || {})
            .sort((a, b) => a.name.localeCompare(b.name))
            .map(user => ({
              label: user.name,
              value: user.sid,
            })),
          defaultOptionLabel: 'Select Created By',
        },
        {
          label: 'Last Updated By',
          key: 'updated_by',
          options: Object.values(users.items || {})
            .sort((a, b) => a.name.localeCompare(b.name))
            .map(user => ({
              label: user.name,
              value: user.sid,
            })),
          defaultOptionLabel: 'Select Last Updated By',
        },
        statusFilter,
      ];
    }

    return (
      <AdminFilter
        className="admin-list__filter-options"
        onChange={filtersChanged}
        filterValues={filters}
        onClearClick={clearAllClicked}
        defaultValue=""
        retainDefaultValue
        fields={filterFields}
        renderAsCard={false}
        includeDateTime
        startDateLowerLimit={startDateLowerLimit.toDate()}
        startDateUpperLimit={startDateUpperLimit.toDate()}
        endDateLowerLimit={endDateLowerLimit.toDate()}
        endDateUpperLimit={endDateUpperLimit.toDate()}
        includeCampaignIdFilter={type === ruleTypes.CAMPAIGN || type === ruleTypes.CCAU_CAMPAIGN}
      />
    );
  };

  const renderSidePanel = () => {
    if (!selectedRuleId || !isSidePanelOpen) {
      return;
    }
    const selectedRule = rules.find(rule => rule.id === selectedRuleId);
    if (!selectedRule) {
      return;
    }
    if ((type === ruleTypes.CAMPAIGN || type === ruleTypes.CCAU_CAMPAIGN) && !teams.isLoading && isEmpty(teams.items)) {
      getTeams({ flag: TEAMS_FLAGS.SKIP_ALL });
    }
    const { actionList } = getActionItems(selectedRule);
    // TODO: remove when https://jira.agile.bns/browse/CANVAS-2628 is implemented
    if (document.getElementsByClassName('BottomSheet__content')[0]) {
      document.getElementsByClassName('BottomSheet__content')[0].removeAttribute('tabIndex');
    }

    return (
      <SideSheet
        headline=""
        isSheetVisible={isSidePanelOpen}
        setSheetVisible={setIsSidePanelOpen}
        className={classnames('admin-list__sidepanel', { 'admin-list__panel-offset': panelOffset })}
        style={{ top: `${panelOffset}px` }}
        type="persistent"
        hideHeading
      >
        <div className='admin-list__sidepanel__container'>
          <div className="admin-list__panel-section">
            { isSetActiveLoading
              ? <IconSpinner size={32} className="admin-list__panel-badge"/>
              : <StatusBadge
                  className="admin-list__panel-badge"
                  status={selectedRule.status}
                  disabled={selectedRule.disabled}
                  endTime={selectedRule.end_at}
                  startTime={selectedRule.start_at}
                />
            }
            <button className="admin-list__panel-icon" onClick={() => setIsSidePanelOpen(false)}>
              <IconClose/>
            </button>
          </div>

          <TextHeadline className="admin-list__panel-heading" component="h2" size={21}>{ selectedRule.external_ref || 'Alert' }</TextHeadline>
          <TextBody className="admin-list__panel-name" component="p">{ selectedRule.name }</TextBody>

          <div className="admin-list__panel-section admin-list__panel-icons">
            { actionList.map(action => {
              const isDisabled = isSetActiveLoading && (action.menuName === 'Activate' || action.menuName === 'Deactivate');
              return (
                <div key={`action-${action.menuName}`} className={`admin-list__panel-icon-container ${isDisabled ? 'admin-list__panel-icon-container--disabled' : ''}`} >
                  <button className={`admin-list__panel-icon ${isDisabled ? 'admin-list__panel-icon--disabled' : ''}`}
                    onClick={action.onClick} disabled={isDisabled}>
                    { action.iconType({ color: `${isDisabled ? 'gray' : 'blue'}` }) }
                  </button>
                  <TextLegal component="p" bold={true}>{ action.menuName }</TextLegal>
                </div>
              );
            }) }
          </div>

          <ul>
            <li className="admin-list__panel-section admin-list__panel-list-item">
              <TextSubtitle type="3" component="p">Content Type</TextSubtitle>
              <TextCaption component="p">{ selectedRule.content_type }</TextCaption>
            </li>

            <li className="admin-list__panel-section admin-list__panel-list-item">
              <TextSubtitle type="3" component="p">Content Name</TextSubtitle>
              { content.isLoading
                ? <IconSpinner size={24} />
                : <TextCaption component="p" className="admin-list__panel-content-name">{ content.contentItem.content.name }</TextCaption>
              }
            </li>

            <li className="admin-list__panel-section admin-list__panel-list-item">
              <TextSubtitle type="3" component="p">Contentful ID</TextSubtitle>
              <div className="admin-list__panel-content-id">
                <TextCaption component="p">{ selectedRule.content_id }</TextCaption>
                <CanvasLink
                  href={`/api/v1/contents/spaces/${selectedRule.content_space}/entries/contents/${selectedRule.content_id}`}
                  target="_blank"
                  type="emphasis"
                  size={14}
                  externalIconLabel="External link"
                >
                  Contentful Link
                </CanvasLink>
              </div>
            </li>
            { (type === ruleTypes.CAMPAIGN || type === ruleTypes.CCAU_CAMPAIGN) &&
              <>
                <li className="admin-list__panel-section admin-list__panel-list-item">
                  <TextSubtitle type="3" component="p">Created by (Team)</TextSubtitle>
                  { teams.isLoading
                    ? <IconSpinner size={24} />
                    : <TextCaption component="p">{ teamMapping[sidMapping[selectedRule.created_by]?.teamId] }</TextCaption>
                  }
                </li>
                <li className="admin-list__panel-section admin-list__panel-list-item">
                  <TextSubtitle type="3" component="p">Created by (User)</TextSubtitle>
                  <TextCaption component="p">{ sidMapping[selectedRule.created_by]?.name || selectedRule.created_by }</TextCaption>
                </li>
                <li className="admin-list__panel-section admin-list__panel-list-item">
                  <TextSubtitle type="3" component="p">Created on</TextSubtitle>
                  <TextCaption component="p" className="admin-list__panel-date">{ moment(selectedRule.created_at).format('lll') }</TextCaption>
                </li>
                <li className="admin-list__panel-section admin-list__panel-list-item">
                  <TextSubtitle type="3" component="p">Last updated by (Team)</TextSubtitle>
                  { teams.isLoading
                    ? <IconSpinner size={24} />
                    : <TextCaption component="p">{ teamMapping[sidMapping[selectedRule.updated_by]?.teamId] }</TextCaption>
                  }
                </li>
                <li className="admin-list__panel-section admin-list__panel-list-item">
                  <TextSubtitle type="3" component="p">Last updated by (User)</TextSubtitle>
                  <TextCaption component="p">{ sidMapping[selectedRule.updated_by]?.name || selectedRule.updated_by }</TextCaption>
                </li>
                <li className="admin-list__panel-section admin-list__panel-list-item">
                  <TextSubtitle type="3" component="p">Last updated on</TextSubtitle>
                  <TextCaption component="p" className="admin-list__panel-date">{ moment(selectedRule.updated_at).format('lll') }</TextCaption>
                </li>
              </>
            }
            { type === ruleTypes.ALERT &&
              <>
                <li className="admin-list__panel-section admin-list__panel-list-item">
                  <TextSubtitle type="3" component="p">Pages</TextSubtitle>
                  <div>
                    { selectedRule.pages && selectedRule.pages.length
                      ? selectedRule.pages.map(page => <TextCaption key={page} component="p" className="admin-list__panel-platform">{ page }</TextCaption>)
                      : <TextCaption component="p">-</TextCaption>
                    }
                  </div>
                </li>

                <li className="admin-list__panel-section admin-list__panel-list-item">
                  <TextSubtitle type="3" component="p">Containers</TextSubtitle>
                  <TextCaption component="p">{ selectedRule.container }</TextCaption>
                </li>
              </>
            }
            <li className="admin-list__panel-section admin-list__panel-list-item">
              <TextSubtitle type="3" component="p">Platform</TextSubtitle>
              <div>{ selectedRule.platforms.map(platform => <TextCaption key={platform} component="p" className="admin-list__panel-platform">{ platformsUI[platform] }</TextCaption>) }</div>
            </li>
          </ul>
        </div>
    </SideSheet>
    );
  };

  const checkApplicationsContainersPages = () => {
    getApplications();
    if (!pages.isLoading && isEmpty(pages.items)) {
      getPages();
    }
    if (!containers.isLoading && isEmpty(containers.items)) {
      getContainers();
    }
  };

  const showActionItem = (row) => {
    const selectedApp = Object.values(applications.items).find(({ applicationId }) => applicationId === row.application);
    const selectedContainer = Object.values(containers.items).find(({ containerId, applicationId }) => (containerId === row.container && applicationId === row.application));
    const selectedPages = Object.values(pages.items).filter(({ pageId }) => row.pages?.includes(pageId));
    if (selectedApp && !selectedApp.status) {
        return false;
    } else if (selectedContainer && !selectedContainer.status) {
        return false;
    } else if (selectedPages?.length) {
        const inactivePage = selectedPages.find(({ status }) => !status);
        return !inactivePage;
    } else {
      return true;
    }
  };

  const getActionItems = (row) => {
    if (!row) {
      return;
    }

    const {
      status,
      disabled,
      content_type: contentType,
      content_id: contentId,
      container,
      id,
      dismissable_flag: isDismissible,
      content_space: contentfulSpace,
      application,
      mass_targeting: massTargeting,
    } = row;

    const isExpired = new Date(row.end_at) < Date.now();
    const activateButtonDisplayed = (status === STATUS.PUBLISHED && disabled && canEditRule(access, permissions, type, row).canManage && !isExpired);
    const isActivateDisplayed = activateButtonDisplayed ? showActionItem(row) : false;
    const actionList = [ {
      iconType: IconEdit,
      onClick: () => history.push(`/${type}s/${id}`),
      menuName: status === STATUS.DRAFT && canCreate ? 'Edit' : 'View',
    } ];

    if (canEditRule(access, permissions, type, row).canManage) {
      actionList.push({
        iconType: IconDuplicate,
        onClick: () => history.push(`/${type}s/${id}/duplicate`),
        menuName: 'Duplicate',
      });
    };

    actionList.push({
      iconType: IconShow,
      onClick: openPreviewWindow(
        contentType,
        contentId,
        container,
        isDismissible,
        contentfulSpace,
        application,
        massTargeting?.languages
      ),
      menuName: 'Preview',
    });

    if (status === STATUS.PUBLISHED && !disabled && canEditRule(access, permissions, type, row).canManage && !isExpired) {
      actionList.push({
        iconType: IconClose,
        onClick: () => {
          setSelectedRuleId(id);
          setRuleActive(id, status, false);
        },
        menuName: 'Deactivate',
      });
    };

    if (isActivateDisplayed) {
      actionList.push({
        iconType: IconCheck,
        onClick: () => {
          setSelectedRuleId(id);
          setRuleActive(id, status, true);
        },
        menuName: 'Activate',
      });
    }

    if ((status === STATUS.DRAFT || status === STATUS.SUBMITTED || status === STATUS.REVIEWED) && canEditRule(access, permissions, type, row).canManage) {
      actionList.push({
        iconType: IconDelete,
        onClick: () => {
          setIsActionListOpen(false);
          setIdToDelete(id);
        },
        menuName: 'Delete',
      });
    }
    return { actionList, actionItems: actionList.map(action => <ActionMenuListItem key={action.menuName} icon={action.iconType} onClick={action.onClick}>{ action.menuName }</ActionMenuListItem>) };
  };

  const renderDeleteModal = () => {
    if (idToDelete && isSidePanelOpen) {
      setIsSidePanelOpen(false);
    }
    const closeDeleteModal = () => setIdToDelete(null);
    return (
      <ModalDialogue
        headline="Are you sure?"
        primaryButtonLabel="Delete"
        primaryAction={() => {
          deleteRule(idToDelete);
          setSelectedRuleId(null);
          closeDeleteModal();
        }}
        secondaryButtonLabel="Cancel"
        secondaryAction={closeDeleteModal}
        isModalVisible={!!idToDelete}
        setModalVisible={closeDeleteModal}
      >
        This { type } will be deleted.
      </ModalDialogue>
    );
  };

  return (
    <div className={classnames('admin-list', { 'admin-list--extended': !filteringOptionsCollapsed })}>
      <div className="admin-list__action-bar">
        <TextHeadline
          component="h1"
          className="admin-list__header"
        >
          { `${ruleTypesDisplayName[type]}s ` }
          { isLoading && <IconSpinner size={24} /> }
        </TextHeadline>
        { permissions[`${type}s_manage`] && canCreate && (
          <SecondaryButton
            type="caution"
            buttonType="button"
            onClick={() => history.push(`/${formatWord(type, { plural: true })}/create`)}
          >
            Create New
          </SecondaryButton>
        ) }
      </div>
      <Tabs className="admin-list__status-tabs" onClick={(tabIndex) => tabChanged(tabIndex)}>
        <Tab label="All" />
        <Tab label="Published" />
        <Tab label="Pending" />
      </Tabs>
      <div className="admin-list__filter-accordion">
        <div className="admin-list__search">
          <Search
            id="name"
            placeholder="Search"
            onChange={e => filtersChanged({ search: e.target.value, pageNumber: 1 }, true)}
            showLabel={false}
            value={filters?.search || ''}
            clearButtonLabel="Clear search"
            searchButtonLabel="Search"
            label="Search"
          />
        </div>
        <div className="admin-list__action-button-container">
          { type === ruleTypes.ALERT ? <ExportAlertModal /> : <ExportCampaignModal /> }
          <TextButton
            Icon={filteringOptionsCollapsed ? IconFilter : IconClose}
            onClick={toggleFilterSection}
          >
            { filteringOptionsCollapsed ? 'Filters' : 'Cancel' }
          </TextButton>
        </div>
      </div>
      { renderFilterSection() }
      <Table
        id="admin-campaign-list-table"
        title=""
        className="admin-list__listings-table"
        resetSortOnDataChange={false}
        columns={tableColumns({
          type,
          ruleTypes,
          sortableColumnProperties,
          filters,
          ruleTypesDisplayName,
          isSetActiveLoading,
          tableData,
          getActionItems,
          getContentById,
          selectedRuleId,
          setSelectedRuleId,
          setIsSidePanelOpen,
          isActionListOpen,
          setIsActionListOpen,
        })}
        data={tableData}
      />
      { rules && pagination.total === 0 && (
        <TextCaption component="p">No results available</TextCaption>
      ) }
      { rules && pagination.total > pagination.limit && (
        <DesktopPagination
          id="pagination"
          className="admin-list__pagination"
          totalResultCount={pagination.total}
          onChange={(pageNumber) => filtersChanged({ pageNumber })}
          firstButtonLabel="First"
          prevButtonLabel="Previous"
          nextButtonLabel="Next"
          lastButtonLabel="Last"
          navigationLabel="Pagination Navigation"
          pageSize={10}
          currentPage={rules ? (pagination.offset / pagination.limit) + 1 : 1}
        />
      ) }
      { renderDeleteModal() }
      { renderSidePanel() }
    </div>
  );
};

RulesList.defaultProps = {
  pagination: {},
};

RulesList.propTypes = {
  rules: PropTypes.arrayOf(PropTypes.object),
  isLoading: PropTypes.bool,
  isSetActiveLoading: PropTypes.bool,
  users: PropTypes.shape({
    items: PropTypes.array,
    isLoading: PropTypes.bool,
  }).isRequired,
  getRules: PropTypes.func.isRequired,
  setRuleActive: PropTypes.func.isRequired,
  deleteRule: PropTypes.func.isRequired,
  pagination: PropTypes.shape({
    total: PropTypes.number,
    limit: PropTypes.number,
    offset: PropTypes.number,
  }),
  type: PropTypes.string.isRequired,
  access: PropTypes.object.isRequired,
  permissions: PropTypes.object.isRequired,
  content: PropTypes.object,
  getContentById: PropTypes.func,
  applications: PropTypes.shape({
    items: PropTypes.array,
    isLoading: PropTypes.bool,
  }),
  containers: PropTypes.shape({
    items: PropTypes.array,
    isLoading: PropTypes.bool,
  }),
  pages: PropTypes.shape({
    items: PropTypes.array,
    isLoading: PropTypes.bool,
  }),
  ruleSubTypes: PropTypes.shape({
    items: PropTypes.array,
    isLoading: PropTypes.bool,
  }),
  teams: PropTypes.shape({
    items: PropTypes.object,
    isLoading: PropTypes.bool,
  }),
  getApplications: PropTypes.func,
  getContainers: PropTypes.func,
  getPages: PropTypes.func,
  getRuleSubTypes: PropTypes.func,
  getTeams: PropTypes.func,
};

export default RulesList;
