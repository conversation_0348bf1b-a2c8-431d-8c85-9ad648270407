import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { getAlerts, setAlertActive, deleteAlert } from '../../store/actions/alerts';
import { getCampaigns, setCampaignActive, deleteCampaign } from '../../store/actions/campaigns';
import { getContentById } from '../../store/actions/content';
import { getRuleSubTypes } from '../../store/actions/ruleSubTypes';
import { getApplications } from '../../store/actions/applications';
import { getPages } from '../../store/actions/pages';
import { getContainers } from '../../store/actions/containers';
import { getTeams } from '../../store/actions/teams';

import RulesList from './list';
import { ruleTypes, mapObjectToArray } from '../../constants';

const commonStateSelector = state => ({
  users: state.users,
  permissions: state.authenticated.permissions,
  applications: state.applications,
  content: state.content,
  teams: state.teams,
});

// Connected Alerts List Component
const alertMapStateToProps = state => ({
  rules: mapObjectToArray(state.alerts.items || []),
  pagination: state.alerts.pagination,
  type: ruleTypes.ALERT,
  isLoading: state.alerts.isLoading,
  isSetActiveLoading: state.alerts.isSetAlertActiveLoading,
  access: state.authenticated.access.alerts,
  containers: state.containers,
  pages: state.pages,
  ...commonStateSelector(state),
});

const alertMapDispatchToProps = dispatch => bindActionCreators({
  getRules: getAlerts,
  setRuleActive: setAlertActive,
  deleteRule: deleteAlert,
  getContentById,
  getTeams,
  getApplications,
  getPages,
  getContainers,
}, dispatch);

export const AlertList = connect(alertMapStateToProps, alertMapDispatchToProps)(RulesList);

// Connected Campaigns List Component
const campaignMapStateToProps = state => ({
  rules: mapObjectToArray(state.campaigns.items || []),
  pagination: state.campaigns.pagination,
  type: window.location.pathname === '/ccau_campaigns' ? ruleTypes.CCAU_CAMPAIGN : ruleTypes.CAMPAIGN,
  isLoading: state.campaigns.isLoading || state.applications.isLoading,
  isSetActiveLoading: state.campaigns.isSetCampaignActiveLoading,
  containers: state.containers,
  pages: state.pages,
  ruleSubTypes: state.ruleSubTypes,
  access: state.authenticated.access.campaigns,
  ...commonStateSelector(state),
});

const campaignMapDispatchToProps = dispatch => bindActionCreators({
  getRules: getCampaigns,
  setRuleActive: setCampaignActive,
  deleteRule: deleteCampaign,
  getContentById,
  getRuleSubTypes,
  getApplications,
  getPages,
  getContainers,
  getTeams,
}, dispatch);

export const CampaignList = connect(campaignMapStateToProps, campaignMapDispatchToProps)(RulesList);
