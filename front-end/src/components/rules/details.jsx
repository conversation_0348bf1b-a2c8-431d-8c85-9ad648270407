import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';

import TextIntroduction from 'canvas-core-react/lib/TextIntroduction';
import BackButton from 'canvas-core-react/lib/BackButton';
import PillButton from 'canvas-core-react/lib/PillButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import Margin from 'canvas-core-react/lib/Margin';
import Modal from 'canvas-core-react/lib/internal/Modal';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import Card from 'canvas-core-react/lib/Card';
import InputGroup from 'canvas-core-react/lib/InputGroup';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import {
  InputTextField,
  InputCheckboxField,
  InputRadioButtonField,
  InputSelectField,
  InputGroupField,
  DateField,
} from '../formFields';
import { VersionTargetingModal } from '../modal/versionTargetingModal';
import { SavedVersions } from '../versionTargeting/savedVersions';
import {
  addThousandSeparator,
  STATUS,
  mapIdToKey,
  mapTruePropsToArray,
  MAX_SCENE_POINTS,
  removeThousandSeparator,
  ruleTypes as ruleTypesConstants,
  mapObjectToArray,
  ruleTypes,
} from '../../constants';

import ContentModalConnected from '../modal/contentContainer';
import StatusBadge from '../core/statusBadge';
import { AssignmentAutosuggest } from '../autosuggest';
import { canEditRule } from './utils';
import { normalizedStrCompare, capitalize } from '../../utils';
import AdvancedTargetingSection from './advancedTargetingSection';
import { min, max, maxValue, campaignName, required, campaignId, offerId } from '../../utils/validation';
import RadioButton from 'canvas-core-react/lib/RadioButton';
import { TargetingMetadata, Card2SV, LanguageCard } from './DetailsCards';
import LOBTargeting from './DetailsCards/wealthTargeting/lobTargeting';
import DemographicTargeting from './DetailsCards/wealthTargeting/demographicTargeting';
import IClubTiersTargeting, { iClubTierOptions } from './DetailsCards/wealthTargeting/iClubTiersTargeting';
import SegmentIdTargeting, { SEGMENTID_OPTIONS } from './DetailsCards/wealthTargeting/segmentTargeting';
import InvestingKnowledgeTargeting, { INVESTMENT_TYPES, KNOWLEDGE_LEVELS } from './DetailsCards/wealthTargeting/investingKnowledgeTargeting';

const fixFullName = assignees => {
  if (!assignees || !Array.isArray(assignees)) {
    return;
  }
  return assignees.map(user => ({
    ...user,
    fullName: user.full_name || user.name,
  }));
};

const validation = {
  min4: min(4),
  min5: min(5),
  max15: max(15),
  max40: max(40),
  max50: max(50),
  max100: max(100),
  noMessage: value => (
    normalizedStrCompare((value || '').trim(), 'MESSAGE')
      ? `"MESSAGE" is not a valid campaign ID for Targeted campaigns. Please change campaign type accordingly.`
      : undefined
  ),
  maxValueOneMillion: maxValue(MAX_SCENE_POINTS),
  campaignId,
  offerId,
};

export default class Details extends React.Component {
  static propTypes = {
    status: PropTypes.string,
    title: PropTypes.string.isRequired,
    formValues: PropTypes.object.isRequired,
    onSubmit: PropTypes.func.isRequired,
    onApprove: PropTypes.func.isRequired,
    onPublish: PropTypes.func.isRequired,
    onActivate: PropTypes.func.isRequired,
    onCancel: PropTypes.func.isRequired,
    onDuplicate: PropTypes.func.isRequired,
    onDelete: PropTypes.func.isRequired,
    deleteContent: PropTypes.func.isRequired,
    openContentModal: PropTypes.func.isRequired,
    updateDetails: PropTypes.func.isRequired,
    type: PropTypes.oneOf([ ruleTypesConstants.CAMPAIGN, ruleTypesConstants.ALERT, ruleTypesConstants.CCAU_CAMPAIGN ]),
    permissions: PropTypes.object.isRequired,
    access: PropTypes.object.isRequired,
    containers: PropTypes.shape({
      items: PropTypes.array,
      isLoading: PropTypes.bool,
    }).isRequired,
    pages: PropTypes.shape({
      items: PropTypes.array,
      isLoading: PropTypes.bool,
    }).isRequired,
    isDuplicating: PropTypes.bool,
    productBook: PropTypes.array,
    addAlert: PropTypes.func.isRequired,
    removeAlert: PropTypes.func.isRequired,
    ruleTypes: PropTypes.array,
    ruleSubTypeMap: PropTypes.object.isRequired,
    applications: PropTypes.array,
    platforms: PropTypes.array.isRequired,
    formChange: PropTypes.func.isRequired,
    formReset: PropTypes.func,
    formUntouch: PropTypes.func,
    users: PropTypes.shape({
      items: PropTypes.object,
      isLoading: PropTypes.bool,
    }),
    formValid: PropTypes.bool.isRequired,
  };

  state = {
    assigneesSuggestions: [],
    promptUserAssignment: false,
    editable: true,
    popup: false,
    disableAssignmentSubmission: true,
    initialPlatformsSet: false,
    isSelectedPlacementDisabled: false,
    targetScenePoints: false,
  };

  get isCampaign() {
    return this.props.type === ruleTypesConstants.CAMPAIGN || this.props.type === ruleTypesConstants.CCAU_CAMPAIGN;
  }

  get getRuleAction() {
    return canEditRule(this.props.access, this.props.permissions, this.props.type, this.props.formValues);
  }

  get formDisabled() {
    switch (this.props.status) {
      case STATUS.SUBMITTED:
      case STATUS.REVIEWED:
      case STATUS.PUBLISHED:
        return true;
      default:
        return this.props.formValues.id ? !this.getRuleAction.canManage
        : !canEditRule(this.props.access, this.props.permissions, this.props.type);
    }
  }

  get reduxFormName() {
    return this.isCampaign ? 'campaignDetails' : 'alertDetails';
  }

  get editingMode() {
    return !this.props.status || this.props.status === STATUS.DRAFT;
  }

  get filteredContainersForApplicationAndRuleType() {
    const { containers, type } = this.props;
    if (!containers.items) {
      return;
    }
    ;

    return mapIdToKey(
      Object.values(containers.items)
        .filter(c => {
          const matchingRuleType = c.rule_type === type;
          const matchingApplication = c.application === this.currentApp?.id;
          return matchingRuleType && matchingApplication;
        })
    );
  }

  get containerSelected() {
    const containers = this.filteredContainersForApplicationAndRuleType;
    const containerList = containers && mapObjectToArray(containers);
    const container = containerList?.find(c => c.containerId === this.props.formValues.container);
    return container;
  }

  get currentRuleType() {
    const { ruleTypes, type } = this.props;
    return ruleTypes.find(({ slug }) => slug === type) || {};
  }

  get activeAppsForCurrentRuleType() {
    const { applications } = this.props;
    // List only active applications if creating a new campaign/alert, otherwise list all applications
    return Object.values(applications).filter(({ status, ruleTypeIds }) =>
      (!this.props.status ? status : true) && ruleTypeIds.includes(this.currentRuleType?.id));
  }

  get currentApp() {
    const { formValues } = this.props;
    return this.activeAppsForCurrentRuleType?.find(({ applicationId }) =>
      applicationId === formValues.application);
  }

  get availablePlatforms() {
    const { platforms } = this.props;
    return platforms.filter(platform => (this.currentApp?.platformIds || []).indexOf(platform.id) !== -1);
  }

  openApplicationSwitchPopup = (oldApplicationValue) => {
    this.setState({
      popup: 'applicationSwitch',
      oldApplicationValue,
    });
  }

  openPopupDelete = () => {
    this.setState({ popup: 'delete' });
  };

  closePopup = () => {
    this.setState({ popup: false });
  };

  resetValuesOnAppAwitch = (selectedApp) => {
    const { formValues, formChange, platforms, ruleSubTypeMap } = this.props;

    // if application is changed, select all platforms for newly selected app
    const targetApp = this.activeAppsForCurrentRuleType?.find(({ applicationId }) => applicationId === selectedApp);
    const targetPlatforms = platforms.filter(({id}) => targetApp.platformIds.indexOf(id) !== -1); // eslint-disable-line
    const targetPlatformSlugs = targetPlatforms.map(({slug}) => slug); // eslint-disable-line
    [ ...Object.keys(formValues.platforms), ...targetPlatformSlugs ].forEach(platform =>
      formChange(this.reduxFormName, `platforms.${platform}`, targetPlatformSlugs.indexOf(platform) !== -1));

    this.props.formReset('targetingMetadata');
    this.props.deleteContent();
    formChange(this.reduxFormName, 'container', null);
    formChange(this.reduxFormName, 'pages', null);

    if (this.isCampaign) {
      formChange(`campaignDetails`, 'external_ref', null);

      const accessRuleSubTypes = this.props.access.ruleSubTypes[selectedApp].manage;
      const currentRuleSubTypes = targetApp.ruleSubTypes
        .filter(ruleSubType => accessRuleSubTypes.includes(ruleSubType))
        .map(ruleSubType => Object.values(ruleSubTypeMap).find(o => o.type === ruleSubType)?.type);
      if ((!formValues.type || !currentRuleSubTypes.includes(formValues.type)) && targetApp.ruleSubTypeIds.length > 0) {
        formChange('campaignDetails', 'type', currentRuleSubTypes[0]);
      }
    }
  }

  renderApplicationSwitchPopup = () => {
    const { formChange, formValues } = this.props;
    const { oldApplicationValue } = this.state;

    const undoApplicationChange = () => {
      formChange(this.reduxFormName, 'application', oldApplicationValue);
      this.closePopup();
    };

    const deleteTargetingMetadata = () => {
      this.resetValuesOnAppAwitch(formValues['application']);
      this.closePopup();
    };

    return (
      <ModalDialogue
        isModalVisible={this.state.popup === 'applicationSwitch'}
        headline="Are you sure?"
        primaryButtonLabel="Continue"
        primaryAction={deleteTargetingMetadata}
        secondaryButtonLabel="Cancel"
        secondaryAction={undoApplicationChange}
        setModalVisible={undoApplicationChange}
      >
        <p>The container, page, and content combination you have chosen will no longer be valid for the new application
          you have selected.</p>
        <br/>
        <p>To continue with the current application, you will have to select new content.</p>
      </ModalDialogue>
    );
  };

  platformCheckboxChecked = (platform) => {
    const { formValues: { platforms } } = this.props;
    return !!platforms[platform];
  };

  renderTooltip(platform) {
    const { type } = this.props;
    const supportedVTPlatforms = {
      android: true,
      ios: true,
    };
    // if we aren't editing, or our platform isn't checked
    if (!this.editingMode || !this.platformCheckboxChecked(platform) || !supportedVTPlatforms[platform]) {
      return null;
    }
    return (
      <VersionTargetingModal
        platform={platform}
        formName={type === ruleTypesConstants.CAMPAIGN || type === ruleTypesConstants.CCAU_CAMPAIGN ? 'campaignDetails' : 'alertDetails'}
      />
    );
  }

  getAssigneeSuggestions = async() => {
    const { formValues, type, isDuplicating } = this.props;
    const accessDeterminantsComplete = formValues.container && formValues.pages && formValues.pages.length && formValues.type;
    if ((type === ruleTypesConstants.CAMPAIGN ||
     type === ruleTypesConstants.CCAU_CAMPAIGN
    ) &&
      accessDeterminantsComplete) {
      // rule id exists once campaign is saved as draft or created for review
      const ruleId = formValues.id && !isDuplicating ? `/${formValues.id}` : '';
      const fetchCampaignUsers = await (await fetch(`/api/v1/campaign-users${ruleId}`)).json();
      const assignees = fetchCampaignUsers && fetchCampaignUsers.data;
      if (assignees && Array.isArray(assignees)) {
        this.setState({
          assigneesSuggestions: assignees.map(assignee => ({ // eslint-disable-line react/prop-types
            ...assignee,
            fullName: assignee.full_name || assignee.name,
          })),
        });
      }
    }
  }

  componentDidUpdate(prevProps) {
    const { formValues, users, status, isDuplicating } = this.props;
    const duplicatedCampaign = !prevProps.isDuplicating && isDuplicating;
    const campaignSavedOrStatusChanged = (prevProps.formValues.id !== formValues.id) || (prevProps.status !== status);
    const allUsersFinishedLoading = (prevProps.users && prevProps.users.isLoading) && (!users.isLoading && users.items);
    const accessDeterminantChanged =
      (prevProps.formValues.container !== formValues.container) ||
      (prevProps.formValues.pages !== formValues.pages) ||
      (prevProps.formValues.type !== formValues.type);
    // when duplicating a rule, status field should not be cloned into new rule
    isDuplicating && formValues.status && this.props.updateDetails({ status: undefined });
    if ((duplicatedCampaign || campaignSavedOrStatusChanged || allUsersFinishedLoading || accessDeterminantChanged)) {
      this.getAssigneeSuggestions();
    }
    // If form has been initialized with an application, preselect all associated platforms
    if (!this.state.initialPlatformsSet && !this.props.status && this.availablePlatforms.length && !isDuplicating) {
      this.setInitialPlatforms();
    }
    if (formValues.application !== prevProps.formValues.application) {
      this.setIsSelectedPlacementDisabled();
    }
  }

  componentDidMount() {
    this.getAssigneeSuggestions();
    this.setIsSelectedPlacementDisabled();
    if (this.props.formValues.mass_targeting?.by_scene_points) {
      this.setState({ targetScenePoints: true });
    }
  }

  handleAssigneeChange = newAssignees => {
    this.setState({ disableAssignmentSubmission: newAssignees.length === 0 });
    this.props.updateDetails({ newAssignees });
  }

  hideAssignmentPopup = () => {
    this.setState({
      promptUserAssignment: false,
    });
  }

  renderAssignmentPopup = () => {
    if (this.props.type === ruleTypesConstants.ALERT) {
      return null;
    }

    const { assigneesSuggestions, promptUserAssignment } = this.state;
    const { status } = this.props;
    return (
      <Modal
        headline="This campaign needs to be assigned"
        isModalVisible={promptUserAssignment}
        setModalVisible={this.hideAssignmentPopup}
      >
        <AssignmentAutosuggest
          data={assigneesSuggestions}
          dataLegend={{
            keyName: 'sid',
            valueName: 'fullName',
          }}
          onChange={this.handleAssigneeChange}
          editable
          fromPopup
        />
        <div className="admin-details__modal-footer">
          <SecondaryButton
            className="admin-details__modal-button"
            onClick={this.hideAssignmentPopup}
          >
            Cancel
          </SecondaryButton>
          <PrimaryButton
            className="admin-details__modal-button"
            disabled={this.state.disableAssignmentSubmission}
            onClick={status === 'submitted' ? this.approve(true) : this.submitForReview}
          >
            Submit
          </PrimaryButton>
        </div>
      </Modal>
    );
  }

  submitForReview = (event) => {
    if (event) {
      event.preventDefault();
    }
    const { formValid, type } = this.props;
    const { promptUserAssignment } = this.state;
    if (type === ruleTypesConstants.ALERT) {
      return this.props.onSubmit()();
    }
    // if no assignees or only assignee is self, prompt to assign users
    if (formValid) {
      if (promptUserAssignment) {
        this.props.onSubmit()();
        this.setState({
          promptUserAssignment: false,
        });
      } else {
        this.setState({
          promptUserAssignment: true,
          disableAssignmentSubmission: true,
        });
      }
      return false;
    } else {
      this.props.onSubmit()();
      this.setState({
        promptUserAssignment: false,
      });
    }
  }

  approve = (fromPopup) => () => {
    if (fromPopup) {
      this.props.onApprove(false)();
    }
    this.setState({
      promptUserAssignment: !fromPopup,
      disableAssignmentSubmission: true,
    });
  };

  applicationChanged = (event, selectedApp) => {
    const { formValues } = this.props;

    // show warning modal to confirm application switch & form reset
    if (formValues.content_id) {
      this.openApplicationSwitchPopup(formValues['application']);
    } else {
      this.resetValuesOnAppAwitch(selectedApp);
    }
  }

  setInitialPlatforms() {
    const { formChange } = this.props;
    const availablePlatformsSlugs = this.availablePlatforms?.map(({slug}) => slug); // eslint-disable-line
    availablePlatformsSlugs.forEach(platform => formChange(this.reduxFormName, `platforms.${platform}`, true));
    this.setState({ initialPlatformsSet: true });
  }

  atLeastOnePlatformSelected = (values, allValues) => {
    const { platforms } = this.props;
    const selectedApp = this.activeAppsForCurrentRuleType?.find(({ applicationId }) => applicationId === allValues.application);
    const availablePlatforms = platforms.filter(({id}) => selectedApp?.platformIds.includes(id)); // eslint-disable-line
    const selectedPlatforms = mapTruePropsToArray(values || {});
    if (availablePlatforms.length && selectedPlatforms.length === 0) {
      return 'You must select at least one platform.';
    }
  };

  renderAdvancedTargetingSection = () => {
    const { formValues } = this.props;

    if (!this.isCampaign || (formValues.type !== 'mass')) {
      return;
    }

    const productSelections = formValues?.mass_targeting?.by_product;
    const initialSelections = productSelections && {
      include: [ ...(productSelections.any_of || []), ...(productSelections.all_of || []) ],
      exclude: [ ...(productSelections.none_of || []) ],
    };

    return (
      <Field
        name="advancedTargeting"
        productBook={this.props.productBook}
        initialSelections={initialSelections}
        defaultIncludeMode={productSelections?.all_of ? 'all' : 'any'}
        disabled={this.formDisabled}
        isOptional
        includeSearch
        includeSelectionPreview
        component={AdvancedTargetingSection}
      />
    );
  }

  handleScenePointsTargetingChange = (e) => {
    const { formChange, formUntouch } = this.props;
    const targetScenePoints = e.target.value === 'yes';
    if (!targetScenePoints) {
      // Reset scene points targeting values
      formChange('campaignDetails', 'mass_targeting.by_scene_points.targeting_criteria', '');
      formChange('campaignDetails', 'mass_targeting.by_scene_points.range_min', '');
      formChange('campaignDetails', 'mass_targeting.by_scene_points.range_max', '');
      formChange('campaignDetails', 'mass_targeting.by_scene_points.points', '');
      formUntouch('campaignDetails', 'mass_targeting.by_scene_points.targeting_criteria');
      formUntouch('campaignDetails', 'mass_targeting.by_scene_points.range_min');
      formUntouch('campaignDetails', 'mass_targeting.by_scene_points.range_max');
      formUntouch('campaignDetails', 'mass_targeting.by_scene_points.points');
    }
    this.setState({ targetScenePoints });
  }

  validateLOBSelection = (value, allValues) => {
    if (allValues.type === 'wealth' && (!value || value.length === 0)) {
      return 'At least one LOB must be selected';
    }
    return undefined;
  }

  validateDemographicSelection = (value, allValues) => {
    if (allValues.type === 'wealth' && value) {
      // Validate age range
      if (value.age_min && value.age_max) {
        const minAge = parseInt(value.age_min, 10);
        const maxAge = parseInt(value.age_max, 10);

        if (minAge > maxAge) {
          return 'Minimum age cannot be greater than maximum age';
        }
      }

      // Validate age values are within range
      if (value.age_min && (value.age_min < 1 || value.age_min > 99)) {
        return 'Age must be between 1 and 99';
      }
      if (value.age_max && (value.age_max < 1 || value.age_max > 99)) {
        return 'Age must be between 1 and 99';
      }
    }
    return undefined;
  }

  validateUpperRange = (value, allValues, props) => {
    if (this.state.targetScenePoints) {
      const max = value && parseInt(value);
      if (!max) {
        return 'Required';
      }
      if (max > MAX_SCENE_POINTS) {
        return `Must be ${addThousandSeparator(MAX_SCENE_POINTS)} or less`;
      }

      const { range_min: rangeMin } = allValues.mass_targeting.by_scene_points;
      const min = rangeMin && parseInt(rangeMin);
      if (min && max && max <= min) {
        return 'Must be greater than the lower limit';
      }
    }
  }

  validateAmount = (value, allValues, props) => {
    if (this.state.targetScenePoints) {
      const val = value && parseInt(value);
      if (!val && val !== 0) {
        return 'Required';
      }

      const { targeting_criteria: targetingCriteria } = allValues.mass_targeting.by_scene_points;
      if (targetingCriteria === 'less') {
        if (val < 1) {
          return 'Must be 1 or more';
        }
      }
      if (val > MAX_SCENE_POINTS) {
        return `Must be ${addThousandSeparator(MAX_SCENE_POINTS)} or less`;
      }
    }
  }

  renderRewardsTargetingSection = () => {
    const { formValues } = this.props;
    if (!this.isCampaign || !formValues.application || (formValues.type !== 'mass')) {
      return;
    }

    return (
      <Card className="admin-details__card" type="floatLow">
        <TextHeadline
          component="h2"
          size={21}
          className="admin-details__sub-header"
        >
          Target by Scene Points
        </TextHeadline>
        <Margin side="bottom" xs={18}>
          <InputGroup id="target-scene-points" legend="Target scene points" inline>
            <RadioButton
              id="target-scene-points-yes"
              label="Yes"
              name="target-scene-points-yes"
              value="yes"
              checked={this.state.targetScenePoints}
              disabled={this.formDisabled}
              onChange={this.handleScenePointsTargetingChange}
            />
            <RadioButton
              id="target-scene-points-no"
              label="No"
              name="target-scene-points-no"
              value="no"
              checked={!this.state.targetScenePoints}
              disabled={this.formDisabled}
              onChange={this.handleScenePointsTargetingChange}
            />
          </InputGroup>
        </Margin>
        {
          this.state.targetScenePoints &&
          <div className="admin-details__scene-fields">
            <Field
              className="admin-details__scene-field"
              name="mass_targeting.by_scene_points.targeting_criteria"
              label="Targeting criteria"
              placeholder="Select operator"
              component={InputSelectField}
              disabled={this.formDisabled}
              validate={this.state.targetScenePoints && [ required ]}
              options={[
                { id: 'equal', name: 'Equal to (=)' },
                { id: 'less', name: 'Less than (<)' },
                { id: 'lessEqual', name: 'Less than or equal to (<=)' },
                { id: 'greater', name: 'Greater than (>)' },
                { id: 'greaterEqual', name: 'Greater than or equal to (>=)' },
                { id: 'range', name: 'Range' },
              ]}
            />
            {
              this.props.formValues.mass_targeting?.by_scene_points?.targeting_criteria === 'range'
                ? <>
                <Field
                  className="admin-details__scene-field"
                  name="mass_targeting.by_scene_points.range_min"
                  label="Amount (From)"
                  placeholder="Enter amount"
                  component={InputTextField}
                  format={addThousandSeparator}
                  normalize={removeThousandSeparator}
                  disabled={this.formDisabled}
                  validate={this.state.targetScenePoints && [ required, validation.maxValueOneMillion ]}
                  autoComplete="off"
                />
                <Field
                  className="admin-details__scene-field"
                  name="mass_targeting.by_scene_points.range_max"
                  label="Amount (To)"
                  placeholder="Enter amount"
                  component={InputTextField}
                  format={addThousandSeparator}
                  normalize={removeThousandSeparator}
                  disabled={this.formDisabled}
                  validate={this.validateUpperRange}
                  autoComplete="off"
                />
              </>
                : <Field
                  className="admin-details__scene-field"
                  name="mass_targeting.by_scene_points.points"
                  label="Amount"
                  placeholder="Enter amount"
                  component={InputTextField}
                  format={addThousandSeparator}
                  normalize={removeThousandSeparator}
                  disabled={this.formDisabled}
                  validate={this.validateAmount}
                  autoComplete="off"
                />
            }
          </div>
        }
      </Card>
    );
  }

  renderCampaignTargetingOptions = () => {
    const { application, type } = this.props.formValues;
    if (!this.isCampaign || !application) {
      return;
    }

    const currentApplication = Object.values(this.props.applications).find(a => a.applicationId === application) || {};

    // get and map the subtypes of the current application to subType objects
    const accessRuleSubTypes = this.props.access.ruleSubTypes[application].manage || [];
    let displayedSubTypes = (currentApplication.ruleSubTypes || [])
      .filter(ruleSubType => accessRuleSubTypes.includes(ruleSubType))
      .map(ruleSubType => Object.values(this.props.ruleSubTypeMap).find(o => o.type === ruleSubType));

    // check if there is 'type' form value and if it isn't in the displayed sub types we will add it
    // this covers the case in which a rule had a certain sub type saved for an application and the application later
    // removed that sub type
    if (this.formDisabled && type && !displayedSubTypes.find(subType => subType.type === type)) {
      const missingSubType = Object.values(this.props.ruleSubTypeMap).find(subType => subType.type === type);
      if (missingSubType) {
        displayedSubTypes.unshift(missingSubType);
      }
    }

    const displayedSubTypeFields =
    displayedSubTypes.map(({ description, type }) => (
      <Field
        key={`rule-sub-type-${type}`}
        id={type}
        name="type"
        label={description}
        component={InputRadioButtonField}
        onChange={() => {
          const updates = {
            external_ref: type === 'targeted' || type === 'offer' ? null : type.toUpperCase(),
            type,
          };

          // Auto-select all LOBs when wealth type is selected
          if (type === 'wealth') {
            const application = this.props.formValues.application;
            const LOB_OPTIONS = {
              starburst: [ 'SDBI' ],
              atlantis: [ 'SMI', 'CASL', 'TRST', 'SPCGIIA', 'REGL' ],
              default: [ 'SDBI', 'SMI', 'CASL', 'TRST', 'SPCGIIA', 'REGL' ],
            };
            const lobOptions = LOB_OPTIONS[application] || LOB_OPTIONS.default;

            updates.mass_targeting = {
              ...this.props.formValues.mass_targeting,
              wealth_lobs: lobOptions,
              by_demographic: {
                languages: [ 'en', 'fr' ],
                country: [ 'canada', 'non-canada' ],
                provinces: [ 'AB', 'BC', 'MB', 'NB', 'NL', 'NT', 'NS', 'NU', 'ON', 'PE', 'QC', 'SK', 'YT' ],
                gender: [ 'mr', 'ms', 'undisclosed' ],
                age_min: '',
                age_max: '',
              },
              iclub_tiers: iClubTierOptions.map(t => t.code),
              segment_ids: SEGMENTID_OPTIONS.map(s => s.value),
              investment_knowledge: (() => {
                const initialState = {};
                // Default to all selected
                INVESTMENT_TYPES.forEach(type => {
                  initialState[type] = KNOWLEDGE_LEVELS;
                });
            
                return initialState;
              })(),
            };
          }

          this.props.updateDetails(updates);
        }}
        disabled={this.formDisabled}
      />
    ));

    return (
      <Card className="admin-details__card" type="floatLow">
        <TextHeadline
          component="h2"
          size={21}
          className="admin-details__sub-header"
        >
          Campaign Type
        </TextHeadline>
        <InputGroup
          id="campaign-type-radio-group"
          name="type"
          legend=""
        >
          <div className="admin-details__radio-container">
            { displayedSubTypeFields }
          </div>
        </InputGroup>
        <div className="admin-details__no-legend">
          <Field
            name="urgent"
            label="Urgent"
            component={InputCheckboxField}
            disabled={this.formDisabled}/>
        </div>
        <Field
          name="dismissable_flag"
          label="Dismissible (N/A for Inbox Updates)"
          component={InputCheckboxField}
          disabled={this.formDisabled}
        />
      </Card>
    );
  }

  renderPlatforms = (platforms, type) => {
    return (
      <Field
        id="platforms-group"
        legend="Platforms"
        name="platforms"
        component={InputGroupField}
        validate={[ this.atLeastOnePlatformSelected ]}
        className="admin-details__platforms"
      >
        { platforms.map(platform => (
          <React.Fragment key={platform.slug}>
            <div style={{ display: 'flex' }}>
              <Field
                key={platform.slug}
                name={`platforms.${platform.slug}`}
                label={platform.name}
                component={InputCheckboxField}
                disabled={this.formDisabled}
              />
              { this.renderTooltip(platform.slug) }
            </div>
            { this.platformCheckboxChecked(platform.slug) && (
              <Field
                name="platforms_targeting"
                component={SavedVersions}
                platform={platform.slug}
                editingMode={this.editingMode}
                formName={type === ruleTypesConstants.CAMPAIGN || type === ruleTypesConstants.CCAU_CAMPAIGN ? 'campaignDetails' : 'alertDetails'}
              />
            ) }
          </React.Fragment>
        )) }
      </Field>
    );
  }

  setIsSelectedPlacementDisabled = () => {
    const { applications, containers, pages, formValues, status } = this.props;
    if (status === STATUS.DRAFT || status === STATUS.SUBMITTED || status === STATUS.PUBLISHED || status === STATUS.REVIEWED) {
      const selectedApp = Object.values(applications).find(({ applicationId }) => applicationId === formValues.application);
      const selectedContainer = Object.values(containers.items).find(({ containerId, applicationId }) => (containerId === formValues.container && applicationId === formValues.application));
      const selectedPages = Object.values(pages.items).filter(({ pageId }) => formValues.pages?.includes(pageId));
      if (selectedApp && !selectedApp.status) {
        this.setState({ isSelectedPlacementDisabled: true });
        this.props.addAlert(selectedApp.name, 'application');
      } else if (selectedContainer && !selectedContainer.status) {
        this.setState({ isSelectedPlacementDisabled: true });
        this.props.addAlert(selectedContainer.name, 'container');
      } else if (selectedPages?.length) {
        const inactivePage = selectedPages.find(({ status }) => !status);
        if (inactivePage) {
          this.setState({ isSelectedPlacementDisabled: true });
          this.props.addAlert(inactivePage.name, 'page');
        } else {
          this.setState({ isSelectedPlacementDisabled: false });
          this.props.removeAlert();
        }
      } else {
        this.setState({ isSelectedPlacementDisabled: false });
        this.props.removeAlert();
      }
    }
  }

  render() {
    const {
      status,
      type,
      access,
      formValues,
      formChange,
      applications,

    } = this.props;
    const { assigneesSuggestions, isSelectedPlacementDisabled } = this.state;
    // get active/enabled applications for current rule type
    const apps = this.activeAppsForCurrentRuleType;
    // get current selected application
    const currentApp = this.currentApp;
    // get platforms based on app
    const currentPlatforms = this.availablePlatforms;

    return (
      <>
        <div className="admin-details__assignment-popup">
          { this.renderAssignmentPopup() }
        </div>
        <div className="admin-details">
          <form onSubmit={e => e.preventDefault()} > { /* canvas 6 action menu list some how triggers submit on open */ }
            <div className="admin-details__action-bar">
              <TextIntroduction
                component="h1"
                className="admin-details__header"
              >
                { this.props.title }
              </TextIntroduction>
              <div className="admin-details__action-buttons">
                { status && this.getRuleAction.canManage &&
                <PillButton
                  buttonType="button"
                  type="caution"
                  className="admin-details__action-button"
                  onClick={this.props.onDuplicate}>
                  Duplicate
                </PillButton>
                }
                { status === STATUS.PUBLISHED && !formValues.disabled && this.getRuleAction.canManage &&
                <PillButton
                  buttonType="button"
                  type="caution"
                  className="admin-details__action-button"
                  onClick={this.props.onActivate(formValues.status, false)}>
                  Deactivate
                </PillButton>
                }
                { status === STATUS.PUBLISHED && formValues.disabled && this.getRuleAction.canManage &&
                <PillButton
                  buttonType="button"
                  type="caution"
                  className="admin-details__action-button"
                  onClick={this.props.onActivate(formValues.status)}
                  disabled={isSelectedPlacementDisabled}>
                  Activate
                </PillButton>
                }
                { status && (status === STATUS.DRAFT || status === STATUS.SUBMITTED || status === STATUS.REVIEWED) && this.getRuleAction.canManage &&
                <PillButton
                  buttonType="button"
                  type="caution"
                  className="admin-details__action-button"
                  onClick={this.openPopupDelete}>
                  Delete
                </PillButton>
                }
              </div>
            </div>
            {
              status &&
            <TextIntroduction component="h3">
              Current Status: <StatusBadge status={status} disabled={formValues.disabled} startTime={formValues.start_at} endTime={formValues.end_at} />
            </TextIntroduction>
            }
            <Card className="admin-details__card" type="floatLow">
              { formValues.id && this.isCampaign && (status !== 'published') && <div>
                <AssignmentAutosuggest
                  data={assigneesSuggestions}
                  dataLegend={{
                    keyName: 'sid',
                    valueName: 'fullName',
                  }}
                  editable={false}
                  onChange={this.handleAssigneeChange}
                  initialSelection={fixFullName(formValues.assignees)}
                  noLock
                />
              </div> }
              <TextHeadline
                component="h2"
                size={21}
                className="admin-details__sub-header"
              >
                { type === ruleTypes.CCAU_CAMPAIGN ? capitalize('campaign') : capitalize(type) } Information
              </TextHeadline>
              <Field
                className="admin-details__field"
                name="name"
                label={`${type === ruleTypes.CCAU_CAMPAIGN ? capitalize('campaign') : capitalize(type)} Title`}
                placeholder={`${type === ruleTypes.CCAU_CAMPAIGN ? capitalize('campaign') : capitalize(type)} Title`}
                component={InputTextField}
                disabled={this.formDisabled}
                validate={[
                  validation.min5,
                  validation.max100,
                  campaignName,
                  required,
                ]}
                autoComplete="off"
              />
              <Field
                className="admin-details__field"
                name="application"
                label="Application"
                placeholder="Select application&hellip;"
                component={InputSelectField}
                disabled={this.formDisabled}
                onChange={this.applicationChanged}
                options={apps
                  .filter(({ applicationId }) => {
                    return this.formDisabled ||
                      (access.containers[applicationId]?.manage?.length &&
                        (type === ruleTypesConstants.ALERT ||
                          (access.pages[applicationId]?.manage?.length && access.ruleSubTypes[applicationId]?.manage?.length)));
                  })
                  .map(({ name, applicationId }) => ({ id: applicationId, name }))
                }
                validate={[ required ]}
              />
            </Card>
            { this.renderCampaignTargetingOptions() }
            { this.isCampaign && formValues.type === 'wealth' && (
              <Field
                name="mass_targeting.wealth_lobs"
                component={LOBTargeting}
                application={formValues.application}
                disabled={this.formDisabled}
                isWealthCampaignSelected={true}
                validate={[ this.validateLOBSelection ]}
              />
            ) }
            { this.isCampaign && formValues.type === 'wealth' && formValues.mass_targeting?.wealth_lobs?.length > 0 && (
              <Field
                name="mass_targeting.by_demographic"
                component={DemographicTargeting}
                disabled={this.formDisabled}
                isWealthCampaignSelected={true}
                validate={[ this.validateDemographicSelection ]}
              />
            ) }
            { this.isCampaign && formValues.type === 'wealth' && (
              <Field
                name="mass_targeting.iclub_tiers"
                component={IClubTiersTargeting}
                disabled={this.formDisabled}
                isITradeLobSelected={formValues.mass_targeting?.wealth_lobs?.includes('SDBI')}
              />
            ) }
            { this.isCampaign && formValues.type === 'wealth' && (
              <Field
                name="mass_targeting.segment_ids"
                component={SegmentIdTargeting}
                disabled={this.formDisabled}
                isITradeLobSelected={formValues.mass_targeting?.wealth_lobs?.includes('SDBI')}
              />
            ) }
            { this.isCampaign && formValues.type === 'wealth' && (
              <Field
                name="mass_targeting.investment_knowledge"
                component={InvestingKnowledgeTargeting}
                disabled={this.formDisabled}
                isITradeLobSelected={formValues.mass_targeting?.wealth_lobs?.includes('SDBI')}
              />
            ) }
            { this.isCampaign && formValues.type === 'targeted' && (
              <Card className="admin-details__card" type="floatLow">
                <TextHeadline
                  component="h2"
                  size={21}
                  className="admin-details__sub-header"
                >
                  Targeting by Campaign ID
                </TextHeadline>
                <Field
                  required
                  name="external_ref"
                  label={this.props.type === ruleTypesConstants.CCAU_CAMPAIGN ? 'Campaign ID' : 'KT/PEGA Campaign ID'}
                  placeholder="Campaign ID"
                  component={InputTextField}
                  disabled={this.formDisabled}
                  validate={[
                    validation.min4,
                    validation.max15,
                    validation.noMessage,
                    validation.campaignId,
                  ]}
                  autoComplete="off"
                />
              </Card>
            ) }
            { /* Conditional rendering for Offer ID card */ }
            { this.isCampaign && formValues.type === 'offer' && (
              <Card className="admin-details__card" type="floatLow">
                <TextHeadline
                  component="h2"
                  size={21}
                  className="admin-details__sub-header"
                >
                  Targeting by Offer ID
                </TextHeadline>
                <Field
                  required
                  name="external_ref"
                  label="Offer ID"
                  placeholder="Enter Offer ID"
                  component={InputTextField}
                  disabled={this.formDisabled}
                  validate={[
                    validation.offerId,
                  ]}
                  autoComplete="off"
                />
              </Card>
            ) }
            <Card2SV formValues={formValues} isCampaign={this.isCampaign} formDisabled={this.formDisabled} />
            { this.renderAdvancedTargetingSection() }
            { this.renderRewardsTargetingSection() }
            <Card className="admin-details__card" type="floatLow">
              <TextHeadline
                component="h2"
                size={21}
                className="admin-details__sub-header"
              >
                Targeting Dimension
              </TextHeadline>
              <div className="admin-details__date-fields">
                <Field
                  className="admin-details__date-field"
                  required
                  name="start_at"
                  label="Start date"
                  placeholder="MM/DD/YYYY HH:MM AM/PM"
                  component={DateField}
                  disabled={this.formDisabled}
                  autoComplete="off"
                />
                <Field
                  className="admin-details__date-field"
                  required
                  name="end_at"
                  label="End date"
                  placeholder="MM/DD/YYYY HH:MM AM/PM"
                  component={DateField}
                  disabled={this.formDisabled}
                  autoComplete="off"
                />
              </div>
              { formValues.application && this.renderPlatforms(currentPlatforms, type) }
              <LanguageCard formChange={formChange} applications={applications} formValues={formValues} isCampaign={this.isCampaign} formDisabled={this.formDisabled}/>
            </Card>

            <div className='admin-details__placement'>
              <Field
                name="contentful"
                component={InputGroupField}
              >
                <TargetingMetadata
                  applications={this.props.applications}
                  containers={this.filteredContainersForApplicationAndRuleType}
                  deleteContent={this.props.deleteContent}
                  formDisabled={this.formDisabled}
                  formValues={formValues}
                  handleChange={this.props.updateDetails}
                  openContentModal={this.props.openContentModal}
                  pages={this.props.pages}
                  productBook={this.props.productBook}
                  type={this.props.type}
                  access={access}
                />
              </Field>
            </div>

            <div className="admin-details__action-bar">
              <BackButton type="button" onClick={this.props.onCancel}>Cancel</BackButton>
              <div className="admin-details__action-buttons">
                { ((!status || status === STATUS.DRAFT) && this.getRuleAction) &&
                  <SecondaryButton
                    className="admin-details__action-button"
                    onClick={this.props.onSubmit(STATUS.DRAFT)}>
                    Save as Draft
                  </SecondaryButton>
                }
                { status === STATUS.SUBMITTED && (type === ruleTypesConstants.CAMPAIGN || type === ruleTypesConstants.CCAU_CAMPAIGN) && this.getRuleAction.canReview &&
                  <SecondaryButton
                    type="button"
                    className="admin-details__action-button"
                    onClick={this.props.onApprove(true, false)}>
                    Reject
                  </SecondaryButton>
                }
                { ((status === STATUS.REVIEWED && (type === ruleTypesConstants.CAMPAIGN || type === ruleTypesConstants.CCAU_CAMPAIGN) && this.getRuleAction.canApprove) ||
                  (status === STATUS.SUBMITTED && type === ruleTypesConstants.ALERT && this.getRuleAction.canApprove)) &&
                  <SecondaryButton
                    type="button"
                    className="admin-details__action-button"
                    onClick={this.props.onPublish(false)}
                    disabled={isSelectedPlacementDisabled}
                    >
                    Reject
                  </SecondaryButton>
                }
                { ((!status || status === STATUS.DRAFT) && this.getRuleAction.canManage) &&
                  <PrimaryButton
                    className="admin-details__action-button"
                    onClick={this.submitForReview}
                    disabled={isSelectedPlacementDisabled}>
                    Submit for Review
                  </PrimaryButton>
                }
                { status === STATUS.SUBMITTED && (type === ruleTypesConstants.CAMPAIGN || type === ruleTypesConstants.CCAU_CAMPAIGN) && this.getRuleAction.canReview &&
                  <PrimaryButton
                    type="button"
                    className="admin-details__action-button details__action-button--approve"
                    onClick={this.approve(false)}
                    disabled={isSelectedPlacementDisabled}>
                    Approve
                  </PrimaryButton>
                }
                { ((status === STATUS.REVIEWED && (type === ruleTypesConstants.CAMPAIGN || type === ruleTypesConstants.CCAU_CAMPAIGN) && this.getRuleAction.canApprove) ||
                  (status === STATUS.SUBMITTED && type === ruleTypesConstants.ALERT && this.getRuleAction.canApprove)) &&
                    <PrimaryButton
                      type="button"
                      className="admin-details__action-button"
                      onClick={this.props.onPublish()}
                      disabled={isSelectedPlacementDisabled}
                      >
                      Publish
                    </PrimaryButton>
                }
              </div>
            </div>
          </form>
          <ModalDialogue
            headline="Are you sure?"
            primaryButtonLabel="Delete"
            primaryAction={this.props.onDelete}
            secondaryButtonLabel="Cancel"
            secondaryAction={this.closePopup}
            isModalVisible={this.state.popup === 'delete'}
            setModalVisible={this.closePopup}
          >
            This { type === ruleTypes.CCAU_CAMPAIGN ? capitalize('campaign') : type } will be deleted.
          </ModalDialogue>
          { this.renderApplicationSwitchPopup() }
          <ContentModalConnected
            type={this.props.type}
            onSave={this.props.updateDetails}
            container={this.containerSelected}
            contentfulSpace={currentApp?.contentful_space}
            pages={this.props.pages}
            isDismissible={formValues.dismissable_flag}
          />
        </div>
      </>
    );
  }
}
