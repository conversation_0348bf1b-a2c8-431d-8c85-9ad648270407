.admin-details {
  &__action-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 3.6rem;
  }

  &__action-right-bar {
    display: flex;
    align-items: center;
    justify-content: right;
    margin: 1rem;
    padding: 2rem;
    padding-right: 0;
  }

  &__header {
    &.TextIntroduction__text {
      font-size: 3.6rem;
    }
  }

  &__sub-header {
    margin-bottom: 3.6rem;

    &.TextIntroduction__text {
      font-size: 2.4rem;
    }
  }

  &__card {
    margin: 3.6rem 0;
  }

  &__field {
    margin-bottom: 2.5rem;
    max-width: 60rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__scene-fields {
    display: flex;
    flex-direction: column;

    @include mq($from: tablet) {
      flex-direction: row;
    }
  }

  &__scene-field {
    &:not(:last-child) {
      margin-bottom: 1.8rem;

      @include mq($from: tablet) {
        margin-right: 1.8rem;
      }
    }
  }

  &__date-fields {
    display: flex;
    margin-bottom: 3.6rem;
    flex-direction: column;

    @include mq($from: tablet) {
      flex-direction: row;
    }
  }

  &__date-field {
    &:not(:last-child) {
      margin-bottom: 1.8rem;

      @include mq($from: tablet) {
        margin-right: 1.8rem;
      }
    }
  }

  &__platforms {
    legend {
      line-height: 1.6rem;
    }

    .TooltipIcon__button--active {
      &::after {
        display: block;
        background-color: white;
        border-left: 0.1rem solid $canvas-gray-400;
        border-top: 0.1rem solid $canvas-gray-400;
        content: "";
        height: 1.8rem;
        left: 0;
        position: absolute;
        top: 100%;
        width: 1.8rem;
        z-index: 2;
        transform: rotate(45deg) translate(0.4rem, 0.4rem);
      }
    }

    .Checkbox__container {
      display: inline-block;
    }
  }

  &__language {
    margin-top: 4rem;
  }

  &__action-buttons {
    display: flex;
  }

  &__action-button {
    margin-left: 3rem;

    &:first-child {
      margin-left: 0;
    }
  }

  &__action-info {
    height: 0;
    margin-top: 1rem;
  }

  &__content-table {
    .TableHead {
      padding-top: 0;
    }

    table-layout: auto;
    white-space: normal;

    .table__header-item {
      text-transform: uppercase;
    }

    .TableBody__cell {
      align-items: center;
    }

    .StandaloneLink {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .admin-details__table-list {
    list-style-type: none;
    word-break: break-all;
  }

  &__campaign-enrollment_tooltip_item {
    margin-bottom: 2rem;
  }

  .admin-details__table-list-item {
    &:not(:last-child) {
      padding-bottom: 0.5rem;
    }
  }

  .form__input-group {
    padding: 0;
    margin: 0;
  }

  &__group-legend {
    margin-bottom: 1.6rem;
  }

  &__autosuggest-table {
    @include mq($from: tablet) {
      display: flex;
    }
  }

  &__autosuggest {
    @extend .admin-details__field;
  }

  &__autosuggest-column {
    & > div {
      margin-bottom: 1.6rem;
    }

    @include mq($from: tablet) {
      width: calc(50% - 1.8rem / 2);

      & > div:not(:last-child) {
        margin-bottom: 1.6rem;
      }

      &:first-child {
        margin-right: 1.8rem;
      }
    }
  }

  &__radio-container {
    display: flex;
    align-items: center;

    .form__input--radio,
    .RadioButton__container { // new Canvas radio button
      margin-top: 0;

      &:not(:first-child) {
        margin-left: 2.6rem;
      }
    }
  }

  &__no-legend {
    display: flex;
    align-items: center;
    margin-top: 1.8rem;
    margin-bottom: 0;

    & > :not(:first-child) {
      margin-left: 2.6rem;
    }
  }

  legend.input-group__legend {
    margin-bottom: 1rem;
  }

  &__modal-footer {
    display: flex;
    justify-content: flex-end;
  }

  &__modal-button {
    &:not(:last-child) {
      margin-right: 1.2rem;
    }

    min-height: 4.8rem;
  }

  &__placement {
    .Error__container {
      margin-left: 3.6rem;
      position: relative;
      top: -8rem;
    }
  }

  .ToggleSwitch__label {
    padding-top: 0.9rem;
  }

  &__field_2sv_options {
    max-width: unset;
  }
}
