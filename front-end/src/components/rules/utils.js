import { get, omit, values } from 'lodash';
import {
  ruleSubTypesDisplayName as ruleSubType,
  ruleTypes,
} from '../../constants';
import allPermissions from '../../constants/permissionsList';
import { formatWord } from '../../utils';

const getProductPlacements = (campaignDetailsForm = {}) => {
  const isTargeted =
    ruleSubType.targeted === ruleSubType[campaignDetailsForm.type];
  const isMass = ruleSubType.mass === ruleSubType[campaignDetailsForm.type];
  let result = [];

  if (isTargeted) {
    result = campaignDetailsForm.mass_targeting?.product_pages?.any_of || [];
  } else if (isMass) {
    const activeSelectionTouched = !!campaignDetailsForm.advancedTargeting;
    if (activeSelectionTouched) {
      result = campaignDetailsForm.advancedTargeting?.include || [];
    } else {
      const productTargeting = campaignDetailsForm.mass_targeting?.by_product;
      const potentialTargetingQueries = (productTargeting && Object.keys(productTargeting)) || [];
      const targetingQuery = potentialTargetingQueries.length > 0 && potentialTargetingQueries[0];
      result = (targetingQuery && productTargeting[targetingQuery]) || [];
    }
  }
  return result;
};

/**
 * check if the current user can ( duplicate , review , approve , ..etc)
 * @param {Object} access - The access object which contains all the access for the containers and pages
 * @param {string[]} permissions - array of the current user's permissions
 * @param {"campaign"|"alert"} type - type of the rule
 * @param {object} rule - rule object
 * @returns {object} -

 */

const canEditRule = (access, permissions, type, rule) => {
  const { containers, pages, ruleSubTypes } = access;
  // To check if the user has the access to edit , review or approve this rule  we need to check two things
  // 1- if this user's team has the correct access so we check the access first
  // 2- if the user has the correct role so we check the permissions for this user
  if (!rule) {
    const key = type === ruleTypes.CCAU_CAMPAIGN ? ruleTypes.CCAU_CAMPAIGN : formatWord(type, { plural: true });
    const { manageAlert, manageCampaigns, manageCCAUCampaigns } = canViewManageRuleType({ [key]: access }, permissions);
    switch (type) {
      case ruleTypes.CAMPAIGN:
        return manageCampaigns;
      case ruleTypes.CCAU_CAMPAIGN:
        return manageCCAUCampaigns;
      case ruleTypes.ALERT:
        return manageAlert;
    }
  }

  const hasContainerAccess = containers[rule.application] && rule.container ? [ ...(containers[rule.application].manage || []) ].includes(rule.container) : true;
  // If rule.container is null then we are duplciating a rule and page would be an empty array
  // Add logic to check rule.pages.length to handle empty pages bug: PIGEON-5022
  const hasPageAccess = pages[rule.application] && rule.container ? [ ...(pages[rule.application].manage || []) ].some(page => rule.pages?.length > 0 ? rule.pages.includes(page) : true) : true;

  if (type === ruleTypes.CAMPAIGN) {
    const type = rule.external_ref ? rule.external_ref : rule.type;
    const typeList = [ 'MASS', 'MESSAGE', 'WEALTH', 'mass', 'message' , 'wealth'  ].some(ref => ref === type) ? type.toLowerCase() : 'targeted';
    const ruleSubTypeAccessArr = [ ...(ruleSubTypes[rule.application]?.manage || []) ];
    const checkRuleSubType = ruleSubTypeAccessArr.includes(typeList);
    const hasRuleSubTypeAccess = ruleSubTypes[rule.application] && checkRuleSubType;
    const userTeamHasEditAccessForCampaigns = hasContainerAccess && hasPageAccess && hasRuleSubTypeAccess;

    return {
        canManage: !!(permissions['campaigns_manage'] && userTeamHasEditAccessForCampaigns),
        canReview: !!(permissions['campaigns_review'] && userTeamHasEditAccessForCampaigns),
        canApprove: !!(permissions['campaigns_approve'] && userTeamHasEditAccessForCampaigns),
      };
  }
  if (type === ruleTypes.CCAU_CAMPAIGN) {
    const hasRuleSubTypeAccess = ruleSubTypes[rule.application] && [ ...(ruleSubTypes[rule.application].manage || []) ].includes([ 'MASS', 'MESSAGE' ].some(ref => ref === rule.external_ref) ? rule.external_ref.toLowerCase() : 'targeted');
    const userTeamHasEditAccessForCampaigns = hasContainerAccess && hasPageAccess && hasRuleSubTypeAccess;

    return {
      canManage: !!(permissions['ccau_campaigns_manage'] && userTeamHasEditAccessForCampaigns),
      canReview: !!(permissions['ccau_campaigns_review'] && userTeamHasEditAccessForCampaigns),
      canApprove: !!(permissions['ccau_campaigns_approve'] && userTeamHasEditAccessForCampaigns),
    };
  }
  if (ruleTypes.ALERT) {
    const userTeamHasEditAccessForAlerts = hasContainerAccess && (!rule.pages?.length || hasPageAccess);
    return {
      canManage: !!(permissions['alerts_manage'] && userTeamHasEditAccessForAlerts),
      canApprove: !!(permissions['alerts_approve'] && userTeamHasEditAccessForAlerts),
    };
  }
};

/**
 * This function returns group of flags to determine the user ability
 * @param {Object} access - The access object which contains all the access for the containers and pages
 * @param {string[]} permissions - array of the current user's permissions
 */
const canViewManageRuleType = (access, permissions) => {
  // View Campaigns
  const viewContainersAndPagesCampaigns = values(omit(access.campaigns?.pages || {}, [ 'sol', 'storefront' ])).some(p => p.view || p.manage) &&
  values(omit(access.campaigns?.containers || {}, [ 'sol', 'storefront' ])).some(c => c.view || c.manage);

  // View CCAU Campaigns
  const viewContainersAndPagesCCAUCampaigns = values(access.ccau_campaign?.pages || {}).some(p => p.view || p.manage) &&
  values(access.ccau_campaign?.containers || {}).some(c => c.view || c.manage);

  // View SOL
  const viewContainersAndPagesSOL = !!get(access, 'campaigns.pages.sol', false) &&
  !!get(access, 'campaigns.containers.sol', false);

  // View StoreFront
  const viewContainersAndPageStoreFront = !!get(access, 'campaigns.pages.storefront', false) &&
  !!get(access, 'campaigns.containers.storefront', false);

  // View Alerts
  const viewContainersAndPageAlerts = values(access.alerts?.pages || {}).some(p => p.view || p.manage) &&
  values(access.alerts?.containers || {}).some(c => c.view || c.manage);

  // Manage Campaigns
  const manageContainersAndPagesCampaigns = values(omit(access.campaigns?.pages || {}, [ 'sol', 'storefront' ])).some(p => p.manage) &&
  values(omit(access.campaigns?.containers || {}, [ 'sol', 'storefront' ])).some(c => c.manage);

  // Manage CCAU Campaigns
  const manageContainersAndPagesCCAUCampaigns = values(access.ccau_campaign?.pages || {}).some(p => p.manage) &&
  values(access.ccau_campaign?.containers || {}).some(c => c.manage);

  // Manage SOL
  const manageContainersAndPagesSOL = !!get(access, 'campaigns.pages.sol', false) &&
  !!get(access, 'campaigns.containers.sol', false);

  // Manage StoreFront
  const manageContainersAndPageStoreFront = !!get(access, 'campaigns.pages.storefront', false) &&
  !!get(access, 'campaigns.containers.storefront', false);

  // Manage Alerts
  const manageContainersAndPageAlerts = values(access.alerts?.pages || {}).some(p => p.manage) &&
  values(access.alerts?.containers || {}).some(c => c.manage);

  return {
    viewCampaigns: viewContainersAndPagesCampaigns && permissions[allPermissions.CAMPAIGNS_VIEW],
    viewCCAUCampaigns: viewContainersAndPagesCCAUCampaigns && permissions[allPermissions.CCAU_CAMPAIGNS_VIEW],
    viewSOL: viewContainersAndPagesSOL && permissions[allPermissions.CAMPAIGNS_VIEW],
    viewStoreFront: viewContainersAndPageStoreFront && permissions[allPermissions.CAMPAIGNS_VIEW],
    viewAlert: viewContainersAndPageAlerts && permissions[allPermissions.ALERTS_VIEW],
    manageCampaigns: manageContainersAndPagesCampaigns && permissions[allPermissions.CAMPAIGNS_MANAGE],
    manageCCAUCampaigns: manageContainersAndPagesCCAUCampaigns && permissions[allPermissions.CCAU_CAMPAIGNS_MANAGE],
    manageSOL: manageContainersAndPagesSOL && permissions[allPermissions.CAMPAIGNS_MANAGE],
    manageStoreFront: manageContainersAndPageStoreFront && permissions[allPermissions.CAMPAIGNS_MANAGE],
    manageAlert: manageContainersAndPageAlerts && permissions[allPermissions.ALERTS_MANAGE],
  };
};

export { getProductPlacements, canEditRule, canViewManageRuleType };
