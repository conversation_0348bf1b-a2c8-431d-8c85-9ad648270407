import React from 'react';
import PropTypes from 'prop-types';
import { render, fireEvent, within } from '@testing-library/react';
import { shallow } from 'enzyme';
import mockAxios from 'axios';
import moment from 'moment';
import { cloneDeep } from 'lodash';

import { createStore, applyMiddleware, compose } from 'redux';
import { Provider } from 'react-redux';
import { reduxForm } from 'redux-form';
import thunk from 'redux-thunk';
import rootReducer from '../../store/reducers';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import PrimaryButton from 'canvas-core-react/lib/PrimaryButton';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import PillButton from 'canvas-core-react/lib/PillButton';
import Modal from 'canvas-core-react/lib/internal/Modal';
import {
  STATUS,
  ruleTypes as ruleTypesConstants,
  mapObjectToArray,
} from '../../constants';
import Details from './details';
import { DetailsContainer, validate } from './detailsContainer';
import { AssignmentAutosuggest } from '../autosuggest';
import LOBTargeting from './DetailsCards/wealthTargeting/lobTargeting';

global.fetch = () => ({ json: () => {} });

const applications = [
  {
    'applicationId': 'nova',
    'id': 1,
    'name': 'Nova Mobile',
    'platformIds': [ 2, 3 ], // ios, android
    'platforms': [ 'iOS', 'Android' ],
    'ruleTypeIds': [ 1, 2 ], // alert, campaign
    'ruleTypes': [ 'alert', 'campaign' ],
    'ruleSubTypeIds': [ 1, 2, 3, 4 ],
    'ruleSubTypes': [ 'targeted', 'mass', 'message', 'offer' ],
    'rule_version': 1,
    'status': true,
    'contentful_space': '4szkx38resvm',
  },
  {
    'applicationId': 'phoenix',
    'id': 2,
    'name': 'Phoenix',
    'platformIds': [ 1 ], // web
    'platforms': [ 'Web' ],
    'ruleTypeIds': [ 1, 2 ], // alert, campaign
    'ruleTypes': [ 'alert', 'campaign' ],
    'ruleSubTypeIds': [ 1, 2 ],
    'ruleSubTypes': [ 'targeted', 'mass' ],
    'rule_version': 1,
    'status': true,
    'contentful_space': '4szkx38resvm',
  },
];

const containers = [
  {
    'application': 1, // nova
    'containerId': 'my-activity',
    'content_type': [ 'standingCampaign' ],
    'id': 1,
    'name': 'my-activity',
    'pages': [ 1 ], // activities
    'rule_type': 'campaign',
    'status': true,
  },
  {
    'application': 1, // nova
    'containerId': 'offers-and-programs',
    'content_type': [ 'targetedCampaign' ],
    'id': 2,
    'name': 'offers-and-programs',
    'pages': [ 2, 3 ], // accounts, account-key
    'rule_type': 'campaign',
    'status': true,
  },
];

const pages = [
  {
    id: 1,
    application: 1,
    name: 'activities',
    pageId: 'activities',
    status: true,
    description: 'NOVA mobile - My Activity tab',
    containers: [
      1,
      68,
    ],
  },
  {
    id: 2,
    application: 1,
    name: 'accounts',
    pageId: 'accounts',
    status: true,
    description: 'NOVA mobile - My Accounts tab',
    containers: [
      2,
      37,
    ],
  },
  {
    id: 3,
    application: 1,
    name: 'account-key',
    pageId: 'account-key',
    status: true,
    description: 'NOVA mobile - My Accounts tab',
    containers: [
      2,
    ],
  },
];

const productBook = [
  {
    'category': 'banking',
    'code': 'DDA',
    'description': 'Current Account (B:DDA:CA)',
    'id': '100060',
    'ownership': 'B',
    'sub_code': 'CA',
  }, {
    'category': 'investing',
    'code': 'TFS',
    'description': 'Tax-Free Savings - BNS (B:TFS:SB)',
    'id': '340860',
    'ownership': 'B',
    'sub_code': 'SB',
  }, {
    'category': 'borrowing',
    'code': 'AFP',
    'description': 'Future Amex Platinum (B:AFP)',
    'id': '620020',
    'ownership': 'B',
  },
];

const platforms = [
  { 'id': 1, 'name': 'Web', 'slug': 'web' },
  { 'id': 2, 'name': 'iOS', 'slug': 'ios' },
  { 'id': 3, 'name': 'Android', 'slug': 'android' },
];

const ruleTypes = [
  { 'id': 1, 'rule_type': 'alert', 'slug': 'alert' },
  { 'id': 2, 'rule_type': 'campaign', 'slug': 'campaign' },
  { 'id': 3, 'rule_type': 'vignette', 'slug': 'vignette' },
  { 'id': 4, 'rule_type': 'vignette-broadcast', 'slug': 'vignette-broadcast' },
  { 'id': 5, 'rule_type': 'vignette-priority', 'slug': 'vignette-priority' },
  { 'id': 7, 'rule_type': 'estore', 'slug': 'estore' },
];

const ruleSubTypes = [
  { id: 1, type: 'targeted', description: 'Targeted Campaign' },
  { id: 2, type: 'mass', description: 'Mass Campaign' },
  { id: 3, type: 'message', description: 'Mass Message' },
  { id: 4, type: 'offer', description: 'Offer Campaign' },
];

const mappedRuleSubTypes = {
  1: { id: 1, type: 'targeted', description: 'Targeted Campaign' },
  2: { id: 2, type: 'mass', description: 'Mass Campaign' },
  3: { id: 3, type: 'message', description: 'Mass Message' },
  4: { id: 4, type: 'offer', description: 'Offer Campaign' },
};

const users = {
  1: {
    email: '<EMAIL>',
    id: 1,
    name: 'John Doe',
    roles: [ 1 ],
    sid: 's0000001',
  },
  2: {
    email: '<EMAIL>',
    id: 2,
    name: 'Jane Doe',
    roles: [ 1 ],
    sid: 's0000002',
  },
};

const access = {
  containers: {
    nova: { view: [ 'offers-and-programs' ], manage: [ 'offers-and-programs' ] },
    phoenix: { view: [ 'phoenix-alert' ], manage: [ 'phoenix-alert' ] },
    starburst: { view: [ 'offers-and-programs' ], manage: [ 'offers-and-programs' ] },
    atlantis: { view: [ 'offers-and-programs' ], manage: [ 'offers-and-programs' ] },
  },
  pages: {
    nova: { view: [ 'accounts', 'account-key' ], manage: [ 'accounts', 'account-key' ] },
    phoenix: { view: [ 'lgn-12345' ], manage: [ 'lgn-12345' ] },
    starburst: { view: [ 'accounts', 'account-key' ], manage: [ 'accounts', 'account-key' ] },
    atlantis: { view: [ 'accounts', 'account-key' ], manage: [ 'accounts', 'account-key' ] },
  },
  ruleSubTypes: {
    nova: { view: [ 'targeted', 'mass', 'message', 'offer' ], manage: [ 'targeted', 'mass', 'message', 'offer' ] },
    phoenix: { view: [ 'targeted', 'mass' ], manage: [ 'targeted', 'mass' ] },
    starburst: { view: [ 'targeted', 'mass', 'message', 'wealth' ], manage: [ 'targeted', 'mass', 'message', 'wealth' ] },
    atlantis: { view: [ 'targeted', 'mass', 'message', 'wealth' ], manage: [ 'targeted', 'mass', 'message', 'wealth' ] },
  },
};

// Redux form values
const alertInitialValues = {
  platforms: {},
  application: 'nova',
  platforms_targeting: [],
  mass_targeting: {
    enrollment_status: [],
  },
};

const campaignInitialValues = {
  platforms: {},
  type: 'targeted',
  ownershipRelationship: 'or',
  advancedTargetingRelationship: 'or',
  platforms_targeting: [],
};

const offerInitialValues = {
  platforms: {},
  application: 'nova',
  type: 'offer',
  ownershipRelationship: 'or',
  advancedTargetingRelationship: 'or',
  platforms_targeting: [],
};

const campaignCompletedValues = {
  ...campaignInitialValues,
  application: 'nova',
  assignees: [ { full_name: 'John Doe', sid: 's0000001' } ],
  container: 'offers-and-programs',
  content_id: 'test-content-id',
  content_type: 'targetedCampaign',
  external_ref: 'ABC123',
  name: 'test-campaign-title',
  platforms: { ios: true, android: true },
  pages: [ 'accounts', 'account-key' ],
  start_at: '2021-05-01T00:00:00.000Z',
  end_at: '2025-01-01T00:00:00.000Z',
  status: STATUS.DRAFT,
  disabled: false,
  id: 'test-campaign-id',
  dismissable_flag: false,
  mass_targeting: {
    by_scene_points: { targeting_criteria: 'greater', points: 9320 },
    by_product: { any_of: [ productBook[0], productBook[1] ] },
    product_pages: { any_of: [
      productBook[0],
      productBook[1],
      { ownership: 'B', code: 'out-of-date-product' },
    ] },
    enrollment_status: [],
    languages: [],
  },
};

const commonDetailsProps = {
  applications: applications,
  assignees: [ ],
  containers: {
    isLoading: false,
    items: containers,
  },
  deleteContent: jest.fn(),
  formChange: jest.fn(),
  formReset: jest.fn(),
  formValid: false,
  formValues: campaignInitialValues,
  isDuplicating: false,
  onActivate: jest.fn(),
  onApprove: () => jest.fn(),
  onCancel: jest.fn(),
  onDelete: jest.fn(),
  onDuplicate: jest.fn(),
  onPublish: jest.fn(),
  onSubmit: () => jest.fn(),
  onTerminate: jest.fn(),
  addAlert: jest.fn(),
  removeAlert: jest.fn(),
  openContentModal: jest.fn(),
  pages: {
    isLoading: false,
    items: pages,
  },
  access,
  permissions: { admin: true },
  platforms: platforms,
  productBook: [ ],
  ruleTypes: ruleTypes,
  ruleSubTypeMap: mappedRuleSubTypes,
  status: undefined,
  title: 'Create a New Campaign',
  type: ruleTypesConstants.CAMPAIGN,
  updateDetails: jest.fn(),
  users: {
    isLoading: false,
    items: users,
  },
};

const commonDetailsContainerProps = {
  addAlert: jest.fn(),
  addSnackbar: jest.fn(),
  applications: applications,
  containers: { isLoading: false, items: containers },
  pages: { isLoading: false, items: pages },
  formChange: jest.fn(),
  formReset: jest.fn(),
  formValues: campaignInitialValues,
  getApplications: jest.fn(),
  getContainers: jest.fn(),
  getPages: jest.fn(),
  getPlatforms: jest.fn(),
  getRuleTypes: jest.fn(),
  getRuleSubTypes: jest.fn(),
  getUsers: jest.fn(),
  handleSubmit: fn => fn,
  history: { goBack: jest.fn(), push: jest.fn(), replace: jest.fn(), listen: jest.fn() },
  match: { params: { id: '', action: '' } },
  location: { pathname: '' },
  openContentModal: jest.fn(),
  platforms: platforms,
  access,
  permissions: { admin: true, campaigns_manage: true },
  populateDetails: jest.fn(),
  removeAlert: jest.fn(),
  ruleTypes: ruleTypes,
  ruleSubTypes: ruleSubTypes,
  type: ruleTypesConstants.CAMPAIGN,
  updateDetails: jest.fn(),
  users: { isLoading: false, items: users },
  valid: true,
};

mockAxios.get.mockImplementation((url) => {
  if (url.includes('/product-book')) {
    return Promise.resolve({
      config: {},
      data: productBook,
      status: 200,
    });
  } else if (url.includes('/contents/spaces/')) {
    return Promise.resolve({ data: { items: [ ] }, status: 200 });
  } else if (url.includes('/containers')) {
    return Promise.resolve({
      config: {},
      data: mapObjectToArray(containers),
      status: 200,
    });
  } else {
    return Promise.resolve({ data: { }, status: 200 });
  }
});

mockAxios.post.mockImplementation((url) => {
  if (url.includes('/alert-rules')) {
    return Promise.resolve({
      data: { id: 'new-alert-id' },
      status: 200,
    });
  } else {
    return Promise.resolve({ data: { }, status: 200 });
  }
});

describe('Details Snapshot - Create a New Alert (undefined status)', () => {
  const wrapper = shallow(
    <Details
      {...commonDetailsProps}
      formValues={alertInitialValues}
      permissions={{ alerts_manage: true }}
      title={'Create a New Alert'}
      type={ruleTypesConstants.ALERT}
    />,
  );
  global.snapshot(wrapper);
});

describe('Details Snapshot - Edit a Campaign (draft status)', () => {
  const wrapper = shallow(
    <Details
      {...commonDetailsProps}
      formValues={campaignCompletedValues}
      permissions={{ campaigns_manage: true }}
      status={STATUS.DRAFT}
      title="Edit a Campaign"
    />,
  );
  global.snapshot(wrapper);

  it('submit for review class member function should be called', () => {
    const instance = wrapper.instance();
    const preventDefaultMock = jest.fn();
    instance.submitForReview({
      preventDefault: preventDefaultMock,
    });
    expect(preventDefaultMock).toHaveBeenCalled();
  });
});

describe('Details Snapshot - Edit a Campaign (draft status) CCAU', () => {
  const wrapper = shallow(
    <Details
      {...commonDetailsProps}
      formValues={campaignCompletedValues}
      permissions={{ campaigns_manage: true }}
      status={STATUS.DRAFT}
      title="Edit a Campaign"
      type={ruleTypesConstants.CCAU_CAMPAIGN}
    />,
  );
  global.snapshot(wrapper);

  it('submit for review class member function should be called', () => {
    const instance = wrapper.instance();
    const preventDefaultMock = jest.fn();
    instance.submitForReview({
      preventDefault: preventDefaultMock,
    });
    expect(preventDefaultMock).toHaveBeenCalled();
  });
});

describe('Details Snapshot - View a Campaign (draft status)', () => {
  const viewOnlyAccess = JSON.parse(JSON.stringify(access));
  [ 'containers', 'pages', 'ruleSubTypes' ].forEach(accessType => {
    delete viewOnlyAccess[accessType].nova.manage;
    delete viewOnlyAccess[accessType].phoenix.manage;
  });
  const wrapper = shallow(
    <Details
      {...{ ...commonDetailsProps, access: viewOnlyAccess }}
      formValues={campaignCompletedValues}
      permissions={{ 'campaigns:create': true }}
      status={STATUS.DRAFT}
      title="Edit a Campaign"
    />,
  );
  global.snapshot(wrapper);

  it('submit for review class member function should be called', () => {
    const instance = wrapper.instance();
    const preventDefaultMock = jest.fn();
    instance.submitForReview({
      preventDefault: preventDefaultMock,
    });
    expect(preventDefaultMock).toHaveBeenCalled();
  });
});

describe('Details Snapshot - View a Campaign (draft status) CCAU', () => {
  const viewOnlyAccess = JSON.parse(JSON.stringify(access));
  [ 'containers', 'pages', 'ruleSubTypes' ].forEach(accessType => {
    delete viewOnlyAccess[accessType].nova.manage;
    delete viewOnlyAccess[accessType].phoenix.manage;
  });
  const wrapper = shallow(
    <Details
      {...{ ...commonDetailsProps, access: viewOnlyAccess, type: ruleTypesConstants.CCAU_CAMPAIGN }}
      formValues={campaignCompletedValues}
      permissions={{ 'campaigns:create': true }}
      status={STATUS.DRAFT}
      title="Edit a Campaign"
    />,
  );
  global.snapshot(wrapper);

  it('submit for review class member function should be called', () => {
    const instance = wrapper.instance();
    const preventDefaultMock = jest.fn();
    instance.submitForReview({
      preventDefault: preventDefaultMock,
    });
    expect(preventDefaultMock).toHaveBeenCalled();
  });
});

describe('Details Snapshot - View a Campaign (submitted status)', () => {
  const wrapper = shallow(
    <Details
      {...commonDetailsProps}
      formValues={{ ...campaignCompletedValues, status: STATUS.SUBMITTED }}
      permissions={{ 'campaigns_manage': true }}
      status={STATUS.SUBMITTED}
      title="View a Campaign"
    />,
  );
  global.snapshot(wrapper);
});

describe('Details Snapshot - View a Campaign (submitted status) CCAU', () => {
  const wrapper = shallow(
    <Details
      {...commonDetailsProps}
      formValues={{ ...campaignCompletedValues, status: STATUS.SUBMITTED }}
      permissions={{ 'campaigns_manage': true }}
      status={STATUS.SUBMITTED}
      title="View a Campaign"
      type={ruleTypesConstants.CCAU_CAMPAIGN}
    />,
  );
  global.snapshot(wrapper);
});

describe('Details Snapshot - View a Campaign (published status)', () => {
  const wrapper = shallow(
    <Details
      {...commonDetailsProps}
      formValues={{ ...campaignCompletedValues, status: STATUS.PUBLISHED }}
      permissions={{ 'campaigns_manage': true }}
      status={STATUS.PUBLISHED}
      title="View a Campaign"
    />,
  );
  global.snapshot(wrapper);
});

describe('Details Snapshot - View a Campaign (published status, inactive)', () => {
  const wrapper = shallow(
    <Details
      {...commonDetailsProps}
      formValues={{ ...campaignCompletedValues, status: STATUS.PUBLISHED, disabled: true }}
      permissions={{}}
      status={STATUS.PUBLISHED}
      title="View a Campaign"
    />,
  );
  global.snapshot(wrapper);
});

describe('Details Container Snapshot - Create a New Campaign (undefined status)', () => {
  const mockReplace = jest.fn();
  const wrapper = shallow(
    <DetailsContainer {...commonDetailsContainerProps}
      formValues={alertInitialValues}
      history={{
        replace: mockReplace,
      }}
      match={{ params: {} }}
      type={ruleTypesConstants.ALERT}
    />,
  );

  global.snapshot(wrapper);
  const instance = wrapper.instance();
  let values = { ...alertInitialValues };
  mockAxios.reset();

  it('All Errors', () => {
    expect(validate(values, commonDetailsContainerProps)).toMatchSnapshot();
  });

  it('Start/end date validation', () => {
    values = {
      ...values,
      start_at: moment('2018-07-13T00:00:00.000Z'),
      end_at: moment('2018-07-12T00:00:00.000Z'),
    };
    expect(instance.formatValues(values)).toMatchSnapshot();
    expect(validate(values, commonDetailsContainerProps).end_at).toBeTruthy();
  });

  // test loses its meaning with dynamic platforms

  it('does not fail platform validation if application is phoenix', () => {
    values = {
      ...values,
      application: 'phoenix',
    };
    expect(instance.formatValues(values)).toMatchSnapshot();
    const platformErrors = validate(values, commonDetailsContainerProps);
    expect(platformErrors.platform).toBeFalsy();
  });

  it('App version validation', () => {
    values = {
      ...values,
      platforms: { ios: true, android: true },
      pages: [ 'test' ],
      app_version: '1.1',
    };
    expect(instance.formatValues(values)).toMatchSnapshot();
    const appVersionErrors = validate(values, commonDetailsContainerProps);
    expect(appVersionErrors.platform).toBeFalsy();
  });

  it('Add enrollmentStatus ', () => {
    values = {
      ...values,
      end_at: moment('2018-07-14T00:00:00.000Z'),
      mass_targeting: {
        ...values.mass_targeting,
        enrollmentStatus: [ 'null' ],
        device_lock: 'none',
      },
    };
    expect(instance.formatValues(values)).toMatchSnapshot();
    expect(validate(values, commonDetailsContainerProps).contentful).toBeTruthy();
  });

  it('Container validation', () => {
    values = {
      ...values,
      end_at: moment('2018-07-14T00:00:00.000Z'),
    };
    expect(instance.formatValues(values)).toMatchSnapshot();
    expect(validate(values, commonDetailsContainerProps).contentful).toBeTruthy();
  });

  it('No error validation', () => {
    values = {
      ...values,
      name: 'test-name',
      external_ref: 'test-external-ref',
      container: 'container',
      mass_targeting: {
        ...values.mass_targeting,
        device_lock: 'none',
        product_pages: { any_of: [
          { ...productBook[1], label: 'B:TFS:SB Tax-Free Savings - BNS (B:TFS:SB)' },
          { ...productBook[0], label: 'B:DDA:CA Current Account (B:DDA:CA)' },
        ] },
      },
      content_id: 'test-container',
    };
    expect(instance.formatValues(values)).toMatchSnapshot();
    expect(validate(values, commonDetailsContainerProps)).toStrictEqual({});
  });

  it('submit the new alert', () => {
    instance.submit()(values);
  });

  it('should have sent post request with the formatted data and route to the newly created alert', () => {
    const formattedValues = {
      name: 'test-name',
      start_at: '2018-07-13T00:00:00.000Z',
      end_at: '2018-07-14T00:00:00.000Z',
      pages: [ 'test' ],
      platforms: [ 'ios', 'android' ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: undefined,
      content_id: 'test-container',
      container: 'container',
      targeting: { products: [] },
      status: 'submitted',
      application: 'phoenix',
      mass_targeting: {
        enrollment_status: [],
        languages: [],
      },
      platforms_targeting: [ { platform: 'ios', items: [] }, { platform: 'android', items: [] } ],
    };
    expect(mockAxios.post).toHaveBeenCalledWith('/alert-rules', formattedValues, undefined);
    expect(mockReplace).toHaveBeenCalledWith('/alerts/new-alert-id');
  });

    it('alerts when application is phoenix to make sure page is required', () => {
    // Test phoenix alert without pages - should fail validation
    values = {
      ...values,
      application: 'phoenix',
      pages: undefined,
    };
    const alertProps = {
      ...commonDetailsContainerProps,
      type: ruleTypesConstants.ALERT,
    };
    const validationErrors = validate(values, alertProps);
    expect(validationErrors.contentful).toBe('Page is required');

    // Test phoenix alert with empty pages array - should fail validation
    values = {
      ...values,
      application: 'phoenix',
      pages: [],
    };
    const validationErrorsEmpty = validate(values, alertProps);
    expect(validationErrorsEmpty.contentful).toBe('Page is required');

    // Test phoenix alert with pages - should pass validation
    values = {
      ...values,
      application: 'phoenix',
      pages: [ 'lgn-12345' ],
      content_id: 'test-content',
    };
    const validationErrorsWithPages = validate(values, alertProps);
    expect(validationErrorsWithPages.contentful).toBeFalsy();

    // Test non-phoenix alert without pages - should pass validation (pages not required for other apps)
    values = {
      ...values,
      application: 'nova',
      pages: undefined,
      content_id: 'test-content',
    };
    const validationErrorsNonPhoenix = validate(values, alertProps);
    expect(validationErrorsNonPhoenix.contentful).toBeFalsy();
  });
});

describe('Details Container Snapshot - Edit a Campaign (draft status)', () => {
  const pushMock = jest.fn();
  const wrapper = shallow(
    <DetailsContainer
      {...commonDetailsContainerProps}
      history={{
        push: pushMock,
      }}
      formValues={campaignCompletedValues}
      match={{ params: { id: 'test-id' } }}
      permissions={{ 'campaigns_manage': true }}
      status={STATUS.DRAFT}
    />,
  );

  global.snapshot(wrapper);
  const instance = wrapper.instance();
  mockAxios.reset();
  let values = {
    ...campaignCompletedValues,
    type: 'mass',
    banking: [ {
      'category': 'banking',
      'code': 'DDA',
      'description': 'Current Account (B:DDA:CA)',
      'id': '100060',
      'ownership': 'B',
      'sub_code': 'CA',
    } ],
    businessOwnership: true,
    external_ref: 'MASS',
  };

  it('submit the draft mass campaign', () => {
    instance.submit()(values);
    const formattedValues = {
      name: 'test-campaign-title',
      start_at: '2021-05-01T00:00:00.000Z',
      end_at: '2025-01-01T00:00:00.000Z',
      platforms: [ 'ios', 'android' ],
      app_version: null,
      content_space: '4szkx38resvm',
      content_type: 'targetedCampaign',
      content_id: 'test-content-id',
      container: 'offers-and-programs',
      dismissable_flag: false,
      targeting: { products: [] },
      status: 'submitted',
      application: 'nova',
      mass: true,
      mass_targeting: campaignCompletedValues.mass_targeting,
      platforms_targeting: [ { platform: 'ios', items: [] }, { platform: 'android', items: [] } ],
      external_ref: 'MASS',
      pages: campaignCompletedValues.pages,
      urgent: undefined,
    };
    expect(mockAxios.put).toHaveBeenCalledWith('/campaign-rules/test-id', formattedValues, undefined);
  });

  it('delete the draft camapgin', () => {
    instance.delete();
  });

  it('should have navigated back', () => {
    expect(pushMock).toHaveBeenCalledWith('/campaigns');
  });
});

describe('Details Container Snapshot - View a Campaign (submitted status)', () => {
  const wrapper = shallow(
    <DetailsContainer
      {...commonDetailsContainerProps}
      formValues={{ ...campaignCompletedValues, status: STATUS.SUBMITTED }}
      match={{ params: { id: 'test-id' } }}
      permissions={{ 'campaigns_manage': true }}
      status={STATUS.SUBMITTED}
    />,
  );

  global.snapshot(wrapper);
  const instance = wrapper.instance();
  mockAxios.reset();
  let values = {
    ...campaignCompletedValues,
    newAssignees: [ { full_name: 'Jane Doe', sid: 's0000002' } ],
  };

  it('approve the submitted campaign', () => {
    instance.approve()(values);
    const reqBody = {
      status: STATUS.REVIEWED,
      assignees: [ 's0000002' ],
      type: 'campaign',
    };
    expect(mockAxios.patch).toHaveBeenCalledWith('/campaign-rules/test-id', reqBody,

    undefined);
  });
});

describe('Campaign Details - Create a New Campaign (undefined status)', () => {
  const CampaignDetailsContainer = props => <DetailsContainer {...props} type="campaign" />;
  const DetailsContainerComponent = ({ initialState = {}, ...props }) => {
    const state = {
      form: { campaignDetails: { values: { platforms_targeting: [] } } },
      ...initialState,
    };
    const store = createStore(rootReducer, state, compose(applyMiddleware(thunk)));
    const ReduxDetailsContainerForm = reduxForm({ form: 'campaignDetails', validate: validate })(CampaignDetailsContainer);
    return (
      <Provider store={store} >
        <ReduxDetailsContainerForm {...commonDetailsContainerProps} {...props}/>
      </Provider>
    );
  };
  DetailsContainerComponent.propTypes = {
    initialState: PropTypes.object,
    formValues: PropTypes.object,
  };

  beforeEach(() => {
    commonDetailsContainerProps.formChange.mockClear();
  });

  test('should update campaign title', async() => {
    const { findByText, getByPlaceholderText } = render(<DetailsContainerComponent/>);

    await findByText('Create a New Campaign');
    const titleInput = getByPlaceholderText('Campaign Title');

    fireEvent.change(titleInput, { target: { value: 'Abc' } });
    expect(titleInput).toHaveValue('Abc');
  });

  test('App version targeting - opening version targeting modal', async() => {
    const { getAllByText, findByText, queryByText } = render(<DetailsContainerComponent
      formValues={{
        ...commonDetailsContainerProps.formValues,
        application: 'nova',
        platforms: { ios: true, android: true },
      }}
    />);
    await findByText('Create a New Campaign');
    fireEvent.click(getAllByText('Add Version Targeting')[0]);
    expect(queryByText('iOS version targeting')).toBeInTheDocument();
  });

  test('should update details when selecting a campaign type', async() => {
    const { queryByText, findByText, getByLabelText, rerender } = render(<DetailsContainerComponent />);

    await findByText('Create a New Campaign');
    expect(queryByText('Targeted Campaign')).not.toBeInTheDocument();

    fireEvent.change(getByLabelText('Application'), { target: { value: 'nova' } });
    rerender(
      <DetailsContainerComponent
        formValues={{
          ...commonDetailsContainerProps.formValues,
          application: 'nova',
          platforms: { ios: true, android: true },
        }}
      />,
    );
    await findByText('Create a New Campaign');
    fireEvent.click(getByLabelText('Targeted Campaign'));
    expect(commonDetailsContainerProps.updateDetails).toHaveBeenCalledWith({ external_ref: null, type: 'targeted' });
    fireEvent.click(getByLabelText('Mass Campaign'));
    expect(commonDetailsContainerProps.updateDetails).toHaveBeenCalledWith({ external_ref: 'MASS', type: 'mass' });
    fireEvent.click(getByLabelText('Mass Message'));
    expect(commonDetailsContainerProps.updateDetails).toHaveBeenCalledWith({ external_ref: 'MESSAGE', type: 'message' });
  });

  test('should select an application and display associated platforms', async() => {
    const { findByText, getByLabelText, getByText, rerender, queryByText } = render(<DetailsContainerComponent/>);

    await findByText('Create a New Campaign');
    expect(getByText('Select container').closest('select')).toBeDisabled();
    const applicationInput = getByLabelText('Application');
    fireEvent.change(applicationInput, { target: { value: 'nova' } });
    expect(applicationInput).toHaveValue('nova');
    expect(commonDetailsContainerProps.formChange).toHaveBeenCalledWith('campaignDetails', 'platforms.ios', true);
    expect(commonDetailsContainerProps.formChange).toHaveBeenCalledWith('campaignDetails', 'platforms.android', true);

    // Rerender with selected app since form change function is mocked
    rerender(
      <DetailsContainerComponent
        formValues={{
          ...commonDetailsContainerProps.formValues,
          application: 'nova',
          platforms: { ios: true, android: true },
        }}
      />,
    );

    await findByText('Create a New Campaign');
    expect(getByText('Select container').closest('select')).not.toBeDisabled();
    expect(getByText('iOS')).toBeInTheDocument();
    expect(getByText('Android')).toBeInTheDocument();
    expect(queryByText('Web')).not.toBeInTheDocument();
  });

  test('should allow product page placement to be edited', async() => {
    // because the package we use (react-datetime) uses moment() to get today date and the add rdtToday class to the day div
    // and the snapshot is fixed so here we overwrite today
    Date.now = jest.fn(() => new Date('2022-03-23T12:33:37.000Z'));
    const {
      baseElement,
      getByLabelText,
      getByText,
      getByPlaceholderText,
      queryByText,
      rerender,
    } = await render(
      <DetailsContainerComponent/>,
    );

    expect(queryByText('Create a New Campaign')).toBeInTheDocument();
    expect(getByText('Select container').closest('select')).toBeDisabled();
    const applicationInput = getByLabelText('Application');
    fireEvent.change(applicationInput, { target: { value: 'nova' } });
    expect(applicationInput).toHaveValue('nova');
    expect(commonDetailsContainerProps.formChange).toHaveBeenCalledWith('campaignDetails', 'platforms.ios', true);
    expect(commonDetailsContainerProps.formChange).toHaveBeenCalledWith('campaignDetails', 'platforms.android', true);

    // when no product is selected
    await rerender(
      <DetailsContainerComponent
        formValues={{
          ...campaignCompletedValues,
          mass_targeting: undefined,
          pages: [ 'accounts' ],
          assignees: undefined,
          status: undefined,
        }}
      />,
    );
    expect(queryByText('Create a New Campaign')).toBeInTheDocument();
    expect(getByText('Select container').closest('select')).not.toBeDisabled();
    const containerInput = getByLabelText('Container');
    fireEvent.change(containerInput, { target: { value: '2' } });
    expect(commonDetailsContainerProps.updateDetails).toHaveBeenCalledWith({
      container: 'offers-and-programs',
      mass_targeting: { product_pages: {} },
      pages: undefined,
    });

    // add account-key page by enabling product specific page targeting
    const productCheckbox = getByLabelText(`Target specific product account details page`);
    fireEvent.click(productCheckbox);
    expect(commonDetailsContainerProps.updateDetails).toHaveBeenCalledWith({
      pages: [ 'accounts', 'account-key' ],
    });

    // rerender with form filled
    await rerender(
      <DetailsContainerComponent
        formValues={{
          ...campaignCompletedValues,
          pages: [ 'accounts', 'account-key' ],
          assignees: undefined,
          status: undefined,
        }}
      />,
    );

    // remove account-key page by disabling product specific page targeting
    fireEvent.click(getByLabelText(`Target specific product account details page`));
    expect(commonDetailsContainerProps.updateDetails).toHaveBeenCalledWith({
      pages: [ 'accounts' ],
    });
    // add it back by re-checking the checkbox
    fireEvent.click(getByLabelText(`Target specific product account details page`));
    expect(commonDetailsContainerProps.updateDetails).toHaveBeenCalledWith({
      pages: [ 'accounts', 'account-key' ],
    });

    // delete one of the products
    expect(getByText('Target products')).toBeInTheDocument();
    const tagLabel = getByText('B:DDA:CA Current Account (B:DDA:CA)');
    const tagDeleteButton = tagLabel.parentElement.querySelector('button');
    fireEvent.click(tagDeleteButton);
    expect(commonDetailsContainerProps.updateDetails).toHaveBeenCalledWith({
      mass_targeting: {
        ...campaignCompletedValues.mass_targeting,
        product_pages: { any_of: [ { ...productBook[1], label: 'B:TFS:SB Tax-Free Savings - BNS (B:TFS:SB)' } ] },
      },
    });

    // add a product
    fireEvent.focus(getByPlaceholderText('Select products'));
    fireEvent.click(getByText('B:DDA:CA Current Account (B:DDA:CA)'));
    expect(commonDetailsContainerProps.updateDetails).toHaveBeenCalledWith({
      mass_targeting: {
        ...campaignCompletedValues.mass_targeting,
        product_pages: { any_of: [
          { ...productBook[1], label: 'B:TFS:SB Tax-Free Savings - BNS (B:TFS:SB)' },
          { ...productBook[0], label: 'B:DDA:CA Current Account (B:DDA:CA)' },
        ] },
      },
    });

    // repeat using keyboard, test movement of focus with arrow keys and selection functionality with enter key
    fireEvent.click(getByText('B:DDA:CA Current Account (B:DDA:CA)').parentElement.querySelector('button'));
    let expectedFocus = getByPlaceholderText('Select products');
    fireEvent.focus(expectedFocus);
    fireEvent.keyDown(expectedFocus, { key: 'ArrowDown', code: 'ArrowDown', charCode: 40 });
    expectedFocus = getByText('B Business').parentElement;
    expect(expectedFocus).toHaveFocus();
    fireEvent.keyDown(expectedFocus, { key: 'ArrowDown', code: 'ArrowDown', charCode: 40 });
    expectedFocus = getByText('B:AFP Future Amex Platinum (B:AFP)').parentElement;
    expect(expectedFocus).toHaveFocus();
    fireEvent.keyDown(expectedFocus, { key: 'ArrowDown', code: 'ArrowDown', charCode: 40 });
    expectedFocus = getByText('B:DDA:CA Current Account (B:DDA:CA)').parentElement;
    expect(expectedFocus).toHaveFocus();
    fireEvent.keyDown(expectedFocus, { key: 'ArrowDown', code: 'ArrowDown', charCode: 40 });
    expectedFocus = getByText('R Retail').parentElement;
    expect(expectedFocus).toHaveFocus();
    fireEvent.keyDown(expectedFocus, { key: 'ArrowUp', code: 'ArrowUp', charCode: 38 });
    expectedFocus = getByText('B:DDA:CA Current Account (B:DDA:CA)').parentElement;
    expect(expectedFocus).toHaveFocus();
    commonDetailsContainerProps.updateDetails.mockClear();
    fireEvent.click(expectedFocus);
    expect(commonDetailsContainerProps.updateDetails).toHaveBeenCalledWith({
      mass_targeting: {
        ...campaignCompletedValues.mass_targeting,
        product_pages: { any_of: [
          { ...productBook[1], label: 'B:TFS:SB Tax-Free Savings - BNS (B:TFS:SB)' },
          { ...productBook[0], label: 'B:DDA:CA Current Account (B:DDA:CA)' },
        ] },
      },
    });

    // cover scenarios for new product selection and mass campaign type
    await rerender(
      <DetailsContainerComponent
        formValues={{
          ...campaignCompletedValues,
          type: 'mass',
          advancedTargeting: {
            include: [ productBook[0], productBook[1] ],
            includeMode: 'any',
          },
          assignees: undefined,
          status: undefined,
        }}
      />,
    );
    expect(baseElement).toMatchSnapshot();
  });

  test('should allow selected content to be previewed', async() => {
    const {
      getByLabelText,
      getByText,
      queryByText,
      rerender,
    } = await render(
      <DetailsContainerComponent/>,
    );

    expect(queryByText('Create a New Campaign')).toBeInTheDocument();
    expect(getByText('Select container').closest('select')).toBeDisabled();
    const applicationInput = getByLabelText('Application');
    fireEvent.change(applicationInput, { target: { value: 'nova' } });

    await rerender(<DetailsContainerComponent formValues={{ ...campaignCompletedValues }} />);
    window.open = jest.fn();
    fireEvent.click(getByText('Preview'));
    expect(window.open).toHaveBeenCalledWith(
      '/preview/4szkx38resvm/targetedCampaign/test-content-id/offers-and-programs/nova/false/',
      'winname',
      'directories=0,titlebar=0,toolbar=0,location=0,status=0,menubar=0,scrollbars=0,resizable=0,width=480,height=720',
    );
  });

  test('should submit for review (invalid form)', () => {
    const onSubmitMock = jest.fn(() => jest.fn());
    const wrapper = shallow(
      <Details
        {...commonDetailsProps}
        permissions={{ 'campaigns_manage': true }}
        onSubmit={onSubmitMock}
      />,
    );
    expect(wrapper.state()).toHaveProperty('promptUserAssignment', false);
    wrapper.find(PrimaryButton).at(0).simulate('click'); // Submit For Review
    expect(wrapper.state()).toHaveProperty('promptUserAssignment', false);
    expect(onSubmitMock).toHaveBeenCalled(); // Triggers errors state
  });

  test('should submit for review (valid form)', () => {
    const onSubmitMock = jest.fn(() => jest.fn());
    const wrapper = shallow(
      <Details
        {...commonDetailsProps}
        permissions={{ 'campaigns_manage': true }}
        formValid={true}
        onSubmit={onSubmitMock}
      />,
    );
    expect(wrapper.state()).toHaveProperty('promptUserAssignment', false);
    wrapper.find(PrimaryButton).at(0).simulate('click'); // Submit For Review
    expect(wrapper.state()).toHaveProperty('promptUserAssignment', true);
    expect(wrapper.find(Modal).prop('isModalVisible')).toBe(true);

    // Change assignee
    wrapper.find(AssignmentAutosuggest).simulate('change', [ { full_name: 'Jane Doe', sid: 's0000002' } ]);
    expect(commonDetailsProps.updateDetails).toHaveBeenCalledWith({ newAssignees: [ { full_name: 'Jane Doe', sid: 's0000002' } ] });

    wrapper.find(Modal).find(PrimaryButton).simulate('click'); // Submit
    expect(wrapper.state()).toHaveProperty('promptUserAssignment', false);
    expect(onSubmitMock).toHaveBeenCalled();
  });

  test('should trigger application switch modal and cancel', () => {
    const wrapper = shallow(
      <Details
        {...commonDetailsProps}
        formValues={{ ...commonDetailsProps.formValues, application: 'nova', content_id: '23JZaUb1g3SjrLiqgTWHKU' }}
      />,
    );
    wrapper.find({ name: 'application' }).simulate('change', {}, 'phoenix');
    expect(wrapper.state()).toHaveProperty('popup', 'applicationSwitch');
    wrapper.find(ModalDialogue).at(1).invoke('secondaryAction')(); // Cancel
    expect(wrapper.state()).toHaveProperty('popup', false);
  });

  test('should trigger application switch modal and continue', () => {
    const wrapper = shallow(
      <Details
        {...commonDetailsProps}
        formValues={{ ...commonDetailsProps.formValues, application: 'nova', content_id: '23JZaUb1g3SjrLiqgTWHKU' }}
      />,
    );
    wrapper.find({ name: 'application' }).simulate('change', {}, 'phoenix');
    expect(wrapper.state()).toHaveProperty('popup', 'applicationSwitch');
    wrapper.find(ModalDialogue).at(1).invoke('primaryAction')(); // Continue
    expect(wrapper.state()).toHaveProperty('popup', false);
  });
});

describe('Campaign Details - Create Offer', () => {
  const CampaignDetailsContainer = (props) => <DetailsContainer {...props} type="campaign" />;
  const DetailsContainerComponent = ({ initialState = {}, ...props }) => {
    const state = {
      form: { campaignDetails: { values: { platforms_targeting: [] } } },
      ...initialState,
    };
    const store = createStore(rootReducer, state, compose(applyMiddleware(thunk)));
    const ReduxDetailsContainerForm = reduxForm({ form: 'campaignDetails', validate: validate })(
      CampaignDetailsContainer,
    );
    return (
      <Provider store={store}>
        <ReduxDetailsContainerForm {...commonDetailsContainerProps} {...props} />
      </Provider>
    );
  };
  DetailsContainerComponent.propTypes = {
    initialState: PropTypes.object,
    formValues: PropTypes.object,
  };

  beforeEach(() => {
    commonDetailsContainerProps.formChange.mockClear();
  });

  test('should display "Offer Campaign" option if the selected app supports', async() => {
    const { queryByText, findByText, getByLabelText, rerender } = await render(
      <DetailsContainerComponent />,
    );

    await findByText('Create a New Campaign');
    expect(queryByText('Offer Campaign')).not.toBeInTheDocument();

    fireEvent.change(getByLabelText('Application'), { target: { value: 'nova' } });
    await rerender(
      <DetailsContainerComponent
        {...commonDetailsContainerProps}
        formValues={{
          ...offerInitialValues,
        }}
      />,
    );
    fireEvent.click(getByLabelText('Offer Campaign'));
    expect(commonDetailsContainerProps.updateDetails).toHaveBeenCalledWith({
      external_ref: null,
      type: 'offer',
    });
  });

  test('should display "Offer ID" field if "Offer Campaign" is selected', async() => {
    const { queryByText, getByLabelText, getByPlaceholderText } = await render(
      <DetailsContainerComponent
        {...commonDetailsContainerProps}
        formValues={{
          ...offerInitialValues,
        }}
      />,
    );

    fireEvent.click(getByLabelText('Offer Campaign'));
    expect(commonDetailsContainerProps.updateDetails).toHaveBeenCalledWith({
      external_ref: null,
      type: 'offer',
    });
    expect(queryByText('Targeting by Offer ID')).toBeInTheDocument();
    expect(queryByText('Offer ID')).toBeInTheDocument();

    const offerIDInput = getByPlaceholderText('Enter Offer ID');

    fireEvent.change(offerIDInput, { target: { value: 'OFR-123' } });
    expect(offerIDInput).toHaveValue('OFR-123');
  });
});

describe('Campaign Details - Edit a Campaign (draft status)', () => {
  const DetailsContainerComponent = ({ initialState = {}, ...props }) => {
    const state = {
      form: { campaignDetails: { values: { platforms_targeting: [] } } },
      ...initialState,
    };
    const store = createStore(rootReducer, state, compose(applyMiddleware(thunk)));
    const ReduxDetailsContainerForm = reduxForm({ form: 'campaignDetails' })(DetailsContainer);
    return (
      <Provider store={store} >
        <ReduxDetailsContainerForm {...commonDetailsContainerProps}
          match={{ params: { id: campaignCompletedValues.id } }}
          formValues={{
            ...campaignCompletedValues,
            status: STATUS.DRAFT,
          }}
          {...props}
        />
      </Provider>
    );
  };
  DetailsContainerComponent.propTypes = {
    initialState: PropTypes.object,
  };

  beforeEach(() => {
    commonDetailsContainerProps.addAlert.mockClear();
    commonDetailsContainerProps.updateDetails.mockClear();
  });

  it('should add alert if previously selected application is disabled', async() => {
    const mockApps = cloneDeep(commonDetailsContainerProps.applications);
    mockApps[0].status = false; // Disable Nova

    const { findByText } = render(
      <DetailsContainerComponent
        applications={mockApps}
      />,
    );
    await findByText('Edit a Campaign');
    expect(commonDetailsContainerProps.addAlert).toHaveBeenCalledWith({
      message: '"Nova Mobile" is currently disabled. Please enable the application to continue.',
    });
  });

  test('should delete content (container, page, content type)', async() => {
    const { findByText } = render(<DetailsContainerComponent/>);

    await findByText('Edit a Campaign');
    const contentTable = document.getElementsByClassName('targeting-metadata__content-action-menu')[0];
    fireEvent.click(within(contentTable).getByRole('button', { name: 'Action menu', hidden: true }));
    fireEvent.click(within(contentTable).getByText('Delete'));
    expect(commonDetailsContainerProps.updateDetails).toHaveBeenCalledWith({
      content_id: undefined,
      content_type: undefined,
      container: undefined,
    });
  });

  test('should duplicate the draft campaign', async() => {
    const { findByText, getByText } = render(<DetailsContainerComponent permissions={{ 'campaigns_manage': true }} />);

    await findByText('Edit a Campaign');
    fireEvent.click(getByText('Duplicate'));
    expect(commonDetailsContainerProps.history.push).toHaveBeenCalledWith('/campaigns/test-campaign-id/duplicate');
  });

  test('should delete the draft campaign', () => {
    const wrapper = shallow(
      <Details
        {...commonDetailsProps}
        formValues={campaignCompletedValues}
        permissions={{ 'campaigns_manage': true }}
        status={STATUS.DRAFT}
      />,
    );

    wrapper.find(PillButton).at(1).simulate('click'); // 0 - Duplicate, 1 - Delete
    expect(wrapper.state()).toHaveProperty('popup', 'delete');
    expect(wrapper.find(ModalDialogue).at(0).prop('isModalVisible')).toBe(true);
  });
});

describe('Campaign Details - View a Campaign (submitted status)', () => {
  const DetailsContainerComponent = ({ initialState = {}, ...props }) => {
    const state = {
      form: { campaignDetails: { values: { platforms_targeting: [] } } },
      ...initialState,
    };
    const store = createStore(rootReducer, state, compose(applyMiddleware(thunk)));
    const ReduxDetailsContainerForm = reduxForm({ form: 'campaignDetails' })(DetailsContainer);
    return (
      <Provider store={store}>
        <ReduxDetailsContainerForm {...commonDetailsContainerProps}
          match={{ params: { id: campaignCompletedValues.id } }}
          formValues={{ ...campaignCompletedValues, status: STATUS.SUBMITTED }}
          {...props}
        />
      </Provider>
    );
  };
  DetailsContainerComponent.propTypes = {
    initialState: PropTypes.object,
  };

  beforeEach(() => {
    commonDetailsContainerProps.history.push.mockClear();
  });

  test('should display the assignee\'s formatted names', async() => {
    const { findByText, getByText, queryByText } = render(<DetailsContainerComponent/>);

    await findByText('View a Campaign');
    expect(getByText('Doe, John')).toBeInTheDocument();
    expect(queryByText('Doe, Jane')).not.toBeInTheDocument();
  });

  it('should reject the submitted campaign', () => {
    const onApproveMock = jest.fn(() => jest.fn());
    const wrapper = shallow(
      <Details
        {...commonDetailsProps}
        formValues={campaignCompletedValues}
        permissions={{ 'campaigns_review': true }}
        status={STATUS.SUBMITTED}
        onApprove={onApproveMock}
      />,
    );
    wrapper.find(SecondaryButton).at(0).simulate('click'); // Reject
    expect(onApproveMock).toHaveBeenCalledWith(true, false);
  });

  it('should approve & submit the submitted campaign', () => {
    const onApproveMock = jest.fn(() => jest.fn());
    const wrapper = shallow(
      <Details
        {...commonDetailsProps}
        formValues={campaignCompletedValues}
        permissions={{ 'campaigns_review': true }}
        status={STATUS.SUBMITTED}
        onApprove={onApproveMock}
      />,
    );

    expect(wrapper.state()).toHaveProperty('promptUserAssignment', false);
    wrapper.find(PrimaryButton).at(1).simulate('click'); // Approve
    expect(wrapper.state()).toHaveProperty('promptUserAssignment', true);
    wrapper.find(Modal).find(PrimaryButton).simulate('click'); // Submit
    expect(onApproveMock).toHaveBeenCalledWith(false);
  });

  it('should approve & submit the submitted campaign CCAU', () => {
    const onApproveMock = jest.fn(() => jest.fn());
    const wrapper = shallow(
      <Details
        {...commonDetailsProps}
        formValues={campaignCompletedValues}
        permissions={{ 'ccau_campaigns_review': true }}
        status={STATUS.SUBMITTED}
        onApprove={onApproveMock}
        type={ruleTypesConstants.CCAU_CAMPAIGN}
      />,
    );

    expect(wrapper.state()).toHaveProperty('promptUserAssignment', false);
    wrapper.find(PrimaryButton).at(1).simulate('click'); // Approve
    expect(wrapper.state()).toHaveProperty('promptUserAssignment', true);
    wrapper.find(Modal).find(PrimaryButton).simulate('click'); // Submit
    expect(onApproveMock).toHaveBeenCalledWith(false);
  });

  it('should approve & cancel the submitted campaign', () => {
    const onApproveMock = jest.fn(() => jest.fn());
    const wrapper = shallow(
      <Details
        {...commonDetailsProps}
        formValues={campaignCompletedValues}
        permissions={{ 'campaigns_review': true }}
        status={STATUS.SUBMITTED}
        onApprove={onApproveMock}
      />,
    );

    expect(wrapper.state()).toHaveProperty('promptUserAssignment', false);
    wrapper.find(PrimaryButton).at(1).simulate('click'); // Approve
    expect(wrapper.state()).toHaveProperty('promptUserAssignment', true);
    wrapper.find(Modal).find(SecondaryButton).simulate('click'); // Cancel
    expect(wrapper.state()).toHaveProperty('promptUserAssignment', false);
  });
});

describe('Campaign Details - View a Campaign (reviewed status)', () => {
  const DetailsContainerComponent = ({ initialState = {}, ...props }) => {
    const state = {
      form: { campaignDetails: { values: { platforms_targeting: [] } } },
      ...initialState,
    };
    const store = createStore(rootReducer, state, compose(applyMiddleware(thunk)));
    const ReduxDetailsContainerForm = reduxForm({ form: 'campaignDetails' })(DetailsContainer);
    return (
      <Provider store={store} >
        <ReduxDetailsContainerForm {...commonDetailsContainerProps}
          match={{ params: { id: campaignCompletedValues.id } }}
          formValues={{ ...campaignCompletedValues, status: STATUS.REVIEWED }}
          {...props}
        />
      </Provider>
    );
  };
  DetailsContainerComponent.propTypes = {
    initialState: PropTypes.object,
  };

  beforeEach(() => {
    commonDetailsContainerProps.history.push.mockClear();
    commonDetailsContainerProps.populateDetails.mockClear();
  });

  test('should publish the reviewed campaign', async() => {
    const { findByText, getByText } = render(
      <DetailsContainerComponent permissions={{ 'campaigns_approve': true }}/>,
    );

    await findByText('View a Campaign');
    fireEvent.click(getByText('Publish'));
    expect(commonDetailsContainerProps.populateDetails).toHaveBeenCalledWith({ productBook });
  });

  test('should reject the reviewed campaign', async() => {
    const { findByText, getByText } = render(
      <DetailsContainerComponent permissions={{ 'campaigns_approve': true }} />,
    );

    await findByText('View a Campaign');
    fireEvent.click(getByText('Reject'));
    expect(commonDetailsContainerProps.populateDetails).toHaveBeenCalledWith({ productBook });
  });

  test('should publish the reviewed campaign CCAU', async() => {
    const { findByText, getByText } = render(
      <DetailsContainerComponent permissions={{ 'ccau_campaigns_approve': true }} type={ruleTypesConstants.CCAU_CAMPAIGN}/>,
    );

    await findByText('View a Campaign');
    fireEvent.click(getByText('Publish'));
    expect(commonDetailsContainerProps.populateDetails).toHaveBeenCalledWith({ productBook });
  });

  test('should reject the reviewed campaign CCAU', async() => {
    const { findByText, getByText } = render(
      <DetailsContainerComponent permissions={{ 'ccau_campaigns_approve': true }} type={ruleTypesConstants.CCAU_CAMPAIGN} />,
    );

    await findByText('View a Campaign');
    fireEvent.click(getByText('Reject'));
    expect(commonDetailsContainerProps.populateDetails).toHaveBeenCalledWith({ productBook });
  });
});

describe('Campaign Details - View a Campaign (published status)', () => {
  const DetailsContainerComponent = ({ initialState = {}, ...props }) => {
    const state = {
      form: { campaignDetails: { values: { platforms_targeting: [] } } },
      ...initialState,
    };
    const store = createStore(rootReducer, state, compose(applyMiddleware(thunk)));
    const ReduxDetailsContainerForm = reduxForm({ form: 'campaignDetails' })(DetailsContainer);
    return (
      <Provider store={store}>
        <ReduxDetailsContainerForm {...commonDetailsContainerProps}
          match={{ params: { id: campaignCompletedValues.id } }}
          formValues={{
            ...campaignCompletedValues,
            status: STATUS.PUBLISHED,
          }}
          {...props}
        />
      </Provider>
    );
  };
  DetailsContainerComponent.propTypes = {
    initialState: PropTypes.object,
  };

  beforeEach(() => {
    commonDetailsContainerProps.history.push.mockClear();
    commonDetailsContainerProps.populateDetails.mockClear();
  });

  test('should deactivate the published campaign (form enabled)', async() => {
    const { findByText, getByText } = render(<DetailsContainerComponent
      permissions={{ 'campaigns_manage': true }}
    />);

    await findByText('View a Campaign');
    fireEvent.click(getByText('Deactivate'));
    expect(commonDetailsContainerProps.populateDetails).toHaveBeenCalledTimes(1);
    expect(commonDetailsContainerProps.populateDetails).toHaveBeenCalledWith({ productBook });
  });

  test('should activate the published campaign (form disabled)', async() => {
    const { findByText, getByText } = render(<DetailsContainerComponent
      permissions={{ 'campaigns_manage': true }}
      formValues={{ ...campaignCompletedValues, disabled: true, status: STATUS.PUBLISHED }}
    />);

    await findByText('View a Campaign');
    fireEvent.click(getByText('Activate'));
    expect(commonDetailsContainerProps.populateDetails).toHaveBeenCalledTimes(1);
    expect(commonDetailsContainerProps.populateDetails).toHaveBeenCalledWith({ productBook });
  });
});

describe('LOB Targeting Integration Tests', () => {
  const wealthCampaignValues = {
    type: 'wealth',
    application: 'nova',
    platforms: { ios: true, android: true },
    mass_targeting: {
      product_pages: {},
      enrollment_status: [],
      wealth_lobs: [ 'SDBI', 'SMI' ],
    },
  };

  const commonDetailsPropsWithWealth = {
    ...commonDetailsProps,
    ruleSubTypeMap: {
      1: { id: 1, type: 'targeted', description: 'Targeted Campaign' },
      2: { id: 2, type: 'mass', description: 'Mass Campaign' },
      3: { id: 3, type: 'wealth', description: 'Wealth Campaign' },
    },
    access: {
      containers: {
        nova: { view: [], manage: [] },
        starburst: { view: [], manage: [] },
        atlantis: { view: [], manage: [] },
      },
      pages: {
        nova: { view: [], manage: [] },
        starburst: { view: [], manage: [] },
        atlantis: { view: [], manage: [] },
      },
      ruleSubTypes: {
        nova: { view: [ 'targeted', 'mass', 'wealth' ], manage: [ 'targeted', 'mass', 'wealth' ] },
        starburst: { view: [ 'targeted', 'mass', 'wealth' ], manage: [ 'targeted', 'mass', 'wealth' ] },
        atlantis: { view: [ 'targeted', 'mass', 'wealth' ], manage: [ 'targeted', 'mass', 'wealth' ] },
      },
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should render LOB Field component for wealth campaigns', () => {
    const component = shallow(<Details
      {...commonDetailsPropsWithWealth}
      formValues={wealthCampaignValues}
      isCampaign={true}
    />);

    const lobField = component.find('Field[name="mass_targeting.wealth_lobs"]');
    expect(lobField).toHaveLength(1);
    expect(lobField.prop('component')).toBe(LOBTargeting);
  });

  test('should not render LOB Field component for non-wealth campaigns', () => {
    const component = shallow(<Details
      {...commonDetailsPropsWithWealth}
      formValues={{ ...wealthCampaignValues, type: 'mass' }}
      isCampaign={true}
    />);

    expect(component.find('Field[name="mass_targeting.wealth_lobs"]')).toHaveLength(0);
  });

  test('should pass correct props to LOB Field component', () => {
    const component = shallow(<Details
      {...commonDetailsPropsWithWealth}
      formValues={wealthCampaignValues}
      permissions={{ 'campaigns_manage': true }}
      status={STATUS.DRAFT}
      isCampaign={true}
    />);

    const lobField = component.find('Field[name="mass_targeting.wealth_lobs"]');
    expect(lobField.prop('application')).toBe('nova');
    expect(lobField.prop('disabled')).toBe(false);
    expect(lobField.prop('isWealthCampaignSelected')).toBe(true);
    expect(lobField.prop('validate')).toStrictEqual([ expect.any(Function) ]);
  });

  test('should validate LOB selection correctly', () => {
    const component = shallow(<Details
      {...commonDetailsPropsWithWealth}
      formValues={wealthCampaignValues}
      isCampaign={true}
    />);

    const instance = component.instance();

    // Test validation with empty selection for wealth type
    const errorMsg = instance.validateLOBSelection([], { type: 'wealth' });
    expect(errorMsg).toBe('At least one LOB must be selected');

    // Test validation with valid selection
    const noError = instance.validateLOBSelection([ 'SDBI' ], { type: 'wealth' });
    expect(noError).toBeUndefined();

    // Test validation for non-wealth type (should not validate)
    const noErrorNonWealth = instance.validateLOBSelection([], { type: 'mass' });
    expect(noErrorNonWealth).toBeUndefined();
  });

  test('should disable LOB Field component when form is disabled', () => {
    const component = shallow(<Details
      {...commonDetailsPropsWithWealth}
      formValues={wealthCampaignValues}
      permissions={{}} // No permissions
      status={STATUS.DRAFT}
      isCampaign={true}
    />);

    const lobField = component.find('Field[name="mass_targeting.wealth_lobs"]');
    expect(lobField.prop('disabled')).toBe(true);
  });

  test('should handle undefined mass_targeting object', () => {
    const wealthCampaignNoMassTargeting = {
      ...wealthCampaignValues,
      mass_targeting: undefined,
    };

    const component = shallow(<Details
      {...commonDetailsPropsWithWealth}
      formValues={wealthCampaignNoMassTargeting}
      isCampaign={true}
    />);

    const lobField = component.find('Field[name="mass_targeting.wealth_lobs"]');
    expect(lobField).toHaveLength(1);
    // Redux-form will handle undefined values properly
  });

  test('should pass correct application prop from form values', () => {
    const starburstWealthCampaign = {
      ...wealthCampaignValues,
      application: 'starburst',
    };

    const component = shallow(<Details
      {...commonDetailsPropsWithWealth}
      formValues={starburstWealthCampaign}
      permissions={{ 'campaigns_manage': true }}
      isCampaign={true}
      status={STATUS.DRAFT}
    />);

    const lobField = component.find('Field[name="mass_targeting.wealth_lobs"]');
    expect(lobField.prop('application')).toBe('starburst');
  });
});
