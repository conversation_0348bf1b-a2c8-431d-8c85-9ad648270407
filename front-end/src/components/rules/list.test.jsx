import React from 'react';
import { createMemoryHistory } from 'history';
import { Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { render, fireEvent, waitFor, queryByText } from '@testing-library/react';
import { within } from '@testing-library/dom';
import List from './list';
import { ruleTypes } from '../../constants';
import userEvent from '@testing-library/user-event';
import { act } from 'react-dom/test-utils';

const rulesResponse = [
  {
    'application': 'nova',
    'id': 'PtD8ZEPFpLcj',
    'name': 'Sample rule #1',
    'start_at': '2018-07-13T00:00:00.000Z',
    'end_at': '2038-07-30T23:59:59.000Z',
    'updated_at': '2018-07-13T00:00:00.000Z',
    'content_space': '4szkx38resvm',
    'content_type': 'sample-content-type-1',
    'content_id': 'sample-content-id-1',
    'container': 'sample-container-1',
    'dismissable_flag': true,
    'pages': [ 'sample-page' ],
    'platforms': [ ],
    'app_version': null,
    'status': 'draft',
    'disabled': false,
    'created_by': 's1234567',
    'updated_by': 's1234567',
  },
  {
    'id': 'jhrotTqfCeiN',
    'name': 'Sample rule #2',
    'start_at': '2018-07-15T00:00:00.000Z',
    'end_at': '2018-08-30T23:59:59.000Z',
    'updated_at': '2018-07-15T00:00:00.000Z',
    'content_space': '4szkx38resvm',
    'content_type': null,
    'content_id': null,
    'container': null,
    'dismissable_flag': true,
    'pages': [ ],
    'platforms': [ ],
    'app_version': null,
    'status': 'draft',
    'disabled': true,
    'created_by': 's1234567',
    'updated_by': 's1234567',
  },
  {
    'application': 'nova',
    'id': 'uVEPJeHe0hTr',
    'name': 'Sample rule #3',
    'start_at': '2018-08-01T00:00:00.000Z',
    'updated_at': '2018-08-01T00:00:00.000Z',
    'end_at': '2029-08-30T23:59:59.000Z',
    'content_space': '4szkx38resvm',
    'content_type': null,
    'content_id': null,
    'container': 'offers-and-programs',
    'dismissable_flag': true,
    'pages': [ 'sample-page' ],
    'platforms': [ ],
    'app_version': null,
    'status': 'published',
    'disabled': true,
    'created_by': 's1234567',
    'updated_by': 's1234567',
  },
  {
    'application': 'nova',
    'id': 'LFPvqZR0EDZ2',
    'name': 'Sample rule #4',
    'start_at': '2018-08-01T00:00:00.000Z',
    'updated_at': '2018-08-01T00:00:00.000Z',
    'end_at': '2029-08-30T23:59:59.000Z',
    'content_space': '4szkx38resvm',
    'content_type': null,
    'content_id': null,
    'container': 'offers-and-programs',
    'dismissable_flag': true,
    'pages': [ 'sample-page' ],
    'platforms': [ ],
    'app_version': null,
    'status': 'published',
    'disabled': false,
    'created_by': 's1234567',
    'updated_by': 's1234567',
  },
  {
    'id': 'LFPvqZR0EDA1',
    'name': 'Sample rule #5',
    'start_at': '2018-08-01T00:00:00.000Z',
    'updated_at': '2018-08-01T00:00:00.000Z',
    'end_at': '2030-08-30T23:59:59.000Z',
    'content_space': '4szkx38resvm',
    'content_type': null,
    'content_id': 'disabled-container',
    'container': 'disabled-container',
    'dismissable_flag': true,
    'pages': [ 'inactive-page' ],
    'platforms': [ ],
    'app_version': null,
    'status': 'published',
    'disabled': true,
    'created_by': 's1234567',
    'updated_by': 's1234567',
    application: 'disabled-application',
  },
];

const mockUsers = {
  isLoading: false,
  items: [
     { sid: 's1234567', name: 'First Last', team_id: 1 },
     { sid: 's7654321', name: 'A B', team_id: 1 },
  ],
};

const mockAccess = {
  containers: {
    nova: {
      view: [ 'offers-and-programs', 'my-activity', 'sample-container-1' ],
      manage: [ 'offers-and-programs', 'my-activity', 'sample-container-1' ],
    },
    abm: {
      view: [ 'preOffer', 'mainOffer' ],
    },
  },
  pages: {
    nova: {
      view: [ 'accounts', 'activities', 'sample-page' ],
      manage: [ 'accounts', 'activities', 'sample-page' ],
    },
    abm: {
      view: [ 'preMainMenu' ],
    },
  },
  ruleSubTypes: {
    nova: {
      view: [ 'targeted', 'mass', 'message' ],
      manage: [ 'targeted', 'mass', 'message' ],
    },
    abm: {
      view: [ 'targeted', 'mass' ],
    },
  },
};

const mockPermissions = {
  'campaigns_view': true,
  'campaigns_manage': true,
  'ccau_campaigns_view': true,
  'ccau_campaigns_manage': true,
};

const mockApplications = {
  isLoading: false,
  items: [
     { id: 1, name: 'Nova Mobile', applicationId: 'nova', status: true, ruleSubTypes: [ 'targeted', 'mass', 'message' ], ruleSubTypeIds: [ 1, 2, 3 ], ruleTypes: [ 'alert', 'campaign' ] },
     { id: 2, name: 'ABM', applicationId: 'abm', status: true, ruleSubTypes: [ 'targeted', 'mass', 'message' ], ruleSubTypeIds: [ 1, 2, 3 ], ruleTypes: [ 'campaign' ] },
     { id: 3, name: 'Disabled Application', applicationId: 'disabled-application', status: false, ruleSubTypes: [ 'targeted', 'mass', 'message' ], ruleSubTypeIds: [ 1, 2, 3 ], ruleTypes: [ 'alert', 'campaign' ] },
  ],
};

const mockContainers = {
  isLoading: false,
  items: [
     { id: 1, application: 1, applicationId: 'nova', containerId: 'offers-and-programs', content_type: [ 'targetedCampaign' ], name: 'offers-and-programs', pages: [ 1 ], rule_type: 'campaign', status: true },
     { id: 2, application: 1, applicationId: 'nova', containerId: 'my-activity', content_type: [ 'standingCampaign' ], name: 'my-activity', pages: [ 2 ], rule_type: 'campaign', status: true },
     { id: 3, application: 1, applicationId: 'nova', containerId: 'disabled-container', content_type: [ 'standingCampaign' ], name: 'disabled-container', pages: [ 2 ], rule_type: 'campaign', status: false },
  ],
};

const mockPages = {
  isLoading: false,
  items: [
     { id: 1, name: 'accounts', pageId: 'accounts', application: 1, applicationId: 'nova', containers: [ 1 ], status: true },
     { id: 2, name: 'activities', pageId: 'activities', application: 1, applicationId: 'nova', containers: [ 2 ], status: true },
     { id: 3, name: 'inactive page', pageId: 'inactive-page', application: 1, applicationId: 'nova', containers: [ 2 ], status: false },
  ],
};

const mockContent = {
  isLoading: false,
  contentItem: {
    content: {
      name: 'test contentful name',
    },
  },
};

const mockTeams = {
  isLoading: false,
  items: {
    1: {
      id: 1, name: 'Pigeon Team', active: true,
    },
  },
};

const mockRuleSubTypes = {
  items: [
    { description: 'Targeted Campaign', id: 1, ruleTypeId: null, type: 'targeted' },
    { description: 'Mass Campaign', id: 2, ruleTypeId: null, type: 'mass' },
    { description: 'Mass Message', id: 3, ruleTypeId: null, type: 'message' },
  ],
};

const mockLocation = { pathname: '/campaigns' };

const commonProps = {
  getRules: jest.fn(),
  deleteRule: jest.fn(),
  setRuleActive: jest.fn(),
  getContentById: jest.fn(),
  getRuleSubTypes: jest.fn(),
  getApplications: jest.fn(),
  getTeams: jest.fn(),
  type: ruleTypes.CAMPAIGN,
  rules: rulesResponse,
  users: mockUsers,
  access: mockAccess,
  permissions: mockPermissions,
  applications: mockApplications,
  content: mockContent,
  containers: mockContainers,
  pages: mockPages,
  ruleSubTypes: mockRuleSubTypes,
  teams: mockTeams,
  pagination: {
    total: 1000,
    offset: 0,
    limit: 2,
  },
};

const mockStore = configureMockStore([ thunk ]);
const defaultStore = mockStore({
  campaigns: {
    pagination: {
      total: 100,
    },
  },
  authenticated: {
    permissions: { admin: true },
    access: {
      campaigns: {},
      ccau_campaigns: {},
      alerts: {},
    },
  },
});

describe('Rules List', () => {
  const mockScroll = jest.fn();

  beforeEach(() => {
    mockScroll.mockClear();
  });

  beforeAll(() => {
    document.getElementById = () => ({
      focus: jest.fn(),
    });

    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      configurable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(min-width: 1025px)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });
  });

  test('Calls getRules when loaded', () => {
    const getRules = jest.fn();

    render(
      <Provider store={defaultStore}>
        <Router history={createMemoryHistory()}>
          <List
            {...commonProps}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );

    expect(getRules).toHaveBeenCalled();
  });

  test('should search for campaigns', async() => {
    const getRules = jest.fn();
    const history = createMemoryHistory();
    const { getByPlaceholderText, rerender, getByLabelText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );

    getRules.mockReset();
    fireEvent.change(getByPlaceholderText('Search'), { target: { value: 'CLI01' } });
    expect(getRules).not.toHaveBeenCalled(); // it is debounced 500ms
    await waitFor(() => expect(getRules).toHaveBeenCalledWith({ search: 'CLI01', sort: '-updated_at', type: 'campaign' }, false));

    getRules.mockReset();
    fireEvent.click(getByLabelText('Last'));
    expect(getRules).toHaveBeenCalledWith({ pageNumber: 100, search: 'CLI01', sort: '-updated_at', type: 'campaign' }, false);

    // if the last page were clicked the rules offset would change, let's simulate that
    getRules.mockReset();

    rerender(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            rules={rulesResponse}
            getRules={getRules}
            pagination={{
              total: 1000,
              limit: 2,
              offset: 500,
            }}
          />
        </Router>
      </Provider>,
    );

    // select the first page
    expect(getByLabelText('First')).not.toBeDisabled();
    fireEvent.click(getByLabelText('First'));
    expect(getRules).toHaveBeenCalledWith({ search: 'CLI01', sort: '-updated_at', type: 'campaign' }, false);
  });

  test('filter by valid start date - calendar selection', async() => {
    const getRules = jest.fn();
    const history = createMemoryHistory();
    const { getByText, getByLabelText, getAllByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );
    getRules.mockReset();

    // Open filter section
    fireEvent.click(getByText('Filters'));
    act(() => {
      // Open start date calendar component
      fireEvent.click(getByLabelText('Start Date'));
      // Select a day
      userEvent.click(getAllByText('10')[0]);
      // Confirm selection
      fireEvent.click(getAllByText('Set')[0]);
    });

    const isoStringRegex = /\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)/;
    expect(getRules).toHaveBeenCalled();
    expect(getRules.mock.calls[0][0].start_date_gt).toMatch(isoStringRegex);

    // clear the filters
    getRules.mockReset();
    fireEvent.click(getByText('Clear all').closest('button'));
    expect(getRules).toHaveBeenCalledWith({ sort: '-updated_at', type: 'campaign' }, false);
  });

  test('filter by valid end date - calendar selection', async() => {
    const getRules = jest.fn();
    const history = createMemoryHistory();
    const { getByText, getByLabelText, getAllByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );
    getRules.mockReset();

    // Open filter section
    fireEvent.click(getByText('Filters'));
    act(() => {
      // Open end date calendar component
      fireEvent.click(getByLabelText('End Date'));
      // Select a day
      userEvent.click(getAllByText('10')[3]);
      // Confirm selection
      fireEvent.click(getAllByText('Set')[1]);
    });

    const isoStringRegex = /\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)/;
    expect(getRules).toHaveBeenCalled();
    expect(getRules.mock.calls[0][0].end_date_lt).toMatch(isoStringRegex);
  });

  test('filter by status dropdown', async() => {
    const getRules = jest.fn();
    const history = createMemoryHistory();
    const { getByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            rules={rulesResponse}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );
    getRules.mockReset();

    fireEvent.click(getByText('Filters'));
    const filterSection = document.getElementsByClassName('admin-filter')[0];

    // open status dropdown - should display all status'
    fireEvent.click(within(filterSection).getByLabelText('Status'));
    expect(getByText('Upcoming')).toBeInTheDocument();
    expect(getByText('Expired')).toBeInTheDocument();
    expect(getByText('Active')).toBeInTheDocument();
    expect(getByText('Inactive')).toBeInTheDocument();
    expect(getByText('Draft')).toBeInTheDocument();

    fireEvent.change(within(filterSection).getByLabelText('Status'), { target: { value: 'upcoming' } });
    expect(getRules).toHaveBeenCalled();
    expect(getRules.mock.calls[0][0].status).toStrictEqual('published');
    expect(getRules.mock.calls[0][0].disabled).toStrictEqual(false);
    expect(getRules.mock.calls[0][0]).toHaveProperty('start_date_gt');

    getRules.mockReset();
    fireEvent.change(within(filterSection).getByLabelText('Status'), { target: { value: 'expired' } });
    expect(getRules).toHaveBeenCalled();
    expect(getRules.mock.calls[0][0].status).toStrictEqual('published');
    expect(getRules.mock.calls[0][0]).toHaveProperty('end_date_lt');

    getRules.mockReset();
    fireEvent.change(within(filterSection).getByLabelText('Status'), { target: { value: 'active' } });
    expect(getRules).toHaveBeenCalled();
    expect(getRules.mock.calls[0][0].status).toStrictEqual('published');
    expect(getRules.mock.calls[0][0].disabled).toStrictEqual(false);
    expect(getRules.mock.calls[0][0]).toHaveProperty('start_date_lt');
    expect(getRules.mock.calls[0][0]).toHaveProperty('end_date_gt');

    getRules.mockReset();
    fireEvent.change(within(filterSection).getByLabelText('Status'), { target: { value: 'inactive' } });
    expect(getRules).toHaveBeenCalled();
    expect(getRules.mock.calls[0][0].status).toStrictEqual('published');
    expect(getRules.mock.calls[0][0].disabled).toStrictEqual(true);
    expect(getRules.mock.calls[0][0]).toHaveProperty('end_date_gt');

    getRules.mockReset();
    fireEvent.change(within(filterSection).getByLabelText('Status'), { target: { value: 'draft' } });
    expect(getRules).toHaveBeenCalled();
    expect(getRules).toHaveBeenCalledWith({ status: 'draft', sort: '-updated_at', type: 'campaign' }, false);
  });

  test('filter by status tab', async() => {
    const getRules = jest.fn();
    const history = createMemoryHistory();
    const { getByText, getAllByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            rules={rulesResponse}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );
    getRules.mockReset();
    fireEvent.click(getByText('Filters'));
    const filterSection = document.getElementsByClassName('admin-filter')[0];

    fireEvent.click(getByText('Published'));
    expect(getRules).toHaveBeenCalled();
    expect(getRules).toHaveBeenCalledWith({ sort: '-updated_at', status: 'published', type: 'campaign' }, false);

    getRules.mockReset();
    fireEvent.click(getByText('Pending'));
    expect(getRules).toHaveBeenCalled();
    expect(getRules).toHaveBeenCalledWith({ sort: '-updated_at', status: 'pending', type: 'campaign' }, false);

    fireEvent.change(within(filterSection).getByLabelText('Status'), { target: { value: 'draft' } });
    fireEvent.click(getByText('Published'));

    fireEvent.change(within(filterSection).getByLabelText('Status'), { target: { value: 'expired' } });
    fireEvent.click(getByText('Pending'));

    getRules.mockReset();
    fireEvent.click(getAllByText('All')[0]);
    expect(getRules).toHaveBeenCalled();
    expect(getRules).toHaveBeenCalledWith({ sort: '-updated_at', type: 'campaign' }, false);
  });

  test.skip('filter by status & date - upcoming/start date', async() => {
    const getRules = jest.fn();
    const history = createMemoryHistory();
    const { getByText, getByRole } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            rules={rulesResponse}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );

    fireEvent.click(getByText('Filters'));
    const filterSection = document.getElementsByClassName('admin-filter')[0];
    fireEvent.change(within(filterSection).getByLabelText('Status'), { target: { value: 'upcoming' } });
    getRules.mockReset();

    const startDateInput = getByRole('textbox', { name: 'Start Date (DD/MMM/YYYY)' });
    act(() => {
      userEvent.click(startDateInput);
    });
    userEvent.type(startDateInput, '01/JAN/225');
    expect(startDateInput).toHaveValue('01/JAN/2025');

    const endDateInput = getByRole('textbox', { name: 'End Date (DD/MMM/YYYY)' });
    act(() => {
    userEvent.click(endDateInput);
    });
    userEvent.type(endDateInput, '01/FEB/225');
    expect(endDateInput).toHaveValue('01/FEB/2025');

    expect(getRules).toHaveBeenCalled();
    expect(getRules.mock.calls[0][0].status).toStrictEqual('published');
    expect(getRules.mock.calls[0][0]).toHaveProperty('start_date_gt');
  });

  test.skip('filter by status & date - expired/start date', async() => {
    const getRules = jest.fn();
    const history = createMemoryHistory();
    const { getByText, getByRole } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            rules={rulesResponse}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );

    fireEvent.click(getByText('Filters'));
    const filterSection = document.getElementsByClassName('admin-filter')[0];
    act(() => {
      fireEvent.change(within(filterSection).getByLabelText('Status'), { target: { value: 'expired' } });
    });
    getRules.mockReset();

    const startDateInput = getByRole('textbox', { name: 'Start Date (DD/MMM/YYYY)' });
    act(() => {
    userEvent.click(startDateInput);
    });
    userEvent.type(startDateInput, '01/JAN/220');
    expect(startDateInput).toHaveValue('01/JAN/2020');

    const endDateInput = getByRole('textbox', { name: 'End Date (DD/MMM/YYYY)' });
    act(() => {
    userEvent.click(endDateInput);
    });
    userEvent.type(endDateInput, '01/FEB/220');
    expect(endDateInput).toHaveValue('01/FEB/2020');

    expect(getRules).toHaveBeenCalled();
    expect(getRules.mock.calls[0][0].status).toStrictEqual('published');
    expect(getRules.mock.calls[0][0]).toHaveProperty('start_date_gt');
  });

  test('filter by container dropdown', async() => {
    const getRules = jest.fn();
    const history = createMemoryHistory();
    const { getByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            rules={rulesResponse}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );
    getRules.mockReset();

    fireEvent.click(getByText('Filters'));
    const filterSection = document.getElementsByClassName('admin-filter')[0];

    act(() => {
      // open application dropdown - should display nova & abm
      fireEvent.click(within(filterSection).getByLabelText('Applications'));
    });
    expect(getByText('Nova Mobile')).not.toBeDisabled();
    expect(getByText('ABM')).not.toBeDisabled();
    fireEvent.change(within(filterSection).getByLabelText('Applications'), { target: { value: 'nova' } });

    // open pages dropdown - should display activities & accounts
    fireEvent.click(within(filterSection).getByLabelText('Pages'));
    expect(getByText('Activities')).not.toBeDisabled();
    expect(getByText('Accounts')).not.toBeDisabled();

    // select my-activity container from dropdown
    fireEvent.change(within(filterSection).getByLabelText('Containers'), { target: { value: 'my-activity' } });
    expect(getRules).toHaveBeenCalledWith({ application: 'nova', container: 'my-activity', sort: '-updated_at', type: 'campaign' }, false);

    // open application dropdown - should only display nova now
    fireEvent.click(within(filterSection).getByLabelText('Applications'));
    expect(getByText('Nova Mobile')).not.toBeDisabled();

    // open pages dropdown - should display only display activities now
    fireEvent.click(within(filterSection).getByLabelText('Pages'));
    expect(getByText('Activities')).not.toBeDisabled();
  });

  test('filter by pages dropdown', async() => {
    const getRules = jest.fn();
    const history = createMemoryHistory();
    const { getByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            rules={rulesResponse}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );
    getRules.mockReset();

    fireEvent.click(getByText('Filters'));
    const filterSection = document.getElementsByClassName('admin-filter')[0];

    // select page from dropdown
    getRules.mockReset();
    fireEvent.change(within(filterSection).getByLabelText('Applications'), { target: { value: 'nova' } });
    fireEvent.change(within(filterSection).getByLabelText('Pages'), { target: { value: 'accounts' } });
    expect(getRules).toHaveBeenCalledWith({ application: 'nova', page: 'accounts', sort: '-updated_at', type: 'campaign' }, false);

    // select default option (i.e. All)
    getRules.mockReset();
    fireEvent.change(within(filterSection).getByLabelText('Pages'), { target: { value: '' } });
    expect(getRules).toHaveBeenCalledWith({ application: 'nova', sort: '-updated_at', type: 'campaign' }, false);
  });

  test('filter by application dropdown', async() => {
    const getRules = jest.fn();
    const history = createMemoryHistory();
    const { getByText, queryByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            rules={rulesResponse}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );
    getRules.mockReset();

    fireEvent.click(getByText('Filters'));
    const filterSection = document.getElementsByClassName('admin-filter')[0];

    // open containers dropdown - should display my-activity & offers-and-programs
    fireEvent.click(within(filterSection).getByLabelText('Containers'));
    expect(queryByText('My-activity')).not.toBeInTheDocument(); // because the user didn't select an application yet
    expect(queryByText('Offers-and-programs')).not.toBeInTheDocument(); // because the user didn't select an application yet

    // open pages dropdown - should display activities & accounts
    fireEvent.click(within(filterSection).getByLabelText('Pages'));
    expect(queryByText('Activities')).not.toBeInTheDocument(); // because the user didn't select an application yet
    expect(queryByText('Accounts')).not.toBeInTheDocument(); // because the user didn't select an application yet

    // select application from dropdown
    getRules.mockReset();
    fireEvent.change(within(filterSection).getByLabelText('Applications'), { target: { value: 'abm' } });
    expect(getRules).toHaveBeenCalledWith({ application: 'abm', sort: '-updated_at', type: 'campaign' }, false);

    // open dropdowns - nothing should display as no containers/pages are linked to abm
    fireEvent.click(within(filterSection).getByLabelText('Containers'));
    expect(queryByText('My-activity')).not.toBeInTheDocument();
    expect(queryByText('Offers-and-programs')).not.toBeInTheDocument();

    // select application from dropdown
    getRules.mockReset();
    fireEvent.change(within(filterSection).getByLabelText('Applications'), { target: { value: 'nova' } });
    expect(getRules).toHaveBeenCalledWith({ application: 'nova', sort: '-updated_at', type: 'campaign' }, false);

   // open dropdowns - should display all nova's pages
    fireEvent.click(within(filterSection).getByLabelText('Pages'));
    expect(getByText('Activities')).toBeInTheDocument();
  });

  test('filter by application dropdown - campaign types', async() => {
    const applications = {
      items: [
        { id: 1, name: 'Nova Mobile', applicationId: 'nova', status: true, ruleSubTypes: [ 'targeted', 'mass' ], ruleSubTypeIds: [ 1, 2 ], ruleTypes: [ 'alert', 'campaign' ] },
      ],
    };
    const getRules = jest.fn();
    const history = createMemoryHistory();
    const { getByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            applications={applications}
            rules={rulesResponse}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );
    getRules.mockReset();

    fireEvent.click(getByText('Filters'));
    const filterSection = document.getElementsByClassName('admin-filter')[0];

    // open campaign types dropdown - should all types
    fireEvent.click(within(filterSection).getByLabelText('Campaign Types'));
    expect(getByText('Targeted Campaigns')).toBeInTheDocument();
    expect(getByText('Mass Campaigns')).toBeInTheDocument();
    expect(getByText('Mass Messages')).toBeInTheDocument();

    // select application from dropdown
    getRules.mockReset();
    fireEvent.change(within(filterSection).getByLabelText('Applications'), { target: { value: 'nova' } });
    expect(getRules).toHaveBeenCalledWith({ application: 'nova', sort: '-updated_at', type: 'campaign' }, false);

    // open campaign types dropdown - message will be denoted as deprecated since it's not included in novas rule sub types
    fireEvent.click(within(filterSection).getByLabelText('Campaign Types'));
    expect(getByText('Targeted Campaigns')).toBeInTheDocument();
    expect(getByText('Mass Campaigns')).toBeInTheDocument();
    expect(getByText('Mass Messages (Deprecated)')).toBeInTheDocument();
  });

  test('should render filters - alert', () => {
    const getRules = jest.fn();
    const history = createMemoryHistory();
    const store = mockStore({
      alerts: {
        pagination: {
          total: 100,
        },
      },
      authenticated: {
        permissions: { admin: true },
        access: {},
      },
    });
    const { getByText, queryAllByText } = render(
      <Provider store={store}>
        <Router history={history}>
          <List
            {...commonProps}
            type={ruleTypes.ALERT}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );
    expect(getRules).toHaveBeenCalled();
    expect(queryAllByText('Created By')).toHaveLength(1); // table column title
    fireEvent.click(getByText('Filters'));
    expect(queryAllByText('Created By')).toHaveLength(2); // filter dropdown title
  });

  test('getRules called when sort changes', async() => {
    const getRules = jest.fn();
    const history = createMemoryHistory();
    const { getByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );

    getRules.mockReset();
    fireEvent.click(getByText('Campaign Name'));
    expect(getRules).toHaveBeenCalledWith({ sort: '-name', type: 'campaign' }, false);

    // fire again on campaign name to reverse the sort
    getRules.mockReset();
    fireEvent.click(getByText('Campaign Name'));
    expect(getRules).toHaveBeenCalledWith({ sort: 'name', type: 'campaign' }, false);
  });

  test('action menu - draft campaign', async() => {
    const getRules = jest.fn();
    const push = jest.fn();
    const deleteRule = jest.fn();
    const history = { push: push, listen: jest.fn(), location: mockLocation, createHref: jest.fn() };
    window.open = jest.fn();
    const { getAllByLabelText, queryByText: queryByTextLocal, rerender, queryAllByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            getRules={getRules}
            deleteRule={deleteRule}
          />
        </Router>
      </Provider>,
    );
    push.mockReset();

    fireEvent.click(getAllByLabelText('Action menu')[0]); // sample rule #1
    const activeActionMenu = document.querySelector('[data-testid="actionmenu-ul"][open]');

    expect(queryByText(activeActionMenu, 'Edit')).toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Duplicate')).toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Preview')).toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Activate')).not.toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Deactivate')).not.toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Delete')).toBeInTheDocument();

    fireEvent.click(queryByText(activeActionMenu, 'Edit'));
    expect(history.push).toHaveBeenCalledWith('/campaigns/PtD8ZEPFpLcj');

    rerender(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            getRules={getRules}
            deleteRule={deleteRule}
          />
        </Router>
      </Provider>,
    );
    push.mockReset();

    fireEvent.click(getAllByLabelText('Action menu')[0]);
    fireEvent.click(queryByText(activeActionMenu, 'Duplicate'));
    expect(history.push).toHaveBeenCalledWith('/campaigns/PtD8ZEPFpLcj/duplicate');

    rerender(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            getRules={getRules}
            deleteRule={deleteRule}
          />
        </Router>
      </Provider>,
    );

    fireEvent.click(getAllByLabelText('Action menu')[0]);
    fireEvent.click(queryByText(activeActionMenu, 'Preview'));
    expect(window.open).toHaveBeenCalledWith(
      '/preview/4szkx38resvm/sample-content-type-1/sample-content-id-1/sample-container-1/nova/true/undefined',
      'winname',
      'directories=0,titlebar=0,toolbar=0,location=0,status=0,menubar=0,scrollbars=0,resizable=0,width=480,height=720',
    );

    rerender(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            getRules={getRules}
            deleteRule={deleteRule}
          />
        </Router>
      </Provider>,
    );
    getRules.mockReset();

    fireEvent.click(getAllByLabelText('Action menu')[0]);
    fireEvent.click(queryByText(activeActionMenu, 'Delete'));
    expect(queryByTextLocal('Are you sure?')).toBeInTheDocument();
    fireEvent.click(queryAllByText('Delete')[1]);
    expect(deleteRule).toHaveBeenCalledWith('PtD8ZEPFpLcj');

    rerender(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            getRules={getRules}
            deleteRule={deleteRule}
          />
        </Router>
      </Provider>,
    );
    getRules.mockReset();
    deleteRule.mockReset();

    fireEvent.click(getAllByLabelText('Action menu')[0]);
    fireEvent.click(queryByText(activeActionMenu, 'Delete'));
    expect(queryByTextLocal('Are you sure?')).toBeInTheDocument();
    fireEvent.click(queryByTextLocal('Cancel'));
    expect(deleteRule).not.toHaveBeenCalled();
  });

  test('action menu - inactive campaign (status - published, disabled - true)', async() => {
    const getRules = jest.fn();
    const setRuleActive = jest.fn();
    const push = jest.fn();
    const history = { push: push, listen: jest.fn(), location: mockLocation, createHref: jest.fn() };
    window.open = jest.fn();
    const { getAllByLabelText, queryAllByTestId } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            getRules={getRules}
            setRuleActive={setRuleActive}
          />
        </Router>
      </Provider>,
    );
    getRules.mockReset();
    fireEvent.click(getAllByLabelText('Action menu')[2]);
    expect(queryAllByTestId('actionmenu-ul')[2].hasAttribute('open')).toBeTruthy();
    expect(queryAllByTestId('actionmenu-ul')[1].hasAttribute('open')).toBeFalsy();
    const activeActionMenu = document.querySelector('[data-testid="actionmenu-ul"][open]');

    expect(queryByText(activeActionMenu, 'View')).toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Duplicate')).toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Preview')).toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Activate')).toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Deactivate')).not.toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Delete')).not.toBeInTheDocument();

    fireEvent.click(queryByText(activeActionMenu, 'Activate'));
    expect(setRuleActive).toHaveBeenCalledWith('uVEPJeHe0hTr', 'published', true);
    expect(getRules).not.toHaveBeenCalled();
  });

  test('action menu - draft campaign (with view-only access)', async() => {
    const push = jest.fn();
    const history = { push: push, listen: jest.fn(), location: mockLocation, createHref: jest.fn() };
    window.open = jest.fn();
    const viewOnlyAccess = JSON.parse(JSON.stringify(mockAccess));
    [ 'containers', 'pages', 'ruleSubTypes' ].forEach(accessType => {
      delete viewOnlyAccess[accessType].nova.manage;
    });

    const { getByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List {...{ ...commonProps, access: viewOnlyAccess }} />
        </Router>
      </Provider>,
    );
    fireEvent.click(getByText('Sample rule #1'));
    const panelSection = document.getElementsByClassName('admin-list__panel-offset')[0];
    expect(queryByText(panelSection, 'View')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Edit')).not.toBeInTheDocument();
    expect(queryByText(panelSection, 'Preview')).toBeInTheDocument();
  });

  test('action menu - published campaign (status - published, disabled - false)', async() => {
    const getRules = jest.fn();
    const setRuleActive = jest.fn();
    const push = jest.fn();
    const history = { push: push, listen: jest.fn(), location: mockLocation, createHref: jest.fn() };
    window.open = jest.fn();
    const { getAllByLabelText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            getRules={getRules}
            setRuleActive={setRuleActive}
          />
        </Router>
      </Provider>,
    );
    getRules.mockReset();

    fireEvent.click(getAllByLabelText('Action menu')[3]); // sample rule #4 (mobile & desktop action menus are rendered so 4th rule is 7th element)
    const activeActionMenu = document.querySelector('[data-testid="actionmenu-ul"][open]');
    expect(queryByText(activeActionMenu, 'View')).toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Duplicate')).toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Preview')).toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Activate')).not.toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Deactivate')).toBeInTheDocument();
    expect(queryByText(activeActionMenu, 'Delete')).not.toBeInTheDocument();

    fireEvent.click(queryByText(activeActionMenu, 'Deactivate'));
    expect(setRuleActive).toHaveBeenCalledWith('LFPvqZR0EDZ2', 'published', false);
    expect(getRules).not.toHaveBeenCalled();
  });

  test('side panel - draft campaign - edit', async() => {
    const push = jest.fn();
    const history = { push, listen: jest.fn(), location: mockLocation, createHref: jest.fn() };
    const { getByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List {...commonProps} />
        </Router>
      </Provider>,
    );
    push.mockReset();

    fireEvent.click(getByText('Sample rule #1'));
    const panelSection = document.getElementsByClassName('admin-list__panel-offset')[0];

    expect(queryByText(panelSection, 'Edit')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Duplicate')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Preview')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Activate')).not.toBeInTheDocument();
    expect(queryByText(panelSection, 'Deactivate')).not.toBeInTheDocument();
    expect(queryByText(panelSection, 'Delete')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Created by (User)')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Created by (Team)')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Last updated by (User)')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Last updated by (Team)')).toBeInTheDocument();

    // Side Panel Buttons: 0 - close, 1 - edit/view, 2 - duplicate, 3 - Preview, 4 - Delete
    fireEvent.click(within(panelSection).getAllByRole('button')[1]);
    expect(history.push).toHaveBeenCalledWith('/campaigns/PtD8ZEPFpLcj');
  });

  test('side panel - draft campaign - edit CCAU', async() => {
    const push = jest.fn();
    const history = { push, listen: jest.fn(), location: mockLocation, createHref: jest.fn() };
    const { getByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List {...commonProps}
          type={ruleTypes.CCAU_CAMPAIGN}
          />
        </Router>
      </Provider>,
    );
    push.mockReset();

    fireEvent.click(getByText('Sample rule #1'));
    const panelSection = document.getElementsByClassName('admin-list__panel-offset')[0];

    expect(queryByText(panelSection, 'Edit')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Duplicate')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Preview')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Activate')).not.toBeInTheDocument();
    expect(queryByText(panelSection, 'Deactivate')).not.toBeInTheDocument();
    expect(queryByText(panelSection, 'Delete')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Created by (User)')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Created by (Team)')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Last updated by (User)')).toBeInTheDocument();
    expect(queryByText(panelSection, 'Last updated by (Team)')).toBeInTheDocument();

    // Side Panel Buttons: 0 - close, 1 - edit/view, 2 - duplicate, 3 - Preview, 4 - Delete
    fireEvent.click(within(panelSection).getAllByRole('button')[1]);
    expect(history.push).toHaveBeenCalledWith('/ccau_campaigns/PtD8ZEPFpLcj');
  });

  test('side panel - draft campaign - duplicate', async() => {
    const push = jest.fn();
    const history = { push, listen: jest.fn(), location: mockLocation, createHref: jest.fn() };
    const { getByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List {...commonProps} />
        </Router>
      </Provider>,
    );
    push.mockReset();

    fireEvent.click(getByText('Sample rule #1'));
    const panelSection = document.getElementsByClassName('admin-list__panel-offset')[0];
    fireEvent.click(within(panelSection).getAllByRole('button')[2]);
    expect(history.push).toHaveBeenCalledWith('/campaigns/PtD8ZEPFpLcj/duplicate');
  });

  test('side panel - draft campaign - preview', async() => {
    const history = { push: jest.fn(), listen: jest.fn(), location: mockLocation, createHref: jest.fn() };
    window.open = jest.fn();
    const { getByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List {...commonProps} />
        </Router>
      </Provider>,
    );

    fireEvent.click(getByText('Sample rule #1'));
    const panelSection = document.getElementsByClassName('admin-list__panel-offset')[0];
    fireEvent.click(within(panelSection).getAllByRole('button')[3]);
    expect(window.open).toHaveBeenCalledWith(
      '/preview/4szkx38resvm/sample-content-type-1/sample-content-id-1/sample-container-1/nova/true/undefined',
      'winname',
      'directories=0,titlebar=0,toolbar=0,location=0,status=0,menubar=0,scrollbars=0,resizable=0,width=480,height=720',
    );
  });

  test('side panel - draft campaign - delete', async() => {
    const deleteRule = jest.fn();
    const history = { push: jest.fn(), listen: jest.fn(), location: mockLocation, createHref: jest.fn() };
    const { queryByText, queryAllByText, getByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            deleteRule={deleteRule}
          />
        </Router>
      </Provider>,
    );

    fireEvent.click(getByText('Sample rule #1'));
    const panelSection = document.getElementsByClassName('admin-list__panel-offset')[0];
    fireEvent.click(within(panelSection).getAllByRole('button')[4]);
    expect(queryByText('Are you sure?')).toBeInTheDocument();
    fireEvent.click(queryAllByText('Delete')[1]); // there is two elemnt with Delete text 1) the action menu 2) the delete modal
    expect(deleteRule).toHaveBeenCalledWith('PtD8ZEPFpLcj');
  });

  test('side panel - close modal', async() => {
    const push = jest.fn();
    const history = { push, listen: jest.fn(), location: mockLocation, createHref: jest.fn() };
    const { getByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List {...commonProps} />
        </Router>
      </Provider>,
    );
    push.mockReset();
    fireEvent.click(getByText('Sample rule #1'));
    const panelSection = document.getElementsByClassName('admin-list__panel-section')[0];
    fireEvent.click(within(panelSection).getAllByRole('button')[0]);
    expect(history.push).not.toHaveBeenCalled();
  });

  test('hide activate action for disabled applications containers or pages', () => {
    const getRules = jest.fn();
    const push = jest.fn();
    const deleteRule = jest.fn();
    const history = { push: push, listen: jest.fn(), location: mockLocation, createHref: jest.fn() };
    window.open = jest.fn();
    const mockRules = rulesResponse.filter(r => r.id === 'LFPvqZR0EDA1'); // This rule has disabled applications, pages and container
    const { getAllByLabelText, queryByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            rules={mockRules}
            getRules={getRules}
            deleteRule={deleteRule}
          />
        </Router>
      </Provider>,
    );
    push.mockReset();

    fireEvent.click(getAllByLabelText('Action menu')[0]); // sample rule #1
    expect(queryByText('Activate')).toBeNull();
  });

  test('side panel - draft campaign - preview for rule with language targetting', async() => {
    const history = { push: jest.fn(), listen: jest.fn(), location: mockLocation, createHref: jest.fn() };
    window.open = jest.fn();
    const getRules = jest.fn();
    const mockRules = [ ...rulesResponse ];
    mockRules[0].mass_targeting = {
      languages: [ 'en', 'fr' ],
    };
    const { getByText } = render(
      <Provider store={defaultStore}>
        <Router history={history}>
          <List
            {...commonProps}
            rules={mockRules}
            getRules={getRules}
          />
        </Router>
      </Provider>,
    );

    fireEvent.click(getByText('Sample rule #1'));
    const panelSection = document.getElementsByClassName('admin-list__panel-offset')[0];
    fireEvent.click(within(panelSection).getAllByRole('button')[3]);
    expect(window.open).toHaveBeenCalledWith(
      '/preview/4szkx38resvm/sample-content-type-1/sample-content-id-1/sample-container-1/nova/true/en,fr',
      'winname',
      'directories=0,titlebar=0,toolbar=0,location=0,status=0,menubar=0,scrollbars=0,resizable=0,width=480,height=720',
    );
  });
});
