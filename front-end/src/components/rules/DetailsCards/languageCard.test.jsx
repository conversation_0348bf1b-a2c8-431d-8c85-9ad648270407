import React from 'react';
import { render } from '@testing-library/react';
import { reduxForm } from 'redux-form';
import { createStore } from 'redux';
import { Provider } from 'react-redux';
import mockAxios from 'axios';
import LanguageCardContainer from './languageCard';
import { act } from 'react-dom/test-utils';

const mockApplications = { '1': { 'applicationId': 1, 'contentful_space': '4szkx38resvm' } };

const mockLocalesRes = {
  data: {
    total: 2,
    items: [
      { code: 'en-US', name: 'English (United States)', default: true },
      { code: 'fr', name: 'French' },
    ],
  },
  notifications: [],
};
describe('Languages Card', () => {
  test('render language card', async() => {
    mockAxios.get.mockImplementation(url => Promise.resolve(mockLocalesRes));

    const LanguageCard = ({ ...props }) => {
      const store = createStore(() => { });
      const ReduxLanguageCard = reduxForm({ form: 'languageForm' })(LanguageCardContainer);

      return (
        <Provider store={store}>
          <ReduxLanguageCard {...props} />
        </Provider>
      );
    };
    let element;
    await act(async() => {
      const { container } = render(
        <LanguageCard applications={mockApplications}
          formChange={jest.fn()}
          formValues={{ application: 1 }}
          isCampaign
          formDisabled={false} />
      );
      element = container;
    });
    expect(element).toMatchSnapshot();
  });
  test('render language card - not a campaign', async() => {
    mockAxios.get.mockImplementation(url => Promise.resolve(mockLocalesRes));

    const LanguageCard = ({ ...props }) => {
      const store = createStore(() => { });
      const ReduxLanguageCard = reduxForm({ form: 'languageForm' })(LanguageCardContainer);

      return (
        <Provider store={store}>
          <ReduxLanguageCard {...props} />
        </Provider>
      );
    };
    let element;
    await act(async() => {
      const { container } = render(
        <LanguageCard applications={mockApplications}
          formChange={jest.fn()}
          formValues={{ application: 1 }}
          isCampaign={false}
          formDisabled={false} />
      );
      element = container;
    });
    expect(element).toMatchSnapshot();
  });
  test('render language card - edit mode', async() => {
    mockAxios.get.mockImplementation(url => Promise.resolve(mockLocalesRes));

    const LanguageCard = ({ ...props }) => {
      const store = createStore(() => { });
      const ReduxLanguageCard = reduxForm({ form: 'languageForm' })(LanguageCardContainer);

      return (
        <Provider store={store}>
          <ReduxLanguageCard {...props} />
        </Provider>
      );
    };
    let element;
    await act(async() => {
      const { container } = render(
        <LanguageCard applications={mockApplications}
          formChange={jest.fn()}
          formValues={{ application: 1, id: '1234567' }}
          isCampaign
          formDisabled={false} />
      );
      element = container;
    });
    expect(element).toMatchSnapshot();
  });
  test('render language card - old campaign', async() => {
    mockAxios.get.mockImplementation(url => Promise.resolve(mockLocalesRes));

    const LanguageCard = ({ ...props }) => {
      const store = createStore(() => { });
      const ReduxLanguageCard = reduxForm({ form: 'languageForm' })(LanguageCardContainer);

      return (
        <Provider store={store}>
          <ReduxLanguageCard {...props} />
        </Provider>
      );
    };
    let element;
    await act(async() => {
      const { container } = render(
        <LanguageCard applications={mockApplications}
          formChange={jest.fn()}
          formValues={{ application: 1, mass_targeting: {} }}
          isCampaign
          formDisabled={false} />
      );
      element = container;
    });
    expect(element).toMatchSnapshot();
  });
});
