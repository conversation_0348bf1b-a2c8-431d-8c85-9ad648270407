.segment-targeting {
  &__card {
    margin-bottom: 2rem;
  }

  &__header {
    margin-bottom: 1.5rem;
  }

  &__section {
    margin-bottom: 1.5rem;
    
    &:last-of-type {
      margin-bottom: 0;
    }
  }

  &__row {
    margin-bottom: 0.5rem;
  }

  &__province-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  &__province-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
  }
  
  &__select-all,
  &__clear-all {
    background: none;
    border: none;
    padding: 0;
    font-size: 16px;
    cursor: pointer;
    text-decoration: none;
    
    &:focus {
      outline: 2px solid #0066cc;
      outline-offset: 2px;
    }
  }
  
  &__select-all {
    color: #0066cc;
    
    &:hover:not([disabled]) {
      text-decoration: underline;
    }
    
    &[disabled] {
      color: #999;
      cursor: not-allowed;
    }
  }
  
  &__clear-all {
    color: #666;
    
    &:hover:not([disabled]) {
      text-decoration: underline;
    }
    
    &[disabled] {
      color: #999;
      cursor: not-allowed;
    }
  }
  
  &__separator {
    color: #ccc;
    margin: 0 0.25rem;
  }

  &__province-pills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
    margin-top: 0.5rem;
  }
  
  &__pill {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    font-size: 0.875rem;
    line-height: 1.2;
    
    &:hover {
      background-color: #efefef;
    }
  }
  
  &__pill-label {
    margin-right: 0.5rem;
  }
  
  &__pill-close {
    width: 20px;
    height: 20px;
    min-width: 20px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: 1px solid #ccc;
    border-radius: 50%;
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
    color: #666;
    
    &:hover:not([disabled]) {
      background-color: rgba(0, 0, 0, 0.1);
      border-color: #999;
    }
    
    &[disabled] {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }

  &__province-form {
    // Form wrapper to prevent submission
    margin: 0;
  }

  &__province-container {
    margin-top: 0;
    position: relative;
    
    // Override the global #content style for MultiSelect button
    #segmentId-multiselect {
      #content {
        min-height: auto !important;
        height: auto !important;
        max-height: none !important;
      }
    }
    
    // The MultiSelectWithControls handles its own spacing and styling
    .multiselect-with-controls {
      // Remove extra spacing from the controls header since we moved it
      &__header {
        display: none;
      }
      
      // Hide chips since we're showing them above
      &__chips {
        display: none;
      }
      
      // Fix the canvas-core MultiSelect ListBox positioning issue
      // The ListBox is absolutely positioned but creates whitespace
      &__dropdown {
        position: relative;
        
        // Override canvas-core MultiSelect styles to fix spacing
        // Use global styles to ensure we hit the right elements
        :global {
          // Remove extra height from the wrapper
          .MultiSelectstyle__MultiSelectWrapper-canvas-core__sc-13nynyr-0,
          [class*="MultiSelectWrapper"] {
            min-height: 1rem !important;
            overflow: visible !important;
          }
          
          
          // Target the ListBox directly
          .ListBoxstyle__StyledListBox-canvas-core__sc-1pk57v4-0,
          [class*="StyledListBox"],
          ul[role="listbox"] {
            position: absolute !important;
            top: 100% !important;
            margin-top: 0.5rem !important;
            z-index: 1000 !important;
            
            // Hide it when closed
            &[aria-expanded="false"],
            &:not([open="true"]) {
              display: none !important;
              visibility: hidden !important;
              height: 0 !important;
              overflow: hidden !important;
            }
          }
        }
      }
    }
  }

  &__age-container {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
    align-items: end;
  }

  &__age-field {
    min-width: 120px;
  }

  &__error {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    color: #be061b;
  }

  &__warning {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    color: #be061b;
  }

  &__disabled {
    background-color: #F6F6F6 !important
  }


  // Responsive design
  @media (max-width: 768px) {
    &__age-container {
      flex-direction: column;
      gap: 0.75rem;
      align-items: stretch;
    }

    &__age-field {
      min-width: auto;
    }
    
    &__province-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
    
    &__province-pills {
      gap: 0.375rem;
    }
    
    &__pill {
      font-size: 0.8125rem;
      padding: 0.2rem 0.6rem;
    }
  }
}