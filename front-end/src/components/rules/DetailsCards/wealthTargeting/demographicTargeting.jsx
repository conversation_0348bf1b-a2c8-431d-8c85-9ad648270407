import React, { useState } from 'react';
import PropTypes from 'prop-types';
import Card from 'canvas-core-react/lib/Card';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import Checkbox from 'canvas-core-react/lib/Checkbox';
import MultiSelectWithControls from './MultiSelectWithControls';
import TextField from 'canvas-core-react/lib/TextField';
import IconErrorUniversal from 'canvas-core-react/lib/IconErrorUniversal';
import './demographicTargeting.scss';

// Language options
const LANGUAGE_OPTIONS = [
  { code: 'en', description: 'English' },
  { code: 'fr', description: 'French' },
];

// Country options (radio button - single selection)
const COUNTRY_OPTIONS = [
  { code: 'canada', description: 'Canada' },
  { code: 'non-canada', description: 'Non-Canada' },
];

// Gender options
const GENDER_OPTIONS = [
  { code: 'mr', description: 'Male' },
  { code: 'ms', description: 'Female' },
  { code: 'undisclosed', description: 'Undisclosed' },
];

// Canadian provinces options for MultiSelect
const PROVINCE_OPTIONS = [
  { code: 'AB', description: 'Alberta', label: 'Alberta', value: 'AB', name: 'provinces' },
  { code: 'BC', description: 'British Columbia', label: 'British Columbia', value: 'BC', name: 'provinces' },
  { code: 'MB', description: 'Manitoba', label: 'Manitoba', value: 'MB', name: 'provinces' },
  { code: 'NB', description: 'New Brunswick', label: 'New Brunswick', value: 'NB', name: 'provinces' },
  { code: 'NL', description: 'Newfoundland and Labrador', label: 'Newfoundland and Labrador', value: 'NL', name: 'provinces' },
  { code: 'NT', description: 'Northwest Territories', label: 'Northwest Territories', value: 'NT', name: 'provinces' },
  { code: 'NS', description: 'Nova Scotia', label: 'Nova Scotia', value: 'NS', name: 'provinces' },
  { code: 'NU', description: 'Nunavut', label: 'Nunavut', value: 'NU', name: 'provinces' },
  { code: 'ON', description: 'Ontario', label: 'Ontario', value: 'ON', name: 'provinces' },
  { code: 'PE', description: 'Prince Edward Island', label: 'Prince Edward Island', value: 'PE', name: 'provinces' },
  { code: 'QC', description: 'Quebec', label: 'Quebec', value: 'QC', name: 'provinces' },
  { code: 'SK', description: 'Saskatchewan', label: 'Saskatchewan', value: 'SK', name: 'provinces' },
  { code: 'YT', description: 'Yukon', label: 'Yukon', value: 'YT', name: 'provinces' },
];

const DemographicTargeting = ({
  // Redux Form props
  input,
  meta,
  // Component-specific props
  disabled,
  value: directValue,
  onChange: directOnChange,
  isWealthCampaignSelected,
  error: directError,
  ...rest
}) => {
  // Default value structure
  const defaultValue = {
    languages: [ 'en', 'fr' ], // Default both languages selected
    country: [ 'canada', 'non-canada' ], // Default both countries selected
    provinces: [ 'AB', 'BC', 'MB', 'NB', 'NL', 'NT', 'NS', 'NU', 'ON', 'PE', 'QC', 'SK', 'YT' ], // Default all provinces
    gender: [ 'mr', 'ms', 'undisclosed' ], // Default all genders selected
    age_min: '',
    age_max: '',
  };

  // Support both direct usage and Redux Form usage
  const value = input?.value || directValue || defaultValue;
  const onChange = input?.onChange || directOnChange;
  const error = (meta?.touched && meta?.error) || directError;

  // Initialize the form value if it's empty on mount
  React.useEffect(() => {
    if (input && onChange && (!input.value || Object.keys(input.value).length === 0)) {
      onChange(defaultValue);
    }
  }, []); // Only run on mount

  // State for MultiSelect
  const [ isProvinceSelectOpen, setIsProvinceSelectOpen ] = useState(false);

  // Handle MultiSelect open/close to prevent scrolling issues
  const handleProvinceSelectToggle = (isOpen, event) => {
    setIsProvinceSelectOpen(isOpen);

    // If opening, scroll the element into view gently to prevent jarring movement
    if (isOpen) {
      setTimeout(() => {
        const element = document.getElementById('province-multiselect');
        if (element) {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'nearest',
          });
        }
      }, 100);
    }
  };

  // Create province options with checked status for MultiSelect
  const provinceOptionsWithChecked = PROVINCE_OPTIONS.map(province => ({
    ...province,
    checked: value.provinces?.includes(province.code) || false,
  }));

  const handleFieldChange = (field, newValue) => {
    if (disabled) return;

    const updatedValue = {
      ...value,
      [field]: newValue,
    };

    // If country changes and canada is removed, clear provinces
    if (field === 'country' && !newValue.includes('canada')) {
      updatedValue.provinces = [];
    } else if (field === 'country' && newValue.includes('canada') && !value.country?.includes('canada')) {
      // If canada is added back, restore all provinces
      updatedValue.provinces = PROVINCE_OPTIONS.map(p => p.code);
    }

    onChange(updatedValue);
  };

  const handleLanguageChange = (languageCode, checked) => {
    if (disabled) return;

    const currentLanguages = value.languages || [];
    let newLanguages;

    if (checked) {
      newLanguages = [ ...currentLanguages, languageCode ];
    } else {
      newLanguages = currentLanguages.filter(code => code !== languageCode);
    }

    handleFieldChange('languages', newLanguages);
  };

  const handleProvinceChange = (checked, item, index, provinceValue, selectedItems, event) => {
    if (disabled) return;

    // Only stop propagation, don't prevent default checkbox behavior
    if (event) {
      event.stopPropagation();
    }

    const currentProvinces = value.provinces || [];
    let newProvinces;

    if (checked) {
      newProvinces = [ ...currentProvinces, item.value ];
    } else {
      newProvinces = currentProvinces.filter(code => code !== item.value);
    }

    handleFieldChange('provinces', newProvinces);
  };

  const handleAgeChange = (field, ageValue) => {
    if (disabled) return;

    // Only allow numbers 1-99
    if (ageValue !== '' && (isNaN(ageValue) || ageValue < 1 || ageValue > 99)) {
      return;
    }

    handleFieldChange(field, ageValue);
  };

  const isCanadaSelected = value.country?.includes('canada');

  // Handle Select All Provinces
  const handleSelectAllProvinces = () => {
    if (disabled) return;
    handleFieldChange('provinces', PROVINCE_OPTIONS.map(p => p.code));
  };

  // Handle Clear All Provinces
  const handleClearAllProvinces = () => {
    if (disabled) return;
    handleFieldChange('provinces', []);
  };

  if (!isWealthCampaignSelected) {
    return null;
  }

  return (
    <Card className="demographic-targeting__card" type="floatLow">
      <TextHeadline
        component="h2"
        size={21}
        className="demographic-targeting__header"
      >
        Target by Demographic
      </TextHeadline>

      { /* Customer Profile Language */ }
      <div className="demographic-targeting__section">
        <TextSubtitle type="2" component="h3">
          Customer profile language
        </TextSubtitle>
        <div className="demographic-targeting__checkbox-group">
          { LANGUAGE_OPTIONS.map((language) => (
            <Checkbox
              key={language.code}
              id={`language-${language.code}`}
              label={language.description}
              checked={value.languages?.includes(language.code) || false}
              onChange={(e) => handleLanguageChange(language.code, e.target.checked)}
              disabled={disabled}
            />
          )) }
        </div>
      </div>

      { /* Country */ }
      <div className="demographic-targeting__section">
        <TextSubtitle type="2" component="h3">
          Country
        </TextSubtitle>
        <div className="demographic-targeting__checkbox-group">
          { COUNTRY_OPTIONS.map((country) => (
            <Checkbox
              key={country.code}
              id={`country-${country.code}`}
              label={country.description}
              checked={value.country?.includes(country.code) || false}
              onChange={(e) => {
                const currentCountries = value.country || [];
                let newCountries;
                if (e.target.checked) {
                  newCountries = [ ...currentCountries, country.code ];
                } else {
                  newCountries = currentCountries.filter(c => c !== country.code);
                }
                handleFieldChange('country', newCountries);
              }}
              disabled={disabled}
            />
          )) }
        </div>
      </div>

      { /* Canadian Province - Only show when Canada is selected */ }
      { isCanadaSelected && (
        <div className="demographic-targeting__section">
          <div className="demographic-targeting__province-header">
            <TextSubtitle type="2" component="h3">
              Canadian province
            </TextSubtitle>
            <div className="demographic-targeting__province-controls">
              <button
                type="button"
                onClick={handleSelectAllProvinces}
                disabled={disabled || value.provinces?.length === PROVINCE_OPTIONS.length}
                className="demographic-targeting__select-all"
              >
                ✓ Select all { PROVINCE_OPTIONS.length }
              </button>
              <span className="demographic-targeting__separator">|</span>
              <button
                type="button"
                onClick={handleClearAllProvinces}
                disabled={disabled || value.provinces?.length === 0}
                className="demographic-targeting__clear-all"
              >
                ✕ Clear all
              </button>
            </div>
          </div>

          <form
            onSubmit={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            className="demographic-targeting__province-form"
          >
            <div
              className="demographic-targeting__province-container"
              role="group"
              aria-label="Province selection"
            >
              <MultiSelectWithControls
                id="province-multiselect"
                label="Province"
                placeholder="Select Canadian province(s)"
                options={provinceOptionsWithChecked}
                isMutliSelectOpen={isProvinceSelectOpen}
                setIsMultiSelectOpen={handleProvinceSelectToggle}
                onChange={handleProvinceChange}
                disabled={disabled}
                itemsSelectedLocale="Canadian province(s) selected"
                className="demographic-targeting__province-multiselect"
                showChips={false}
                showControls={false}
                key={value.provinces?.join(',')}
              />
            </div>
          </form>

          { /* Province Pills */ }
          { value.provinces?.length > 0 && (
            <div className="demographic-targeting__province-pills">
              { value.provinces.map((provinceCode) => {
                const province = PROVINCE_OPTIONS.find(p => p.code === provinceCode);
                return province ? (
                  <div key={provinceCode} className="demographic-targeting__pill">
                    <span className="demographic-targeting__pill-label">{ province.label }</span>
                    <button
                      type="button"
                      className="demographic-targeting__pill-close"
                      onClick={() => handleProvinceChange(false, province, null, province.value)}
                      disabled={disabled}
                      aria-label={`Remove ${province.label}`}
                    >
                      ×
                    </button>
                  </div>
                ) : null;
              }) }
            </div>
          ) }
        </div>
      ) }

      { /* Age Group */ }
      <div className="demographic-targeting__section">
        <TextSubtitle
          type="2"
          component="h3"
          className={error && error.includes('age') ? 'demographic-targeting__subtitle--error' : ''}
        >
          Age group
        </TextSubtitle>
        <div className="demographic-targeting__age-container">
          <div className="demographic-targeting__age-field">
            <TextField
              id="age-from"
              label="From"
              type="number"
              value={value.age_min || ''}
              onChange={(e) => handleAgeChange('age_min', e.target.value)}
              disabled={disabled}
              placeholder="Num > 0"
              min="1"
              max="99"
              error={error && error.includes('age')}
            />
          </div>
          <div className="demographic-targeting__age-field">
            <TextField
              id="age-to"
              label="To"
              type="number"
              value={value.age_max || ''}
              onChange={(e) => handleAgeChange('age_max', e.target.value)}
              disabled={disabled}
              placeholder="Num < 100"
              min="1"
              max="99"
              error={error && error.includes('age')}
            />
          </div>
        </div>
        { error && error.includes('age') && (
          <div className="demographic-targeting__error demographic-targeting__error--inline">
            <IconErrorUniversal color="red" />
            <TextCaption type="error" component="p" className="demographic-targeting__error-text">{ error }</TextCaption>
          </div>
        ) }
      </div>

      { /* Gender */ }
      <div className="demographic-targeting__section">
        <TextSubtitle type="2" component="h3">
          Gender
        </TextSubtitle>
        <div className="demographic-targeting__checkbox-group">
          { GENDER_OPTIONS.map((gender) => (
            <Checkbox
              key={gender.code}
              id={`gender-${gender.code}`}
              label={gender.description}
              checked={value.gender?.includes(gender.code) || false}
              onChange={(e) => {
                const currentGenders = value.gender || [];
                let newGenders;
                if (e.target.checked) {
                  newGenders = [ ...currentGenders, gender.code ];
                } else {
                  newGenders = currentGenders.filter(g => g !== gender.code);
                }
                handleFieldChange('gender', newGenders);
              }}
              disabled={disabled}
            />
          )) }
        </div>
      </div>

      { error && !error.includes('age') && (
        <div className="demographic-targeting__error">
          <IconErrorUniversal color="red" />
          <TextCaption type="error" component="p">{ error }</TextCaption>
        </div>
      ) }
    </Card>
  );
};

DemographicTargeting.propTypes = {
  // Redux Form props (optional for backward compatibility)
  input: PropTypes.shape({
    value: PropTypes.object,
    onChange: PropTypes.func.isRequired,
  }),
  meta: PropTypes.shape({
    touched: PropTypes.bool,
    error: PropTypes.string,
  }),
  // Component-specific props
  disabled: PropTypes.bool,
  // Direct props (used when not with Redux Form)
  value: PropTypes.object,
  onChange: PropTypes.func,
  isWealthCampaignSelected: PropTypes.bool.isRequired,
  error: PropTypes.string,
};

export default DemographicTargeting;
