import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import MultiSelectWithControls from './MultiSelectWithControls';

describe('MultiSelectWithControls Component', () => {
  const mockOptions = [
    { name: 'option1', label: 'Option 1', value: 'opt1', checked: false },
    { name: 'option2', label: 'Option 2', value: 'opt2', checked: true },
    { name: 'option3', label: 'Option 3', value: 'opt3', checked: false },
  ];

  const defaultProps = {
    id: 'test-multiselect',
    label: 'Test MultiSelect',
    placeholder: 'Select options',
    options: mockOptions,
    onChange: jest.fn(),
    isMutliSelectOpen: false,
    setIsMultiSelectOpen: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render the MultiSelect component', () => {
      render(<MultiSelectWithControls {...defaultProps} />);
      expect(screen.getByLabelText('Test MultiSelect')).toBeInTheDocument();
    });

    it('should render with custom className', () => {
      const { container } = render(
        <MultiSelectWithControls {...defaultProps} className="custom-class" />
      );
      expect(container.firstChild).toHaveClass('multiselect-with-controls', 'custom-class');
    });

    it('should not render controls when showControls is false', () => {
      render(<MultiSelectWithControls {...defaultProps} showControls={false} />);
      expect(screen.queryByText(/Select all/)).not.toBeInTheDocument();
      expect(screen.queryByText(/Clear all/)).not.toBeInTheDocument();
    });

    it('should render controls when showControls is true', () => {
      render(<MultiSelectWithControls {...defaultProps} showControls={true} />);
      expect(screen.getByText('✓ Select all 3')).toBeInTheDocument();
      expect(screen.getByText('✕ Clear all')).toBeInTheDocument();
    });

    it('should not render chips when showChips is false', () => {
      const { container } = render(<MultiSelectWithControls {...defaultProps} showChips={false} />);
      const chips = container.querySelectorAll('.multiselect-with-controls__chip');
      expect(chips).toHaveLength(0);
    });

    it('should render chips for selected items when showChips is true', () => {
      const { container } = render(<MultiSelectWithControls {...defaultProps} showChips={true} />);
      // PillButton renders the label, so we can search for it directly
      expect(screen.getByText('Option 2')).toBeInTheDocument();
      // Check that the pill button exists
      const pillButton = container.querySelector('.PillButton__button');
      expect(pillButton).toBeInTheDocument();
      expect(pillButton).toHaveAttribute('label', 'Option 2');
    });
  });

  describe('Select All functionality', () => {
    it('should call onChange for all unchecked items when Select All is clicked', () => {
      render(<MultiSelectWithControls {...defaultProps} showControls={true} />);

      const selectAllButton = screen.getByText('✓ Select all 3');
      fireEvent.click(selectAllButton);

      // Should be called twice (for opt1 and opt3 which were unchecked)
      expect(defaultProps.onChange).toHaveBeenCalledTimes(2);
      expect(defaultProps.onChange).toHaveBeenCalledWith(
        true,
        expect.objectContaining({ value: 'opt1', checked: true }),
        0,
        'opt1',
        expect.any(Array),
        null
      );
      expect(defaultProps.onChange).toHaveBeenCalledWith(
        true,
        expect.objectContaining({ value: 'opt3', checked: true }),
        2,
        'opt3',
        expect.any(Array),
        null
      );
    });

    it('should disable Select All button when all items are selected', () => {
      const allSelectedOptions = mockOptions.map(opt => ({ ...opt, checked: true }));
      render(
        <MultiSelectWithControls
          {...defaultProps}
          options={allSelectedOptions}
          showControls={true}
        />
      );

      const selectAllButton = screen.getByText('✓ Select all 3');
      expect(selectAllButton).toBeDisabled();
    });

    it('should not call onChange when disabled', () => {
      render(
        <MultiSelectWithControls {...defaultProps} showControls={true} disabled={true} />
      );

      const selectAllButton = screen.getByText('✓ Select all 3');
      fireEvent.click(selectAllButton);

      expect(defaultProps.onChange).not.toHaveBeenCalled();
    });
  });

  describe('Clear All functionality', () => {
    it('should call onChange for all checked items when Clear All is clicked', () => {
      render(<MultiSelectWithControls {...defaultProps} showControls={true} />);

      const clearAllButton = screen.getByText('✕ Clear all');
      fireEvent.click(clearAllButton);

      // Should be called once (for opt2 which was checked)
      expect(defaultProps.onChange).toHaveBeenCalledTimes(1);
      expect(defaultProps.onChange).toHaveBeenCalledWith(
        false,
        expect.objectContaining({ value: 'opt2', checked: false }),
        1,
        'opt2',
        [],
        null
      );
    });

    it('should disable Clear All button when no items are selected', () => {
      const noSelectedOptions = mockOptions.map(opt => ({ ...opt, checked: false }));
      render(
        <MultiSelectWithControls
          {...defaultProps}
          options={noSelectedOptions}
          showControls={true}
        />
      );

      const clearAllButton = screen.getByText('✕ Clear all');
      expect(clearAllButton).toBeDisabled();
    });
  });

  describe('Chip removal functionality', () => {
    it('should call onChange when a chip is removed', async() => {
      render(<MultiSelectWithControls {...defaultProps} showChips={true} />);

      // Find the PillButton's close button
      const closeButtons = screen.getAllByRole('button');
      const chipCloseButton = closeButtons.find(button =>
        button.closest('.pill-button__close') !== null
      );

      if (chipCloseButton) {
        fireEvent.click(chipCloseButton);

        await waitFor(() => {
          expect(defaultProps.onChange).toHaveBeenCalledWith(
            false,
            expect.objectContaining({ value: 'opt2' }),
            1,
            'opt2',
            expect.any(Array),
            null
          );
        });
      }
    });

    it('should not remove chip when disabled', () => {
      render(
        <MultiSelectWithControls {...defaultProps} showChips={true} disabled={true} />
      );

      const closeButtons = screen.getAllByRole('button');
      const chipCloseButton = closeButtons.find(button =>
        button.closest('.pill-button__close') !== null
      );

      if (chipCloseButton) {
        fireEvent.click(chipCloseButton);
        expect(defaultProps.onChange).not.toHaveBeenCalled();
      }
    });
  });

  describe('Props validation', () => {
    it('should render with custom itemsSelectedLocale', () => {
      render(
        <MultiSelectWithControls
          {...defaultProps}
          itemsSelectedLocale="custom items selected"
        />
      );

      // This prop is passed to the MultiSelect component
      expect(screen.getByLabelText('Test MultiSelect')).toBeInTheDocument();
    });

    it('should handle empty options array', () => {
      render(<MultiSelectWithControls {...defaultProps} options={[]} />);
      expect(screen.getByLabelText('Test MultiSelect')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper focus management for Select All button', () => {
      render(<MultiSelectWithControls {...defaultProps} showControls={true} />);

      const selectAllButton = screen.getByText('✓ Select all 3');
      selectAllButton.focus();

      expect(selectAllButton).toHaveFocus();
    });

    it('should have proper focus management for Clear All button', () => {
      render(<MultiSelectWithControls {...defaultProps} showControls={true} />);

      const clearAllButton = screen.getByText('✕ Clear all');
      clearAllButton.focus();

      expect(clearAllButton).toHaveFocus();
    });
  });
});
