import React, { useCallback, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import Table from 'canvas-core-react/lib/Table';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import Card from 'canvas-core-react/lib/Card';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import Checkbox from 'canvas-core-react/lib/Checkbox';
import './investmentKnowledgeTargeting.scss';
import IconCautionUniversal from 'canvas-core-react/lib/IconCautionUniversal';

export const KNOWLEDGE_LEVELS = ['L', 'M', 'H'];
export const INVESTMENT_TYPES = [
  'mf_knowledge',
  'fix_income_knowledge',
  'stock_knowledge',
  'margin_knowledge',
  'equity_options_knowledge',
  'short_sale_knowledge',
];

const InvestingKnowledgeTargeting = ({
  // Redux Form props
  input,
  meta,
  // Component-specific props
  application,
  disabled,
  value: directValue,
  onChange: directOnChange,
  isITradeLobSelected,
  error: directError,
  ...rest
}) => {

  // Support both direct usage and Redux Form usage
  const value = input?.value || directValue || (() => {
    const initialState = {};
    // Default to all selected
    INVESTMENT_TYPES.forEach(type => {
      initialState[type] = KNOWLEDGE_LEVELS;
    });

    return initialState;
  })();

  const onChange = input?.onChange || directOnChange || [];
  const error = (meta?.touched && meta?.error) || directError;

  // Initialize the form value if it's empty on mount
  useEffect(() => {
    if (input && onChange && (!input.value)) {
      onChange(value);
    }
  }, []); // Only run on mount

  const handleKnowledgeChange = useCallback((type, level) => {
    const currentSelections = value[type] || [];
    const newSelections = currentSelections.includes(level)
    ? currentSelections.filter(lvl => lvl !== level)
    : [ ...currentSelections, level ];

    const newState = {
      ...value,
      [type]: newSelections,
    };

    onChange(newState);
  }, [value, onChange]);

   // Handle Select All
   const handleSelectAll = () => {
    if (disabled) return;
    const newState = {};
    INVESTMENT_TYPES.forEach(type => {
      newState[type] = KNOWLEDGE_LEVELS;
    });
    onChange(newState);
  };

  // Handle Clear All
  const handleClearAll = () => {
    if (disabled) return;
    const newState = {};
    INVESTMENT_TYPES.forEach(type => {
      newState[type] = [];
    });
    onChange(newState);
  };

  const tableData = useMemo(() => {
    return INVESTMENT_TYPES.map(type => ({
      type,
      knowledge: value[type] || [],
    }));
  }, [value])

  const selectedCount = useMemo(() => {
    return Object.values(value).reduce((count, levels) => count + (levels?.length || 0), 0);
  }, [value]);

  const totalCount = INVESTMENT_TYPES.length * KNOWLEDGE_LEVELS.length;
  const isAllSelected = selectedCount === totalCount;
  const isNoneSelected = selectedCount === 0;

  const columns = useMemo(() => {
    const displayNames = {
      mf_knowledge: 'Mutual Fund',
      fix_income_knowledge: 'Fixed Income',
      stock_knowledge: 'Stock',
      margin_knowledge: 'Margin',
      equity_options_knowledge: 'Equity Options',
      short_sale_knowledge: 'Short Sale',
    };

    return [
      {
        name: 'Investment type',
        cellFormatter: row => (
          <TextCaption component="p">
            { displayNames[row.type] }
          </TextCaption>
        ),
        selector: '',
        grow: 2,
        style: { textAlign: 'left' },
      }, {
        name: 'Low',
        cellFormatter: row => (
          <Checkbox
            id={`${row.type}-low`}
            checked={row.knowledge.includes('L') || false}
            onChange={() => handleKnowledgeChange(row.type, 'L')}
            disabled={disabled}
          />
        ),
        selector: '',
      }, {
        name: 'Medium',
        cellFormatter: row => (
          <Checkbox
            id={`${row.type}-medium`}
            checked={row.knowledge.includes('M') || false}
            onChange={() => handleKnowledgeChange(row.type, 'M')}
            disabled={disabled}
          />
        ),
        selector: '',
      }, {
        name: 'High',
        cellFormatter: (row) => (
          <Checkbox
            id={`${row.type}-high`}
            checked={row.knowledge.includes('H') || false}
            onChange={() => handleKnowledgeChange(row.type, 'H')}
            disabled={disabled}
          />
        ),
        selector: '',
      },
    ]
  }, [handleKnowledgeChange]);

  return (
    <Card className={classnames("investment-knowledge-targeting__card", { "investment-knowledge-targeting__disabled": !isITradeLobSelected })} type="floatLow">
      <TextHeadline
        component="h2"
        size={21}
        className="investment-knowledge-targeting__header"
      >
        Target by Investing Knowledge Level
      </TextHeadline>

      { isITradeLobSelected ? (
        <>
        <div className="investment-knowledge-targeting__section">
          <Table
            className="investment-knowledge-targeting__table"
            id="investment-knowledge-targeting"
            title=""
            size='small'
            columns={columns}
            data={tableData}
          />
          <div className='investment-knowledge-targeting__table__footer'>
              <TextCaption component='p'>{selectedCount} of {totalCount} selected</TextCaption>
              <button
                  type="button"
                  onClick={handleSelectAll}
                  disabled={disabled || isAllSelected}
                  className="investment-knowledge-targeting__select-all"
                >
                  ✓ Select all { totalCount }
                </button>
                <button
                type="button"
                onClick={handleClearAll}
                disabled={disabled || isNoneSelected}
                className="investment-knowledge-targeting__clear-all"
              >
                ✕ Clear all
              </button>
          </div>
        </div>
      </>
      ) : (
        <div className="investment-knowledge-targeting__warning">
          <IconCautionUniversal color="red" />
          <TextCaption type="error" component="p">Only for ITrade LOB</TextCaption>
        </div>
      ) }

    </Card>
  );
};

InvestingKnowledgeTargeting.propTypes = {
  // Redux Form props (optional for backward compatibility)
  input: PropTypes.shape({
    value: PropTypes.object,
    onChange: PropTypes.func.isRequired,
  }),
  meta: PropTypes.shape({
    touched: PropTypes.bool,
    error: PropTypes.string,
  }),
  // Component-specific props
  disabled: PropTypes.bool,
  // Direct props (used when not with Redux Form)
  value: PropTypes.object,
  onChange: PropTypes.func,
  isITradeLobSelected: PropTypes.bool.isRequired,
  error: PropTypes.string,
};

InvestingKnowledgeTargeting.defaultProps = {
  data: [],
};

export default InvestingKnowledgeTargeting;
