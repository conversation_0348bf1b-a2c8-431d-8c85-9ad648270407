.multiselect-with-controls {
  position: relative;
  
  &__header {
    margin-bottom: 0.75rem;
  }
  
  &__controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
  }
  
  &__select-all,
  &__clear-all {
    background: none;
    border: none;
    padding: 0.25rem 0.5rem;
    font-size: 1.75rem;
    line-height: 1.2;
    cursor: pointer;
    text-decoration: none;
    
    &:focus {
      outline: 2px solid #0066cc;
      outline-offset: 2px;
    }
  }
  
  &__select-all {
    color: #0066cc;
    
    &:hover:not([disabled]) {
      color: #0052a3;
      text-decoration: underline;
    }
    
    &[disabled] {
      color: #999;
      cursor: not-allowed;
    }
  }
  
  &__clear-all {
    color: #cc6600;
    
    &:hover:not([disabled]) {
      color: #a3520a;
      text-decoration: underline;
    }
    
    &[disabled] {
      color: #999;
      cursor: not-allowed;
    }
  }
  
  &__separator {
    color: #ccc;
    margin: 0 0.25rem;
  }
  
  &__chips {
    margin-bottom: 0.75rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  &__chip {
    // Override canvas-core PillButton styles for consistent appearance
    :global {
      .pill-button {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        background-color: #f5f5f5;
        border: 1px solid #e0e0e0;
        border-radius: 20px;
        font-size: 0.875rem;
        line-height: 1.2;
        max-width: 200px;
        
        &:hover {
          background-color: #efefef;
        }
        
        .pill-button__label {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-right: 0.5rem;
        }
        
        .pill-button__close {
          width: 16px;
          height: 16px;
          min-width: 16px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: transparent;
          border: none;
          cursor: pointer;
          flex-shrink: 0;
          
          svg {
            width: 10px;
            height: 10px;
          }
          
          &:hover {
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 50%;
          }
        }
      }
    }
  }
  
  &__dropdown {
    position: relative;
  }
  
  // Responsive design
  @media (max-width: 768px) {
    &__controls {
      font-size: 0.8rem;
      gap: 0.375rem;
    }
    
    &__select-all,
    &__clear-all {
      padding: 0.2rem 0.4rem;
      font-size: 0.8rem;
    }
    
    &__chips {
      margin-bottom: 0.75rem;
      
      .grid .row {
        margin-bottom: -0.375rem;
      }
    }
    
    &__chip {
      margin-bottom: 0.375rem;
    }
  }
  
  // Disabled state
  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
  
  // Loading state (for future enhancement)
  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    color: #666;
    font-style: italic;
  }
}