import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import DemographicTargeting from './demographicTargeting';

const mockOnChange = jest.fn();

const defaultProps = {
  disabled: false,
  value: {
    languages: [ 'en', 'fr' ],
    country: ['canada'],
    provinces: [ 'AB', 'BC', 'MB', 'NB', 'NL', 'NT', 'NS', 'NU', 'ON', 'PE', 'QC', 'SK', 'YT' ],
    gender: 'mr',
    age_min: '',
    age_max: '',
  },
  onChange: mockOnChange,
  isWealthCampaignSelected: true,
  error: null,
};

describe('DemographicTargeting Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render the component when wealth campaign is selected', () => {
      render(<DemographicTargeting {...defaultProps} />);

      expect(screen.getByText('Target by Demographic')).toBeInTheDocument();
      expect(screen.getByText('Customer profile language')).toBeInTheDocument();
      expect(screen.getByText('Country')).toBeInTheDocument();
      expect(screen.getByText('Canadian province')).toBeInTheDocument();
      expect(screen.getByText('Age group')).toBeInTheDocument();
      expect(screen.getByText('Gender')).toBeInTheDocument();
    });

    it('should not render when wealth campaign is not selected', () => {
      render(<DemographicTargeting {...defaultProps} isWealthCampaignSelected={false} />);

      expect(screen.queryByText('Target by Demographic')).not.toBeInTheDocument();
    });

    it('should render all language options', () => {
      render(<DemographicTargeting {...defaultProps} />);

      expect(screen.getByLabelText('English')).toBeInTheDocument();
      expect(screen.getByLabelText('French')).toBeInTheDocument();
    });

    it('should render all country options', () => {
      render(<DemographicTargeting {...defaultProps} />);

      expect(screen.getByLabelText('Canada')).toBeInTheDocument();
      expect(screen.getByLabelText('Non-Canada')).toBeInTheDocument();
    });

    it('should render all gender options', () => {
      render(<DemographicTargeting {...defaultProps} />);

      expect(screen.getByLabelText('Male')).toBeInTheDocument();
      expect(screen.getByLabelText('Female')).toBeInTheDocument();
      expect(screen.getByLabelText('Undisclosed')).toBeInTheDocument();
    });

    it('should render age input fields', () => {
      render(<DemographicTargeting {...defaultProps} />);

      expect(screen.getByLabelText('From')).toBeInTheDocument();
      expect(screen.getByLabelText('To')).toBeInTheDocument();
    });
  });

  describe('Redux Form Integration', () => {
    it('should work with Redux Form input and meta props', () => {
      const reduxFormProps = {
        input: {
          value: {
            languages: [ 'en' ],
            country: ['canada'], // Single selection
            provinces: [ 'AB' ],
            gender: 'mr', // Single selection
            age_min: '18',
            age_max: '65',
          },
          onChange: mockOnChange,
        },
        meta: {
          touched: true,
          error: 'Test error message',
        },
        isWealthCampaignSelected: true,
      };

      render(<DemographicTargeting {...reduxFormProps} />);

      expect(screen.getByLabelText('English')).toBeChecked();
      expect(screen.getByLabelText('French')).not.toBeChecked();
      expect(screen.getByLabelText('Canada')).toBeChecked();
      expect(screen.getByLabelText('Male')).toBeChecked();
      expect(screen.getByText('Test error message')).toBeInTheDocument();
    });
  });

  describe('Value Prop Handling', () => {
    it('should pre-select options based on value prop', () => {
      const customValue = {
        languages: [ 'en' ],
        country: ['canada'], // Single selection
        provinces: [ 'AB', 'BC' ],
        gender: 'ms', // Single selection
        age_min: '25',
        age_max: '45',
      };

      render(<DemographicTargeting {...defaultProps} value={customValue} />);

      expect(screen.getByLabelText('English')).toBeChecked();
      expect(screen.getByLabelText('French')).not.toBeChecked();
      expect(screen.getByLabelText('Canada')).toBeChecked();
      expect(screen.getByLabelText('Non-Canada')).not.toBeChecked();
      expect(screen.getByLabelText('Male')).not.toBeChecked();
      expect(screen.getByLabelText('Female')).toBeChecked();
      expect(screen.getByLabelText('Undisclosed')).not.toBeChecked();
      expect(screen.getByDisplayValue('25')).toBeInTheDocument();
      expect(screen.getByDisplayValue('45')).toBeInTheDocument();
    });

    it('should handle undefined value gracefully', () => {
      render(<DemographicTargeting {...defaultProps} value={undefined} />);

      // Should use default values (all selections for country and gender)
      expect(screen.getByLabelText('English')).toBeChecked();
      expect(screen.getByLabelText('French')).toBeChecked();
      expect(screen.getByLabelText('Canada')).toBeChecked();
      expect(screen.getByLabelText('Non-Canada')).toBeChecked();
      expect(screen.getByLabelText('Male')).toBeChecked();
      expect(screen.getByLabelText('Female')).toBeChecked();
      expect(screen.getByLabelText('Undisclosed')).toBeChecked();
    });
  });

  describe('Language Selection', () => {
    it('should handle language checkbox changes', () => {
      render(<DemographicTargeting {...defaultProps} value={{ ...defaultProps.value, languages: [ 'en' ] }} />);

      const frenchCheckbox = screen.getByLabelText('French');
      fireEvent.click(frenchCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith({
        ...defaultProps.value,
        languages: [ 'en', 'fr' ],
      });
    });

    it('should handle language deselection', () => {
      render(<DemographicTargeting {...defaultProps} value={{ ...defaultProps.value, languages: [ 'en', 'fr' ] }} />);

      const englishCheckbox = screen.getByLabelText('English');
      fireEvent.click(englishCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith({
        ...defaultProps.value,
        languages: [ 'fr' ],
      });
    });
  });

  describe('Country and Province Logic', () => {
    it('should show province multiselect when Canada is selected', () => {
      render(<DemographicTargeting {...defaultProps} value={{ ...defaultProps.value, country: ['canada'] }} />);

      expect(screen.getByText('Canadian province')).toBeInTheDocument();
      expect(screen.getByLabelText('Province')).toBeInTheDocument();
    });

    it('should hide province multiselect when Canada is not selected', () => {
      render(<DemographicTargeting {...defaultProps} value={{ ...defaultProps.value, country: ['non-canada'] }} />);

      expect(screen.queryByText('Canadian province')).not.toBeInTheDocument();
      expect(screen.queryByLabelText('Province')).not.toBeInTheDocument();
    });

    it('should clear provinces when switching from Canada to Non-Canada', () => {
      render(<DemographicTargeting {...defaultProps} value={{
        ...defaultProps.value,
        country: ['canada'],
        provinces: [ 'AB', 'BC' ],
      }} />);

      const nonCanadaCheckbox = screen.getByLabelText('Non-Canada');
      fireEvent.click(nonCanadaCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith({
        ...defaultProps.value,
        country: ['canada', 'non-canada'],
        provinces: [ 'AB', 'BC' ],
      });
    });

    it('should auto-select all provinces when adding Canada to selection', () => {
      render(<DemographicTargeting {...defaultProps} value={{
        ...defaultProps.value,
        country: ['non-canada'],
        provinces: [],
      }} />);

      const canadaCheckbox = screen.getByLabelText('Canada');
      fireEvent.click(canadaCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith({
        ...defaultProps.value,
        country: ['non-canada', 'canada'],
        provinces: [ 'AB', 'BC', 'MB', 'NB', 'NL', 'NT', 'NS', 'NU', 'ON', 'PE', 'QC', 'SK', 'YT' ],
      });
    });
  });

  describe('Gender Selection', () => {
    it('should handle gender selection changes', () => {
      render(<DemographicTargeting {...defaultProps} value={{ ...defaultProps.value, gender: ['mr'] }} />);

      const femaleCheckbox = screen.getByLabelText('Female');
      fireEvent.click(femaleCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith({
        ...defaultProps.value,
        gender: ['mr', 'ms'],
      });
    });

    it('should handle different gender selection', () => {
      render(<DemographicTargeting {...defaultProps} value={{ ...defaultProps.value, gender: ['ms'] }} />);

      const undisclosedCheckbox = screen.getByLabelText('Undisclosed');
      fireEvent.click(undisclosedCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith({
        ...defaultProps.value,
        gender: ['ms', 'undisclosed'],
      });
    });
  });

  describe('Age Range Input', () => {
    it('should handle age input changes', () => {
      render(<DemographicTargeting {...defaultProps} />);

      const ageFromInput = screen.getByLabelText('From');
      fireEvent.change(ageFromInput, { target: { value: '25' } });

      expect(mockOnChange).toHaveBeenCalledWith({
        ...defaultProps.value,
        age_min: '25',
      });
    });

    it('should handle age_max input changes', () => {
      render(<DemographicTargeting {...defaultProps} />);

      const ageToInput = screen.getByLabelText('To');
      fireEvent.change(ageToInput, { target: { value: '65' } });

      expect(mockOnChange).toHaveBeenCalledWith({
        ...defaultProps.value,
        age_max: '65',
      });
    });

    it('should reject invalid age values (less than 1)', () => {
      render(<DemographicTargeting {...defaultProps} />);

      const ageFromInput = screen.getByLabelText('From');
      fireEvent.change(ageFromInput, { target: { value: '0' } });

      expect(mockOnChange).not.toHaveBeenCalled();
    });

    it('should reject invalid age values (greater than 99)', () => {
      render(<DemographicTargeting {...defaultProps} />);

      const ageFromInput = screen.getByLabelText('From');
      fireEvent.change(ageFromInput, { target: { value: '100' } });

      expect(mockOnChange).not.toHaveBeenCalled();
    });

    it('should reject non-numeric age values', () => {
      render(<DemographicTargeting {...defaultProps} />);

      const ageFromInput = screen.getByLabelText('From');
      fireEvent.change(ageFromInput, { target: { value: 'abc' } });

      expect(mockOnChange).not.toHaveBeenCalled();
    });

    it('should allow empty age values', () => {
      render(<DemographicTargeting {...defaultProps} value={{ ...defaultProps.value, age_min: '25' }} />);

      const ageFromInput = screen.getByLabelText('From');
      fireEvent.change(ageFromInput, { target: { value: '' } });

      expect(mockOnChange).toHaveBeenCalledWith({
        ...defaultProps.value,
        age_min: '',
      });
    });
  });

  describe('Disabled State', () => {
    it('should disable all inputs when disabled prop is true', () => {
      render(<DemographicTargeting {...defaultProps} disabled={true} />);

      expect(screen.getByLabelText('English')).toBeDisabled();
      expect(screen.getByLabelText('French')).toBeDisabled();
      expect(screen.getByLabelText('Canada')).toBeDisabled();
      expect(screen.getByLabelText('Non-Canada')).toBeDisabled();
      expect(screen.getByLabelText('Male')).toBeDisabled();
      expect(screen.getByLabelText('Female')).toBeDisabled();
      expect(screen.getByLabelText('Undisclosed')).toBeDisabled();
      expect(screen.getByLabelText('From')).toBeDisabled();
      expect(screen.getByLabelText('To')).toBeDisabled();
    });

    it('should not trigger onChange when disabled and checkboxes are clicked', () => {
      render(<DemographicTargeting {...defaultProps} disabled={true} />);

      const englishCheckbox = screen.getByLabelText('English');
      fireEvent.click(englishCheckbox);

      expect(mockOnChange).not.toHaveBeenCalled();
    });

    it('should not trigger onChange when disabled and age inputs are changed', () => {
      render(<DemographicTargeting {...defaultProps} disabled={true} />);

      const ageFromInput = screen.getByLabelText('From');
      fireEvent.change(ageFromInput, { target: { value: '25' } });

      expect(mockOnChange).not.toHaveBeenCalled();
    });
  });

  describe('Error Display', () => {
    it('should show error message when error prop is provided', () => {
      render(<DemographicTargeting {...defaultProps} error="Test error message" />);

      expect(screen.getByText('Test error message')).toBeInTheDocument();
    });

    it('should not show error message when error prop is not provided', () => {
      render(<DemographicTargeting {...defaultProps} />);

      expect(screen.queryByText('Test error message')).not.toBeInTheDocument();
    });

    it('should show Redux Form error when meta.touched and meta.error are provided', () => {
      const reduxFormProps = {
        input: {
          value: defaultProps.value,
          onChange: mockOnChange,
        },
        meta: {
          touched: true,
          error: 'Redux Form error',
        },
        isWealthCampaignSelected: true,
      };

      render(<DemographicTargeting {...reduxFormProps} />);

      expect(screen.getByText('Redux Form error')).toBeInTheDocument();
    });
  });

  describe('Province MultiSelect Integration', () => {
    it('should handle province selection changes', () => {
      render(<DemographicTargeting {...defaultProps} value={{
        ...defaultProps.value,
        country: ['canada'],
        provinces: [ 'AB' ],
      }} />);

      // Since MultiSelect is a complex component, we'll test by checking the rendered content
      expect(screen.getByText('Canadian province')).toBeInTheDocument();
      expect(screen.getByLabelText('Province')).toBeInTheDocument();
    });
  });

  describe('Province Selection Controls', () => {
    it('should render Select All and Clear All buttons for provinces', () => {
      render(<DemographicTargeting {...defaultProps} value={{
        ...defaultProps.value,
        country: ['canada'],
        provinces: [ 'AB', 'BC' ],
      }} />);

      expect(screen.getByText('✓ Select all 13')).toBeInTheDocument();
      expect(screen.getByText('✕ Clear all')).toBeInTheDocument();
    });

    it('should select all provinces when Select all is clicked', () => {
      render(<DemographicTargeting {...defaultProps} value={{
        ...defaultProps.value,
        country: ['canada'],
        provinces: [ 'AB' ],
      }} />);

      const selectAllButton = screen.getByText('✓ Select all 13');
      fireEvent.click(selectAllButton);

      expect(mockOnChange).toHaveBeenCalledWith({
        ...defaultProps.value,
        country: ['canada'],
        provinces: [ 'AB', 'BC', 'MB', 'NB', 'NL', 'NT', 'NS', 'NU', 'ON', 'PE', 'QC', 'SK', 'YT' ],
      });
    });

    it('should clear all provinces when Clear all is clicked', () => {
      render(<DemographicTargeting {...defaultProps} value={{
        ...defaultProps.value,
        country: ['canada'],
        provinces: [ 'AB', 'BC', 'ON' ],
      }} />);

      const clearAllButton = screen.getByText('✕ Clear all');
      fireEvent.click(clearAllButton);

      expect(mockOnChange).toHaveBeenCalledWith({
        ...defaultProps.value,
        country: ['canada'],
        provinces: [],
      });
    });

    it('should disable Select all when all provinces are selected', () => {
      render(<DemographicTargeting {...defaultProps} />);

      const selectAllButton = screen.getByText('✓ Select all 13');
      expect(selectAllButton).toBeDisabled();
    });

    it('should disable Clear all when no provinces are selected', () => {
      render(<DemographicTargeting {...defaultProps} value={{
        ...defaultProps.value,
        country: ['canada'],
        provinces: [],
      }} />);

      const clearAllButton = screen.getByText('✕ Clear all');
      expect(clearAllButton).toBeDisabled();
    });
  });

  describe('Province Pills Display', () => {
    it('should display province pills for selected provinces', () => {
      render(<DemographicTargeting {...defaultProps} value={{
        ...defaultProps.value,
        country: ['canada'],
        provinces: [ 'AB', 'BC', 'ON' ],
      }} />);

      // Check for pills specifically by looking for elements with pill class
      const pills = screen.getAllByText('Alberta');
      expect(pills.find(el => el.className.includes('pill-label'))).toBeInTheDocument();

      const bcPills = screen.getAllByText('British Columbia');
      expect(bcPills.find(el => el.className.includes('pill-label'))).toBeInTheDocument();

      const onPills = screen.getAllByText('Ontario');
      expect(onPills.find(el => el.className.includes('pill-label'))).toBeInTheDocument();
    });

    it('should remove province when pill close button is clicked', () => {
      render(<DemographicTargeting {...defaultProps} value={{
        ...defaultProps.value,
        country: ['canada'],
        provinces: [ 'AB', 'BC' ],
      }} />);

      // Find the close button for Alberta by looking for the pill specifically
      const albertaPills = screen.getAllByText('Alberta');
      const albertaPill = albertaPills.find(el => el.className.includes('pill-label'))?.closest('.demographic-targeting__pill');
      const closeButton = albertaPill.querySelector('.demographic-targeting__pill-close');

      fireEvent.click(closeButton);

      expect(mockOnChange).toHaveBeenCalledWith({
        ...defaultProps.value,
        country: ['canada'],
        provinces: [ 'BC' ],
      });
    });

    it('should not display pills when no provinces are selected', () => {
      const { container } = render(<DemographicTargeting {...defaultProps} value={{
        ...defaultProps.value,
        country: ['canada'],
        provinces: [],
      }} />);

      // Check that no pill container exists
      const pillContainer = container.querySelector('.demographic-targeting__province-pills');
      expect(pillContainer).not.toBeInTheDocument();
    });
  });

  describe('Form Prevention', () => {
    it('should prevent form submission when pressing enter in province select', () => {
      const { container } = render(
        <div>
          <DemographicTargeting {...defaultProps} value={{
            ...defaultProps.value,
            country: ['canada'],
          }} />
        </div>
      );

      const form = container.querySelector('.demographic-targeting__province-form');
      fireEvent.submit(form);

      // Form should have prevented default so no submit happens
      expect(form).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels for province removal buttons', () => {
      render(<DemographicTargeting {...defaultProps} value={{
        ...defaultProps.value,
        country: ['canada'],
        provinces: [ 'AB' ],
      }} />);

      expect(screen.getByLabelText('Remove Alberta')).toBeInTheDocument();
    });

    it('should have proper role for province container', () => {
      render(<DemographicTargeting {...defaultProps} value={{
        ...defaultProps.value,
        country: ['canada'],
      }} />);

      expect(screen.getByRole('group', { name: 'Province selection' })).toBeInTheDocument();
    });
  });
});
