import React, { useState } from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import Card from 'canvas-core-react/lib/Card';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import MultiSelectWithControls from './MultiSelectWithControls';
import IconErrorUniversal from 'canvas-core-react/lib/IconErrorUniversal';
import './segmentTargeting.scss';
import IconCautionUniversal from 'canvas-core-react/lib/IconCautionUniversal';

// SegmentId options
export const SEGMENTID_OPTIONS = [
  { code: 'UNSEGMENTED', description: 'Unsegmented', label: 'Unsegmented (1)', value: '1', name: 'segment_ids' },
  { code: 'OFFER', description: 'Offer', label: 'Offer (2)', value: '2', name: 'segment_ids' },
  { code: 'STAFF', description: 'Staff', label: 'Staff (3)', value: '3', name: 'segmentId' },
  { code: 'INACTIVE_MAIN_STREET', description: 'Inactive Main Street', label: 'Inactive Main Street (4)', value: '4', name: 'segment_ids' },
  { code: 'DISENGAGED_MAIN_STREET', description: 'Disengaged Main Street (5)', label: 'Disengaged Main Street (5)', value: '5', name: 'segment_ids' },
  { code: 'EMERGING_MAIN_STREET', description: 'Emerging Main Street (6)', label: 'Emerging Main Street (6)', value: '6', name: 'segment_ids' },
  { code: 'MAIN_STREET_INVESTOR_ASSESTS', description: 'Main Street Investor - Assets (7)', label: 'Main Street Investor - Assets (7)', value: '7', name: 'segment_ids' },
  { code: 'MAIN_STREET_INVESTOR_TRADE', description: 'Main Street Investor - Trade (8)', label: 'Main Street Investor - Trade (8)', value: '8', name: 'segment_ids' },
  { code: 'MAIN_STREET_TRADER', description: 'Main Street Trader (9)', label: 'Main Street Trader (9)', value: '9', name: 'segment_ids' },
  { code: 'MASS_AFFLUENT_INVESTOR', description: 'Mass Affluent Investor (10)', label: 'Mass Affluent Investor (10)', value: '10', name: 'segment_ids' },
  { code: 'AFFLUENT_INVESTOR', description: 'Affluent Investor (11)', label: 'Affluent Investor (11)', value: '11', name: 'segment_ids' },
  { code: 'ACTIVE_TRADER', description: 'Active Trader (12)', label: 'Active Trader (12)', value: '12', name: 'segment_ids' },
  { code: 'HIGHLY_ACTIVE_TRADER', description: 'Highly Active Trader (13)', label: 'Highly Active Trader (13)', value: '13', name: 'segment_ids' },
];

const SegmentIdTargeting = ({
  // Redux Form props
  input,
  meta,
  // Component-specific props
  disabled,
  value: directValue,
  onChange: directOnChange,
  isITradeLobSelected,
  error: directError,
  ...rest
}) => {
  // Default value structure
  const defaultValue = SEGMENTID_OPTIONS.map(s => s.value);

  // Support both direct usage and Redux Form usage
  const value = input?.value || directValue || [];

  const onChange = input?.onChange || directOnChange;
  const error = (meta?.touched && meta?.error) || directError;

  // Initialize the form value if it's empty on mount
  React.useEffect(() => {
    if (input && onChange && (!input.value || Object.keys(input.value).length === 0)) {
      onChange(defaultValue);
    }
  }, []); // Only run on mount

  // State for MultiSelect
  const [ isProvinceSelectOpen, setIsProvinceSelectOpen ] = useState(false);

  // Handle MultiSelect open/close to prevent scrolling issues
  const handleSegmentIdSelectToggle = (isOpen, event) => {
    setIsProvinceSelectOpen(isOpen);

    // If opening, scroll the element into view gently to prevent jarring movement
    if (isOpen) {
      setTimeout(() => {
        const element = document.getElementById('segmentId-multiselect');
        if (element) {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'nearest',
          });
        }
      }, 100);
    }
  };

  // Create segment Id options with checked status for MultiSelect
  const segmentIdOptionsWithChecked = SEGMENTID_OPTIONS.map(segment => ({
    ...segment,
    checked: value?.includes(segment.value) || false,
  }));

  const handleSegmentIdChange = (checked, item, index, segmentIdValue, selectedItems, event) => {
    if (disabled) return;

    // Only stop propagation, don't prevent default checkbox behavior
    if (event) {
      event.stopPropagation();
    }

    const currentSegmentIds = value || [];
    let newSegmentIds;

    if (checked) {
      newSegmentIds = [ ...currentSegmentIds, item.value ];
    } else {
      newSegmentIds = currentSegmentIds.filter(id => id !== item.value);
    }

    onChange(newSegmentIds);
  };

  // Handle Select All segmentId
  const handleSelectAllSegmentIds = () => {
    if (disabled) return;
    onChange(SEGMENTID_OPTIONS.map(s => s.value));
  };

  // Handle Clear All segmentId
  const handleClearAllSegmentIds = () => {
    if (disabled) return;
    onChange([]);
  };

  return (
    <Card className={classnames('segment-targeting__card', { 'segment-targeting__disabled': !isITradeLobSelected })} type="floatLow">
      <TextHeadline
        component="h2"
        size={21}
        className="segment-targeting__header"
      >
        Target by Segment ID
      </TextHeadline>

      { isITradeLobSelected ? (
        <>
        <div className="segment-targeting__section">
          <div className="segment-targeting__province-header">
            <TextSubtitle type="2" component="h3">
              Segment ID
            </TextSubtitle>
            <div className="segment-targeting__province-controls">
              <button
                type="button"
                onClick={handleSelectAllSegmentIds}
                disabled={disabled || value?.length === SEGMENTID_OPTIONS.length}
                className="segment-targeting__select-all"
              >
                ✓ Select all { SEGMENTID_OPTIONS.length }
              </button>
              <span className="segment-targeting__separator">|</span>
              <button
                type="button"
                onClick={handleClearAllSegmentIds}
                disabled={disabled || value?.length === 0}
                className="segment-targeting__clear-all"
              >
                ✕ Clear all
              </button>
            </div>
          </div>

            <form
              onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              className="segment-targeting__province-form"
            >
              <div
                className="segment-targeting__province-container"
                role="group"
                aria-label="Segment ID selection"
              >
                <MultiSelectWithControls
                  id="segmentId-multiselect"
                  // label="Segment ID"
                  placeholder="Select Segment ID(s)"
                  options={segmentIdOptionsWithChecked}
                  isMutliSelectOpen={isProvinceSelectOpen}
                  setIsMultiSelectOpen={handleSegmentIdSelectToggle}
                  onChange={handleSegmentIdChange}
                  disabled={disabled}
                  itemsSelectedLocale="Segment ID(s) selected"
                  className="segment-targeting__province-multiselect"
                  showChips={false}
                  showControls={false}
                  key={value?.join(',')}
                />
            </div>
          </form>

          { /* SegmentId Pills */ }
          { value?.length > 0 && (
            <div className="segment-targeting__province-pills">
              { value.map((segmentId) => {
                const segment = SEGMENTID_OPTIONS.find(s => s.value === segmentId);
                return segment ? (
                  <div key={segmentId} className="segment-targeting__pill">
                    <span className="segment-targeting__pill-label">{ segment.label }</span>
                    <button
                      type="button"
                      className="segment-targeting__pill-close"
                      onClick={() => handleSegmentIdChange(false, segment, null, segment.value)}
                      disabled={disabled}
                      aria-label={`Remove ${segment.label}`}
                    >
                      ×
                    </button>
                  </div>
                ) : null;
              }) }
            </div>
          ) }
        </div>
        </>
      ) : (
        <div className="segment-targeting__warning">
          <IconCautionUniversal color="red" />
          <TextCaption type="error" component="p">Only for ITrade LOB</TextCaption>
        </div>
      ) }

      { error && (
        <div className="segment-targeting__error">
          <IconErrorUniversal color="red" />
          <TextCaption type="error" component="p">{ error }</TextCaption>
        </div>
      ) }
    </Card>
  );
};

SegmentIdTargeting.propTypes = {
  // Redux Form props (optional for backward compatibility)
  input: PropTypes.shape({
    value: PropTypes.object,
    onChange: PropTypes.func.isRequired,
  }),
  meta: PropTypes.shape({
    touched: PropTypes.bool,
    error: PropTypes.string,
  }),
  // Component-specific props
  disabled: PropTypes.bool,
  // Direct props (used when not with Redux Form)
  value: PropTypes.object,
  onChange: PropTypes.func,
  isITradeLobSelected: PropTypes.bool.isRequired,
  error: PropTypes.string,
};

export default SegmentIdTargeting;
