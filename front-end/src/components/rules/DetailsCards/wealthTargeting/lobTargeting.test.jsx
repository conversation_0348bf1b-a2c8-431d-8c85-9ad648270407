import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import LOBTargeting from './lobTargeting';

const mockOnChange = jest.fn();

const defaultProps = {
  application: 'default',
  disabled: false,
  value: [],
  onChange: mockOnChange,
  isWealthCampaignSelected: true,
  error: null,
};

describe('LOBTargeting Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render LOB targeting component when wealth campaign is selected', () => {
      render(<LOBTargeting {...defaultProps} />);

      expect(screen.getByText('Target by Wealth line(s) of Business (LOB)')).toBeInTheDocument();
      expect(screen.getByText('LOB')).toBeInTheDocument();
    });

    it('should not render when wealth campaign is not selected', () => {
      render(<LOBTargeting {...defaultProps} isWealthCampaignSelected={false} />);

      expect(screen.queryByText('Target by Wealth line(s) of Business (LOB)')).not.toBeInTheDocument();
    });

    it('should render all default LOB options', () => {
      render(<LOBTargeting {...defaultProps} />);

      expect(screen.getByLabelText('iTrade')).toBeInTheDocument();
      expect(screen.getByLabelText('ScotiaMcLeod')).toBeInTheDocument();
      expect(screen.getByLabelText('Private Investment Counsel')).toBeInTheDocument();
      expect(screen.getByLabelText('Scotia TRUST')).toBeInTheDocument();
      expect(screen.getByLabelText('International Investment Advisory')).toBeInTheDocument();
      expect(screen.getByLabelText('1832 AMUS')).toBeInTheDocument();
    });

    it('should render only iTrade for starburst application', () => {
      render(<LOBTargeting {...defaultProps} application="starburst" />);

      expect(screen.getByLabelText('iTrade')).toBeInTheDocument();
      expect(screen.queryByLabelText('ScotiaMcLeod')).not.toBeInTheDocument();
      expect(screen.queryByLabelText('Private Investment Counsel')).not.toBeInTheDocument();
    });

    it('should render all non-iTrade LOBs for atlantis application', () => {
      render(<LOBTargeting {...defaultProps} application="atlantis" />);

      expect(screen.queryByLabelText('iTrade')).not.toBeInTheDocument();
      expect(screen.getByLabelText('ScotiaMcLeod')).toBeInTheDocument();
      expect(screen.getByLabelText('Private Investment Counsel')).toBeInTheDocument();
      expect(screen.getByLabelText('Scotia TRUST')).toBeInTheDocument();
      expect(screen.getByLabelText('International Investment Advisory')).toBeInTheDocument();
      expect(screen.getByLabelText('1832 AMUS')).toBeInTheDocument();
    });
  });

  describe('Value Prop', () => {
    it('should pre-select LOBs based on value prop', () => {
      render(<LOBTargeting {...defaultProps} value={[ 'SDBI', 'SMI' ]} />);

      expect(screen.getByLabelText('iTrade')).toBeChecked();
      expect(screen.getByLabelText('ScotiaMcLeod')).toBeChecked();
      expect(screen.getByLabelText('Private Investment Counsel')).not.toBeChecked();
    });

    it('should render with empty selection when value is empty array', () => {
      render(<LOBTargeting {...defaultProps} value={[]} />);

      expect(screen.getByLabelText('iTrade')).not.toBeChecked();
      expect(screen.getByLabelText('ScotiaMcLeod')).not.toBeChecked();
      expect(screen.getByLabelText('Private Investment Counsel')).not.toBeChecked();
    });
  });

  describe('LOB Selection', () => {
    it('should handle individual LOB selection', () => {
      render(<LOBTargeting {...defaultProps} />);

      const itradeCheckbox = screen.getByLabelText('iTrade');
      fireEvent.click(itradeCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith(expect.arrayContaining([ 'SDBI' ]));
    });

    it('should handle individual LOB deselection', () => {
      render(<LOBTargeting {...defaultProps} value={[ 'SDBI', 'SMI' ]} />);

      const itradeCheckbox = screen.getByLabelText('iTrade');
      fireEvent.click(itradeCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith([ 'SMI' ]);
    });

    it('should handle multiple LOB selections', () => {
      const { rerender } = render(<LOBTargeting {...defaultProps} value={[]} />);

      const itradeCheckbox = screen.getByLabelText('iTrade');
      fireEvent.click(itradeCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith([ 'SDBI' ]);

      // Reset mock and test second selection
      mockOnChange.mockClear();
      rerender(<LOBTargeting {...defaultProps} value={[ 'SDBI' ]} />);
      const smiCheckbox = screen.getByLabelText('ScotiaMcLeod');
      fireEvent.click(smiCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith([ 'SDBI', 'SMI' ]);
    });
  });

  describe('Select All/Clear All', () => {
    it('should show "Select all" when no LOBs are selected', () => {
      render(<LOBTargeting {...defaultProps} value={[]} />);

      expect(screen.getByText('✓ Select all 6')).toBeInTheDocument();
    });

    it('should show "Clear all" when all LOBs are selected', () => {
      render(<LOBTargeting {...defaultProps} value={[ 'SDBI', 'SMI', 'CASL', 'TRST', 'SPCGIIA', 'REGL' ]} />);

      expect(screen.getByText('✕ Clear all')).toBeInTheDocument();
    });

    it('should select all LOBs when "Select all" is clicked', () => {
      render(<LOBTargeting {...defaultProps} value={[]} />);

      const selectAllLink = screen.getByText('✓ Select all 6');
      fireEvent.click(selectAllLink);

      expect(mockOnChange).toHaveBeenCalledWith([ 'SDBI', 'SMI', 'CASL', 'TRST', 'SPCGIIA', 'REGL' ]);
    });

    it('should clear all LOBs when "Clear all" is clicked', () => {
      // Start with all LOBs selected to show "Clear all"
      render(<LOBTargeting {...defaultProps} value={[ 'SDBI', 'SMI', 'CASL', 'TRST', 'SPCGIIA', 'REGL' ]} />);

      const clearAllLink = screen.getByText('✕ Clear all');
      fireEvent.click(clearAllLink);

      expect(mockOnChange).toHaveBeenCalledWith([]);
    });

    it('should show "Clear all" for starburst application when all LOBs are selected', () => {
      render(<LOBTargeting {...defaultProps} application="starburst" value={[ 'SDBI' ]} />);

      // When all available LOBs (just ITRADE for starburst) are selected, show "Clear all"
      // For starburst with only 1 LOB option, it still shows Select all 1
      expect(screen.getByText('✕ Clear all')).toBeInTheDocument();
    });
  });

  describe('Error State', () => {
    it('should show error message when error prop is provided', () => {
      render(<LOBTargeting {...defaultProps} value={[]} error="At least one LOB must be selected" />);

      expect(screen.getByText('At least one LOB must be selected')).toBeInTheDocument();
    });

    it('should not show error message when error prop is not provided', () => {
      render(<LOBTargeting {...defaultProps} value={[ 'SDBI' ]} />);

      expect(screen.queryByText('At least one LOB must be selected')).not.toBeInTheDocument();
    });
  });

  describe('Disabled State', () => {
    it('should disable all checkboxes when disabled prop is true', () => {
      render(<LOBTargeting {...defaultProps} disabled={true} />);

      expect(screen.getByLabelText('iTrade')).toBeDisabled();
      expect(screen.getByLabelText('ScotiaMcLeod')).toBeDisabled();
      expect(screen.getByLabelText('Private Investment Counsel')).toBeDisabled();
    });

    it('should not trigger action when select/clear all link is clicked while disabled', () => {
      jest.clearAllMocks();
      render(<LOBTargeting {...defaultProps} disabled={true} value={[ 'SDBI', 'SMI', 'CASL', 'TRST', 'SPCGIIA', 'REGL' ]} />);

      const clearAllLink = screen.getByText('✕ Clear all');
      fireEvent.click(clearAllLink);

      // Should not trigger onChange when disabled
      expect(mockOnChange).not.toHaveBeenCalled();
    });

    it('should not trigger onChange when disabled checkbox is clicked', () => {
      jest.clearAllMocks();
      render(<LOBTargeting {...defaultProps} disabled={true} value={[]} />);

      const itradeCheckbox = screen.getByLabelText('iTrade');
      fireEvent.click(itradeCheckbox);

      // Should not trigger onChange when disabled
      expect(mockOnChange).not.toHaveBeenCalled();
    });
  });

  describe('Checkbox States', () => {
    it('should show indeterminate state for select all when some LOBs are selected', () => {
      render(<LOBTargeting {...defaultProps} value={[ 'SDBI' ]} />);

      // With partial selection, the select all should show "Select all" text
      // but the checkbox state should be indeterminate (handled internally)
      expect(screen.getByText('✓ Select all 6')).toBeInTheDocument();
    });
  });

  describe('LOB Code Mapping', () => {
    it('should use correct LOB codes in onChange callbacks', () => {
      render(<LOBTargeting {...defaultProps} value={[]} />);

      const itradeCheckbox = screen.getByLabelText('iTrade');
      const smiCheckbox = screen.getByLabelText('ScotiaMcLeod');
      const caslCheckbox = screen.getByLabelText('Private Investment Counsel');
      const trstCheckbox = screen.getByLabelText('Scotia TRUST');
      const spcgiiaCheckbox = screen.getByLabelText('International Investment Advisory');
      const reglCheckbox = screen.getByLabelText('1832 AMUS');

      fireEvent.click(itradeCheckbox);
      expect(mockOnChange).toHaveBeenCalledWith(expect.arrayContaining([ 'SDBI' ]));

      fireEvent.click(smiCheckbox);
      expect(mockOnChange).toHaveBeenCalledWith(expect.arrayContaining([ 'SMI' ]));

      fireEvent.click(caslCheckbox);
      expect(mockOnChange).toHaveBeenCalledWith(expect.arrayContaining([ 'CASL' ]));

      fireEvent.click(trstCheckbox);
      expect(mockOnChange).toHaveBeenCalledWith(expect.arrayContaining([ 'TRST' ]));

      fireEvent.click(spcgiiaCheckbox);
      expect(mockOnChange).toHaveBeenCalledWith(expect.arrayContaining([ 'SPCGIIA' ]));

      fireEvent.click(reglCheckbox);
      expect(mockOnChange).toHaveBeenCalledWith(expect.arrayContaining([ 'REGL' ]));
    });
  });
});
