import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import IClubTiersTargeting, { iClubTierOptions } from './iClubTiersTargeting';

const mockOnChange = jest.fn();

const defaultProps = {
  application: 'default',
  disabled: false,
  value: [],
  onChange: mockOnChange,
  isITradeLobSelected: true,
  error: null,
};

describe('IClubTiersTargeting Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render iClubTiers targeting component when wealth campaign is selected', () => {
      render(<IClubTiersTargeting {...defaultProps} />);

      expect(screen.getByText('Target by iCLUB Tiers')).toBeInTheDocument();
      expect(screen.getByText('iCLUB Tiers')).toBeInTheDocument();
    });

    it('should render all default iClub Tiers options', () => {
      render(<IClubTiersTargeting {...defaultProps} />);

      expect(screen.getByLabelText('Mainstreet (0)')).toBeInTheDocument();
      expect(screen.getByLabelText('Gold (1)')).toBeInTheDocument();
      expect(screen.getByLabelText('Platinum (2)')).toBeInTheDocument();
      expect(screen.getByLabelText('Platinum + (3)')).toBeInTheDocument();
    });
  });

  describe('Value Prop', () => {
    it('should pre-select iCLub Tiers based on value prop', () => {
      render(<IClubTiersTargeting {...defaultProps} value={[ 'Mainstreet', 'Gold' ]} />);

      expect(screen.getByLabelText('Mainstreet (0)')).toBeChecked();
      expect(screen.getByLabelText('Gold (1)')).toBeChecked();
      expect(screen.getByLabelText('Platinum (2)')).not.toBeChecked();
    });

    it('should render with empty selection when value is empty array', () => {
      render(<IClubTiersTargeting {...defaultProps} value={[]} />);

      expect(screen.getByLabelText('Mainstreet (0)')).not.toBeChecked();
      expect(screen.getByLabelText('Gold (1)')).not.toBeChecked();
    });
  });

  describe('Tier Selection', () => {
    it('should handle individual Tier selection', () => {
      render(<IClubTiersTargeting {...defaultProps} />);

      const tierCheckbox = screen.getByLabelText('Mainstreet (0)');
      fireEvent.click(tierCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith(expect.arrayContaining([ 'Mainstreet' ]));
    });

    it('should handle individual Tier deselection', () => {
      render(<IClubTiersTargeting {...defaultProps} value={[ 'Mainstreet', 'Gold' ]} />);

      const itradeCheckbox = screen.getByLabelText('Mainstreet (0)');
      fireEvent.click(itradeCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith([ 'Gold' ]);
    });

    it('should handle multiple tier selections', () => {
      const { rerender } = render(<IClubTiersTargeting {...defaultProps} value={[]} />);

      const MainstreetCheckbox = screen.getByLabelText('Mainstreet (0)');
      fireEvent.click(MainstreetCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith([ 'Mainstreet' ]);

      // Reset mock and test second selection
      mockOnChange.mockClear();
      rerender(<IClubTiersTargeting {...defaultProps} value={[ 'Mainstreet' ]} />);
      const goldCheckbox = screen.getByLabelText('Gold (1)');
      fireEvent.click(goldCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith([ 'Mainstreet', 'Gold' ]);
    });
  });

  describe('Select All/Clear All', () => {
    it('should show "Select all" when no tiers are selected', () => {
      render(<IClubTiersTargeting {...defaultProps} value={[]} />);

      expect(screen.getByText('✓ Select all 4')).toBeInTheDocument();
    });

    it('should show "Clear all" when all tiers are selected', () => {
      render(<IClubTiersTargeting {...defaultProps} value={iClubTierOptions.map(t => t.code)} />);

      expect(screen.getByText('✕ Clear all')).toBeInTheDocument();
    });

    it('should select all tiers when "Select all" is clicked', () => {
      render(<IClubTiersTargeting {...defaultProps} value={[]} />);

      const selectAllLink = screen.getByText('✓ Select all 4');
      fireEvent.click(selectAllLink);

      expect(mockOnChange).toHaveBeenCalledWith(iClubTierOptions.map(t => t.code));
    });

    it('should clear all Tiers when "Clear all" is clicked', () => {
      // Start with all LOBs selected to show "Clear all"
      render(<IClubTiersTargeting {...defaultProps} value={iClubTierOptions.map(t => t.code)} />);

      const clearAllLink = screen.getByText('✕ Clear all');
      fireEvent.click(clearAllLink);

      expect(mockOnChange).toHaveBeenCalledWith([]);
    });
  });

  describe('Error State', () => {
    it('should show error message when error prop is provided', () => {
      render(<IClubTiersTargeting {...defaultProps} value={[]} error="At least one IClub Tier must be selected" />);

      expect(screen.getByText('At least one IClub Tier must be selected')).toBeInTheDocument();
    });
  });

  });
