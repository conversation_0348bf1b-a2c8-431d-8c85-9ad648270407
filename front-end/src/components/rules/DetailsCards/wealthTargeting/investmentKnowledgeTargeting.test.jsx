import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import InvestmentKnowledgeTargeting, { INVESTMENT_TYPES, KNOWLEDGE_LEVELS } from './investingKnowledgeTargeting';

const mockOnChange = jest.fn();

const defaultProps = {
  disabled: false,
  value: [],
  onChange: mockOnChange,
  isITradeLobSelected: true,
  error: null,
};

describe('Investment Knowledge Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const totalCount = INVESTMENT_TYPES.length * KNOWLEDGE_LEVELS.length;

  const getInitialState = () => {
    const initialState = {};
    INVESTMENT_TYPES.forEach(type => {
      initialState[type] = KNOWLEDGE_LEVELS;
    });

    return initialState;
  };

  const getClearedState = () => {
    const newState = {};
    INVESTMENT_TYPES.forEach(type => {
      newState[type] = [];
    });
    return newState;
  };

  describe('Rendering', () => {
    it('should render correctly with all checkboxes selected', () => {
      render(<InvestmentKnowledgeTargeting {...defaultProps} />);

      expect(screen.getByText('Target by Investing Knowledge Level')).toBeInTheDocument();
      expect(screen).toMatchSnapshot();
    });
  });
});
