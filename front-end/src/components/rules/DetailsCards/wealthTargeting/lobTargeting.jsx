import React from 'react';
import PropTypes from 'prop-types';
import Card from 'canvas-core-react/lib/Card';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import Checkbox from 'canvas-core-react/lib/Checkbox';
import Link from 'canvas-core-react/lib/Link';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import IconErrorUniversal from 'canvas-core-react/lib/IconErrorUniversal';
import './lobTargeting.scss';

// LOB options by application
const LOB_OPTIONS = {
  starburst: [
    { code: 'SDBI', description: 'iTrade' },
  ],
  atlantis: [
    { code: 'SMI', description: 'ScotiaMcLeod' },
    { code: 'CASL', description: 'Private Investment Counsel' },
    { code: 'TRST', description: 'Scotia TRUST' },
    { code: 'SPCGIIA', description: 'International Investment Advisory' },
    { code: 'REGL', description: '1832 AMUS' },
  ],
  default: [
    { code: 'SDBI', description: 'iTrade' },
    { code: 'SMI', description: 'ScotiaMcLeod' },
    { code: 'CASL', description: 'Private Investment Counsel' },
    { code: 'TRST', description: 'Scotia TRUST' },
    { code: 'SPCGIIA', description: 'International Investment Advisory' },
    { code: 'REGL', description: '1832 AMUS' },
  ],
};

const LOBTargeting = ({
  // Redux Form props
  input,
  meta,
  // Component-specific props
  application,
  disabled,
  value: directValue,
  onChange: directOnChange,
  isWealthCampaignSelected,
  error: directError,
  ...rest
}) => {
  // Support both direct usage and Redux Form usage
  const value = input?.value || directValue || [];
  const onChange = input?.onChange || directOnChange;
  const error = (meta?.touched && meta?.error) || directError;
  // Get LOB options based on application
  const lobOptions = LOB_OPTIONS[application] || LOB_OPTIONS.default;

  const handleLOBChange = (lobCode, checked) => {
    if (disabled) return; // Don't execute when disabled

    let newSelection;
    if (checked) {
      newSelection = [ ...value, lobCode ];
    } else {
      newSelection = value.filter(code => code !== lobCode);
    }
    onChange(newSelection);
  };

  if (!isWealthCampaignSelected) {
    return null;
  }

  return (
    <Card className="lob-targeting__card" type="floatLow">
      <TextHeadline
        component="h2"
        size={21}
        className="lob-targeting__header"
      >
        Target by Wealth line(s) of Business (LOB)
      </TextHeadline>

      <div className="lob-targeting__subtitle-row">
        <TextSubtitle type="2" component="h3">
          LOB
        </TextSubtitle>
        <Link
          type="emphasis"
          href="#"
          onClick={(e) => {
            e.preventDefault();
            if (!disabled && value.length !== lobOptions.length) {
              const allLOBCodes = lobOptions.map(lob => lob.code);
              onChange(allLOBCodes);
            }
          }}
          disabled={disabled || value.length === lobOptions.length}
        >
          ✓ Select all { lobOptions.length }
        </Link>
        <Link
          type="emphasis"
          href="#"
          onClick={(e) => {
            e.preventDefault();
            if (!disabled && value.length > 0) {
              onChange([]);
            }
          }}
          disabled={disabled || value.length === 0}
        >
          ✕ Clear all
        </Link>
      </div>

      <div>
        { lobOptions.map((lob) => (
          <Checkbox
            key={lob.code}
            id={`lob-${lob.code}`}
            label={lob.description}
            checked={value.includes(lob.code)}
            onChange={(e) => handleLOBChange(lob.code, e.target.checked)}
            disabled={disabled}
          />
        )) }
      </div>

      { error && (
        <div className="lob-targeting__error">
          <IconErrorUniversal color="red" />
          <TextCaption type="error" component="p">{ error }</TextCaption>
        </div>
      ) }
    </Card>
  );
};

LOBTargeting.propTypes = {
  // Redux Form props (optional for backward compatibility)
  input: PropTypes.shape({
    value: PropTypes.array,
    onChange: PropTypes.func.isRequired,
  }),
  meta: PropTypes.shape({
    touched: PropTypes.bool,
    error: PropTypes.string,
  }),
  // Component-specific props
  application: PropTypes.string,
  disabled: PropTypes.bool,
  // Direct props (used when not with Redux Form)
  value: PropTypes.array,
  onChange: PropTypes.func,
  isWealthCampaignSelected: PropTypes.bool.isRequired,
  error: PropTypes.string,
};

export default LOBTargeting;
