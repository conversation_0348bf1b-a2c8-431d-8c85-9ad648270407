import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import MultiSelect from 'canvas-core-react/lib/MultiSelect';
import PillButton from 'canvas-core-react/lib/PillButton';
import './MultiSelectWithControls.scss';

/**
 * Enhanced MultiSelect component with Select All, Clear All, and chip display functionality
 */
const MultiSelectWithControls = ({
  id,
  label,
  placeholder,
  options,
  isMutliSelectOpen,
  setIsMultiSelectOpen,
  onChange,
  disabled = false,
  itemsSelectedLocale = 'items selected',
  showChips = true,
  showControls = true,
  className = '',
  ...rest
}) => {
  // Calculate selected items
  const selectedItems = useMemo(() => {
    return options.filter(option => option.checked);
  }, [ options ]);

  const totalItems = options.length;
  const selectedCount = selectedItems.length;

  // Handle Select All
  const handleSelectAll = () => {
    if (disabled) return;

    const allSelectedOptions = options.map(option => ({
      ...option,
      checked: true,
    }));

    // Call onChange for each item to maintain compatibility
    allSelectedOptions.forEach((option, index) => {
      if (!options[index].checked) {
        onChange(true, option, index, option.value, allSelectedOptions.filter(opt => opt.checked), null);
      }
    });
  };

  // Handle Clear All
  const handleClearAll = () => {
    if (disabled) return;

    const allDeselectedOptions = options.map(option => ({
      ...option,
      checked: false,
    }));

    // Call onChange for each item to maintain compatibility
    allDeselectedOptions.forEach((option, index) => {
      if (options[index].checked) {
        onChange(false, option, index, option.value, [], null);
      }
    });
  };

  // Handle individual chip removal
  const handleChipRemove = (chipItem) => {
    if (disabled) return;

    const itemIndex = options.findIndex(option => option.value === chipItem.value);
    if (itemIndex !== -1) {
      const newSelectedItems = selectedItems.filter(item => item.value !== chipItem.value);
      onChange(false, chipItem, itemIndex, chipItem.value, newSelectedItems, null);
    }
  };

  return (
    <div className={`multiselect-with-controls ${className}`}>
      { /* Select All / Clear All Controls */ }
      { showControls && totalItems > 0 && (
        <div className="multiselect-with-controls__header">
          <div className="multiselect-with-controls__controls">
            <button
              type="button"
              onClick={handleSelectAll}
              disabled={disabled || selectedCount === totalItems}
              className="multiselect-with-controls__select-all"
            >
              ✓ Select all { totalItems }
            </button>
            <span className="multiselect-with-controls__separator">|</span>
            <button
              type="button"
              onClick={handleClearAll}
              disabled={disabled || selectedCount === 0}
              className="multiselect-with-controls__clear-all"
            >
              ✕ Clear all
            </button>
          </div>
        </div>
      ) }

      { /* Main MultiSelect Component */ }
      <div className="multiselect-with-controls__dropdown">
        <MultiSelect
          id={id}
          label={label}
          placeholder={placeholder}
          options={options}
          isMutliSelectOpen={isMutliSelectOpen}
          setIsMultiSelectOpen={setIsMultiSelectOpen}
          onChange={onChange}
          disabled={disabled}
          itemsSelectedLocale={itemsSelectedLocale}
          {...rest}
        />
      </div>

      { /* Selected Items as Chips */ }
      { showChips && selectedItems.length > 0 && (
        <div className="multiselect-with-controls__chips">
          { selectedItems.map((item) => (
            <div key={item.value} className="multiselect-with-controls__chip">
              <PillButton
                label={item.label}
                onClose={() => handleChipRemove(item)}
                disabled={disabled}
                size="small"
              />
            </div>
          )) }
        </div>
      ) }
    </div>
  );
};

MultiSelectWithControls.propTypes = {
  // Required props (inherited from MultiSelect)
  id: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  placeholder: PropTypes.string.isRequired,
  options: PropTypes.arrayOf(PropTypes.shape({
    name: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
    value: PropTypes.string.isRequired,
    disabled: PropTypes.bool,
    checked: PropTypes.bool,
    checkColor: PropTypes.string,
  })).isRequired,

  // MultiSelect callback props
  isMutliSelectOpen: PropTypes.bool,
  setIsMultiSelectOpen: PropTypes.func,
  onChange: PropTypes.func,

  // Optional props
  disabled: PropTypes.bool,
  itemsSelectedLocale: PropTypes.string,
  className: PropTypes.string,

  // Enhanced functionality props
  showChips: PropTypes.bool,
  showControls: PropTypes.bool,
};

export default MultiSelectWithControls;
