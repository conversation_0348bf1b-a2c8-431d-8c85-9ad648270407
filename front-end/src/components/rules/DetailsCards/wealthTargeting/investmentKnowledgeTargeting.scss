.investment-knowledge-targeting {
  &__card {
    margin-bottom: 2rem;
  }

  &__header {
    margin-bottom: 1.5rem;
  }

  &__subtitle-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  &__actions {
    display: flex;
    gap: 1rem;
  }

  &__row {
    margin-bottom: 0.5rem;
  }

  &__error {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    color: #d9534f;
  }

  &__warning {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    color: #d9534f;
  }

  &__disabled {
    background-color: #F6F6F6 !important
  }

  &__table {
    width: 50% !important;

    &__footer {
      width: 50%;
      height: 60px;
      display: flex;
      padding: 6px 30px;
      align-items: anchor-center;
      justify-content: center;
      gap: 18px;
      align-self: stretch;
      box-shadow: 0 2px 10px 0 rgba(0, 34, 91, 0.11);
    }
  }

  &__select-all,
  &__clear-all {
    background: none;
    border: none;
    padding: 0;
    font-size: 16px;
    cursor: pointer;
    text-decoration: none;
    
    &:focus {
      outline: 2px solid #0066cc;
      outline-offset: 2px;
    }
  }

  &__select-all {
    color: #0066cc;
    
    &:hover:not([disabled]) {
      text-decoration: underline;
    }
    
    &[disabled] {
      color: #999;
      cursor: not-allowed;
    }
  }
  
  &__clear-all {
    color: #666;
    
    &:hover:not([disabled]) {
      text-decoration: underline;
    }
    
    &[disabled] {
      color: #999;
      cursor: not-allowed;
    }
  }
}

#table-investment-knowledge-targeting {
  overflow-x: unset; // ToDO: Pass this as Table prop post canvas 14 update.
}