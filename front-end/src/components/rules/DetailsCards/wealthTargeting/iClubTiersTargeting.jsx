import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import Card from 'canvas-core-react/lib/Card';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import Checkbox from 'canvas-core-react/lib/Checkbox';
import Link from 'canvas-core-react/lib/Link';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import IconErrorUniversal from 'canvas-core-react/lib/IconErrorUniversal';
import IconCautionUniversal from 'canvas-core-react/lib/IconCautionUniversal';
import './iClubTiersTargeting.scss';

// iCLUB options
export const iClubTierOptions = [
  { code: 'Mainstreet', description: 'Mainstreet (0)' },
  { code: 'Gold', description: 'Gold (1)' },
  { code: 'Platinum', description: 'Platinum (2)' },
  { code: 'Platinum+', description: 'Platinum + (3)' },
];

const IClubTiersTargeting = ({
  // Redux Form props
  input,
  meta,
  // Component-specific props
  application,
  disabled,
  value: directValue,
  onChange: directOnChange,
  isITradeLobSelected,
  error: directError,
  ...rest
}) => {
  const defaultValue = iClubTierOptions.map(s => s.code);
  // Support both direct usage and Redux Form usage
  const value = input?.value || directValue || [];
  const onChange = input?.onChange || directOnChange;
  const error = (meta?.touched && meta?.error) || directError;

  // Initialize the form value if it's empty on mount
  React.useEffect(() => {
    if (input && onChange && (!input.value || Object.keys(input.value).length === 0)) {
      onChange(defaultValue);
    }
  }, []); // Only run on mount

  const handleIClubTierChange = (tierCode, checked) => {
    if (disabled) return; // Don't execute when disabled

    let newSelection;
    if (checked) {
      newSelection = [ ...value, tierCode ];
    } else {
      newSelection = value.filter(code => code !== tierCode);
    }
    onChange(newSelection);
  };

  return (
    <Card className={classnames('iclubtiers-targeting__card', { 'iclubtiers-targeting__disabled': !isITradeLobSelected })} type="floatLow">
      <TextHeadline
        component="h2"
        size={21}
        className="iclubtiers-targeting__header"
      >
        Target by iCLUB Tiers
      </TextHeadline>

      { isITradeLobSelected ? (
        <>
        <div className="iclubtiers-targeting__subtitle-row">
        <TextSubtitle type="2" component="h3">
          iCLUB Tiers
        </TextSubtitle>
        <Link
          type="emphasis"
          href="#"
          onClick={(e) => {
            e.preventDefault();
            if (!disabled && value.length !== iClubTierOptions.length) {
              const alliClubTierCodes = iClubTierOptions.map(tier => tier.code);
              onChange(alliClubTierCodes);
            }
          }}
          disabled={disabled || value.length === iClubTierOptions.length}
        >
          ✓ Select all { iClubTierOptions.length }
        </Link>
        <Link
          type="emphasis"
          href="#"
          onClick={(e) => {
            e.preventDefault();
            if (!disabled && value.length > 0) {
              onChange([]);
            }
          }}
          disabled={disabled || value.length === 0}
        >
          ✕ Clear all
        </Link>
      </div>

      <div>
        { iClubTierOptions.map((tier) => (
          <Checkbox
            key={tier.code}
            id={`tier-${tier.code}`}
            label={tier.description}
            checked={value.includes(tier.code)}
            onChange={(e) => handleIClubTierChange(tier.code, e.target.checked)}
            disabled={disabled}
          />
        )) }
      </div>
        </>
      ) : (
        <div className="iclubtiers-targeting__warning">
          <IconCautionUniversal color="red" />
          <TextCaption type="error" component="p">Only for ITrade LOB</TextCaption>
        </div>
      ) }

      { error && (
        <div className="iclubtiers-targeting__error">
          <IconErrorUniversal color="red" />
          <TextCaption type="error" component="p">{ error }</TextCaption>
        </div>
      ) }
    </Card>
  );
};

IClubTiersTargeting.propTypes = {
  // Redux Form props (optional for backward compatibility)
  input: PropTypes.shape({
    value: PropTypes.array,
    onChange: PropTypes.func.isRequired,
  }),
  meta: PropTypes.shape({
    touched: PropTypes.bool,
    error: PropTypes.string,
  }),
  // Component-specific props
  application: PropTypes.string,
  disabled: PropTypes.bool,
  // Direct props (used when not with Redux Form)
  value: PropTypes.array,
  onChange: PropTypes.func,
  isITradeLobSelected: PropTypes.bool.isRequired,
  error: PropTypes.string,
};

export default IClubTiersTargeting;
