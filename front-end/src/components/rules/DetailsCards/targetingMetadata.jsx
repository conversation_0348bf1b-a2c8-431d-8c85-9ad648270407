import React, { useState, useRef, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Field, reduxForm, formValueSelector } from 'redux-form';

import ActionMenuList from 'canvas-core-react/lib/ActionMenuList';
import ActionMenuListItem from 'canvas-core-react/lib/ActionMenuListItem';
import Card from 'canvas-core-react/lib/Card';

import IconAdd from 'canvas-core-react/lib/IconAdd';
import IconEdit from 'canvas-core-react/lib/IconEdit';
import IconDelete from 'canvas-core-react/lib/IconDelete';
import IconTip from 'canvas-core-react/lib/IconTip';
import Link from 'canvas-core-react/lib/Link';
import Table from 'canvas-core-react/lib/Table';
import TextButton from 'canvas-core-react/lib/TextButton';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';

import {
  openPreviewWindow,
  ruleTypes as ruleTypesConstants,
  ruleSubTypesDisplayName as ruleSubType,
  mapObjectToArray,
  CAMPAIGN_BOX_CONTAINER,
} from '../../../constants';
import { getProductPlacements } from '../utils';
import { InputGroupField, InputSelectField, InputSelectsField } from '../../formFields';

import ProductTargeting from './productTargeting/productTargeting';
import TextCaption from 'canvas-core-react/lib/TextCaption';

const TargetingMetadata = ({
  access,
  applications,
  containers,
  containerSelection,
  deleteContent,
  formDisabled,
  formValues,
  handleChange,
  openContentModal,
  pages,
  productBook,
  change,
  untouch,
  type,
}) => {
  const pageAutosuggestRef = useRef();
  const fullProductBook = useMemo(() => ([
    { description: 'Retail', ownership: 'R' },
    { description: 'Business', ownership: 'B' },
    ...productBook,
  ]), [ productBook ]);
  const isTargeted = ruleSubType.targeted === ruleSubType[formValues.type];
  const isMass = ruleSubType.mass === ruleSubType[formValues.type];
  const [ containerOptions, setContainerOptions ] = useState([]);
  const [ isMenuOpen, setIsMenuOpen ] = useState(false);
  const [ campaignBoxContainerId, setCampaignBoxContainerId ] = useState();

  useEffect(() => {
    let containerDropdownOptions = Object.values(containers);
    if (formValues.application === 'orion') {
      setCampaignBoxContainerId(containerDropdownOptions.filter(c => c.containerId === CAMPAIGN_BOX_CONTAINER)[0].id);
    }
    // If form is enabled, only list containers that are 1. active 2. belong to at least one active page
    // 3. the user has access to manage 4. belong to at least one page the user has access to manage
    if (!formDisabled && formValues.application) {
      const accessContainers = access.containers[formValues.application]?.manage || [];
      const accessPages = access.pages[formValues.application]?.manage || [];
      containerDropdownOptions = containerDropdownOptions
        .filter((c) => {
          return c.status && ((c.pages && c.pages.length) ? c.pages.some(p => pages.items.find(i => i.id === p)?.status) : true) &&
            accessContainers.includes(c.containerId) &&
            (type === ruleTypesConstants.ALERT
              ? true
              : accessPages.some(accessPage =>
                Object.values(pages.items).filter(page => c.pages.includes(page.id)).map(({ pageId }) => pageId).includes(accessPage))
            );
        });
    }
    setContainerOptions(containerDropdownOptions);
  }, [ containers, pages, formValues.application ]);

  const containerChanged = (event, container) => {
    change('pages', []);
    untouch('pages');
    if (pageAutosuggestRef.current) {
      pageAutosuggestRef.current.resetSelections();
    }
    handleChange({
      container: containers[container].containerId,
      pages: undefined,
      mass_targeting: { ...(formValues.mass_targeting || {}), product_pages: {} },
    });
    deleteContent();
  };

  /** add missing button attribute for canvas 6 action menu component bug */
  const fixCanvasButtons = () => {
    if (formValues.content_id && !formDisabled) {
      document
        .querySelector('.targeting-metadata__content-action-menu')
        .querySelectorAll('button')
        .forEach(el => { el.type = 'button'; });
    }
  };

  // # product targeting section #############################################################
  const campaignTypePrev = useRef();
  useEffect(() => {
    // if switching from targeted or mass, clear previously saved account-key page and product page selections
    // due to tech debt accumulated causing the two campaign subtypes having different data source for products placement
    // old selections will be no longer valid for the new rule, and should be cleaned up
    if ([ ruleSubType.targeted, ruleSubType.mass ].includes(ruleSubType[campaignTypePrev.current])) {
      handleChange({
        mass_targeting: { ...(formValues.mass_targeting || {}), product_pages: {} }, // clear product placements
        pages: getPageSelections().map(p => p.pageId), // page list excluding dynamic pages
      });
    }
    campaignTypePrev.current = formValues.type;
  }, [ formValues.type ]);

  const productPlacements = useMemo(
    () => getProductPlacements(formValues),
    [ formValues.type, formValues.advancedTargeting, formValues.mass_targeting ]
  );

  const activateProductTargeting = formValues.pages?.includes('account-key') &&
    (isTargeted || (isMass && !!productPlacements.length));

  // manually add or remove account-key page from store, dynamic page ids must be hidden from ui
  const handleProductTargetingChecked = () => {
    const newPageSelections = getPageSelections(); // dynamic pages are filtered out here
    if (!activateProductTargeting) { // add back dynamic pages when flipping from not activated state
      const accountKeyPage = getActivePages().find(p => p.pageId === 'account-key');
      accountKeyPage && newPageSelections.push(accountKeyPage);
    }
    const pageIds = newPageSelections.map(p => p.pageId);
    handleChange({ pages: pageIds });
  };

  const handleProductChange = products => {
    handleChange({
      mass_targeting: { ...(formValues.mass_targeting || {}), product_pages: { any_of: products } },
    });
  };

  // # end product targeting section ############################################################

  useEffect(() => {
    fixCanvasButtons();
  });

  useEffect(() => {
    // populate targeting metadata form with data from parent form
    if (!containerSelection && formValues.container) {
      change('container', mapObjectToArray(containers).find(c => c.containerId === formValues.container)?.id);
    }
  }, [ formValues.container ]);

  const contentTableColumns = [
    {
      name: 'Contentful Link',
      grow: 1,
      cellFormatter: row => {
        const contentfulSpace = row.content_space || Object.values(applications || {}).find(app => app.applicationId === row.application).contentful_space;
        return (
          <Link
            className='targeting-metadata__content-table-link'
            type='emphasis'
            href={`/api/v1/contents/spaces/${contentfulSpace}/entries/contents/${row['content_id']}`}
            target='_blank'
          >{ row['content_id'] }</Link>
        );
      },
      selector: '',
    }, {
      name: 'Content Type',
      selector: 'content_type',
    }, {
      name: 'Preview Link',
      cellFormatter: row => {
        const contentfulSpace = row.content_space || Object.values(applications || {}).find(app => app.applicationId === row.application).contentful_space;
        const targetedLanguages = row.mass_targeting && row.mass_targeting.languages && Object.keys(row.mass_targeting.languages || {}).filter(key => row.mass_targeting.languages[key]);
        return (
          <button
            className='targeting-metadata__content-table-link'
            onClick={openPreviewWindow(row.content_type, row.content_id, row.container, row.dismissable_flag, contentfulSpace, row.application, targetedLanguages)}
          >Preview</button>
        );
      },
      selector: '',
    }, ...(!formDisabled ? [ {
      name: 'Action',
      cellFormatter: row => {
        const menuItems = [
          { menuName: 'Edit', iconType: IconEdit, onClick: () => { fixCanvasButtons(); openContentModal(); } },
          { menuName: 'Delete', iconType: IconDelete, onClick: () => { fixCanvasButtons(); deleteContent(); } },
        ];
        return (
          <ActionMenuList
            className='targeting-metadata__content-action-menu'
            icon='horizontal-small'
            isMenuOpen={isMenuOpen}
            setIsMenuOpen={setIsMenuOpen}
            bottomSheet={{ heading: '' }}
          >
            { menuItems.map(action => <ActionMenuListItem key={action.menuName} icon={action.iconType} onClick={action.onClick}>{ action.menuName }</ActionMenuListItem>) }
          </ActionMenuList>
        );
      },
      selector: '',
    } ] : []),
  ];

  // # page placement section #####################################################
  const handlePageChange = (pages = []) => {
    const pagesNew = pages.map(p => p.pageId);
    if (formValues.pages?.includes('account-key')) pagesNew.push('account-key');
    handleChange({ pages: pagesNew });
  };

  // Pages where current container exists on
  const getActivePages = (options = {}) => {
    if (!containerSelection || !pages) return [];
    const compatibleKeys = containers[containerSelection]?.pages || [];
    // filter page ids with a truthy status if form is enabled
    const activePages = formDisabled ? compatibleKeys : compatibleKeys.filter(id => pages.items.find(p => p.id === id)?.status);
    const excludeDynamicPages = options.excludeDynamicPages
      ? activePages.filter(id => pages.items.find(p => p.id === id).pageId !== 'account-key')
      : activePages;
    const accessPages = access.pages[formValues.application]?.manage || [];
    const pagesList = Object.values(pages.items || {});
    return excludeDynamicPages.filter(id => accessPages.includes(pagesList.find(p => p.id === id)?.pageId)).map(id => pages.items.find(p => p.id === id)); // map filtered id to actual page object
  };

  // page selections visible on the UI, which excludes dynamic pages
  const getPageSelections = () => {
    const activePages = getActivePages({ excludeDynamicPages: true });
    const pages = formValues.pages?.reduce((out, p) => {
      const pageDetails = activePages.find(ap => ap.pageId === p);
      pageDetails && out.push(pageDetails);
      return out;
    }, []);
    return pages || [];
  };

  return (
    <Card className="targeting-metadata__card" type="floatLow">
      <TextHeadline component="h2" size={21} className="targeting-metadata__sub-header">Targeting Metadata</TextHeadline>
      { /* container section */ }
      <Field
        name="container"
        label="Container"
        placeholder="Select container"
        className='targeting-metadata__container'
        component={InputSelectField}
        onChange={containerChanged}
        options={containerOptions}
        optionName="name"
        disabled={!formValues.application || formDisabled}
      />
      { campaignBoxContainerId && Number(containerSelection) === Number(campaignBoxContainerId) &&
      <div style={{ marginBottom: '2rem' }}>
      <TextCaption component='p'>
      <IconTip color='blue' /> <span>Please note that campaigns in this container will also be pushed <p style={{ marginLeft: '2rem' }}>to Inbox Updates</p></span>
      </TextCaption>
      </div>
      }

      { containerSelection &&
        <>
          { /* product targeting section, render if selected container exists on account-key page */ }
          { (isMass || isTargeted) && getActivePages()?.some(p => p.pageId === 'account-key') &&
            <ProductTargeting
              // pre-select / pre-activate product targeting, previously saved data indicates this should be done
              // the only scenario where targeting data exists but pre-select should not happen is when creating
              // a new campaign, and adv targeting picks are made, but form is not disabled
              active={activateProductTargeting}
              disabled={formDisabled || (isMass && !productPlacements.length)}
              editable={isTargeted}
              handleActivation={handleProductTargetingChecked}
              handleEdit={handleProductChange}
              productBook={fullProductBook}
              productsTargeted={productPlacements}
              campaignType={formValues.type}
            />
          }

          { /* pages section */ }
          { getActivePages().length > 0 &&
            <Field
              childRef={pageAutosuggestRef}
              className='targeting-metadata__pages'
              component={InputSelectsField}
              editable={!formValues.application || !formDisabled}
              initialSelection={getPageSelections()}
              label="Pages"
              name="pages"
              onChange={handlePageChange}
              options={getActivePages({ excludeDynamicPages: true })}
              optionName="name"
              placeholder="Select pages"
              secondaryLabel={activateProductTargeting ? '(Optional)' : ''}
              showSuggestionsOnFocus
              hideSuggestionsAfterSelection
            />
          }
        </>
      }

      { /* content section */ }
      { formValues.content_id &&
        <Table
          id="campaign-content-table"
          className="targeting-metadata__content-table"
          title=""
          columns={contentTableColumns}
          data={[ formValues ]}
        />
      }

      { !formValues.content_id && containerSelection &&
        <Field
          name="contentful"
          component={InputGroupField}
        >
          <TextButton
            Icon={IconAdd}
            iconPosition="left"
            type="button"
            onClick={openContentModal}
          >
            Add content
          </TextButton>
        </Field>
      }
    </Card>
  );
};

TargetingMetadata.propTypes = {
  access: PropTypes.object,
  applications: PropTypes.array,
  change: PropTypes.func,
  containers: PropTypes.object,
  containerSelection: PropTypes.number,
  deleteContent: PropTypes.func,
  formDisabled: PropTypes.bool,
  formValues: PropTypes.object,
  handleChange: PropTypes.func,
  openContentModal: PropTypes.func,
  reset: PropTypes.func,
  pages: PropTypes.object,
  pageSelections: PropTypes.array,
  productBook: PropTypes.array,
  untouch: PropTypes.func,
  type: PropTypes.oneOf([ ruleTypesConstants.CAMPAIGN, ruleTypesConstants.ALERT, ruleTypesConstants.CCAU_CAMPAIGN ]),
};

const selector = formValueSelector('targetingMetadata');

const TargetingMetadataReduxForm = reduxForm({
  form: 'targetingMetadata',
  initialValues: {
    pages: [],
  },
})(TargetingMetadata);

export default connect(state => ({
  containerSelection: selector(state, 'container'),
}))(TargetingMetadataReduxForm);
