import React, { useMemo } from 'react';
import PropTypes from 'prop-types';

import Checkbox from 'canvas-core-react/lib/Checkbox';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import Tooltip from 'canvas-core-react/lib/Tooltip';

import Tag from '../../../autosuggest/tag';
import Autosuggest from '../../../autosuggest-v2';

/**
 * Target selected product's account details page.
 * This field component allows preview or selection of products
 * where selected product's account detail page will become
 * a page placement for a given rule.
 */
const ProductTargeting = ({
  active = false, // activate if conditions allow, e.g. not disabled and products targeted
  disabled = false,
  editable = false,
  handleActivation = () => {},
  handleEdit = () => {},
  productBook,
  productsTargeted = [],
  campaignType,
}) => {
  const generateTagLabel = (productTargeted, productBookRecord) => {
    const codeLabel = productTargeted.code ? `:${productBookRecord.code}` : '';
    const subCodeLabel = productTargeted.sub_code ? `:${productBookRecord.sub_code}` : '';
    const value = `${productBookRecord.ownership}${codeLabel}${subCodeLabel} ${productBookRecord.description}`;
    return value;
  };

  const userSelectableProducts = useMemo(() => {
    const out = productBook.map(p => ({
      ...p,
      label: generateTagLabel(p, p),
    }));
    return out;
  }, [ productBook ]);

  const preSelectedProducts = useMemo(() => {
    const products = productsTargeted.reduce((out, p) => {
      // find the exact product book record which matches a product selection
      const productSearchResult = [ 'ownership', 'code', 'id' ].reduce((result, prop) => {
        return result.filter(bookRecord => p.hasOwnProperty(prop)
          ? p[prop] === bookRecord[prop]
          : !bookRecord.hasOwnProperty(prop)
        );
      }, productBook);
      // no need to reduce search space after item found for performance gain
      // as use case only exists for 1-3 products targeted
      const pRecord = productSearchResult[0];
      if (pRecord) { // conveniently filter out products no longer offered
        const tagLabel = generateTagLabel(p, pRecord);
        out.push({ ...pRecord, label: tagLabel });
      }
      return out;
    }, []);
    return products;
  }, [ productsTargeted ]);

  return (
    <div className='product-targeting'>
      <Checkbox
        id="product-targeting__checkbox"
        className="product-targeting__checkbox"
        label={campaignType === 'targeted' ? 'Target specific product account details page' : 'Target selected product account details page'}
        disabled={disabled}
        checked={active}
        onChange={handleActivation}
        tooltip={
          <Tooltip
            id="product-targeting__checkbox-tooltip"
            heading="Target account details page"
            closeButtonLabel="Close"
            infoButtonLabel="Info"
          >
            <TextCaption component="p">
              {
                campaignType === 'targeted'
                  ? `This option allows you to target a specific product account details page.
                  (Ex. Displaying a Credit Limit Increase campaign on the Future Amex Gold account details page only.)`

                  : `This option allows you to target the account details page for products that have been selected in the advanced targeting component.
            (Ex. Displaying a Credit Limit Increase campaign on the Future Amex Gold account details page only.)`
              }
            </TextCaption>
          </Tooltip>
        }
      />

      { active && !editable && productsTargeted.length > 0 &&
        <>
          <label htmlFor='targeted-products' className='product-targeting__title'>Target products</label>
          <div id='targeted-products' className='product-targeting__tags'>
            { productsTargeted.map((product, index) => {
              const tagLabel = generateTagLabel(product, product);
              const tagProps = { index, value: tagLabel, disabled: true, onDelete: () => {} };
              return <Tag key={index} {...tagProps} />;
            }) }
          </div>
        </>
      }

      { active && editable &&
        <Autosuggest
          className='product-targeting__selector'
          editable={editable && !disabled}
          data={userSelectableProducts.sort((a, b) => a['label'].toLowerCase() < b['label'].toLowerCase() ? -1 : 1)}
          dataLegend={{ keyName: 'id', valueName: 'label' }}
          initialSelection={preSelectedProducts}
          label='Target products'
          limit={userSelectableProducts.length}
          onChange={handleEdit}
          placeholder='Select products'
          secondaryLabel=''
          showSuggestionsOnFocus
          hideSuggestionsAfterSelection
        />
      }
    </div>
  );
};

ProductTargeting.propTypes = {
  active: PropTypes.bool,
  disabled: PropTypes.bool,
  editable: PropTypes.bool,
  handleActivation: PropTypes.func,
  handleEdit: PropTypes.func,
  productBook: PropTypes.array.isRequired,
  productsTargeted: PropTypes.array,
  campaignType: PropTypes.string,
};

export default ProductTargeting;
