div.targeting-metadata__card {
  margin: 0 0 3rem 0;
  padding: 3.6rem 3.6rem 6rem 3.6rem;
}

.targeting-metadata {
  &__card {
    .Table__wrapper { overflow-x: visible; }
  }

  &__content-action-menu {
    ul { box-shadow: 0 0.2rem 1rem rgba(0, 34, 91, 0.11); }
    button.ActionMenu__KebabButton { height: 2.4rem; }
    .ActionMenu__dialog--top-left { left: 1.2rem; }
  }

  &__sub-header {
    margin-bottom: 3.6rem;
    &.TextIntroduction__text { font-size: 2.6rem; }
  }

  &__content-table {
    thead tr { border-bottom: 0.1rem solid $canvas-gray-600; }
    tbody tr { border-bottom: 0.1rem solid $canvas-gray-400; }
    thead th { min-width: 20rem; }
    tbody th { min-width: 20rem; }
    thead { padding-top: unset; }

    &-link:hover {
      color: #005e80;
      border: none;
    }
  }

  &__table-link {
    &:hover { text-decoration: underline; }

    .TextButton__button {
      margin: 0;
      padding: 0;
    }
  }

  &__container {
    width: 45rem;
    padding-bottom: 1.8rem;
  }

  &__pages {
    padding-bottom: 1.8rem;
    max-width: 45rem;
  }

  &__contentful { button { padding-bottom: 1rem; } }

  .InputGroup__error { margin-left: 3.6rem; }
}

a.targeting-metadata__content-table-link,
button.targeting-metadata__content-table-link {
  font-size: 1.4rem;
  height: 2.1rem;
  background: none !important;
  border: none;
  padding: 0 !important;
  font-family: $font-bold-family;
  color: $canvas-dark-blue;
  text-decoration: underline;
  cursor: pointer;
  min-width: auto;
}

// hide canvas default onHover ux
a.targeting-metadata__content-table-link > span, // default state
a.targeting-metadata__content-table-link:hover.Link__link > span, // hover over link
a.targeting-metadata__content-table-link:hover > span:hover { // hover over inner text label
  border: none;
}
