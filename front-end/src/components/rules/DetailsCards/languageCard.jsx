import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { mapValues, every, values, isEmpty } from 'lodash';
import { InputCheckboxField, InputGroupField } from '../../formFields';
import { useLocals } from '../../../hooks/useLocals';

const LanguagesName = {
  en: 'English',
  es: 'Spanish',
  fr: 'French',
};

const LanguageCard = ({ isCampaign, formValues, formDisabled, applications, formChange }) => {
  if (!(isCampaign && formValues.application)) {
    return null;
  }

  const onGetLocals = (languagesOptions) => {
    /**  on creating new rule the languages will be true by default
     *   OR open already created rule before adding targeting by language
     * */
    if (!formValues.id || isEmpty(formValues.mass_targeting?.languages)) {
      return formChange(
        'campaignDetails',
        'mass_targeting.languages',
        mapValues(languagesOptions, () => true),
      );
    }
  };
  const { languages } = useLocals({
    applications,
    selectedApp: formValues.application,
    onGetLocals,
  });

  const validateLanguage = useCallback(() => {
    return (
      every(values(formValues.mass_targeting?.languages || {}), (e) => !e) &&
      'You must select at least one language.'
    );
  }, [ formValues.mass_targeting?.languages || {} ]);

  return (
    <Field
      id="campaign-language-group"
      legend="Languages"
      className="admin-details__language"
      validate={[ validateLanguage ]}
      component={InputGroupField}
      name={`mass_targeting.languages`}
    >
      { languages.languagesCode.map((code, i) => (
        <Field
          key={code}
          name={`mass_targeting.languages.${code}`}
          label={LanguagesName[code]}
          component={InputCheckboxField}
          disabled={formDisabled}
        />
      )) }
    </Field>
  );
};

LanguageCard.propTypes = {
  isCampaign: PropTypes.bool,
  formValues: PropTypes.object.isRequired,
  formDisabled: PropTypes.bool,
  applications: PropTypes.array.isRequired,
  formChange: PropTypes.func,
};

export default LanguageCard;
