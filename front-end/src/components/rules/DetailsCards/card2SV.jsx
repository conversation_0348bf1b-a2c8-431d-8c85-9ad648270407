import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import classnames from 'classnames';

import Card from 'canvas-core-react/lib/Card';
import TextHeadline from 'canvas-core-react/lib/TextHeadline';
import InputGroup from 'canvas-core-react/lib/InputGroup';
import Tooltip from 'canvas-core-react/lib/Tooltip';
import TextBody from 'canvas-core-react/lib/TextBody';

import { InputCheckboxField, InputRadioButtonField } from '../../formFields';
import { ruleSubTypes } from '../../../constants';

const enrollmentStatus = [
  {
    name: 'REGISTERED',
    label: 'Registered users',
  },
  {
    name: 'ENROLL_REQUIRED',
    label: 'Registration required',
  },
  {
    name: 'NEW',
    label: 'New users',
  },
  {
    name: 'null',
    label: 'Unregistered users',
  },
];

const lockStatus = [
  {
    name: 'none',
    label: 'None',
  },
  {
    name: 'on',
    label: 'On',
  },
  {
    name: 'off',
    label: 'Off',
  },
];

const Card2SV = ({
  isCampaign = false,
  formValues,
  formDisabled,
}) => {
  // Dispaly this card only in case of target campian or mass message only and for nove  app only
  const shouldDisplayCard =
    isCampaign &&
    (formValues.type === ruleSubTypes.TARGETED ||
      formValues.type === ruleSubTypes.MASS) &&
    formValues.application === 'nova';

  if (!shouldDisplayCard) {
    return null;
  }

  return (
    <Card className='admin-details__card' type='floatLow'>
      <TextHeadline
        component='h2'
        size={21}
        className='admin-details__sub-header'
      >
        Target by 2SV status
      </TextHeadline>
      <InputGroup
        id='campaign-enrollment-status-radio-group'
        legend='2SV enrollment status'
        secondaryLabel='(Optional)'
        inline
        tooltip={<TooltipBody />}
        className={classnames('admin-details__field', 'admin-details__field_2sv_options')}
      >
        { enrollmentStatus.map(({ name, label }) => (
          <Field
            key={name}
            name={`mass_targeting.enrollment_status.${name}`}
            label={label}
            component={InputCheckboxField}
            disabled={formDisabled}
          />
        )) }
      </InputGroup>
      <InputGroup
        id='campaign-lock-status-radio-group'
        name='mass_targeting.device_lock'
        legend='Device lock enable status'
        inline
      >
        { lockStatus.map(({ name, label }) => (
          <Field
            id={name}
            key={name}
            name='mass_targeting.device_lock'
            label={label}
            component={InputRadioButtonField}
            disabled={formDisabled}
          />
        )) }
      </InputGroup>
    </Card>
  );
};

Card2SV.propTypes = {
  isCampaign: PropTypes.bool.isRequired,
  formDisabled: PropTypes.bool.isRequired,
  formValues: PropTypes.object.isRequired,
};

// Tooltip Body
const TooltipBody = () => {
  return (
    <Tooltip
      id='campaign-enrollment-status-tooltip'
      heading='Enrollment status'
      infoButtonLabel='Info'
      closeButtonLabel='close'
    >
      <TextBody component='h4' type='2' bold>
        Registered users
      </TextBody>
      <TextBody
        component='p'
        type='2'
        className='admin-details__campaign-enrollment_tooltip_item'
      >
        Customers who have already registered for 2SV.
      </TextBody>
      <TextBody component='h4' type='2' bold>
        Registration required
      </TextBody>
      <TextBody
        component='p'
        type='2'
        className='admin-details__campaign-enrollment_tooltip_item'
      >
        Customer who have previously deactivated 2SV and must reactivate 2SV to
        continue banking, or are in the mandatory rollout population.
      </TextBody>
      <TextBody component='h4' type='2' bold>
        New users
      </TextBody>
      <TextBody
        component='p'
        type='2'
        className='admin-details__campaign-enrollment_tooltip_item'
      >
        New customers who will be required to activate 2SV if they choose to
        download and use the Nova mobile app.
      </TextBody>
      <TextBody component='h4' type='2' bold>
        Unregistered users
      </TextBody>
      <TextBody component='p' type='2'>
        Existing customers who haven&apos;t fallen into the mandatory population
        yet, or has removed themselves from 2SV by calling the call centre.
      </TextBody>
    </Tooltip>
  );
};

export default Card2SV;
