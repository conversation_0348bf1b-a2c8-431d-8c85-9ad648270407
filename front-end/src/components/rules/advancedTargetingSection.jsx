import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import Card from 'canvas-core-react/lib/Card';
import Tooltip from 'canvas-core-react/lib/Tooltip';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';

import AdvancedTargeting from '../../components/advancedTargeting';
import { aInB, capitalize } from '../../utils';
import { includeOptions } from '../advancedTargeting/common';
import { mapPropToKey } from '../../constants';

const componentTypeColumnConfig = {
  products: [ 'include', 'exclude' ],
  permissions: [ 'include', 'exclude' ],
};

const getProductTree = (productBook, initialSelections) => {
  const input = [
    { description: 'Retail', ownership: 'R' },
    { description: 'Business', ownership: 'B' },
    ...productBook,
  ];

  input.forEach(item => {
    const filteredItem = {
      ownership: item.ownership,
      code: item.code,
      'sub_code': item['sub_code'],
    };

    // determine here if the item should be included or excluded by comparing to initial selections
    item.include = initialSelections.include.some(selection => aInB(selection, filteredItem));
    item.exclude = initialSelections.exclude.some(selection => aInB(selection, filteredItem));
  });

  const output = [];

  const itemProperties = [ 'ownership', 'category', 'code', 'sub_code' ];

  const getItemInsertionPoint = (product, insertionPoint = output, propertiesToAdd = [ ...itemProperties ]) => {
    // get first item property, remove from propertyList
    const currentProperty = propertiesToAdd.shift();

    const hasAdditionalPropertiesToAdd = propertiesToAdd.some(p => !!product[p]);

    if (!product[currentProperty]) {
      return;
    }

    // does an item for this property already exist?
    let foundItem = insertionPoint.find(item => item[currentProperty] === product[currentProperty]);

    if (!foundItem) {
      const pi = itemProperties.indexOf(currentProperty);

      const properties = itemProperties.slice(0, pi + 1).reduce((acc, v) => {
        if (!product[v]) {
          return acc;
        }

        acc[v] = product[v];
        return acc;
      }, {});
      foundItem = {
        label: currentProperty === 'category'
          ? capitalize(product.category)
          : currentProperty === 'code'
            ? 'Product Group'
            : 'Unknown item',
        children: currentProperty !== 'sub_code' ? [] : undefined,
        virtualGroup: currentProperty === 'category' ? true : undefined,
        ...properties,
      };

      insertionPoint.push(foundItem);
    } else {
      foundItem.children = foundItem.children || [];
    }

    if (!hasAdditionalPropertiesToAdd) {
      foundItem.label = product.description;
      foundItem.include = product.include;
      foundItem.exclude = product.exclude;
      foundItem.id = product.id
    }

    insertionPoint = foundItem.children;
    getItemInsertionPoint(product, insertionPoint, propertiesToAdd);
  };

  input.forEach(product => getItemInsertionPoint(product));

  return output;
};

const getPermissionsTree = (applications, containers, pages, initialSelections) => {
  // convert initialSelections into a map e.g. { application: {1: {}}}
  const selectionMap = Object.keys(initialSelections).reduce((acc, val) => {
    acc[val] = mapPropToKey(initialSelections[val], 'id');
    return acc;
  }, {});

  const buildCampaignAlertChildren = (ruleType) => Object.values(applications)
    .filter(o => o.ruleTypes.includes(ruleType))
    .map(o => {
      return {
        label: o.name,
        type: 'application',
        id: o.id,
        children: [ {
          label: 'Pages',
          children: Object.values(pages).map(page => {
            if (page.application === o.id) {
              return {
                label: page.name,
                type: 'page',
                id: page.id,
                include: !!selectionMap.pages?.[page.id] && selectionMap.pages[page.id].ruleTypes.includes(`${ruleType}s`),
              };
            }
          }).filter(page => page),
        }, {
          label: 'Containers',
          children: Object.values(containers).map(container => {
            if (container.application === o.id) {
              return {
                label: container.name,
                type: 'container',
                id: container.id,
                include: !!selectionMap.containers?.[container.id] && selectionMap.containers[container.id].ruleTypes.includes(`${ruleType}s`),
              };
            }
          }).filter(container => container),
        }, {
          label: 'Campaign types',
          children: [ {
            label: 'Mass Campaign',
            type: 'ruleType',
            id: 'mass',
            applicationId: o.id, // id this rule type relates to
          }, {
            label: 'Mass Message',
            type: 'ruleType',
            id: 'message',
            applicationId: o.id,
          }, {
            label: 'Targeted Campaign',
            type: 'ruleType',
            id: 'targeted',
            applicationId: o.id,
          } ],
        } ],
      };
    });

  const output = [ {
    label: 'Campaigns',
    children: buildCampaignAlertChildren('campaign'),
  }, {
    label: 'Alerts',
    children: buildCampaignAlertChildren('alert'),
  }, {
    label: 'Placement',
    children: [ 'Applications', 'Pages', 'Containers' ].map((name, i) => {
      return {
        label: name,
        type: 'placement',
        id: name.toLowerCase(),
      };
    }),
  }, {
    label: 'Access control',
    children: [
      { label: 'Teams', id: 'teams' },
      { label: 'Users & roles (own team)', id: 'usersRoles' },
      { label: 'Users & roles (all teams)', id: 'usersRoles' },
    ].map(({ label, id }) => {
      return {
        label: label,
        type: 'accessControl',
        id: id,
      };
    }),
  }, {
    label: 'Variable Mapping',
    children: [ 'KT', 'Pega' ].map(name => {
      return {
        label: name,
        type: 'variableMapping',
        id: name.toLowerCase(),
      };
    }),
  } ];

  return output;
};

const AdvancedTargetingSection = ({
  componentType,
  productBook,
  initialSelections,
  onChange,
  input,
  disabled,
  defaultIncludeMode,
  labels,
  isOptional,
  includeSearch,
  includeSelectionPreview,
  applications,
  containers,
  pages,
  disableOwnershipSelection,
  selectOnlyIncludeorExclude,
  productRelationShipCheckBoxes,
}) => {
  const isOffersProductSelections = disableOwnershipSelection ? initialSelections : null;
  // turn the product book or applications/containers/pages into a tree that Advanced Targeting understands
  const targetingData = useMemo(() => {
    switch (componentType) {
      case 'products':
        return getProductTree(productBook, initialSelections);
      case 'permissions':
        return getPermissionsTree(applications, containers, pages, initialSelections);
    }
  }, [ productBook, applications, containers, pages, isOffersProductSelections ]);

  const itemTagRenderer = obj => {
    if (obj.virtualGroup) {
      return null;
    }

    // don't render for only ownership
    if (obj.ownership && (!obj.code && !obj.sub_code)) {
      return null;
    }

    return (
      [ 'ownership', 'code', 'sub_code' ]
        .map(key => obj[key])
        .filter(Boolean)
        .join(':')
    );
  };

  // eslint-disable-next-line camelcase
  const onChangeFormatter = ({ ownership, code, sub_code , id , label }) => ({ ownership, code, sub_code , id , description:label });
  const permissionsOnChangeFormatter = ({ category, label, children, type, id, include, parent }) => ({ category, label, children, type, id, include, parent });

  return (
    <Card className="advanced-targeting-section" type="floatLow">
      <h2 className="advanced-targeting-section__heading heading">{ labels.sectionTitle }</h2>
      <TextSubtitle type="2" component="h3" className="advanced-targeting-section__subtitle">
        { labels.componentTitle } { isOptional && <span className="advanced-targeting-section__optional-text">(optional)</span> }
        <Tooltip
          id="target-by-tooltip"
          heading={labels.componentTitle}
          infoButtonLabel="Info"
          closeButtonLabel="close"
        >
          <TextCaption component="p">{ labels.tooltipLabel }</TextCaption>
        </Tooltip>
      </TextSubtitle>
      <AdvancedTargeting
        data={targetingData}
        renderItemTag={itemTagRenderer}
        defaultIncludeMode={defaultIncludeMode}
        onChangeFormatter={componentType === 'products' ? onChangeFormatter : permissionsOnChangeFormatter}
        onChange={onChange || input.onChange}
        disabled={disabled}
        includeSearch={includeSearch}
        labels={{
          groupingTitle: labels.groupingTitle,
          searchPlaceholder: labels.searchPlaceholder,
          includeLabel: labels.includeLabel,
          excludeLabel: labels.excludeLabel,
        }}
        includeSelectionPreview={includeSelectionPreview}
        columns={componentTypeColumnConfig[componentType]}
        disableOwnershipSelection={disableOwnershipSelection}
        selectOnlyIncludeorExclude={selectOnlyIncludeorExclude}
        productRelationShipCheckBoxes={productRelationShipCheckBoxes}
      />
    </Card>
  );
};

AdvancedTargetingSection.propTypes = {
  className: PropTypes.string,
  componentType: PropTypes.oneOf([ 'products', 'permissions' ]),
  productBook: PropTypes.array,
  initialSelections: PropTypes.shape({
    include: PropTypes.array,
    exclude: PropTypes.array,
  }),
  onChange: PropTypes.func,
  input: PropTypes.object,
  disabled: PropTypes.bool,
  defaultIncludeMode: PropTypes.oneOf(Object.values(includeOptions)),
  labels: PropTypes.shape({
    sectionTitle: PropTypes.string,
    componentTitle: PropTypes.string,
    tooltipLabel: PropTypes.string,
    groupingTitle: PropTypes.string,
    searchPlaceholder: PropTypes.string,
    includeLabel: PropTypes.string,
    excludeLabel: PropTypes.string,
  }),
  applications: PropTypes.object,
  pages: PropTypes.object,
  containers: PropTypes.object,
  includeSearch: PropTypes.bool,
  includeSelectionPreview: PropTypes.bool,
  isOptional: PropTypes.bool,
  disableOwnershipSelection: PropTypes.bool,
  selectOnlyIncludeorExclude: PropTypes.bool,
  disableAncestorCheckState: PropTypes.bool,
  productRelationShipCheckBoxes: PropTypes.array,
};

AdvancedTargetingSection.defaultProps = {
  componentType: 'products',
  initialSelections: { include: [], exclude: [] },
  labels: {
    sectionTitle: 'Advanced targeting',
    componentTitle: 'Target by',
    tooltipLabel: 'Select the targeting criteria to include or exclude for this campaign',
    groupingTitle: 'Categories',
    searchPlaceholder: 'Search',
    includeLabel: 'Include',
    excludeLabel: 'Exclude',
  },
  includeSearch: false,
  includeSelectionPreview: false,
  isOptional: false,
  disableOwnershipSelection: false,
  selectOnlyIncludeorExclude: false,
  disableAncestorCheckState: false,
  productRelationShipCheckBoxes: null,
};

export default AdvancedTargetingSection;
