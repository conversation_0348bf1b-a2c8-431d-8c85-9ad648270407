import React from 'react';
import { render, fireEvent, within } from '@testing-library/react';
import AdvancedTargetingSection from './advancedTargetingSection';

const productBook = [ {
  'id': '100050',
  'description': 'Current Account',
  'ownership': 'R',
  'code': 'DDA',
  'sub_code': 'CA',
  'category': 'banking',
}, {
  'id': '100060',
  'description': 'Current Account',
  'ownership': 'B',
  'code': 'DDA',
  'sub_code': 'CA',
  'category': 'banking',
}, {
  'id': '100100',
  'description': 'Scotia Chequing',
  'ownership': 'B',
  'code': 'DDA',
  'sub_code': 'SC',
  'category': 'banking',
}, {
  'id': '100020',
  'description': 'DDA',
  'ownership': 'B',
  'code': 'DDA',
  'category': 'banking',
}, {
  'id': '200060',
  'description': 'AgriInvest Account',
  'ownership': 'B',
  'code': 'SAV',
  'sub_code': 'AG',
  'category': 'banking',
}, {
  'id': '200500',
  'description': 'Ultimate Package',
  'ownership': 'B',
  'code': 'SAV',
  'sub_code': 'AU',
  'category': 'banking',
}, {
  'id': '200600',
  'description': 'Student Banking Advantage Plan',
  'ownership': 'B',
  'code': 'SAV',
  'sub_code': 'BA',
  'category': 'banking',
}, {
  'id': '200080',
  'description': 'Basic',
  'ownership': 'B',
  'code': 'SAV',
  'sub_code': 'BB',
  'category': 'banking',
}, {
  'id': '200100',
  'description': 'Blue Chip',
  'ownership': 'B',
  'code': 'SAV',
  'sub_code': 'BC',
  'category': 'banking',
}, {
  'id': '200120',
  'description': 'Money Master',
  'ownership': 'B',
  'code': 'SAV',
  'sub_code': 'BM',
  'category': 'banking',
}, {
  'id': '200580',
  'description': 'Basic Plus',
  'ownership': 'B',
  'code': 'SAV',
  'sub_code': 'BP',
  'category': 'banking',
}, {
  // lone product in group AXG to test auto generating group name
  'category': 'borrowing',
  'code': 'AXG',
  'description': 'Scotiabank Gold Amex Card',
  'id': '500250',
  'ownership': 'R',
  'sub_code': 'GC',
} ];

describe('AdvancedTargetingSection - Product Book', () => {
  test('onChange called when product is selected', () => {
    const onChange = jest.fn();
    const { getByLabelText } = render(
      <AdvancedTargetingSection
        productBook={productBook}
        onChange={onChange}
      />,
    );
    expect(onChange).not.toHaveBeenCalled();

    fireEvent.click(getByLabelText('Include all Retail items'));
    expect(onChange).toHaveBeenCalled();
  });

  test('initial selections', () => {
    const initialSelections = {
      include: [
        { ownership: 'R' },
        {
          'ownership': 'B',
          'code': 'DDA',
          'sub_code': 'CA',
        },
      ],
      exclude: [ {
        'ownership': 'B',
        'code': 'SAV',
        'sub_code': 'BA',
      },
      ],
    };

    const { getByLabelText } = render(
      <AdvancedTargetingSection
        productBook={productBook}
        initialSelections={initialSelections}
        onChange={jest.fn()}
      />,
    );

    expect(getByLabelText('Include all Retail items')).toBeChecked();
    expect(getByLabelText('Include the Current Account item')).toBeChecked();
    expect(getByLabelText('Exclude all Business items')).toBePartiallyChecked();
    expect(getByLabelText('Exclude the Student Banking Advantage Plan item')).toBeChecked();
  });

  test('auto generate group name for missing group level products', () => {
    const onChange = jest.fn();
    const { getByText, queryByText } = render(
      <AdvancedTargetingSection productBook={productBook} onChange={onChange}/>,
    );
    // expand retail section
    expect(queryByText('Borrowing')).not.toBeInTheDocument();
    fireEvent.click(getByText('Retail'));
    expect(getByText('Borrowing')).toBeInTheDocument();

    // expand borrowing section
    expect(queryByText('R:AXP')).not.toBeInTheDocument();
    fireEvent.click(getByText('Borrowing'));

    // asserts
    const productGroupTag = getByText('R:AXG');
    const generatedGroupName = within(productGroupTag.parentElement).queryByText('Product Group');
    expect(generatedGroupName).toBeInTheDocument();
  });
});

const applications = {
  1: {
    applicationId: 'nova',
    id: 3,
    name: 'Nova Mobile',
    platformIds: [ 2, 3 ],
    platforms: [ 'iOS', 'Android' ],
    ruleTypeIds: [ 1, 2 ],
    ruleTypes: [ 'alert', 'campaign' ],
    rule_version: 1,
    status: true,
  },
  2: {
    applicationId: 'abm',
    id: 5,
    name: 'ABM',
    platformIds: [ 1 ],
    platforms: [ 'Web' ],
    ruleTypeIds: [ 2 ],
    ruleTypes: [ 'campaign' ],
    rule_version: 1,
    status: true,
  },
};

const containers = { 1: {
  application: 3, // nova
  containerId: 'my-activity',
  content_type: [ 'standingCampaign' ],
  description: 'My Activity',
  id: 2,
  name: 'my-activity',
  pages: [ 1 ],
  rule_type: 'campaign',
  status: true,
},
2: {
  application: 3, // nova
  containerId: 'offers-and-programs',
  content_type: [ 'targetedCampaign' ],
  description: 'Offers and Programs',
  id: 3,
  name: 'offers-and-programs',
  pages: [ 2 ],
  rule_type: 'campaign',
  status: true,
},
3: {
  application: 5, // abm
  containerId: 'preOffer',
  content_type: [ 'abm__campaign' ],
  description: 'ABM - Preliminary offer container',
  id: 92,
  name: 'preOffer',
  pages: [ 68 ],
  rule_type: 'campaign',
  status: true,
},
4: {
  application: 5, // abm
  containerId: 'mainOffer',
  content_type: [ 'abm__campaign' ],
  description: 'ABM - Main page offer container',
  id: 93,
  name: 'mainOffer',
  pages: [ 68 ],
  rule_type: 'campaign',
  status: true,
} };

const pages = { 1: {
  application: 3, // nova
  containers: [ 2 ],
  description: 'NOVA mobile - My Activity tab',
  id: 1,
  name: 'activities',
  pageId: 'activities',
  status: true,
},
2: {
  application: 3, // nova
  containers: [ 3, 4 ],
  description: 'NOVA mobile - My Accounts tab',
  id: 2,
  name: 'accounts',
  pageId: 'accounts',
  status: true,
},
3: {
  application: 5, // abm
  containers: [ 92, 93 ],
  description: 'ABM - Page presented before main menu',
  id: 68,
  name: 'preMainMenu',
  pageId: 'preMainMenu',
  status: true,
} };

const variableMappingHostNames = [ 'Host name 1', 'Host name 2' ];

describe('AdvancedTargetingSection - Permissions', () => {
  test('onChange called when product is selected', () => {
    const onChange = jest.fn();
    const { getByLabelText } = render(
      <AdvancedTargetingSection
        componentType='permissions'
        applications={applications}
        containers={containers}
        pages={pages}
        variableMappingHostNames={variableMappingHostNames}
        onChange={onChange}
      />,
    );
    expect(onChange).not.toHaveBeenCalled();

    fireEvent.click(getByLabelText('Include all Campaigns items'));
    expect(onChange).toHaveBeenCalled();
  });
});
