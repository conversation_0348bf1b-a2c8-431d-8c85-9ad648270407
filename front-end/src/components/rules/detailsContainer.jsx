import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { reduxForm, change, untouch, reset } from 'redux-form';
import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { omit, cloneDeep } from 'lodash';

import { req } from '../../api';
import {
  populateAlertDetails,
  updateAlertDetails,
} from '../../store/actions/alerts';
import {
  populateCampaignDetails,
  updateCampaignDetails,
} from '../../store/actions/campaigns';
import { openContentModal } from '../../store/actions/modal';
import { addSnackbar } from '../../store/actions/snackbar';
import { addAlert, removeAlert } from '../../store/actions/alertBanner';
import { getContainers } from '../../store/actions/containers';
import { getApplications } from '../../store/actions/applications';
import { getRuleTypes } from '../../store/actions/rule-types';
import { getRuleSubTypes } from '../../store/actions/ruleSubTypes';
import { getPlatforms } from '../../store/actions/platforms';
import { getUsers } from '../../store/actions/users';
import { convertToSemver } from '../versionTargeting/constants';
import { getPages } from '../../store/actions/pages';
import { getProductPlacements } from './utils';

import Details from './details';
import {
  STATUS,
  DATE_ISO_FORMAT,
  ruleTypes,
  ruleSubTypesDisplayName as ruleSubType,
  mapIdToKey,
  mapAlertStatusToTitle,
  mapCampaignStatusToTitle,
  setBrowserTitle,
  scrollToTop,
  formatWord,
  mapTruePropsToArray,
} from '../../constants';

const emptyTargetingObject = platform => ({ platform, items: [] });

export class DetailsContainer extends React.PureComponent {
  static propTypes = {
    formValues: PropTypes.object.isRequired,
    match: PropTypes.shape({
      params: PropTypes.shape({
        id: PropTypes.string,
        action: PropTypes.string,
      }).isRequired,
    }).isRequired,
    history: PropTypes.shape({
      goBack: PropTypes.func.isRequired,
      push: PropTypes.func.isRequired,
      replace: PropTypes.func.isRequired,
      listen: PropTypes.func.isRequired,
    }).isRequired,
    location: PropTypes.shape({
      pathname: PropTypes.string,
    }),
    handleSubmit: PropTypes.func.isRequired,
    populateDetails: PropTypes.func.isRequired,
    getContainers: PropTypes.func.isRequired,
    updateDetails: PropTypes.func.isRequired,
    openContentModal: PropTypes.func.isRequired,
    addSnackbar: PropTypes.func.isRequired,
    addAlert: PropTypes.func.isRequired,
    removeAlert: PropTypes.func.isRequired,
    type: PropTypes.string.isRequired,
    permissions: PropTypes.object.isRequired,
    access: PropTypes.object.isRequired,
    assignees: PropTypes.array,
    containers: PropTypes.shape({
      items: PropTypes.array,
      isLoading: PropTypes.bool,
    }).isRequired,
    pages: PropTypes.shape({
      items: PropTypes.array,
      isLoading: PropTypes.bool,
    }).isRequired,
    valid: PropTypes.bool,
    users: PropTypes.object,
    getUsers: PropTypes.func,
    getPages: PropTypes.func,
    formChange: PropTypes.func.isRequired,
    formReset: PropTypes.func,
    formUntouch: PropTypes.func,
    getApplications: PropTypes.func.isRequired,
    getRuleTypes: PropTypes.func.isRequired,
    getRuleSubTypes: PropTypes.func.isRequired,
    getPlatforms: PropTypes.func.isRequired,
    applications: PropTypes.array,
    ruleTypes: PropTypes.array,
    ruleSubTypes: PropTypes.array,
    platforms: PropTypes.array,
  };

  state = { ready: false };

  async init() {
    const productBook = await req.get('/product-book');
    this.setState({ productBook: productBook.data });
    if (this.ID) {
      this.fetchById(this.ID).then(() => {
        setBrowserTitle(this.title);
      });
    } else {
      this.setState({ ready: true });
      setBrowserTitle(this.title);
    }
  }

  componentDidMount = () => {
    const { users, type } = this.props;

    this.init();
    this.props.getContainers();
    this.props.getApplications();
    this.props.getRuleTypes();
    this.props.getRuleSubTypes();
    this.props.getPlatforms();
    this.props.getPages();
    if (type === ruleTypes.CAMPAIGN) {
      if (
        !users ||
        !users.items ||
        !Array.isArray(users.items) ||
        users.items.length === 0
      ) {
        this.props.getUsers();
      }
    }
  };

  componentDidUpdate(prevProps) {
    if (prevProps.location.pathname !== this.props.location.pathname) {
      this.init();
    }
  }

  get ID() {
    return this.props.match.params.id;
  }

  get urlRouteForType() {
    return this.props.type === ruleTypes.CCAU_CAMPAIGN
      ? '/ccau_campaigns'
      : this.props.type === ruleTypes.CAMPAIGN
      ? '/campaigns'
      : '/alerts';
  }

  get status() {
    if (this.duplicated) {
      return null;
    }
    return this.props.formValues.status;
  }

  get title() {
    if (this.props.type === ruleTypes.ALERT) {
      return mapAlertStatusToTitle(this.status);
    }
    if (
      this.props.type === ruleTypes.CAMPAIGN ||
      this.props.type === ruleTypes.CCAU_CAMPAIGN
    ) {
      return mapCampaignStatusToTitle(this.status);
    }
  }

  get duplicated() {
    return this.props.match.params.action === 'duplicate';
  }

  get apiType() {
    return this.props.type === ruleTypes.CCAU_CAMPAIGN
      ? ruleTypes.CAMPAIGN
      : this.props.type;
  }

  fetchById = id => {
    return req
      .get(`/${this.apiType}-rules/${id}`)
      .then(res => {
        // If rule's rule type (campaign/ccau campaign) doesn't match url rule type, redirect to url rule type list page
        if (res.data.type && res.data.type !== this.props.type) {
          this.goBack();
        }
        // Populate details first
        this.props.populateDetails({
          ...res.data,
          productBook: this.state.productBook,
        });

        // Then transform external_ref to type field for form state
        if (res.data.external_ref) {
          const knownTypes = [ 'MASS', 'MESSAGE', 'WEALTH' ];
          const offerIdPattern = /^(OFF|BEN|PER)-\d{1,10}$/i;

          if (knownTypes.includes(res.data.external_ref.toUpperCase())) {
            // This is a non-targeted campaign, update form type to lowercase external_ref
            const newType = res.data.external_ref.toLowerCase();
            this.props.formChange('campaignDetails', 'type', newType);
          } else if (offerIdPattern.test(res.data.external_ref)) {
            // This is an offer campaign with offer ID pattern
            this.props.formChange('campaignDetails', 'type', 'offer');
          } else {
            // This is a targeted campaign with custom external_ref
            this.props.formChange('campaignDetails', 'type', 'targeted');
          }
        }
        this.setState({ ready: true });
      })
      .catch(err => {
        if (err.response.status === 404) {
          this.goBack();
        }
      });
  };

  goBack = () => {
    this.props.history.push(this.urlRouteForType);
  };

  duplicate = () => {
    this.props.history.push(`${this.urlRouteForType}/${this.ID}/duplicate`);
  };

  formatAssignees = assignees => {
    if (!assignees || !Array.isArray(assignees)) {
      return;
    }
    return assignees.map(({ sid }) => sid);
  };

  formatValues = (values, status) => {
    let filteredPlatformTargeting;
    const platforms = mapTruePropsToArray(values.platforms || {});
    const platformTargeting = convertToSemver(values.platforms_targeting);
    const isCampaign =
      ruleTypes.CAMPAIGN === this.props.type ||
      ruleTypes.CCAU_CAMPAIGN === this.props.type;
    const isTargeted = ruleSubType.targeted === ruleSubType[values.type];
    const enrollmentStatus = mapTruePropsToArray(
      values?.mass_targeting?.enrollment_status || {},
    );
    const languages = mapTruePropsToArray(
      values?.mass_targeting?.languages || {},
    );
    // if the platform has been checked, include its related platform_targeting in submission
    filteredPlatformTargeting = platformTargeting // eslint-disable-line camelcase
      ? platformTargeting.filter(platform_targeting =>
          platforms.includes(platform_targeting.platform),
        ) // eslint-disable-line camelcase
      : [];
    // if we have a platform that has been checked, but has no targeting items, then add an empty platforms_targeting object to filteredPlatformTargeting
    platforms.forEach(platform => {
      const hasTargeting = filteredPlatformTargeting.find(
        item => item.platform === platform,
      );
      if (!hasTargeting) {
        filteredPlatformTargeting.push(emptyTargetingObject(platform));
      }
    });

    let payload = {
      name: values.name && values.name.trim(),
      start_at: moment(values.start_at).toISOString(),
      end_at: moment(values.end_at).toISOString(),
      platforms,
      app_version:
        values.platforms.ios && values.platforms.android
          ? null
          : values.app_version && values.app_version.trim(),
      content_space: Object.values(this.props.applications || {}).find(
        app => app.applicationId === values.application,
      ).contentful_space,
      content_type: values.content_type,
      content_id: values.content_id,
      container: values.container,
      pages: values.pages || [],
      targeting: {
        products: [],
      },
      status,
      application: values.application,
    };

    if (isCampaign && isTargeted) {
      // TODO create dedicated db column for json structured used in targeting metadata or dynamic page placement
      payload.mass_targeting = cloneDeep(values.mass_targeting);
    }

    if (values.type === 'mass') {
      payload.mass = true;

      payload.mass_targeting = values.mass_targeting || {};
      if (!payload.mass_targeting.by_product) {
        payload.mass_targeting = { v: 1 };
      }

      if (values.advancedTargeting) {
        const productTargeting = {};
        if (values.advancedTargeting.include?.length > 0) {
          productTargeting[`${values.advancedTargeting.includeMode}_of`] =
            values.advancedTargeting.include;
        }
        if (values.advancedTargeting.exclude?.length > 0) {
          productTargeting['none_of'] = values.advancedTargeting.exclude;
        }
        payload.mass_targeting.by_product = productTargeting;
      }
    }

    if (values.type !== 'message') {
      payload.mass_targeting = payload.mass_targeting || {};
      if (values.mass_targeting?.by_scene_points) {
        payload.mass_targeting.by_scene_points =
          values.mass_targeting?.by_scene_points;
      }

      payload.mass_targeting.enrollment_status = enrollmentStatus.map(e =>
        e === 'null' ? null : e,
      );

      if (
        values?.mass_targeting?.device_lock &&
        values.mass_targeting.device_lock !== 'none'
      ) {
        payload.mass_targeting.device_lock =
          values.mass_targeting.device_lock === 'on';
      } else {
        payload = omit(payload, 'mass_targeting.device_lock');
      }
    }

    payload.platforms_targeting = filteredPlatformTargeting;

    // TODO tw - bug? not checking anything against rule type constant
    if (
      values.newAssignees &&
      ruleTypes.CAMPAIGN &&
      ruleTypes.CCAU_CAMPAIGN &&
      status !== STATUS.DRAFT
    ) {
      payload.assignees = this.formatAssignees(values.newAssignees);
    }

    if (
      this.props.type === ruleTypes.CAMPAIGN ||
      this.props.type === ruleTypes.CCAU_CAMPAIGN
    ) {
      payload.external_ref =
        values.type === 'targeted' || values.type === 'offer'
          ? values.external_ref.trim()
          : values.type.toUpperCase();
      payload.pages = values.pages;
      payload.urgent = values.urgent;
      payload.dismissable_flag = values.dismissable_flag;
    }

    if (this.props.type === ruleTypes.CCAU_CAMPAIGN) {
      payload.type = ruleTypes.CCAU_CAMPAIGN;
    }

    if (values.type === 'wealth') {
      payload.mass_targeting = payload.mass_targeting || {};

      if (values.mass_targeting?.wealth_lobs) {
        payload.mass_targeting.wealth_lobs = values.mass_targeting.wealth_lobs;
      }

      if (values.mass_targeting?.by_demographic) {
        payload.mass_targeting.by_demographic =
          values.mass_targeting.by_demographic;
      }

      if (values.mass_targeting?.iclub_tiers) {
        payload.mass_targeting.iclub_tiers = values.mass_targeting.iclub_tiers;
      }

      if (values.mass_targeting?.segment_ids) {
        payload.mass_targeting.segment_ids = values.mass_targeting.segment_ids;
      }

      if (values.mass_targeting?.investment_knowledge) {
        payload.mass_targeting.investment_knowledge =
          values.mass_targeting.investment_knowledge;
      }

      // For wealth campaigns, don't add languages at root level since they're part of by_demographic
      payload.mass_targeting = cloneDeep(payload.mass_targeting);
    } else {
      // For non-wealth campaigns, add languages at root level
      payload.mass_targeting = cloneDeep({
        ...payload.mass_targeting,
        languages,
      });
    }
    return payload;
  };

  makeToast = (name, action) => {
    this.props.addSnackbar({
      message: `${formatWord(
        this.props.type === ruleTypes.CCAU_CAMPAIGN
          ? ruleTypes.CAMPAIGN
          : this.props.type,
        { capitalize: true },
      )} "${name}" has been ${action} successfully.`,
    });
  };

  onActionSuccess = (data, action) => {
    this.makeToast(data.name, action);
    scrollToTop();
    this.props.populateDetails({
      ...data,
      productBook: this.state.productBook,
    });
  };

  submit = (status = STATUS.SUBMITTED) =>
    this.props.handleSubmit(values => {
      const isUpdate = this.ID && !this.duplicated;
      const payload = this.formatValues(values, status);
      const request = isUpdate
        ? req.put(`/${this.apiType}-rules/${this.ID}`, payload)
        : req.post(`/${this.apiType}-rules`, payload);
      request.then(res => {
        this.onActionSuccess(
          res.data,
          status === STATUS.SUBMITTED ? 'submitted' : 'saved',
        );
        if (!isUpdate) {
          this.props.history.replace(`${this.urlRouteForType}/${res.data.id}`);
        }
      });
    });

  approve = (shouldEmpty, approved = true) =>
    this.props.handleSubmit(values => {
      let withAssignees = {};
      if (
        !shouldEmpty &&
        values &&
        values.newAssignees &&
        Array.isArray(values.newAssignees)
      ) {
        withAssignees = {
          assignees: this.formatAssignees(values.newAssignees),
        };
      }
      req
        .patch(`/${this.apiType}-rules/${this.ID}`, {
          status: approved ? STATUS.REVIEWED : STATUS.DRAFT,
          ...withAssignees,
          type: this.props.type,
        })
        .then(res => {
          this.onActionSuccess(res.data, approved ? 'approved' : 'rejected');
        });
    });

  publish =
    (approved = true) =>
    () => {
      req
        .patch(`/${this.apiType}-rules/${this.ID}`, {
          status: approved ? STATUS.PUBLISHED : STATUS.DRAFT,
          type: this.props.type,
        })
        .then(res => {
          this.onActionSuccess(res.data, approved ? 'published' : 'rejected');
        });
    };

  activate =
    (status, activated = true) =>
    () => {
      req
        .patch(`/${this.apiType}-rules/${this.ID}`, {
          status: status,
          disabled: !activated,
          type: this.props.type,
        })
        .then(res => {
          this.onActionSuccess(
            res.data,
            activated ? 'activated' : 'deactivated',
          );
        });
    };

  delete = () => {
    req.delete(`/${this.apiType}-rules/${this.ID}`).then(() => {
      this.makeToast(this.props.formValues.name, 'deleted');
      this.goBack();
    });
  };

  deleteContent = () => {
    this.props.formReset && this.props.formReset('contentModal');
    this.props.updateDetails({
      content_id: undefined,
      content_type: undefined,
    });
  };

  addAlert = (name, placementType) => {
    this.props.addAlert({
      message: `"${name}" is currently disabled. Please enable the ${placementType} to continue.`,
    });
  };

  render() {
    const { location } = this.props;
    return (
      this.state.ready &&
      this.props.containers && (
        <Details
          status={this.status}
          formValues={this.props.formValues}
          onSubmit={this.submit}
          onApprove={this.approve}
          onPublish={this.publish}
          onActivate={this.activate}
          onCancel={this.goBack}
          onDuplicate={this.duplicate}
          onDelete={this.delete}
          title={this.title}
          deleteContent={this.deleteContent}
          addAlert={this.addAlert}
          removeAlert={this.props.removeAlert}
          openContentModal={this.props.openContentModal}
          updateDetails={this.props.updateDetails}
          formChange={this.props.formChange}
          formReset={this.props.formReset}
          formUntouch={this.props.formUntouch}
          type={this.props.type}
          permissions={this.props.permissions}
          access={this.props.access}
          assignees={this.status === 'draft' ? this.props.assignees : []}
          containers={this.props.containers}
          pages={this.props.pages}
          formValid={this.props.valid}
          users={this.props.users}
          isDuplicating={location.pathname.includes('/duplicate')}
          productBook={this.state.productBook}
          applications={this.props.applications}
          ruleTypes={this.props.ruleTypes}
          ruleSubTypeMap={mapIdToKey(this.props.ruleSubTypes || [])}
          platforms={this.props.platforms}
        />
      )
    );
  }
}

export const validate = (values, props) => {
  const errors = {};
  // validation logic
  if (
    !values.start_at ||
    !moment(values.start_at, DATE_ISO_FORMAT, true).isValid()
  ) {
    errors.start_at = `You must enter a valid date (MM/DD/YYYY HH:MM AM/PM).`;
  }
  if (
    !values.end_at ||
    !moment(values.end_at, DATE_ISO_FORMAT, true).isValid()
  ) {
    errors.end_at = `You must enter a valid date (MM/DD/YYYY HH:MM AM/PM).`;
  }
  if (
    values.start_at &&
    values.end_at &&
    moment(values.end_at).isBefore(values.start_at)
  ) {
    errors.end_at =
      'You must enter a publication End date later than the Start date.';
  }
  if (!values.content_id) {
    errors.contentful = 'You must select a container/Contentful linkage.';
  }

  if (ruleTypes.ALERT !== props.type) {
    if (
      [ ruleSubType.targeted, ruleSubType.mass ].includes(
        ruleSubType[values.type],
      ) &&
      values.pages?.includes('account-key') &&
      !getProductPlacements(values).length
    ) {
      errors.contentful = 'Product is required';
    }
    if (!values.pages?.length) {
      errors.contentful = 'Page is required';
    }
  } else if (ruleTypes.ALERT === props.type) {
    // Only Phoenix alerts require page selection
    if (values.application === 'phoenix' && !values.pages?.length) {
      errors.contentful = 'Page is required';
    }
  }
  if (!values.container) {
    errors.contentful = 'Container is required';
  }
  if (values.type === 'targeted' && !values.external_ref) {
    errors.external_ref = 'You must enter a campaign ID.';
  }
  if (values.type === 'offer' && !values.external_ref) {
    errors.external_ref = 'You must enter an offer ID';
  }
  return errors;
};

const mapAlertStateToProps = state => ({
  formValues: state.form.alertDetails.values,
  permissions: state.authenticated.permissions,
  access: state.authenticated.access?.alerts || {},
  type: ruleTypes.ALERT,
  containers: state.containers,
  pages: state.pages,
  applications: state.applications.items || [],
  ruleTypes: state.ruleTypes.items || [],
  ruleSubTypes: state.ruleSubTypes.items || [],
  platforms: state.platforms.items || [],
});

const mapAlertDispatchToProps = dispatch =>
  bindActionCreators(
    {
      populateDetails: populateAlertDetails,
      updateDetails: updateAlertDetails,
      openContentModal,
      addSnackbar,
      addAlert,
      removeAlert,
      getContainers,
      getPages,
      getApplications,
      getRuleTypes,
      getRuleSubTypes,
      getPlatforms,
      formChange: (formName, formField, formValue) =>
        change(formName, formField, formValue),
      formReset: formName => reset(formName),
    },
    dispatch,
  );

@connect(mapAlertStateToProps, mapAlertDispatchToProps)
@reduxForm({
  form: 'alertDetails',
  validate,
  initialValues: {
    platforms: {},
    application: 'nova',
    platforms_targeting: [],
  },
})
export class AlertDetails extends DetailsContainer {}

const mapCampaignStateToProps = state => {
  return {
    formValues: state.form.campaignDetails.values,
    permissions: state.authenticated.permissions,
    access: state.authenticated.access?.campaigns || {},
    type: window.location.pathname.includes('/ccau_campaigns')
      ? ruleTypes.CCAU_CAMPAIGN
      : ruleTypes.CAMPAIGN,
    containers: state.containers,
    pages: state.pages,
    users: state.users,
    applications: state.applications.items || [],
    ruleTypes: state.ruleTypes.items || [],
    ruleSubTypes: state.ruleSubTypes.items || [],
    platforms: state.platforms.items || [],
    authenticated: state.authenticated || {},
  };
};

const mapCampaignDispatchToProps = dispatch =>
  bindActionCreators(
    {
      populateDetails: populateCampaignDetails,
      updateDetails: updateCampaignDetails,
      openContentModal,
      addSnackbar,
      addAlert,
      removeAlert,
      getContainers,
      getPages,
      getUsers,
      getApplications,
      getRuleTypes,
      getRuleSubTypes,
      getPlatforms,
      formChange: (formName, formField, formValue) =>
        change(formName, formField, formValue),
      formReset: formName => reset(formName),
      formUntouch: (formName, formField) => untouch(formName, formField),
    },
    dispatch,
  );

@connect(mapCampaignStateToProps, mapCampaignDispatchToProps)
@reduxForm({
  form: 'campaignDetails',
  validate,
  enableReinitialize: true,
  initialValues: {
    platforms: {},
    ownershipRelationship: 'or',
    advancedTargetingRelationship: 'or',
    platforms_targeting: [],
    dismissable_flag: true,
    mass_targeting: {
      device_lock: 'none',
    },
  },
})
export class CampaignDetails extends DetailsContainer {}
