// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Campaign Details - Create a New Campaign (undefined status) should allow product page placement to be edited 1`] = `
<body
  style="overflow: unset;"
>
  <div>
    <div
      class="admin-details__assignment-popup"
    />
    <div
      class="admin-details"
    >
      <form>
         
        <div
          class="admin-details__action-bar"
        >
          <h1
            class="TextIntroductionstyle__Text-canvas-core__sc-rmax1m-0 bvdIjD TextIntroduction__text admin-details__header"
            color="black"
          >
            Create a New Campaign
          </h1>
          <div
            class="admin-details__action-buttons"
          />
        </div>
        <div
          class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 YKZtT Card__container admin-details__card"
          type="floatLow"
        >
          <div>
            <div
              class="assignment"
            >
              <label
                class="assignment__label label"
                for="25"
              >
                Assignee
              </label>
              <div
                class="autosuggest assignment__autosuggest"
              >
                <div
                  class="assignment__no-assignees"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 krCJZm SvgIcon__icon"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="24"
                    viewBox="0 0 30 30"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M16.4655 15.5344C20.0649 15.5344 22.9827 12.6166 22.9827 9.01719C22.9827 5.41781 20.0649 2.49994 16.4655 2.49994C12.8661 2.49994 9.94824 5.41781 9.94824 9.01719C9.94824 12.6166 12.8661 15.5344 16.4655 15.5344Z"
                      fill="none"
                      fill-rule="evenodd"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M29.5 29.4999V26.3964C29.5 22.9684 26.4779 20.1895 22.75 20.1895H9.25001C5.52208 20.1895 2.5 22.9684 2.5 26.3964V29.4999"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <span>
                    Unassigned
                  </span>
                </div>
              </div>
            </div>
          </div>
          <h2
            class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP admin-details__sub-header"
            color="black"
            size="21"
          >
            Campaign
             Information
          </h2>
          <div
            class="input-text-field admin-details__field"
          >
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
            >
              <div
                class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
              >
                <label
                  class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                  for="name-input"
                  id="name-label"
                  label="Campaign Title"
                >
                  Campaign Title
                </label>
              </div>
              <input
                aria-describedby=""
                autocomplete="off"
                class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 ddelfF Input__input TextFieldstyle__StyledInput-canvas-core__sc-w9yen5-0 liAfho"
                id="name-input"
                placeholder="Campaign Title"
                type="text"
                value=""
              />
            </div>
          </div>
          <div
            class="input-select-field admin-details__field"
          >
            <div
              class="custom-select-tooltip"
            >
              <span
                class="custom-select-label-tooltip"
              >
                Application
              </span>
            </div>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
            >
              <div
                class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
              >
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                >
                  <label
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                    for="application"
                    label="Application"
                  >
                    Application
                  </label>
                </div>
                <select
                  aria-describedby=""
                  class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                  id="application"
                  name="application"
                >
                  <option
                    class="Selector__placeholder"
                    disabled=""
                    value=""
                  >
                    Select application…
                  </option>
                  <option
                    value="nova"
                  >
                    Nova Mobile
                  </option>
                  <option
                    value="phoenix"
                  >
                    Phoenix
                  </option>
                </select>
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                  color="currentColor"
                  focusable="false"
                  role="presentation"
                  size="18"
                  viewBox="0 0 30 30"
                >
                  <path
                    d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
        <div
          class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 YKZtT Card__container admin-details__card"
          type="floatLow"
        >
          <h2
            class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP admin-details__sub-header"
            color="black"
            size="21"
          >
            Campaign Type
          </h2>
          <fieldset
            class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 InputGroup__fieldset"
            id="campaign-type-radio-group-inputgroup"
            name="type"
          >
            <legend
              class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 hlGdcC"
            >
              
            </legend>
            <div
              class="admin-details__radio-container"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container"
              >
                <label
                  class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 LChGv RadioButton__label"
                  for="targeted"
                >
                  <input
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                    id="targeted"
                    name="type"
                    type="radio"
                    value=""
                  />
                  <span
                    class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                  >
                    <span
                      class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                    />
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="18"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        cx="12"
                        cy="12"
                        r="12"
                        stroke="none"
                      />
                    </svg>
                  </span>
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                  >
                    <div
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                    >
                      Targeted Campaign
                    </div>
                  </div>
                </label>
              </div>
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container"
              >
                <label
                  class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 LChGv RadioButton__label"
                  for="mass"
                >
                  <input
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                    id="mass"
                    name="type"
                    type="radio"
                    value=""
                  />
                  <span
                    class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                  >
                    <span
                      class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                    />
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="18"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        cx="12"
                        cy="12"
                        r="12"
                        stroke="none"
                      />
                    </svg>
                  </span>
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                  >
                    <div
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                    >
                      Mass Campaign
                    </div>
                  </div>
                </label>
              </div>
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container"
              >
                <label
                  class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 LChGv RadioButton__label"
                  for="message"
                >
                  <input
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                    id="message"
                    name="type"
                    type="radio"
                    value=""
                  />
                  <span
                    class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                  >
                    <span
                      class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                    />
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="18"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        cx="12"
                        cy="12"
                        r="12"
                        stroke="none"
                      />
                    </svg>
                  </span>
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                  >
                    <div
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                    >
                      Mass Message
                    </div>
                  </div>
                </label>
              </div>
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container"
              >
                <label
                  class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 LChGv RadioButton__label"
                  for="offer"
                >
                  <input
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                    id="offer"
                    name="type"
                    type="radio"
                    value=""
                  />
                  <span
                    class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                  >
                    <span
                      class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                    />
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="18"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        cx="12"
                        cy="12"
                        r="12"
                        stroke="none"
                      />
                    </svg>
                  </span>
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                  >
                    <div
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                    >
                      Offer Campaign
                    </div>
                  </div>
                </label>
              </div>
            </div>
          </fieldset>
          <div
            class="admin-details__no-legend"
          >
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
            >
              <div
                class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
              >
                <label
                  class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                  for="urgent"
                >
                  <input
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                    id="urgent"
                    type="checkbox"
                    value=""
                  />
                  <span
                    class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="12"
                      viewBox="0 0 15 2"
                    >
                      <path
                        d="M1.5 1H13.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                      />
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="12"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                        stroke="none"
                      />
                    </svg>
                  </span>
                  <div
                    class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                  >
                    Urgent
                  </div>
                </label>
              </div>
            </div>
          </div>
          <div
            class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
          >
            <div
              class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
            >
              <label
                class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                for="dismissable_flag"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                  id="dismissable_flag"
                  type="checkbox"
                  value=""
                />
                <span
                  class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="12"
                    viewBox="0 0 15 2"
                  >
                    <path
                      d="M1.5 1H13.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                    />
                  </svg>
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="12"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                      stroke="none"
                    />
                  </svg>
                </span>
                <div
                  class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                >
                  Dismissible (N/A for Inbox Updates)
                </div>
              </label>
            </div>
          </div>
        </div>
        <div
          class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 YKZtT Card__container admin-details__card"
          type="floatLow"
        >
          <h2
            class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP admin-details__sub-header"
            color="black"
            size="21"
          >
            Target by 2SV status
          </h2>
          <fieldset
            class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 iUkhXD InputGroup__fieldset admin-details__field admin-details__field_2sv_options"
            id="campaign-enrollment-status-radio-group-inputgroup"
          >
            <legend
              class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 fsDDjX"
            >
              2SV enrollment status
              <span
                aria-hidden="false"
                class="Labelstyle__SecondaryLabel-canvas-core__sc-pnw8rp-1 gFzVHV"
              >
                (Optional)
              </span>
              <div
                class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                id="tooltip-container-campaign-enrollment-status-tooltip"
              >
                <div
                  class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                >
                  <button
                    aria-label="Info, Enrollment status"
                    class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
                    id="desktop-icon-campaign-enrollment-status-tooltip"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btSiGf"
                      color="black"
                      focusable="false"
                      role="presentation"
                      size="18"
                      viewBox="0 0 30 30"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                        fill="none"
                        fill-rule="evenodd"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M15 20.9062V14.1562"
                        fill="none"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <circle
                        cx="14.9998"
                        cy="9.1309"
                        r="0.7"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </legend>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
            >
              <div
                class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
              >
                <label
                  class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                  for="mass_targeting.enrollment_status.REGISTERED"
                >
                  <input
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                    id="mass_targeting.enrollment_status.REGISTERED"
                    type="checkbox"
                    value=""
                  />
                  <span
                    class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="12"
                      viewBox="0 0 15 2"
                    >
                      <path
                        d="M1.5 1H13.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                      />
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="12"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                        stroke="none"
                      />
                    </svg>
                  </span>
                  <div
                    class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                  >
                    Registered users
                  </div>
                </label>
              </div>
            </div>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
            >
              <div
                class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
              >
                <label
                  class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                  for="mass_targeting.enrollment_status.ENROLL_REQUIRED"
                >
                  <input
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                    id="mass_targeting.enrollment_status.ENROLL_REQUIRED"
                    type="checkbox"
                    value=""
                  />
                  <span
                    class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="12"
                      viewBox="0 0 15 2"
                    >
                      <path
                        d="M1.5 1H13.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                      />
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="12"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                        stroke="none"
                      />
                    </svg>
                  </span>
                  <div
                    class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                  >
                    Registration required
                  </div>
                </label>
              </div>
            </div>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
            >
              <div
                class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
              >
                <label
                  class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                  for="mass_targeting.enrollment_status.NEW"
                >
                  <input
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                    id="mass_targeting.enrollment_status.NEW"
                    type="checkbox"
                    value=""
                  />
                  <span
                    class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="12"
                      viewBox="0 0 15 2"
                    >
                      <path
                        d="M1.5 1H13.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                      />
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="12"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                        stroke="none"
                      />
                    </svg>
                  </span>
                  <div
                    class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                  >
                    New users
                  </div>
                </label>
              </div>
            </div>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
            >
              <div
                class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
              >
                <label
                  class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                  for="mass_targeting.enrollment_status.null"
                >
                  <input
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                    id="mass_targeting.enrollment_status.null"
                    type="checkbox"
                    value=""
                  />
                  <span
                    class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="12"
                      viewBox="0 0 15 2"
                    >
                      <path
                        d="M1.5 1H13.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                      />
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="12"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                        stroke="none"
                      />
                    </svg>
                  </span>
                  <div
                    class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                  >
                    Unregistered users
                  </div>
                </label>
              </div>
            </div>
          </fieldset>
          <fieldset
            class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 iUkhXD InputGroup__fieldset"
            id="campaign-lock-status-radio-group-inputgroup"
            name="mass_targeting.device_lock"
          >
            <legend
              class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 fsDDjX"
            >
              Device lock enable status
            </legend>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container"
            >
              <label
                class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 LChGv RadioButton__label"
                for="none"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                  id="none"
                  name="mass_targeting.device_lock"
                  type="radio"
                  value=""
                />
                <span
                  class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                >
                  <span
                    class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                  />
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      cx="12"
                      cy="12"
                      r="12"
                      stroke="none"
                    />
                  </svg>
                </span>
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                >
                  <div
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                  >
                    None
                  </div>
                </div>
              </label>
            </div>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container"
            >
              <label
                class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 LChGv RadioButton__label"
                for="on"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                  id="on"
                  name="mass_targeting.device_lock"
                  type="radio"
                  value=""
                />
                <span
                  class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                >
                  <span
                    class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                  />
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      cx="12"
                      cy="12"
                      r="12"
                      stroke="none"
                    />
                  </svg>
                </span>
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                >
                  <div
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                  >
                    On
                  </div>
                </div>
              </label>
            </div>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container"
            >
              <label
                class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 LChGv RadioButton__label"
                for="off"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                  id="off"
                  name="mass_targeting.device_lock"
                  type="radio"
                  value=""
                />
                <span
                  class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                >
                  <span
                    class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                  />
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      cx="12"
                      cy="12"
                      r="12"
                      stroke="none"
                    />
                  </svg>
                </span>
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                >
                  <div
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                  >
                    Off
                  </div>
                </div>
              </label>
            </div>
          </fieldset>
        </div>
        <div
          class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 YKZtT Card__container advanced-targeting-section"
          type="floatLow"
        >
          <h2
            class="advanced-targeting-section__heading heading"
          >
            Advanced targeting
          </h2>
          <h3
            class="TextSubtitlestyle__Text-canvas-core__sc-1l85m0x-0 gPYxBg TextSubtitle__text advanced-targeting-section__subtitle"
            color="black"
            type="2"
          >
            Target by
             
            <span
              class="advanced-targeting-section__optional-text"
            >
              (optional)
            </span>
            <div
              class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
              id="tooltip-container-target-by-tooltip"
            >
              <div
                class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
              >
                <button
                  aria-label="Info, Target by"
                  class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
                  id="desktop-icon-target-by-tooltip"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btSiGf"
                    color="black"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 30 30"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                      fill="none"
                      fill-rule="evenodd"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M15 20.9062V14.1562"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <circle
                      cx="14.9998"
                      cy="9.1309"
                      r="0.7"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </h3>
          <div
            class="advanced-targeting"
          >
            <div
              class="advanced-targeting__search-container"
            >
              <svg
                aria-hidden="true"
                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon advanced-targeting__search-icon"
                color="currentColor"
                focusable="false"
                role="presentation"
                size="18"
                viewBox="0 0 30 30"
              >
                <path
                  d="M20.2473 20.247L28.5001 28.4999"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  clip-rule="evenodd"
                  d="M23.121 12.3118C23.121 6.34166 18.2806 1.4999 12.3105 1.4999C6.3404 1.4999 1.5 6.34166 1.5 12.3118C1.5 18.2805 6.3404 23.1209 12.3105 23.1209C18.2806 23.1209 23.121 18.2805 23.121 12.3118Z"
                  fill="none"
                  fill-rule="evenodd"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <input
                class="advanced-targeting__search-input"
                placeholder="Search"
                value=""
              />
            </div>
            <div
              class="advanced-targeting__label-bar"
            >
              <button
                aria-label="Expand all Categories"
                class="advanced-targeting__expand-button"
                type="button"
              >
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 hsxYZC SvgIcon__icon"
                  color="currentColor"
                  focusable="false"
                  role="presentation"
                  size="14"
                  viewBox="0 0 30 30"
                >
                  <path
                    d="M15.0001 2.27197L15.0001 27.7278"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M27.728 14.9999H2.27213"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
              <div
                class="advanced-targeting__relationship-label advanced-targeting__relationship-label--label-bar"
              >
                Categories
              </div>
              <div
                class="advanced-targeting__relationship-labels"
              >
                <div
                  class="advanced-targeting__relationship-label advanced-targeting__relationship-label--label-bar"
                >
                  Include
                </div>
                <div
                  class="advanced-targeting__relationship-label advanced-targeting__relationship-label--label-bar"
                >
                  Exclude
                </div>
              </div>
            </div>
            <div
              class="advanced-targeting__dropdown-container"
            >
              <ul
                class="advanced-targeting__listing"
              >
                <li
                  class="targeting-item"
                >
                  <div
                    class="targeting-item__hover-item"
                  >
                    <div
                      class="targeting-item__content-container"
                    >
                      <div
                        class="targeting-item__content targeting-item__content--child"
                      >
                        <div
                          class="targeting-item__item-information"
                        >
                          <label
                            class="targeting-item__label"
                          >
                            Retail
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="targeting-item__checkbox-container"
                  >
                    <div
                      class="targeting-checkbox targeting-item__checkbox targeting-item__checkbox--include"
                    >
                      <input
                        aria-label="Include the Retail item"
                        class="targeting-checkbox__input"
                        type="checkbox"
                      />
                      <span
                        class="targeting-checkbox__control"
                      />
                    </div>
                    <div
                      class="targeting-checkbox targeting-item__checkbox targeting-item__checkbox--exclude"
                    >
                      <input
                        aria-label="Exclude the Retail item"
                        class="targeting-checkbox__input"
                        type="checkbox"
                      />
                      <span
                        class="targeting-checkbox__control"
                      />
                    </div>
                  </div>
                </li>
                <li
                  class="targeting-item"
                >
                  <button
                    aria-label="Business collapsed"
                    class="targeting-item__hover-item targeting-item__hover-item--group"
                    type="button"
                  >
                    <div
                      class="targeting-item__content-container"
                    >
                      <div
                        class="targeting-item__content"
                      >
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon targeting-item__chevron"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="18"
                          viewBox="0 0 30 30"
                        >
                          <path
                            d="M8.4375 1.87491L21.5625 14.9999L8.4375 28.1249"
                            fill="none"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <div
                          class="targeting-item__item-information"
                        >
                          <label
                            class="targeting-item__label targeting-item__label--group"
                          >
                            Business
                          </label>
                        </div>
                      </div>
                    </div>
                  </button>
                  <div
                    class="targeting-item__checkbox-container"
                  >
                    <div
                      class="targeting-checkbox targeting-item__checkbox targeting-item__checkbox--include"
                    >
                      <input
                        aria-label="Include all Business items"
                        class="targeting-checkbox__input"
                        type="checkbox"
                      />
                      <span
                        class="targeting-checkbox__control"
                      />
                    </div>
                    <div
                      class="targeting-checkbox targeting-item__checkbox targeting-item__checkbox--exclude"
                    >
                      <input
                        aria-label="Exclude all Business items"
                        class="targeting-checkbox__input"
                        type="checkbox"
                      />
                      <span
                        class="targeting-checkbox__control"
                      />
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <div
              class="advanced-targeting__selection-container"
            >
              <div
                class="advanced-targeting__preview-section"
              >
                <div
                  class="advanced-targeting__relationship-group"
                >
                  <h3
                    class="TextSubtitlestyle__Text-canvas-core__sc-1l85m0x-0 boDPwx TextSubtitle__text advanced-targeting__relationship-label"
                    color="black"
                    type="1"
                  >
                    Include:
                    <div
                      class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                      id="tooltip-container-include-tooltip"
                    >
                      <div
                        class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                      >
                        <button
                          aria-label="Info, Include"
                          class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
                          id="desktop-icon-include-tooltip"
                          type="button"
                        >
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btSiGf"
                            color="black"
                            focusable="false"
                            role="presentation"
                            size="18"
                            viewBox="0 0 30 30"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                              fill="none"
                              fill-rule="evenodd"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              d="M15 20.9062V14.1562"
                              fill="none"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <circle
                              cx="14.9998"
                              cy="9.1309"
                              r="0.7"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </h3>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container advanced-targeting__radio"
                  >
                    <label
                      class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 hJXOQg RadioButton__label"
                      disabled=""
                      for="include-any"
                    >
                      <input
                        checked=""
                        class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 kgbyfK Input__input Input__input--disabled RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                        disabled=""
                        id="include-any"
                        name="include"
                        type="radio"
                        value=""
                      />
                      <span
                        class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                      >
                        <span
                          class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                        />
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="18"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            cx="12"
                            cy="12"
                            r="12"
                            stroke="none"
                          />
                        </svg>
                      </span>
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                      >
                        <div
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                        >
                          Any
                        </div>
                      </div>
                    </label>
                  </div>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container advanced-targeting__radio"
                  >
                    <label
                      class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 hJXOQg RadioButton__label"
                      disabled=""
                      for="include-all"
                    >
                      <input
                        class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 kgbyfK Input__input Input__input--disabled RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                        disabled=""
                        id="include-all"
                        name="include"
                        type="radio"
                        value=""
                      />
                      <span
                        class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                      >
                        <span
                          class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                        />
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="18"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            cx="12"
                            cy="12"
                            r="12"
                            stroke="none"
                          />
                        </svg>
                      </span>
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                      >
                        <div
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                        >
                          All
                        </div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
              <div
                class="advanced-targeting__preview-section advanced-targeting__preview-section--exclude"
              >
                <div
                  class="advanced-targeting__relationship-group"
                >
                  <h3
                    class="TextSubtitlestyle__Text-canvas-core__sc-1l85m0x-0 boDPwx TextSubtitle__text advanced-targeting__relationship-label"
                    color="black"
                    type="1"
                  >
                    Exclude:
                    <div
                      class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                      id="tooltip-container-exclude-tooltip"
                    >
                      <div
                        class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                      >
                        <button
                          aria-label="Info, Exclude"
                          class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
                          id="desktop-icon-exclude-tooltip"
                          type="button"
                        >
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btSiGf"
                            color="black"
                            focusable="false"
                            role="presentation"
                            size="18"
                            viewBox="0 0 30 30"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                              fill="none"
                              fill-rule="evenodd"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              d="M15 20.9062V14.1562"
                              fill="none"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <circle
                              cx="14.9998"
                              cy="9.1309"
                              r="0.7"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </h3>
                  <div
                    class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container advanced-targeting__radio"
                  >
                    <label
                      class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 hJXOQg RadioButton__label"
                      disabled=""
                      for="exclude-any"
                    >
                      <input
                        checked=""
                        class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 kgbyfK Input__input Input__input--disabled RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                        disabled=""
                        id="exclude-any"
                        name="exclude"
                        type="radio"
                        value=""
                      />
                      <span
                        class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                      >
                        <span
                          class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                        />
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="18"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            cx="12"
                            cy="12"
                            r="12"
                            stroke="none"
                          />
                        </svg>
                      </span>
                      <div
                        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                      >
                        <div
                          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                        >
                          Any
                        </div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
              <div
                class="advanced-targeting__no-items"
              >
                No items selected
              </div>
              <div
                class="advanced-targeting__no-items advanced-targeting__no-items--exclude"
              >
                No items selected
              </div>
            </div>
          </div>
        </div>
        <div
          class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 YKZtT Card__container admin-details__card"
          type="floatLow"
        >
          <h2
            class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP admin-details__sub-header"
            color="black"
            size="21"
          >
            Target by Scene Points
          </h2>
          <div
            class="Marginstyle__Wrapper-canvas-core__sc-dm8riu-0 SfIA Margin__container"
          >
            <fieldset
              class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 iUkhXD InputGroup__fieldset"
              id="target-scene-points-inputgroup"
            >
              <legend
                class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 fsDDjX"
              >
                Target scene points
              </legend>
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container"
              >
                <label
                  class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 LChGv RadioButton__label"
                  for="target-scene-points-yes"
                >
                  <input
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                    id="target-scene-points-yes"
                    name="target-scene-points-yes"
                    type="radio"
                    value="yes"
                  />
                  <span
                    class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                  >
                    <span
                      class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                    />
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="18"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        cx="12"
                        cy="12"
                        r="12"
                        stroke="none"
                      />
                    </svg>
                  </span>
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                  >
                    <div
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                    >
                      Yes
                    </div>
                  </div>
                </label>
              </div>
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container"
              >
                <label
                  class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 LChGv RadioButton__label"
                  for="target-scene-points-no"
                >
                  <input
                    checked=""
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                    id="target-scene-points-no"
                    name="target-scene-points-no"
                    type="radio"
                    value="no"
                  />
                  <span
                    class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                  >
                    <span
                      class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                    />
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="18"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        cx="12"
                        cy="12"
                        r="12"
                        stroke="none"
                      />
                    </svg>
                  </span>
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                  >
                    <div
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                    >
                      No
                    </div>
                  </div>
                </label>
              </div>
            </fieldset>
          </div>
          <div
            class="admin-details__scene-fields"
          >
            <div
              class="input-select-field admin-details__scene-field"
            >
              <div
                class="custom-select-tooltip"
              >
                <span
                  class="custom-select-label-tooltip"
                >
                  Targeting criteria
                </span>
              </div>
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
              >
                <div
                  class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                >
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                  >
                    <label
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                      for="mass_targeting.by_scene_points.targeting_criteria"
                      label="Targeting criteria"
                    >
                      Targeting criteria
                    </label>
                  </div>
                  <select
                    aria-describedby=""
                    class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                    id="mass_targeting.by_scene_points.targeting_criteria"
                    name="mass_targeting.by_scene_points.targeting_criteria"
                  >
                    <option
                      class="Selector__placeholder"
                      disabled=""
                      value=""
                    >
                      Select operator
                    </option>
                    <option
                      value="equal"
                    >
                      Equal to (=)
                    </option>
                    <option
                      value="greater"
                    >
                      Greater than (&gt;)
                    </option>
                    <option
                      value="greaterEqual"
                    >
                      Greater than or equal to (&gt;=)
                    </option>
                    <option
                      value="less"
                    >
                      Less than (&lt;)
                    </option>
                    <option
                      value="lessEqual"
                    >
                      Less than or equal to (&lt;=)
                    </option>
                    <option
                      value="range"
                    >
                      Range
                    </option>
                  </select>
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </div>
            </div>
            <div
              class="input-text-field admin-details__scene-field"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
              >
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                >
                  <label
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                    for="mass_targeting.by_scene_points.points-input"
                    id="mass_targeting.by_scene_points.points-label"
                    label="Amount"
                  >
                    Amount
                  </label>
                </div>
                <input
                  aria-describedby=""
                  autocomplete="off"
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 ddelfF Input__input TextFieldstyle__StyledInput-canvas-core__sc-w9yen5-0 liAfho"
                  id="mass_targeting.by_scene_points.points-input"
                  placeholder="Enter amount"
                  type="text"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
        <div
          class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 YKZtT Card__container admin-details__card"
          type="floatLow"
        >
          <h2
            class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP admin-details__sub-header"
            color="black"
            size="21"
          >
            Targeting Dimension
          </h2>
          <div
            class="admin-details__date-fields"
          >
            <div
              class="rdt input-date-field admin-details__date-field"
            >
              <div>
                <div
                  class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input form-control"
                >
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                  >
                    <label
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                      for="start_at-input"
                      id="start_at-label"
                      label="Start date"
                    >
                      Start date
                    </label>
                  </div>
                  <input
                    aria-describedby=""
                    autocomplete="off"
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 ddelfF Input__input TextFieldstyle__StyledInput-canvas-core__sc-w9yen5-0 liAfho form-control"
                    id="start_at-input"
                    placeholder="MM/DD/YYYY HH:MM AM/PM"
                    required=""
                    type="text"
                    value=""
                  />
                </div>
              </div>
              <div
                class="rdtPicker"
              >
                <div
                  class="rdtDays"
                >
                  <table>
                    <thead>
                      <tr>
                        <th
                          class="rdtPrev"
                        >
                          <span>
                            ‹
                          </span>
                        </th>
                        <th
                          class="rdtSwitch"
                          colspan="5"
                          data-value="2"
                        >
                          March 2022
                        </th>
                        <th
                          class="rdtNext"
                        >
                          <span>
                            ›
                          </span>
                        </th>
                      </tr>
                      <tr>
                        <th
                          class="dow"
                        >
                          Su
                        </th>
                        <th
                          class="dow"
                        >
                          Mo
                        </th>
                        <th
                          class="dow"
                        >
                          Tu
                        </th>
                        <th
                          class="dow"
                        >
                          We
                        </th>
                        <th
                          class="dow"
                        >
                          Th
                        </th>
                        <th
                          class="dow"
                        >
                          Fr
                        </th>
                        <th
                          class="dow"
                        >
                          Sa
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td
                          class="rdtDay rdtOld"
                          data-value="27"
                        >
                          27
                        </td>
                        <td
                          class="rdtDay rdtOld"
                          data-value="28"
                        >
                          28
                        </td>
                        <td
                          class="rdtDay"
                          data-value="1"
                        >
                          1
                        </td>
                        <td
                          class="rdtDay"
                          data-value="2"
                        >
                          2
                        </td>
                        <td
                          class="rdtDay"
                          data-value="3"
                        >
                          3
                        </td>
                        <td
                          class="rdtDay"
                          data-value="4"
                        >
                          4
                        </td>
                        <td
                          class="rdtDay"
                          data-value="5"
                        >
                          5
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="rdtDay"
                          data-value="6"
                        >
                          6
                        </td>
                        <td
                          class="rdtDay"
                          data-value="7"
                        >
                          7
                        </td>
                        <td
                          class="rdtDay"
                          data-value="8"
                        >
                          8
                        </td>
                        <td
                          class="rdtDay"
                          data-value="9"
                        >
                          9
                        </td>
                        <td
                          class="rdtDay"
                          data-value="10"
                        >
                          10
                        </td>
                        <td
                          class="rdtDay"
                          data-value="11"
                        >
                          11
                        </td>
                        <td
                          class="rdtDay"
                          data-value="12"
                        >
                          12
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="rdtDay"
                          data-value="13"
                        >
                          13
                        </td>
                        <td
                          class="rdtDay"
                          data-value="14"
                        >
                          14
                        </td>
                        <td
                          class="rdtDay"
                          data-value="15"
                        >
                          15
                        </td>
                        <td
                          class="rdtDay"
                          data-value="16"
                        >
                          16
                        </td>
                        <td
                          class="rdtDay"
                          data-value="17"
                        >
                          17
                        </td>
                        <td
                          class="rdtDay"
                          data-value="18"
                        >
                          18
                        </td>
                        <td
                          class="rdtDay"
                          data-value="19"
                        >
                          19
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="rdtDay"
                          data-value="20"
                        >
                          20
                        </td>
                        <td
                          class="rdtDay"
                          data-value="21"
                        >
                          21
                        </td>
                        <td
                          class="rdtDay"
                          data-value="22"
                        >
                          22
                        </td>
                        <td
                          class="rdtDay rdtToday"
                          data-value="23"
                        >
                          23
                        </td>
                        <td
                          class="rdtDay"
                          data-value="24"
                        >
                          24
                        </td>
                        <td
                          class="rdtDay"
                          data-value="25"
                        >
                          25
                        </td>
                        <td
                          class="rdtDay"
                          data-value="26"
                        >
                          26
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="rdtDay"
                          data-value="27"
                        >
                          27
                        </td>
                        <td
                          class="rdtDay"
                          data-value="28"
                        >
                          28
                        </td>
                        <td
                          class="rdtDay"
                          data-value="29"
                        >
                          29
                        </td>
                        <td
                          class="rdtDay"
                          data-value="30"
                        >
                          30
                        </td>
                        <td
                          class="rdtDay"
                          data-value="31"
                        >
                          31
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="1"
                        >
                          1
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="2"
                        >
                          2
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="rdtDay rdtNew"
                          data-value="3"
                        >
                          3
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="4"
                        >
                          4
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="5"
                        >
                          5
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="6"
                        >
                          6
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="7"
                        >
                          7
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="8"
                        >
                          8
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="9"
                        >
                          9
                        </td>
                      </tr>
                    </tbody>
                    <tfoot>
                      <tr>
                        <td
                          class="rdtTimeToggle"
                          colspan="7"
                        >
                          12:00 AM
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </div>
            <div
              class="rdt input-date-field admin-details__date-field"
            >
              <div>
                <div
                  class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input form-control"
                >
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                  >
                    <label
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                      for="end_at-input"
                      id="end_at-label"
                      label="End date"
                    >
                      End date
                    </label>
                  </div>
                  <input
                    aria-describedby=""
                    autocomplete="off"
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 ddelfF Input__input TextFieldstyle__StyledInput-canvas-core__sc-w9yen5-0 liAfho form-control"
                    id="end_at-input"
                    placeholder="MM/DD/YYYY HH:MM AM/PM"
                    required=""
                    type="text"
                    value=""
                  />
                </div>
              </div>
              <div
                class="rdtPicker"
              >
                <div
                  class="rdtDays"
                >
                  <table>
                    <thead>
                      <tr>
                        <th
                          class="rdtPrev"
                        >
                          <span>
                            ‹
                          </span>
                        </th>
                        <th
                          class="rdtSwitch"
                          colspan="5"
                          data-value="2"
                        >
                          March 2022
                        </th>
                        <th
                          class="rdtNext"
                        >
                          <span>
                            ›
                          </span>
                        </th>
                      </tr>
                      <tr>
                        <th
                          class="dow"
                        >
                          Su
                        </th>
                        <th
                          class="dow"
                        >
                          Mo
                        </th>
                        <th
                          class="dow"
                        >
                          Tu
                        </th>
                        <th
                          class="dow"
                        >
                          We
                        </th>
                        <th
                          class="dow"
                        >
                          Th
                        </th>
                        <th
                          class="dow"
                        >
                          Fr
                        </th>
                        <th
                          class="dow"
                        >
                          Sa
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td
                          class="rdtDay rdtOld"
                          data-value="27"
                        >
                          27
                        </td>
                        <td
                          class="rdtDay rdtOld"
                          data-value="28"
                        >
                          28
                        </td>
                        <td
                          class="rdtDay"
                          data-value="1"
                        >
                          1
                        </td>
                        <td
                          class="rdtDay"
                          data-value="2"
                        >
                          2
                        </td>
                        <td
                          class="rdtDay"
                          data-value="3"
                        >
                          3
                        </td>
                        <td
                          class="rdtDay"
                          data-value="4"
                        >
                          4
                        </td>
                        <td
                          class="rdtDay"
                          data-value="5"
                        >
                          5
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="rdtDay"
                          data-value="6"
                        >
                          6
                        </td>
                        <td
                          class="rdtDay"
                          data-value="7"
                        >
                          7
                        </td>
                        <td
                          class="rdtDay"
                          data-value="8"
                        >
                          8
                        </td>
                        <td
                          class="rdtDay"
                          data-value="9"
                        >
                          9
                        </td>
                        <td
                          class="rdtDay"
                          data-value="10"
                        >
                          10
                        </td>
                        <td
                          class="rdtDay"
                          data-value="11"
                        >
                          11
                        </td>
                        <td
                          class="rdtDay"
                          data-value="12"
                        >
                          12
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="rdtDay"
                          data-value="13"
                        >
                          13
                        </td>
                        <td
                          class="rdtDay"
                          data-value="14"
                        >
                          14
                        </td>
                        <td
                          class="rdtDay"
                          data-value="15"
                        >
                          15
                        </td>
                        <td
                          class="rdtDay"
                          data-value="16"
                        >
                          16
                        </td>
                        <td
                          class="rdtDay"
                          data-value="17"
                        >
                          17
                        </td>
                        <td
                          class="rdtDay"
                          data-value="18"
                        >
                          18
                        </td>
                        <td
                          class="rdtDay"
                          data-value="19"
                        >
                          19
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="rdtDay"
                          data-value="20"
                        >
                          20
                        </td>
                        <td
                          class="rdtDay"
                          data-value="21"
                        >
                          21
                        </td>
                        <td
                          class="rdtDay"
                          data-value="22"
                        >
                          22
                        </td>
                        <td
                          class="rdtDay rdtToday"
                          data-value="23"
                        >
                          23
                        </td>
                        <td
                          class="rdtDay"
                          data-value="24"
                        >
                          24
                        </td>
                        <td
                          class="rdtDay"
                          data-value="25"
                        >
                          25
                        </td>
                        <td
                          class="rdtDay"
                          data-value="26"
                        >
                          26
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="rdtDay"
                          data-value="27"
                        >
                          27
                        </td>
                        <td
                          class="rdtDay"
                          data-value="28"
                        >
                          28
                        </td>
                        <td
                          class="rdtDay"
                          data-value="29"
                        >
                          29
                        </td>
                        <td
                          class="rdtDay"
                          data-value="30"
                        >
                          30
                        </td>
                        <td
                          class="rdtDay"
                          data-value="31"
                        >
                          31
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="1"
                        >
                          1
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="2"
                        >
                          2
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="rdtDay rdtNew"
                          data-value="3"
                        >
                          3
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="4"
                        >
                          4
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="5"
                        >
                          5
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="6"
                        >
                          6
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="7"
                        >
                          7
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="8"
                        >
                          8
                        </td>
                        <td
                          class="rdtDay rdtNew"
                          data-value="9"
                        >
                          9
                        </td>
                      </tr>
                    </tbody>
                    <tfoot>
                      <tr>
                        <td
                          class="rdtTimeToggle"
                          colspan="7"
                        >
                          12:00 AM
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <fieldset
            class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 InputGroup__fieldset admin-details__platforms"
            id="platforms-group-inputgroup"
          >
            <legend
              class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 hlGdcC"
            >
              Platforms
            </legend>
            <div
              style="display: flex;"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="platforms.ios"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      id="platforms.ios"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    >
                      iOS
                    </div>
                  </label>
                </div>
              </div>
              <button
                class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button version-targeting__button--add"
                color="blue"
              >
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                  color="currentColor"
                  focusable="false"
                  role="presentation"
                  size="16"
                  viewBox="0 0 30 30"
                >
                  <path
                    d="M15.0001 2.27197L15.0001 27.7278"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M27.728 14.9999H2.27213"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                >
                  Add Version Targeting
                </span>
              </button>
            </div>
            <ul
              class="saved-versions__list"
            />
            <div
              style="display: flex;"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container"
              >
                <div
                  class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                >
                  <label
                    class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                    for="platforms.android"
                  >
                    <input
                      class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                      id="platforms.android"
                      type="checkbox"
                      value=""
                    />
                    <span
                      class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 15 2"
                      >
                        <path
                          d="M1.5 1H13.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        />
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="12"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                          stroke="none"
                        />
                      </svg>
                    </span>
                    <div
                      class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                    >
                      Android
                    </div>
                  </label>
                </div>
              </div>
              <button
                class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button version-targeting__button--add"
                color="blue"
              >
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                  color="currentColor"
                  focusable="false"
                  role="presentation"
                  size="16"
                  viewBox="0 0 30 30"
                >
                  <path
                    d="M15.0001 2.27197L15.0001 27.7278"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M27.728 14.9999H2.27213"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                >
                  Add Version Targeting
                </span>
              </button>
            </div>
            <ul
              class="saved-versions__list"
            />
          </fieldset>
          <fieldset
            class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 InputGroup__fieldset admin-details__language"
            id="campaign-language-group-inputgroup"
          >
            <legend
              class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 hlGdcC"
            >
              Languages
            </legend>
          </fieldset>
        </div>
        <div
          class="admin-details__placement"
        >
          <fieldset
            class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 InputGroup__fieldset"
            id="28-inputgroup"
          >
            <legend
              class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 hlGdcC"
            >
              
            </legend>
            <div
              class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 YKZtT Card__container targeting-metadata__card"
              type="floatLow"
            >
              <h2
                class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP targeting-metadata__sub-header"
                color="black"
                size="21"
              >
                Targeting Metadata
              </h2>
              <div
                class="input-select-field targeting-metadata__container"
              >
                <div
                  class="custom-select-tooltip"
                >
                  <span
                    class="custom-select-label-tooltip"
                  >
                    Container
                  </span>
                </div>
                <div
                  class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
                >
                  <div
                    class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                  >
                    <div
                      class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                    >
                      <label
                        class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                        for="container"
                        label="Container"
                      >
                        Container
                      </label>
                    </div>
                    <select
                      aria-describedby=""
                      class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                      id="container"
                      name="container"
                    >
                      <option
                        class="Selector__placeholder"
                        disabled=""
                        value=""
                      >
                        Select container
                      </option>
                      <option
                        value="2"
                      >
                        offers-and-programs
                      </option>
                    </select>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="18"
                      viewBox="0 0 30 30"
                    >
                      <path
                        d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                        fill="none"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <div
                class="product-targeting"
              >
                <div
                  class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input Checkboxstyle__StyledInputContainer-canvas-core__sc-1p7p9fh-0 Checkbox__container product-targeting__checkbox"
                >
                  <div
                    class="Checkboxstyle__CheckboxWrapper-canvas-core__sc-1p7p9fh-1 kquVOm Checkbox__tooltipInput"
                  >
                    <label
                      class="Checkboxstyle__CheckboxLabel-canvas-core__sc-1p7p9fh-3 gfRdtJ Checkbox__label"
                      for="product-targeting__checkbox"
                    >
                      <input
                        checked=""
                        class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input Checkboxstyle__StyledInput-canvas-core__sc-1p7p9fh-5 bDaSaB Checkbox__input"
                        id="product-targeting__checkbox"
                        type="checkbox"
                        value=""
                      />
                      <span
                        class="Checkboxstyle__CheckboxIconWrapper-canvas-core__sc-1p7p9fh-4 xIAWP Checkbox__span"
                      >
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 gvzPYo SvgIcon__icon Checkbox__icon Checkbox__icon--indeterminate"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="12"
                          viewBox="0 0 15 2"
                        >
                          <path
                            d="M1.5 1H13.5"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                          />
                        </svg>
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fBcnDK SvgIcon__icon Checkbox__icon Checkbox__icon--check"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="12"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M13.85 10.667l6.533-5.683A1.727 1.727 0 0 1 22.65 7.59l-6.511 5.663-6.371 5.74c-.7.63-1.775.585-2.418-.103l-6.134-6.559a1.727 1.727 0 0 1 2.522-2.359l4.977 5.321 5.134-4.626z"
                            stroke="none"
                          />
                        </svg>
                      </span>
                      <div
                        class="Checkboxstyle__CheckboxVisibleLabel-canvas-core__sc-1p7p9fh-6 irbPIX Label__label Label__label--checkbox Label__label--inline"
                      >
                        Target selected product account details page
                      </div>
                    </label>
                    <div
                      class="Checkboxstyle__CheckboxTooltipContainer-canvas-core__sc-1p7p9fh-2 eGPKiN Checkbox__tooltipContainer"
                    >
                      <div
                        class="Tooltipstyle__Wrapper-canvas-core__sc-j4598g-0 crSYlP Tooltip__container"
                        id="tooltip-container-product-targeting__checkbox-tooltip"
                      >
                        <div
                          class="DesktopTooltipstyle__Wrapper-canvas-core__sc-1g2dm1c-0 dBdGJD"
                        >
                          <button
                            aria-label="Info, Target account details page"
                            class="TooltipIconstyle__Wrapper-canvas-core__sc-1ct47lk-1 dFjfPS TooltipIcon__button TooltipIcon__button--desktop TooltipIcon__button--bottom"
                            id="desktop-icon-product-targeting__checkbox-tooltip"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eOLEbe SvgIcon__icon TooltipIconstyle__IconTipWrapper-canvas-core__sc-1ct47lk-0 btSiGf"
                              color="black"
                              focusable="false"
                              role="presentation"
                              size="18"
                              viewBox="0 0 30 30"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M28.5 14.9999C28.5 22.4544 22.4545 28.4999 15 28.4999C7.54309 28.4999 1.5 22.4544 1.5 14.9999C1.5 7.54301 7.54309 1.49992 15 1.49992C22.4545 1.49992 28.5 7.54301 28.5 14.9999Z"
                                fill="none"
                                fill-rule="evenodd"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M15 20.9062V14.1562"
                                fill="none"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <circle
                                cx="14.9998"
                                cy="9.1309"
                                r="0.7"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <label
                  class="product-targeting__title"
                  for="targeted-products"
                >
                  Target products
                </label>
                <div
                  class="product-targeting__tags"
                  id="targeted-products"
                >
                  <div
                    class="tag"
                    title="B:DDA:CA Current Account (B:DDA:CA)"
                  >
                    <span
                      class="tag__value"
                    >
                      B:DDA:CA Current Account (B:DDA:CA)
                    </span>
                  </div>
                  <div
                    class="tag"
                    title="B:TFS:SB Tax-Free Savings - BNS (B:TFS:SB)"
                  >
                    <span
                      class="tag__value"
                    >
                      B:TFS:SB Tax-Free Savings - BNS (B:TFS:SB)
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="input-select-field targeting-metadata__pages"
              >
                <div
                  class="autosuggest-v2"
                >
                  <label
                    class="autosuggest-v2__label"
                    for="pages"
                  >
                    Pages
                  </label>
                   (Optional)
                  <div
                    class="autosuggest-v2__input-wrapper autosuggest-v2__input-wrapper--editable"
                  >
                    <div
                      class="tag tag--editing"
                      title="accounts"
                    >
                      <span
                        class="tag__value"
                      >
                        accounts
                      </span>
                      <button
                        aria-label="Delete"
                        class="tag__delete"
                      >
                        ✕
                      </button>
                    </div>
                    <div
                      class="autosuggest-v2__edit-btn"
                      data-testid="edit"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="18"
                        viewBox="0 0 30 30"
                      >
                        <path
                          d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                          fill="none"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="Tablestyle__StyledTable-canvas-core__sc-mfsabp-0 dvntiZ Table"
              >
                <div
                  class="Tablestyle__StyledTableWrapper-canvas-core__sc-mfsabp-1 ikRFBm Table__wrapper"
                  id="table-campaign-content-table"
                >
                  <table
                    class="Tablestyle__StyledDataTable-canvas-core__sc-mfsabp-3 cmNDld Table__dataTable targeting-metadata__content-table"
                  >
                    <caption
                      aria-live="polite"
                      class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iRSezP TableHeadingstyle__StyledHeading-canvas-core__sc-2qo8vm-0 bPEdEe Table__title"
                      color="black"
                      size="21"
                    />
                    <thead
                      class="TableHeadstyle__StyledThead-canvas-core__sc-avmfdv-0 fsSCSr TableHead"
                      role="rowgroup"
                    >
                      <tr
                        class="TableHeadstyle__StyledTr-canvas-core__sc-avmfdv-1 jNOJtZ"
                        role="row"
                      >
                        <th
                          class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 laXIhK"
                          data="[object Object]"
                          role="columnheader"
                          scope="col"
                        >
                          <p
                            class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                            color="black"
                          >
                            Contentful Link
                          </p>
                        </th>
                        <th
                          class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                          data="[object Object]"
                          role="columnheader"
                          scope="col"
                        >
                          <p
                            class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                            color="black"
                          >
                            Content Type
                          </p>
                        </th>
                        <th
                          class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                          data="[object Object]"
                          role="columnheader"
                          scope="col"
                        >
                          <p
                            class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                            color="black"
                          >
                            Preview Link
                          </p>
                        </th>
                        <th
                          class="TableHeadstyle__StyledTh-canvas-core__sc-avmfdv-3 jMdjah"
                          data="[object Object]"
                          role="columnheader"
                          scope="col"
                        >
                          <p
                            class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text TableHeadstyle__StyledText-canvas-core__sc-avmfdv-9 dodZdh"
                            color="black"
                          >
                            Action
                          </p>
                        </th>
                      </tr>
                    </thead>
                    <tbody
                      class="TableBodystyle__StyledTbody-canvas-core__sc-iq7avh-0 ctfvtU TableBody"
                      role="rowgroup"
                    >
                      <tr
                        class="TableBodystyle__StyledTr-canvas-core__sc-iq7avh-1 jGMNii"
                        role="row"
                      >
                        <th
                          class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 eRCLvM"
                          role="rowheader"
                          scope="row"
                        >
                          <a
                            class="Linkstyle__Wrapper-canvas-core__sc-nzeldc-1 hqbNYC targeting-metadata__content-table-link"
                            href="/api/v1/contents/spaces/4szkx38resvm/entries/contents/test-content-id"
                            size="16"
                            target="_blank"
                            to="/api/v1/contents/spaces/4szkx38resvm/entries/contents/test-content-id"
                            type="emphasis"
                          >
                            <span
                              class="Linkstyle__Text-canvas-core__sc-nzeldc-0 ictHEk"
                              type="emphasis"
                            >
                              test-content-id
                            </span>
                          </a>
                        </th>
                        <td
                          class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                          role="cell"
                        >
                          <p
                            class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text TableBodystyle__StyledContent-canvas-core__sc-iq7avh-3 bmWurE"
                            color="black"
                          >
                            targetedCampaign
                          </p>
                        </td>
                        <td
                          class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                          role="cell"
                        >
                          <button
                            class="targeting-metadata__content-table-link"
                          >
                            Preview
                          </button>
                        </td>
                        <td
                          class="TableBodystyle__StyledTd-canvas-core__sc-iq7avh-2 idzVUv"
                          role="cell"
                        >
                          <div
                            class="ActionMenustyle__MenuWrapper-canvas-core__sc-bc19ak-0 kqkdaS targeting-metadata__content-action-menu"
                          >
                            <button
                              aria-expanded="false"
                              aria-haspopup="true"
                              aria-label="Action menu"
                              class="ActionMenustyle__WrapperKebob-canvas-core__sc-bc19ak-1 kvdpEH ActionMenu__KebabButton"
                              data-testid="ActionMenuList-test"
                              id="kebob-button"
                              type="button"
                            >
                              <svg
                                aria-hidden="true"
                                class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 cuYZRS SvgIcon__icon"
                                color="black"
                                focusable="false"
                                role="presentation"
                                size="24"
                                viewBox="0 0 32 32"
                              >
                                <path
                                  clip-rule="evenodd"
                                  d="M13 5.49992C13 3.84392 14.344 2.49992 16 2.49992C17.6575 2.49992 19 3.84392 19 5.49992C19 7.15591 17.6575 8.49991 16 8.49991C14.344 8.49991 13 7.15591 13 5.49992ZM13 26.5C13 24.844 14.344 23.5 16 23.5C17.6575 23.5 19 24.844 19 26.5C19 28.156 17.6575 29.5 16 29.5C14.344 29.5 13 28.156 13 26.5ZM16 12.9999C14.344 12.9999 13 14.3439 13 15.9999C13 17.6559 14.344 18.9999 16 18.9999C17.6575 18.9999 19 17.6559 19 15.9999C19 14.3439 17.6575 12.9999 16 12.9999Z"
                                  fill-rule="evenodd"
                                  stroke="none"
                                />
                              </svg>
                            </button>
                            <div>
                              <ul
                                class="ActionMenuListDesktopstyle__WrapperDesktop-canvas-core__sc-1x08cuj-0 hCExhu ActionMenu__dialog"
                                data-testid="actionmenu-ul"
                              >
                                <li
                                  class="ActionMenuListItemstyle__MenuListItem-canvas-core__sc-41fdr-2 cezwAS"
                                >
                                  <button
                                    class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 eEjFkP TextButton__button ActionMenuListItemstyle__StyledButton-canvas-core__sc-41fdr-0 eMBMPI"
                                    color="black"
                                    type="button"
                                  >
                                    <svg
                                      aria-hidden="true"
                                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                                      color="currentColor"
                                      focusable="false"
                                      role="presentation"
                                      size="16"
                                      viewBox="0 0 30 30"
                                    >
                                      <path
                                        clip-rule="evenodd"
                                        d="M28.5 8.99917L8.99965 28.4999H1.5V21.0007L21.0004 1.49992L28.5 8.99917Z"
                                        fill="none"
                                        fill-rule="evenodd"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                      />
                                      <path
                                        d="M16.1143 6.47101L23.4159 13.7729"
                                        fill="none"
                                        stroke-linecap="square"
                                      />
                                    </svg>
                                    <span
                                      class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                                    >
                                      Edit
                                    </span>
                                  </button>
                                </li>
                                <li
                                  class="ActionMenuListItemstyle__MenuListItem-canvas-core__sc-41fdr-2 cezwAS"
                                >
                                  <button
                                    class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 eEjFkP TextButton__button ActionMenuListItemstyle__StyledButton-canvas-core__sc-41fdr-0 eMBMPI"
                                    color="black"
                                    type="button"
                                  >
                                    <svg
                                      aria-hidden="true"
                                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
                                      color="currentColor"
                                      focusable="false"
                                      role="presentation"
                                      size="16"
                                      viewBox="0 0 30 30"
                                    >
                                      <path
                                        d="M18.8977 13.5471L17.3499 21.9875"
                                        fill="none"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                      />
                                      <path
                                        d="M11.5088 13.5471L13.0567 21.9837"
                                        fill="none"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                      />
                                      <path
                                        d="M9.52832 6.70208V3.37484C9.52832 2.33931 10.3678 1.49984 11.4033 1.49984L17.2502 1.49984H19.3921C20.4276 1.49984 21.2671 2.3393 21.2671 3.37484V6.70208"
                                        fill="none"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                      />
                                      <path
                                        d="M1.72485 6.90275H28.2754"
                                        fill="none"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                      />
                                      <path
                                        d="M26.4994 7.38814C25.4572 13.6422 24.4149 19.8962 23.3727 26.1503C23.1473 27.5031 21.9109 28.5 20.4584 28.5H9.62805C8.15415 28.5 6.90641 27.4743 6.70552 26.0976L3.91528 6.9758"
                                        fill="none"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                      />
                                    </svg>
                                    <span
                                      class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
                                    >
                                      Delete
                                    </span>
                                  </button>
                                </li>
                              </ul>
                            </div>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </fieldset>
        </div>
        <div
          class="admin-details__action-bar"
        >
          <button
            class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button BackButtonstyle__Button-canvas-core__sc-1evm24c-0 fwzMSb NavButton__button--back"
            type="button"
          >
            <span
              class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
              tabindex="-1"
            >
              <span
                class="ButtonCore__icon"
              >
                 
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon"
                  color="currentColor"
                  focusable="false"
                  role="presentation"
                  size="18"
                  viewBox="0 0 30 30"
                >
                  <path
                    d="M1.5 14.9999H28.5"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M12.0652 4.43467L1.5 14.9999L12.0652 25.5651"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                 
              </span>
              <span
                class="ButtonCore__text"
              >
                Cancel
              </span>
            </span>
          </button>
          <div
            class="admin-details__action-buttons"
          >
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary admin-details__action-button"
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Save as Draft
                </span>
              </span>
            </button>
            <button
              class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button PrimaryButtonstyle__StyledPrimaryButtonCore-canvas-core__sc-11pddd2-0 jbDssn Button__button--primary admin-details__action-button"
            >
              <span
                class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                tabindex="-1"
              >
                <span
                  class="ButtonCore__text"
                >
                  Submit for Review
                </span>
              </span>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</body>
`;

exports[`Details Container Snapshot - Create a New Campaign (undefined status) Add enrollmentStatus  1`] = `
{
  "app_version": null,
  "application": "phoenix",
  "container": undefined,
  "content_id": undefined,
  "content_space": "4szkx38resvm",
  "content_type": undefined,
  "end_at": "2018-07-14T00:00:00.000Z",
  "mass_targeting": {
    "enrollment_status": [],
    "languages": [],
  },
  "name": undefined,
  "pages": [
    "test",
  ],
  "platforms": [
    "ios",
    "android",
  ],
  "platforms_targeting": [
    {
      "items": [],
      "platform": "ios",
    },
    {
      "items": [],
      "platform": "android",
    },
  ],
  "start_at": "2018-07-13T00:00:00.000Z",
  "status": undefined,
  "targeting": {
    "products": [],
  },
}
`;

exports[`Details Container Snapshot - Create a New Campaign (undefined status) All Errors 1`] = `
{
  "contentful": "Container is required",
  "end_at": "You must enter a valid date (MM/DD/YYYY HH:MM AM/PM).",
  "start_at": "You must enter a valid date (MM/DD/YYYY HH:MM AM/PM).",
}
`;

exports[`Details Container Snapshot - Create a New Campaign (undefined status) App version validation 1`] = `
{
  "app_version": null,
  "application": "phoenix",
  "container": undefined,
  "content_id": undefined,
  "content_space": "4szkx38resvm",
  "content_type": undefined,
  "end_at": "2018-07-12T00:00:00.000Z",
  "mass_targeting": {
    "enrollment_status": [],
    "languages": [],
  },
  "name": undefined,
  "pages": [
    "test",
  ],
  "platforms": [
    "ios",
    "android",
  ],
  "platforms_targeting": [
    {
      "items": [],
      "platform": "ios",
    },
    {
      "items": [],
      "platform": "android",
    },
  ],
  "start_at": "2018-07-13T00:00:00.000Z",
  "status": undefined,
  "targeting": {
    "products": [],
  },
}
`;

exports[`Details Container Snapshot - Create a New Campaign (undefined status) Container validation 1`] = `
{
  "app_version": null,
  "application": "phoenix",
  "container": undefined,
  "content_id": undefined,
  "content_space": "4szkx38resvm",
  "content_type": undefined,
  "end_at": "2018-07-14T00:00:00.000Z",
  "mass_targeting": {
    "enrollment_status": [],
    "languages": [],
  },
  "name": undefined,
  "pages": [
    "test",
  ],
  "platforms": [
    "ios",
    "android",
  ],
  "platforms_targeting": [
    {
      "items": [],
      "platform": "ios",
    },
    {
      "items": [],
      "platform": "android",
    },
  ],
  "start_at": "2018-07-13T00:00:00.000Z",
  "status": undefined,
  "targeting": {
    "products": [],
  },
}
`;

exports[`Details Container Snapshot - Create a New Campaign (undefined status) No error validation 1`] = `
{
  "app_version": null,
  "application": "phoenix",
  "container": "container",
  "content_id": "test-container",
  "content_space": "4szkx38resvm",
  "content_type": undefined,
  "end_at": "2018-07-14T00:00:00.000Z",
  "mass_targeting": {
    "enrollment_status": [],
    "languages": [],
  },
  "name": "test-name",
  "pages": [
    "test",
  ],
  "platforms": [
    "ios",
    "android",
  ],
  "platforms_targeting": [
    {
      "items": [],
      "platform": "ios",
    },
    {
      "items": [],
      "platform": "android",
    },
  ],
  "start_at": "2018-07-13T00:00:00.000Z",
  "status": undefined,
  "targeting": {
    "products": [],
  },
}
`;

exports[`Details Container Snapshot - Create a New Campaign (undefined status) Snapshot 1`] = `
<Details
  access={
    {
      "containers": {
        "atlantis": {
          "manage": [
            "offers-and-programs",
          ],
          "view": [
            "offers-and-programs",
          ],
        },
        "nova": {
          "manage": [
            "offers-and-programs",
          ],
          "view": [
            "offers-and-programs",
          ],
        },
        "phoenix": {
          "manage": [
            "phoenix-alert",
          ],
          "view": [
            "phoenix-alert",
          ],
        },
        "starburst": {
          "manage": [
            "offers-and-programs",
          ],
          "view": [
            "offers-and-programs",
          ],
        },
      },
      "pages": {
        "atlantis": {
          "manage": [
            "accounts",
            "account-key",
          ],
          "view": [
            "accounts",
            "account-key",
          ],
        },
        "nova": {
          "manage": [
            "accounts",
            "account-key",
          ],
          "view": [
            "accounts",
            "account-key",
          ],
        },
        "phoenix": {
          "manage": [
            "lgn-12345",
          ],
          "view": [
            "lgn-12345",
          ],
        },
        "starburst": {
          "manage": [
            "accounts",
            "account-key",
          ],
          "view": [
            "accounts",
            "account-key",
          ],
        },
      },
      "ruleSubTypes": {
        "atlantis": {
          "manage": [
            "targeted",
            "mass",
            "message",
            "wealth",
          ],
          "view": [
            "targeted",
            "mass",
            "message",
            "wealth",
          ],
        },
        "nova": {
          "manage": [
            "targeted",
            "mass",
            "message",
            "offer",
          ],
          "view": [
            "targeted",
            "mass",
            "message",
            "offer",
          ],
        },
        "phoenix": {
          "manage": [
            "targeted",
            "mass",
          ],
          "view": [
            "targeted",
            "mass",
          ],
        },
        "starburst": {
          "manage": [
            "targeted",
            "mass",
            "message",
            "wealth",
          ],
          "view": [
            "targeted",
            "mass",
            "message",
            "wealth",
          ],
        },
      },
    }
  }
  addAlert={[Function]}
  applications={
    [
      {
        "applicationId": "nova",
        "contentful_space": "4szkx38resvm",
        "id": 1,
        "name": "Nova Mobile",
        "platformIds": [
          2,
          3,
        ],
        "platforms": [
          "iOS",
          "Android",
        ],
        "ruleSubTypeIds": [
          1,
          2,
          3,
          4,
        ],
        "ruleSubTypes": [
          "targeted",
          "mass",
          "message",
          "offer",
        ],
        "ruleTypeIds": [
          1,
          2,
        ],
        "ruleTypes": [
          "alert",
          "campaign",
        ],
        "rule_version": 1,
        "status": true,
      },
      {
        "applicationId": "phoenix",
        "contentful_space": "4szkx38resvm",
        "id": 2,
        "name": "Phoenix",
        "platformIds": [
          1,
        ],
        "platforms": [
          "Web",
        ],
        "ruleSubTypeIds": [
          1,
          2,
        ],
        "ruleSubTypes": [
          "targeted",
          "mass",
        ],
        "ruleTypeIds": [
          1,
          2,
        ],
        "ruleTypes": [
          "alert",
          "campaign",
        ],
        "rule_version": 1,
        "status": true,
      },
    ]
  }
  assignees={[]}
  containers={
    {
      "isLoading": false,
      "items": [
        {
          "application": 1,
          "containerId": "my-activity",
          "content_type": [
            "standingCampaign",
          ],
          "id": 1,
          "name": "my-activity",
          "pages": [
            1,
          ],
          "rule_type": "campaign",
          "status": true,
        },
        {
          "application": 1,
          "containerId": "offers-and-programs",
          "content_type": [
            "targetedCampaign",
          ],
          "id": 2,
          "name": "offers-and-programs",
          "pages": [
            2,
            3,
          ],
          "rule_type": "campaign",
          "status": true,
        },
      ],
    }
  }
  deleteContent={[Function]}
  formChange={[MockFunction]}
  formReset={[MockFunction]}
  formValid={true}
  formValues={
    {
      "application": "nova",
      "mass_targeting": {
        "enrollment_status": [],
      },
      "platforms": {},
      "platforms_targeting": [],
    }
  }
  isDuplicating={false}
  onActivate={[Function]}
  onApprove={[Function]}
  onCancel={[Function]}
  onDelete={[Function]}
  onDuplicate={[Function]}
  onPublish={[Function]}
  onSubmit={[Function]}
  openContentModal={[MockFunction]}
  pages={
    {
      "isLoading": false,
      "items": [
        {
          "application": 1,
          "containers": [
            1,
            68,
          ],
          "description": "NOVA mobile - My Activity tab",
          "id": 1,
          "name": "activities",
          "pageId": "activities",
          "status": true,
        },
        {
          "application": 1,
          "containers": [
            2,
            37,
          ],
          "description": "NOVA mobile - My Accounts tab",
          "id": 2,
          "name": "accounts",
          "pageId": "accounts",
          "status": true,
        },
        {
          "application": 1,
          "containers": [
            2,
          ],
          "description": "NOVA mobile - My Accounts tab",
          "id": 3,
          "name": "account-key",
          "pageId": "account-key",
          "status": true,
        },
      ],
    }
  }
  permissions={
    {
      "admin": true,
      "campaigns_manage": true,
    }
  }
  platforms={
    [
      {
        "id": 1,
        "name": "Web",
        "slug": "web",
      },
      {
        "id": 2,
        "name": "iOS",
        "slug": "ios",
      },
      {
        "id": 3,
        "name": "Android",
        "slug": "android",
      },
    ]
  }
  productBook={
    [
      {
        "category": "banking",
        "code": "DDA",
        "description": "Current Account (B:DDA:CA)",
        "id": "100060",
        "ownership": "B",
        "sub_code": "CA",
      },
      {
        "category": "investing",
        "code": "TFS",
        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
        "id": "340860",
        "ownership": "B",
        "sub_code": "SB",
      },
      {
        "category": "borrowing",
        "code": "AFP",
        "description": "Future Amex Platinum (B:AFP)",
        "id": "620020",
        "ownership": "B",
      },
    ]
  }
  removeAlert={[MockFunction]}
  ruleSubTypeMap={
    {
      "1": {
        "description": "Targeted Campaign",
        "id": 1,
        "type": "targeted",
      },
      "2": {
        "description": "Mass Campaign",
        "id": 2,
        "type": "mass",
      },
      "3": {
        "description": "Mass Message",
        "id": 3,
        "type": "message",
      },
      "4": {
        "description": "Offer Campaign",
        "id": 4,
        "type": "offer",
      },
    }
  }
  ruleTypes={
    [
      {
        "id": 1,
        "rule_type": "alert",
        "slug": "alert",
      },
      {
        "id": 2,
        "rule_type": "campaign",
        "slug": "campaign",
      },
      {
        "id": 3,
        "rule_type": "vignette",
        "slug": "vignette",
      },
      {
        "id": 4,
        "rule_type": "vignette-broadcast",
        "slug": "vignette-broadcast",
      },
      {
        "id": 5,
        "rule_type": "vignette-priority",
        "slug": "vignette-priority",
      },
      {
        "id": 7,
        "rule_type": "estore",
        "slug": "estore",
      },
    ]
  }
  title="Create a New Alert"
  type="alert"
  updateDetails={[MockFunction]}
  users={
    {
      "isLoading": false,
      "items": {
        "1": {
          "email": "<EMAIL>",
          "id": 1,
          "name": "John Doe",
          "roles": [
            1,
          ],
          "sid": "s0000001",
        },
        "2": {
          "email": "<EMAIL>",
          "id": 2,
          "name": "Jane Doe",
          "roles": [
            1,
          ],
          "sid": "s0000002",
        },
      },
    }
  }
/>
`;

exports[`Details Container Snapshot - Create a New Campaign (undefined status) Start/end date validation 1`] = `
{
  "app_version": undefined,
  "application": "nova",
  "container": undefined,
  "content_id": undefined,
  "content_space": "4szkx38resvm",
  "content_type": undefined,
  "end_at": "2018-07-12T00:00:00.000Z",
  "mass_targeting": {
    "enrollment_status": [],
    "languages": [],
  },
  "name": undefined,
  "pages": [],
  "platforms": [],
  "platforms_targeting": [],
  "start_at": "2018-07-13T00:00:00.000Z",
  "status": undefined,
  "targeting": {
    "products": [],
  },
}
`;

exports[`Details Container Snapshot - Create a New Campaign (undefined status) does not fail platform validation if application is phoenix 1`] = `
{
  "app_version": undefined,
  "application": "phoenix",
  "container": undefined,
  "content_id": undefined,
  "content_space": "4szkx38resvm",
  "content_type": undefined,
  "end_at": "2018-07-12T00:00:00.000Z",
  "mass_targeting": {
    "enrollment_status": [],
    "languages": [],
  },
  "name": undefined,
  "pages": [],
  "platforms": [],
  "platforms_targeting": [],
  "start_at": "2018-07-13T00:00:00.000Z",
  "status": undefined,
  "targeting": {
    "products": [],
  },
}
`;

exports[`Details Container Snapshot - Edit a Campaign (draft status) Snapshot 1`] = `
<Details
  access={
    {
      "containers": {
        "atlantis": {
          "manage": [
            "offers-and-programs",
          ],
          "view": [
            "offers-and-programs",
          ],
        },
        "nova": {
          "manage": [
            "offers-and-programs",
          ],
          "view": [
            "offers-and-programs",
          ],
        },
        "phoenix": {
          "manage": [
            "phoenix-alert",
          ],
          "view": [
            "phoenix-alert",
          ],
        },
        "starburst": {
          "manage": [
            "offers-and-programs",
          ],
          "view": [
            "offers-and-programs",
          ],
        },
      },
      "pages": {
        "atlantis": {
          "manage": [
            "accounts",
            "account-key",
          ],
          "view": [
            "accounts",
            "account-key",
          ],
        },
        "nova": {
          "manage": [
            "accounts",
            "account-key",
          ],
          "view": [
            "accounts",
            "account-key",
          ],
        },
        "phoenix": {
          "manage": [
            "lgn-12345",
          ],
          "view": [
            "lgn-12345",
          ],
        },
        "starburst": {
          "manage": [
            "accounts",
            "account-key",
          ],
          "view": [
            "accounts",
            "account-key",
          ],
        },
      },
      "ruleSubTypes": {
        "atlantis": {
          "manage": [
            "targeted",
            "mass",
            "message",
            "wealth",
          ],
          "view": [
            "targeted",
            "mass",
            "message",
            "wealth",
          ],
        },
        "nova": {
          "manage": [
            "targeted",
            "mass",
            "message",
            "offer",
          ],
          "view": [
            "targeted",
            "mass",
            "message",
            "offer",
          ],
        },
        "phoenix": {
          "manage": [
            "targeted",
            "mass",
          ],
          "view": [
            "targeted",
            "mass",
          ],
        },
        "starburst": {
          "manage": [
            "targeted",
            "mass",
            "message",
            "wealth",
          ],
          "view": [
            "targeted",
            "mass",
            "message",
            "wealth",
          ],
        },
      },
    }
  }
  addAlert={[Function]}
  applications={
    [
      {
        "applicationId": "nova",
        "contentful_space": "4szkx38resvm",
        "id": 1,
        "name": "Nova Mobile",
        "platformIds": [
          2,
          3,
        ],
        "platforms": [
          "iOS",
          "Android",
        ],
        "ruleSubTypeIds": [
          1,
          2,
          3,
          4,
        ],
        "ruleSubTypes": [
          "targeted",
          "mass",
          "message",
          "offer",
        ],
        "ruleTypeIds": [
          1,
          2,
        ],
        "ruleTypes": [
          "alert",
          "campaign",
        ],
        "rule_version": 1,
        "status": true,
      },
      {
        "applicationId": "phoenix",
        "contentful_space": "4szkx38resvm",
        "id": 2,
        "name": "Phoenix",
        "platformIds": [
          1,
        ],
        "platforms": [
          "Web",
        ],
        "ruleSubTypeIds": [
          1,
          2,
        ],
        "ruleSubTypes": [
          "targeted",
          "mass",
        ],
        "ruleTypeIds": [
          1,
          2,
        ],
        "ruleTypes": [
          "alert",
          "campaign",
        ],
        "rule_version": 1,
        "status": true,
      },
    ]
  }
  containers={
    {
      "isLoading": false,
      "items": [
        {
          "application": 1,
          "containerId": "my-activity",
          "content_type": [
            "standingCampaign",
          ],
          "id": 1,
          "name": "my-activity",
          "pages": [
            1,
          ],
          "rule_type": "campaign",
          "status": true,
        },
        {
          "application": 1,
          "containerId": "offers-and-programs",
          "content_type": [
            "targetedCampaign",
          ],
          "id": 2,
          "name": "offers-and-programs",
          "pages": [
            2,
            3,
          ],
          "rule_type": "campaign",
          "status": true,
        },
      ],
    }
  }
  deleteContent={[Function]}
  formChange={[MockFunction]}
  formReset={[MockFunction]}
  formValid={true}
  formValues={
    {
      "advancedTargetingRelationship": "or",
      "application": "nova",
      "assignees": [
        {
          "full_name": "John Doe",
          "sid": "s0000001",
        },
      ],
      "container": "offers-and-programs",
      "content_id": "test-content-id",
      "content_type": "targetedCampaign",
      "disabled": false,
      "dismissable_flag": false,
      "end_at": "2025-01-01T00:00:00.000Z",
      "external_ref": "ABC123",
      "id": "test-campaign-id",
      "mass_targeting": {
        "by_product": {
          "any_of": [
            {
              "category": "banking",
              "code": "DDA",
              "description": "Current Account (B:DDA:CA)",
              "id": "100060",
              "ownership": "B",
              "sub_code": "CA",
            },
            {
              "category": "investing",
              "code": "TFS",
              "description": "Tax-Free Savings - BNS (B:TFS:SB)",
              "id": "340860",
              "ownership": "B",
              "sub_code": "SB",
            },
          ],
        },
        "by_scene_points": {
          "points": 9320,
          "targeting_criteria": "greater",
        },
        "enrollment_status": [],
        "languages": [],
        "product_pages": {
          "any_of": [
            {
              "category": "banking",
              "code": "DDA",
              "description": "Current Account (B:DDA:CA)",
              "id": "100060",
              "ownership": "B",
              "sub_code": "CA",
            },
            {
              "category": "investing",
              "code": "TFS",
              "description": "Tax-Free Savings - BNS (B:TFS:SB)",
              "id": "340860",
              "ownership": "B",
              "sub_code": "SB",
            },
            {
              "code": "out-of-date-product",
              "ownership": "B",
            },
          ],
        },
      },
      "name": "test-campaign-title",
      "ownershipRelationship": "or",
      "pages": [
        "accounts",
        "account-key",
      ],
      "platforms": {
        "android": true,
        "ios": true,
      },
      "platforms_targeting": [],
      "start_at": "2021-05-01T00:00:00.000Z",
      "status": "draft",
      "type": "targeted",
    }
  }
  isDuplicating={false}
  onActivate={[Function]}
  onApprove={[Function]}
  onCancel={[Function]}
  onDelete={[Function]}
  onDuplicate={[Function]}
  onPublish={[Function]}
  onSubmit={[Function]}
  openContentModal={[MockFunction]}
  pages={
    {
      "isLoading": false,
      "items": [
        {
          "application": 1,
          "containers": [
            1,
            68,
          ],
          "description": "NOVA mobile - My Activity tab",
          "id": 1,
          "name": "activities",
          "pageId": "activities",
          "status": true,
        },
        {
          "application": 1,
          "containers": [
            2,
            37,
          ],
          "description": "NOVA mobile - My Accounts tab",
          "id": 2,
          "name": "accounts",
          "pageId": "accounts",
          "status": true,
        },
        {
          "application": 1,
          "containers": [
            2,
          ],
          "description": "NOVA mobile - My Accounts tab",
          "id": 3,
          "name": "account-key",
          "pageId": "account-key",
          "status": true,
        },
      ],
    }
  }
  permissions={
    {
      "campaigns_manage": true,
    }
  }
  platforms={
    [
      {
        "id": 1,
        "name": "Web",
        "slug": "web",
      },
      {
        "id": 2,
        "name": "iOS",
        "slug": "ios",
      },
      {
        "id": 3,
        "name": "Android",
        "slug": "android",
      },
    ]
  }
  productBook={
    [
      {
        "category": "banking",
        "code": "DDA",
        "description": "Current Account (B:DDA:CA)",
        "id": "100060",
        "ownership": "B",
        "sub_code": "CA",
      },
      {
        "category": "investing",
        "code": "TFS",
        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
        "id": "340860",
        "ownership": "B",
        "sub_code": "SB",
      },
      {
        "category": "borrowing",
        "code": "AFP",
        "description": "Future Amex Platinum (B:AFP)",
        "id": "620020",
        "ownership": "B",
      },
    ]
  }
  removeAlert={[MockFunction]}
  ruleSubTypeMap={
    {
      "1": {
        "description": "Targeted Campaign",
        "id": 1,
        "type": "targeted",
      },
      "2": {
        "description": "Mass Campaign",
        "id": 2,
        "type": "mass",
      },
      "3": {
        "description": "Mass Message",
        "id": 3,
        "type": "message",
      },
      "4": {
        "description": "Offer Campaign",
        "id": 4,
        "type": "offer",
      },
    }
  }
  ruleTypes={
    [
      {
        "id": 1,
        "rule_type": "alert",
        "slug": "alert",
      },
      {
        "id": 2,
        "rule_type": "campaign",
        "slug": "campaign",
      },
      {
        "id": 3,
        "rule_type": "vignette",
        "slug": "vignette",
      },
      {
        "id": 4,
        "rule_type": "vignette-broadcast",
        "slug": "vignette-broadcast",
      },
      {
        "id": 5,
        "rule_type": "vignette-priority",
        "slug": "vignette-priority",
      },
      {
        "id": 7,
        "rule_type": "estore",
        "slug": "estore",
      },
    ]
  }
  status="draft"
  title="Edit a Campaign"
  type="campaign"
  updateDetails={[MockFunction]}
  users={
    {
      "isLoading": false,
      "items": {
        "1": {
          "email": "<EMAIL>",
          "id": 1,
          "name": "John Doe",
          "roles": [
            1,
          ],
          "sid": "s0000001",
        },
        "2": {
          "email": "<EMAIL>",
          "id": 2,
          "name": "Jane Doe",
          "roles": [
            1,
          ],
          "sid": "s0000002",
        },
      },
    }
  }
/>
`;

exports[`Details Container Snapshot - View a Campaign (submitted status) Snapshot 1`] = `
<Details
  access={
    {
      "containers": {
        "atlantis": {
          "manage": [
            "offers-and-programs",
          ],
          "view": [
            "offers-and-programs",
          ],
        },
        "nova": {
          "manage": [
            "offers-and-programs",
          ],
          "view": [
            "offers-and-programs",
          ],
        },
        "phoenix": {
          "manage": [
            "phoenix-alert",
          ],
          "view": [
            "phoenix-alert",
          ],
        },
        "starburst": {
          "manage": [
            "offers-and-programs",
          ],
          "view": [
            "offers-and-programs",
          ],
        },
      },
      "pages": {
        "atlantis": {
          "manage": [
            "accounts",
            "account-key",
          ],
          "view": [
            "accounts",
            "account-key",
          ],
        },
        "nova": {
          "manage": [
            "accounts",
            "account-key",
          ],
          "view": [
            "accounts",
            "account-key",
          ],
        },
        "phoenix": {
          "manage": [
            "lgn-12345",
          ],
          "view": [
            "lgn-12345",
          ],
        },
        "starburst": {
          "manage": [
            "accounts",
            "account-key",
          ],
          "view": [
            "accounts",
            "account-key",
          ],
        },
      },
      "ruleSubTypes": {
        "atlantis": {
          "manage": [
            "targeted",
            "mass",
            "message",
            "wealth",
          ],
          "view": [
            "targeted",
            "mass",
            "message",
            "wealth",
          ],
        },
        "nova": {
          "manage": [
            "targeted",
            "mass",
            "message",
            "offer",
          ],
          "view": [
            "targeted",
            "mass",
            "message",
            "offer",
          ],
        },
        "phoenix": {
          "manage": [
            "targeted",
            "mass",
          ],
          "view": [
            "targeted",
            "mass",
          ],
        },
        "starburst": {
          "manage": [
            "targeted",
            "mass",
            "message",
            "wealth",
          ],
          "view": [
            "targeted",
            "mass",
            "message",
            "wealth",
          ],
        },
      },
    }
  }
  addAlert={[Function]}
  applications={
    [
      {
        "applicationId": "nova",
        "contentful_space": "4szkx38resvm",
        "id": 1,
        "name": "Nova Mobile",
        "platformIds": [
          2,
          3,
        ],
        "platforms": [
          "iOS",
          "Android",
        ],
        "ruleSubTypeIds": [
          1,
          2,
          3,
          4,
        ],
        "ruleSubTypes": [
          "targeted",
          "mass",
          "message",
          "offer",
        ],
        "ruleTypeIds": [
          1,
          2,
        ],
        "ruleTypes": [
          "alert",
          "campaign",
        ],
        "rule_version": 1,
        "status": true,
      },
      {
        "applicationId": "phoenix",
        "contentful_space": "4szkx38resvm",
        "id": 2,
        "name": "Phoenix",
        "platformIds": [
          1,
        ],
        "platforms": [
          "Web",
        ],
        "ruleSubTypeIds": [
          1,
          2,
        ],
        "ruleSubTypes": [
          "targeted",
          "mass",
        ],
        "ruleTypeIds": [
          1,
          2,
        ],
        "ruleTypes": [
          "alert",
          "campaign",
        ],
        "rule_version": 1,
        "status": true,
      },
    ]
  }
  assignees={[]}
  containers={
    {
      "isLoading": false,
      "items": [
        {
          "application": 1,
          "containerId": "my-activity",
          "content_type": [
            "standingCampaign",
          ],
          "id": 1,
          "name": "my-activity",
          "pages": [
            1,
          ],
          "rule_type": "campaign",
          "status": true,
        },
        {
          "application": 1,
          "containerId": "offers-and-programs",
          "content_type": [
            "targetedCampaign",
          ],
          "id": 2,
          "name": "offers-and-programs",
          "pages": [
            2,
            3,
          ],
          "rule_type": "campaign",
          "status": true,
        },
      ],
    }
  }
  deleteContent={[Function]}
  formChange={[MockFunction]}
  formReset={[MockFunction]}
  formValid={true}
  formValues={
    {
      "advancedTargetingRelationship": "or",
      "application": "nova",
      "assignees": [
        {
          "full_name": "John Doe",
          "sid": "s0000001",
        },
      ],
      "container": "offers-and-programs",
      "content_id": "test-content-id",
      "content_type": "targetedCampaign",
      "disabled": false,
      "dismissable_flag": false,
      "end_at": "2025-01-01T00:00:00.000Z",
      "external_ref": "ABC123",
      "id": "test-campaign-id",
      "mass_targeting": {
        "by_product": {
          "any_of": [
            {
              "category": "banking",
              "code": "DDA",
              "description": "Current Account (B:DDA:CA)",
              "id": "100060",
              "ownership": "B",
              "sub_code": "CA",
            },
            {
              "category": "investing",
              "code": "TFS",
              "description": "Tax-Free Savings - BNS (B:TFS:SB)",
              "id": "340860",
              "ownership": "B",
              "sub_code": "SB",
            },
          ],
        },
        "by_scene_points": {
          "points": 9320,
          "targeting_criteria": "greater",
        },
        "enrollment_status": [],
        "languages": [],
        "product_pages": {
          "any_of": [
            {
              "category": "banking",
              "code": "DDA",
              "description": "Current Account (B:DDA:CA)",
              "id": "100060",
              "ownership": "B",
              "sub_code": "CA",
            },
            {
              "category": "investing",
              "code": "TFS",
              "description": "Tax-Free Savings - BNS (B:TFS:SB)",
              "id": "340860",
              "ownership": "B",
              "sub_code": "SB",
            },
            {
              "code": "out-of-date-product",
              "ownership": "B",
            },
          ],
        },
      },
      "name": "test-campaign-title",
      "ownershipRelationship": "or",
      "pages": [
        "accounts",
        "account-key",
      ],
      "platforms": {
        "android": true,
        "ios": true,
      },
      "platforms_targeting": [],
      "start_at": "2021-05-01T00:00:00.000Z",
      "status": "submitted",
      "type": "targeted",
    }
  }
  isDuplicating={false}
  onActivate={[Function]}
  onApprove={[Function]}
  onCancel={[Function]}
  onDelete={[Function]}
  onDuplicate={[Function]}
  onPublish={[Function]}
  onSubmit={[Function]}
  openContentModal={[MockFunction]}
  pages={
    {
      "isLoading": false,
      "items": [
        {
          "application": 1,
          "containers": [
            1,
            68,
          ],
          "description": "NOVA mobile - My Activity tab",
          "id": 1,
          "name": "activities",
          "pageId": "activities",
          "status": true,
        },
        {
          "application": 1,
          "containers": [
            2,
            37,
          ],
          "description": "NOVA mobile - My Accounts tab",
          "id": 2,
          "name": "accounts",
          "pageId": "accounts",
          "status": true,
        },
        {
          "application": 1,
          "containers": [
            2,
          ],
          "description": "NOVA mobile - My Accounts tab",
          "id": 3,
          "name": "account-key",
          "pageId": "account-key",
          "status": true,
        },
      ],
    }
  }
  permissions={
    {
      "campaigns_manage": true,
    }
  }
  platforms={
    [
      {
        "id": 1,
        "name": "Web",
        "slug": "web",
      },
      {
        "id": 2,
        "name": "iOS",
        "slug": "ios",
      },
      {
        "id": 3,
        "name": "Android",
        "slug": "android",
      },
    ]
  }
  productBook={
    [
      {
        "category": "banking",
        "code": "DDA",
        "description": "Current Account (B:DDA:CA)",
        "id": "100060",
        "ownership": "B",
        "sub_code": "CA",
      },
      {
        "category": "investing",
        "code": "TFS",
        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
        "id": "340860",
        "ownership": "B",
        "sub_code": "SB",
      },
      {
        "category": "borrowing",
        "code": "AFP",
        "description": "Future Amex Platinum (B:AFP)",
        "id": "620020",
        "ownership": "B",
      },
    ]
  }
  removeAlert={[MockFunction]}
  ruleSubTypeMap={
    {
      "1": {
        "description": "Targeted Campaign",
        "id": 1,
        "type": "targeted",
      },
      "2": {
        "description": "Mass Campaign",
        "id": 2,
        "type": "mass",
      },
      "3": {
        "description": "Mass Message",
        "id": 3,
        "type": "message",
      },
      "4": {
        "description": "Offer Campaign",
        "id": 4,
        "type": "offer",
      },
    }
  }
  ruleTypes={
    [
      {
        "id": 1,
        "rule_type": "alert",
        "slug": "alert",
      },
      {
        "id": 2,
        "rule_type": "campaign",
        "slug": "campaign",
      },
      {
        "id": 3,
        "rule_type": "vignette",
        "slug": "vignette",
      },
      {
        "id": 4,
        "rule_type": "vignette-broadcast",
        "slug": "vignette-broadcast",
      },
      {
        "id": 5,
        "rule_type": "vignette-priority",
        "slug": "vignette-priority",
      },
      {
        "id": 7,
        "rule_type": "estore",
        "slug": "estore",
      },
    ]
  }
  status="submitted"
  title="View a Campaign"
  type="campaign"
  updateDetails={[MockFunction]}
  users={
    {
      "isLoading": false,
      "items": {
        "1": {
          "email": "<EMAIL>",
          "id": 1,
          "name": "John Doe",
          "roles": [
            1,
          ],
          "sid": "s0000001",
        },
        "2": {
          "email": "<EMAIL>",
          "id": 2,
          "name": "Jane Doe",
          "roles": [
            1,
          ],
          "sid": "s0000002",
        },
      },
    }
  }
/>
`;

exports[`Details Snapshot - Create a New Alert (undefined status) Snapshot 1`] = `
<Fragment>
  <div
    className="admin-details__assignment-popup"
  />
  <div
    className="admin-details"
  >
    <form
      onSubmit={[Function]}
    >
       
      <div
        className="admin-details__action-bar"
      >
        <f
          bold={false}
          className="admin-details__header"
          color="black"
          component="h1"
          italic={false}
        >
          Create a New Alert
        </f>
        <div
          className="admin-details__action-buttons"
        />
      </div>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Alert
           Information
        </u>
        <Field
          autoComplete="off"
          className="admin-details__field"
          component={[Function]}
          disabled={false}
          label="Alert Title"
          name="name"
          placeholder="Alert Title"
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
        <Field
          className="admin-details__field"
          component={[Function]}
          disabled={false}
          label="Application"
          name="application"
          onChange={[Function]}
          options={
            [
              {
                "id": "nova",
                "name": "Nova Mobile",
              },
              {
                "id": "phoenix",
                "name": "Phoenix",
              },
            ]
          }
          placeholder="Select application…"
          validate={
            [
              [Function],
            ]
          }
        />
      </f>
      <Card2SV
        formDisabled={false}
        formValues={
          {
            "application": "nova",
            "mass_targeting": {
              "enrollment_status": [],
            },
            "platforms": {},
            "platforms_targeting": [],
          }
        }
        isCampaign={false}
      />
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting Dimension
        </u>
        <div
          className="admin-details__date-fields"
        >
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={false}
            label="Start date"
            name="start_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={false}
            label="End date"
            name="end_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
        </div>
        <Field
          className="admin-details__platforms"
          component={[Function]}
          id="platforms-group"
          legend="Platforms"
          name="platforms"
          validate={
            [
              [Function],
            ]
          }
        >
          <div
            style={
              {
                "display": "flex",
              }
            }
          >
            <Field
              component={[Function]}
              disabled={false}
              key="ios"
              label="iOS"
              name="platforms.ios"
            />
          </div>
          <div
            style={
              {
                "display": "flex",
              }
            }
          >
            <Field
              component={[Function]}
              disabled={false}
              key="android"
              label="Android"
              name="platforms.android"
            />
          </div>
        </Field>
        <LanguageCard
          applications={
            [
              {
                "applicationId": "nova",
                "contentful_space": "4szkx38resvm",
                "id": 1,
                "name": "Nova Mobile",
                "platformIds": [
                  2,
                  3,
                ],
                "platforms": [
                  "iOS",
                  "Android",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                  3,
                  4,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                  "message",
                  "offer",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
              {
                "applicationId": "phoenix",
                "contentful_space": "4szkx38resvm",
                "id": 2,
                "name": "Phoenix",
                "platformIds": [
                  1,
                ],
                "platforms": [
                  "Web",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
            ]
          }
          formChange={[MockFunction]}
          formDisabled={false}
          formValues={
            {
              "application": "nova",
              "mass_targeting": {
                "enrollment_status": [],
              },
              "platforms": {},
              "platforms_targeting": [],
            }
          }
          isCampaign={false}
        />
      </f>
      <div
        className="admin-details__placement"
      >
        <Field
          component={[Function]}
          name="contentful"
        >
          <Connect(ReduxForm)
            access={
              {
                "containers": {
                  "atlantis": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "phoenix-alert",
                    ],
                    "view": [
                      "phoenix-alert",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                },
                "pages": {
                  "atlantis": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "lgn-12345",
                    ],
                    "view": [
                      "lgn-12345",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                },
                "ruleSubTypes": {
                  "atlantis": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "targeted",
                      "mass",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                },
              }
            }
            applications={
              [
                {
                  "applicationId": "nova",
                  "contentful_space": "4szkx38resvm",
                  "id": 1,
                  "name": "Nova Mobile",
                  "platformIds": [
                    2,
                    3,
                  ],
                  "platforms": [
                    "iOS",
                    "Android",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                    3,
                    4,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                    "message",
                    "offer",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
                {
                  "applicationId": "phoenix",
                  "contentful_space": "4szkx38resvm",
                  "id": 2,
                  "name": "Phoenix",
                  "platformIds": [
                    1,
                  ],
                  "platforms": [
                    "Web",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
              ]
            }
            containers={{}}
            deleteContent={[MockFunction]}
            formDisabled={false}
            formValues={
              {
                "application": "nova",
                "mass_targeting": {
                  "enrollment_status": [],
                },
                "platforms": {},
                "platforms_targeting": [],
              }
            }
            handleChange={[MockFunction]}
            openContentModal={[MockFunction]}
            pages={
              {
                "isLoading": false,
                "items": [
                  {
                    "application": 1,
                    "containers": [
                      1,
                      68,
                    ],
                    "description": "NOVA mobile - My Activity tab",
                    "id": 1,
                    "name": "activities",
                    "pageId": "activities",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                      37,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 2,
                    "name": "accounts",
                    "pageId": "accounts",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 3,
                    "name": "account-key",
                    "pageId": "account-key",
                    "status": true,
                  },
                ],
              }
            }
            productBook={[]}
            type="alert"
          />
        </Field>
      </div>
      <div
        className="admin-details__action-bar"
      >
        <f
          onClick={[MockFunction]}
          type="button"
        >
          Cancel
        </f>
        <div
          className="admin-details__action-buttons"
        >
          <d
            className="admin-details__action-button"
            disabled={false}
            labelPadding={36}
            onClick={[MockFunction]}
            size="regular"
          >
            Save as Draft
          </d>
          <d
            className="admin-details__action-button"
            disabled={false}
            labelPadding={36}
            onClick={[Function]}
            size="regular"
          >
            Submit for Review
          </d>
        </div>
      </div>
    </form>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[MockFunction]}
      primaryButtonLabel="Delete"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      This 
      alert
       will be deleted.
    </g>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[Function]}
      primaryButtonLabel="Continue"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      <p>
        The container, page, and content combination you have chosen will no longer be valid for the new application you have selected.
      </p>
      <br />
      <p>
        To continue with the current application, you will have to select new content.
      </p>
    </g>
    <Connect(ReduxForm)
      contentfulSpace="4szkx38resvm"
      onSave={[MockFunction]}
      pages={
        {
          "isLoading": false,
          "items": [
            {
              "application": 1,
              "containers": [
                1,
                68,
              ],
              "description": "NOVA mobile - My Activity tab",
              "id": 1,
              "name": "activities",
              "pageId": "activities",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
                37,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 2,
              "name": "accounts",
              "pageId": "accounts",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 3,
              "name": "account-key",
              "pageId": "account-key",
              "status": true,
            },
          ],
        }
      }
      type="alert"
    />
  </div>
</Fragment>
`;

exports[`Details Snapshot - Edit a Campaign (draft status) CCAU Snapshot 1`] = `
<Fragment>
  <div
    className="admin-details__assignment-popup"
  >
    <f
      headline="This campaign needs to be assigned"
      isModalVisible={false}
      isOnlyClosedByButton={false}
      message=""
      setModalVisible={[Function]}
    >
      <AssignmentAutosuggest
        data={[]}
        dataLegend={
          {
            "keyName": "sid",
            "valueName": "fullName",
          }
        }
        editable={true}
        fromPopup={true}
        onChange={[Function]}
      />
      <div
        className="admin-details__modal-footer"
      >
        <d
          className="admin-details__modal-button"
          disabled={false}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Cancel
        </d>
        <d
          className="admin-details__modal-button"
          disabled={true}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Submit
        </d>
      </div>
    </f>
  </div>
  <div
    className="admin-details"
  >
    <form
      onSubmit={[Function]}
    >
       
      <div
        className="admin-details__action-bar"
      >
        <f
          bold={false}
          className="admin-details__header"
          color="black"
          component="h1"
          italic={false}
        >
          Edit a Campaign
        </f>
        <div
          className="admin-details__action-buttons"
        />
      </div>
      <f
        bold={false}
        color="black"
        component="h3"
        italic={false}
      >
        Current Status: 
        <StatusBadge
          disabled={false}
          endTime="2025-01-01T00:00:00.000Z"
          startTime="2021-05-01T00:00:00.000Z"
          status="draft"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <div>
          <AssignmentAutosuggest
            data={[]}
            dataLegend={
              {
                "keyName": "sid",
                "valueName": "fullName",
              }
            }
            editable={false}
            initialSelection={
              [
                {
                  "fullName": "John Doe",
                  "full_name": "John Doe",
                  "sid": "s0000001",
                },
              ]
            }
            noLock={true}
            onChange={[Function]}
          />
        </div>
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign
           Information
        </u>
        <Field
          autoComplete="off"
          className="admin-details__field"
          component={[Function]}
          disabled={true}
          label="Campaign Title"
          name="name"
          placeholder="Campaign Title"
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
        <Field
          className="admin-details__field"
          component={[Function]}
          disabled={true}
          label="Application"
          name="application"
          onChange={[Function]}
          options={[]}
          placeholder="Select application…"
          validate={
            [
              [Function],
            ]
          }
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign Type
        </u>
        <d
          className={null}
          disabled={false}
          error={null}
          form={null}
          id="campaign-type-radio-group"
          inline={false}
          inputRef={null}
          labelLeft={false}
          legend=""
          name="type"
          tooltip={null}
        >
          <div
            className="admin-details__radio-container"
          >
            <Field
              component={[Function]}
              disabled={true}
              id="targeted"
              key="rule-sub-type-targeted"
              label="Targeted Campaign"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="mass"
              key="rule-sub-type-mass"
              label="Mass Campaign"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="message"
              key="rule-sub-type-message"
              label="Mass Message"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="offer"
              key="rule-sub-type-offer"
              label="Offer Campaign"
              name="type"
              onChange={[Function]}
            />
          </div>
        </d>
        <div
          className="admin-details__no-legend"
        >
          <Field
            component={[Function]}
            disabled={true}
            label="Urgent"
            name="urgent"
          />
        </div>
        <Field
          component={[Function]}
          disabled={true}
          label="Dismissible (N/A for Inbox Updates)"
          name="dismissable_flag"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting by Campaign ID
        </u>
        <Field
          autoComplete="off"
          component={[Function]}
          disabled={true}
          label="Campaign ID"
          name="external_ref"
          placeholder="Campaign ID"
          required={true}
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
      </f>
      <Card2SV
        formDisabled={true}
        formValues={
          {
            "advancedTargetingRelationship": "or",
            "application": "nova",
            "assignees": [
              {
                "full_name": "John Doe",
                "sid": "s0000001",
              },
            ],
            "container": "offers-and-programs",
            "content_id": "test-content-id",
            "content_type": "targetedCampaign",
            "disabled": false,
            "dismissable_flag": false,
            "end_at": "2025-01-01T00:00:00.000Z",
            "external_ref": "ABC123",
            "id": "test-campaign-id",
            "mass_targeting": {
              "by_product": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                ],
              },
              "by_scene_points": {
                "points": 9320,
                "targeting_criteria": "greater",
              },
              "enrollment_status": [],
              "languages": [],
              "product_pages": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                  {
                    "code": "out-of-date-product",
                    "ownership": "B",
                  },
                ],
              },
            },
            "name": "test-campaign-title",
            "ownershipRelationship": "or",
            "pages": [
              "accounts",
              "account-key",
            ],
            "platforms": {
              "android": true,
              "ios": true,
            },
            "platforms_targeting": [],
            "start_at": "2021-05-01T00:00:00.000Z",
            "status": "draft",
            "type": "targeted",
          }
        }
        isCampaign={true}
      />
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting Dimension
        </u>
        <div
          className="admin-details__date-fields"
        >
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={true}
            label="Start date"
            name="start_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={true}
            label="End date"
            name="end_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
        </div>
        <Field
          className="admin-details__platforms"
          component={[Function]}
          id="platforms-group"
          legend="Platforms"
          name="platforms"
          validate={
            [
              [Function],
            ]
          }
        />
        <LanguageCard
          applications={
            [
              {
                "applicationId": "nova",
                "contentful_space": "4szkx38resvm",
                "id": 1,
                "name": "Nova Mobile",
                "platformIds": [
                  2,
                  3,
                ],
                "platforms": [
                  "iOS",
                  "Android",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                  3,
                  4,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                  "message",
                  "offer",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
              {
                "applicationId": "phoenix",
                "contentful_space": "4szkx38resvm",
                "id": 2,
                "name": "Phoenix",
                "platformIds": [
                  1,
                ],
                "platforms": [
                  "Web",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
            ]
          }
          formChange={[MockFunction]}
          formDisabled={true}
          formValues={
            {
              "advancedTargetingRelationship": "or",
              "application": "nova",
              "assignees": [
                {
                  "full_name": "John Doe",
                  "sid": "s0000001",
                },
              ],
              "container": "offers-and-programs",
              "content_id": "test-content-id",
              "content_type": "targetedCampaign",
              "disabled": false,
              "dismissable_flag": false,
              "end_at": "2025-01-01T00:00:00.000Z",
              "external_ref": "ABC123",
              "id": "test-campaign-id",
              "mass_targeting": {
                "by_product": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                  ],
                },
                "by_scene_points": {
                  "points": 9320,
                  "targeting_criteria": "greater",
                },
                "enrollment_status": [],
                "languages": [],
                "product_pages": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                    {
                      "code": "out-of-date-product",
                      "ownership": "B",
                    },
                  ],
                },
              },
              "name": "test-campaign-title",
              "ownershipRelationship": "or",
              "pages": [
                "accounts",
                "account-key",
              ],
              "platforms": {
                "android": true,
                "ios": true,
              },
              "platforms_targeting": [],
              "start_at": "2021-05-01T00:00:00.000Z",
              "status": "draft",
              "type": "targeted",
            }
          }
          isCampaign={true}
        />
      </f>
      <div
        className="admin-details__placement"
      >
        <Field
          component={[Function]}
          name="contentful"
        >
          <Connect(ReduxForm)
            access={
              {
                "containers": {
                  "atlantis": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "phoenix-alert",
                    ],
                    "view": [
                      "phoenix-alert",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                },
                "pages": {
                  "atlantis": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "lgn-12345",
                    ],
                    "view": [
                      "lgn-12345",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                },
                "ruleSubTypes": {
                  "atlantis": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "targeted",
                      "mass",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                },
              }
            }
            applications={
              [
                {
                  "applicationId": "nova",
                  "contentful_space": "4szkx38resvm",
                  "id": 1,
                  "name": "Nova Mobile",
                  "platformIds": [
                    2,
                    3,
                  ],
                  "platforms": [
                    "iOS",
                    "Android",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                    3,
                    4,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                    "message",
                    "offer",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
                {
                  "applicationId": "phoenix",
                  "contentful_space": "4szkx38resvm",
                  "id": 2,
                  "name": "Phoenix",
                  "platformIds": [
                    1,
                  ],
                  "platforms": [
                    "Web",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
              ]
            }
            containers={{}}
            deleteContent={[MockFunction]}
            formDisabled={true}
            formValues={
              {
                "advancedTargetingRelationship": "or",
                "application": "nova",
                "assignees": [
                  {
                    "full_name": "John Doe",
                    "sid": "s0000001",
                  },
                ],
                "container": "offers-and-programs",
                "content_id": "test-content-id",
                "content_type": "targetedCampaign",
                "disabled": false,
                "dismissable_flag": false,
                "end_at": "2025-01-01T00:00:00.000Z",
                "external_ref": "ABC123",
                "id": "test-campaign-id",
                "mass_targeting": {
                  "by_product": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                    ],
                  },
                  "by_scene_points": {
                    "points": 9320,
                    "targeting_criteria": "greater",
                  },
                  "enrollment_status": [],
                  "languages": [],
                  "product_pages": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                      {
                        "code": "out-of-date-product",
                        "ownership": "B",
                      },
                    ],
                  },
                },
                "name": "test-campaign-title",
                "ownershipRelationship": "or",
                "pages": [
                  "accounts",
                  "account-key",
                ],
                "platforms": {
                  "android": true,
                  "ios": true,
                },
                "platforms_targeting": [],
                "start_at": "2021-05-01T00:00:00.000Z",
                "status": "draft",
                "type": "targeted",
              }
            }
            handleChange={[MockFunction]}
            openContentModal={[MockFunction]}
            pages={
              {
                "isLoading": false,
                "items": [
                  {
                    "application": 1,
                    "containers": [
                      1,
                      68,
                    ],
                    "description": "NOVA mobile - My Activity tab",
                    "id": 1,
                    "name": "activities",
                    "pageId": "activities",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                      37,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 2,
                    "name": "accounts",
                    "pageId": "accounts",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 3,
                    "name": "account-key",
                    "pageId": "account-key",
                    "status": true,
                  },
                ],
              }
            }
            productBook={[]}
            type="ccau_campaign"
          />
        </Field>
      </div>
      <div
        className="admin-details__action-bar"
      >
        <f
          onClick={[MockFunction]}
          type="button"
        >
          Cancel
        </f>
        <div
          className="admin-details__action-buttons"
        >
          <d
            className="admin-details__action-button"
            disabled={false}
            labelPadding={36}
            onClick={[MockFunction]}
            size="regular"
          >
            Save as Draft
          </d>
        </div>
      </div>
    </form>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[MockFunction]}
      primaryButtonLabel="Delete"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      This 
      Campaign
       will be deleted.
    </g>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[Function]}
      primaryButtonLabel="Continue"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      <p>
        The container, page, and content combination you have chosen will no longer be valid for the new application you have selected.
      </p>
      <br />
      <p>
        To continue with the current application, you will have to select new content.
      </p>
    </g>
    <Connect(ReduxForm)
      isDismissible={false}
      onSave={[MockFunction]}
      pages={
        {
          "isLoading": false,
          "items": [
            {
              "application": 1,
              "containers": [
                1,
                68,
              ],
              "description": "NOVA mobile - My Activity tab",
              "id": 1,
              "name": "activities",
              "pageId": "activities",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
                37,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 2,
              "name": "accounts",
              "pageId": "accounts",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 3,
              "name": "account-key",
              "pageId": "account-key",
              "status": true,
            },
          ],
        }
      }
      type="ccau_campaign"
    />
  </div>
</Fragment>
`;

exports[`Details Snapshot - Edit a Campaign (draft status) Snapshot 1`] = `
<Fragment>
  <div
    className="admin-details__assignment-popup"
  >
    <f
      headline="This campaign needs to be assigned"
      isModalVisible={false}
      isOnlyClosedByButton={false}
      message=""
      setModalVisible={[Function]}
    >
      <AssignmentAutosuggest
        data={[]}
        dataLegend={
          {
            "keyName": "sid",
            "valueName": "fullName",
          }
        }
        editable={true}
        fromPopup={true}
        onChange={[Function]}
      />
      <div
        className="admin-details__modal-footer"
      >
        <d
          className="admin-details__modal-button"
          disabled={false}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Cancel
        </d>
        <d
          className="admin-details__modal-button"
          disabled={true}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Submit
        </d>
      </div>
    </f>
  </div>
  <div
    className="admin-details"
  >
    <form
      onSubmit={[Function]}
    >
       
      <div
        className="admin-details__action-bar"
      >
        <f
          bold={false}
          className="admin-details__header"
          color="black"
          component="h1"
          italic={false}
        >
          Edit a Campaign
        </f>
        <div
          className="admin-details__action-buttons"
        >
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[MockFunction]}
            type="caution"
          >
            Duplicate
          </d>
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Delete
          </d>
        </div>
      </div>
      <f
        bold={false}
        color="black"
        component="h3"
        italic={false}
      >
        Current Status: 
        <StatusBadge
          disabled={false}
          endTime="2025-01-01T00:00:00.000Z"
          startTime="2021-05-01T00:00:00.000Z"
          status="draft"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <div>
          <AssignmentAutosuggest
            data={[]}
            dataLegend={
              {
                "keyName": "sid",
                "valueName": "fullName",
              }
            }
            editable={false}
            initialSelection={
              [
                {
                  "fullName": "John Doe",
                  "full_name": "John Doe",
                  "sid": "s0000001",
                },
              ]
            }
            noLock={true}
            onChange={[Function]}
          />
        </div>
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign
           Information
        </u>
        <Field
          autoComplete="off"
          className="admin-details__field"
          component={[Function]}
          disabled={false}
          label="Campaign Title"
          name="name"
          placeholder="Campaign Title"
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
        <Field
          className="admin-details__field"
          component={[Function]}
          disabled={false}
          label="Application"
          name="application"
          onChange={[Function]}
          options={
            [
              {
                "id": "nova",
                "name": "Nova Mobile",
              },
              {
                "id": "phoenix",
                "name": "Phoenix",
              },
            ]
          }
          placeholder="Select application…"
          validate={
            [
              [Function],
            ]
          }
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign Type
        </u>
        <d
          className={null}
          disabled={false}
          error={null}
          form={null}
          id="campaign-type-radio-group"
          inline={false}
          inputRef={null}
          labelLeft={false}
          legend=""
          name="type"
          tooltip={null}
        >
          <div
            className="admin-details__radio-container"
          >
            <Field
              component={[Function]}
              disabled={false}
              id="targeted"
              key="rule-sub-type-targeted"
              label="Targeted Campaign"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={false}
              id="mass"
              key="rule-sub-type-mass"
              label="Mass Campaign"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={false}
              id="message"
              key="rule-sub-type-message"
              label="Mass Message"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={false}
              id="offer"
              key="rule-sub-type-offer"
              label="Offer Campaign"
              name="type"
              onChange={[Function]}
            />
          </div>
        </d>
        <div
          className="admin-details__no-legend"
        >
          <Field
            component={[Function]}
            disabled={false}
            label="Urgent"
            name="urgent"
          />
        </div>
        <Field
          component={[Function]}
          disabled={false}
          label="Dismissible (N/A for Inbox Updates)"
          name="dismissable_flag"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting by Campaign ID
        </u>
        <Field
          autoComplete="off"
          component={[Function]}
          disabled={false}
          label="KT/PEGA Campaign ID"
          name="external_ref"
          placeholder="Campaign ID"
          required={true}
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
      </f>
      <Card2SV
        formDisabled={false}
        formValues={
          {
            "advancedTargetingRelationship": "or",
            "application": "nova",
            "assignees": [
              {
                "full_name": "John Doe",
                "sid": "s0000001",
              },
            ],
            "container": "offers-and-programs",
            "content_id": "test-content-id",
            "content_type": "targetedCampaign",
            "disabled": false,
            "dismissable_flag": false,
            "end_at": "2025-01-01T00:00:00.000Z",
            "external_ref": "ABC123",
            "id": "test-campaign-id",
            "mass_targeting": {
              "by_product": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                ],
              },
              "by_scene_points": {
                "points": 9320,
                "targeting_criteria": "greater",
              },
              "enrollment_status": [],
              "languages": [],
              "product_pages": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                  {
                    "code": "out-of-date-product",
                    "ownership": "B",
                  },
                ],
              },
            },
            "name": "test-campaign-title",
            "ownershipRelationship": "or",
            "pages": [
              "accounts",
              "account-key",
            ],
            "platforms": {
              "android": true,
              "ios": true,
            },
            "platforms_targeting": [],
            "start_at": "2021-05-01T00:00:00.000Z",
            "status": "draft",
            "type": "targeted",
          }
        }
        isCampaign={true}
      />
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting Dimension
        </u>
        <div
          className="admin-details__date-fields"
        >
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={false}
            label="Start date"
            name="start_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={false}
            label="End date"
            name="end_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
        </div>
        <Field
          className="admin-details__platforms"
          component={[Function]}
          id="platforms-group"
          legend="Platforms"
          name="platforms"
          validate={
            [
              [Function],
            ]
          }
        >
          <div
            style={
              {
                "display": "flex",
              }
            }
          >
            <Field
              component={[Function]}
              disabled={false}
              key="ios"
              label="iOS"
              name="platforms.ios"
            />
            <Connect(ReduxForm)
              formName="campaignDetails"
              platform="ios"
            />
          </div>
          <Field
            component={[Function]}
            editingMode={true}
            formName="campaignDetails"
            name="platforms_targeting"
            platform="ios"
          />
          <div
            style={
              {
                "display": "flex",
              }
            }
          >
            <Field
              component={[Function]}
              disabled={false}
              key="android"
              label="Android"
              name="platforms.android"
            />
            <Connect(ReduxForm)
              formName="campaignDetails"
              platform="android"
            />
          </div>
          <Field
            component={[Function]}
            editingMode={true}
            formName="campaignDetails"
            name="platforms_targeting"
            platform="android"
          />
        </Field>
        <LanguageCard
          applications={
            [
              {
                "applicationId": "nova",
                "contentful_space": "4szkx38resvm",
                "id": 1,
                "name": "Nova Mobile",
                "platformIds": [
                  2,
                  3,
                ],
                "platforms": [
                  "iOS",
                  "Android",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                  3,
                  4,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                  "message",
                  "offer",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
              {
                "applicationId": "phoenix",
                "contentful_space": "4szkx38resvm",
                "id": 2,
                "name": "Phoenix",
                "platformIds": [
                  1,
                ],
                "platforms": [
                  "Web",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
            ]
          }
          formChange={[MockFunction]}
          formDisabled={false}
          formValues={
            {
              "advancedTargetingRelationship": "or",
              "application": "nova",
              "assignees": [
                {
                  "full_name": "John Doe",
                  "sid": "s0000001",
                },
              ],
              "container": "offers-and-programs",
              "content_id": "test-content-id",
              "content_type": "targetedCampaign",
              "disabled": false,
              "dismissable_flag": false,
              "end_at": "2025-01-01T00:00:00.000Z",
              "external_ref": "ABC123",
              "id": "test-campaign-id",
              "mass_targeting": {
                "by_product": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                  ],
                },
                "by_scene_points": {
                  "points": 9320,
                  "targeting_criteria": "greater",
                },
                "enrollment_status": [],
                "languages": [],
                "product_pages": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                    {
                      "code": "out-of-date-product",
                      "ownership": "B",
                    },
                  ],
                },
              },
              "name": "test-campaign-title",
              "ownershipRelationship": "or",
              "pages": [
                "accounts",
                "account-key",
              ],
              "platforms": {
                "android": true,
                "ios": true,
              },
              "platforms_targeting": [],
              "start_at": "2021-05-01T00:00:00.000Z",
              "status": "draft",
              "type": "targeted",
            }
          }
          isCampaign={true}
        />
      </f>
      <div
        className="admin-details__placement"
      >
        <Field
          component={[Function]}
          name="contentful"
        >
          <Connect(ReduxForm)
            access={
              {
                "containers": {
                  "atlantis": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "phoenix-alert",
                    ],
                    "view": [
                      "phoenix-alert",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                },
                "pages": {
                  "atlantis": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "lgn-12345",
                    ],
                    "view": [
                      "lgn-12345",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                },
                "ruleSubTypes": {
                  "atlantis": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "targeted",
                      "mass",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                },
              }
            }
            applications={
              [
                {
                  "applicationId": "nova",
                  "contentful_space": "4szkx38resvm",
                  "id": 1,
                  "name": "Nova Mobile",
                  "platformIds": [
                    2,
                    3,
                  ],
                  "platforms": [
                    "iOS",
                    "Android",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                    3,
                    4,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                    "message",
                    "offer",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
                {
                  "applicationId": "phoenix",
                  "contentful_space": "4szkx38resvm",
                  "id": 2,
                  "name": "Phoenix",
                  "platformIds": [
                    1,
                  ],
                  "platforms": [
                    "Web",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
              ]
            }
            containers={
              {
                "1": {
                  "application": 1,
                  "containerId": "my-activity",
                  "content_type": [
                    "standingCampaign",
                  ],
                  "id": 1,
                  "name": "my-activity",
                  "pages": [
                    1,
                  ],
                  "rule_type": "campaign",
                  "status": true,
                },
                "2": {
                  "application": 1,
                  "containerId": "offers-and-programs",
                  "content_type": [
                    "targetedCampaign",
                  ],
                  "id": 2,
                  "name": "offers-and-programs",
                  "pages": [
                    2,
                    3,
                  ],
                  "rule_type": "campaign",
                  "status": true,
                },
              }
            }
            deleteContent={[MockFunction]}
            formDisabled={false}
            formValues={
              {
                "advancedTargetingRelationship": "or",
                "application": "nova",
                "assignees": [
                  {
                    "full_name": "John Doe",
                    "sid": "s0000001",
                  },
                ],
                "container": "offers-and-programs",
                "content_id": "test-content-id",
                "content_type": "targetedCampaign",
                "disabled": false,
                "dismissable_flag": false,
                "end_at": "2025-01-01T00:00:00.000Z",
                "external_ref": "ABC123",
                "id": "test-campaign-id",
                "mass_targeting": {
                  "by_product": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                    ],
                  },
                  "by_scene_points": {
                    "points": 9320,
                    "targeting_criteria": "greater",
                  },
                  "enrollment_status": [],
                  "languages": [],
                  "product_pages": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                      {
                        "code": "out-of-date-product",
                        "ownership": "B",
                      },
                    ],
                  },
                },
                "name": "test-campaign-title",
                "ownershipRelationship": "or",
                "pages": [
                  "accounts",
                  "account-key",
                ],
                "platforms": {
                  "android": true,
                  "ios": true,
                },
                "platforms_targeting": [],
                "start_at": "2021-05-01T00:00:00.000Z",
                "status": "draft",
                "type": "targeted",
              }
            }
            handleChange={[MockFunction]}
            openContentModal={[MockFunction]}
            pages={
              {
                "isLoading": false,
                "items": [
                  {
                    "application": 1,
                    "containers": [
                      1,
                      68,
                    ],
                    "description": "NOVA mobile - My Activity tab",
                    "id": 1,
                    "name": "activities",
                    "pageId": "activities",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                      37,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 2,
                    "name": "accounts",
                    "pageId": "accounts",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 3,
                    "name": "account-key",
                    "pageId": "account-key",
                    "status": true,
                  },
                ],
              }
            }
            productBook={[]}
            type="campaign"
          />
        </Field>
      </div>
      <div
        className="admin-details__action-bar"
      >
        <f
          onClick={[MockFunction]}
          type="button"
        >
          Cancel
        </f>
        <div
          className="admin-details__action-buttons"
        >
          <d
            className="admin-details__action-button"
            disabled={false}
            labelPadding={36}
            onClick={[MockFunction]}
            size="regular"
          >
            Save as Draft
          </d>
          <d
            className="admin-details__action-button"
            disabled={false}
            labelPadding={36}
            onClick={[Function]}
            size="regular"
          >
            Submit for Review
          </d>
        </div>
      </div>
    </form>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[MockFunction]}
      primaryButtonLabel="Delete"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      This 
      campaign
       will be deleted.
    </g>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[Function]}
      primaryButtonLabel="Continue"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      <p>
        The container, page, and content combination you have chosen will no longer be valid for the new application you have selected.
      </p>
      <br />
      <p>
        To continue with the current application, you will have to select new content.
      </p>
    </g>
    <Connect(ReduxForm)
      container={
        {
          "application": 1,
          "containerId": "offers-and-programs",
          "content_type": [
            "targetedCampaign",
          ],
          "id": 2,
          "name": "offers-and-programs",
          "pages": [
            2,
            3,
          ],
          "rule_type": "campaign",
          "status": true,
        }
      }
      contentfulSpace="4szkx38resvm"
      isDismissible={false}
      onSave={[MockFunction]}
      pages={
        {
          "isLoading": false,
          "items": [
            {
              "application": 1,
              "containers": [
                1,
                68,
              ],
              "description": "NOVA mobile - My Activity tab",
              "id": 1,
              "name": "activities",
              "pageId": "activities",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
                37,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 2,
              "name": "accounts",
              "pageId": "accounts",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 3,
              "name": "account-key",
              "pageId": "account-key",
              "status": true,
            },
          ],
        }
      }
      type="campaign"
    />
  </div>
</Fragment>
`;

exports[`Details Snapshot - View a Campaign (draft status) CCAU Snapshot 1`] = `
<Fragment>
  <div
    className="admin-details__assignment-popup"
  >
    <f
      headline="This campaign needs to be assigned"
      isModalVisible={false}
      isOnlyClosedByButton={false}
      message=""
      setModalVisible={[Function]}
    >
      <AssignmentAutosuggest
        data={[]}
        dataLegend={
          {
            "keyName": "sid",
            "valueName": "fullName",
          }
        }
        editable={true}
        fromPopup={true}
        onChange={[Function]}
      />
      <div
        className="admin-details__modal-footer"
      >
        <d
          className="admin-details__modal-button"
          disabled={false}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Cancel
        </d>
        <d
          className="admin-details__modal-button"
          disabled={true}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Submit
        </d>
      </div>
    </f>
  </div>
  <div
    className="admin-details"
  >
    <form
      onSubmit={[Function]}
    >
       
      <div
        className="admin-details__action-bar"
      >
        <f
          bold={false}
          className="admin-details__header"
          color="black"
          component="h1"
          italic={false}
        >
          Edit a Campaign
        </f>
        <div
          className="admin-details__action-buttons"
        />
      </div>
      <f
        bold={false}
        color="black"
        component="h3"
        italic={false}
      >
        Current Status: 
        <StatusBadge
          disabled={false}
          endTime="2025-01-01T00:00:00.000Z"
          startTime="2021-05-01T00:00:00.000Z"
          status="draft"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <div>
          <AssignmentAutosuggest
            data={[]}
            dataLegend={
              {
                "keyName": "sid",
                "valueName": "fullName",
              }
            }
            editable={false}
            initialSelection={
              [
                {
                  "fullName": "John Doe",
                  "full_name": "John Doe",
                  "sid": "s0000001",
                },
              ]
            }
            noLock={true}
            onChange={[Function]}
          />
        </div>
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign
           Information
        </u>
        <Field
          autoComplete="off"
          className="admin-details__field"
          component={[Function]}
          disabled={true}
          label="Campaign Title"
          name="name"
          placeholder="Campaign Title"
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
        <Field
          className="admin-details__field"
          component={[Function]}
          disabled={true}
          label="Application"
          name="application"
          onChange={[Function]}
          options={[]}
          placeholder="Select application…"
          validate={
            [
              [Function],
            ]
          }
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign Type
        </u>
        <d
          className={null}
          disabled={false}
          error={null}
          form={null}
          id="campaign-type-radio-group"
          inline={false}
          inputRef={null}
          labelLeft={false}
          legend=""
          name="type"
          tooltip={null}
        >
          <div
            className="admin-details__radio-container"
          >
            <Field
              component={[Function]}
              disabled={true}
              id="targeted"
              key="rule-sub-type-targeted"
              label="Targeted Campaign"
              name="type"
              onChange={[Function]}
            />
          </div>
        </d>
        <div
          className="admin-details__no-legend"
        >
          <Field
            component={[Function]}
            disabled={true}
            label="Urgent"
            name="urgent"
          />
        </div>
        <Field
          component={[Function]}
          disabled={true}
          label="Dismissible (N/A for Inbox Updates)"
          name="dismissable_flag"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting by Campaign ID
        </u>
        <Field
          autoComplete="off"
          component={[Function]}
          disabled={true}
          label="Campaign ID"
          name="external_ref"
          placeholder="Campaign ID"
          required={true}
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
      </f>
      <Card2SV
        formDisabled={true}
        formValues={
          {
            "advancedTargetingRelationship": "or",
            "application": "nova",
            "assignees": [
              {
                "full_name": "John Doe",
                "sid": "s0000001",
              },
            ],
            "container": "offers-and-programs",
            "content_id": "test-content-id",
            "content_type": "targetedCampaign",
            "disabled": false,
            "dismissable_flag": false,
            "end_at": "2025-01-01T00:00:00.000Z",
            "external_ref": "ABC123",
            "id": "test-campaign-id",
            "mass_targeting": {
              "by_product": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                ],
              },
              "by_scene_points": {
                "points": 9320,
                "targeting_criteria": "greater",
              },
              "enrollment_status": [],
              "languages": [],
              "product_pages": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                  {
                    "code": "out-of-date-product",
                    "ownership": "B",
                  },
                ],
              },
            },
            "name": "test-campaign-title",
            "ownershipRelationship": "or",
            "pages": [
              "accounts",
              "account-key",
            ],
            "platforms": {
              "android": true,
              "ios": true,
            },
            "platforms_targeting": [],
            "start_at": "2021-05-01T00:00:00.000Z",
            "status": "draft",
            "type": "targeted",
          }
        }
        isCampaign={true}
      />
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting Dimension
        </u>
        <div
          className="admin-details__date-fields"
        >
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={true}
            label="Start date"
            name="start_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={true}
            label="End date"
            name="end_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
        </div>
        <Field
          className="admin-details__platforms"
          component={[Function]}
          id="platforms-group"
          legend="Platforms"
          name="platforms"
          validate={
            [
              [Function],
            ]
          }
        />
        <LanguageCard
          applications={
            [
              {
                "applicationId": "nova",
                "contentful_space": "4szkx38resvm",
                "id": 1,
                "name": "Nova Mobile",
                "platformIds": [
                  2,
                  3,
                ],
                "platforms": [
                  "iOS",
                  "Android",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                  3,
                  4,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                  "message",
                  "offer",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
              {
                "applicationId": "phoenix",
                "contentful_space": "4szkx38resvm",
                "id": 2,
                "name": "Phoenix",
                "platformIds": [
                  1,
                ],
                "platforms": [
                  "Web",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
            ]
          }
          formChange={[MockFunction]}
          formDisabled={true}
          formValues={
            {
              "advancedTargetingRelationship": "or",
              "application": "nova",
              "assignees": [
                {
                  "full_name": "John Doe",
                  "sid": "s0000001",
                },
              ],
              "container": "offers-and-programs",
              "content_id": "test-content-id",
              "content_type": "targetedCampaign",
              "disabled": false,
              "dismissable_flag": false,
              "end_at": "2025-01-01T00:00:00.000Z",
              "external_ref": "ABC123",
              "id": "test-campaign-id",
              "mass_targeting": {
                "by_product": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                  ],
                },
                "by_scene_points": {
                  "points": 9320,
                  "targeting_criteria": "greater",
                },
                "enrollment_status": [],
                "languages": [],
                "product_pages": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                    {
                      "code": "out-of-date-product",
                      "ownership": "B",
                    },
                  ],
                },
              },
              "name": "test-campaign-title",
              "ownershipRelationship": "or",
              "pages": [
                "accounts",
                "account-key",
              ],
              "platforms": {
                "android": true,
                "ios": true,
              },
              "platforms_targeting": [],
              "start_at": "2021-05-01T00:00:00.000Z",
              "status": "draft",
              "type": "targeted",
            }
          }
          isCampaign={true}
        />
      </f>
      <div
        className="admin-details__placement"
      >
        <Field
          component={[Function]}
          name="contentful"
        >
          <Connect(ReduxForm)
            access={
              {
                "containers": {
                  "atlantis": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "nova": {
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "phoenix": {
                    "view": [
                      "phoenix-alert",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                },
                "pages": {
                  "atlantis": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "nova": {
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "phoenix": {
                    "view": [
                      "lgn-12345",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                },
                "ruleSubTypes": {
                  "atlantis": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                  "nova": {
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                  },
                  "phoenix": {
                    "view": [
                      "targeted",
                      "mass",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                },
              }
            }
            applications={
              [
                {
                  "applicationId": "nova",
                  "contentful_space": "4szkx38resvm",
                  "id": 1,
                  "name": "Nova Mobile",
                  "platformIds": [
                    2,
                    3,
                  ],
                  "platforms": [
                    "iOS",
                    "Android",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                    3,
                    4,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                    "message",
                    "offer",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
                {
                  "applicationId": "phoenix",
                  "contentful_space": "4szkx38resvm",
                  "id": 2,
                  "name": "Phoenix",
                  "platformIds": [
                    1,
                  ],
                  "platforms": [
                    "Web",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
              ]
            }
            containers={{}}
            deleteContent={[MockFunction]}
            formDisabled={true}
            formValues={
              {
                "advancedTargetingRelationship": "or",
                "application": "nova",
                "assignees": [
                  {
                    "full_name": "John Doe",
                    "sid": "s0000001",
                  },
                ],
                "container": "offers-and-programs",
                "content_id": "test-content-id",
                "content_type": "targetedCampaign",
                "disabled": false,
                "dismissable_flag": false,
                "end_at": "2025-01-01T00:00:00.000Z",
                "external_ref": "ABC123",
                "id": "test-campaign-id",
                "mass_targeting": {
                  "by_product": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                    ],
                  },
                  "by_scene_points": {
                    "points": 9320,
                    "targeting_criteria": "greater",
                  },
                  "enrollment_status": [],
                  "languages": [],
                  "product_pages": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                      {
                        "code": "out-of-date-product",
                        "ownership": "B",
                      },
                    ],
                  },
                },
                "name": "test-campaign-title",
                "ownershipRelationship": "or",
                "pages": [
                  "accounts",
                  "account-key",
                ],
                "platforms": {
                  "android": true,
                  "ios": true,
                },
                "platforms_targeting": [],
                "start_at": "2021-05-01T00:00:00.000Z",
                "status": "draft",
                "type": "targeted",
              }
            }
            handleChange={[MockFunction]}
            openContentModal={[MockFunction]}
            pages={
              {
                "isLoading": false,
                "items": [
                  {
                    "application": 1,
                    "containers": [
                      1,
                      68,
                    ],
                    "description": "NOVA mobile - My Activity tab",
                    "id": 1,
                    "name": "activities",
                    "pageId": "activities",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                      37,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 2,
                    "name": "accounts",
                    "pageId": "accounts",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 3,
                    "name": "account-key",
                    "pageId": "account-key",
                    "status": true,
                  },
                ],
              }
            }
            productBook={[]}
            type="ccau_campaign"
          />
        </Field>
      </div>
      <div
        className="admin-details__action-bar"
      >
        <f
          onClick={[MockFunction]}
          type="button"
        >
          Cancel
        </f>
        <div
          className="admin-details__action-buttons"
        >
          <d
            className="admin-details__action-button"
            disabled={false}
            labelPadding={36}
            onClick={[MockFunction]}
            size="regular"
          >
            Save as Draft
          </d>
        </div>
      </div>
    </form>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[MockFunction]}
      primaryButtonLabel="Delete"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      This 
      Campaign
       will be deleted.
    </g>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[Function]}
      primaryButtonLabel="Continue"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      <p>
        The container, page, and content combination you have chosen will no longer be valid for the new application you have selected.
      </p>
      <br />
      <p>
        To continue with the current application, you will have to select new content.
      </p>
    </g>
    <Connect(ReduxForm)
      isDismissible={false}
      onSave={[MockFunction]}
      pages={
        {
          "isLoading": false,
          "items": [
            {
              "application": 1,
              "containers": [
                1,
                68,
              ],
              "description": "NOVA mobile - My Activity tab",
              "id": 1,
              "name": "activities",
              "pageId": "activities",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
                37,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 2,
              "name": "accounts",
              "pageId": "accounts",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 3,
              "name": "account-key",
              "pageId": "account-key",
              "status": true,
            },
          ],
        }
      }
      type="ccau_campaign"
    />
  </div>
</Fragment>
`;

exports[`Details Snapshot - View a Campaign (draft status) Snapshot 1`] = `
<Fragment>
  <div
    className="admin-details__assignment-popup"
  >
    <f
      headline="This campaign needs to be assigned"
      isModalVisible={false}
      isOnlyClosedByButton={false}
      message=""
      setModalVisible={[Function]}
    >
      <AssignmentAutosuggest
        data={[]}
        dataLegend={
          {
            "keyName": "sid",
            "valueName": "fullName",
          }
        }
        editable={true}
        fromPopup={true}
        onChange={[Function]}
      />
      <div
        className="admin-details__modal-footer"
      >
        <d
          className="admin-details__modal-button"
          disabled={false}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Cancel
        </d>
        <d
          className="admin-details__modal-button"
          disabled={true}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Submit
        </d>
      </div>
    </f>
  </div>
  <div
    className="admin-details"
  >
    <form
      onSubmit={[Function]}
    >
       
      <div
        className="admin-details__action-bar"
      >
        <f
          bold={false}
          className="admin-details__header"
          color="black"
          component="h1"
          italic={false}
        >
          Edit a Campaign
        </f>
        <div
          className="admin-details__action-buttons"
        />
      </div>
      <f
        bold={false}
        color="black"
        component="h3"
        italic={false}
      >
        Current Status: 
        <StatusBadge
          disabled={false}
          endTime="2025-01-01T00:00:00.000Z"
          startTime="2021-05-01T00:00:00.000Z"
          status="draft"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <div>
          <AssignmentAutosuggest
            data={[]}
            dataLegend={
              {
                "keyName": "sid",
                "valueName": "fullName",
              }
            }
            editable={false}
            initialSelection={
              [
                {
                  "fullName": "John Doe",
                  "full_name": "John Doe",
                  "sid": "s0000001",
                },
              ]
            }
            noLock={true}
            onChange={[Function]}
          />
        </div>
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign
           Information
        </u>
        <Field
          autoComplete="off"
          className="admin-details__field"
          component={[Function]}
          disabled={true}
          label="Campaign Title"
          name="name"
          placeholder="Campaign Title"
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
        <Field
          className="admin-details__field"
          component={[Function]}
          disabled={true}
          label="Application"
          name="application"
          onChange={[Function]}
          options={
            [
              {
                "id": "nova",
                "name": "Nova Mobile",
              },
              {
                "id": "phoenix",
                "name": "Phoenix",
              },
            ]
          }
          placeholder="Select application…"
          validate={
            [
              [Function],
            ]
          }
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign Type
        </u>
        <d
          className={null}
          disabled={false}
          error={null}
          form={null}
          id="campaign-type-radio-group"
          inline={false}
          inputRef={null}
          labelLeft={false}
          legend=""
          name="type"
          tooltip={null}
        >
          <div
            className="admin-details__radio-container"
          >
            <Field
              component={[Function]}
              disabled={true}
              id="targeted"
              key="rule-sub-type-targeted"
              label="Targeted Campaign"
              name="type"
              onChange={[Function]}
            />
          </div>
        </d>
        <div
          className="admin-details__no-legend"
        >
          <Field
            component={[Function]}
            disabled={true}
            label="Urgent"
            name="urgent"
          />
        </div>
        <Field
          component={[Function]}
          disabled={true}
          label="Dismissible (N/A for Inbox Updates)"
          name="dismissable_flag"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting by Campaign ID
        </u>
        <Field
          autoComplete="off"
          component={[Function]}
          disabled={true}
          label="KT/PEGA Campaign ID"
          name="external_ref"
          placeholder="Campaign ID"
          required={true}
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
      </f>
      <Card2SV
        formDisabled={true}
        formValues={
          {
            "advancedTargetingRelationship": "or",
            "application": "nova",
            "assignees": [
              {
                "full_name": "John Doe",
                "sid": "s0000001",
              },
            ],
            "container": "offers-and-programs",
            "content_id": "test-content-id",
            "content_type": "targetedCampaign",
            "disabled": false,
            "dismissable_flag": false,
            "end_at": "2025-01-01T00:00:00.000Z",
            "external_ref": "ABC123",
            "id": "test-campaign-id",
            "mass_targeting": {
              "by_product": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                ],
              },
              "by_scene_points": {
                "points": 9320,
                "targeting_criteria": "greater",
              },
              "enrollment_status": [],
              "languages": [],
              "product_pages": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                  {
                    "code": "out-of-date-product",
                    "ownership": "B",
                  },
                ],
              },
            },
            "name": "test-campaign-title",
            "ownershipRelationship": "or",
            "pages": [
              "accounts",
              "account-key",
            ],
            "platforms": {
              "android": true,
              "ios": true,
            },
            "platforms_targeting": [],
            "start_at": "2021-05-01T00:00:00.000Z",
            "status": "draft",
            "type": "targeted",
          }
        }
        isCampaign={true}
      />
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting Dimension
        </u>
        <div
          className="admin-details__date-fields"
        >
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={true}
            label="Start date"
            name="start_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={true}
            label="End date"
            name="end_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
        </div>
        <Field
          className="admin-details__platforms"
          component={[Function]}
          id="platforms-group"
          legend="Platforms"
          name="platforms"
          validate={
            [
              [Function],
            ]
          }
        >
          <div
            style={
              {
                "display": "flex",
              }
            }
          >
            <Field
              component={[Function]}
              disabled={true}
              key="ios"
              label="iOS"
              name="platforms.ios"
            />
            <Connect(ReduxForm)
              formName="campaignDetails"
              platform="ios"
            />
          </div>
          <Field
            component={[Function]}
            editingMode={true}
            formName="campaignDetails"
            name="platforms_targeting"
            platform="ios"
          />
          <div
            style={
              {
                "display": "flex",
              }
            }
          >
            <Field
              component={[Function]}
              disabled={true}
              key="android"
              label="Android"
              name="platforms.android"
            />
            <Connect(ReduxForm)
              formName="campaignDetails"
              platform="android"
            />
          </div>
          <Field
            component={[Function]}
            editingMode={true}
            formName="campaignDetails"
            name="platforms_targeting"
            platform="android"
          />
        </Field>
        <LanguageCard
          applications={
            [
              {
                "applicationId": "nova",
                "contentful_space": "4szkx38resvm",
                "id": 1,
                "name": "Nova Mobile",
                "platformIds": [
                  2,
                  3,
                ],
                "platforms": [
                  "iOS",
                  "Android",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                  3,
                  4,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                  "message",
                  "offer",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
              {
                "applicationId": "phoenix",
                "contentful_space": "4szkx38resvm",
                "id": 2,
                "name": "Phoenix",
                "platformIds": [
                  1,
                ],
                "platforms": [
                  "Web",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
            ]
          }
          formChange={[MockFunction]}
          formDisabled={true}
          formValues={
            {
              "advancedTargetingRelationship": "or",
              "application": "nova",
              "assignees": [
                {
                  "full_name": "John Doe",
                  "sid": "s0000001",
                },
              ],
              "container": "offers-and-programs",
              "content_id": "test-content-id",
              "content_type": "targetedCampaign",
              "disabled": false,
              "dismissable_flag": false,
              "end_at": "2025-01-01T00:00:00.000Z",
              "external_ref": "ABC123",
              "id": "test-campaign-id",
              "mass_targeting": {
                "by_product": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                  ],
                },
                "by_scene_points": {
                  "points": 9320,
                  "targeting_criteria": "greater",
                },
                "enrollment_status": [],
                "languages": [],
                "product_pages": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                    {
                      "code": "out-of-date-product",
                      "ownership": "B",
                    },
                  ],
                },
              },
              "name": "test-campaign-title",
              "ownershipRelationship": "or",
              "pages": [
                "accounts",
                "account-key",
              ],
              "platforms": {
                "android": true,
                "ios": true,
              },
              "platforms_targeting": [],
              "start_at": "2021-05-01T00:00:00.000Z",
              "status": "draft",
              "type": "targeted",
            }
          }
          isCampaign={true}
        />
      </f>
      <div
        className="admin-details__placement"
      >
        <Field
          component={[Function]}
          name="contentful"
        >
          <Connect(ReduxForm)
            access={
              {
                "containers": {
                  "atlantis": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "nova": {
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "phoenix": {
                    "view": [
                      "phoenix-alert",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                },
                "pages": {
                  "atlantis": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "nova": {
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "phoenix": {
                    "view": [
                      "lgn-12345",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                },
                "ruleSubTypes": {
                  "atlantis": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                  "nova": {
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                  },
                  "phoenix": {
                    "view": [
                      "targeted",
                      "mass",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                },
              }
            }
            applications={
              [
                {
                  "applicationId": "nova",
                  "contentful_space": "4szkx38resvm",
                  "id": 1,
                  "name": "Nova Mobile",
                  "platformIds": [
                    2,
                    3,
                  ],
                  "platforms": [
                    "iOS",
                    "Android",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                    3,
                    4,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                    "message",
                    "offer",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
                {
                  "applicationId": "phoenix",
                  "contentful_space": "4szkx38resvm",
                  "id": 2,
                  "name": "Phoenix",
                  "platformIds": [
                    1,
                  ],
                  "platforms": [
                    "Web",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
              ]
            }
            containers={
              {
                "1": {
                  "application": 1,
                  "containerId": "my-activity",
                  "content_type": [
                    "standingCampaign",
                  ],
                  "id": 1,
                  "name": "my-activity",
                  "pages": [
                    1,
                  ],
                  "rule_type": "campaign",
                  "status": true,
                },
                "2": {
                  "application": 1,
                  "containerId": "offers-and-programs",
                  "content_type": [
                    "targetedCampaign",
                  ],
                  "id": 2,
                  "name": "offers-and-programs",
                  "pages": [
                    2,
                    3,
                  ],
                  "rule_type": "campaign",
                  "status": true,
                },
              }
            }
            deleteContent={[MockFunction]}
            formDisabled={true}
            formValues={
              {
                "advancedTargetingRelationship": "or",
                "application": "nova",
                "assignees": [
                  {
                    "full_name": "John Doe",
                    "sid": "s0000001",
                  },
                ],
                "container": "offers-and-programs",
                "content_id": "test-content-id",
                "content_type": "targetedCampaign",
                "disabled": false,
                "dismissable_flag": false,
                "end_at": "2025-01-01T00:00:00.000Z",
                "external_ref": "ABC123",
                "id": "test-campaign-id",
                "mass_targeting": {
                  "by_product": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                    ],
                  },
                  "by_scene_points": {
                    "points": 9320,
                    "targeting_criteria": "greater",
                  },
                  "enrollment_status": [],
                  "languages": [],
                  "product_pages": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                      {
                        "code": "out-of-date-product",
                        "ownership": "B",
                      },
                    ],
                  },
                },
                "name": "test-campaign-title",
                "ownershipRelationship": "or",
                "pages": [
                  "accounts",
                  "account-key",
                ],
                "platforms": {
                  "android": true,
                  "ios": true,
                },
                "platforms_targeting": [],
                "start_at": "2021-05-01T00:00:00.000Z",
                "status": "draft",
                "type": "targeted",
              }
            }
            handleChange={[MockFunction]}
            openContentModal={[MockFunction]}
            pages={
              {
                "isLoading": false,
                "items": [
                  {
                    "application": 1,
                    "containers": [
                      1,
                      68,
                    ],
                    "description": "NOVA mobile - My Activity tab",
                    "id": 1,
                    "name": "activities",
                    "pageId": "activities",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                      37,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 2,
                    "name": "accounts",
                    "pageId": "accounts",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 3,
                    "name": "account-key",
                    "pageId": "account-key",
                    "status": true,
                  },
                ],
              }
            }
            productBook={[]}
            type="campaign"
          />
        </Field>
      </div>
      <div
        className="admin-details__action-bar"
      >
        <f
          onClick={[MockFunction]}
          type="button"
        >
          Cancel
        </f>
        <div
          className="admin-details__action-buttons"
        >
          <d
            className="admin-details__action-button"
            disabled={false}
            labelPadding={36}
            onClick={[MockFunction]}
            size="regular"
          >
            Save as Draft
          </d>
        </div>
      </div>
    </form>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[MockFunction]}
      primaryButtonLabel="Delete"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      This 
      campaign
       will be deleted.
    </g>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[Function]}
      primaryButtonLabel="Continue"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      <p>
        The container, page, and content combination you have chosen will no longer be valid for the new application you have selected.
      </p>
      <br />
      <p>
        To continue with the current application, you will have to select new content.
      </p>
    </g>
    <Connect(ReduxForm)
      container={
        {
          "application": 1,
          "containerId": "offers-and-programs",
          "content_type": [
            "targetedCampaign",
          ],
          "id": 2,
          "name": "offers-and-programs",
          "pages": [
            2,
            3,
          ],
          "rule_type": "campaign",
          "status": true,
        }
      }
      contentfulSpace="4szkx38resvm"
      isDismissible={false}
      onSave={[MockFunction]}
      pages={
        {
          "isLoading": false,
          "items": [
            {
              "application": 1,
              "containers": [
                1,
                68,
              ],
              "description": "NOVA mobile - My Activity tab",
              "id": 1,
              "name": "activities",
              "pageId": "activities",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
                37,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 2,
              "name": "accounts",
              "pageId": "accounts",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 3,
              "name": "account-key",
              "pageId": "account-key",
              "status": true,
            },
          ],
        }
      }
      type="campaign"
    />
  </div>
</Fragment>
`;

exports[`Details Snapshot - View a Campaign (published status) Snapshot 1`] = `
<Fragment>
  <div
    className="admin-details__assignment-popup"
  >
    <f
      headline="This campaign needs to be assigned"
      isModalVisible={false}
      isOnlyClosedByButton={false}
      message=""
      setModalVisible={[Function]}
    >
      <AssignmentAutosuggest
        data={[]}
        dataLegend={
          {
            "keyName": "sid",
            "valueName": "fullName",
          }
        }
        editable={true}
        fromPopup={true}
        onChange={[Function]}
      />
      <div
        className="admin-details__modal-footer"
      >
        <d
          className="admin-details__modal-button"
          disabled={false}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Cancel
        </d>
        <d
          className="admin-details__modal-button"
          disabled={true}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Submit
        </d>
      </div>
    </f>
  </div>
  <div
    className="admin-details"
  >
    <form
      onSubmit={[Function]}
    >
       
      <div
        className="admin-details__action-bar"
      >
        <f
          bold={false}
          className="admin-details__header"
          color="black"
          component="h1"
          italic={false}
        >
          View a Campaign
        </f>
        <div
          className="admin-details__action-buttons"
        >
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[MockFunction]}
            type="caution"
          >
            Duplicate
          </d>
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={null}
            type="caution"
          >
            Deactivate
          </d>
        </div>
      </div>
      <f
        bold={false}
        color="black"
        component="h3"
        italic={false}
      >
        Current Status: 
        <StatusBadge
          disabled={false}
          endTime="2025-01-01T00:00:00.000Z"
          startTime="2021-05-01T00:00:00.000Z"
          status="published"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign
           Information
        </u>
        <Field
          autoComplete="off"
          className="admin-details__field"
          component={[Function]}
          disabled={true}
          label="Campaign Title"
          name="name"
          placeholder="Campaign Title"
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
        <Field
          className="admin-details__field"
          component={[Function]}
          disabled={true}
          label="Application"
          name="application"
          onChange={[Function]}
          options={
            [
              {
                "id": "nova",
                "name": "Nova Mobile",
              },
              {
                "id": "phoenix",
                "name": "Phoenix",
              },
            ]
          }
          placeholder="Select application…"
          validate={
            [
              [Function],
            ]
          }
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign Type
        </u>
        <d
          className={null}
          disabled={false}
          error={null}
          form={null}
          id="campaign-type-radio-group"
          inline={false}
          inputRef={null}
          labelLeft={false}
          legend=""
          name="type"
          tooltip={null}
        >
          <div
            className="admin-details__radio-container"
          >
            <Field
              component={[Function]}
              disabled={true}
              id="targeted"
              key="rule-sub-type-targeted"
              label="Targeted Campaign"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="mass"
              key="rule-sub-type-mass"
              label="Mass Campaign"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="message"
              key="rule-sub-type-message"
              label="Mass Message"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="offer"
              key="rule-sub-type-offer"
              label="Offer Campaign"
              name="type"
              onChange={[Function]}
            />
          </div>
        </d>
        <div
          className="admin-details__no-legend"
        >
          <Field
            component={[Function]}
            disabled={true}
            label="Urgent"
            name="urgent"
          />
        </div>
        <Field
          component={[Function]}
          disabled={true}
          label="Dismissible (N/A for Inbox Updates)"
          name="dismissable_flag"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting by Campaign ID
        </u>
        <Field
          autoComplete="off"
          component={[Function]}
          disabled={true}
          label="KT/PEGA Campaign ID"
          name="external_ref"
          placeholder="Campaign ID"
          required={true}
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
      </f>
      <Card2SV
        formDisabled={true}
        formValues={
          {
            "advancedTargetingRelationship": "or",
            "application": "nova",
            "assignees": [
              {
                "full_name": "John Doe",
                "sid": "s0000001",
              },
            ],
            "container": "offers-and-programs",
            "content_id": "test-content-id",
            "content_type": "targetedCampaign",
            "disabled": false,
            "dismissable_flag": false,
            "end_at": "2025-01-01T00:00:00.000Z",
            "external_ref": "ABC123",
            "id": "test-campaign-id",
            "mass_targeting": {
              "by_product": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                ],
              },
              "by_scene_points": {
                "points": 9320,
                "targeting_criteria": "greater",
              },
              "enrollment_status": [],
              "languages": [],
              "product_pages": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                  {
                    "code": "out-of-date-product",
                    "ownership": "B",
                  },
                ],
              },
            },
            "name": "test-campaign-title",
            "ownershipRelationship": "or",
            "pages": [
              "accounts",
              "account-key",
            ],
            "platforms": {
              "android": true,
              "ios": true,
            },
            "platforms_targeting": [],
            "start_at": "2021-05-01T00:00:00.000Z",
            "status": "published",
            "type": "targeted",
          }
        }
        isCampaign={true}
      />
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting Dimension
        </u>
        <div
          className="admin-details__date-fields"
        >
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={true}
            label="Start date"
            name="start_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={true}
            label="End date"
            name="end_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
        </div>
        <Field
          className="admin-details__platforms"
          component={[Function]}
          id="platforms-group"
          legend="Platforms"
          name="platforms"
          validate={
            [
              [Function],
            ]
          }
        >
          <div
            style={
              {
                "display": "flex",
              }
            }
          >
            <Field
              component={[Function]}
              disabled={true}
              key="ios"
              label="iOS"
              name="platforms.ios"
            />
          </div>
          <Field
            component={[Function]}
            editingMode={false}
            formName="campaignDetails"
            name="platforms_targeting"
            platform="ios"
          />
          <div
            style={
              {
                "display": "flex",
              }
            }
          >
            <Field
              component={[Function]}
              disabled={true}
              key="android"
              label="Android"
              name="platforms.android"
            />
          </div>
          <Field
            component={[Function]}
            editingMode={false}
            formName="campaignDetails"
            name="platforms_targeting"
            platform="android"
          />
        </Field>
        <LanguageCard
          applications={
            [
              {
                "applicationId": "nova",
                "contentful_space": "4szkx38resvm",
                "id": 1,
                "name": "Nova Mobile",
                "platformIds": [
                  2,
                  3,
                ],
                "platforms": [
                  "iOS",
                  "Android",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                  3,
                  4,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                  "message",
                  "offer",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
              {
                "applicationId": "phoenix",
                "contentful_space": "4szkx38resvm",
                "id": 2,
                "name": "Phoenix",
                "platformIds": [
                  1,
                ],
                "platforms": [
                  "Web",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
            ]
          }
          formChange={[MockFunction]}
          formDisabled={true}
          formValues={
            {
              "advancedTargetingRelationship": "or",
              "application": "nova",
              "assignees": [
                {
                  "full_name": "John Doe",
                  "sid": "s0000001",
                },
              ],
              "container": "offers-and-programs",
              "content_id": "test-content-id",
              "content_type": "targetedCampaign",
              "disabled": false,
              "dismissable_flag": false,
              "end_at": "2025-01-01T00:00:00.000Z",
              "external_ref": "ABC123",
              "id": "test-campaign-id",
              "mass_targeting": {
                "by_product": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                  ],
                },
                "by_scene_points": {
                  "points": 9320,
                  "targeting_criteria": "greater",
                },
                "enrollment_status": [],
                "languages": [],
                "product_pages": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                    {
                      "code": "out-of-date-product",
                      "ownership": "B",
                    },
                  ],
                },
              },
              "name": "test-campaign-title",
              "ownershipRelationship": "or",
              "pages": [
                "accounts",
                "account-key",
              ],
              "platforms": {
                "android": true,
                "ios": true,
              },
              "platforms_targeting": [],
              "start_at": "2021-05-01T00:00:00.000Z",
              "status": "published",
              "type": "targeted",
            }
          }
          isCampaign={true}
        />
      </f>
      <div
        className="admin-details__placement"
      >
        <Field
          component={[Function]}
          name="contentful"
        >
          <Connect(ReduxForm)
            access={
              {
                "containers": {
                  "atlantis": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "phoenix-alert",
                    ],
                    "view": [
                      "phoenix-alert",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                },
                "pages": {
                  "atlantis": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "lgn-12345",
                    ],
                    "view": [
                      "lgn-12345",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                },
                "ruleSubTypes": {
                  "atlantis": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "targeted",
                      "mass",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                },
              }
            }
            applications={
              [
                {
                  "applicationId": "nova",
                  "contentful_space": "4szkx38resvm",
                  "id": 1,
                  "name": "Nova Mobile",
                  "platformIds": [
                    2,
                    3,
                  ],
                  "platforms": [
                    "iOS",
                    "Android",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                    3,
                    4,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                    "message",
                    "offer",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
                {
                  "applicationId": "phoenix",
                  "contentful_space": "4szkx38resvm",
                  "id": 2,
                  "name": "Phoenix",
                  "platformIds": [
                    1,
                  ],
                  "platforms": [
                    "Web",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
              ]
            }
            containers={
              {
                "1": {
                  "application": 1,
                  "containerId": "my-activity",
                  "content_type": [
                    "standingCampaign",
                  ],
                  "id": 1,
                  "name": "my-activity",
                  "pages": [
                    1,
                  ],
                  "rule_type": "campaign",
                  "status": true,
                },
                "2": {
                  "application": 1,
                  "containerId": "offers-and-programs",
                  "content_type": [
                    "targetedCampaign",
                  ],
                  "id": 2,
                  "name": "offers-and-programs",
                  "pages": [
                    2,
                    3,
                  ],
                  "rule_type": "campaign",
                  "status": true,
                },
              }
            }
            deleteContent={[MockFunction]}
            formDisabled={true}
            formValues={
              {
                "advancedTargetingRelationship": "or",
                "application": "nova",
                "assignees": [
                  {
                    "full_name": "John Doe",
                    "sid": "s0000001",
                  },
                ],
                "container": "offers-and-programs",
                "content_id": "test-content-id",
                "content_type": "targetedCampaign",
                "disabled": false,
                "dismissable_flag": false,
                "end_at": "2025-01-01T00:00:00.000Z",
                "external_ref": "ABC123",
                "id": "test-campaign-id",
                "mass_targeting": {
                  "by_product": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                    ],
                  },
                  "by_scene_points": {
                    "points": 9320,
                    "targeting_criteria": "greater",
                  },
                  "enrollment_status": [],
                  "languages": [],
                  "product_pages": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                      {
                        "code": "out-of-date-product",
                        "ownership": "B",
                      },
                    ],
                  },
                },
                "name": "test-campaign-title",
                "ownershipRelationship": "or",
                "pages": [
                  "accounts",
                  "account-key",
                ],
                "platforms": {
                  "android": true,
                  "ios": true,
                },
                "platforms_targeting": [],
                "start_at": "2021-05-01T00:00:00.000Z",
                "status": "published",
                "type": "targeted",
              }
            }
            handleChange={[MockFunction]}
            openContentModal={[MockFunction]}
            pages={
              {
                "isLoading": false,
                "items": [
                  {
                    "application": 1,
                    "containers": [
                      1,
                      68,
                    ],
                    "description": "NOVA mobile - My Activity tab",
                    "id": 1,
                    "name": "activities",
                    "pageId": "activities",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                      37,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 2,
                    "name": "accounts",
                    "pageId": "accounts",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 3,
                    "name": "account-key",
                    "pageId": "account-key",
                    "status": true,
                  },
                ],
              }
            }
            productBook={[]}
            type="campaign"
          />
        </Field>
      </div>
      <div
        className="admin-details__action-bar"
      >
        <f
          onClick={[MockFunction]}
          type="button"
        >
          Cancel
        </f>
        <div
          className="admin-details__action-buttons"
        />
      </div>
    </form>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[MockFunction]}
      primaryButtonLabel="Delete"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      This 
      campaign
       will be deleted.
    </g>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[Function]}
      primaryButtonLabel="Continue"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      <p>
        The container, page, and content combination you have chosen will no longer be valid for the new application you have selected.
      </p>
      <br />
      <p>
        To continue with the current application, you will have to select new content.
      </p>
    </g>
    <Connect(ReduxForm)
      container={
        {
          "application": 1,
          "containerId": "offers-and-programs",
          "content_type": [
            "targetedCampaign",
          ],
          "id": 2,
          "name": "offers-and-programs",
          "pages": [
            2,
            3,
          ],
          "rule_type": "campaign",
          "status": true,
        }
      }
      contentfulSpace="4szkx38resvm"
      isDismissible={false}
      onSave={[MockFunction]}
      pages={
        {
          "isLoading": false,
          "items": [
            {
              "application": 1,
              "containers": [
                1,
                68,
              ],
              "description": "NOVA mobile - My Activity tab",
              "id": 1,
              "name": "activities",
              "pageId": "activities",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
                37,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 2,
              "name": "accounts",
              "pageId": "accounts",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 3,
              "name": "account-key",
              "pageId": "account-key",
              "status": true,
            },
          ],
        }
      }
      type="campaign"
    />
  </div>
</Fragment>
`;

exports[`Details Snapshot - View a Campaign (published status, inactive) Snapshot 1`] = `
<Fragment>
  <div
    className="admin-details__assignment-popup"
  >
    <f
      headline="This campaign needs to be assigned"
      isModalVisible={false}
      isOnlyClosedByButton={false}
      message=""
      setModalVisible={[Function]}
    >
      <AssignmentAutosuggest
        data={[]}
        dataLegend={
          {
            "keyName": "sid",
            "valueName": "fullName",
          }
        }
        editable={true}
        fromPopup={true}
        onChange={[Function]}
      />
      <div
        className="admin-details__modal-footer"
      >
        <d
          className="admin-details__modal-button"
          disabled={false}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Cancel
        </d>
        <d
          className="admin-details__modal-button"
          disabled={true}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Submit
        </d>
      </div>
    </f>
  </div>
  <div
    className="admin-details"
  >
    <form
      onSubmit={[Function]}
    >
       
      <div
        className="admin-details__action-bar"
      >
        <f
          bold={false}
          className="admin-details__header"
          color="black"
          component="h1"
          italic={false}
        >
          View a Campaign
        </f>
        <div
          className="admin-details__action-buttons"
        />
      </div>
      <f
        bold={false}
        color="black"
        component="h3"
        italic={false}
      >
        Current Status: 
        <StatusBadge
          disabled={true}
          endTime="2025-01-01T00:00:00.000Z"
          startTime="2021-05-01T00:00:00.000Z"
          status="published"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign
           Information
        </u>
        <Field
          autoComplete="off"
          className="admin-details__field"
          component={[Function]}
          disabled={true}
          label="Campaign Title"
          name="name"
          placeholder="Campaign Title"
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
        <Field
          className="admin-details__field"
          component={[Function]}
          disabled={true}
          label="Application"
          name="application"
          onChange={[Function]}
          options={
            [
              {
                "id": "nova",
                "name": "Nova Mobile",
              },
              {
                "id": "phoenix",
                "name": "Phoenix",
              },
            ]
          }
          placeholder="Select application…"
          validate={
            [
              [Function],
            ]
          }
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign Type
        </u>
        <d
          className={null}
          disabled={false}
          error={null}
          form={null}
          id="campaign-type-radio-group"
          inline={false}
          inputRef={null}
          labelLeft={false}
          legend=""
          name="type"
          tooltip={null}
        >
          <div
            className="admin-details__radio-container"
          >
            <Field
              component={[Function]}
              disabled={true}
              id="targeted"
              key="rule-sub-type-targeted"
              label="Targeted Campaign"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="mass"
              key="rule-sub-type-mass"
              label="Mass Campaign"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="message"
              key="rule-sub-type-message"
              label="Mass Message"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="offer"
              key="rule-sub-type-offer"
              label="Offer Campaign"
              name="type"
              onChange={[Function]}
            />
          </div>
        </d>
        <div
          className="admin-details__no-legend"
        >
          <Field
            component={[Function]}
            disabled={true}
            label="Urgent"
            name="urgent"
          />
        </div>
        <Field
          component={[Function]}
          disabled={true}
          label="Dismissible (N/A for Inbox Updates)"
          name="dismissable_flag"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting by Campaign ID
        </u>
        <Field
          autoComplete="off"
          component={[Function]}
          disabled={true}
          label="KT/PEGA Campaign ID"
          name="external_ref"
          placeholder="Campaign ID"
          required={true}
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
      </f>
      <Card2SV
        formDisabled={true}
        formValues={
          {
            "advancedTargetingRelationship": "or",
            "application": "nova",
            "assignees": [
              {
                "full_name": "John Doe",
                "sid": "s0000001",
              },
            ],
            "container": "offers-and-programs",
            "content_id": "test-content-id",
            "content_type": "targetedCampaign",
            "disabled": true,
            "dismissable_flag": false,
            "end_at": "2025-01-01T00:00:00.000Z",
            "external_ref": "ABC123",
            "id": "test-campaign-id",
            "mass_targeting": {
              "by_product": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                ],
              },
              "by_scene_points": {
                "points": 9320,
                "targeting_criteria": "greater",
              },
              "enrollment_status": [],
              "languages": [],
              "product_pages": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                  {
                    "code": "out-of-date-product",
                    "ownership": "B",
                  },
                ],
              },
            },
            "name": "test-campaign-title",
            "ownershipRelationship": "or",
            "pages": [
              "accounts",
              "account-key",
            ],
            "platforms": {
              "android": true,
              "ios": true,
            },
            "platforms_targeting": [],
            "start_at": "2021-05-01T00:00:00.000Z",
            "status": "published",
            "type": "targeted",
          }
        }
        isCampaign={true}
      />
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting Dimension
        </u>
        <div
          className="admin-details__date-fields"
        >
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={true}
            label="Start date"
            name="start_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={true}
            label="End date"
            name="end_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
        </div>
        <Field
          className="admin-details__platforms"
          component={[Function]}
          id="platforms-group"
          legend="Platforms"
          name="platforms"
          validate={
            [
              [Function],
            ]
          }
        >
          <div
            style={
              {
                "display": "flex",
              }
            }
          >
            <Field
              component={[Function]}
              disabled={true}
              key="ios"
              label="iOS"
              name="platforms.ios"
            />
          </div>
          <Field
            component={[Function]}
            editingMode={false}
            formName="campaignDetails"
            name="platforms_targeting"
            platform="ios"
          />
          <div
            style={
              {
                "display": "flex",
              }
            }
          >
            <Field
              component={[Function]}
              disabled={true}
              key="android"
              label="Android"
              name="platforms.android"
            />
          </div>
          <Field
            component={[Function]}
            editingMode={false}
            formName="campaignDetails"
            name="platforms_targeting"
            platform="android"
          />
        </Field>
        <LanguageCard
          applications={
            [
              {
                "applicationId": "nova",
                "contentful_space": "4szkx38resvm",
                "id": 1,
                "name": "Nova Mobile",
                "platformIds": [
                  2,
                  3,
                ],
                "platforms": [
                  "iOS",
                  "Android",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                  3,
                  4,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                  "message",
                  "offer",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
              {
                "applicationId": "phoenix",
                "contentful_space": "4szkx38resvm",
                "id": 2,
                "name": "Phoenix",
                "platformIds": [
                  1,
                ],
                "platforms": [
                  "Web",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
            ]
          }
          formChange={[MockFunction]}
          formDisabled={true}
          formValues={
            {
              "advancedTargetingRelationship": "or",
              "application": "nova",
              "assignees": [
                {
                  "full_name": "John Doe",
                  "sid": "s0000001",
                },
              ],
              "container": "offers-and-programs",
              "content_id": "test-content-id",
              "content_type": "targetedCampaign",
              "disabled": true,
              "dismissable_flag": false,
              "end_at": "2025-01-01T00:00:00.000Z",
              "external_ref": "ABC123",
              "id": "test-campaign-id",
              "mass_targeting": {
                "by_product": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                  ],
                },
                "by_scene_points": {
                  "points": 9320,
                  "targeting_criteria": "greater",
                },
                "enrollment_status": [],
                "languages": [],
                "product_pages": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                    {
                      "code": "out-of-date-product",
                      "ownership": "B",
                    },
                  ],
                },
              },
              "name": "test-campaign-title",
              "ownershipRelationship": "or",
              "pages": [
                "accounts",
                "account-key",
              ],
              "platforms": {
                "android": true,
                "ios": true,
              },
              "platforms_targeting": [],
              "start_at": "2021-05-01T00:00:00.000Z",
              "status": "published",
              "type": "targeted",
            }
          }
          isCampaign={true}
        />
      </f>
      <div
        className="admin-details__placement"
      >
        <Field
          component={[Function]}
          name="contentful"
        >
          <Connect(ReduxForm)
            access={
              {
                "containers": {
                  "atlantis": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "phoenix-alert",
                    ],
                    "view": [
                      "phoenix-alert",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                },
                "pages": {
                  "atlantis": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "lgn-12345",
                    ],
                    "view": [
                      "lgn-12345",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                },
                "ruleSubTypes": {
                  "atlantis": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "targeted",
                      "mass",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                },
              }
            }
            applications={
              [
                {
                  "applicationId": "nova",
                  "contentful_space": "4szkx38resvm",
                  "id": 1,
                  "name": "Nova Mobile",
                  "platformIds": [
                    2,
                    3,
                  ],
                  "platforms": [
                    "iOS",
                    "Android",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                    3,
                    4,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                    "message",
                    "offer",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
                {
                  "applicationId": "phoenix",
                  "contentful_space": "4szkx38resvm",
                  "id": 2,
                  "name": "Phoenix",
                  "platformIds": [
                    1,
                  ],
                  "platforms": [
                    "Web",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
              ]
            }
            containers={
              {
                "1": {
                  "application": 1,
                  "containerId": "my-activity",
                  "content_type": [
                    "standingCampaign",
                  ],
                  "id": 1,
                  "name": "my-activity",
                  "pages": [
                    1,
                  ],
                  "rule_type": "campaign",
                  "status": true,
                },
                "2": {
                  "application": 1,
                  "containerId": "offers-and-programs",
                  "content_type": [
                    "targetedCampaign",
                  ],
                  "id": 2,
                  "name": "offers-and-programs",
                  "pages": [
                    2,
                    3,
                  ],
                  "rule_type": "campaign",
                  "status": true,
                },
              }
            }
            deleteContent={[MockFunction]}
            formDisabled={true}
            formValues={
              {
                "advancedTargetingRelationship": "or",
                "application": "nova",
                "assignees": [
                  {
                    "full_name": "John Doe",
                    "sid": "s0000001",
                  },
                ],
                "container": "offers-and-programs",
                "content_id": "test-content-id",
                "content_type": "targetedCampaign",
                "disabled": true,
                "dismissable_flag": false,
                "end_at": "2025-01-01T00:00:00.000Z",
                "external_ref": "ABC123",
                "id": "test-campaign-id",
                "mass_targeting": {
                  "by_product": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                    ],
                  },
                  "by_scene_points": {
                    "points": 9320,
                    "targeting_criteria": "greater",
                  },
                  "enrollment_status": [],
                  "languages": [],
                  "product_pages": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                      {
                        "code": "out-of-date-product",
                        "ownership": "B",
                      },
                    ],
                  },
                },
                "name": "test-campaign-title",
                "ownershipRelationship": "or",
                "pages": [
                  "accounts",
                  "account-key",
                ],
                "platforms": {
                  "android": true,
                  "ios": true,
                },
                "platforms_targeting": [],
                "start_at": "2021-05-01T00:00:00.000Z",
                "status": "published",
                "type": "targeted",
              }
            }
            handleChange={[MockFunction]}
            openContentModal={[MockFunction]}
            pages={
              {
                "isLoading": false,
                "items": [
                  {
                    "application": 1,
                    "containers": [
                      1,
                      68,
                    ],
                    "description": "NOVA mobile - My Activity tab",
                    "id": 1,
                    "name": "activities",
                    "pageId": "activities",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                      37,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 2,
                    "name": "accounts",
                    "pageId": "accounts",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 3,
                    "name": "account-key",
                    "pageId": "account-key",
                    "status": true,
                  },
                ],
              }
            }
            productBook={[]}
            type="campaign"
          />
        </Field>
      </div>
      <div
        className="admin-details__action-bar"
      >
        <f
          onClick={[MockFunction]}
          type="button"
        >
          Cancel
        </f>
        <div
          className="admin-details__action-buttons"
        />
      </div>
    </form>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[MockFunction]}
      primaryButtonLabel="Delete"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      This 
      campaign
       will be deleted.
    </g>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[Function]}
      primaryButtonLabel="Continue"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      <p>
        The container, page, and content combination you have chosen will no longer be valid for the new application you have selected.
      </p>
      <br />
      <p>
        To continue with the current application, you will have to select new content.
      </p>
    </g>
    <Connect(ReduxForm)
      container={
        {
          "application": 1,
          "containerId": "offers-and-programs",
          "content_type": [
            "targetedCampaign",
          ],
          "id": 2,
          "name": "offers-and-programs",
          "pages": [
            2,
            3,
          ],
          "rule_type": "campaign",
          "status": true,
        }
      }
      contentfulSpace="4szkx38resvm"
      isDismissible={false}
      onSave={[MockFunction]}
      pages={
        {
          "isLoading": false,
          "items": [
            {
              "application": 1,
              "containers": [
                1,
                68,
              ],
              "description": "NOVA mobile - My Activity tab",
              "id": 1,
              "name": "activities",
              "pageId": "activities",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
                37,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 2,
              "name": "accounts",
              "pageId": "accounts",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 3,
              "name": "account-key",
              "pageId": "account-key",
              "status": true,
            },
          ],
        }
      }
      type="campaign"
    />
  </div>
</Fragment>
`;

exports[`Details Snapshot - View a Campaign (submitted status) CCAU Snapshot 1`] = `
<Fragment>
  <div
    className="admin-details__assignment-popup"
  >
    <f
      headline="This campaign needs to be assigned"
      isModalVisible={false}
      isOnlyClosedByButton={false}
      message=""
      setModalVisible={[Function]}
    >
      <AssignmentAutosuggest
        data={[]}
        dataLegend={
          {
            "keyName": "sid",
            "valueName": "fullName",
          }
        }
        editable={true}
        fromPopup={true}
        onChange={[Function]}
      />
      <div
        className="admin-details__modal-footer"
      >
        <d
          className="admin-details__modal-button"
          disabled={false}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Cancel
        </d>
        <d
          className="admin-details__modal-button"
          disabled={true}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Submit
        </d>
      </div>
    </f>
  </div>
  <div
    className="admin-details"
  >
    <form
      onSubmit={[Function]}
    >
       
      <div
        className="admin-details__action-bar"
      >
        <f
          bold={false}
          className="admin-details__header"
          color="black"
          component="h1"
          italic={false}
        >
          View a Campaign
        </f>
        <div
          className="admin-details__action-buttons"
        />
      </div>
      <f
        bold={false}
        color="black"
        component="h3"
        italic={false}
      >
        Current Status: 
        <StatusBadge
          disabled={false}
          endTime="2025-01-01T00:00:00.000Z"
          startTime="2021-05-01T00:00:00.000Z"
          status="submitted"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <div>
          <AssignmentAutosuggest
            data={[]}
            dataLegend={
              {
                "keyName": "sid",
                "valueName": "fullName",
              }
            }
            editable={false}
            initialSelection={
              [
                {
                  "fullName": "John Doe",
                  "full_name": "John Doe",
                  "sid": "s0000001",
                },
              ]
            }
            noLock={true}
            onChange={[Function]}
          />
        </div>
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign
           Information
        </u>
        <Field
          autoComplete="off"
          className="admin-details__field"
          component={[Function]}
          disabled={true}
          label="Campaign Title"
          name="name"
          placeholder="Campaign Title"
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
        <Field
          className="admin-details__field"
          component={[Function]}
          disabled={true}
          label="Application"
          name="application"
          onChange={[Function]}
          options={[]}
          placeholder="Select application…"
          validate={
            [
              [Function],
            ]
          }
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign Type
        </u>
        <d
          className={null}
          disabled={false}
          error={null}
          form={null}
          id="campaign-type-radio-group"
          inline={false}
          inputRef={null}
          labelLeft={false}
          legend=""
          name="type"
          tooltip={null}
        >
          <div
            className="admin-details__radio-container"
          >
            <Field
              component={[Function]}
              disabled={true}
              id="targeted"
              key="rule-sub-type-targeted"
              label="Targeted Campaign"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="mass"
              key="rule-sub-type-mass"
              label="Mass Campaign"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="message"
              key="rule-sub-type-message"
              label="Mass Message"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="offer"
              key="rule-sub-type-offer"
              label="Offer Campaign"
              name="type"
              onChange={[Function]}
            />
          </div>
        </d>
        <div
          className="admin-details__no-legend"
        >
          <Field
            component={[Function]}
            disabled={true}
            label="Urgent"
            name="urgent"
          />
        </div>
        <Field
          component={[Function]}
          disabled={true}
          label="Dismissible (N/A for Inbox Updates)"
          name="dismissable_flag"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting by Campaign ID
        </u>
        <Field
          autoComplete="off"
          component={[Function]}
          disabled={true}
          label="Campaign ID"
          name="external_ref"
          placeholder="Campaign ID"
          required={true}
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
      </f>
      <Card2SV
        formDisabled={true}
        formValues={
          {
            "advancedTargetingRelationship": "or",
            "application": "nova",
            "assignees": [
              {
                "full_name": "John Doe",
                "sid": "s0000001",
              },
            ],
            "container": "offers-and-programs",
            "content_id": "test-content-id",
            "content_type": "targetedCampaign",
            "disabled": false,
            "dismissable_flag": false,
            "end_at": "2025-01-01T00:00:00.000Z",
            "external_ref": "ABC123",
            "id": "test-campaign-id",
            "mass_targeting": {
              "by_product": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                ],
              },
              "by_scene_points": {
                "points": 9320,
                "targeting_criteria": "greater",
              },
              "enrollment_status": [],
              "languages": [],
              "product_pages": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                  {
                    "code": "out-of-date-product",
                    "ownership": "B",
                  },
                ],
              },
            },
            "name": "test-campaign-title",
            "ownershipRelationship": "or",
            "pages": [
              "accounts",
              "account-key",
            ],
            "platforms": {
              "android": true,
              "ios": true,
            },
            "platforms_targeting": [],
            "start_at": "2021-05-01T00:00:00.000Z",
            "status": "submitted",
            "type": "targeted",
          }
        }
        isCampaign={true}
      />
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting Dimension
        </u>
        <div
          className="admin-details__date-fields"
        >
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={true}
            label="Start date"
            name="start_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={true}
            label="End date"
            name="end_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
        </div>
        <Field
          className="admin-details__platforms"
          component={[Function]}
          id="platforms-group"
          legend="Platforms"
          name="platforms"
          validate={
            [
              [Function],
            ]
          }
        />
        <LanguageCard
          applications={
            [
              {
                "applicationId": "nova",
                "contentful_space": "4szkx38resvm",
                "id": 1,
                "name": "Nova Mobile",
                "platformIds": [
                  2,
                  3,
                ],
                "platforms": [
                  "iOS",
                  "Android",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                  3,
                  4,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                  "message",
                  "offer",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
              {
                "applicationId": "phoenix",
                "contentful_space": "4szkx38resvm",
                "id": 2,
                "name": "Phoenix",
                "platformIds": [
                  1,
                ],
                "platforms": [
                  "Web",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
            ]
          }
          formChange={[MockFunction]}
          formDisabled={true}
          formValues={
            {
              "advancedTargetingRelationship": "or",
              "application": "nova",
              "assignees": [
                {
                  "full_name": "John Doe",
                  "sid": "s0000001",
                },
              ],
              "container": "offers-and-programs",
              "content_id": "test-content-id",
              "content_type": "targetedCampaign",
              "disabled": false,
              "dismissable_flag": false,
              "end_at": "2025-01-01T00:00:00.000Z",
              "external_ref": "ABC123",
              "id": "test-campaign-id",
              "mass_targeting": {
                "by_product": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                  ],
                },
                "by_scene_points": {
                  "points": 9320,
                  "targeting_criteria": "greater",
                },
                "enrollment_status": [],
                "languages": [],
                "product_pages": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                    {
                      "code": "out-of-date-product",
                      "ownership": "B",
                    },
                  ],
                },
              },
              "name": "test-campaign-title",
              "ownershipRelationship": "or",
              "pages": [
                "accounts",
                "account-key",
              ],
              "platforms": {
                "android": true,
                "ios": true,
              },
              "platforms_targeting": [],
              "start_at": "2021-05-01T00:00:00.000Z",
              "status": "submitted",
              "type": "targeted",
            }
          }
          isCampaign={true}
        />
      </f>
      <div
        className="admin-details__placement"
      >
        <Field
          component={[Function]}
          name="contentful"
        >
          <Connect(ReduxForm)
            access={
              {
                "containers": {
                  "atlantis": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "phoenix-alert",
                    ],
                    "view": [
                      "phoenix-alert",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                },
                "pages": {
                  "atlantis": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "lgn-12345",
                    ],
                    "view": [
                      "lgn-12345",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                },
                "ruleSubTypes": {
                  "atlantis": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "targeted",
                      "mass",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                },
              }
            }
            applications={
              [
                {
                  "applicationId": "nova",
                  "contentful_space": "4szkx38resvm",
                  "id": 1,
                  "name": "Nova Mobile",
                  "platformIds": [
                    2,
                    3,
                  ],
                  "platforms": [
                    "iOS",
                    "Android",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                    3,
                    4,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                    "message",
                    "offer",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
                {
                  "applicationId": "phoenix",
                  "contentful_space": "4szkx38resvm",
                  "id": 2,
                  "name": "Phoenix",
                  "platformIds": [
                    1,
                  ],
                  "platforms": [
                    "Web",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
              ]
            }
            containers={{}}
            deleteContent={[MockFunction]}
            formDisabled={true}
            formValues={
              {
                "advancedTargetingRelationship": "or",
                "application": "nova",
                "assignees": [
                  {
                    "full_name": "John Doe",
                    "sid": "s0000001",
                  },
                ],
                "container": "offers-and-programs",
                "content_id": "test-content-id",
                "content_type": "targetedCampaign",
                "disabled": false,
                "dismissable_flag": false,
                "end_at": "2025-01-01T00:00:00.000Z",
                "external_ref": "ABC123",
                "id": "test-campaign-id",
                "mass_targeting": {
                  "by_product": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                    ],
                  },
                  "by_scene_points": {
                    "points": 9320,
                    "targeting_criteria": "greater",
                  },
                  "enrollment_status": [],
                  "languages": [],
                  "product_pages": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                      {
                        "code": "out-of-date-product",
                        "ownership": "B",
                      },
                    ],
                  },
                },
                "name": "test-campaign-title",
                "ownershipRelationship": "or",
                "pages": [
                  "accounts",
                  "account-key",
                ],
                "platforms": {
                  "android": true,
                  "ios": true,
                },
                "platforms_targeting": [],
                "start_at": "2021-05-01T00:00:00.000Z",
                "status": "submitted",
                "type": "targeted",
              }
            }
            handleChange={[MockFunction]}
            openContentModal={[MockFunction]}
            pages={
              {
                "isLoading": false,
                "items": [
                  {
                    "application": 1,
                    "containers": [
                      1,
                      68,
                    ],
                    "description": "NOVA mobile - My Activity tab",
                    "id": 1,
                    "name": "activities",
                    "pageId": "activities",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                      37,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 2,
                    "name": "accounts",
                    "pageId": "accounts",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 3,
                    "name": "account-key",
                    "pageId": "account-key",
                    "status": true,
                  },
                ],
              }
            }
            productBook={[]}
            type="ccau_campaign"
          />
        </Field>
      </div>
      <div
        className="admin-details__action-bar"
      >
        <f
          onClick={[MockFunction]}
          type="button"
        >
          Cancel
        </f>
        <div
          className="admin-details__action-buttons"
        />
      </div>
    </form>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[MockFunction]}
      primaryButtonLabel="Delete"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      This 
      Campaign
       will be deleted.
    </g>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[Function]}
      primaryButtonLabel="Continue"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      <p>
        The container, page, and content combination you have chosen will no longer be valid for the new application you have selected.
      </p>
      <br />
      <p>
        To continue with the current application, you will have to select new content.
      </p>
    </g>
    <Connect(ReduxForm)
      isDismissible={false}
      onSave={[MockFunction]}
      pages={
        {
          "isLoading": false,
          "items": [
            {
              "application": 1,
              "containers": [
                1,
                68,
              ],
              "description": "NOVA mobile - My Activity tab",
              "id": 1,
              "name": "activities",
              "pageId": "activities",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
                37,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 2,
              "name": "accounts",
              "pageId": "accounts",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 3,
              "name": "account-key",
              "pageId": "account-key",
              "status": true,
            },
          ],
        }
      }
      type="ccau_campaign"
    />
  </div>
</Fragment>
`;

exports[`Details Snapshot - View a Campaign (submitted status) Snapshot 1`] = `
<Fragment>
  <div
    className="admin-details__assignment-popup"
  >
    <f
      headline="This campaign needs to be assigned"
      isModalVisible={false}
      isOnlyClosedByButton={false}
      message=""
      setModalVisible={[Function]}
    >
      <AssignmentAutosuggest
        data={[]}
        dataLegend={
          {
            "keyName": "sid",
            "valueName": "fullName",
          }
        }
        editable={true}
        fromPopup={true}
        onChange={[Function]}
      />
      <div
        className="admin-details__modal-footer"
      >
        <d
          className="admin-details__modal-button"
          disabled={false}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Cancel
        </d>
        <d
          className="admin-details__modal-button"
          disabled={true}
          labelPadding={36}
          onClick={[Function]}
          size="regular"
        >
          Submit
        </d>
      </div>
    </f>
  </div>
  <div
    className="admin-details"
  >
    <form
      onSubmit={[Function]}
    >
       
      <div
        className="admin-details__action-bar"
      >
        <f
          bold={false}
          className="admin-details__header"
          color="black"
          component="h1"
          italic={false}
        >
          View a Campaign
        </f>
        <div
          className="admin-details__action-buttons"
        >
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[MockFunction]}
            type="caution"
          >
            Duplicate
          </d>
          <d
            buttonType="button"
            className="admin-details__action-button"
            disabled={false}
            invertedFocusState="blue"
            onClick={[Function]}
            type="caution"
          >
            Delete
          </d>
        </div>
      </div>
      <f
        bold={false}
        color="black"
        component="h3"
        italic={false}
      >
        Current Status: 
        <StatusBadge
          disabled={false}
          endTime="2025-01-01T00:00:00.000Z"
          startTime="2021-05-01T00:00:00.000Z"
          status="submitted"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <div>
          <AssignmentAutosuggest
            data={[]}
            dataLegend={
              {
                "keyName": "sid",
                "valueName": "fullName",
              }
            }
            editable={false}
            initialSelection={
              [
                {
                  "fullName": "John Doe",
                  "full_name": "John Doe",
                  "sid": "s0000001",
                },
              ]
            }
            noLock={true}
            onChange={[Function]}
          />
        </div>
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign
           Information
        </u>
        <Field
          autoComplete="off"
          className="admin-details__field"
          component={[Function]}
          disabled={true}
          label="Campaign Title"
          name="name"
          placeholder="Campaign Title"
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
        <Field
          className="admin-details__field"
          component={[Function]}
          disabled={true}
          label="Application"
          name="application"
          onChange={[Function]}
          options={
            [
              {
                "id": "nova",
                "name": "Nova Mobile",
              },
              {
                "id": "phoenix",
                "name": "Phoenix",
              },
            ]
          }
          placeholder="Select application…"
          validate={
            [
              [Function],
            ]
          }
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Campaign Type
        </u>
        <d
          className={null}
          disabled={false}
          error={null}
          form={null}
          id="campaign-type-radio-group"
          inline={false}
          inputRef={null}
          labelLeft={false}
          legend=""
          name="type"
          tooltip={null}
        >
          <div
            className="admin-details__radio-container"
          >
            <Field
              component={[Function]}
              disabled={true}
              id="targeted"
              key="rule-sub-type-targeted"
              label="Targeted Campaign"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="mass"
              key="rule-sub-type-mass"
              label="Mass Campaign"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="message"
              key="rule-sub-type-message"
              label="Mass Message"
              name="type"
              onChange={[Function]}
            />
            <Field
              component={[Function]}
              disabled={true}
              id="offer"
              key="rule-sub-type-offer"
              label="Offer Campaign"
              name="type"
              onChange={[Function]}
            />
          </div>
        </d>
        <div
          className="admin-details__no-legend"
        >
          <Field
            component={[Function]}
            disabled={true}
            label="Urgent"
            name="urgent"
          />
        </div>
        <Field
          component={[Function]}
          disabled={true}
          label="Dismissible (N/A for Inbox Updates)"
          name="dismissable_flag"
        />
      </f>
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting by Campaign ID
        </u>
        <Field
          autoComplete="off"
          component={[Function]}
          disabled={true}
          label="KT/PEGA Campaign ID"
          name="external_ref"
          placeholder="Campaign ID"
          required={true}
          validate={
            [
              [Function],
              [Function],
              [Function],
              [Function],
            ]
          }
        />
      </f>
      <Card2SV
        formDisabled={true}
        formValues={
          {
            "advancedTargetingRelationship": "or",
            "application": "nova",
            "assignees": [
              {
                "full_name": "John Doe",
                "sid": "s0000001",
              },
            ],
            "container": "offers-and-programs",
            "content_id": "test-content-id",
            "content_type": "targetedCampaign",
            "disabled": false,
            "dismissable_flag": false,
            "end_at": "2025-01-01T00:00:00.000Z",
            "external_ref": "ABC123",
            "id": "test-campaign-id",
            "mass_targeting": {
              "by_product": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                ],
              },
              "by_scene_points": {
                "points": 9320,
                "targeting_criteria": "greater",
              },
              "enrollment_status": [],
              "languages": [],
              "product_pages": {
                "any_of": [
                  {
                    "category": "banking",
                    "code": "DDA",
                    "description": "Current Account (B:DDA:CA)",
                    "id": "100060",
                    "ownership": "B",
                    "sub_code": "CA",
                  },
                  {
                    "category": "investing",
                    "code": "TFS",
                    "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                    "id": "340860",
                    "ownership": "B",
                    "sub_code": "SB",
                  },
                  {
                    "code": "out-of-date-product",
                    "ownership": "B",
                  },
                ],
              },
            },
            "name": "test-campaign-title",
            "ownershipRelationship": "or",
            "pages": [
              "accounts",
              "account-key",
            ],
            "platforms": {
              "android": true,
              "ios": true,
            },
            "platforms_targeting": [],
            "start_at": "2021-05-01T00:00:00.000Z",
            "status": "submitted",
            "type": "targeted",
          }
        }
        isCampaign={true}
      />
      <f
        className="admin-details__card"
        lgPadding={36}
        mdPadding={30}
        smPadding={24}
        type="floatLow"
        xsPadding={18}
      >
        <u
          className="admin-details__sub-header"
          color="black"
          component="h2"
          size={21}
        >
          Targeting Dimension
        </u>
        <div
          className="admin-details__date-fields"
        >
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={true}
            label="Start date"
            name="start_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
          <Field
            autoComplete="off"
            className="admin-details__date-field"
            component={[Function]}
            disabled={true}
            label="End date"
            name="end_at"
            placeholder="MM/DD/YYYY HH:MM AM/PM"
            required={true}
          />
        </div>
        <Field
          className="admin-details__platforms"
          component={[Function]}
          id="platforms-group"
          legend="Platforms"
          name="platforms"
          validate={
            [
              [Function],
            ]
          }
        >
          <div
            style={
              {
                "display": "flex",
              }
            }
          >
            <Field
              component={[Function]}
              disabled={true}
              key="ios"
              label="iOS"
              name="platforms.ios"
            />
          </div>
          <Field
            component={[Function]}
            editingMode={false}
            formName="campaignDetails"
            name="platforms_targeting"
            platform="ios"
          />
          <div
            style={
              {
                "display": "flex",
              }
            }
          >
            <Field
              component={[Function]}
              disabled={true}
              key="android"
              label="Android"
              name="platforms.android"
            />
          </div>
          <Field
            component={[Function]}
            editingMode={false}
            formName="campaignDetails"
            name="platforms_targeting"
            platform="android"
          />
        </Field>
        <LanguageCard
          applications={
            [
              {
                "applicationId": "nova",
                "contentful_space": "4szkx38resvm",
                "id": 1,
                "name": "Nova Mobile",
                "platformIds": [
                  2,
                  3,
                ],
                "platforms": [
                  "iOS",
                  "Android",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                  3,
                  4,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                  "message",
                  "offer",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
              {
                "applicationId": "phoenix",
                "contentful_space": "4szkx38resvm",
                "id": 2,
                "name": "Phoenix",
                "platformIds": [
                  1,
                ],
                "platforms": [
                  "Web",
                ],
                "ruleSubTypeIds": [
                  1,
                  2,
                ],
                "ruleSubTypes": [
                  "targeted",
                  "mass",
                ],
                "ruleTypeIds": [
                  1,
                  2,
                ],
                "ruleTypes": [
                  "alert",
                  "campaign",
                ],
                "rule_version": 1,
                "status": true,
              },
            ]
          }
          formChange={[MockFunction]}
          formDisabled={true}
          formValues={
            {
              "advancedTargetingRelationship": "or",
              "application": "nova",
              "assignees": [
                {
                  "full_name": "John Doe",
                  "sid": "s0000001",
                },
              ],
              "container": "offers-and-programs",
              "content_id": "test-content-id",
              "content_type": "targetedCampaign",
              "disabled": false,
              "dismissable_flag": false,
              "end_at": "2025-01-01T00:00:00.000Z",
              "external_ref": "ABC123",
              "id": "test-campaign-id",
              "mass_targeting": {
                "by_product": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                  ],
                },
                "by_scene_points": {
                  "points": 9320,
                  "targeting_criteria": "greater",
                },
                "enrollment_status": [],
                "languages": [],
                "product_pages": {
                  "any_of": [
                    {
                      "category": "banking",
                      "code": "DDA",
                      "description": "Current Account (B:DDA:CA)",
                      "id": "100060",
                      "ownership": "B",
                      "sub_code": "CA",
                    },
                    {
                      "category": "investing",
                      "code": "TFS",
                      "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                      "id": "340860",
                      "ownership": "B",
                      "sub_code": "SB",
                    },
                    {
                      "code": "out-of-date-product",
                      "ownership": "B",
                    },
                  ],
                },
              },
              "name": "test-campaign-title",
              "ownershipRelationship": "or",
              "pages": [
                "accounts",
                "account-key",
              ],
              "platforms": {
                "android": true,
                "ios": true,
              },
              "platforms_targeting": [],
              "start_at": "2021-05-01T00:00:00.000Z",
              "status": "submitted",
              "type": "targeted",
            }
          }
          isCampaign={true}
        />
      </f>
      <div
        className="admin-details__placement"
      >
        <Field
          component={[Function]}
          name="contentful"
        >
          <Connect(ReduxForm)
            access={
              {
                "containers": {
                  "atlantis": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "phoenix-alert",
                    ],
                    "view": [
                      "phoenix-alert",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "offers-and-programs",
                    ],
                    "view": [
                      "offers-and-programs",
                    ],
                  },
                },
                "pages": {
                  "atlantis": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "lgn-12345",
                    ],
                    "view": [
                      "lgn-12345",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "accounts",
                      "account-key",
                    ],
                    "view": [
                      "accounts",
                      "account-key",
                    ],
                  },
                },
                "ruleSubTypes": {
                  "atlantis": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                  "nova": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "offer",
                    ],
                  },
                  "phoenix": {
                    "manage": [
                      "targeted",
                      "mass",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                    ],
                  },
                  "starburst": {
                    "manage": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                    "view": [
                      "targeted",
                      "mass",
                      "message",
                      "wealth",
                    ],
                  },
                },
              }
            }
            applications={
              [
                {
                  "applicationId": "nova",
                  "contentful_space": "4szkx38resvm",
                  "id": 1,
                  "name": "Nova Mobile",
                  "platformIds": [
                    2,
                    3,
                  ],
                  "platforms": [
                    "iOS",
                    "Android",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                    3,
                    4,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                    "message",
                    "offer",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
                {
                  "applicationId": "phoenix",
                  "contentful_space": "4szkx38resvm",
                  "id": 2,
                  "name": "Phoenix",
                  "platformIds": [
                    1,
                  ],
                  "platforms": [
                    "Web",
                  ],
                  "ruleSubTypeIds": [
                    1,
                    2,
                  ],
                  "ruleSubTypes": [
                    "targeted",
                    "mass",
                  ],
                  "ruleTypeIds": [
                    1,
                    2,
                  ],
                  "ruleTypes": [
                    "alert",
                    "campaign",
                  ],
                  "rule_version": 1,
                  "status": true,
                },
              ]
            }
            containers={
              {
                "1": {
                  "application": 1,
                  "containerId": "my-activity",
                  "content_type": [
                    "standingCampaign",
                  ],
                  "id": 1,
                  "name": "my-activity",
                  "pages": [
                    1,
                  ],
                  "rule_type": "campaign",
                  "status": true,
                },
                "2": {
                  "application": 1,
                  "containerId": "offers-and-programs",
                  "content_type": [
                    "targetedCampaign",
                  ],
                  "id": 2,
                  "name": "offers-and-programs",
                  "pages": [
                    2,
                    3,
                  ],
                  "rule_type": "campaign",
                  "status": true,
                },
              }
            }
            deleteContent={[MockFunction]}
            formDisabled={true}
            formValues={
              {
                "advancedTargetingRelationship": "or",
                "application": "nova",
                "assignees": [
                  {
                    "full_name": "John Doe",
                    "sid": "s0000001",
                  },
                ],
                "container": "offers-and-programs",
                "content_id": "test-content-id",
                "content_type": "targetedCampaign",
                "disabled": false,
                "dismissable_flag": false,
                "end_at": "2025-01-01T00:00:00.000Z",
                "external_ref": "ABC123",
                "id": "test-campaign-id",
                "mass_targeting": {
                  "by_product": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                    ],
                  },
                  "by_scene_points": {
                    "points": 9320,
                    "targeting_criteria": "greater",
                  },
                  "enrollment_status": [],
                  "languages": [],
                  "product_pages": {
                    "any_of": [
                      {
                        "category": "banking",
                        "code": "DDA",
                        "description": "Current Account (B:DDA:CA)",
                        "id": "100060",
                        "ownership": "B",
                        "sub_code": "CA",
                      },
                      {
                        "category": "investing",
                        "code": "TFS",
                        "description": "Tax-Free Savings - BNS (B:TFS:SB)",
                        "id": "340860",
                        "ownership": "B",
                        "sub_code": "SB",
                      },
                      {
                        "code": "out-of-date-product",
                        "ownership": "B",
                      },
                    ],
                  },
                },
                "name": "test-campaign-title",
                "ownershipRelationship": "or",
                "pages": [
                  "accounts",
                  "account-key",
                ],
                "platforms": {
                  "android": true,
                  "ios": true,
                },
                "platforms_targeting": [],
                "start_at": "2021-05-01T00:00:00.000Z",
                "status": "submitted",
                "type": "targeted",
              }
            }
            handleChange={[MockFunction]}
            openContentModal={[MockFunction]}
            pages={
              {
                "isLoading": false,
                "items": [
                  {
                    "application": 1,
                    "containers": [
                      1,
                      68,
                    ],
                    "description": "NOVA mobile - My Activity tab",
                    "id": 1,
                    "name": "activities",
                    "pageId": "activities",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                      37,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 2,
                    "name": "accounts",
                    "pageId": "accounts",
                    "status": true,
                  },
                  {
                    "application": 1,
                    "containers": [
                      2,
                    ],
                    "description": "NOVA mobile - My Accounts tab",
                    "id": 3,
                    "name": "account-key",
                    "pageId": "account-key",
                    "status": true,
                  },
                ],
              }
            }
            productBook={[]}
            type="campaign"
          />
        </Field>
      </div>
      <div
        className="admin-details__action-bar"
      >
        <f
          onClick={[MockFunction]}
          type="button"
        >
          Cancel
        </f>
        <div
          className="admin-details__action-buttons"
        />
      </div>
    </form>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[MockFunction]}
      primaryButtonLabel="Delete"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      This 
      campaign
       will be deleted.
    </g>
    <g
      alwaysStackButtons={false}
      headline="Are you sure?"
      isModalVisible={false}
      primaryAction={[Function]}
      primaryButtonLabel="Continue"
      secondaryAction={[Function]}
      secondaryButtonLabel="Cancel"
      setModalVisible={[Function]}
      usePrimaryButtons={false}
      width="wide"
    >
      <p>
        The container, page, and content combination you have chosen will no longer be valid for the new application you have selected.
      </p>
      <br />
      <p>
        To continue with the current application, you will have to select new content.
      </p>
    </g>
    <Connect(ReduxForm)
      container={
        {
          "application": 1,
          "containerId": "offers-and-programs",
          "content_type": [
            "targetedCampaign",
          ],
          "id": 2,
          "name": "offers-and-programs",
          "pages": [
            2,
            3,
          ],
          "rule_type": "campaign",
          "status": true,
        }
      }
      contentfulSpace="4szkx38resvm"
      isDismissible={false}
      onSave={[MockFunction]}
      pages={
        {
          "isLoading": false,
          "items": [
            {
              "application": 1,
              "containers": [
                1,
                68,
              ],
              "description": "NOVA mobile - My Activity tab",
              "id": 1,
              "name": "activities",
              "pageId": "activities",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
                37,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 2,
              "name": "accounts",
              "pageId": "accounts",
              "status": true,
            },
            {
              "application": 1,
              "containers": [
                2,
              ],
              "description": "NOVA mobile - My Accounts tab",
              "id": 3,
              "name": "account-key",
              "pageId": "account-key",
              "status": true,
            },
          ],
        }
      }
      type="campaign"
    />
  </div>
</Fragment>
`;
