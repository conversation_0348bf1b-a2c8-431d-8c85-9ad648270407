import { access, permissions } from '../../utils/testing-library-utils-data';
import {
  getProductPlacements,
  canEditRule,
  canViewManageRuleType,
} from './utils';

describe('getProductPlacements', () => {
  const productBook = [
    {
      'category': 'banking',
      'code': 'DDA',
      'description': 'Current Account (B:DDA:CA)',
      'id': '100060',
      'ownership': 'B',
      'sub_code': 'CA',
    }, {
      'category': 'investing',
      'code': 'TFS',
      'description': 'Tax-Free Savings - BNS (B:TFS:SB)',
      'id': '340860',
      'ownership': 'B',
      'sub_code': 'SB',
    }, {
      'category': 'borrowing',
      'code': 'AFP',
      'description': 'Future Amex Platinum (B:AFP)',
      'id': '620020',
      'ownership': 'B',
    },
  ];

  const products = [
    { ...productBook[0], label: 'B:DDA:CA Current Account (B:DDA:CA)' },
    { ...productBook[1], label: 'B:TFS:SB Tax-Free Savings - BNS (B:TFS:SB)' },
  ];

  it('should retrieve product placements for targeted campaigns', () => {
    const campaignDetailsForm = {
      type: 'targeted',
      mass_targeting: { product_pages: { any_of: products } },
    };
    expect(getProductPlacements(campaignDetailsForm)).toStrictEqual(products);
  });

  it('should retrieve product placements for mass campaigns', () => {
    const campaignDetailsForm = { type: 'mass' };

    // mass campaign on new form
    campaignDetailsForm.advancedTargeting = { include: products };
    expect(getProductPlacements(campaignDetailsForm)).toStrictEqual(products);

    // mass campaign on prefilled form
    delete campaignDetailsForm.advancedTargeting;
    campaignDetailsForm.mass_targeting = { by_product: { any_of: products } };
    expect(getProductPlacements(campaignDetailsForm)).toStrictEqual(products);
  });

  it('should gracefully handle missing data', () => {
    // missing campaign form
    expect(getProductPlacements()).toStrictEqual([]);

    // missing campaign type
    expect(getProductPlacements({})).toStrictEqual([]);

    // target campaign missing products
    const campaignDetailsForm = { type: 'targeted', mass_targeting: { product_pages: { any_of: undefined } } };
    expect(getProductPlacements(campaignDetailsForm)).toStrictEqual([]);
    delete campaignDetailsForm.product_pages;
    expect(getProductPlacements(campaignDetailsForm)).toStrictEqual([]);
    delete campaignDetailsForm.mass_targeting;
    expect(getProductPlacements(campaignDetailsForm)).toStrictEqual([]);

    // mass campaign on new form missing products
    const emptyArrStr = JSON.stringify([]);
    campaignDetailsForm.type = 'mass';
    campaignDetailsForm.advancedTargeting = {};
    expect(getProductPlacements(campaignDetailsForm)).toStrictEqual([]);
    campaignDetailsForm.advancedTargeting.include = [];
    expect(getProductPlacements(campaignDetailsForm)).toStrictEqual([]);
    campaignDetailsForm.advancedTargeting.include.any_of = [];
    expect(JSON.stringify(getProductPlacements(campaignDetailsForm))).toStrictEqual(emptyArrStr);
    delete campaignDetailsForm.advancedTargeting;

    // mass campaign on prefilled form missing products
    campaignDetailsForm.mass_targeting = {};
    expect(JSON.stringify(getProductPlacements(campaignDetailsForm))).toStrictEqual(emptyArrStr);
    campaignDetailsForm.mass_targeting.by_product = {};
    expect(JSON.stringify(getProductPlacements(campaignDetailsForm))).toStrictEqual(emptyArrStr);
    campaignDetailsForm.mass_targeting.by_product.any_of = [];
    expect(JSON.stringify(getProductPlacements(campaignDetailsForm))).toStrictEqual(emptyArrStr);

    // mass messages campaign
    campaignDetailsForm.type = 'message';
    expect(getProductPlacements(campaignDetailsForm)).toStrictEqual([]);
  });
});

describe('canEditRule', () => {
  const mockCampaign = { application: 'nova', container: 'offers-and-programs', pages: [ 'accounts' ], external_ref: '12345', type: 'targeted' };
  const mockAlert = { application: 'nova', container: 'alert', pages: [ 'login' ] };
  const mockAlertWithoutPage = { application: 'nova', container: 'alert', pages: [ ] };
  const access = { containers: {}, pages: {}, ruleSubTypes: {} };
  const permissions = [];

  it('the user should not be able to edit or review or approve ', () => {
    const canEdit = canEditRule(access, permissions, 'campaign', mockCampaign);
    expect(canEdit).toStrictEqual({ canManage: false, canReview: false, canApprove: false });
  });

  it('the user should be able to edit only ', () => {
    const canEditAccess = { containers: { nova: { manage: [ 'offers-and-programs' ] } }, pages: { nova: { manage: [ 'accounts' ] } }, ruleSubTypes: { nova: { manage: [ 'targeted' ] } } };
    const canEditPermissions = { 'campaigns_manage': true };
    const canEdit = canEditRule(canEditAccess, canEditPermissions, 'campaign', mockCampaign);
    expect(canEdit).toStrictEqual({ canManage: true, canReview: false, canApprove: false });
  });

  it('the user should be able to review and edit ', () => {
    const canEditAccess = { containers: { nova: { manage: [ 'offers-and-programs' ] } }, pages: { nova: { manage: [ 'accounts' ] } }, ruleSubTypes: { nova: { manage: [ 'targeted' ] } } };
    const canEditPermissions = { 'campaigns_manage': true, 'campaigns_review': true };
    const canEdit = canEditRule(canEditAccess, canEditPermissions, 'campaign', mockCampaign);
    expect(canEdit).toStrictEqual({ canManage: true, canReview: true, canApprove: false });
  });

  it('the user should be able to approve  and review and edit ', () => {
    const canEditAccess = { containers: { nova: { manage: [ 'offers-and-programs' ] } }, pages: { nova: { manage: [ 'accounts' ] } }, ruleSubTypes: { nova: { manage: [ 'targeted' ] } } };
    const canEditPermissions = { 'campaigns_manage': true, 'campaigns_review': true, 'campaigns_approve': true };
    const canEdit = canEditRule(canEditAccess, canEditPermissions, 'campaign', mockCampaign);
    expect(canEdit).toStrictEqual({ canManage: true, canReview: true, canApprove: true });
  });

  it('the user should be able to approve  and review and edit CCAU', () => {
    const canEditAccess = { containers: { nova: { manage: [ 'offers-and-programs' ] } }, pages: { nova: { manage: [ 'accounts' ] } }, ruleSubTypes: { nova: { manage: [ 'targeted' ] } } };
    const canEditPermissions = { 'ccau_campaigns_manage': true, 'ccau_campaigns_review': true, 'ccau_campaigns_approve': true };
    const canEdit = canEditRule(canEditAccess, canEditPermissions, 'ccau_campaign', mockCampaign);
    expect(canEdit).toStrictEqual({ canManage: true, canReview: true, canApprove: true });
  });

  it('the user should not be able to do any action ', () => {
    const canEditAccess = { containers: { nova: { manage: [ 'offers-and-programs' ] } }, pages: { nova: { manage: [ 'pages' ] } }, ruleSubTypes: { nova: { manage: [ 'targeted' ] } } };
    const canEditPermissions = { 'campaigns_manage': true, 'campaigns_review': true, 'campaigns_approve': true };
    const canEdit = canEditRule(canEditAccess, canEditPermissions, 'campaign', mockCampaign);
    expect(canEdit).toStrictEqual({ canManage: false, canReview: false, canApprove: false });
  });

  it('the user should not be able to do any action CCAU', () => {
    const canEditAccess = { containers: { nova: { manage: [ 'offers-and-programs' ] } }, pages: { nova: { manage: [ 'pages' ] } }, ruleSubTypes: { nova: { manage: [ 'targeted' ] } } };
    const canEditPermissions = { 'ccau_campaigns_manage': true, 'ccau_campaigns_review': true, 'ccau_campaigns_approve': true };
    const canEdit = canEditRule(canEditAccess, canEditPermissions, 'ccau_campaign', mockCampaign);
    expect(canEdit).toStrictEqual({ canManage: false, canReview: false, canApprove: false });
  });

  it('the user should not be able to edit or approve - alert ', () => {
    const canEdit = canEditRule(access, permissions, 'alert', mockAlert);
    expect(canEdit).toStrictEqual({ canManage: false, canApprove: false });
  });

  it('the user should be able to edit only - alert ', () => {
    const canEditAccess = { containers: { nova: { manage: [ 'alert' ] } }, pages: { nova: { manage: [ 'login' ] } } };
    const canEditPermissions = { 'alerts_manage': true };
    const canEdit = canEditRule(canEditAccess, canEditPermissions, 'alert', mockAlert);
    expect(canEdit).toStrictEqual({ canManage: true, canApprove: false });
  });

  it('the user should be able to approve and edit - alert', () => {
    const canEditAccess = { containers: { nova: { manage: [ 'alert' ] } }, pages: { nova: { manage: [ 'login' ] } } };
    const canEditPermissions = { 'alerts_manage': true, 'alerts_approve': true };
    const canEdit = canEditRule(canEditAccess, canEditPermissions, 'alert', mockAlert);
    expect(canEdit).toStrictEqual({ canManage: true, canApprove: true });
  });

  it('the user should not be able to do any action - alert', () => {
    const canEditAccess = { containers: { nova: { manage: [ 'alert' ] } }, pages: { nova: { manage: [ 'other-page' ] } } };
    const canEditPermissions = { 'alerts_manage': true, 'alerts_approve': true };
    const canEdit = canEditRule(canEditAccess, canEditPermissions, 'alert', mockAlert);
    expect(canEdit).toStrictEqual({ canManage: false, canApprove: false });
  });

  it('the user should be able to edit only - alert ( without page ) ', () => {
    const canEditAccess = { containers: { nova: { manage: [ 'alert' ] } }, pages: { } };
    const canEditPermissions = { 'alerts_manage': true };
    const canEdit = canEditRule(canEditAccess, canEditPermissions, 'alert', mockAlertWithoutPage);
    expect(canEdit).toStrictEqual({ canManage: true, canApprove: false });
  });

  it('the user should be able to approve and edit - alert ( without page )', () => {
    const canEditAccess = { containers: { nova: { manage: [ 'alert' ] } }, pages: { } };
    const canEditPermissions = { 'alerts_manage': true, 'alerts_approve': true };
    const canEdit = canEditRule(canEditAccess, canEditPermissions, 'alert', mockAlertWithoutPage);
    expect(canEdit).toStrictEqual({ canManage: true, canApprove: true });
  });

  it('the user should not be able to do any action - alert ( without page )', () => {
    const canEditAccess = { containers: { nova: { manage: [ 'other-container' ] } }, pages: { } };
    const canEditPermissions = { 'alerts_manage': true, 'alerts_approve': true };
    const canEdit = canEditRule(canEditAccess, canEditPermissions, 'alert', mockAlertWithoutPage);
    expect(canEdit).toStrictEqual({ canManage: false, canApprove: false });
  });
});

describe('canViewManageRuleType', () => {
  it('The user should has all abilities  ', () => {
    const { viewCampaigns, viewCCAUCampaigns, viewSOL, viewAlert, viewStoreFront } = canViewManageRuleType(access, permissions);
    expect(viewCampaigns).toStrictEqual(true);
    expect(viewCCAUCampaigns).toStrictEqual(true);
    expect(viewSOL).toStrictEqual(true);
    expect(viewAlert).toStrictEqual(true);
    expect(viewStoreFront).toStrictEqual(true);
  });
});
