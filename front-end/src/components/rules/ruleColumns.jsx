import React from 'react';
import moment from 'moment';

import TextCaption from 'canvas-core-react/lib/TextCaption';
import TextButton from 'canvas-core-react/lib/TextButton';
import IconInfo from 'canvas-core-react/lib/IconInfo';
import ActionMenuList from 'canvas-core-react/lib/ActionMenuList';
import Tooltip from 'canvas-core-react/lib/Tooltip';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';

import StatusBadge from '../core/statusBadge';
import { capitalize } from '../../utils';

export const tableColumns = ({
    type,
    ruleTypes,
    sortableColumnProperties,
    filters,
    ruleTypesDisplayName,
    isSetActiveLoading,
    tableData,
    getActionItems,
    getContentById,
    selectedRuleId,
    setSelectedRuleId,
    setIsSidePanelOpen,
    isActionListOpen,
    setIsActionListOpen,
}) => {
  const columns = [
      ...(type === ruleTypes.CAMPAIGN || type === ruleTypes.CCAU_CAMPAIGN ? [ {
        name: 'ID',
        cellFormatter: row => (
          <>
            { row.urgent && (
              <Tooltip
                id={`urgent-icon-${row.id}`}
                heading="Urgent"
                infoButtonLabel="Info"
                closeButtonLabel="Close"
              >
                <TextCaption component="p" className="admin-list__tooltip-text">This is an urgent campaign</TextCaption>
              </Tooltip>
            )
            }
            <TextCaption component="p">
              { row.external_ref || 'N/A' }
            </TextCaption>
          </>
        ),
        selector: '',
        bodyStyle: { display: 'flex', flexFlow: 'wrap' },
        minWidth: 'auto',
        ...sortableColumnProperties('external_ref', filters),
      } ] : []),
      {
        name: `${ruleTypesDisplayName[type]} Name`,
        cellFormatter: row => (
          <TextButton
            Icon={IconInfo}
            iconPosition="right"
            className="admin-list__name-link"
            onClick={() => {
              setSelectedRuleId(row.id);
              setIsSidePanelOpen(true);
              getContentById(row.content_type, row.content_id, row.content_space);
            }}
          >
            { row.name }
          </TextButton>
        ),
        selector: '',
        grow: 2,
        bodyStyle: { textAlign: 'left', wordBreak: 'break-word', width: 'max-content' },
        ...sortableColumnProperties('name', filters),
      }, {
        name: 'Start Date',
        cellFormatter: row => <TextCaption component="p">{ moment(row.start_at).format('lll') }</TextCaption>,
        selector: '',
        minWidth: 'auto',
        ...sortableColumnProperties('start_at', filters),
      }, {
        name: 'End Date',
        cellFormatter: row => <TextCaption component="p">{ moment(row.end_at).format('lll') }</TextCaption>,
        selector: '',
        minWidth: 'auto',
        ...sortableColumnProperties('end_at', filters),
      }, {
        name: 'App',
        cellFormatter: row => <TextCaption component="p">{ capitalize(row.application) }</TextCaption>,
        selector: '',
        minWidth: 'auto',
        ...sortableColumnProperties('application', filters),
      }, ...(type === ruleTypes.CAMPAIGN || type === ruleTypes.CCAU_CAMPAIGN ? [ {
        name: 'Page',
        selector: '',
        minWidth: 'auto',
        cellFormatter: row => (
          row.pages.map((page, index) => <TextCaption key={`page-${index}`} component="p">{ page }</TextCaption>)
        ),
        bodyStyle: { flexDirection: 'column', wordBreak: 'break-word' },
      }, {
        name: 'Container',
        selector: 'container',
      } ] : [ {
        name: 'Created By',
        selector: 'created_by',
        ...sortableColumnProperties('created_by', filters),
        minWidth: 'auto',
      }, {
        name: 'Last Updated By',
        selector: 'updated_by',
        ...sortableColumnProperties('updated_by', filters),
        minWidth: 'auto',
      }, {
        name: 'Last Updated At',
        cellFormatter: row => <TextCaption component="p">{ moment(row.updated_at).format('lll') }</TextCaption>,
        selector: '',
        ...sortableColumnProperties('updated_at', filters),
        minWidth: 'auto',
      } ]), {
        name: 'Status',
        cellFormatter: row => {
          return isSetActiveLoading && selectedRuleId === row.id
            ? <IconSpinner size={24} />
            : <StatusBadge
              className="admin-list__status-badge"
              status={row.status}
              disabled={row.disabled}
              endTime={row.end_at}
              startTime={row.start_at}
            />;
        },
        selector: '',
        minWidth: 'auto',
        grow: 1.2,
      }, {
        name: 'Action',
        cellFormatter: row => {
          const index = tableData.findIndex(_ => _.id === row.id);
          const isBottom = tableData.length > 3 && tableData.length - index > 3;
          return (<ActionMenuList isMenuOpen={row.id === isActionListOpen} setIsMenuOpen={(v) => setIsActionListOpen(v ? row.id : false)} bottomSheet={{ heading: '' }} iconType="horizontal-small" dialogPosition={isBottom ? 'top-center' : 'bottom-center'}>
            { getActionItems(row).actionItems }
          </ActionMenuList>);
      },
        selector: '',
        minWidth: 'auto',
      },
  ];
  return columns;
};
