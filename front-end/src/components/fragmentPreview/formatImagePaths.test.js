import { formatSOLImagePaths } from './formatImagePaths';

describe('formatImagePaths function should replace image src attributes correctly', () => {
  it('single image', () => {
    const mockHtml = '<img src="/contentdocs/image.png" />';
    const expectedHtml = '<img src="/api/v1/contents/vignette/static?path=%2Fimage.png&repo=dm-authenticated" />';
    expect(formatSOLImagePaths(mockHtml)).toStrictEqual(expectedHtml);
  });

  it('multiple images', () => {
    const mockHtml = '<img src="/contentdocs/image.png" /><img src="/contentdocs/image1.png" /><img src="/contentdocs/image2.png" />';
    const expectedHtml = '<img src="/api/v1/contents/vignette/static?path=%2Fimage.png&repo=dm-authenticated" /><img src="/api/v1/contents/vignette/static?path=%2Fimage1.png&repo=dm-authenticated" /><img src="/api/v1/contents/vignette/static?path=%2Fimage2.png&repo=dm-authenticated" />';
    expect(formatSOLImagePaths(mockHtml)).toStrictEqual(expectedHtml);
  });

  it('image tag without />', () => {
    const mockHtml = '<img src="/contentdocs/image.png" >';
    const expectedHtml = '<img src="/api/v1/contents/vignette/static?path=%2Fimage.png&repo=dm-authenticated" >';
    expect(formatSOLImagePaths(mockHtml)).toStrictEqual(expectedHtml);
  });

  it('non-image tag with src', () => {
    const mockHtml = '<img /><source src="horse.ogg" type="audio/ogg">';
    const expectedHtml = '<img /><source src="horse.ogg" type="audio/ogg">';
    expect(formatSOLImagePaths(mockHtml)).toStrictEqual(expectedHtml);
  });

  it('image tag with src that needs to be URL encoded', () => {
    const mockHtml = '<img src="/contentdocs/image1.png?param1=p&s" />';
    const expectedHtml = '<img src="/api/v1/contents/vignette/static?path=%2Fimage1.png%3Fparam1%3Dp%26s&repo=dm-authenticated" />';
    expect(formatSOLImagePaths(mockHtml)).toStrictEqual(expectedHtml);
  });

  it('image tag with src that has &amp; (web fragment #55)', () => {
    const mockHtml = '<img src="/contentdocs/P&amp;S/image1.png?param1=p&s" />';
    const expectedHtml = '<img src="/api/v1/contents/vignette/static?path=%2FP%26S%2Fimage1.png%3Fparam1%3Dp%26s&repo=dm-authenticated" />';
    expect(formatSOLImagePaths(mockHtml)).toStrictEqual(expectedHtml);
  });

  it('should return original html if no image path is formatted', () => {
    const mockHtml = '<html><h1>test heading></h1><img src="https://scotiabank.com" /></html>';
    expect(formatSOLImagePaths(mockHtml)).toStrictEqual(mockHtml);
  });
});
