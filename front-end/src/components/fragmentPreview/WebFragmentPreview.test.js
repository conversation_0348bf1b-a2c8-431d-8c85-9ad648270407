import React from 'react';
import FragmentPreview from './WebFragmentPreview.jsx';
import { act } from 'react-dom/test-utils';
import { render } from '@testing-library/react';

const mockApiResponses = {
  '/api/v1/contents/vignette?type=template': {
    'total': 3,
    'items': [
      { 'web_fragment_id': 1000002, 'name': 'template 1', 'updated_ts': '2019-11-04T15:41:26.650Z', 'fragment_file_path': '/some/path', 'fragment_file_name': 'main_template.ejs' },
      { 'web_fragment_id': 1000003, 'name': 'template 2', 'updated_ts': '2019-11-01T19:03:56.010Z', 'fragment_file_path': '/some/path', 'fragment_file_name': 'main_template2.ejs' },
      { 'web_fragment_id': 1000001, 'name': 'template 3', 'updated_ts': '2019-11-01T19:03:56.010Z', 'fragment_file_path': '/some/path', 'fragment_file_name': 'main_template.ejs' },
    ],
  },
  // first template
  '/api/v1/contents/vignette/1000002': {
    'web_fragment_id': 1000002,
    'name': 'main template',
    'fragment_file_path': '/some/path',
    'fragment_file_name': 'main_template.ejs',
    'fragment_type': 'template',
    'content': '<body><%- fragment.body %></body>',
    'content_hash': 'c908fa15d339011d0c6c3ec7903b4ecd',
    'created_ts': '2019-10-31T19:43:18.533Z',
    'updated_ts': '2019-11-04T15:41:26.650Z',
  },
  // web fragment
  '/api/v1/contents/vignette/999999': {
    'web_fragment_id': 999999,
    'name': 'main template',
    'fragment_file_path': '/some/path',
    'fragment_file_name': 'main_template.ejs',
    'fragment_type': 'webfragment',
    'content': 'webfragment content',
    'content_hash': 'c908fa15d339011d0c6c3ec7903b4ecd',
    'created_ts': '2019-10-31T19:43:18.533Z',
    'updated_ts': '2019-11-04T15:41:26.650Z',
  },
};

describe('Web Fragment Preview component', () => {
  it('should render the component', async() => {
    let wrapper;
    await act(async() => {
      global.fetch = (url) => ({ json: () => mockApiResponses[url] });
      global.ejs = {
        render: jest.fn().mockImplementation((template, { fragment: { body } }) => {
          return template.replace('<%- fragment.body %>', body);
        }),
      };
      wrapper = render(<FragmentPreview
        match={{
          params: {
            webfragmentId: '999999',
            type: 'estore',
          },
        }}
      />);
    });
    expect(wrapper.asFragment()).toMatchSnapshot();
  });
});
