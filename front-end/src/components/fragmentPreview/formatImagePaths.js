const proxyPath = '/api/v1/contents/vignette/static?path=';

const formatImagePaths = (html, stringToReplace, repo) => (
  html.replace(/<img[^<>]*\ssrc=['"](\S*)['"][^<>]*\/?>/gim, (fullMatch, captureGroup) => {
    if (!captureGroup.startsWith(stringToReplace)) { // no proxy required on image path
      return fullMatch;
    }
    const imagePath = captureGroup.replace(stringToReplace, '');
    const encodedImagePath = encodeURIComponent(imagePath.replace('&amp;', '&')).concat(`&repo=${repo}`);
    return fullMatch.replace(stringToReplace, proxyPath).replace(imagePath, encodedImagePath);
  })
);

export const formatSOLImagePaths = html => formatImagePaths(html, '/contentdocs', 'dm-authenticated');
export const formatStorefrontImagePaths = html => formatImagePaths(html, '/storefront', 'dm-storefront');
