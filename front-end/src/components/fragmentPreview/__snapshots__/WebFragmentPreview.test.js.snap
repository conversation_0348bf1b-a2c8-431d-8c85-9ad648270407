// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Web Fragment Preview component should render the component 1`] = `
<DocumentFragment>
  <div
    class="webfragment-preview"
  >
    <div
      class="webfragment-preview__controls"
    >
      <div
        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
      >
        <label
          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
        >
          Select template
        </label>
      </div>
      <div
        class="Filterstyle__Wrapper-canvas-core__sc-1p1jufl-0 XBzhB Filter__wrap"
      >
        <select
          class="Filterstyle__Select-canvas-core__sc-1p1jufl-1 cVdbwH Filter__select"
          name="template_select"
        >
          <option
            name=" "
            value=" "
          >
            Default Template
          </option>
          <option
            name="1000002"
            value="1000002"
          >
            template 1
          </option>
          <option
            name="1000003"
            value="1000003"
          >
            template 2
          </option>
          <option
            name="1000001"
            value="1000001"
          >
            template 3
          </option>
        </select>
        <svg
          aria-hidden="true"
          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Filter__icon Filter__icon--right"
          color="currentColor"
          focusable="false"
          role="presentation"
          size="18"
          viewBox="0 0 30 30"
        >
          <path
            d="M28.5 8.24991L15 21.7499L1.5 8.24991"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>
    <div
      class="webfragment-preview__controls"
    >
      <div
        class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
      >
        <label
          class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
        >
          Select container
        </label>
      </div>
      <div
        class="Filterstyle__Wrapper-canvas-core__sc-1p1jufl-0 XBzhB Filter__wrap"
      >
        <select
          class="Filterstyle__Select-canvas-core__sc-1p1jufl-1 cVdbwH Filter__select"
          name="container_select"
        >
          <option
            name="body"
            value="body"
          >
            body
          </option>
        </select>
        <svg
          aria-hidden="true"
          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Filter__icon Filter__icon--right"
          color="currentColor"
          focusable="false"
          role="presentation"
          size="18"
          viewBox="0 0 30 30"
        >
          <path
            d="M28.5 8.24991L15 21.7499L1.5 8.24991"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>
    <div
      class="webfragment-preview__preview-wrap"
    >
      <h2
        class="webfragment-preview__label"
      >
        Preview
      </h2>
      <div
        class="webfragment-preview__preview"
      >
        <iframe
          sandbox="allow-scripts allow-same-origin"
          srcdoc="<!doctype html>
<html>
  <body>webfragment content</body>
</html>"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;
