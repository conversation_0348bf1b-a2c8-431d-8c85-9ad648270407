import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import Filter from 'canvas-core-react/lib/Filter';
import { formatStorefrontImagePaths, formatSOLImagePaths } from './formatImagePaths';
import { ruleTypes } from '../../constants';

const containerMatchRegex = /<%-\s*fragment\.(.*?)\s*%>/g;

const defaultTemplate = `<!doctype html>
<html>
  <body><%- fragment.body %></body>
</html>`;

const WebFragmentPreview = (props) => {
  const [ templateId, setTemplateId ] = useState();
  const [ templateContent, setTemplateContent ] = useState(defaultTemplate);
  const [ fragmentId, setFragmentId ] = useState();
  const [ fragmentContent, setFragmentContent ] = useState('Loading...');
  const [ templates, setTemplates ] = useState([]);
  const [ containers, setContainers ] = useState([]);
  const [ container, setContainer ] = useState();
  const [ previewHtml, setPreviewHtml ] = useState('');

  const formatImagePaths = props.match.params.type === ruleTypes.ESTORE ? formatStorefrontImagePaths : formatSOLImagePaths;
  const getHtmlById = async id => {
    const template = await (await fetch(
      `/api/v1/contents/vignette/${id}`
    )).json();
    return template.content;
  };

  useEffect(() => {
    const init = async() => {
      const templatesResponse = await (await fetch(
        '/api/v1/contents/vignette?type=template'
      )).json();
      setTemplates([ {
        fragment_file_name: 'default.ejs',
        fragment_file_path: 'template',
        name: 'Default Template',
        updated_ts: '2019-11-13T19:38:25.884Z',
        web_fragment_id: ' ',
      }, ...templatesResponse.items ]);
      setFragmentId(props.match.params.webfragmentId);
      setTemplateId(' ');
    };
    init();
  }, [ templates.items ]);

  useEffect(() => {
    if (!templateId) {
      return;
    }
    const setTemplate = async() => {
      let content;
      if (String(templateId) === ' ') {
        content = defaultTemplate;
      } else {
        content = await getHtmlById(templateId);
      }
      setTemplateContent(content);
    };
    setTemplate();
  }, [ templateId ]);

  useEffect(() => {
    if (!fragmentId) {
      return;
    }
    const setFragment = async() => {
      const content = await getHtmlById(fragmentId);
      setFragmentContent(content);
    };
    setFragment();
  }, [ fragmentId ]);

  useEffect(() => {
    const parsedContainers = [];
    let parsedContainer;
    while ((parsedContainer = containerMatchRegex.exec(templateContent)) !== null) {
      if (parsedContainer) {
        parsedContainers.push(parsedContainer[1]);
      }
    }
    setContainers(parsedContainers);
    if (parsedContainers.length > 0 && !parsedContainer) {
      setContainer(parsedContainers[0]);
    }
  }, [ templateContent ]);

  useEffect(() => {
    // eslint-disable-next-line no-undef
    const previewHtmlEjs = ejs.render(
      templateContent,
      {
        fragment: {
          [container]: fragmentContent,
        },
      },
      { client: true }
    );
    setPreviewHtml(formatImagePaths(previewHtmlEjs));
  }, [ templateContent, fragmentContent, container ]);

  return (
    <div className="webfragment-preview">
      <div className="webfragment-preview__controls">
        <Filter
          label="Select template"
          isLabelVisible
          onChange={e => setTemplateId(e.target.value)}
          name="template_select"
          value={templateId}
        >
          { templates.map(template => (
            <option
              key={template.web_fragment_id}
              name={template.web_fragment_id}
              value={template.web_fragment_id}
            >
              { template.name }
            </option>
          )) }
        </Filter>
      </div>
      <div className="webfragment-preview__controls">
        <Filter
          label="Select container"
          isLabelVisible
          onChange={e => setContainer(e.target.value)}
          name="container_select"
          value={container}
        >
          { containers.map(containerName => (
            <option
              key={containerName}
              name={containerName}
              value={containerName}
            >
              { containerName }
            </option>
          )) }
        </Filter>
      </div>
      <div className="webfragment-preview__preview-wrap">
        <h2 className="webfragment-preview__label">Preview</h2>
        <div className="webfragment-preview__preview">
          <iframe srcDoc={previewHtml} sandbox="allow-scripts allow-same-origin" />
        </div>
      </div>
    </div>
  );
};

WebFragmentPreview.propTypes = {
  match: PropTypes.shape({
    params: PropTypes.shape({
      type: PropTypes.oneOf([ 'estore', 'sol' ]).isRequired,
      webfragmentId: PropTypes.string,
    }),
  }),
};

export default WebFragmentPreview;
