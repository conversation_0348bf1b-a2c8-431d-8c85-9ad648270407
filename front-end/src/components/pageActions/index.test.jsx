import React from 'react';
import { shallow } from 'enzyme';
import PageAction from './index';

describe('PageInfo - match snapshot loading - true', () => {
  const wrapper = shallow(
    <PageAction
      showSearch
    />
  );
  global.snapshot(wrapper);
});

describe('PageInfo - match snapshot loading - false', () => {
  const wrapper = shallow(
    <PageAction
      showSearch={false}
    />
  );
  global.snapshot(wrapper);
});
