// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PageInfo - match snapshot loading - false Snapshot 1`] = `
<Fragment>
  <div
    className="page-actions__actions-container"
  >
    <div
      className="page-actions__search"
    />
    <div>
      <ForwardRef
        Icon={[Function]}
        className={null}
        color="blue"
        iconPosition="left"
        onClick={[Function]}
        weight="bold"
      >
        Filters
      </ForwardRef>
    </div>
  </div>
</Fragment>
`;

exports[`PageInfo - match snapshot loading - true Snapshot 1`] = `
<Fragment>
  <div
    className="page-actions__actions-container"
  >
    <div
      className="page-actions__search"
    >
      <v
        className={null}
        clearButtonLabel=""
        disabled={false}
        error={null}
        id="page-action-search"
        inputRef={null}
        label="Search"
        onChange={[Function]}
        onClear={null}
        onSearchClick={null}
        searchButtonLabel=""
        secondaryLabel={null}
        showLabel={false}
        tooltip={null}
        value=""
        variant="default"
      />
    </div>
    <div>
      <ForwardRef
        Icon={[Function]}
        className={null}
        color="blue"
        iconPosition="left"
        onClick={[Function]}
        weight="bold"
      >
        Filters
      </ForwardRef>
    </div>
  </div>
</Fragment>
`;
