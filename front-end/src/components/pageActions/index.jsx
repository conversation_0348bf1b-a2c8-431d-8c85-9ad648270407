import React, { useState } from 'react';
import Search from 'canvas-core-react/lib/Search';
import TextButton from 'canvas-core-react/lib/TextButton';
import IconFilter from 'canvas-core-react/lib/IconFilter';
import IconClose from 'canvas-core-react/lib/IconClose';
import PropTypes from 'prop-types';

export const pageActions = ({
  showSearch = true,
  value = '',
  onChange = () => {},
  showFilter = true,
  FilterIcon = IconFilter,
  CloseIcon = IconClose,
  filterButtonText = 'Filters',
  closeButtonText = 'Cancel',
  setParentFilteringOptionsCollapsed = () => {},
  children,
}) => {
  const [ filteringOptionsCollapsed, setFilteringOptionsCollapsed ] = useState(true);

  return (
    <>
      <div className="page-actions__actions-container">
        <div className="page-actions__search">
          { showSearch && (
            <Search
              id="page-action-search"
              clearButtonLabel=""
              showLabel={false}
              label="Search"
              searchButtonLabel=""
              value={value}
              onChange={onChange}
            />
          ) }
        </div>
        <div>
          { showFilter && (
            <TextButton
              Icon={filteringOptionsCollapsed ? FilterIcon : CloseIcon}
              onClick={() => {
                setFilteringOptionsCollapsed((isCollapsed) => !isCollapsed);
                setParentFilteringOptionsCollapsed((isCollapsed) => !isCollapsed);
              }}
            >
              { filteringOptionsCollapsed ? filterButtonText : closeButtonText }
            </TextButton>
          ) }
        </div>
      </div>
      { !filteringOptionsCollapsed && children }
    </>
  );
};

pageActions.propTypes = {
  showSearch: PropTypes.bool,
  value: PropTypes.string,
  onChange: PropTypes.func,
  showFilter: PropTypes.bool,
  FilterIcon: PropTypes.element,
  CloseIcon: PropTypes.element,
  filterButtonText: PropTypes.string,
  closeButtonText: PropTypes.string,
  children: PropTypes.node,
  setParentFilteringOptionsCollapsed: PropTypes.func,
};

export default pageActions;
