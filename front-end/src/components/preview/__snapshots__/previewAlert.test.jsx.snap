// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PreviewAlert Renders component 1`] = `
<div>
  <div
    class="preview"
  >
    <div
      class="preview__header"
    >
      <div
        class="preview__header__full-width preview__header--left"
      >
        <form>
          <div
            class="input-select-field content-preview__language-selection"
          >
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
            >
              <div
                class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
              >
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                >
                  <label
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                    for="language"
                    label="Language"
                  >
                    Language
                  </label>
                </div>
                <select
                  aria-describedby=""
                  class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select Selector__select--placeholder"
                  id="language"
                  name="language"
                >
                  <option
                    class="Selector__placeholder"
                    disabled=""
                    selected=""
                    value=""
                  >
                    Select a language
                  </option>
                  <option
                    data-testid="language-option"
                    value="en"
                  >
                    en
                  </option>
                </select>
                <svg
                  aria-hidden="true"
                  class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                  color="currentColor"
                  focusable="false"
                  role="presentation"
                  size="18"
                  viewBox="0 0 30 30"
                >
                  <path
                    d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>
        </form>
        <div
          class="preview__header__row"
        >
          <fieldset
            class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 iUkhXD InputGroup__fieldset"
            id="device-os-inputgroup"
            name="OS"
          >
            <legend
              class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 fsDDjX"
            >
              OS
            </legend>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container"
            >
              <label
                class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 LChGv RadioButton__label"
                for="ios"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                  id="ios"
                  name="ios"
                  type="radio"
                  value="ios"
                />
                <span
                  class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                >
                  <span
                    class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                  />
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      cx="12"
                      cy="12"
                      r="12"
                      stroke="none"
                    />
                  </svg>
                </span>
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                >
                  <div
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                  >
                    ios
                  </div>
                </div>
              </label>
            </div>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container"
            >
              <label
                class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 LChGv RadioButton__label"
                for="Android"
              >
                <input
                  checked=""
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                  id="Android"
                  name="Android"
                  type="radio"
                  value="android"
                />
                <span
                  class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                >
                  <span
                    class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                  />
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      cx="12"
                      cy="12"
                      r="12"
                      stroke="none"
                    />
                  </svg>
                </span>
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                >
                  <div
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                  >
                    Android
                  </div>
                </div>
              </label>
            </div>
          </fieldset>
          <fieldset
            class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 InputGroup__fieldset"
            id="darkmode-inputgroup"
            name="darkmode"
          >
            <legend
              class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 hlGdcC"
            >
              Display Settings
            </legend>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline"
            >
              <label
                class="ToggleSwitchstyle__Wrapper-canvas-core__sc-168jcna-0 iKYIja ToggleSwitch__label"
                for="dark-mode"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input ToggleInput"
                  id="dark-mode"
                  name="Dark Mode"
                  type="checkbox"
                  value=""
                />
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label labelSpacing"
                >
                  <div
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW labelSpacing"
                  >
                    Dark Mode
                  </div>
                </div>
                <span
                  class="ToggleSwitchstyle__StyledSlider-canvas-core__sc-168jcna-1 fsEBQI ToggleSwitch__slider"
                >
                  <span
                    class="ToggleSwitchstyle__StyledSwitch-canvas-core__sc-168jcna-2 kWOVUq ToggleSwitch__switch"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-off"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 10 10"
                    >
                      <g
                        class="IconToggleSwitchOffstyle__StyledG-canvas-core__sc-r3kqbd-0 ezyJRW"
                        fill="none"
                        fill-rule="evenodd"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-496.000000, -153.000000)"
                        >
                          <g
                            transform="translate(439.000000, 143.000000)"
                          >
                            <g
                              transform="translate(47.000000, 0.000000)"
                            >
                              <g
                                id="Group"
                                transform="translate(11.000000, 11.000000)"
                              >
                                <line
                                  id="Stroke-184"
                                  x1="0"
                                  x2="8"
                                  y1="0"
                                  y2="8"
                                />
                                <line
                                  id="Stroke-185"
                                  x1="0"
                                  x2="8"
                                  y1="8"
                                  y2="0"
                                />
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-on"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 11 9"
                    >
                      <g
                        class="IconToggleSwitchOnstyle__StyledG-canvas-core__sc-16ds4pu-0 fVdUxI"
                        fill="none"
                        fill-rule="evenodd"
                        id="Page-1"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-522.000000, -111.000000)"
                        >
                          <g
                            transform="translate(439.000000, 100.000000)"
                          >
                            <g
                              transform="translate(84.000000, 12.000000)"
                            >
                              <polyline
                                points="0 3.73333333 3.15 7 9 0"
                              />
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </span>
                </span>
              </label>
            </div>
          </fieldset>
        </div>
      </div>
    </div>
  </div>
  <div
    class="preview--alert"
  >
    <div
      class="starburst-alert"
    >
      <div>
        <span
          class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text starburst-alert__text-color"
          color="black"
        >
          <div>
            <p>
              test-message
            </p>
          </div>
        </span>
      </div>
      <div
        class="starburst-alert__closebutton_container"
      >
        <button
          type="button"
        >
          <svg
            aria-hidden="true"
            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon starburst-alert__closebutton"
            color="currentColor"
            focusable="false"
            role="presentation"
            size="18"
            viewBox="0 0 30 30"
          >
            <path
              clip-rule="evenodd"
              d="M28.5 14.9999C28.5 22.4561 22.4557 28.4999 15 28.4999C7.54335 28.4999 1.5 22.4561 1.5 14.9999C1.5 7.54371 7.54335 1.4999 15 1.4999C22.4557 1.4999 28.5 7.54371 28.5 14.9999Z"
              fill="none"
              fill-rule="evenodd"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M10.7574 10.7574L19.2427 19.2427"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M19.2433 10.7576L10.7576 19.2424"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          <span
            class="starburst-alert__closebutton_label"
          >
            <strong>
              Close
            </strong>
          </span>
        </button>
      </div>
    </div>
  </div>
</div>
`;
