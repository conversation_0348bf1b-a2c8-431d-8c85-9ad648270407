// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Page rendering should render alert preview page according to snapshot 1`] = `
<body>
  <div>
    <div
      class="preview"
    >
      <div
        class="preview__header"
      >
        <div
          class="preview__header__full-width preview__header--left"
        >
          <form>
            <div
              class="input-select-field content-preview__language-selection"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
              >
                <div
                  class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                >
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                  >
                    <label
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                      for="language"
                      label="Language"
                    >
                      Language
                    </label>
                  </div>
                  <select
                    aria-describedby=""
                    class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                    id="language"
                    name="language"
                  >
                    <option
                      class="Selector__placeholder"
                      disabled=""
                      value=""
                    >
                      Select a language
                    </option>
                    <option
                      data-testid="language-option"
                      value="en-US"
                    >
                      English (United States)
                    </option>
                    <option
                      data-testid="language-option"
                      value="fr"
                    >
                      French
                    </option>
                  </select>
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </form>
          <div
            class="preview__header__row"
          >
            <fieldset
              class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 iUkhXD InputGroup__fieldset"
              id="device-os-inputgroup"
              name="OS"
            >
              <legend
                class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 fsDDjX"
              >
                OS
              </legend>
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container"
              >
                <label
                  class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 LChGv RadioButton__label"
                  for="ios"
                >
                  <input
                    checked=""
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                    id="ios"
                    name="ios"
                    type="radio"
                    value="ios"
                  />
                  <span
                    class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                  >
                    <span
                      class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                    />
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="18"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        cx="12"
                        cy="12"
                        r="12"
                        stroke="none"
                      />
                    </svg>
                  </span>
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                  >
                    <div
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                    >
                      ios
                    </div>
                  </div>
                </label>
              </div>
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input RadioButtonstyle__StyledContainer-canvas-core__sc-582mv8-0 iSGlRi RadioButton__container"
              >
                <label
                  class="RadioButtonstyle__RadioLabel-canvas-core__sc-582mv8-1 LChGv RadioButton__label"
                  for="Android"
                >
                  <input
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input RadioButtonstyle__RadioInput-canvas-core__sc-582mv8-5 jRldnb RadioButton__input"
                    id="Android"
                    name="Android"
                    type="radio"
                    value="android"
                  />
                  <span
                    class="RadioButtonstyle__RadioCircle-canvas-core__sc-582mv8-2 iLJfvw RadioButton__circle"
                  >
                    <span
                      class="RadioButtonstyle__RadioCircleBig-canvas-core__sc-582mv8-3 dITBly"
                    />
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 esgbUF SvgIcon__icon RadioButtonstyle__RadioCircleSmall-canvas-core__sc-582mv8-4 hEnigB"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="18"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        cx="12"
                        cy="12"
                        r="12"
                        stroke="none"
                      />
                    </svg>
                  </span>
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 hAyTlW Label"
                  >
                    <div
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 gpCUGi"
                    >
                      Android
                    </div>
                  </div>
                </label>
              </div>
            </fieldset>
            <fieldset
              class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 InputGroup__fieldset"
              id="darkmode-inputgroup"
              name="darkmode"
            >
              <legend
                class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 hlGdcC"
              >
                Display Settings
              </legend>
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline"
              >
                <label
                  class="ToggleSwitchstyle__Wrapper-canvas-core__sc-168jcna-0 iKYIja ToggleSwitch__label"
                  for="dark-mode"
                >
                  <input
                    class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input ToggleInput"
                    id="dark-mode"
                    name="Dark Mode"
                    type="checkbox"
                    value=""
                  />
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label labelSpacing"
                  >
                    <div
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW labelSpacing"
                    >
                      Dark Mode
                    </div>
                  </div>
                  <span
                    class="ToggleSwitchstyle__StyledSlider-canvas-core__sc-168jcna-1 fsEBQI ToggleSwitch__slider"
                  >
                    <span
                      class="ToggleSwitchstyle__StyledSwitch-canvas-core__sc-168jcna-2 kWOVUq ToggleSwitch__switch"
                    >
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-off"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="10"
                        viewBox="0 0 10 10"
                      >
                        <g
                          class="IconToggleSwitchOffstyle__StyledG-canvas-core__sc-r3kqbd-0 ezyJRW"
                          fill="none"
                          fill-rule="evenodd"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="1"
                        >
                          <g
                            stroke-width="2"
                            transform="translate(-496.000000, -153.000000)"
                          >
                            <g
                              transform="translate(439.000000, 143.000000)"
                            >
                              <g
                                transform="translate(47.000000, 0.000000)"
                              >
                                <g
                                  id="Group"
                                  transform="translate(11.000000, 11.000000)"
                                >
                                  <line
                                    id="Stroke-184"
                                    x1="0"
                                    x2="8"
                                    y1="0"
                                    y2="8"
                                  />
                                  <line
                                    id="Stroke-185"
                                    x1="0"
                                    x2="8"
                                    y1="8"
                                    y2="0"
                                  />
                                </g>
                              </g>
                            </g>
                          </g>
                        </g>
                      </svg>
                      <svg
                        aria-hidden="true"
                        class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-on"
                        color="currentColor"
                        focusable="false"
                        role="presentation"
                        size="10"
                        viewBox="0 0 11 9"
                      >
                        <g
                          class="IconToggleSwitchOnstyle__StyledG-canvas-core__sc-16ds4pu-0 fVdUxI"
                          fill="none"
                          fill-rule="evenodd"
                          id="Page-1"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="1"
                        >
                          <g
                            stroke-width="2"
                            transform="translate(-522.000000, -111.000000)"
                          >
                            <g
                              transform="translate(439.000000, 100.000000)"
                            >
                              <g
                                transform="translate(84.000000, 12.000000)"
                              >
                                <polyline
                                  points="0 3.73333333 3.15 7 9 0"
                                />
                              </g>
                            </g>
                          </g>
                        </g>
                      </svg>
                    </span>
                  </span>
                </label>
              </div>
            </fieldset>
          </div>
        </div>
      </div>
    </div>
    <div
      class="preview--alert"
    >
      <div
        class="nova-alert nova-alert__ios_background_color"
      >
        <div
          class="nova-alert__ios_text_container"
        >
          <svg
            aria-hidden="true"
            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 eHCDHJ SvgIcon__icon nova-alert__ios_icon"
            color="transparent"
            focusable="false"
            role="presentation"
            size="18"
            viewBox="0 0 18 18"
          >
            <path
              clip-rule="evenodd"
              d="M9 0C13.9706 0 18 4.02944 18 9C18 13.9706 13.9706 18 9 18C4.02944 18 0 13.9706 0 9C0 4.02944 4.02944 0 9 0ZM8.025 13.319V8.43103C8.025 7.91684 8.46152 7.5 9 7.5C9.53848 7.5 9.975 7.91684 9.975 8.43103V13.319C9.975 13.8332 9.53848 14.25 9 14.25C8.46152 14.25 8.025 13.8332 8.025 13.319ZM9 3.3C9.74558 3.3 10.35 3.90442 10.35 4.65C10.35 5.39558 9.74558 6 9 6C8.25442 6 7.65 5.39558 7.65 4.65C7.65 3.90442 8.25442 3.3 9 3.3Z"
              fill="#009DD6"
              fill-rule="evenodd"
              stroke="none"
            />
          </svg>
          <div
            class="nova-alert__ios_text"
          >
            <span
              class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 etZanE TextCaption__text nova-alert__text-color"
              color="black"
            />
            <span
              class="TextCaptionstyle__Text-canvas-core__sc-lol886-0 gHKzzz TextCaption__text nova-alert__text-color"
              color="black"
            >
              <div>
                <p>
                  custom message
                </p>
              </div>
            </span>
          </div>
        </div>
        <div
          class="nova-alert__closebutton_container nova-alert__closebutton_container--ios"
        >
          <button
            type="button"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon nova-alert__closebutton_icon ios__closebutton_icon"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="18"
              viewBox="0 0 30 30"
            >
              <path
                d="M27 26.9999L3 2.9999"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M3 26.9999L27 2.9999"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <span />
          </button>
        </div>
      </div>
    </div>
  </div>
</body>
`;

exports[`Page rendering should render campaign preview page according to snapshot 1`] = `
<body
  class="wave-intercept"
>
  <div
    class="wave-intercept"
  >
    <div
      class="preview"
    >
      <div
        class="preview__header"
      >
        <div
          class="preview__header--left"
        >
          <form>
            <div
              class="input-select-field content-preview__language-selection"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
              >
                <div
                  class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                >
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                  >
                    <label
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                      for="language"
                      label="Language"
                    >
                      Language
                    </label>
                  </div>
                  <select
                    aria-describedby=""
                    class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                    id="language"
                    name="language"
                  >
                    <option
                      class="Selector__placeholder"
                      disabled=""
                      value=""
                    >
                      Select a language
                    </option>
                    <option
                      data-testid="language-option"
                      value="en-US"
                    >
                      English (United States)
                    </option>
                    <option
                      data-testid="language-option"
                      value="fr"
                    >
                      French
                    </option>
                  </select>
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div
          class="preview__header--right"
        >
          <fieldset
            class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 iUkhXD InputGroup__fieldset"
            id="settings-inputgroup"
          >
            <legend
              class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 fsDDjX"
            >
              Settings
            </legend>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline"
            >
              <label
                class="ToggleSwitchstyle__Wrapper-canvas-core__sc-168jcna-0 iKYIja ToggleSwitch__label"
                for="live-preview"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input ToggleInput"
                  id="live-preview"
                  name="Live Preview"
                  type="checkbox"
                  value=""
                />
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label labelSpacing"
                >
                  <div
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW labelSpacing"
                  >
                    Live Preview
                  </div>
                </div>
                <span
                  class="ToggleSwitchstyle__StyledSlider-canvas-core__sc-168jcna-1 fsEBQI ToggleSwitch__slider"
                >
                  <span
                    class="ToggleSwitchstyle__StyledSwitch-canvas-core__sc-168jcna-2 kWOVUq ToggleSwitch__switch"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-off"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 10 10"
                    >
                      <g
                        class="IconToggleSwitchOffstyle__StyledG-canvas-core__sc-r3kqbd-0 ezyJRW"
                        fill="none"
                        fill-rule="evenodd"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-496.000000, -153.000000)"
                        >
                          <g
                            transform="translate(439.000000, 143.000000)"
                          >
                            <g
                              transform="translate(47.000000, 0.000000)"
                            >
                              <g
                                id="Group"
                                transform="translate(11.000000, 11.000000)"
                              >
                                <line
                                  id="Stroke-184"
                                  x1="0"
                                  x2="8"
                                  y1="0"
                                  y2="8"
                                />
                                <line
                                  id="Stroke-185"
                                  x1="0"
                                  x2="8"
                                  y1="8"
                                  y2="0"
                                />
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-on"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 11 9"
                    >
                      <g
                        class="IconToggleSwitchOnstyle__StyledG-canvas-core__sc-16ds4pu-0 fVdUxI"
                        fill="none"
                        fill-rule="evenodd"
                        id="Page-1"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-522.000000, -111.000000)"
                        >
                          <g
                            transform="translate(439.000000, 100.000000)"
                          >
                            <g
                              transform="translate(84.000000, 12.000000)"
                            >
                              <polyline
                                points="0 3.73333333 3.15 7 9 0"
                              />
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </span>
                </span>
              </label>
            </div>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline"
            >
              <label
                class="ToggleSwitchstyle__Wrapper-canvas-core__sc-168jcna-0 iKYIja ToggleSwitch__label"
                for="dark-mode"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input ToggleInput"
                  id="dark-mode"
                  name="Dark Mode"
                  type="checkbox"
                  value=""
                />
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label labelSpacing"
                >
                  <div
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW labelSpacing"
                  >
                    Dark Mode
                  </div>
                </div>
                <span
                  class="ToggleSwitchstyle__StyledSlider-canvas-core__sc-168jcna-1 fsEBQI ToggleSwitch__slider"
                >
                  <span
                    class="ToggleSwitchstyle__StyledSwitch-canvas-core__sc-168jcna-2 kWOVUq ToggleSwitch__switch"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-off"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 10 10"
                    >
                      <g
                        class="IconToggleSwitchOffstyle__StyledG-canvas-core__sc-r3kqbd-0 ezyJRW"
                        fill="none"
                        fill-rule="evenodd"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-496.000000, -153.000000)"
                        >
                          <g
                            transform="translate(439.000000, 143.000000)"
                          >
                            <g
                              transform="translate(47.000000, 0.000000)"
                            >
                              <g
                                id="Group"
                                transform="translate(11.000000, 11.000000)"
                              >
                                <line
                                  id="Stroke-184"
                                  x1="0"
                                  x2="8"
                                  y1="0"
                                  y2="8"
                                />
                                <line
                                  id="Stroke-185"
                                  x1="0"
                                  x2="8"
                                  y1="8"
                                  y2="0"
                                />
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-on"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 11 9"
                    >
                      <g
                        class="IconToggleSwitchOnstyle__StyledG-canvas-core__sc-16ds4pu-0 fVdUxI"
                        fill="none"
                        fill-rule="evenodd"
                        id="Page-1"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-522.000000, -111.000000)"
                        >
                          <g
                            transform="translate(439.000000, 100.000000)"
                          >
                            <g
                              transform="translate(84.000000, 12.000000)"
                            >
                              <polyline
                                points="0 3.73333333 3.15 7 9 0"
                              />
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </span>
                </span>
              </label>
            </div>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline"
            >
              <label
                class="ToggleSwitchstyle__Wrapper-canvas-core__sc-168jcna-0 iKYIja ToggleSwitch__label"
                for="preview-new"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input ToggleInput"
                  id="preview-new"
                  name="Preview New"
                  type="checkbox"
                  value=""
                />
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label labelSpacing"
                >
                  <div
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW labelSpacing"
                  >
                    Preview New
                  </div>
                </div>
                <span
                  class="ToggleSwitchstyle__StyledSlider-canvas-core__sc-168jcna-1 fsEBQI ToggleSwitch__slider"
                >
                  <span
                    class="ToggleSwitchstyle__StyledSwitch-canvas-core__sc-168jcna-2 kWOVUq ToggleSwitch__switch"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-off"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 10 10"
                    >
                      <g
                        class="IconToggleSwitchOffstyle__StyledG-canvas-core__sc-r3kqbd-0 ezyJRW"
                        fill="none"
                        fill-rule="evenodd"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-496.000000, -153.000000)"
                        >
                          <g
                            transform="translate(439.000000, 143.000000)"
                          >
                            <g
                              transform="translate(47.000000, 0.000000)"
                            >
                              <g
                                id="Group"
                                transform="translate(11.000000, 11.000000)"
                              >
                                <line
                                  id="Stroke-184"
                                  x1="0"
                                  x2="8"
                                  y1="0"
                                  y2="8"
                                />
                                <line
                                  id="Stroke-185"
                                  x1="0"
                                  x2="8"
                                  y1="8"
                                  y2="0"
                                />
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-on"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 11 9"
                    >
                      <g
                        class="IconToggleSwitchOnstyle__StyledG-canvas-core__sc-16ds4pu-0 fVdUxI"
                        fill="none"
                        fill-rule="evenodd"
                        id="Page-1"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-522.000000, -111.000000)"
                        >
                          <g
                            transform="translate(439.000000, 100.000000)"
                          >
                            <g
                              transform="translate(84.000000, 12.000000)"
                            >
                              <polyline
                                points="0 3.73333333 3.15 7 9 0"
                              />
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </span>
                </span>
              </label>
            </div>
          </fieldset>
        </div>
        <div
          class="preview__header--download"
        >
          <button
            class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button preview__header--download-button"
            color="blue"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="16"
              viewBox="0 0 30 30"
            >
              <path
                d="M28.5001 20.7925V27.606"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M15.0003 1.5165V18.5895"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M8.15112 14.0899L15.0004 20.1941"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M22.1472 14.0899L15.0001 20.1941"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M1.5001 20.7925V27.606"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M28.4558 27.6892H1.65454"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <span
              class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
            >
              Download as PDF
            </span>
          </button>
        </div>
      </div>
      <div
        class="preview__help"
      >
        <div
          class="preview__help-icon"
        >
          <svg
            aria-hidden="true"
            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 CzcDu SvgIcon__icon"
            color="white"
            focusable="false"
            role="presentation"
            size="18"
            viewBox="0 0 24 24"
          >
            <path
              clip-rule="evenodd"
              d="M22.7997 11.9998C22.7997 17.9634 17.9633 22.7998 11.9997 22.7998C6.03418 22.7998 1.19971 17.9634 1.19971 11.9998C1.19971 6.03428 6.03418 1.1998 11.9997 1.1998C17.9633 1.1998 22.7997 6.03428 22.7997 11.9998Z"
              fill="none"
              fill-rule="evenodd"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              clip-rule="evenodd"
              d="M11.9997 17.3248C12.331 17.3248 12.5997 17.0562 12.5997 16.7248V11.3248C12.5997 10.9935 12.331 10.7248 11.9997 10.7248C11.6683 10.7248 11.3997 10.9935 11.3997 11.3248V16.7248C11.3997 17.0562 11.6683 17.3248 11.9997 17.3248Z"
              fill-rule="evenodd"
              stroke="none"
            />
            <ellipse
              cx="11.9996"
              cy="7.30462"
              rx="0.747122"
              ry="0.746635"
              stroke="none"
            />
          </svg>
        </div>
        <div
          class="preview__help-text"
        />
      </div>
      <div
        class="preview__preview-content"
        id="preview-content"
      >
        <div
          class="preview-renderer"
        >
          <h2
            class="TextIntroductionstyle__Text-canvas-core__sc-rmax1m-0 bvdIjD TextIntroduction__text preview-renderer__header"
            color="black"
          >
            Preview
          </h2>
          <div
            class="preview-renderer__preview"
          >
            <div>
              <div
                class="targeted-preview-card"
              >
                
                <h3
                  class="targeted-preview-card__title"
                >
                  <div>
                    test-preview-title
                  </div>
                </h3>
                <div
                  class="targeted-preview-card__description"
                >
                  <h1>
                    QAT - Preview: PA Multiple
                  </h1>
                  <p>
                    $15,500.00
                    <br />
                    (Credit)
                  </p>
                  <p>
                    December 31, 2019
                    <br />
                    (Other3)
                  </p>
                </div>
                <div
                  class="Marginstyle__Wrapper-canvas-core__sc-dm8riu-0 cOycvv Margin__container"
                >
                  <span
                    class="cta-link"
                  >
                    <button
                      class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary"
                    >
                      <span
                        class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                        tabindex="-1"
                      >
                        <span
                          class="ButtonCore__text"
                        >
                          Go!
                        </span>
                      </span>
                    </button>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="preview-renderer"
        >
          <h2
            class="TextIntroductionstyle__Text-canvas-core__sc-rmax1m-0 bvdIjD TextIntroduction__text preview-renderer__header"
            color="black"
          >
            Details
          </h2>
          <div
            class="preview-renderer__preview"
          >
            <div>
              <div
                class="template-generic"
              >
                <h1
                  class="template-generic__header template-generic__header--standalone"
                >
                  <div>
                    test-details-title
                  </div>
                </h1>
                <div
                  class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 YKZtT Card__container card"
                  type="floatLow"
                >
                  <h1
                    class="template-generic__header"
                  >
                    <div>
                      test-details-title
                    </div>
                  </h1>
                  <div
                    class="template-generic__description"
                  >
                    <p>
                      There are the various inline links that Pigeon can render:
                    </p>
                    <ul>
                      <li>
                        <a
                          class="pw-link pw-link-trackable"
                          data-link-page="details"
                          data-link-type="dial"
                          href="tel:**************"
                        >
                          <span>
                            Telephone
                          </span>
                        </a>
                      </li>
                      <li>
                        <a
                          class="pw-link pw-link-trackable"
                          data-link-page="details"
                          data-link-type="email"
                          href="mailto:<EMAIL>"
                        >
                          <span>
                            Email
                          </span>
                        </a>
                      </li>
                      <li>
                        <a
                          class="pw-link pw-link-trackable"
                          data-link-page="details"
                          data-link-type="externalweb"
                          href="http://businessnet.bns/BusinessNet/MediaLibrary/English/_documents/keyTopics/paymentServices/BNS_StandardizedPaymentFileFormatInventoryList.pdf"
                        >
                          <span>
                            PDF Link
                          </span>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon pw-link__icon pw-link__icon--pdf"
                            color="inherit"
                            focusable="false"
                            role="presentation"
                            size="18"
                            viewBox="0 0 30 30"
                          >
                            <path
                              d="M22.1827 24.8897V28.4999H1.53564V8.54341L8.82284 1.49993H22.1827V9.08895"
                              fill="none"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              d="M1.53564 8.54341H8.82284V1.49993"
                              fill="none"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              clip-rule="evenodd"
                              d="M17.9035 13.0714V20.489H20.7558C21.5822 20.489 22.2934 20.3389 22.8895 20.0386C23.4855 19.7384 23.9444 19.311 24.2662 18.7565C24.588 18.2019 24.7489 17.5396 24.7489 16.7696C24.7489 16.0067 24.588 15.3479 24.2662 14.7934C23.9444 14.2388 23.4855 13.8132 22.8895 13.5165C22.2934 13.2198 21.5822 13.0714 20.7558 13.0714H17.9035ZM11.1787 20.4891V13.0716H13.5702C14.6599 13.0716 15.4992 13.3047 16.0879 13.7709C16.6766 14.2372 16.971 14.9012 16.971 15.7631C16.971 16.6179 16.6766 17.2784 16.0879 17.7446C15.4992 18.2109 14.6599 18.444 13.5702 18.444H12.8791V20.4891H11.1787ZM13.3947 14.4915H12.8791V17.0241H13.3947C14.0529 17.0241 14.5301 16.9234 14.8263 16.7221C15.1225 16.5207 15.2706 16.2011 15.2706 15.7631C15.2706 15.318 15.1225 14.9948 14.8263 14.7935C14.5301 14.5922 14.0529 14.4915 13.3947 14.4915ZM20.5802 14.5127H19.6039V19.048H20.5802C21.3555 19.048 21.9533 18.8502 22.3739 18.4546C22.7944 18.059 23.0047 17.4974 23.0047 16.7698C23.0047 16.0563 22.7944 15.5017 22.3739 15.1061C21.9533 14.7105 21.3555 14.5127 20.5802 14.5127ZM25.6924 13.0714V20.489H27.3928V17.6703H30.0256V16.2292H27.3928V14.5125H30.4644V13.0714H25.6924Z"
                              fill-rule="evenodd"
                              stroke="none"
                            />
                            <path
                              d="M17.9035 20.489H17.7785V20.614H17.9035V20.489ZM17.9035 13.0714V12.9464H17.7785V13.0714H17.9035ZM22.8895 20.0386L22.9457 20.1503L22.8895 20.0386ZM24.2662 14.7934L24.1581 14.8561V14.8561L24.2662 14.7934ZM22.8895 13.5165L22.8338 13.6284L22.8895 13.5165ZM11.1787 13.0716V12.9466H11.0537V13.0716H11.1787ZM11.1787 20.4891H11.0537V20.6141H11.1787V20.4891ZM16.0879 13.7709L16.1655 13.6729L16.0879 13.7709ZM12.8791 18.444V18.319H12.7541V18.444H12.8791ZM12.8791 20.4891V20.6141H13.0041V20.4891H12.8791ZM12.8791 14.4915V14.3665H12.7541V14.4915H12.8791ZM12.8791 17.0241H12.7541V17.1491H12.8791V17.0241ZM14.8263 16.7221L14.7561 16.6187L14.8263 16.7221ZM19.6039 14.5127V14.3877H19.4789V14.5127H19.6039ZM19.6039 19.048H19.4789V19.173H19.6039V19.048ZM22.3739 18.4546L22.4595 18.5457L22.3739 18.4546ZM22.3739 15.1061L22.2882 15.1971L22.3739 15.1061ZM25.6924 20.489H25.5674V20.614H25.6924V20.489ZM25.6924 13.0714V12.9464H25.5674V13.0714H25.6924ZM27.3928 20.489V20.614H27.5178V20.489H27.3928ZM27.3928 17.6703V17.5453H27.2678V17.6703H27.3928ZM30.0256 17.6703V17.7953H30.1506V17.6703H30.0256ZM30.0256 16.2292H30.1506V16.1042H30.0256V16.2292ZM27.3928 16.2292H27.2678V16.3542H27.3928V16.2292ZM27.3928 14.5125V14.3875H27.2678V14.5125H27.3928ZM30.4644 14.5125V14.6375H30.5894V14.5125H30.4644ZM30.4644 13.0714H30.5894V12.9464H30.4644V13.0714ZM18.0285 20.489V13.0714H17.7785V20.489H18.0285ZM20.7558 20.364H17.9035V20.614H20.7558V20.364ZM22.8332 19.927C22.2583 20.2166 21.5672 20.364 20.7558 20.364V20.614C21.5972 20.614 22.3285 20.4611 22.9457 20.1503L22.8332 19.927ZM24.1581 18.6937C23.849 19.2264 23.4085 19.6372 22.8332 19.927L22.9457 20.1503C23.5625 19.8396 24.0399 19.3956 24.3743 18.8192L24.1581 18.6937ZM24.6239 16.7696C24.6239 17.522 24.4668 18.1617 24.1581 18.6937L24.3743 18.8192C24.7092 18.2421 24.8739 17.5572 24.8739 16.7696H24.6239ZM24.1581 14.8561C24.4669 15.3883 24.6239 16.0245 24.6239 16.7696H24.8739C24.8739 15.9888 24.7091 15.3076 24.3743 14.7306L24.1581 14.8561ZM22.8338 13.6284C23.4088 13.9146 23.8491 14.3235 24.1581 14.8561L24.3743 14.7306C24.0398 14.154 23.5622 13.7117 22.9452 13.4046L22.8338 13.6284ZM20.7558 13.1964C21.5674 13.1964 22.2587 13.3421 22.8338 13.6284L22.9452 13.4046C22.3281 13.0974 21.5969 12.9464 20.7558 12.9464V13.1964ZM17.9035 13.1964H20.7558V12.9464H17.9035V13.1964ZM11.0537 13.0716V20.4891H11.3037V13.0716H11.0537ZM13.5702 12.9466H11.1787V13.1966H13.5702V12.9466ZM16.1655 13.6729C15.5467 13.1828 14.6758 12.9466 13.5702 12.9466V13.1966C14.6441 13.1966 15.4517 13.4265 16.0103 13.8689L16.1655 13.6729ZM17.096 15.7631C17.096 14.8694 16.7888 14.1666 16.1655 13.6729L16.0103 13.8689C16.5644 14.3078 16.846 14.9331 16.846 15.7631H17.096ZM16.1655 17.8426C16.7886 17.3492 17.096 16.6501 17.096 15.7631H16.846C16.846 16.5857 16.5647 17.2076 16.0103 17.6466L16.1655 17.8426ZM13.5702 18.569C14.6758 18.569 15.5467 18.3327 16.1655 17.8426L16.0103 17.6466C15.4517 18.089 14.6441 18.319 13.5702 18.319V18.569ZM12.8791 18.569H13.5702V18.319H12.8791V18.569ZM13.0041 20.4891V18.444H12.7541V20.4891H13.0041ZM11.1787 20.6141H12.8791V20.3641H11.1787V20.6141ZM12.8791 14.6165H13.3947V14.3665H12.8791V14.6165ZM13.0041 17.0241V14.4915H12.7541V17.0241H13.0041ZM13.3947 16.8991H12.8791V17.1491H13.3947V16.8991ZM14.7561 16.6187C14.4917 16.7984 14.046 16.8991 13.3947 16.8991V17.1491C14.0599 17.1491 14.5686 17.0484 14.8966 16.8255L14.7561 16.6187ZM15.1456 15.7631C15.1456 16.1702 15.0102 16.446 14.7561 16.6187L14.8966 16.8255C15.2349 16.5955 15.3956 16.232 15.3956 15.7631H15.1456ZM14.7561 14.8969C15.0096 15.0692 15.1456 15.3481 15.1456 15.7631H15.3956C15.3956 15.288 15.2355 14.9205 14.8966 14.6901L14.7561 14.8969ZM13.3947 14.6165C14.046 14.6165 14.4917 14.7172 14.7561 14.8969L14.8966 14.6901C14.5686 14.4671 14.0599 14.3665 13.3947 14.3665V14.6165ZM19.6039 14.6377H20.5802V14.3877H19.6039V14.6377ZM19.7289 19.048V14.5127H19.4789V19.048H19.7289ZM20.5802 18.923H19.6039V19.173H20.5802V18.923ZM22.2882 18.3636C21.8979 18.7308 21.3348 18.923 20.5802 18.923V19.173C21.3762 19.173 22.0088 18.9697 22.4595 18.5457L22.2882 18.3636ZM22.8797 16.7698C22.8797 17.4725 22.6774 17.9974 22.2882 18.3636L22.4595 18.5457C22.9114 18.1206 23.1297 17.5223 23.1297 16.7698H22.8797ZM22.2882 15.1971C22.6779 15.5638 22.8797 16.082 22.8797 16.7698H23.1297C23.1297 16.0305 22.9109 15.4397 22.4595 15.0151L22.2882 15.1971ZM20.5802 14.6377C21.3348 14.6377 21.8979 14.8299 22.2882 15.1971L22.4595 15.0151C22.0088 14.591 21.3762 14.3877 20.5802 14.3877V14.6377ZM25.8174 20.489V13.0714H25.5674V20.489H25.8174ZM27.3928 20.364H25.6924V20.614H27.3928V20.364ZM27.2678 17.6703V20.489H27.5178V17.6703H27.2678ZM30.0256 17.5453H27.3928V17.7953H30.0256V17.5453ZM29.9006 16.2292V17.6703H30.1506V16.2292H29.9006ZM27.3928 16.3542H30.0256V16.1042H27.3928V16.3542ZM27.2678 14.5125V16.2292H27.5178V14.5125H27.2678ZM30.4644 14.3875H27.3928V14.6375H30.4644V14.3875ZM30.3394 13.0714V14.5125H30.5894V13.0714H30.3394ZM25.6924 13.1964H30.4644V12.9464H25.6924V13.1964Z"
                              stroke="none"
                            />
                          </svg>
                        </a>
                      </li>
                      <li>
                        <a
                          class="pw-link pw-link-trackable"
                          data-link-page="details"
                          data-link-type="externalweb"
                          href="https://scotiabank.com"
                        >
                          <span>
                            Internet Website
                          </span>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon pw-link__icon"
                            color="inherit"
                            focusable="false"
                            role="presentation"
                            size="18"
                            viewBox="0 0 30 30"
                          >
                            <path
                              d="M12 17.9999L28.5 1.49995"
                              fill="none"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              d="M24 16.4999V25.4999C24 27.1568 22.6568 28.4999 21 28.4999H4.49999C2.84314 28.4999 1.5 27.1568 1.5 25.4999V8.99994C1.5 7.34309 2.84314 5.99995 4.49999 5.99995H13.5"
                              fill="none"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              d="M19.5 1.49995H28.5V10.4999"
                              fill="none"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                        </a>
                        <br />
                        -
                        <a
                          class="pw-link pw-link-trackable"
                          data-link-page="details"
                          data-link-type="novadeeplink"
                          href="scotiabank://interac/payment"
                        >
                          <span>
                            Nova DeepLink
                          </span>
                        </a>
                      </li>
                      <li>
                        <form
                          action="/rs/svc/authentication/verify.rs"
                          class="eexperience-form"
                          method="post"
                        >
                          <input
                            hidden=""
                            name="applyKey"
                            readonly=""
                            type="text"
                            value="supplementry_apply_standalone"
                          />
                          <button
                            class="pw-link pw-link-trackable"
                          >
                            eexp
                          </button>
                        </form>
                      </li>
                      <li>
                        <button
                          class="pw-link pw-link-trackable"
                        >
                          renewmortgage
                        </button>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="preview-renderer"
        >
          <h2
            class="TextIntroductionstyle__Text-canvas-core__sc-rmax1m-0 bvdIjD TextIntroduction__text preview-renderer__header"
            color="black"
          >
            Intercept
          </h2>
          <div
            class="preview-renderer__preview"
          >
            <div>
              <span
                class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                id="htmlTitle"
              >
                test-intercept-title
              </span>
              <div
                aria-labelledby="htmlTitle"
                class="wave-intercept-container"
                id="wave-container"
                role="main"
                tabindex="0"
              >
                <div
                  class="wave-intercept-container__logo"
                >
                  <svg
                    aria-hidden="false"
                    aria-labelledby="bnsLogo-title"
                    class="SvgLogostyle__Wrapper-canvas-core__sc-1ed0csp-0 drBDpx ScotiaLogo"
                    fill="red"
                    focusable="true"
                    role="img"
                    size="24"
                    type="scotia"
                    viewBox="0 0 389.27 99.14"
                  >
                    <title
                      id="bnsLogo-title"
                    >
                      Scotia
                    </title>
                    <path
                      d="M236.11,99.31a34.48,34.48,0,1,0,34.47,34.48A34.52,34.52,0,0,0,236.11,99.31Zm0,49.68a15.2,15.2,0,1,1,15.19-15.2A15.22,15.22,0,0,1,236.11,149Z"
                      transform="translate(-66.73 -69.12)"
                    />
                    <polygon
                      points="247.77 31.83 238.36 31.83 238.36 11.48 217.75 11.48 217.75 31.83 208.34 31.83 208.34 50.48 217.75 50.48 217.75 97.5 238.36 97.5 238.36 50.48 247.77 50.48 247.77 31.83"
                    />
                    <rect
                      height="65.67"
                      width="20.6"
                      x="257.22"
                      y="31.83"
                    />
                    <path
                      d="M334.25,69.13a11.48,11.48,0,1,0,11.48,11.48A11.49,11.49,0,0,0,334.25,69.13Z"
                      transform="translate(-66.73 -69.12)"
                    />
                    <path
                      d="M425.11,166.62V101H405v6.91l-1.86-1.67a25.13,25.13,0,0,0-17.31-6.88c-17.72,0-32.7,15.79-32.7,34.48s15,34.47,32.7,34.47a25.13,25.13,0,0,0,17.31-6.88l1.86-1.66v6.9ZM389.05,149.3a15.52,15.52,0,1,1,15.52-15.51A15.53,15.53,0,0,1,389.05,149.3Z"
                      transform="translate(-66.73 -69.12)"
                    />
                    <path
                      d="M130.69,149a30,30,0,0,0,2.11-12.1c0-6.63-2.08-12.55-5.85-16.68-4.4-4.82-11.92-8.82-22.35-11.9a36.59,36.59,0,0,1-5.86-2.2,14.14,14.14,0,0,1-4.37-3.25A7.36,7.36,0,0,1,92.5,97.5c0-3,1.63-5.12,4.28-6.79,3.34-2.1,9.74-2.3,16.3.13A39.76,39.76,0,0,1,119.72,94l8.76-17.43a49.86,49.86,0,0,0-12.56-5.66,55.49,55.49,0,0,0-14-1.77,37.38,37.38,0,0,0-13.08,2.17,29.79,29.79,0,0,0-10,6.51,30.84,30.84,0,0,0-6.65,10A31.9,31.9,0,0,0,70,99.91a25.59,25.59,0,0,0,7.76,17.4c6,5.63,12.81,7.63,15.55,8.69s5.75,2,7.68,2.72a27.38,27.38,0,0,1,5.64,2.87,9,9,0,0,1,3,3.34,7.53,7.53,0,0,1,.64,4.19,8.59,8.59,0,0,1-2.93,5.66c-1.77,1.66-5,2.61-9.48,2.61a28.68,28.68,0,0,1-11.49-2.76A82.84,82.84,0,0,1,77,139.68l-10.3,17.94c7.19,6.39,18.84,10.64,29.21,10.64a49.53,49.53,0,0,0,15.53-2.48,35.77,35.77,0,0,0,11.77-6.57A30.76,30.76,0,0,0,130.69,149Z"
                      transform="translate(-66.73 -69.12)"
                    />
                    <path
                      d="M444.52,143.67A11.48,11.48,0,1,0,456,155.15,11.48,11.48,0,0,0,444.52,143.67Zm0,20.64a9.17,9.17,0,1,1,9.17-9.16A9.16,9.16,0,0,1,444.52,164.31Z"
                      transform="translate(-66.73 -69.12)"
                    />
                    <path
                      d="M444.5,157.06h-1.84v4.35H440.4V148.88h4.81a4.16,4.16,0,0,1,4.17,4.15,4.08,4.08,0,0,1-2.42,3.65l2.64,4.73h-2.71Zm-1.84-2.1h2.64a1.93,1.93,0,0,0,0-3.84h-2.64Z"
                      transform="translate(-66.73 -69.12)"
                    />
                    <path
                      d="M187.12,143.31a15.2,15.2,0,1,1,0-19l13.65-13.67a34.4,34.4,0,0,0-25.49-11.29c-19,0-35.56,13.53-35.56,34.48s16.55,34.47,35.56,34.47A34.4,34.4,0,0,0,200.77,157Z"
                      transform="translate(-66.73 -69.12)"
                    />
                  </svg>
                </div>
                <div
                  class="wave-intercept-container__content"
                >
                  <div
                    class="wave-intercept-container__image-container"
                  >
                    <img
                      alt="ID Image"
                      src="//images.ctfassets.net/gk5ecj6dpckc/1JC9CYXfDWK3NPQP47inqT/df376c910db7e1ddc7e24862f9bc841f/update-id.svg"
                    />
                  </div>
                  <h1
                    class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iejpSD"
                    color="black"
                  >
                    <div
                      class="wave-intercept-container__heading"
                    >
                      test-intercept-title
                    </div>
                  </h1>
                  <span
                    class="TextBodystyle__Text-canvas-core__sc-xx5i8s-0 cPQYFR TextBody__text"
                    color="black"
                    type="1"
                  >
                    <div
                      class="wave-intercept-container__description"
                    >
                      <p>
                        It seems like your &lt;strong&gt;{SOLUI_CCAU_IDV_ID_TYPE_END}&lt;/strong&gt; has expired. To make your banking experience safer, please update it to a valid document.
                      </p>
                    </div>
                  </span>
                </div>
                <nav
                  class="wave-intercept-container__footer"
                >
                  <button
                    aria-label="Accept button"
                    class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button NavButtonCorestyle__StyledBtnCore-canvas-core__sc-ssda85-0 cklHec SkipButtonstyle__Button-canvas-core__sc-19k2pkk-0 kATXrO NavButton__button--skip wave-intercept-container__cancel-cta cta-link"
                    href="scotia://wave/close"
                  >
                    <span
                      class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                      tabindex="-1"
                    >
                      <span
                        class="ButtonCore__text"
                      >
                        Skip
                      </span>
                    </span>
                  </button>
                  <button
                    aria-label="Accept button"
                    class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button NavButtonCorestyle__StyledBtnCore-canvas-core__sc-ssda85-0 cklHec ContinueSubmitNavButtonCorestyle__ContinueSubmitNavButtonCore-canvas-core__sc-1bpzl3p-0 hWDJTD NavButton__button--continue wave-intercept-container__accept-cta cta-link"
                    href="http://apply.online.scointnet.net//profile/id-remediation/id-verification?lang=en&country=DO&campaignMessageId=SOLUI_MESSAGE_ID_END&requiredIdType=SOLUI_CCAU_IDV_ID_TYPE_END"
                  >
                    <span
                      class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                      tabindex="-1"
                    >
                      <span
                        class="ButtonCore__text"
                      >
                        Continue
                      </span>
                      <span
                        class="ButtonCore__icon"
                      >
                         
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="18"
                          viewBox="0 0 30 30"
                        >
                          <path
                            d="M28.0034 14.9999H1.49998"
                            fill="none"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            d="M17.9348 4.43468L28.5 14.9999L17.9348 25.5651"
                            fill="none"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                         
                      </span>
                    </span>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
`;

exports[`Page rendering should render campaign preview page with only languages targetted 1`] = `
<body
  class="wave-intercept"
>
  <div
    class="wave-intercept"
  >
    <div
      class="preview"
    >
      <div
        class="preview__header"
      >
        <div
          class="preview__header--left"
        >
          <form>
            <div
              class="input-select-field content-preview__language-selection"
            >
              <div
                class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 InputContainer__input"
              >
                <div
                  class="Selectorstyle__Wrapper-canvas-core__sc-zgjj5j-0 KiToi Selector__wrap"
                >
                  <div
                    class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label"
                  >
                    <label
                      class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW"
                      for="language"
                      label="Language"
                    >
                      Language
                    </label>
                  </div>
                  <select
                    aria-describedby=""
                    class="Selectorstyle__Select-canvas-core__sc-zgjj5j-1 iPemYD Selector__select"
                    id="language"
                    name="language"
                  >
                    <option
                      class="Selector__placeholder"
                      disabled=""
                      value=""
                    >
                      Select a language
                    </option>
                    <option
                      data-testid="language-option"
                      value="en-US"
                    >
                      English (United States)
                    </option>
                  </select>
                  <svg
                    aria-hidden="true"
                    class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon Selector__icon"
                    color="currentColor"
                    focusable="false"
                    role="presentation"
                    size="18"
                    viewBox="0 0 30 30"
                  >
                    <path
                      d="M28.5 8.24991L15 21.7499L1.5 8.24991"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div
          class="preview__header--right"
        >
          <fieldset
            class="InputGroupstyle__StyledFieldset-canvas-core__sc-8rf6cz-1 iUkhXD InputGroup__fieldset"
            id="settings-inputgroup"
          >
            <legend
              class="InputGroupstyle__StyledLegend-canvas-core__sc-8rf6cz-0 fsDDjX"
            >
              Settings
            </legend>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline"
            >
              <label
                class="ToggleSwitchstyle__Wrapper-canvas-core__sc-168jcna-0 iKYIja ToggleSwitch__label"
                for="live-preview"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input ToggleInput"
                  id="live-preview"
                  name="Live Preview"
                  type="checkbox"
                  value=""
                />
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label labelSpacing"
                >
                  <div
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW labelSpacing"
                  >
                    Live Preview
                  </div>
                </div>
                <span
                  class="ToggleSwitchstyle__StyledSlider-canvas-core__sc-168jcna-1 fsEBQI ToggleSwitch__slider"
                >
                  <span
                    class="ToggleSwitchstyle__StyledSwitch-canvas-core__sc-168jcna-2 kWOVUq ToggleSwitch__switch"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-off"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 10 10"
                    >
                      <g
                        class="IconToggleSwitchOffstyle__StyledG-canvas-core__sc-r3kqbd-0 ezyJRW"
                        fill="none"
                        fill-rule="evenodd"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-496.000000, -153.000000)"
                        >
                          <g
                            transform="translate(439.000000, 143.000000)"
                          >
                            <g
                              transform="translate(47.000000, 0.000000)"
                            >
                              <g
                                id="Group"
                                transform="translate(11.000000, 11.000000)"
                              >
                                <line
                                  id="Stroke-184"
                                  x1="0"
                                  x2="8"
                                  y1="0"
                                  y2="8"
                                />
                                <line
                                  id="Stroke-185"
                                  x1="0"
                                  x2="8"
                                  y1="8"
                                  y2="0"
                                />
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-on"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 11 9"
                    >
                      <g
                        class="IconToggleSwitchOnstyle__StyledG-canvas-core__sc-16ds4pu-0 fVdUxI"
                        fill="none"
                        fill-rule="evenodd"
                        id="Page-1"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-522.000000, -111.000000)"
                        >
                          <g
                            transform="translate(439.000000, 100.000000)"
                          >
                            <g
                              transform="translate(84.000000, 12.000000)"
                            >
                              <polyline
                                points="0 3.73333333 3.15 7 9 0"
                              />
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </span>
                </span>
              </label>
            </div>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline"
            >
              <label
                class="ToggleSwitchstyle__Wrapper-canvas-core__sc-168jcna-0 iKYIja ToggleSwitch__label"
                for="dark-mode"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input ToggleInput"
                  id="dark-mode"
                  name="Dark Mode"
                  type="checkbox"
                  value=""
                />
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label labelSpacing"
                >
                  <div
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW labelSpacing"
                  >
                    Dark Mode
                  </div>
                </div>
                <span
                  class="ToggleSwitchstyle__StyledSlider-canvas-core__sc-168jcna-1 fsEBQI ToggleSwitch__slider"
                >
                  <span
                    class="ToggleSwitchstyle__StyledSwitch-canvas-core__sc-168jcna-2 kWOVUq ToggleSwitch__switch"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-off"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 10 10"
                    >
                      <g
                        class="IconToggleSwitchOffstyle__StyledG-canvas-core__sc-r3kqbd-0 ezyJRW"
                        fill="none"
                        fill-rule="evenodd"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-496.000000, -153.000000)"
                        >
                          <g
                            transform="translate(439.000000, 143.000000)"
                          >
                            <g
                              transform="translate(47.000000, 0.000000)"
                            >
                              <g
                                id="Group"
                                transform="translate(11.000000, 11.000000)"
                              >
                                <line
                                  id="Stroke-184"
                                  x1="0"
                                  x2="8"
                                  y1="0"
                                  y2="8"
                                />
                                <line
                                  id="Stroke-185"
                                  x1="0"
                                  x2="8"
                                  y1="8"
                                  y2="0"
                                />
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-on"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 11 9"
                    >
                      <g
                        class="IconToggleSwitchOnstyle__StyledG-canvas-core__sc-16ds4pu-0 fVdUxI"
                        fill="none"
                        fill-rule="evenodd"
                        id="Page-1"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-522.000000, -111.000000)"
                        >
                          <g
                            transform="translate(439.000000, 100.000000)"
                          >
                            <g
                              transform="translate(84.000000, 12.000000)"
                            >
                              <polyline
                                points="0 3.73333333 3.15 7 9 0"
                              />
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </span>
                </span>
              </label>
            </div>
            <div
              class="InputContainerstyle__Container-canvas-core__sc-1sov9au-0 gZMTxk InputContainer__input--inline"
            >
              <label
                class="ToggleSwitchstyle__Wrapper-canvas-core__sc-168jcna-0 iKYIja ToggleSwitch__label"
                for="preview-new"
              >
                <input
                  class="Inputstyle__InputMain-canvas-core__sc-1glnvsj-0 cYTPgn Input__input ToggleInput"
                  id="preview-new"
                  name="Preview New"
                  type="checkbox"
                  value=""
                />
                <div
                  class="Labelstyle__LabelWrapper-canvas-core__sc-pnw8rp-3 ejHhuX Label labelSpacing"
                >
                  <div
                    class="Labelstyle__StyledLabel-canvas-core__sc-pnw8rp-0 jFARUW labelSpacing"
                  >
                    Preview New
                  </div>
                </div>
                <span
                  class="ToggleSwitchstyle__StyledSlider-canvas-core__sc-168jcna-1 fsEBQI ToggleSwitch__slider"
                >
                  <span
                    class="ToggleSwitchstyle__StyledSwitch-canvas-core__sc-168jcna-2 kWOVUq ToggleSwitch__switch"
                  >
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-off"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 10 10"
                    >
                      <g
                        class="IconToggleSwitchOffstyle__StyledG-canvas-core__sc-r3kqbd-0 ezyJRW"
                        fill="none"
                        fill-rule="evenodd"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-496.000000, -153.000000)"
                        >
                          <g
                            transform="translate(439.000000, 143.000000)"
                          >
                            <g
                              transform="translate(47.000000, 0.000000)"
                            >
                              <g
                                id="Group"
                                transform="translate(11.000000, 11.000000)"
                              >
                                <line
                                  id="Stroke-184"
                                  x1="0"
                                  x2="8"
                                  y1="0"
                                  y2="8"
                                />
                                <line
                                  id="Stroke-185"
                                  x1="0"
                                  x2="8"
                                  y1="8"
                                  y2="0"
                                />
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                    <svg
                      aria-hidden="true"
                      class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bzEnEJ SvgIcon__icon icon-on"
                      color="currentColor"
                      focusable="false"
                      role="presentation"
                      size="10"
                      viewBox="0 0 11 9"
                    >
                      <g
                        class="IconToggleSwitchOnstyle__StyledG-canvas-core__sc-16ds4pu-0 fVdUxI"
                        fill="none"
                        fill-rule="evenodd"
                        id="Page-1"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                      >
                        <g
                          stroke-width="2"
                          transform="translate(-522.000000, -111.000000)"
                        >
                          <g
                            transform="translate(439.000000, 100.000000)"
                          >
                            <g
                              transform="translate(84.000000, 12.000000)"
                            >
                              <polyline
                                points="0 3.73333333 3.15 7 9 0"
                              />
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </span>
                </span>
              </label>
            </div>
          </fieldset>
        </div>
        <div
          class="preview__header--download"
        >
          <button
            class="TextButtonstyle__StyleTextButton-canvas-core__sc-1ssjpvn-0 kTBAqM TextButton__button preview__header--download-button"
            color="blue"
          >
            <svg
              aria-hidden="true"
              class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 bEEdQG SvgIcon__icon TextButtonstyle__StyledIcon-canvas-core__sc-1ssjpvn-2 khIDhN TextButton__icon--left"
              color="currentColor"
              focusable="false"
              role="presentation"
              size="16"
              viewBox="0 0 30 30"
            >
              <path
                d="M28.5001 20.7925V27.606"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M15.0003 1.5165V18.5895"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M8.15112 14.0899L15.0004 20.1941"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M22.1472 14.0899L15.0001 20.1941"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M1.5001 20.7925V27.606"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M28.4558 27.6892H1.65454"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <span
              class="TextButtonstyle__StyledText-canvas-core__sc-1ssjpvn-1 jBEOtl"
            >
              Download as PDF
            </span>
          </button>
        </div>
      </div>
      <div
        class="preview__help"
      >
        <div
          class="preview__help-icon"
        >
          <svg
            aria-hidden="true"
            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 CzcDu SvgIcon__icon"
            color="white"
            focusable="false"
            role="presentation"
            size="18"
            viewBox="0 0 24 24"
          >
            <path
              clip-rule="evenodd"
              d="M22.7997 11.9998C22.7997 17.9634 17.9633 22.7998 11.9997 22.7998C6.03418 22.7998 1.19971 17.9634 1.19971 11.9998C1.19971 6.03428 6.03418 1.1998 11.9997 1.1998C17.9633 1.1998 22.7997 6.03428 22.7997 11.9998Z"
              fill="none"
              fill-rule="evenodd"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              clip-rule="evenodd"
              d="M11.9997 17.3248C12.331 17.3248 12.5997 17.0562 12.5997 16.7248V11.3248C12.5997 10.9935 12.331 10.7248 11.9997 10.7248C11.6683 10.7248 11.3997 10.9935 11.3997 11.3248V16.7248C11.3997 17.0562 11.6683 17.3248 11.9997 17.3248Z"
              fill-rule="evenodd"
              stroke="none"
            />
            <ellipse
              cx="11.9996"
              cy="7.30462"
              rx="0.747122"
              ry="0.746635"
              stroke="none"
            />
          </svg>
        </div>
        <div
          class="preview__help-text"
        />
      </div>
      <div
        class="preview__preview-content"
        id="preview-content"
      >
        <div
          class="preview-renderer"
        >
          <h2
            class="TextIntroductionstyle__Text-canvas-core__sc-rmax1m-0 bvdIjD TextIntroduction__text preview-renderer__header"
            color="black"
          >
            Preview
          </h2>
          <div
            class="preview-renderer__preview"
          >
            <div>
              <div
                class="targeted-preview-card"
              >
                
                <h3
                  class="targeted-preview-card__title"
                >
                  <div>
                    test-preview-title
                  </div>
                </h3>
                <div
                  class="targeted-preview-card__description"
                >
                  <h1>
                    QAT - Preview: PA Multiple
                  </h1>
                  <p>
                    $15,500.00
                    <br />
                    (Credit)
                  </p>
                  <p>
                    December 31, 2019
                    <br />
                    (Other3)
                  </p>
                </div>
                <div
                  class="Marginstyle__Wrapper-canvas-core__sc-dm8riu-0 cOycvv Margin__container"
                >
                  <span
                    class="cta-link"
                  >
                    <button
                      class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary"
                    >
                      <span
                        class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                        tabindex="-1"
                      >
                        <span
                          class="ButtonCore__text"
                        >
                          Go!
                        </span>
                      </span>
                    </button>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="preview-renderer"
        >
          <h2
            class="TextIntroductionstyle__Text-canvas-core__sc-rmax1m-0 bvdIjD TextIntroduction__text preview-renderer__header"
            color="black"
          >
            Details
          </h2>
          <div
            class="preview-renderer__preview"
          >
            <div>
              <div
                class="template-generic"
              >
                <h1
                  class="template-generic__header template-generic__header--standalone"
                >
                  <div>
                    test-details-title
                  </div>
                </h1>
                <div
                  class="Cardstyle__Wrapper-canvas-core__sc-1mhf12g-0 YKZtT Card__container card"
                  type="floatLow"
                >
                  <h1
                    class="template-generic__header"
                  >
                    <div>
                      test-details-title
                    </div>
                  </h1>
                  <div
                    class="template-generic__description"
                  >
                    <p>
                      There are the various inline links that Pigeon can render:
                    </p>
                    <ul>
                      <li>
                        <a
                          class="pw-link pw-link-trackable"
                          data-link-page="details"
                          data-link-type="dial"
                          href="tel:**************"
                        >
                          <span>
                            Telephone
                          </span>
                        </a>
                      </li>
                      <li>
                        <a
                          class="pw-link pw-link-trackable"
                          data-link-page="details"
                          data-link-type="email"
                          href="mailto:<EMAIL>"
                        >
                          <span>
                            Email
                          </span>
                        </a>
                      </li>
                      <li>
                        <a
                          class="pw-link pw-link-trackable"
                          data-link-page="details"
                          data-link-type="externalweb"
                          href="http://businessnet.bns/BusinessNet/MediaLibrary/English/_documents/keyTopics/paymentServices/BNS_StandardizedPaymentFileFormatInventoryList.pdf"
                        >
                          <span>
                            PDF Link
                          </span>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon pw-link__icon pw-link__icon--pdf"
                            color="inherit"
                            focusable="false"
                            role="presentation"
                            size="18"
                            viewBox="0 0 30 30"
                          >
                            <path
                              d="M22.1827 24.8897V28.4999H1.53564V8.54341L8.82284 1.49993H22.1827V9.08895"
                              fill="none"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              d="M1.53564 8.54341H8.82284V1.49993"
                              fill="none"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              clip-rule="evenodd"
                              d="M17.9035 13.0714V20.489H20.7558C21.5822 20.489 22.2934 20.3389 22.8895 20.0386C23.4855 19.7384 23.9444 19.311 24.2662 18.7565C24.588 18.2019 24.7489 17.5396 24.7489 16.7696C24.7489 16.0067 24.588 15.3479 24.2662 14.7934C23.9444 14.2388 23.4855 13.8132 22.8895 13.5165C22.2934 13.2198 21.5822 13.0714 20.7558 13.0714H17.9035ZM11.1787 20.4891V13.0716H13.5702C14.6599 13.0716 15.4992 13.3047 16.0879 13.7709C16.6766 14.2372 16.971 14.9012 16.971 15.7631C16.971 16.6179 16.6766 17.2784 16.0879 17.7446C15.4992 18.2109 14.6599 18.444 13.5702 18.444H12.8791V20.4891H11.1787ZM13.3947 14.4915H12.8791V17.0241H13.3947C14.0529 17.0241 14.5301 16.9234 14.8263 16.7221C15.1225 16.5207 15.2706 16.2011 15.2706 15.7631C15.2706 15.318 15.1225 14.9948 14.8263 14.7935C14.5301 14.5922 14.0529 14.4915 13.3947 14.4915ZM20.5802 14.5127H19.6039V19.048H20.5802C21.3555 19.048 21.9533 18.8502 22.3739 18.4546C22.7944 18.059 23.0047 17.4974 23.0047 16.7698C23.0047 16.0563 22.7944 15.5017 22.3739 15.1061C21.9533 14.7105 21.3555 14.5127 20.5802 14.5127ZM25.6924 13.0714V20.489H27.3928V17.6703H30.0256V16.2292H27.3928V14.5125H30.4644V13.0714H25.6924Z"
                              fill-rule="evenodd"
                              stroke="none"
                            />
                            <path
                              d="M17.9035 20.489H17.7785V20.614H17.9035V20.489ZM17.9035 13.0714V12.9464H17.7785V13.0714H17.9035ZM22.8895 20.0386L22.9457 20.1503L22.8895 20.0386ZM24.2662 14.7934L24.1581 14.8561V14.8561L24.2662 14.7934ZM22.8895 13.5165L22.8338 13.6284L22.8895 13.5165ZM11.1787 13.0716V12.9466H11.0537V13.0716H11.1787ZM11.1787 20.4891H11.0537V20.6141H11.1787V20.4891ZM16.0879 13.7709L16.1655 13.6729L16.0879 13.7709ZM12.8791 18.444V18.319H12.7541V18.444H12.8791ZM12.8791 20.4891V20.6141H13.0041V20.4891H12.8791ZM12.8791 14.4915V14.3665H12.7541V14.4915H12.8791ZM12.8791 17.0241H12.7541V17.1491H12.8791V17.0241ZM14.8263 16.7221L14.7561 16.6187L14.8263 16.7221ZM19.6039 14.5127V14.3877H19.4789V14.5127H19.6039ZM19.6039 19.048H19.4789V19.173H19.6039V19.048ZM22.3739 18.4546L22.4595 18.5457L22.3739 18.4546ZM22.3739 15.1061L22.2882 15.1971L22.3739 15.1061ZM25.6924 20.489H25.5674V20.614H25.6924V20.489ZM25.6924 13.0714V12.9464H25.5674V13.0714H25.6924ZM27.3928 20.489V20.614H27.5178V20.489H27.3928ZM27.3928 17.6703V17.5453H27.2678V17.6703H27.3928ZM30.0256 17.6703V17.7953H30.1506V17.6703H30.0256ZM30.0256 16.2292H30.1506V16.1042H30.0256V16.2292ZM27.3928 16.2292H27.2678V16.3542H27.3928V16.2292ZM27.3928 14.5125V14.3875H27.2678V14.5125H27.3928ZM30.4644 14.5125V14.6375H30.5894V14.5125H30.4644ZM30.4644 13.0714H30.5894V12.9464H30.4644V13.0714ZM18.0285 20.489V13.0714H17.7785V20.489H18.0285ZM20.7558 20.364H17.9035V20.614H20.7558V20.364ZM22.8332 19.927C22.2583 20.2166 21.5672 20.364 20.7558 20.364V20.614C21.5972 20.614 22.3285 20.4611 22.9457 20.1503L22.8332 19.927ZM24.1581 18.6937C23.849 19.2264 23.4085 19.6372 22.8332 19.927L22.9457 20.1503C23.5625 19.8396 24.0399 19.3956 24.3743 18.8192L24.1581 18.6937ZM24.6239 16.7696C24.6239 17.522 24.4668 18.1617 24.1581 18.6937L24.3743 18.8192C24.7092 18.2421 24.8739 17.5572 24.8739 16.7696H24.6239ZM24.1581 14.8561C24.4669 15.3883 24.6239 16.0245 24.6239 16.7696H24.8739C24.8739 15.9888 24.7091 15.3076 24.3743 14.7306L24.1581 14.8561ZM22.8338 13.6284C23.4088 13.9146 23.8491 14.3235 24.1581 14.8561L24.3743 14.7306C24.0398 14.154 23.5622 13.7117 22.9452 13.4046L22.8338 13.6284ZM20.7558 13.1964C21.5674 13.1964 22.2587 13.3421 22.8338 13.6284L22.9452 13.4046C22.3281 13.0974 21.5969 12.9464 20.7558 12.9464V13.1964ZM17.9035 13.1964H20.7558V12.9464H17.9035V13.1964ZM11.0537 13.0716V20.4891H11.3037V13.0716H11.0537ZM13.5702 12.9466H11.1787V13.1966H13.5702V12.9466ZM16.1655 13.6729C15.5467 13.1828 14.6758 12.9466 13.5702 12.9466V13.1966C14.6441 13.1966 15.4517 13.4265 16.0103 13.8689L16.1655 13.6729ZM17.096 15.7631C17.096 14.8694 16.7888 14.1666 16.1655 13.6729L16.0103 13.8689C16.5644 14.3078 16.846 14.9331 16.846 15.7631H17.096ZM16.1655 17.8426C16.7886 17.3492 17.096 16.6501 17.096 15.7631H16.846C16.846 16.5857 16.5647 17.2076 16.0103 17.6466L16.1655 17.8426ZM13.5702 18.569C14.6758 18.569 15.5467 18.3327 16.1655 17.8426L16.0103 17.6466C15.4517 18.089 14.6441 18.319 13.5702 18.319V18.569ZM12.8791 18.569H13.5702V18.319H12.8791V18.569ZM13.0041 20.4891V18.444H12.7541V20.4891H13.0041ZM11.1787 20.6141H12.8791V20.3641H11.1787V20.6141ZM12.8791 14.6165H13.3947V14.3665H12.8791V14.6165ZM13.0041 17.0241V14.4915H12.7541V17.0241H13.0041ZM13.3947 16.8991H12.8791V17.1491H13.3947V16.8991ZM14.7561 16.6187C14.4917 16.7984 14.046 16.8991 13.3947 16.8991V17.1491C14.0599 17.1491 14.5686 17.0484 14.8966 16.8255L14.7561 16.6187ZM15.1456 15.7631C15.1456 16.1702 15.0102 16.446 14.7561 16.6187L14.8966 16.8255C15.2349 16.5955 15.3956 16.232 15.3956 15.7631H15.1456ZM14.7561 14.8969C15.0096 15.0692 15.1456 15.3481 15.1456 15.7631H15.3956C15.3956 15.288 15.2355 14.9205 14.8966 14.6901L14.7561 14.8969ZM13.3947 14.6165C14.046 14.6165 14.4917 14.7172 14.7561 14.8969L14.8966 14.6901C14.5686 14.4671 14.0599 14.3665 13.3947 14.3665V14.6165ZM19.6039 14.6377H20.5802V14.3877H19.6039V14.6377ZM19.7289 19.048V14.5127H19.4789V19.048H19.7289ZM20.5802 18.923H19.6039V19.173H20.5802V18.923ZM22.2882 18.3636C21.8979 18.7308 21.3348 18.923 20.5802 18.923V19.173C21.3762 19.173 22.0088 18.9697 22.4595 18.5457L22.2882 18.3636ZM22.8797 16.7698C22.8797 17.4725 22.6774 17.9974 22.2882 18.3636L22.4595 18.5457C22.9114 18.1206 23.1297 17.5223 23.1297 16.7698H22.8797ZM22.2882 15.1971C22.6779 15.5638 22.8797 16.082 22.8797 16.7698H23.1297C23.1297 16.0305 22.9109 15.4397 22.4595 15.0151L22.2882 15.1971ZM20.5802 14.6377C21.3348 14.6377 21.8979 14.8299 22.2882 15.1971L22.4595 15.0151C22.0088 14.591 21.3762 14.3877 20.5802 14.3877V14.6377ZM25.8174 20.489V13.0714H25.5674V20.489H25.8174ZM27.3928 20.364H25.6924V20.614H27.3928V20.364ZM27.2678 17.6703V20.489H27.5178V17.6703H27.2678ZM30.0256 17.5453H27.3928V17.7953H30.0256V17.5453ZM29.9006 16.2292V17.6703H30.1506V16.2292H29.9006ZM27.3928 16.3542H30.0256V16.1042H27.3928V16.3542ZM27.2678 14.5125V16.2292H27.5178V14.5125H27.2678ZM30.4644 14.3875H27.3928V14.6375H30.4644V14.3875ZM30.3394 13.0714V14.5125H30.5894V13.0714H30.3394ZM25.6924 13.1964H30.4644V12.9464H25.6924V13.1964Z"
                              stroke="none"
                            />
                          </svg>
                        </a>
                      </li>
                      <li>
                        <a
                          class="pw-link pw-link-trackable"
                          data-link-page="details"
                          data-link-type="externalweb"
                          href="https://scotiabank.com"
                        >
                          <span>
                            Internet Website
                          </span>
                          <svg
                            aria-hidden="true"
                            class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon pw-link__icon"
                            color="inherit"
                            focusable="false"
                            role="presentation"
                            size="18"
                            viewBox="0 0 30 30"
                          >
                            <path
                              d="M12 17.9999L28.5 1.49995"
                              fill="none"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              d="M24 16.4999V25.4999C24 27.1568 22.6568 28.4999 21 28.4999H4.49999C2.84314 28.4999 1.5 27.1568 1.5 25.4999V8.99994C1.5 7.34309 2.84314 5.99995 4.49999 5.99995H13.5"
                              fill="none"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              d="M19.5 1.49995H28.5V10.4999"
                              fill="none"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                        </a>
                        <br />
                        -
                        <a
                          class="pw-link pw-link-trackable"
                          data-link-page="details"
                          data-link-type="novadeeplink"
                          href="scotiabank://interac/payment"
                        >
                          <span>
                            Nova DeepLink
                          </span>
                        </a>
                      </li>
                      <li>
                        <form
                          action="/rs/svc/authentication/verify.rs"
                          class="eexperience-form"
                          method="post"
                        >
                          <input
                            hidden=""
                            name="applyKey"
                            readonly=""
                            type="text"
                            value="supplementry_apply_standalone"
                          />
                          <button
                            class="pw-link pw-link-trackable"
                          >
                            eexp
                          </button>
                        </form>
                      </li>
                      <li>
                        <button
                          class="pw-link pw-link-trackable"
                        >
                          renewmortgage
                        </button>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="preview-renderer"
        >
          <h2
            class="TextIntroductionstyle__Text-canvas-core__sc-rmax1m-0 bvdIjD TextIntroduction__text preview-renderer__header"
            color="black"
          >
            Intercept
          </h2>
          <div
            class="preview-renderer__preview"
          >
            <div>
              <span
                class="SROnlystyle__Wrapper-canvas-core__sc-10lzz2q-0 fSHgUF SROnly__container"
                id="htmlTitle"
              >
                test-intercept-title
              </span>
              <div
                aria-labelledby="htmlTitle"
                class="wave-intercept-container"
                id="wave-container"
                role="main"
                tabindex="0"
              >
                <div
                  class="wave-intercept-container__logo"
                >
                  <svg
                    aria-hidden="false"
                    aria-labelledby="bnsLogo-title"
                    class="SvgLogostyle__Wrapper-canvas-core__sc-1ed0csp-0 drBDpx ScotiaLogo"
                    fill="red"
                    focusable="true"
                    role="img"
                    size="24"
                    type="scotia"
                    viewBox="0 0 389.27 99.14"
                  >
                    <title
                      id="bnsLogo-title"
                    >
                      Scotia
                    </title>
                    <path
                      d="M236.11,99.31a34.48,34.48,0,1,0,34.47,34.48A34.52,34.52,0,0,0,236.11,99.31Zm0,49.68a15.2,15.2,0,1,1,15.19-15.2A15.22,15.22,0,0,1,236.11,149Z"
                      transform="translate(-66.73 -69.12)"
                    />
                    <polygon
                      points="247.77 31.83 238.36 31.83 238.36 11.48 217.75 11.48 217.75 31.83 208.34 31.83 208.34 50.48 217.75 50.48 217.75 97.5 238.36 97.5 238.36 50.48 247.77 50.48 247.77 31.83"
                    />
                    <rect
                      height="65.67"
                      width="20.6"
                      x="257.22"
                      y="31.83"
                    />
                    <path
                      d="M334.25,69.13a11.48,11.48,0,1,0,11.48,11.48A11.49,11.49,0,0,0,334.25,69.13Z"
                      transform="translate(-66.73 -69.12)"
                    />
                    <path
                      d="M425.11,166.62V101H405v6.91l-1.86-1.67a25.13,25.13,0,0,0-17.31-6.88c-17.72,0-32.7,15.79-32.7,34.48s15,34.47,32.7,34.47a25.13,25.13,0,0,0,17.31-6.88l1.86-1.66v6.9ZM389.05,149.3a15.52,15.52,0,1,1,15.52-15.51A15.53,15.53,0,0,1,389.05,149.3Z"
                      transform="translate(-66.73 -69.12)"
                    />
                    <path
                      d="M130.69,149a30,30,0,0,0,2.11-12.1c0-6.63-2.08-12.55-5.85-16.68-4.4-4.82-11.92-8.82-22.35-11.9a36.59,36.59,0,0,1-5.86-2.2,14.14,14.14,0,0,1-4.37-3.25A7.36,7.36,0,0,1,92.5,97.5c0-3,1.63-5.12,4.28-6.79,3.34-2.1,9.74-2.3,16.3.13A39.76,39.76,0,0,1,119.72,94l8.76-17.43a49.86,49.86,0,0,0-12.56-5.66,55.49,55.49,0,0,0-14-1.77,37.38,37.38,0,0,0-13.08,2.17,29.79,29.79,0,0,0-10,6.51,30.84,30.84,0,0,0-6.65,10A31.9,31.9,0,0,0,70,99.91a25.59,25.59,0,0,0,7.76,17.4c6,5.63,12.81,7.63,15.55,8.69s5.75,2,7.68,2.72a27.38,27.38,0,0,1,5.64,2.87,9,9,0,0,1,3,3.34,7.53,7.53,0,0,1,.64,4.19,8.59,8.59,0,0,1-2.93,5.66c-1.77,1.66-5,2.61-9.48,2.61a28.68,28.68,0,0,1-11.49-2.76A82.84,82.84,0,0,1,77,139.68l-10.3,17.94c7.19,6.39,18.84,10.64,29.21,10.64a49.53,49.53,0,0,0,15.53-2.48,35.77,35.77,0,0,0,11.77-6.57A30.76,30.76,0,0,0,130.69,149Z"
                      transform="translate(-66.73 -69.12)"
                    />
                    <path
                      d="M444.52,143.67A11.48,11.48,0,1,0,456,155.15,11.48,11.48,0,0,0,444.52,143.67Zm0,20.64a9.17,9.17,0,1,1,9.17-9.16A9.16,9.16,0,0,1,444.52,164.31Z"
                      transform="translate(-66.73 -69.12)"
                    />
                    <path
                      d="M444.5,157.06h-1.84v4.35H440.4V148.88h4.81a4.16,4.16,0,0,1,4.17,4.15,4.08,4.08,0,0,1-2.42,3.65l2.64,4.73h-2.71Zm-1.84-2.1h2.64a1.93,1.93,0,0,0,0-3.84h-2.64Z"
                      transform="translate(-66.73 -69.12)"
                    />
                    <path
                      d="M187.12,143.31a15.2,15.2,0,1,1,0-19l13.65-13.67a34.4,34.4,0,0,0-25.49-11.29c-19,0-35.56,13.53-35.56,34.48s16.55,34.47,35.56,34.47A34.4,34.4,0,0,0,200.77,157Z"
                      transform="translate(-66.73 -69.12)"
                    />
                  </svg>
                </div>
                <div
                  class="wave-intercept-container__content"
                >
                  <div
                    class="wave-intercept-container__image-container"
                  >
                    <img
                      alt="ID Image"
                      src="//images.ctfassets.net/gk5ecj6dpckc/1JC9CYXfDWK3NPQP47inqT/df376c910db7e1ddc7e24862f9bc841f/update-id.svg"
                    />
                  </div>
                  <h1
                    class="TextHeadlinestyle__Text-canvas-core__sc-rml86m-0 iejpSD"
                    color="black"
                  >
                    <div
                      class="wave-intercept-container__heading"
                    >
                      test-intercept-title
                    </div>
                  </h1>
                  <span
                    class="TextBodystyle__Text-canvas-core__sc-xx5i8s-0 cPQYFR TextBody__text"
                    color="black"
                    type="1"
                  >
                    <div
                      class="wave-intercept-container__description"
                    >
                      <p>
                        It seems like your &lt;strong&gt;{SOLUI_CCAU_IDV_ID_TYPE_END}&lt;/strong&gt; has expired. To make your banking experience safer, please update it to a valid document.
                      </p>
                    </div>
                  </span>
                </div>
                <nav
                  class="wave-intercept-container__footer"
                >
                  <button
                    aria-label="Accept button"
                    class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button NavButtonCorestyle__StyledBtnCore-canvas-core__sc-ssda85-0 cklHec SkipButtonstyle__Button-canvas-core__sc-19k2pkk-0 kATXrO NavButton__button--skip wave-intercept-container__cancel-cta cta-link"
                    href="scotia://wave/close"
                  >
                    <span
                      class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                      tabindex="-1"
                    >
                      <span
                        class="ButtonCore__text"
                      >
                        Skip
                      </span>
                    </span>
                  </button>
                  <button
                    aria-label="Accept button"
                    class="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button NavButtonCorestyle__StyledBtnCore-canvas-core__sc-ssda85-0 cklHec ContinueSubmitNavButtonCorestyle__ContinueSubmitNavButtonCore-canvas-core__sc-1bpzl3p-0 hWDJTD NavButton__button--continue wave-intercept-container__accept-cta cta-link"
                    href="http://apply.online.scointnet.net//profile/id-remediation/id-verification?lang=en&country=DO&campaignMessageId=SOLUI_MESSAGE_ID_END&requiredIdType=SOLUI_CCAU_IDV_ID_TYPE_END"
                  >
                    <span
                      class="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                      tabindex="-1"
                    >
                      <span
                        class="ButtonCore__text"
                      >
                        Continue
                      </span>
                      <span
                        class="ButtonCore__icon"
                      >
                         
                        <svg
                          aria-hidden="true"
                          class="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 fqPzSy SvgIcon__icon"
                          color="currentColor"
                          focusable="false"
                          role="presentation"
                          size="18"
                          viewBox="0 0 30 30"
                        >
                          <path
                            d="M28.0034 14.9999H1.49998"
                            fill="none"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            d="M17.9348 4.43468L28.5 14.9999L17.9348 25.5651"
                            fill="none"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                         
                      </span>
                    </span>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
`;
