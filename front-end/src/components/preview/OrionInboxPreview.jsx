import React from 'react';
import PropTypes from 'prop-types';
import { useLocation } from 'react-router-dom';
import moment from 'moment';
import 'moment/locale/fr';

import TextIntroduction from 'canvas-core-react/lib/TextIntroduction';
import TextCaption from 'canvas-core-react/lib/TextCaption';
import IconMailRead from 'canvas-core-react/lib/IconMailRead';
import IconDelete from 'canvas-core-react/lib/IconDelete';

export default function OrionInboxPreview({ contentData }) {
  const location = useLocation();

  const searchParams = new URLSearchParams(location.search);
  const language = searchParams.get('language') || 'en';
  const formatDateLocalized = (date, locale) => {
    // if (!date) return '';

    const momentDate = moment(date);

    // if (!momentDate.isValid()) return '';

    momentDate.locale(locale);

    return momentDate.format('MMM DD, YYYY');
  };

  return (
    <div className="orion-inbox-preview">
      <TextIntroduction component="h2" className="orion-inbox-preview__header">
        Preview
      </TextIntroduction>
      <div className="orion-inbox-preview__preview">
        <div className="orion-inbox-preview__row" role="row">
          <div className="orion-inbox-preview__content-cell" role="rowheader">
            <button
              tabIndex="0"
              id={`campaign-id-${contentData?.id}`}
              role="button"
              data-testid="viewCampaign-text"
              className="orion-inbox-preview__content-button"
              type="button"
            >
              <TextCaption
                component="p"
                className="orion-inbox-preview__title"
                bold
              >
                { contentData.content.title }
              </TextCaption>
              <TextCaption
                component="p"
                className="orion-inbox-preview__description"
                color="gray"
              >
                { contentData.content.description }
              </TextCaption>
            </button>
          </div>

          <div className="orion-inbox-preview__date-cell" role="cell">
            <button
              className="orion-inbox-preview__date-button"
              role="button"
              data-testid="viewCampaign-date"
              tabIndex="0"
              type="button"
            >
              { formatDateLocalized(contentData.updated_at, language) }
            </button>
          </div>

          <div className="orion-inbox-preview__action-cell" role="cell">
            <Tooltip
              text="Mark as read"
              id={`tooltip-read-${contentData?.id}`}
            >
              <IconMailRead
                className="orion-inbox-preview__icon"
                focusable="false"
                role="button"
                aria-hidden="false"
                aria-label={`Mark as read ${contentData.content.title}`}
                size="24"
                color="blue"
                tabIndex="0"
                data-testid="viewCampaign"
              />
            </Tooltip>
          </div>

          <div className="orion-inbox-preview__action-cell" role="cell">
            <Tooltip
              text="Delete"
              id={`tooltip-delete-${contentData?.id}`}
            >
              <IconDelete
                className="orion-inbox-preview__icon"
                focusable="false"
                role="button"
                aria-hidden="false"
                aria-label={`Delete ${contentData.content.title}`}
                size="24"
                color="blue"
                tabIndex="0"
                data-testid="deleteCampaign"
              />
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
  );
}

OrionInboxPreview.propTypes = {
  contentData: PropTypes.shape({
    id: PropTypes.string,
    updated_at: PropTypes.string,
    content: PropTypes.shape({
      title: PropTypes.string.isRequired,
      description: PropTypes.string.isRequired,
    }).isRequired,
  }).isRequired,
};

const Tooltip = ({ children, text, id }) => {
  const tooltipId = id;
  return (
    <span className="orion-inbox-preview__tooltip">
      { children }
        <span
          id={tooltipId}
          role="tooltip"
          className="orion-inbox-preview__tooltip-text"
        >
          { text }
        </span>
    </span>
  );
};

Tooltip.propTypes = {
  children: PropTypes.element.isRequired,
  text: PropTypes.string.isRequired,
  id: PropTypes.string,
};
