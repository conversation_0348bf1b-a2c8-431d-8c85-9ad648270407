import React from 'react';
import { render } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';

import PreviewAlert from './previewAlert';

describe('PreviewAlert', () => {
  it('Renders component', () => {
    const { container } = render(
      <MemoryRouter
        initialEntries={[
          '/preview/4szkx38resvm/alert/1JCwrz5mlnwGt5NGb2xcGR/alert/nova/undefined?language=en-US',
        ]}
      >
        <PreviewAlert
          content={{ title: 'test-title', message: 'test-message' }}
          handleLanguage={() => jest.fn()}
          type="starburst__alert"
          isDismissible
          availableLocales={[ { code: 'en', name: 'en' } ]}
        />
      </MemoryRouter>
    );

    expect(container).toMatchSnapshot();
  });
});
