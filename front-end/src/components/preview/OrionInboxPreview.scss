$row-hover-background: rgb(227, 224, 234);
.orion-inbox-preview {
  border-top: solid 4px #8230df;
  background-color: #fff;
  height: 100%;

  &__header {
    padding: 1rem 2rem;
    background-color: $brand-white;
    margin: 0;
  }

  &__preview {
    padding: 3rem 12px;
    margin: 0 auto;
    max-width: 1280px;
    background-color: $brand-white;
    height: 100%;

    & > div {
      height: 100%;
    }
  }

  &__row {
    font-size: 1.6rem;
    text-align: left;
    border: 0;
    font-family: inherit;
    margin: 0;
    padding: 0;
    vertical-align: baseline;
    display: flex;
    align-items: stretch;
    background-color: $brand-white;
    border-bottom: 0.1rem solid rgb(226, 232, 238);
    width: 100%;

    &:hover {
      background: $row-hover-background;
    }
  }

  &__content-cell {
    -webkit-font-smoothing: antialiased;
    font-size: 1.6rem;
    text-align: left;
    border: 0;
    font-family: inherit;
    vertical-align: baseline;
    display: flex;
    align-items: flex-start;
    flex: 7 0 0px;
    min-width: 10rem;
    max-width: 100%;
    margin: 0px;
    padding: 1.2rem 1.8rem;
    position: relative;
    transition: box-shadow 0.5s;
    min-height: 4.8rem;

    &:hover {
      background: $row-hover-background;
    }
  }

  &__content-button {
    width: 100%;
    border: none;
    text-align: left;
    padding: 0px;
    background-color: inherit;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;

    &:hover {
      background: $row-hover-background;
    }
  }

  &__title {
    width: 100%;
    text-decoration: underline dotted;
  }

  &__description {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    margin: 0;
  }

  &__date-cell {
    font-size: 1.6rem;
    text-align: left;
    border: 0;
    font-family: inherit;
    vertical-align: baseline;
    display: flex;
    align-items: flex-start;
    flex: 1 0 0px;
    max-width: 100%;
    margin: 0px;
    padding: 1.2rem 1.8rem;
    position: relative;
    transition: box-shadow 0.5s;
    min-height: 4.8rem;
    min-width: 14rem;

    &:hover {
      background: $row-hover-background;
    }
  }

  &__date-button {
    font-size: 1.4rem;
    font-weight: normal;
    line-height: 2.1rem;
    font-family: 'Scotia Bold', Arial, Helvetica, 'sans-serif';
    display: inline-flex;
    align-items: flex-start;
    background-color: inherit;
    border: none;
    text-align: left;
    padding: 0px;
    cursor: pointer;

    &:focus {
      outline: 2px solid #0066cc;
      outline-offset: 2px;
    }
  }

  &__action-cell {
    font-size: 1.6rem;
    text-align: left;
    border: 0;
    font-family: inherit;
    vertical-align: baseline;
    display: flex;
    align-items: flex-start;
    flex: 0.5 0 0px;
    min-width: 6rem;
    max-width: 100%;
    margin: 0px;
    padding: 1.2rem 1.8rem;
    box-sizing: border-box;
    position: relative;
    transition: box-shadow 0.5s;
    min-height: 4.8rem;
    justify-content: flex-end;
    cursor: pointer;

    @media (max-width: 768px) {
      display: none;
    }
  }

  &__tooltip {
    position: relative;
    display: inline-block;

    &:hover .orion-inbox-preview__tooltip-text,
    &:focus-within .orion-inbox-preview__tooltip-text {
      opacity: 1;
      visibility: visible;
      transform: translate(-50%, 0);
    }
  }

  &__tooltip-text {
    width: max-content;
    background-color: #00749d;
    color: $brand-white;
    text-align: center;
    border-radius: 0.4rem;
    padding: 8px;
    position: absolute;
    z-index: 1;
    left: 50%;
    top: 5rem;
    transform: translate(-50%, -50%);
    box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.24);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s, transform 0.2s;
  }
}
