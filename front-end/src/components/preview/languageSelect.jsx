import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';

import { InputSelectField } from '../formFields/reactHookForm';

const LanguageSelect = ({
  defaultLocale,
  languageChanged,
  options,
}) => {
  const { control, getValues, watch } = useForm({
    mode: 'onChange',
    defaultValues: { language: defaultLocale },
  });

  useEffect(() => {
    getValues('language') && languageChanged(getValues('language'))();
  }, [ watch('language') ]);

  return <>
    <form>
      <InputSelectField
        className="content-preview__language-selection"
        control={control}
        name="language"
        label="Language"
        placeholder="Select a language"
        options={options && options.map(lang => ({ id: lang.code, name: lang.name }))}
      />
    </form>
  </>;
};

LanguageSelect.propTypes = {
  defaultLocale: PropTypes.string,
  languageChanged: PropTypes.func.isRequired,
  options: PropTypes.array,
};

export default LanguageSelect;
