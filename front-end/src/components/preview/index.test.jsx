import React from 'react';
import { shallow } from 'enzyme';
import mockAxios from 'axios';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { fireEvent, render, screen } from '@testing-library/react';
import { act } from 'react-dom/test-utils';

import Preview from './index';
import { renderPage } from '../../utils/testing-library-utils';

const map = {};
window.addEventListener = jest.fn((event, cb) => {
  map[event] = cb;
});
window.opener = {
  postMessage: jest.fn(),
};
window.open = jest.fn();
window.close = jest.fn();

const history = {
  push: jest.fn(),
};

const commonInitialState = {
  data: {},
  error: {},
};
const commonProps = {
  match: {
    params: {
      space: 'test-space',
      type: 'campaign',
      contentId: 'test-content-id',
      container: 'container',
      isDismissible: 'false',
      languages: 'en,fr',
    },
  },
  history: history,
  location: { search: '' },
};

const mockLocalesRes = {
  data: {
    total: 2,
    items: [
      { code: 'en-US', name: 'English (United States)', default: true },
      { code: 'fr', name: 'French' },
    ],
  },
  notifications: [],
};
const targetedCampaignPreviewContent = {
  'name': 'QAT - Preview - Bruce: PA Multiple',
  'title': 'test-preview-title',
  'description': '# QAT - Preview: PA Multiple\n\n$15,500.00\n(Credit)\n\nDecember 31, 2019\n(Other3)',
  'ctaLink': {
    'name': 'Pigeon Web - Details',
    'linkText': 'Go!',
    'linkAction': {
      'name': 'Pigeon Web - Details',
      'url': 'pigeon-web',
    },
    'accessibilityText': 'Pigeon Web - Details',
  },
};

const targetedCampaignTemplate2DetailsContent = {
  'title': 'test-details-title',
  'cards': [ {
    'description': 'There are the various inline links that Pigeon can render:\n- [Telephone](tel:**************)\n- [Email](mailto:<EMAIL>)\n- [PDF Link](http://businessnet.bns/BusinessNet/MediaLibrary/English/_documents/keyTopics/paymentServices/BNS_StandardizedPaymentFileFormatInventoryList.pdf)\n- [Internet Website](https://scotiabank.com)\n-[Nova DeepLink](scotiabank://interac/payment)\n- [eexp](eexperience:/rs/svc/authentication/verify.rs?applyKey=supplementry_apply_standalone)\n- [renewmortgage](scotiahome:/verify.bns)',
  } ],
};

const ccauContent = {
  'name': 'WAVE - Intercept - Details ',
  'isScotiaLogoVisible': true,
  'contents': [
    {
      'name': 'WAVE - Intercept - Update ID',
      'heroImage': {
        'name': 'WAVE - Intercept - Image',
        'lightModeImage': {
          'title': 'update-id',
          'description': '',
          'file': {
            'url': '//images.ctfassets.net/gk5ecj6dpckc/1JC9CYXfDWK3NPQP47inqT/df376c910db7e1ddc7e24862f9bc841f/update-id.svg',
            'details': {
              'size': 9584,
              'image': {
                'width': 174,
                'height': 152,
              },
            },
            'fileName': 'update-id.svg',
            'contentType': 'image/svg+xml',
          },
        },
        'altText': 'ID Image',
      },
      'heading': 'test-intercept-title',
      'description': 'It seems like your <strong>{SOLUI_CCAU_IDV_ID_TYPE_END}</strong> has expired. To make your banking experience safer, please update it to a valid document.',
    },
  ],
  'cancelCta': {
    'name': 'WAVE - Intercept - Skip',
    'accessibilityText': 'Accept button',
    'linkText': 'Skip',
    'linkAction': {
      'name': 'WAVE - Skip CTA',
      'url': 'scotia://wave/close',
    },
  },
  'acceptCta': {
    'name': 'WAVE - Intercept - Continue ',
    'accessibilityText': 'Accept button',
    'linkText': 'Continue',
    'linkAction': {
      'name': 'deeplink to cob intro page',
      'url': 'http://apply.online.scointnet.net//profile/id-remediation/id-verification?lang=en&country=DO&campaignMessageId=SOLUI_MESSAGE_ID_END&requiredIdType=SOLUI_CCAU_IDV_ID_TYPE_END',
    },
  },
};

const otherContent = {
  name: 'test-name',
  message: 'custom message',
  details: { title: 'test-details-tile' },
  preview: { title: 'test-preview-tile' },
  interceptDetails: { heading: 'test-intercept-title' },
};

describe('Page rendering', () => {
  beforeEach(() => {
    mockAxios.reset();
    history.push.mockClear();
    mockAxios.get.mockImplementation(url => {
      if (url === '/contents/spaces/test-space/locales') {
        return Promise.resolve(mockLocalesRes);
      } else if (url.includes('?select=preview')) {
        return Promise.resolve({
          data: {
            'content': targetedCampaignPreviewContent,
            type: 'targetedCampaignPreview',
          },
        });
      } else if (url.includes('?select=details')) {
        return Promise.resolve({
          data: {
            'content': targetedCampaignTemplate2DetailsContent,
            type: 'targetedCampaignTemplate2Details',
          },
        });
      } else if (url.includes('?select=interceptDetails')) {
        return Promise.resolve({
          data: {
            'content': ccauContent,
            type: 'ccau_intercept_details',
          },
        });
      } else {
        return Promise.resolve({
          data: {
            content: otherContent,
            type: 'targetedCampaign',
          },
        });
      }
    });
  });

  afterEach(() => {
    mockAxios.reset();
    history.push.mockClear();
  });

  it('should render alert preview page according to snapshot', async() => {
    const store = configureStore([ thunk ])({ commonInitialState });
    const { baseElement } = await renderPage(
      <Provider store={store}>
        <Preview
          match={{
            params: {
              space: 'test-space',
              type: 'alert',
              contentId: 'test-content-id',
              application: 'nova',
              container: 'container',
              isDismissible: 'false',
              languages: 'en,fr',
            },
          }}
          history={history}
          location={{ search: '' }}
        />
      </Provider>
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should render campaign preview page according to snapshot', async() => {
    const store = configureStore([ thunk ])({ commonInitialState });
    const { baseElement } = await renderPage(
      <Provider store={store}>
        <Preview {...commonProps} />
      </Provider>
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should render campaign preview page with only languages targetted', async() => {
    const store = configureStore([ thunk ])({ commonInitialState });
    const { baseElement, getAllByTestId } = await renderPage(
      <Provider store={store}>
        <Preview
          {...commonProps}
          match={{
            params: {
              ...commonProps.match.params,
              languages: 'en',
            },
          }}
        />
      </Provider>
    );
    expect(baseElement).toMatchSnapshot();
    expect(getAllByTestId('language-option')).toHaveLength(1);
  });
});

describe('Fetch content', () => {
  beforeEach(() => {
    mockAxios.reset();
    history.push.mockClear();
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    mockAxios.reset();
    history.push.mockClear();
  });

  it('Should successfully fetch content (campaign)', async() => {
    mockAxios.get.mockImplementation(url => {
      if (url === '/contents/spaces/test-space/locales') {
        return Promise.resolve(mockLocalesRes);
      } else if (url.includes('?select=preview')) {
        return Promise.resolve({
          data: {
            content: targetedCampaignPreviewContent,
            type: 'targetedCampaignPreview',
          },
        });
      } else if (url.includes('?select=details')) {
        return Promise.resolve({
          data: {
            content: { title: 'test-details-title' },
            type: 'targetedCampaignTemplate2Details',
          },
        });
      } else if (url.includes('?select=interceptDetails')) {
        return Promise.resolve({
          data: {
            content: ccauContent,
            type: 'ccau_intercept_details',
          },
        });
      } else {
        return Promise.resolve({
          data: {
            content: otherContent,
            type: 'targetedCampaign',
          },
        });
      }
    });
    const store = configureStore([ thunk ])({ commonInitialState });
    const { getByText, findByText } = render(
      <Provider store={store}>
        <Preview {...commonProps} />
      </Provider>
    );

    await findByText('Preview');
    expect(getByText('test-preview-title')).toBeDefined();
    await findByText('Details');
    expect(getByText('test-details-title')).toBeDefined();
    await findByText('Intercept');
    expect(screen.getAllByText(/test-intercept-title/i)[0]).toBeInTheDocument();
  });

  it('Should throw a 401 error on fetchContent if not not logged in (campaign)', async() => {
    mockAxios.get.mockImplementationOnce(() => Promise.resolve(mockLocalesRes));
    const err = new Error('Unauthorized');
    err.response = { status: 401 };
    mockAxios.get.mockImplementation(() => Promise.reject(err));
    const store = configureStore([ thunk ])({ commonInitialState });
    const { getByText, findByText } = render(
      <Provider store={store}>
        <Preview {...commonProps} />
      </Provider>
    );

    await findByText('Error');
    fireEvent.click(getByText('Sign in'));
    expect(window.open).toHaveBeenCalled();
    expect(window.close).toHaveBeenCalled();
  });

  it('Should throw a 404-cdn error on fetchContent if content not found (campaign)', async() => {
    mockAxios.get.mockImplementationOnce(() => Promise.resolve(mockLocalesRes));
    const err = new Error('Content not found');
    err.response = { status: 404 };
    mockAxios.get.mockImplementation(() => Promise.reject(err));
    const store = configureStore([ thunk ])({ commonInitialState });
    const { getByText, findByText } = render(
      <Provider store={store}>
        <Preview {...commonProps} location={{ search: '?draft=true' }} />
      </Provider>
    );

    await findByText('Error');
    fireEvent.click(getByText('Refresh'));
    // Once on componentDidMount for locale fetch
    // Once on componentDidMount for content, once on re-fetch
    expect(mockAxios.get).toHaveBeenCalledTimes(3);
  });

  it('Should successfully fetch content (alert)', async() => {
    mockAxios.get.mockImplementation(url => {
      const res =
        url === '/contents/spaces/test-space/locales'
          ? mockLocalesRes
          : { data: { content: { title: 'test-title', message: 'test-msg' } } };
      return Promise.resolve(res);
    });
    const store = configureStore([ thunk ])({ commonInitialState });
    const { getByText, findByText } = await render(
      <Provider store={store}>
        <Preview
          {...commonProps}
          match={{
            params: {
              ...commonProps.match.params,
              type: 'alert',
              application: 'nova',
            },
          }}
        />
      </Provider>
    );
    // map.message({}, 'fetch-alert-content-url');
    // without working timer, we're simulating with explicit window message events
    // TODO use jest timers to simulate time based event trigger
    await findByText('test-title');
    expect(getByText('test-msg')).toBeDefined();
  });

  it('Should prompt login on fetchContent error (alert)', async() => {
    mockAxios.get.mockImplementationOnce(() => Promise.resolve(mockLocalesRes));
    const err = new Error('Content not found');
    err.response = { status: 404 };
    mockAxios.get.mockImplementation(() => Promise.reject(err));
    const store = configureStore([ thunk ])({ commonInitialState });
    const { getByText, findByText } = render(
      <Provider store={store}>
        <Preview
          {...commonProps}
          match={{
            params: {
              ...commonProps.match.params,
              type: 'alert',
              application: 'nova',
              isDismissible: 'true',
            },
          }}
        />
      </Provider>
    );

    await findByText('Unauthorized');
    expect(getByText('Please log in to the admin portal')).toBeDefined();
  });

  // temporarily disabled, as test is crashing due to jest timeout
  // it tries to invoke setInterval without managing fake timer with jest
  it.skip('should cover componentDidUpdate - previously not in draft mode', () => {
    const wrapper = shallow(
      <Preview
        {...commonProps}
        location={{ search: '?language=en-US&draft=true' }}
      />
    );
    const instance = wrapper.instance();
    instance.componentDidUpdate({
      location: {
        search: '?language=fr',
      },
    });
  });

  it('should cover componentDidUpdate - previously in draft mode', () => {
    const wrapper = shallow(
      <Preview {...commonProps} location={{ search: '?language=fr' }} />
    );
    const instance = wrapper.instance();
    instance.componentDidUpdate({
      location: {
        search: '?language=en-US&draft=true',
      },
    });
  });
});

describe('User events', () => {
  beforeEach(() => {
    mockAxios.reset();
    history.push.mockClear();
    mockAxios.get.mockImplementation(url => {
      if (url === '/contents/spaces/test-space/locales') {
        return Promise.resolve(mockLocalesRes);
      } else {
        return Promise.resolve({ data: { type: 'Preview' } });
      }
    });
  });

  it('Should toggle to french', async() => {
    const store = configureStore([ thunk ])({ commonInitialState });
    const { getByLabelText } = await renderPage(
      <Provider store={store}>
        <Preview {...commonProps} location={{ search: '?language=en-US' }} />
      </Provider>
    );

    // select english language in drop down menu
    const frenchId = mockLocalesRes.data.items[1].code;
    await act(async() => {
      fireEvent.change(getByLabelText('Language'), {
        target: { value: frenchId },
      });
    });
    expect(history.push).toHaveBeenCalledWith({ search: '?language=fr&platform=ios' });
  });

  it('Should toggle to english', async() => {
    const store = configureStore([ thunk ])({ commonInitialState });
    const { getByLabelText } = await renderPage(
      <Provider store={store}>
        <Preview {...commonProps} location={{ search: '?language=fr&platform=ios' }} />
      </Provider>
    );

    // select english language in drop down menu
    const englishId = mockLocalesRes.data.items[0].code;
    await act(async() => {
      fireEvent.change(getByLabelText('Language'), {
        target: { value: englishId },
      });
    });

    expect(history.push).toHaveBeenCalledWith({ search: '?language=en-US&platform=ios' });
  });

  it('Should toggle to and from live/draft mode', async() => {
    const store = configureStore([ thunk ])({ commonInitialState });
    const { findByText, getByLabelText } = render(
      <Provider store={store}>
        <Preview {...commonProps} />
      </Provider>
    );

    await findByText('Live Preview');
    fireEvent.click(getByLabelText('Live Preview'));
    expect(history.push).toHaveBeenCalledWith({ search: '?draft=true&platform=ios' });
  });

  it('Should toggle to and from dark/light mode', async() => {
    const store = configureStore([ thunk ])({ commonInitialState });
    const { findByText, getByLabelText } = render(
      <Provider store={store}>
        <Preview {...commonProps} />
      </Provider>
    );

    await findByText('Dark Mode');
    fireEvent.click(getByLabelText('Dark Mode'));
    expect(history.push).toHaveBeenCalledWith({ search: '?darkMode=true&platform=ios' });
  });

  it('Should toggle to and from new and viewd badge mode', async() => {
    const store = configureStore([ thunk ])({ commonInitialState });
    const { findByText, getByLabelText } = render(
      <Provider store={store}>
        <Preview {...commonProps} />
      </Provider>
    );

    await findByText('Preview New');
    fireEvent.click(getByLabelText('Preview New'));
    expect(history.push).toHaveBeenCalledWith({ search: '?isNew=true&platform=ios' });
  });
});

describe('Window events', () => {
  beforeEach(() => {
    mockAxios.reset();
    history.push.mockClear();
  });

  it('Window msg event should not trigger content refetch if not in draft mode', () => {
    mockAxios.get.mockImplementation(() => Promise.resolve({ data: {} }));
    const store = configureStore([ thunk ])({ commonInitialState });
    render(
      <Provider store={store}>
        <Preview {...commonProps} />
      </Provider>
    );

    map.message({}, 'test-url');
    // Once on componentDidMount for fetch locales
    // without working timer, we're simulating with explicit window message events
    // TODO use jest timers to simulate time based event trigger
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });

  it('Window msg event should trigger content refetch if in draft mode', () => {
    jest.useFakeTimers();
    mockAxios.get.mockImplementation(url => {
      if (url === '/contents/spaces/test-space/locales') {
        return Promise.resolve(mockLocalesRes);
      } else {
        return Promise.resolve({ data: { type: 'Preview' } });
      }
    });
    const store = configureStore([ thunk ])({ commonInitialState });
    render(
      <Provider store={store}>
        <Preview {...commonProps} location={{ search: '?draft=true' }} />
      </Provider>
    );
    map.message({ data: { type: 'REFETCH_CONTENT' } }, 'some-url');
    // Once on componentDidMount to fetch locales
    // Once from window msg event to get content
    expect(mockAxios.get).toHaveBeenCalledTimes(2);
  });

  it('Window unload event should send close event', () => {
    mockAxios.get.mockImplementation(() => Promise.resolve({ data: {} }));
    const store = configureStore([ thunk ])({ commonInitialState });
    render(
      <Provider store={store}>
        <Preview {...commonProps} location={{ search: '?draft=true' }} />
      </Provider>
    );

    map.beforeunload();
    expect(window.opener.postMessage).toHaveBeenCalledWith(
      {
        type: 'PREVIEW_CLOSED',
      },
      '*'
    );
  });
});
