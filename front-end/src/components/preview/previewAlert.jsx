import React from 'react';
import PropTypes from 'prop-types';

import { AlertTemplate } from 'pigeon-pigeon-web-renderer';
import LanguageSelect from './languageSelect';
import InputGroup from 'canvas-core-react/lib/InputGroup';
import RadioButton from 'canvas-core-react/lib/RadioButton';
import ToggleSwitch from 'canvas-core-react/lib/ToggleSwitch';
import classNames from 'classnames';

const PreviewAlert = ({
  content = { title: '', message: '' },
  defaultLocale,
  handleLanguage,
  handlePlatformToggle,
  handleDarkModeToggle,
  availableLocales,
  darkMode,
  platform,
  type,
  language,
}) => {
  return (
    <>
      <div className="preview">
        <div className="preview__header">
          <div className={classNames('preview__header__full-width', 'preview__header--left')}>
            <LanguageSelect
              defaultLocale={defaultLocale}
              languageChanged={handleLanguage}
              options={availableLocales}
            />
            <div className='preview__header__row'>

              <InputGroup
                id="device-os"
                legend="OS"
                name="OS"
                inline
              >
                  <RadioButton
                    id="ios"
                    label="ios"
                    name="ios"
                    checked={platform === 'ios'}
                    onChange={handlePlatformToggle}
                    value="ios"
                  />
                  <RadioButton
                    id="Android"
                    label="Android"
                    name="Android"
                    checked={platform !== 'ios'}
                    onChange={handlePlatformToggle}
                    value="android"
                  />

              </InputGroup>
              <InputGroup
                id="darkmode"
                legend="Display Settings"
                name="darkmode"
              >
                <ToggleSwitch
                  id="dark-mode"
                  name="Dark Mode"
                  label="Dark Mode"
                  onChange={handleDarkModeToggle}
                  checked={!!darkMode}
                />
              </InputGroup>
            </div>
          </div>
        </div>
      </div>

      <div className={classNames('preview--alert', { 'preview--alert-dark': darkMode })}>
        <AlertTemplate
          content={content}
          type={type}
          language={language}
          isDismissible
          darkMode={darkMode}
          platform={platform}
        />
      </div>
    </>
  );
};

PreviewAlert.propTypes = {
  content: PropTypes.object,
  defaultLocale: PropTypes.string,
  handleLanguage: PropTypes.func,
  availableLocales: PropTypes.array,
  type: PropTypes.string,
  language: PropTypes.string,
  handlePlatformToggle: PropTypes.func,
  handleDarkModeToggle: PropTypes.func,
  darkMode: PropTypes.bool,
  platform: PropTypes.string,
};

export default PreviewAlert;
