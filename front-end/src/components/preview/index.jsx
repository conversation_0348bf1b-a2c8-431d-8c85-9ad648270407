import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import qs from 'qs';
import { PreviewRenderer } from 'pigeon-pigeon-web-renderer';
import 'pigeon-pigeon-web-renderer/dist/main.css';
import ToggleSwitch from 'canvas-core-react/lib/ToggleSwitch';
import ModalDialogue from 'canvas-core-react/lib/ModalDialogue';
import IconInfo from 'canvas-core-react/lib/IconInfo';
import PreviewAlert from './previewAlert';
import { getContentByParams, getLocales } from '../../api/preview';
import LanguageSelect from './languageSelect';
import { DARK_MODE_CAMPAIGNS, PREVIEW_NEW_CAMPAINGNS } from '../../constants';
import { jsPDF as JSPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import TextButton from 'canvas-core-react/lib/TextButton';
import InputGroup from 'canvas-core-react/lib/InputGroup';
import IconDownload from 'canvas-core-react/lib/IconDownload';
import OrionInboxPreview from './OrionInboxPreview';
import { Canvg } from 'canvg';

const LIVE_PREVIEW_REFETCH_TIMEOUT = 2000;

const codeErrorMap = {
  401: 'Please log into the admin portal to use this feature.',
  '404-cdn': 'This entry is not yet published. Publish the entry first to preview it.',
  'other': 'An error occured while initializing the preview',
};
export default class Preview extends React.PureComponent {
  static propTypes = {
    match: PropTypes.shape({
      params: PropTypes.shape({
        space: PropTypes.string.isRequired,
        type: PropTypes.string.isRequired,
        contentId: PropTypes.string.isRequired,
        container: PropTypes.string.isRequired,
        isDismissible: PropTypes.string.isRequired,
        application: PropTypes.string,
        languages: PropTypes.string,
      }).isRequired,
    }).isRequired,
    history: PropTypes.shape({
      push: PropTypes.func.isRequired,
    }).isRequired,
    location: PropTypes.shape({
      search: PropTypes.string.isRequired,
    }).isRequired,
  };

  state = {
    data: null,
  };

  componentDidMount() {
    const { params } = this.props.match;
    this.setState({ error: false });
    getLocales(params)
      .then(async data => {
        if (!data) {
          return;
        }
        const options = data.items?.filter(item => {
          const lng = item.code.split(/[-_]/, 2)[0];
          return params.languages !== 'undefined' ? params.languages.split(',').includes(lng) : true;
        });
        const defaultLocale = options.find(o => o.default === true) || options[0];
        this.handleLanguage(defaultLocale.code)();
        this.setState({ ...this.state, availableLocales: options, defaultLocale });
        this.fetchContent();
      })
      .catch((error) => {
        clearInterval(this.livePreviewInterval);
        const errorCode = error.response && error.response.status;
        this.setState({ error: errorCode || error });
        console.warn('Failed to fetch locales', error);
      });

    const { draft } = qs.parse(this.props.location.search, { ignoreQueryPrefix: true });
    if (draft) {
      this.livePreviewInterval = setInterval(this.fetchContent, LIVE_PREVIEW_REFETCH_TIMEOUT);
    }
    window.addEventListener('message', this.handleWindowMessage, false);
    window.addEventListener('beforeunload', this.handleUnload, false);
  };

  componentWillUnmount() {
    window.removeEventListener('message', this.handleWindowMessage, false);
    window.removeEventListener('beforeunload', this.handleUnload, false);
  }

  handleWindowMessage = (e) => {
    const { draft } = qs.parse(this.props.location.search, { ignoreQueryPrefix: true });
    if (!draft) {
      return false;
    }
    if (e && e.data && e.data.type === 'REFETCH_CONTENT') {
      clearInterval(this.livePreviewInterval);
      this.fetchContent();
      this.livePreviewInterval = setInterval(this.fetchContent, LIVE_PREVIEW_REFETCH_TIMEOUT);
    }
  }

  fetchContent = async() => {
    const { params } = this.props.match;
    const { language, draft } = qs.parse(this.props.location.search, { ignoreQueryPrefix: true });
    this.setState({
      error: false,
    });
    getContentByParams(params, { language, draft })
      .then(async data => {
        if (!data) {
          return;
        }

        if (params.type === 'alert') {
          this.setState({ ...this.state, data });
          return;
        }

        // cases where we are previewing a detail or preview object on its own
        if ([ 'Preview', 'Details' ].includes(data.type)) {
          this.setState({ ...this.state, data });
          return;
        };

        // cases where the content lead us to think this is a Campaign object (i.e. { preview, details })
        const content = data?.content || {};
        if (content.hasOwnProperty('preview') || content.hasOwnProperty('interceptDetails')) {
          const getContent = async select => {
            if (!content.hasOwnProperty(select)) {
              return;
            }
            try {
              const result = await getContentByParams(params, { select, language, draft });
              return result;
            } catch (e) {
              console.warn(`Failed to fetch content for field: ${select}`);
            }
          };
          this.setState({
            ...this.state,
            data,
            preview: await getContent('preview'),
            details: await getContent('details'),
            interceptDetails: await getContent('interceptDetails'),
          });
        // another other content type
        } else {
          this.setState({ ...this.state, data });
        }
      })
      .catch((error) => {
        clearInterval(this.livePreviewInterval);
        if (params.type === 'alert') {
          this.setState({ data: { content: {
            title: 'Unauthorized', message: 'Please log in to the admin portal',
          } } });
        } else {
          const errorCode = (error.response && error.response.status) || 'other';
          const notYetPublishedError = error.response && error.response.data && error.response.data.message.includes('not found');
          this.setState({
            error: notYetPublishedError ? '404-cdn' : errorCode,
          });
        }
      });
  }

  componentDidUpdate = (prevProps) => {
    const { draft, language } = qs.parse(this.props.location.search, { ignoreQueryPrefix: true });
    const { draft: draftBefore, language: oldLanguage } = qs.parse(prevProps.location.search, { ignoreQueryPrefix: true });
    if (draft !== draftBefore) {
      if (draft) {
        this.livePreviewInterval = setInterval(this.fetchContent, LIVE_PREVIEW_REFETCH_TIMEOUT);
      } else {
        this.fetchContent();
        clearInterval(this.livePreviewInterval);
      }
    }
    if (language !== oldLanguage) {
      this.fetchContent();
    }
  }

  handleLivePreviewToggle = () => {
    const { draft, language, darkMode, platform = 'ios', isNew } = qs.parse(this.props.location.search, { ignoreQueryPrefix: true });
    this.props.history.push({
      search: qs.stringify({
        language,
        ...(!draft ? {
          draft: 'true',
        } : {}),
        ...(darkMode ? {
          darkMode: 'true',
        } : {}),
        ...(isNew ? {
          isNew: 'true',
        } : {}),
        platform,
      }, {
        addQueryPrefix: true,
      }),
    });
  }

  handlePlatformToggle = (e) => {
    const { draft, language, darkMode, platform = 'ios', isNew } = qs.parse(this.props.location.search, { ignoreQueryPrefix: true });
    this.props.history.push({
      search: qs.stringify({
        language,
        ...(draft ? {
          draft: 'true',
        } : {}),
        ...(darkMode ? {
          darkMode: 'true',
        } : {}),
        ...(isNew ? {
          isNew: 'true',
        } : {}),
        ...(platform === 'ios' ? {
          platform: 'android',
        } : { platform: 'ios' }),
      }, {
        addQueryPrefix: true,
      }),
    });
  }

  handleDarkModeToggle = () => {
    const { draft, language, darkMode, platform = 'ios', isNew } = qs.parse(this.props.location.search, { ignoreQueryPrefix: true });
    this.props.history.push({
      search: qs.stringify({
        language,
        ...(draft ? {
          draft: 'true',
        } : {}),
        ...(!darkMode ? {
          darkMode: 'true',
        } : {}),
        ...(isNew ? {
          isNew: 'true',
        } : {}),
        platform,
      }, {
        addQueryPrefix: true,
      }),
    });
  }

  handleisNewToggle = () => {
    const { draft, language, darkMode, platform = 'ios', isNew } = qs.parse(this.props.location.search, { ignoreQueryPrefix: true });
    this.props.history.push({
      search: qs.stringify({
        language,
        ...(draft ? {
          draft: 'true',
        } : {}),
        ...(darkMode ? {
          darkMode: 'true',
        } : {}),
        ...(!isNew ? {
          isNew: 'true',
        } : {}),
        platform,
      }, {
        addQueryPrefix: true,
      }),
    });
  }

  handleLanguage = (language) => () => {
    const { draft, darkMode, platform = 'ios', isNew } = qs.parse(this.props.location.search, { ignoreQueryPrefix: true });
    this.props.history.push({
      search: qs.stringify({
        language,
        ...(draft ? {
          draft: 'true',
        } : {}),
        ...(darkMode ? {
          darkMode: 'true',
        } : {}),
        ...(isNew ? {
          isNew: 'true',
        } : {}),
        platform,
      }, {
        addQueryPrefix: true,
      }),
    });
  }

  sendCloseEvent = () => {
    const { opener } = window;
    if (opener) {
      opener.postMessage({
        type: 'PREVIEW_CLOSED',
      }, '*');
    }
  }

  handleUnload = () => {
    this.sendCloseEvent();
  }

  handleLoginClick = () => {
    const { protocol, hostname, port } = window.location;
    this.sendCloseEvent();
    window.open(protocol + '//' + hostname + (!port || (port === 80) ? '' : (':' + port)));
    window.close();
  }

  generatePDF = async() => {
    function getLongestElementToPrint(element) {
      let maxHeight = -1;
      let elementWithMaxHeight = null;

      // Check the current element's height
      if (element.offsetHeight > maxHeight) {
        maxHeight = element.offsetHeight;
        elementWithMaxHeight = element;
      }

      // Iterate over the child elements recursively
      Array.from(element.children).forEach((childElement) => {
        const childElementWithMaxHeight =
          getLongestElementToPrint(childElement);

        // Compare the child element's maximum height with the current maximum height
        if (
          childElementWithMaxHeight &&
          childElementWithMaxHeight.offsetHeight > maxHeight
        ) {
          maxHeight = childElementWithMaxHeight.offsetHeight;
          elementWithMaxHeight = childElementWithMaxHeight;
        }
      });

      return elementWithMaxHeight;
    }
    const isModalOpen =
      !!document.getElementsByClassName('Modal__container').length;
      document.getElementById('preview-content').querySelectorAll('img').forEach((img) => {
        img.setAttribute('crossorigin', 'anonymous');
      });
    const previewElement = isModalOpen
      ? getLongestElementToPrint(
          document.getElementsByClassName('Modal__container')[0],
        )
      : document.getElementById('preview-content');

     const SVGElements = previewElement.querySelectorAll('svg');
     SVGElements.forEach(function(el) {
     el.style.fill = getComputedStyle(el).getPropertyValue('fill');
     el.style.stroke = getComputedStyle(el).getPropertyValue('stroke');
     el.style.color = getComputedStyle(el).getPropertyValue('color');
      const canvas = document.createElement('canvas');
      canvas.classList = el.classList;
      var bBox = el.getBBox();
      canvas.width = bBox.width;
      canvas.height = bBox.height;
      const ctx = canvas.getContext('2d');
      const v = Canvg.fromString(ctx, el.outerHTML);
      v.start();
      el.replaceWith(canvas);
     });

    // remove scroll bar from ABM elements
    const pacc = document.querySelectorAll('.abm-pacc-details__main');

    const odp = document.querySelectorAll('.abm-odp-details__main-offer');
    const features = document.querySelectorAll('.abm-odp-details__feature-list-container');
    for (let i = 0; i < pacc.length; i++) {
      pacc[i].setAttribute('style', 'max-height:unset !important; height:unset !important;');
    }

    for (let i = 0; i < odp.length; i++) {
      odp[i].setAttribute('style', 'max-height:unset !important; height:unset !important;');
    }

    for (let i = 0; i < features.length; i++) {
      features[i].setAttribute('style', 'max-height:unset !important; height:unset !important;');
    }

    const canvas = await html2canvas(previewElement, {
      useCORS: true,
      width: previewElement.scrollWidth,
      height: previewElement.scrollHeight,
    });

    const imgData = canvas.toDataURL('image/jpg', 1.0);
    const pdf = new JSPDF({ format: [ previewElement.scrollWidth + 30, previewElement.scrollHeight + 30 ], unit: 'px' });
    pdf.addImage(imgData, 'JPG', 15, 15, previewElement.scrollWidth, previewElement.scrollHeight);
    pdf.save('campaign.pdf');
  };

  render() {
    const { error } = this.state;
    if (error) {
      return (
        <ModalDialogue
          headline="Error"
          primaryButtonLabel="Refresh"
          primaryAction={this.fetchContent}
          secondaryButtonLabel="Sign in"
          secondaryAction={this.handleLoginClick}
          isModalVisible={true}
          setModalVisible={this.handleLoginClick}
        >
          { codeErrorMap[error] || codeErrorMap.other }
        </ModalDialogue>
      );
    }
    const { type, container, isDismissible, application } = this.props.match.params;
    if (type === 'alert') {
      const { availableLocales, defaultLocale, data } = this.state;
      const { language, darkMode, platform = 'ios' } = qs.parse(this.props.location.search, { ignoreQueryPrefix: true });
      return data ? (
        <PreviewAlert
          content={data.content}
          language={language}
          handleLanguage={this.handleLanguage}
          availableLocales={availableLocales}
          defaultLocale={defaultLocale.code}
          type={`${application}__${type}`}
          handleDarkModeToggle={this.handleDarkModeToggle}
          handlePlatformToggle={this.handlePlatformToggle}
          darkMode={darkMode}
          platform={platform}
        />
      ) : null;
    } else {
      const { preview, details, interceptDetails, data, availableLocales, defaultLocale } = this.state;
      const { language, draft, darkMode, isNew } = qs.parse(this.props.location.search, { ignoreQueryPrefix: true });
      const showHelper = (language === 'fr') || !!draft;
      return (
        !!data && (
          <div className="preview">
            <div className="preview__header">
              <div className="preview__header--left">
                <LanguageSelect
                  defaultLocale={defaultLocale.code}
                  languageChanged={this.handleLanguage}
                  options={availableLocales}
                />
              </div>
              <div className="preview__header--right">
                <InputGroup id="settings" legend="Settings" inline>
                  <ToggleSwitch
                    id="live-preview"
                    name="Live Preview"
                    label="Live Preview"
                    onChange={this.handleLivePreviewToggle}
                    checked={!!draft}
                  />
                  <ToggleSwitch
                    id="dark-mode"
                    name="Dark Mode"
                    label="Dark Mode"
                    onChange={this.handleDarkModeToggle}
                    checked={!!darkMode}
                    disabled={
                      !DARK_MODE_CAMPAIGNS.includes(
                        (preview && preview.type) || interceptDetails?.type,
                      )
                    }
                  />
                  <ToggleSwitch
                    id="preview-new"
                    name="Preview New"
                    label="Preview New"
                    onChange={this.handleisNewToggle}
                    checked={!!isNew}
                    disabled={
                      !PREVIEW_NEW_CAMPAINGNS.includes(preview && preview.type)
                    }
                  />
                </InputGroup>
              </div>
              <div className="preview__header--download">
                <TextButton
                  className="preview__header--download-button"
                  Icon={IconDownload}
                  onClick={this.generatePDF}
                >
                  Download as PDF
                </TextButton>
              </div>
            </div>
            <div
              className={classnames('preview__help', {
                'preview__help--open': showHelper,
              })}
            >
              <div className="preview__help-icon">
                <IconInfo size={18} color={'white'} />
              </div>
              <div className="preview__help-text">
                { language === 'fr' && (
                  <div>Fill out required fields in French.</div>
                ) }
                { !!draft && (
                  <div>
                    Live preview enabled. You can now preview draft content as
                    you type.
                  </div>
                ) }
              </div>
            </div>
            <div id="preview-content" className="preview__preview-content">
              { [ preview, details, interceptDetails ].map((contentData, idx) => {
                if (container === 'inbox-updates---orion' && idx === 0) {
                  return <OrionInboxPreview
                  contentData={contentData}
                    container={container}
                    isDarkMode={!!darkMode}
                  />;
                }
                return (
                  <PreviewRenderer
                    key={`preview-${idx}`}
                    contentData={contentData}
                    container={container}
                    isDarkMode={!!darkMode}
                    isDismissible={isDismissible === 'true'}
                    application={application}
                    isNew={!!isNew}
                  />
                );
              }) }
              { !preview && !details && !interceptDetails && data && (
                <PreviewRenderer
                  contentData={data}
                  isDarkMode={!!darkMode}
                  isNew={!!isNew}
                  application={application}
                />
              ) }
            </div>
          </div>
        )
      );
    }
  }
}
