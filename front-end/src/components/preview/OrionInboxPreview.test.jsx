import React from 'react';
import { render } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';

import OrionInboxPreview from './OrionInboxPreview';

describe('OrionInboxPreview', () => {
  const mockContentData = {
    'id': '1WU7BgWppOa0kA98KJhKZj',
    'language': 'en-US',
    'created_at': '2024-10-04T17:26:26.908Z',
    'updated_at': '2024-10-04T17:26:26.908Z',
    'type': 'orion__target-campaign-preview',
    'content': {
        'name': 'ULOC-CLI_Preview_1024',
        'image': {
            'title': 'PA-ULOC-CLI 1024',
            'description': '',
            'file': {
                'url': '//images.ctfassets.net/4szkx38resvm/6VKuMwhW9VlQ5oC5myvUwL/71bb3f4af5d59b3423130baaeef82198/PA-ULOC-CLI_1024.jpg',
                'details': {
                    'size': 512567,
                    'image': {
                        'width': 2000,
                        'height': 1145,
                    },
                },
                'fileName': 'PA-ULOC-CLI_1024.jpg',
                'contentType': 'image/jpeg',
            },
        },
        'title': 'You’re pre-approved for a credit limit increase on your ScotiaLine® Personal Line of Credit.',
        'description': 'See how a higher credit limit can help you.',
        'detailLink': {
            'text': 'Learn more',
        },
    },
};
  it('should render', () => {
    render(
      <MemoryRouter>
        <OrionInboxPreview contentData={mockContentData} />
      </MemoryRouter>,
    );
  });
});
