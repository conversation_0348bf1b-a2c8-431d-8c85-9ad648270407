import messageCentreCampaign from '../../../src/__mocks__/mockMessageCentreCampaign';
import messageCentreMessage from '../../../src/__mocks__/mockMessageDetail';
import mockOffer from '../../../src/__mocks__/mockOffer';

// barebone default data enough to trigger happy path scenario for most pages
const applications = [
  {
    id: 1,
    name: 'SOL',
    description: 'application 1',
    applicationId: 'sol',
    status: true,
    rule_version: 2,
    contentful_space: '4szkx38resvm',
    team_id: 1,
    platforms: [ 'Web' ],
    platformIds: [ 1 ],
    ruleTypes: [
      'vignette',
      'SOL Priority Campaign (Vignette)',
      'SOL Broadcast Campaign (Vignette)',
    ],
    ruleTypeIds: [ 3, 5, 6 ],
    ruleSubTypeIds: [],
    ruleSubTypes: [],
    team: [ 'Pigeon Team' ],
  },
  {
    id: 2,
    name: 'eExperience Storefront',
    description: null,
    applicationId: 'storefront',
    status: true,
    rule_version: 2,
    contentful_space: '4szkx38resvm',
    team_id: 1,
    platforms: [ 'Web' ],
    platformIds: [ 1 ],
    ruleTypes: [ 'eExperience Storefront Campaign (Vignette)' ],
    ruleTypeIds: [ 7 ],
    ruleSubTypeIds: [],
    ruleSubTypes: [],
    team: [ 'Pigeon Team' ],
  },
  {
    id: 3,
    name: 'Nova Mobile',
    description: null,
    applicationId: 'nova',
    status: true,
    rule_version: 1,
    contentful_space: '4szkx38resvm',
    team_id: 1,
    platforms: [ 'iOS', 'Android' ],
    platformIds: [ 2, 3 ],
    ruleTypes: [ 'alert', 'campaign' ],
    ruleTypeIds: [ 1, 2 ],
    ruleSubTypeIds: [ 1, 2, 3 ],
    ruleSubTypes: [ 'targeted', 'mass', 'message' ],
    team: [ 'Pigeon Team' ],
  },
  {
    id: 4,
    name: 'Phoenix',
    description: null,
    applicationId: 'phoenix',
    status: true,
    rule_version: 1,
    contentful_space: '4szkx38resvm',
    team_id: 1,
    platforms: [ 'Web' ],
    platformIds: [ 1 ],
    ruleTypes: [ 'alert' ],
    ruleTypeIds: [ 1 ],
    ruleSubTypeIds: [ 1, 2, 3 ],
    ruleSubTypes: [ 'targeted', 'mass', 'message' ],
    team: [ 'Pigeon Team' ],
  },
  {
    id: 5,
    name: 'ABM',
    description: null,
    applicationId: 'abm',
    status: true,
    rule_version: 1,
    contentful_space: '4szkx38resvm',
    team_id: 1,
    platforms: [ 'Web' ],
    platformIds: [ 1 ],
    ruleTypes: [ 'campaign' ],
    ruleTypeIds: [ 2 ],
    ruleSubTypeIds: [ 1, 2, 3 ],
    ruleSubTypes: [ 'targeted', 'mass', 'message' ],
    team: [ 'Pigeon Team' ],
  },
];

const pages = [
  {
    id: 1,
    application: applications.find((a) => a.name === 'Nova Mobile').id,
    name: 'activities',
    pageId: 'activities',
    status: true,
    description: 'NOVA mobile - My Activity tab',
    containers: [ 2 ],
  },
  {
    id: 2,
    application: applications.find((a) => a.name === 'Nova Mobile').id,
    name: 'accounts',
    pageId: 'accounts',
    status: true,
    description: 'NOVA mobile - My Accounts tab',
    containers: [ 3, 4 ],
  },
];

const containers = [
  {
    id: 1,
    name: 'alert',
    containerId: 'alert',
    application: 3,
    status: true,
    description: 'Alert',
    content_type: '["alert"]',
    rule_type: 'alert',
    pages: [],
  },
  {
    id: 2,
    name: 'my-activity',
    containerId: 'my-activity',
    application: 3,
    status: true,
    description: 'My Activity',
    content_type: '["standingCampaign"]',
    rule_type: 'campaign',
    pages: [ 1 ],
  },
  {
    id: 3,
    name: 'offers-and-programs',
    containerId: 'offers-and-programs',
    application: 3,
    status: true,
    description: 'Offers and Programs',
    content_type: '["targetedCampaign"]',
    rule_type: 'campaign',
    pages: [ 2 ],
  },
  {
    id: 4,
    name: 'priority-box',
    containerId: 'priority-box',
    application: null,
    status: true,
    description: 'Priority Box',
    content_type: '[]',
    rule_type: 'campaign',
    pages: [ 2 ],
  },
  {
    id: 5,
    name: 'storefront-container',
    containerId: 'storefront-container',
    application: null,
    status: true,
    description: 'Storefront container',
    content_type: '[]',
    rule_type: 'storefront',
    pages: [],
  },
];

const users = [
  {
    id: 1,
    name: 'Test Admin',
    email: '<EMAIL>',
    sid: 's1001000',
    active: true,
    team_id: 1,
    roles: [ 1, 4, 5 ],
  },
  {
    id: 2,
    name: 'Test Regular Team User',
    email: '<EMAIL>',
    sid: 's1002000',
    active: true,
    team_id: 2,
    roles: [ 2 ],
  },
  {
    id: 6,
    name: 'Test Regular Team Owner',
    email: '<EMAIL>',
    sid: 's1002001',
    active: true,
    team_id: 2,
    roles: [ 3 ],
  },
  {
    id: 3,
    name: 'Test Deactivated User',
    email: '<EMAIL>',
    sid: 's1003000',
    active: false,
    team_id: 3,
    roles: [ 7 ],
  },
  {
    id: 4,
    name: 'Test Pigeon Owner',
    email: '<EMAIL>',
    sid: 's1001001',
    active: true,
    team_id: 1,
    roles: [ 4, 5 ],
  },
  {
    id: 5,
    name: 'Test Inactive team owner',
    email: '<EMAIL>',
    sid: 's1001005',
    active: false,
    team_id: 3,
    roles: [ 6 ],
  },
  {
    id: 7,
    name: 'Test Inactive user',
    email: '<EMAIL>',
    sid: 's1001005',
    active: false,
    team_id: 2,
    roles: [ 2 ],
  },
];

const mockTeam = {
  id: 1,
  name: 'Pigeon Team',
  description: 'Team added at DB implementation',
  active: true,
  applications: applications.map((a) => a.name),
  functionality: applications.map((a) => a.id),
  access: {},
  applicationIds: undefined,
};

const teams = [
  { ...mockTeam, id: 1, ownerRoleId: 4 },
  { ...mockTeam, id: 2, name: 'Regular team', ownerRoleId: 3 },
  { ...mockTeam, id: 3, name: 'Regular inactive team', active: false, ownerRoleId: 6 },
];

const roles = [
  {
    id: 1,
    name: 'Admin',
    permissions: [ 'admin', 'teams_view', 'teams_view_super', 'teams_manage', 'teams_manage_super' ],
    team_id: teams[0].id,
    status: 1,
  },
  // regular team base roles, use these in renderer option for initialState to simulate regular team user session
  {
    id: 2,
    name: 'Viewer 1',
    permissions: [ 'users_view', 'teams_view', 'teams_view_super' ],
    team_id: teams[1].id,
    status: 1,
  },
  {
    id: 3,
    name: 'Team owner 1',
    permissions: [
      'users_view',
      'users_manage',
      'teams_view',
      'teams_view_super',
      'teams_manage',
      'teams_manage_super',
    ],
    team_id: teams[1].id,
    status: 1,
  },
  // // pigeon team base roles
  {
    id: 4,
    name: 'Team owner 0',
    permissions: [
      'users_view',
      'users_manage',
      'teams_view',
      'teams_view_super',
      'teams_manage',
      'teams_manage_super',
    ],
    team_id: teams[0].id,
    status: 1,
  },
  {
    id: 5,
    name: 'Viewer 0',
    permissions: [ 'users_view', 'teams_view', 'teams_view_super' ],
    team_id: teams[0].id,
    status: 1,
  },
  // inactive team base roles
  {
    id: 6,
    name: 'Team owner 2',
    permissions: [
      'users_view',
      'users_manage',
      'teams_view',
      'teams_view_super',
      'teams_manage',
      'teams_manage_super',
    ],
    team_id: teams[2].id,
    status: 1,
  },
  {
    id: 7,
    name: 'Viewer 2',
    permissions: [ 'users_view', 'teams_view', 'teams_view_super' ],
    team_id: teams[2].id,
    status: 1,
  },
];

const ruleTypes = [
  {
    id: 1,
    rule_type: 'alert',
    slug: 'alert',
  },
  {
    id: 2,
    rule_type: 'campaign',
    slug: 'campaign',
  },
];

const ruleSubTypes = [
  {
    id: 1,
    description: 'Targeted Campaign',
    type: 'targeted',
    ruleTypeId: null,
  },
  {
    id: 2,
    description: 'Mass Campaign',
    type: 'mass',
    ruleTypeId: null,
  },
  {
    id: 3,
    description: 'Mass Message',
    type: 'message',
    ruleTypeId: null,
  },
];

const platforms = [
  {
    id: 1,
    slug: 'web',
    name: 'Web',
  },
  {
    id: 2,
    slug: 'ios',
    name: 'iOS',
  },
  {
    id: 3,
    slug: 'android',
    name: 'Android',
  },
];

const messageCentreCampaigns = [
  {
    ...messageCentreCampaign,
    id: 1,
    msg_priority: 'N',
    msg_status: 'N',
    name: 'PA Momentum No-Fee',
  },
  {
    ...messageCentreCampaign,
    id: 2,
    msg_status: 'D',
    name: 'PA Momentum No-Fee 1',
  },
  {
    ...messageCentreCampaign,
    id: 3,
    msg_status: 'S',
    name: 'PA Momentum No-Fee 2',
    campaign_type: 'SOL',
  },
];

const messageCentreMessages = [
  {
    ...messageCentreMessage,
    id: 1,
    msg_status: 'N',
    name: `You've been pro-approved for Scotiabank No-Fee Momentum Credit Card.`,
    updated_date: '',
  },
  {
    ...messageCentreMessage,
    id: 2,
    msg_status: 'S',
    name: 'PA Momentum No-Fee 1',
    campaign_type: 'SOL',
  },
];

const offers = [
  {
   ...mockOffer,
   title: 'Offer title 1',
  },
  {
    ...mockOffer,
    offer_id: 'OFR-1',
    title: 'Offer title 2',
  },
];

// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/freeze
const deepFreeze = (o) => {
  // Retrieve the property names defined on object
  const propNames = Reflect.ownKeys(o);

  // Freeze properties before freezing self
  for (const name of propNames) {
    const value = o[name];

    if ((value && typeof value === 'object') || typeof value === 'function') {
      deepFreeze(value);
    }
  }

  return Object.freeze(o);
};

const defaultData = deepFreeze({
  // access
  users,
  teams,
  roles,
  // placements
  applications,
  containers,
  pages,
  // core
  ruleTypes,
  ruleSubTypes,
  platforms,
  messageCentreCampaigns,
  messageCentreMessages,
  offers,
});

export const permissions = {
  alerts_approve: true,
  alerts_manage: true,
  alerts_view: true,
  applications_manage: true,
  applications_manage_super: true,
  applications_view: true,
  applications_view_super: true,
  campaigns_approve: true,
  campaigns_manage: true,
  campaigns_review: true,
  campaigns_view: true,
  ccau_campaigns_approve: true,
  ccau_campaigns_manage: true,
  ccau_campaigns_review: true,
  ccau_campaigns_view: true,
  containers_manage: true,
  containers_manage_super: true,
  containers_view: true,
  containers_view_super: true,
  pages_manage: true,
  pages_manage_super: true,
  pages_view: true,
  pages_view_super: true,
  pega_variable_mapping_approve: true,
  pega_variable_mapping_manage: true,
  pega_variable_mapping_review: true,
  pega_variable_mapping_view: true,
  roles_manage: true,
  roles_manage_super: true,
  roles_view: true,
  roles_view_super: true,
  teams_manage: true,
  teams_manage_super: true,
  teams_view: true,
  teams_view_super: true,
  users_manage: true,
  users_manage_super: true,
  users_view: true,
  users_view_super: true,
};
export const access = {
  campaigns: {
    containers: {
      sol: {
        manage: [ 'accountDetailBrkTop' ],
      },
      'ccau-app': {
        view: [ 'ccau-container' ],
        manage: [ 'ccau-container' ],
      },
      nova: {
        manage: [ 'harmony-campaigns' ],
      },
      storefront: {
        manage: [ 'sf-marquee-bankingaccounts' ],
      },
    },
    pages: {
      sol: {
        manage: [ 'AccountDetails' ],
      },
      nova: {
        manage: [
          'account-key',
          'accounts',
          'activities',
          'brokerages',
          'cc',
          'chq',
          'harmony-rewards',
          'investments',
          'loans',
          'mortgages',
          'mpsa',
          'nova-help-centre',
          'sav',
          'step',
        ],
      },
      storefront: {
        manage: [ 'sf-wealthmanagement' ],
      },
    },
    ruleSubTypes: {
      nova: {
        manage: [ 'targeted', 'mass', 'message' ],
      },
      sol: {
        manage: [ 'targeted', 'mass' ],
      },
      storefront: {
        manage: [ 'targeted', 'mass' ],
      },
      'ccau-app': {
        view: [ 'targeted' ],
        manage: [ 'targeted' ],
      },
    },
  },
  alerts: {
    containers: {
      nova: {
        manage: [ 'alert' ],
      },
    },
    pages: {
      phoenix: {
        manage: [ 'lgn-181c3bdd-fa19-4d6f-8229-b769f8cf81a4' ],
      },
    },
  },
  ccau_campaign: {
    containers: {
      'ccau-app': {
        view: [ 'ccau-container' ],
        manage: [ 'ccau-container' ],
      },
    },
    pages: {
      'ccau-app': {
        view: [ 'ccau-page' ],
        manage: [ 'ccau-page' ],
      },
    },
  },
};

export default defaultData;
