import mockAxios from 'axios';
import {
  reservedRoleName,
  ensureUnique,
  teamRequired,
  roleRequired,
  roleNameValidation,
  validateRuleType,
  validateRuleSubType,
  requiredPermissions,
} from './validation';

describe('Validation', () => {
  describe('General validators', () => {
    beforeEach(() => {
      mockAxios.get.mockImplementation((url) => Promise.resolve({ data: true }));
    });

    test('test ensureUnique ', async() => {
      const checkEnsureUnique = ensureUnique('name', 'name', {
        name: 'testName',
      });
      const message = await checkEnsureUnique('testName');
      expect(message).toStrictEqual(`"name" with the value "testName" already exists`);
    });

    test('test ensureUnique with additional values ', async() => {
      const checkEnsureUnique = ensureUnique('name', 'name', {
        name: 'testName',
      }, [ { desc: '' } ]);
      const message = await checkEnsureUnique('testName');
      expect(message).toStrictEqual(`"name" with the value "testName" already exists`);
    });

    test('test ensureUnique with additional values - false ', async() => {
      mockAxios.get.mockImplementation((url) => Promise.resolve({ data: false }));

      const checkEnsureUnique = ensureUnique('name', 'name', {
        name: 'testName',
      }, [ { desc: '' } ]);
      const message = await checkEnsureUnique('testName');
      expect(message).toStrictEqual(undefined);
    });
  });

  describe('Placement validators', () => {
    test('validateRuleType ', () => {
      const message = validateRuleType([
        { id: 1, rule_type: 'alert', slug: 'alert' },
        { id: 2, rule_type: 'campaign', slug: 'campaign' },
        { id: 7, rule_type: 'CCAU Campaign', slug: 'ccau_campaign' },
      ])([ 1, 2, 7 ]);
      expect(message).toStrictEqual(
        `CCAU Campaign cannot be selected with other rule types`
      );
    });

    test('validateRuleSubType ', () => {
      const message = validateRuleSubType([
        { id: 1, rule_type: 'alert', slug: 'alert' },
        { id: 2, rule_type: 'campaign', slug: 'campaign' },
        { id: 7, rule_type: 'CCAU Campaign', slug: 'ccau_campaign' },
      ], [
        { id: 1, description: 'Targeted Campaign', type: 'targeted' },
        { id: 2, description: 'Mass Campaign', type: 'mass' },
        { id: 3, description: 'Mass Message', type: 'message' },
      ], [ 7 ])([ 1, 2 ]);
      expect(message).toStrictEqual(
        `Only Targeted Campaign can be selected when the CCAU Campaign rule type is selected`
      );
    });
  });

  describe('Access validators', () => {
    test('teamRequired ', () => {
      const message = teamRequired();
      expect(message).toStrictEqual('Team is required');
    });

    test('roleRequired ', () => {
      const message = roleRequired([]);
      expect(message).toStrictEqual('Role selection is required');
    });

    test('roleNameValidation ', () => {
      const message = roleNameValidation('*%$');
      expect(message).toStrictEqual('Name can only contain alphanumeric characters, digits, dashes, underscores, and colons.');
    });

    test('reservedRoleName ', () => {
      const checkReservedRoleName = reservedRoleName();
      const viewerMessage = checkReservedRoleName('viewer');
      const ownerMessage = checkReservedRoleName('team owner');
      expect(viewerMessage).toStrictEqual(
        `"Viewer" and "Team Owner" are reserved role names. Please choose another name.`
      );
      expect(ownerMessage).toStrictEqual(
        `"Viewer" and "Team Owner" are reserved role names. Please choose another name.`
      );
    });

    test('requiredPermissions ', () => {
      const message = requiredPermissions([], 1)();
      expect(message).toStrictEqual('Permissions cannot be removed from the Pigeon team - applications_view');
    });
  });
});
