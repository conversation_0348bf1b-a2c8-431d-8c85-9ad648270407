import React from 'react';
import configureMockStore from 'redux-mock-store';
import { createMemoryHistory } from 'history';
import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
import { Router } from 'react-router';
import thunk from 'redux-thunk';
import { act } from 'react-dom/test-utils';

import { mapPropToKey, mapArrayToTrueProps } from '../constants';
import defaultData from './testing-library-utils-data';
import allPermissions, { mapPermissionsToPermissionLevels } from '../constants/permissionsList';

const mockStore = configureMockStore([ thunk ]);
const mockFetchResults = {};

// map mock api res to mock store state
Object.keys(defaultData).map((k) => {
  // frontend expects access and placement api res to be normalized
  const items = [ 'teams', 'roles', 'applications', 'pages', 'containers' ].includes(k)
    ? mapPropToKey(defaultData[k], 'id')
    : defaultData[k];
  mockFetchResults[k] = { isLoading: false, items };
});

/**
 * Convenience method to render page.
 * Uses react-testing-library for renderer.
 * Supports mocking of redux store and history. Allows override with initStore option.
 * Provides barebones default data to satisfy happy path scenario for most pages.
 * Waits for re-render if there are data fetch hooks on page load.
 *
 * @param {*} children - child react tree to render
 * @param {{store: object, initialState: object, history: object}} options - mock store, initial redux store state, and history
 * @returns {Promise} Promise object resolve to rendered DOM along with history and redux mock store instance
 */
const renderPage = async(children, options = {}) => {
  const store =
    options.store ||
    mockStore({
      // redux-mock-store does not support reducers
      // so state changes due to UI interaction need to be prepopulated

      authenticated: {
        permissions: { admin: true, teams_view: true },
        permissionLevels: mapPermissionsToPermissionLevels(
          mapArrayToTrueProps(Object.values(allPermissions)),
        ),
        sid: defaultData.users[0].sid,
      },
      ...mockFetchResults,
      ...options.initialState,
    });
  const history = options.history || createMemoryHistory();
  let rendered;
  await act(async() => {
    rendered = await render(
      <Router history={history}>
        <Provider store={store}>
          { children }
        </Provider>
      </Router>
    );
  });
  return { ...rendered, history, store };
};

/**
 * Custom wait to allow jest to pause and wait while testing continues.
 * This is useful to test components that leverage canvas 6 modal dialogues,
 * where a known bug tries to place focus on modal about 300ms after initial render.
 * If modal is dismissed within 300ms of initial render, unhandled exception will be thrown.
 * Warning: jest default for max timeout wait time is 5 seconds.
 * If you need more time, you should either override jest default or also budget
 * some buffer around 2% to account for js overhead.
 *
 * @param {number} length - wait time in ms
 * @param {requestCallback} callback - optional callback
 * @returns
 */
const wait = async(length = 0, callback) =>
  new Promise((resolve) => {
    const ts = new Date().getTime();
    setTimeout(() => {
      const te = new Date().getTime();
      const actualWaitTime = te - ts;
      if (actualWaitTime > length * 1.2) {
        console.warn(`waited ${actualWaitTime} ms vs expected ${length} ms`);
      }

      const result = callback && callback();
      resolve(result);
    }, length);
  });

export { defaultData, renderPage, wait };
