import { difference } from 'lodash';
import { addThousandSeparator, emailRegex, OFFER_ID_ERROR_MSG } from '../constants';
import { FULL_PERMISSION_LIST, CAMPAIGN_ALERT_PERMISSIONS, REVIEW_APPROVE_PERMISSIONS } from '../../../src/permissions/permission-constants';
import { INVALID_DATE_ERROR_MSG } from '../components/messageCentre/campaigns/constants';
import * as validateApi from '../api/validate';
import moment from 'moment';

// General validators
export const required = value => (value || typeof value === 'number' || typeof value === 'boolean' ? undefined : 'Required');
export const requiredSpecific = name => value => (value || typeof value === 'number' || typeof value === 'boolean' ? undefined : `${name} is required`);
export const requiredDate = value => (!value || !moment(value).isValid()) ? INVALID_DATE_ERROR_MSG : undefined;
export const requireAtLeastOne = value => (value && Array.isArray(value) && value.length > 0) ? '' : 'You must select at least one';
export const requireAtLeastOneSpecific = name => value => (value && Array.isArray(value) && value.length > 0) ? '' : `${name} selection is required`;
export const min = min => value => (value && value.trim().length < min ? `Must be ${min} characters or more` : undefined);
export const max = max => value => (value && value.trim().length > max ? `Must be ${max} characters or less` : undefined);
export const minValue = min => value => (value && value < min ? `Must be ${addThousandSeparator(min)} or more` : undefined);
export const maxValue = max => value => (value && value > max ? `Must be ${addThousandSeparator(max)} or less` : undefined);
export const id = value => /^[a-zA-Z0-9_\-.]+$/.test(value) ? undefined : 'ID can only contain a-z, A-Z, 0-9, _ and -';
export const campaignId = value => /^[A-Z\d]+$/.test(value) ? undefined : 'Campaign ID can only contain alphanumeric characters and it should be capital letters ';
export const offerId = value => /^(OFF|BEN|PER)-\d{1,10}$/i.test(value) ? undefined : OFFER_ID_ERROR_MSG;
export const campaignName = value => /^[a-zA-Z\d\s_-]+$/.test(value)
  ? undefined
  : 'Name can only contain alphanumeric characters, digits, dashes, and underscores';
export const sid = value => /^s[0-9]+$/.test(value) ? undefined : 'sID starts with a lower case "s", followed by digits';
export const email = value => emailRegex.test(value) ? undefined : 'You must enter a valid email.';
export const usernameRequired = value => value?.length > 0 || 'Name is required';

export const ensureUnique = (key, field, dirtyFields, additionalValues) => async value => {
  if (dirtyFields[field]) {
    const result = await validateApi.checkUniqueness({ key, values: additionalValues ? JSON.stringify([ ...additionalValues, value ]) : JSON.stringify([ value ]) });
    if (result) return `"${field}" with the value "${value}" already exists`;
  }
};

// Placement validators
export const applicationId = value => /^[a-z0-9_-]+$/.test(value) ? undefined : 'ID can only contain a-z, 0-9, _ and -.';

export const validateRuleType = ruleTypes => values => {
  const ccauCampaignRuleTypeId = ruleTypes.find(({ slug }) => slug === 'ccau_campaign')?.id;
  const isCcauRuleTypeSelected = values.includes(ccauCampaignRuleTypeId);
  const isOtherRuleTypesSelected = values.length > 1;
  if (isCcauRuleTypeSelected && isOtherRuleTypesSelected) {
    return 'CCAU Campaign cannot be selected with other rule types';
  }
};
export const validateRuleSubType = (ruleTypes, ruleSubTypes, ruleSubTypeSelected) => values => {
  const ccauCampaignRuleTypeId = ruleTypes.find(({ slug }) => slug === 'ccau_campaign')?.id;
  const isCcauRuleTypeSelected = ruleSubTypeSelected.includes(ccauCampaignRuleTypeId);
  const targetedSubTypeId = ruleSubTypes.find(({ type }) => type === 'targeted')?.id;
  const isOtherRuleSubTypesSelected = values.filter(id => id !== targetedSubTypeId).length >= 1;
  if (isCcauRuleTypeSelected && isOtherRuleSubTypesSelected) {
    return 'Only Targeted Campaign can be selected when the CCAU Campaign rule type is selected';
  }
};

// Access validators
export const teamRequired = value => value > 0 || 'Team is required';
export const roleRequired = value => value?.length > 0 || 'Role selection is required';
export const roleNameValidation = value => /^[a-zA-Z\d\s:_-]+$/.test(value) ? undefined : 'Name can only contain alphanumeric characters, digits, dashes, underscores, and colons.';
export const reservedRoleName = () => value => {
  if (value?.toLowerCase().includes('viewer') || value?.toLowerCase().includes('team owner')) {
    return `"Viewer" and "Team Owner" are reserved role names. Please choose another name.`;
  }
};
export const requiredPermissions = (permissions, teamId) => value => {
  if (parseInt(teamId) === 1) {
    const removedPermissions = difference(FULL_PERMISSION_LIST, permissions)
      .filter(p => !CAMPAIGN_ALERT_PERMISSIONS.includes(p) && !REVIEW_APPROVE_PERMISSIONS.includes(p));
    if (removedPermissions.length) {
      return `Permissions cannot be removed from the Pigeon team - ${removedPermissions[0]}`;
    }
  }
};

export const requiredPlatform = value => value?.length > 0 || 'Platform selection is required';
export const requiredRuleType = value => value?.length > 0 || 'Rule type selection is required';
export const requiredCampaignType = value => value?.length > 0 || 'Campaign type selection is required';
