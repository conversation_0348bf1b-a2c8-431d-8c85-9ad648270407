import moment from 'moment';
import { DATE_TIME_PRESENTATION_FORMAT, DATE_ISO_FORMAT } from '../constants';

export const aInB = (a, b) => {
  for (const aProp in a) {
    const aVal = a[aProp];

    if (!(aProp in b) || b[aProp] !== aVal) {
      return false;
    }
  }
  return true;
};

export const aIsBShallow = (a, b) => Object.keys(a).length === Object.keys(b).length ? aInB(a, b) : false;

export const createIdFromName = name => (
  name.trim().replace(/\s+/g, '-').replace(/[^a-zA-Z0-9_/-]/g, '').toLowerCase()
);

export const removeFalsyKeys = o => {
  for (const key in o) {
    if (!o[key]) {
      delete o[key];
    }
  }
  return o;
};

export const formatWord = (word, { plural = false, capitalize: capitalizeWord = false } = {}) => {
  if (!word) {
    return word;
  }
  if (plural) {
    word += 's';
  }
  if (capitalizeWord) {
    word = word.charAt(0).toUpperCase() + word.substring(1);
  }
  return word;
};

export const capitalize = word => formatWord(word, { capitalize: true });

const normalize = (val = '') => typeof val === 'string' ? val.trim().toLowerCase() : val;

export const normalizedStrCompare = (a = '', b = '') => normalize(a) === normalize(b);

export const normalizedStrIncludes = (a = '', b = '') => normalize(a).includes(normalize(b));

export const timeout = ms => (
  new Promise(resolve => setTimeout(resolve, ms))
);

/**
 * Parse JSON or return the original value if value is not JSON.
 * @param value
 * @returns {any}
 */
export const jsonParseOr = value => {
  try {
    const output = JSON.parse(value);

    // edge case inspired by https://stackoverflow.com/a/20392392
    if (output && typeof output === 'object') {
      return output;
    }
  } catch (e) {}
  return value;
};

/**
 * Matches all searches in a string
 * @param search - the search term
 * @param inputString - the string to search
 * @returns {[]} an array of indices where the search was found
 */
export const matchAll = (search, inputString) => {
  if (!inputString || inputString.length === 0) {
    return [];
  }
  let startIndex = 0;

  const indices = [];
  const lcSearch = search.toLowerCase();
  const lcInput = inputString.toLowerCase();

  let index;
  while ((index = lcInput.indexOf(lcSearch, startIndex)) > -1) {
    indices.push(index);
    startIndex = index + search.length;
  }
  return indices;
};

export const debounce = (fn, wait) => {
  let t;
  return function() {
    clearTimeout(t);
    t = setTimeout(() => fn.apply(this, arguments), wait);
  };
};

let i = 0;
export const uniqueId = () => i++;

/**
 * Format react-datetime calendar date. If value matches desired 'MM/DD/YYYY hh:mm A' format, convert to moment object,
 * otherwise display as is (i.e. manually entered date)
 * @param value
 * @returns {any}
 */
export const formatReactDateTimeValue = (value) => {
  if (value && (moment(value, DATE_TIME_PRESENTATION_FORMAT, true).isValid())) {
    return moment(value, DATE_TIME_PRESENTATION_FORMAT);
  } else if (value && moment(value, DATE_ISO_FORMAT, true).isValid()) {
    return moment(value, DATE_ISO_FORMAT);
  }
  return value;
};

export const createInitialsFromName = (name = '') => {
  const trimmedName = name.trim();
  const names = trimmedName.split(' ');
  // if only a first name, take first two letters, otherwise take a letter from each name
  const initials = names.length === 1
    ? trimmedName.substring(0, 2)
    : names.reduce((acc, v) => `${acc}${(v || '').charAt(0)}`, '');
  return initials.toUpperCase();
};

export const emptyStringsToNull = obj => {
  const data = Object.keys(obj).reduce((acc, key) => {
    acc[key] = obj[key] === '' ? null : obj[key];
    return acc;
  }, {});
  return data;
};

export const getCampaignTypeUsingExternalRef = (externalRef) => {
  if (!externalRef) return null;
  if ([ 'off-', 'ben-', 'per-' ].some(offerIdPrefix => externalRef.toLowerCase().startsWith(offerIdPrefix))) return 'offer';
  return [ 'MASS', 'MESSAGE', 'WEALTH' ].includes(externalRef) ? externalRef.toLowerCase() : 'targeted';
};
