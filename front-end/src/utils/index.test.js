import moment from 'moment';
import { DATE_TIME_PRESENTATION_FORMAT, DATE_ISO_FORMAT } from '../constants';
import {
  capitalize,
  aInB,
  aIsBShallow,
  createIdFromName,
  formatWord,
  removeFalsyKeys,
  normalizedStrCompare,
  normalizedStrIncludes,
  jsonParseOr,
  matchAll,
  formatReactDateTimeValue,
  createInitialsFromName,
  emptyStringsToNull,
} from './index';

describe('utils', () => {
  test('capitalize', () => {
    expect(capitalize('alert')).toStrictEqual('Alert');
  });

  test('formatWord', () => {
    expect(formatWord('alert has been saved successfully')).toStrictEqual('alert has been saved successfully');
    expect(formatWord('alert has been saved successfully', { capitalize: true })).toStrictEqual('Alert has been saved successfully');
    expect(formatWord('/user', { plural: true })).toStrictEqual('/users');
  });

  test('aInB', () => {
    const a = {
      brand: 'toyota',
      model: '4runner',
    };

    const b = {
      brand: 'toyota',
      model: '4runner',
      wheels: 4,
      sunroof: true,
    };

    expect(aInB(a, b)).toBeTruthy();
    expect(aInB(a, a)).toBeTruthy();
    expect(aInB(b, b)).toBeTruthy();
    expect(aInB(b, a)).toBeFalsy();
  });

  test('aIsBShallow', () => {
    const a = { foo: 'bar' };
    const b = { foo: 'bar' };
    const c = { foo: 'bar', baz: 'bur' };
    const d = { baz: 'bur' };
    expect(aIsBShallow(a, b)).toBeTruthy();
    expect(aIsBShallow(a, a)).toBeTruthy();
    expect(aIsBShallow(a, c)).toBeFalsy();
    expect(aIsBShallow(a, d)).toBeFalsy();
  });

  test('createIdFromName', () => {
    const upperCase = 'PAGEID';
    expect(createIdFromName(upperCase)).toStrictEqual(upperCase.toLowerCase());

    const spacedName = 'A Spaced Name';
    const spacedId = 'a-spaced-name';
    expect(createIdFromName(spacedName)).toStrictEqual(spacedId);

    const randomName = 'R@ndøm. Characters, punctuation, and Numbers 12345';
    const randomId = 'rndm-characters-punctuation-and-numbers-12345';
    expect(createIdFromName(randomName)).toStrictEqual(randomId);
  });

  test('removeFalsyKeys', () => {
    const objectWithUndefinedValues = {
      art: 'monkler',
      jane: undefined,
      bob: 0,
      dan: false,
      roy: true,
    };

    expect(removeFalsyKeys(objectWithUndefinedValues)).toStrictEqual({
      art: 'monkler',
      roy: true,
    });
  });

  test('normalizedStrCompare', () => {
    expect(normalizedStrCompare(undefined, 'Hello  ')).toBe(false);
    expect(normalizedStrCompare('hello', 'Hello  ')).toBe(true);
    expect(normalizedStrCompare('hello', 'Hel lo  ')).toBe(false);
    expect(normalizedStrCompare(1, '1')).toBe(false);
  });

  test('normalizedStrIncludes', () => {
    expect(normalizedStrIncludes('R:VCL:ZZ', 'R:VCL')).toBe(true);
    expect(normalizedStrIncludes(undefined, 'R:VCL')).toBe(false);
    expect(normalizedStrIncludes('R:VCL:ZZ', undefined)).toBe(true); // search string defaults to blank string if falsy
  });

  test('jsonParseOr', () => {
    expect(jsonParseOr(null)).toStrictEqual(null);
    expect(jsonParseOr(100)).toStrictEqual(100);
    expect(jsonParseOr('hiya')).toStrictEqual('hiya');
    expect(jsonParseOr(`[1,2,"3"]`)).toStrictEqual([ 1, 2, '3' ]);
    expect(jsonParseOr(`[1,2,3"]`)).toStrictEqual('[1,2,3"]'); // malformed json, returns original string
  });

  test('matchAll', () => {
    const search = 'hi';
    const string = 'Hehi howihi ahi';
    expect(matchAll(search, string)).toStrictEqual([ 2, 9, 13 ]);
    expect(matchAll(search, undefined)).toStrictEqual([]);
  });

  test('formatReactDateTimeValue', () => {
    expect(formatReactDateTimeValue('02/26/2021 11:11 AM'))
      .toStrictEqual(moment('02/26/2021 11:11 AM', DATE_TIME_PRESENTATION_FORMAT));
    expect(formatReactDateTimeValue('2022-06-30T04:00:00.000Z'))
      .toStrictEqual(moment('2022-06-30T04:00:00.000Z', DATE_ISO_FORMAT));
    expect(formatReactDateTimeValue('02-26-2021 11:11 AM')).toStrictEqual('02-26-2021 11:11 AM');
    expect(formatReactDateTimeValue('02/26/2021 11:11')).toStrictEqual('02/26/2021 11:11');
    expect(formatReactDateTimeValue('test')).toStrictEqual('test');
  });

  test('create initials from name', () => {
    expect(createInitialsFromName('Harry Potter')).toBe('HP');
    expect(createInitialsFromName('harry Potter')).toBe('HP');
    expect(createInitialsFromName('Harry ')).toBe('HA');
    expect(createInitialsFromName('Harry James Potter')).toBe('HJP');
    expect(createInitialsFromName('')).toBe('');
    expect(createInitialsFromName('   ')).toBe('');
  });

  test('convert empty strings to null', () => {
      const obj = {
        a: '',
        b: 'example',
      };
      const objWithNull = {
        a: null,
        b: 'example',
      };

      expect(emptyStringsToNull(obj)).toStrictEqual(objWithNull);
  });
});
