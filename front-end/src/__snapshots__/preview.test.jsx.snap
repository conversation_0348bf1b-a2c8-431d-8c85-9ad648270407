// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Preview Snapshot 1`] = `
<Router
  history={
    {
      "action": "POP",
      "block": [Function],
      "createHref": [Function],
      "go": [Function],
      "goBack": [Function],
      "goForward": [Function],
      "length": 1,
      "listen": [Function],
      "location": {
        "hash": "",
        "pathname": "/",
        "search": "",
        "state": undefined,
      },
      "push": [Function],
      "replace": [Function],
    }
  }
>
  <Switch>
    <Route
      component={[Function]}
      path="/preview/:space/:type/:contentId/:container/:application/:isDismissible/:languages"
    />
  </Switch>
</Router>
`;
