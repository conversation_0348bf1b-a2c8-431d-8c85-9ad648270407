// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Index index matches snapshot 1`] = `
<Provider
  store={
    {
      "dispatch": [Function],
      "getState": [Function],
      "replaceReducer": [Function],
      "subscribe": [Function],
      Symbol(observable): [Function],
    }
  }
>
  <Router
    history={
      {
        "action": "POP",
        "block": [Function],
        "createHref": [Function],
        "go": [Function],
        "goBack": [Function],
        "goForward": [Function],
        "length": 1,
        "listen": [Function],
        "location": {
          "hash": "",
          "pathname": "/",
          "search": "",
          "state": undefined,
        },
        "push": [Function],
        "replace": [Function],
      }
    }
  >
    <ScrollToTop />
    <withRouter(Connect(AppWithRouter))>
      <Routes />
    </withRouter(Connect(AppWithRouter))>
  </Router>
</Provider>
`;
