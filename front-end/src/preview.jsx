import 'core-js/es6/promise';
import React from 'react';
import ReactD<PERSON> from 'react-dom';
import { Router, Route, Switch } from 'react-router-dom';
import { createBrowserHistory } from 'history';

import PreviewComponent from './components/preview';

import './assets/preview.scss';

const history = createBrowserHistory();

export default class Preview extends React.PureComponent {
  render() {
    return (
      <Router history={history}>
        <Switch>
          <Route path="/preview/:space/:type/:contentId/:container/:application/:isDismissible/:languages" component={PreviewComponent} />
        </Switch>
      </Router>
    );
  }
}

ReactDOM.render(
  <Preview />,
  document.getElementById('preview')
);
