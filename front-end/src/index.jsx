import React from 'react';
import ReactDOM from 'react-dom';
import { Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import { createBrowserHistory } from 'history';
import App from './components/app';
import Routes from './components/routes/Routes';
import configureStore from './store/configureStore';

import './assets/main.scss';
import ScrollToTop from './hooks/scrollToTop';

const history = createBrowserHistory();
const store = configureStore();

const Index = () => (
  <Provider store={store}>
    <Router history={history}>
      <ScrollToTop />
      <App>
        <Routes />
      </App>
    </Router>
  </Provider>
);

export default Index;

ReactDOM.render(
  <Index />,
  document.getElementById('app')
);
