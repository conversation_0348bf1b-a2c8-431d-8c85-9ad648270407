import qs from 'qs';
import { req } from './index';
import { getPermissions } from './permissions';
import { getUsers } from './users';

const v2baseURL = '/api/v2';

const deleteNulls = (obj) => {
  for (const property in obj) {
    const value = obj[property];
    if ((value === undefined) || (value === null)) {
      delete obj[property];
    }
  }
};

export default {
  getPermissions,
  getUsers,
  getTypes: async() => {
    const url = `/variable-mappings/types`;
    const res = await req.get(url);
    return res.data.data;
  },
  getMappings: async(query = {}) => {
    const res = await req.get(`/variable-mappings/sets${qs.stringify(query, { addQueryPrefix: true })}`);
    return res.data.data;
  },
  createMappingSet: async(newSet) => {
    deleteNulls(newSet);
    const url = `/variable-mappings/sets`;
    const res = await req.post(url, newSet, { baseURL: v2baseURL });
    return res.data.data;
  },
  updateMappings: async(set, newStatus) => {
    deleteNulls(set);
    const url = `/variable-mappings/sets/${set.variable_set_id}`;
    const newSet = {
      ...set,
      'status': newStatus,
    };
    const res = await req.patch(url, newSet, { baseURL: v2baseURL });
    return res.data.data;
  },
  editDraft: async(set) => {
    deleteNulls(set);
    const url = `/variable-mappings/sets/${set.variable_set_id}`;
    const res = await req.put(url, set, { baseURL: v2baseURL });
    return res.data.data;
  },
  getApprovers: async() => {
    const url = `/variable-mappings/approvers`;
    const res = await req.get(url, { baseURL: v2baseURL });
    return res.data;
  },
};
