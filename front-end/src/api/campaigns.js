import qs from 'qs';
import { req } from './index';

export const getCampaigns = (query = {}) => (
  req.get(`/campaign-rules${qs.stringify(query, { addQueryPrefix: true })}`)
    .then(res => res.data)
);

export const setCampaignActive = (id, status, active = true) => (
  req.patch(`/campaign-rules/${id}`, { status: status, disabled: !active })
    .then(res => res.data)
);

export const deleteCampaign = id => (
  req.delete(`/campaign-rules/${id}`)
    .then(res => res.data)
);
