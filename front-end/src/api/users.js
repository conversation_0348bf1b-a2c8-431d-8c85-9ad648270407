import qs from 'qs';
import { req } from './index';

export const getUsers = (query) => (
  req.get(`/users${qs.stringify(query, { addQueryPrefix: true })}`)
    .then(res => res.data)
);

export const getUser = id => (
  req.get(`/users/${id}`)
    .then(res => res.data)
);

export const createUser = (data) => (
  req.post('/users', data)
    .then(res => res.data)
);

export const updateUser = (id, data) => (
  req.patch(`/users/${id}`, data)
    .then(res => res.data)
);

export const activateUser = (id) => (
  req.post(`/users/${id}/activate`)
    .then(res => res.data)
);

export const deactivateUser = (id) => (
  req.post(`/users/${id}/deactivate`)
    .then(res => res.data)
);
