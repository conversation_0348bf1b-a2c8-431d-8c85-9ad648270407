import { activateContainer, deactivateContainer, getContainers } from './containers';
import mockAxios from 'axios';

describe('Test activate/deactivate container api', () => {
  beforeEach(() => {
    mockAxios.post.mockClear();
    mockAxios.get.mockClear();
  });

  it('test set container as active', () => {
    activateContainer('id');
    expect(mockAxios.post).toHaveBeenCalledTimes(1);
  });

  it('test set container as inactive', () => {
    deactivateContainer('id');
    expect(mockAxios.post).toHaveBeenCalledTimes(1);
  });

  it('test get container', () => {
    getContainers();
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });

  it('test get container with query', () => {
    getContainers({});
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });
});
