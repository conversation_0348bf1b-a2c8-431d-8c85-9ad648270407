import qs from 'qs';
import { req } from './index';

export const getMessageCentreCampaigns = (query = {}) =>
  req.get(`/message-centre/campaigns${qs.stringify(query, { addQueryPrefix: true })}`).then(res => {
    return res.data;
  });

export const exportMessageCentreCampaigns = (query = {}) => (
  window.open(`/api/v1/message-centre/campaigns/export${qs.stringify(query, { addQueryPrefix: true })}`)
);

export const createMessageCentreCampaign = data => {
  return req.post(`/message-centre/campaigns`, data).then(res => {
    return res.data;
  });
};

export const updateMessageCentreCampaign = (id, data) => {
  return req.put(`/message-centre/campaigns/${id}`, data).then(res => {
    return res.data;
  });
};

export const getMessageCentreCampaign = id => {
  return req.get(`/message-centre/campaigns/${id}`).then(res => {
    return res.data;
  });
};

export const deleteMessageCentreCampaign = id => {
  return req.delete(`/message-centre/campaigns/${id}`).then(res => {
    return res.data;
  });
};

export const setMessageCentreCampaignActive = (id, status) =>
  req
    .patch(`/message-centre/campaigns/${id}`, { msg_status: status })
    .then(res => res.data);
