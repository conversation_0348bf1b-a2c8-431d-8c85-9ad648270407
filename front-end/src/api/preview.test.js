import { getContentByParams, getLocales } from './preview';
import mockAxios from 'axios';

describe('Test get Content By Params api', () => {
  beforeEach(() => {
    mockAxios.get.mockClear();
  });

  it('test get campaigns', () => {
    getContentByParams({});
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });

  it('test get campaigns with query', () => {
    getContentByParams({}, {});
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });

  it('test get locales', () => {
    getLocales({});
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });
});
