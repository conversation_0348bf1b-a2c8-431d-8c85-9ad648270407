import qs from 'qs';
import { req } from './index';

export const getTeams = (query = {}) => (
  req.get(`/teams${qs.stringify(query, { addQueryPrefix: true })}`)
    .then(res => res.data)
);

export const getTeamOwners = (query = {}) => (
  req.get(`/teams/owners${qs.stringify(query, { addQueryPrefix: true })}`)
    .then(res => res.data)
);

export const getTeam = id => (
  req.get(`/teams/${id}`)
    .then(res => res.data)
);

export const createTeam = (data) => (
  req.post('/teams', data)
    .then(res => res.data)
);

export const updateTeam = (id, data) => (
  req.patch(`/teams/${id}`, data)
    .then(res => res.data)
);

export const activateTeam = (id, query = {}) => (
  req.post(`/teams/${id}/activate${qs.stringify(query, { addQueryPrefix: true })}`)
    .then(res => res.data)
);

export const deactivateTeam = (id) => (
  req.post(`/teams/${id}/deactivate`)
    .then(res => res.data)
);
