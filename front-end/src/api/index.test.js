import { req, setupInterceptors } from './index';
import mockAxios from 'axios';
import { userAuth } from '../store/actions/auth';
import * as alertBanner from '../store/actions/alertBanner';
import * as usersApi from './users';
import * as teamsApi from './teams';
import * as rolesApi from './roles';
const testRoute = '/api/test-route';

describe('Common Request handler (Axios)', () => {
  it('delete', () => {
    req.delete(testRoute);
    expect(mockAxios.delete).toHaveBeenCalledTimes(1);
    expect(mockAxios.delete).toHaveBeenCalledWith(testRoute, undefined);
  });

  it('get', () => {
    req.get(testRoute);
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
    expect(mockAxios.get).toHaveBeenCalledWith(testRoute, undefined);
  });

  it('patch', () => {
    req.patch(testRoute, { data: 'data' });
    expect(mockAxios.patch).toHaveBeenCalledTimes(1);
    expect(mockAxios.patch).toHaveBeenCalledWith(testRoute, { data: 'data' }, undefined);
  });

  it('post', () => {
    req.post(testRoute, { data: 'data' });
    expect(mockAxios.post).toHaveBeenCalledTimes(1);
    expect(mockAxios.post).toHaveBeenCalledWith(testRoute, { data: 'data' }, undefined);
  });

  it('put', () => {
    req.put(testRoute, { data: 'data' });
    expect(mockAxios.put).toHaveBeenCalledTimes(1);
    expect(mockAxios.put).toHaveBeenCalledWith(testRoute, { data: 'data' }, undefined);
  });
});

describe('Axios interceptor testing', () => {
  const store = global.newStore();
  setupInterceptors(store);

  it('401 error', async() => {
    store.dispatch(userAuth({}));
    mockAxios.fail = true;
    try {
      await req.post(testRoute, { response: { status: 401 } });
    } catch (err) {
      expect(store.getState().authenticated).toStrictEqual({ loggedOut: true });
    }
  });

  it('429 error', async() => {
    mockAxios.fail = true;
   const addAlert = jest.spyOn(alertBanner, 'addAlert');
    try {
      await req.post(testRoute, { response: { status: 429 } });
    } catch (err) {
      expect(addAlert).toHaveBeenCalledWith({ message: 'Too many requests' });
    }
  });

  it('500 error - meta data', async() => {
    mockAxios.fail = true;
   const addAlert = jest.spyOn(alertBanner, 'addAlert');
    try {
      await req.post(testRoute, { response: { status: 500, data: { metadata: [ 'error' ] } } });
    } catch (err) {
      expect(addAlert).toHaveBeenCalledTimes(2);
    }
  });

  it('Error with message and metadata', async() => {
    const payload = { response: { status: 500, data: { message: 'error', metadata: [ { message: 'meta1' } ] } } };
    mockAxios.fail = true;
    try {
      await req.post(testRoute, payload);
    } catch (err) {
      expect(err).toBe(payload);
    }
  });

  it('Error with empty data', async() => {
    const payload = { response: { status: 500, data: {} } };
    mockAxios.fail = true;
    try {
      await req.post(testRoute, payload);
    } catch (err) {
      expect(err).toBe(payload);
    }
  });

  it('Error with no data', async() => {
    const payload = { response: { status: 500 } };
    mockAxios.fail = true;
    try {
      await req.post(testRoute, payload);
    } catch (err) {
      expect(err).toBe(payload);
    }
  });

  it('200 Error', async() => {
    const payload = { response: { status: 200 } };
    mockAxios.fail = true;
    try {
      await req.post(testRoute, payload);
    } catch (err) {
      expect(err).toBe(payload);
    }
  });

  it('Empty Error', async() => {
    const payload = {};
    mockAxios.fail = true;
    try {
      await req.post(testRoute, payload);
    } catch (err) {
      expect(err).toBe(payload);
    }
  });
});

describe('individual route handler implementations', () => {
  beforeAll(() => {
    mockAxios.reset();
    const mockRes = new Promise(resolve => resolve({ data: {} }));
    mockAxios.get.mockImplementation(() => mockRes);
    mockAxios.post.mockImplementation(() => mockRes);
    mockAxios.patch.mockImplementation(() => mockRes);
  });

  afterAll(() => {
    mockAxios.reset();
  });

  it('access', async() => {
    const accessAPIs = [ // in format of entity name, api
      [ 'Team', teamsApi ],
      [ 'User', usersApi ],
      [ 'Role', rolesApi ],
    ];

    const testTemplate = route => {
      const routeLC = route.toLowerCase();
      return [
        // in format of fn name, req params, req http verb invoked, expected axios invocation params
        [ `get${route}s`, [ undefined ], 'get', [ `/${routeLC}s`, undefined ] ],
        [ `get${route}`, [ 1 ], 'get', [ `/${routeLC}s/1`, undefined ] ],
        [ `create${route}`, [ { id: 1 } ], 'post', [ `/${routeLC}s`, { id: 1 }, undefined ] ],
        [ `update${route}`, [ 1, { name: 'Bob' } ], 'patch', [ `/${routeLC}s/1`, { name: 'Bob' }, undefined ] ],
        [ `activate${route}`, [ 1 ], 'post', [ `/${routeLC}s/1/activate`, undefined, undefined ] ],
        [ `deactivate${route}`, [ 1 ], 'post', [ `/${routeLC}s/1/deactivate`, undefined, undefined ] ],
      ];
    };

    for (const [ apiName, api ] of accessAPIs) {
      for (const [ fName, fParams, verb, expected ] of testTemplate(apiName)) {
        try {
          // eslint-disable-next-line import/namespace
          await api[ fName ](...fParams);
          expect(mockAxios[verb]).toHaveBeenCalledWith(...expected);
        } catch (e) {
          console.warn(`Test case failed in 'handlers > all routes' for API: ${apiName}, function: /${fName} due to ${e}`);
          throw e; // allow jest assertion failures to bubble up to test runner
        } finally {
          mockAxios.reset();
        }
      };
    };
  });
});
