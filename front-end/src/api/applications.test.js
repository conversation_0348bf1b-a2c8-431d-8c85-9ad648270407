import { activateApplication, getApplications } from './applications';
import mockAxios from 'axios';

describe('Test application api', () => {
  beforeEach(() => {
    mockAxios.post.mockClear();
    mockAxios.get.mockClear();
  });

  it('test getApplications empty query', () => {
    getApplications();
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });

  it('test activate Application api', () => {
    activateApplication('application id');
    expect(mockAxios.post).toHaveBeenCalledTimes(1);
  });

  it('test activate Application api with query', () => {
    activateApplication('application id', {});
    expect(mockAxios.post).toHaveBeenCalledTimes(1);
  });
});
