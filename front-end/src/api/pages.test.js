import { activatePage, deactivatePage, getPages } from './pages';
import mockAxios from 'axios';

describe('Test activate/deactivate page api', () => {
  beforeEach(() => {
    mockAxios.post.mockClear();
    mockAxios.get.mockClear();
  });

  it('test set page as active', () => {
    activatePage('id');
    expect(mockAxios.post).toHaveBeenCalledTimes(1);
  });

  it('test set page as inactive', () => {
    deactivatePage('id');
    expect(mockAxios.post).toHaveBeenCalledTimes(1);
  });

  it('test get Pages', () => {
    getPages();
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });

  it('test get Pages with query', () => {
    getPages({});
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });
});
