import mockAxios from 'axios';
import { getMessageDetail, updateMessageDetail, getMessageCentreMessages } from './message-centre-messages';

describe('Message centre messages api', () => {
    beforeEach(() => {
        mockAxios.get.mockClear();
        mockAxios.put.mockClear();
      });

    test('Get Message Detail', async() => {
        getMessageDetail(1);
        expect(mockAxios.get).toHaveBeenCalledTimes(1);
    });

    test('Update Message Detail', async() => {
        updateMessageDetail(1, {});
        expect(mockAxios.put).toHaveBeenCalledTimes(1);
    });

    test('Get Message List', async() => {
        getMessageCentreMessages();
        expect(mockAxios.get).toHaveBeenCalledTimes(1);
    });
});
