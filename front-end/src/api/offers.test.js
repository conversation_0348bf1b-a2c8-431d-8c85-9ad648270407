import mockAxios from 'axios';
import {
  getOffer,
  updateOffer,
  createOffer,
  getOffers,
  getProductBook,
  updateOfferStatus,
} from './offers';

describe('Message centre api', () => {
    beforeEach(() => {
        mockAxios.get.mockClear();
        mockAxios.post.mockClear();
        mockAxios.put.mockClear();
      });

    test('Get Offer', async() => {
        mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data: {} }));
        getOffer('');
        expect(mockAxios.get).toHaveBeenCalledTimes(1);
    });

    test('Create Offer', async() => {
        createOffer({});
        expect(mockAxios.post).toHaveBeenCalledTimes(1);
    });

    test('Update Offer', async() => {
        updateOffer(1, { title: 'new offer' });
        expect(mockAxios.put).toHaveBeenCalledTimes(1);
    });

    test('Get Offers', async() => {
        getOffers({ limit: 30, offset: 0 });
        expect(mockAxios.get).toHaveBeenCalledTimes(1);
    });

    test('Get Products Book', async() => {
        mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data: {} }));
        getProductBook();
        expect(mockAxios.get).toHaveBeenCalledTimes(1);
    });
});

describe('Test activate Offer api', () => {
    beforeEach(() => {
      mockAxios.put.mockClear();
    });

    it('test set offer as active', () => {
      updateOfferStatus('id', 'RESUME');
      expect(mockAxios.put).toHaveBeenCalledTimes(1);
    });
});
