import qs from 'qs';
import { req } from './index';

export const getLocales = (params) => (
  req.get(`/contents/spaces/${params.space}/locales`)
    .then(res => res.data)
);

export const getContentByParams = (params, query = {}) => (
  req.get(`/contents/spaces/${params.space}/types/${params.type}/contents/${params.contentId}${qs.stringify(query, { addQueryPrefix: true })}`)
    .then(res => res.data)
);
