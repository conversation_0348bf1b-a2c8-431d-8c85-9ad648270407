import axios from 'axios';
import https from 'https';
import _ from 'lodash';

import { addAlert } from '../store/actions/alertBanner';
import { userUnauth } from '../store/actions/auth';

axios.defaults.timeout = 60000;
axios.defaults.baseURL = '/api/v1';
axios.defaults.httpsAgent = new https.Agent({
  rejectUnauthorized: false,
});
axios.defaults.headers = { Pragma: 'no-cache' };

export const req = {
  delete: (url, config) => axios.delete(url, config),
  get: (url, config) => axios.get(url, config),
  patch: (url, data, config) => axios.patch(url, data, config),
  post: (url, data, config) => axios.post(url, data, config),
  put: (url, data, config) => axios.put(url, data, config),
};

export const setupInterceptors = store => {
  axios.interceptors.response.use(null, err => {
    if (err && err.response) {
      if (err.response.status === 401) {
        if (err.response.data && err.response.data.needsRefresh) {
          store.dispatch(addAlert({
            message: 'Your session has expired. The page will refresh automatically.',
            type: 'warning',
          }));

          setTimeout(() => {
            window.location.reload();
          }, 2000);

          return Promise.reject(err);
        } else {
          store.dispatch(userUnauth());
        }
      } else if (err.response.status === 429) {
        store.dispatch(addAlert({ message: 'Too many requests' }));
      } else if (err.response.status !== 200) {
        let error = 'Network request error';
        if (err.response.data) {
          error = err.response.data.message || error;
          if (err.response.data.metadata) {
            const errors = err.response.data.metadata.map(item => _.unescape(item.message || item));
            error = error + ': ' + errors.join(' ');
          }
        }
        store.dispatch(addAlert({ message: error }));
      }
    } else if (err.code === 'ERR_NETWORK') {
      window.location.assign('/');
      store.dispatch(userUnauth());
    }
    return Promise.reject(err);
  });
};
