import mockAxios from 'axios';
import {
    getMessageCentreCampaigns,
    deleteMessageCentreCampaign,
    setMessageCentreCampaignActive,
    createMessageCentreCampaign,
    getMessageCentreCampaign,
} from './message-centre-campaigns';

describe('Message centre api', () => {
    beforeEach(() => {
        mockAxios.get.mockClear();
        mockAxios.post.mockClear();
        mockAxios.patch.mockClear();
      });

    test('Get Campaigns list', async() => {
        getMessageCentreCampaigns({ limit: 30, offset: 0 });
        expect(mockAxios.get).toHaveBeenCalledTimes(1);
    });

    test('Get Campaign', async() => {
        mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data: {} }));
        getMessageCentreCampaign('');
        expect(mockAxios.get).toHaveBeenCalledTimes(1);
    });

    test('deleteMessageCentreCampaign', async() => {
        deleteMessageCentreCampaign('672812b04f18416af71f7a40');
        expect(mockAxios.delete).toHaveBeenCalledTimes(1);
    });

    test('setMessageCentreCampaignActive', async() => {
        setMessageCentreCampaignActive('672812b04f18416af71f7a40', 'New');
        expect(mockAxios.patch).toHaveBeenCalledTimes(1);
    });

    test('createMessageCentreCampaign', async() => {
        createMessageCentreCampaign({});
        expect(mockAxios.post).toHaveBeenCalledTimes(1);
    });
});
