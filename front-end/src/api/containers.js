import { req } from './index';
import qs from 'qs';

const endpoint = '/containers';

export const getContainers = (query = {}) => (
  req.get(`${endpoint}${qs.stringify(query, { addQueryPrefix: true })}`)
    .then(res => res.data)
);

export const getContainer = id => req.get(`${endpoint}/${id}`);

export const deleteContainer = id => req.delete(`${endpoint}/${id}`);

export const postContainer = values => req.post(endpoint, values);

export const patchContainer = (id, values) => req.patch(`${endpoint}/${id}`, values);

export const activateContainer = (id) => (
  req.post(`/containers/${id}/activate`)
    .then(res => res.data)
);

export const deactivateContainer = (id) => (
  req.post(`/containers/${id}/deactivate`)
    .then(res => res.data)
);
