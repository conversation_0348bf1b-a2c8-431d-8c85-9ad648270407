import { getVignettes } from './vignettes';
import mockAxios from 'axios';

describe('Test Vignettes api', () => {
  beforeEach(() => {
    mockAxios.get.mockClear();
  });

  it('test get vignettes', () => {
    getVignettes();
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });

  it('test get vignettes with query', () => {
    getVignettes({});
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });
});
