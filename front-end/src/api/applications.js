import qs from 'qs';
import { req } from './index';

export const getApplications = (query = {}) => (
  req.get(`/applications${qs.stringify(query, { addQueryPrefix: true })}`)
    .then(res => res.data)
);

export const getApplication = (id) => (
  req.get(`/applications/${id}`)
    .then(res => res.data)
);

export const createApplication = (data) => (
  req.post('/applications', data)
    .then(res => res.data)
);

export const updateApplication = (id, data) => (
  req.patch(`/applications/${id}`, data)
    .then(res => res.data)
);

export const activateApplication = (id, query = {}) => (
  req.post(`/applications/${id}/activate${qs.stringify(query, { addQueryPrefix: true })}`)
    .then(res => res.data)
);

export const deactivateApplication = (id) => (
  req.post(`/applications/${id}/deactivate`)
    .then(res => res.data)
);
