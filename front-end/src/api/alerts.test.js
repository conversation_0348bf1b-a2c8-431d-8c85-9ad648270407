import { getAlerts, setAlertActive, deleteAlert } from './alerts';
import mockAxios from 'axios';

const mockId = '1';

describe('getAlerts', () => {
  beforeEach(() => {
    mockAxios.get.mockClear();
  });

  it('called without a param', () => {
    getAlerts();
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });

  it('called with a param', () => {
    getAlerts({});
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });
});

it('setAlertActive', () => {
  setAlertActive(mockId);
  expect(mockAxios.patch).toHaveBeenCalledTimes(1);
});

it('deleteAlert ', () => {
  deleteAlert(mockId);
  expect(mockAxios.delete).toHaveBeenCalledTimes(1);
});
