import qs from 'qs';
import { req } from './index';

export const getAlerts = (query = {}) => (
  req.get(`/alert-rules${qs.stringify(query, { addQueryPrefix: true })}`)
    .then(res => res.data)
);

export const setAlertActive = (id, status, active = true) => (
  req.patch(`/alert-rules/${id}`, { status: status, disabled: !active })
    .then(res => res.data)
);

export const deleteAlert = id => (
  req.delete(`/alert-rules/${id}`)
    .then(res => res.data)
);
