import { getCampaigns, setCampaignActive } from './campaigns';
import mockAxios from 'axios';

describe('Test get Campaigns api', () => {
  beforeEach(() => {
    mockAxios.get.mockClear();
  });

  it('test get campaigns', () => {
    getCampaigns();
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });

  it('test get campaigns with query', () => {
    getCampaigns({});
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });
});

describe('Test activate Campaigns api', () => {
  beforeEach(() => {
    mockAxios.patch.mockClear();
  });

  it('test set campaigns as active', () => {
    setCampaignActive('id', 'draft');
    expect(mockAxios.patch).toHaveBeenCalledTimes(1);
  });

  it('test set campaigns as active with query', () => {
    setCampaignActive('id', 'draft', false);
    expect(mockAxios.patch).toHaveBeenCalledTimes(1);
  });
});
