import qs from 'qs';
import { req } from './index';
import { CONTENT_SPACE_ID } from '../constants';

export const getContentTypes = (contentfulSpace) => (
  req.get(`/contents/spaces/${contentfulSpace || CONTENT_SPACE_ID}/types`)
    .then(res => res.data)
);

export const getContentItems = ({ type, contentfulSpace, ...query }) => {
  return req.get(`/contents/spaces/${contentfulSpace || CONTENT_SPACE_ID}/types/${type}/contents${qs.stringify(query, { addQueryPrefix: true })}`)
    .then(res => res.data);
};

export const getContentById = (type, id, contentfulSpace) => {
  return req.get(`/contents/spaces/${contentfulSpace || CONTENT_SPACE_ID}/types/${type}/contents/${id}`)
    .then(res => res.data);
};

export const getWebFragments = (query) => (
  req.get(`/contents/vignette${qs.stringify(query, { addQueryPrefix: true })}`)
    .then(res => res.data)
);

export const getWebFragment = (contentId) => (
  req.get(`/contents/vignette/${contentId}`)
    .then(res => res.data)
);
