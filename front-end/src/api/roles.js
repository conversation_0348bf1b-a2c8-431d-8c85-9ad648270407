import qs from 'qs';
import { req } from './index';

export const getRoles = (query = {}) => (
  req.get(`/roles${qs.stringify(query, { addQueryPrefix: true })}`)
    .then(res => res.data)
);

export const getRole = (id) => (
  req.get(`/roles/${id}`)
    .then(res => res.data)
);

export const updateRole = (id, values) => (
  req.patch(`/roles/${id}`, values)
    .then(res => res.data)
);

export const createRole = values => (
  req.post(`/roles`, values)
    .then(res => res.data)
);

export const activateRole = (id) => (
  req.post(`/roles/${id}/activate`)
    .then(res => res.data)
);

export const deactivateRole = (id) => (
  req.post(`/roles/${id}/deactivate`)
    .then(res => res.data)
);
