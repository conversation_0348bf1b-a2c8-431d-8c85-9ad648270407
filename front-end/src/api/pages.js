import qs from 'qs';
import { req } from './index';

const endpoint = '/pages';

export const getPages = (query = {}) => (
  req.get(`/pages${qs.stringify(query, { addQueryPrefix: true })}`)
    .then(res => res.data)
);

export const getPage = id => req.get(`${endpoint}/${id}`);

export const deletePage = id => req.delete(`${endpoint}/${id}`);

export const postPage = values => req.post(endpoint, values);

export const patchPage = (id, values) => req.patch(`${endpoint}/${id}`, values);

export const activatePage = (id) => (
  req.post(`/pages/${id}/activate`)
    .then(res => res.data)
);

export const deactivatePage = (id) => (
  req.post(`/pages/${id}/deactivate`)
    .then(res => res.data)
);
