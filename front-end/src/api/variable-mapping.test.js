import api from './variable-mapping';
import mockAxios from 'axios';

describe('variable mapping routes should generate correct configs for axios to consume', () => {
  const mappingSet = {
    'variable_set_id': 224,
    'created_at': '2021-09-14T22:20:26.213Z',
    'created_by': 's1990028',
    'status': 'draft',
    'description': 'Test vars edit',
    'nullfield': null,
    'approver_sid': 's1990028',
    'variables': [
      {
        'variable_template': 'SOLUI_TEST_END',
        'variable_campaign': 'some_campaign',
        'variable_type': 'date',
      },
    ],
  };

  it('getPermissions', async() => {
    await api.getPermissions();
    expect(mockAxios.get).toHaveBeenCalledWith('/permissions', undefined);
  });

  it('getUsers', async() => {
    await api.getUsers();
    expect(mockAxios.get).toHaveBeenCalledWith('/users', undefined);
  });

  it('getTypes', async() => {
    await api.getTypes();
    expect(mockAxios.get).toHaveBeenCalledWith('/variable-mappings/types', undefined);
  });

  it('getMappings', async() => {
    await api.getMappings({ status: 'draft' });
    expect(mockAxios.get).toHaveBeenCalledWith('/variable-mappings/sets?status=draft', undefined);
  });

  it('getMappings with query', async() => {
    await api.getMappings();
    expect(mockAxios.get).toHaveBeenCalledWith('/variable-mappings/sets', undefined);
  });

  it('createMappingSet', async() => {
    await api.createMappingSet(mappingSet);
    expect(mockAxios.post).toHaveBeenCalledWith(
      '/variable-mappings/sets',
      mappingSet,
      { baseURL: '/api/v2' }
    );
  });

  it('updateMappings', async() => {
    await api.updateMappings(mappingSet, 'active');
    expect(mockAxios.patch).toHaveBeenCalledWith(
      '/variable-mappings/sets/224',
      { ...mappingSet, status: 'active' },
      { baseURL: '/api/v2' },
    );
  });

  it('editDrafts', async() => {
    await api.editDraft(mappingSet);
    expect(mockAxios.put).toHaveBeenCalledWith(
      '/variable-mappings/sets/224',
      mappingSet,
      { baseURL: '/api/v2' },
    );
  });

  it('getApprovers', async() => {
    await api.getApprovers();
    expect(mockAxios.get).toHaveBeenCalledWith(
      '/variable-mappings/approvers',
      { 'baseURL': '/api/v2' },
    );
  });
});
