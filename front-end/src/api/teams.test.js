import { getTeamOwners } from './teams';
import mockAxios from 'axios';

describe('Test teams api', () => {
  beforeEach(() => {
    mockAxios.get.mockClear();
  });

  it('test get team owner', () => {
    getTeamOwners();
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });

  it('test get team owner with query', () => {
    getTeamOwners({});
    expect(mockAxios.get).toHaveBeenCalledTimes(1);
  });
});
