import qs from 'qs';
import { req } from './index';

export const getMessageDetail = (id, query) =>
  req.get(`/message-centre/messages/${id}${qs.stringify(query, { addQueryPrefix: true })}`).then((res) => res.data);

export const updateMessageDetail = (id, data) =>
  req.put(`/message-centre/messages/${id}`, data).then((res) => res.data);

export const getMessageCentreMessages = (query = {}) =>
  req
    .get(`/message-centre/messages${qs.stringify(query, { addQueryPrefix: true })}`)
    .then((res) => res.data);

export const exportMessageCentreMessages = (query = {}) => (
  window.open(`/api/v1/message-centre/messages/export${qs.stringify(query, { addQueryPrefix: true })}`)
);
