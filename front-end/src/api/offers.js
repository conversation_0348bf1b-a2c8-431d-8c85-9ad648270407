import qs from 'qs';
import { req } from './index';

export const getOffers = (query = {}) => {
  return req.get(`/offers${qs.stringify(query, { addQueryPrefix: true })}`).then(res => {
    return res.data;
  });
};

export const exportOffers = (query = {}) => (
  window.open(`/api/v1/offers/export${qs.stringify(query, { addQueryPrefix: true })}`)
);

export const getOffer = id => {
  return req.get(`/offers/${id}`).then(res => {
    return res.data;
  });
};

export const createOffer = data => {
  return req.post(`/offers`, data).then(res => {
    return res.data;
  });
};

export const updateOffer = (id, data) => {
  return req.put(`/offers/${id}`, data).then(res => {
    return res.data;
  });
};

export const updateOfferLocation = (id, data) => {
  return req.put(`/offers/updateOfferLocation/${id}`, data).then(res => {
    return res.data;
  });
};

export const getOfferAssignees = (query = {}) => {
  return req.get(`/offers/assignees${qs.stringify(query, { addQueryPrefix: true })}`).then(res => {
    return res.data;
  });
};

export const approveOffer = (id, data) => {
  return req.patch(`/offers/${id}`, data).then(res => {
    return res.status;
  });
};

export const getProductBook = () => {
  return req.get(`/product-book`).then(res => {
    return res.data;
  });
};

export const updateOfferStatus = (id, action) => (
  req.put(`/offers/${id}/status`, { action })
    .then(res => res.data)
);

export const deleteOffer = id => (
  req.delete(`/offers/${id}`)
    .then(res => res.data)
);
