import React from 'react';
import { shallow } from 'enzyme';

import { Provider } from 'react-redux';
import { Router } from 'react-router-dom';
import App from './components/app';
import Routes from './components/routes/Routes';
import Index from './index';

jest.mock('react-dom', () => ({
  render: jest.fn(),
}));

describe('Index', () => {
  test('index matches snapshot', () => {
    const wrapper = shallow(<Index />);
    const elementsInIndex = [ Provider, Router, App, Routes ];
    elementsInIndex.forEach(element => expect(wrapper.find(element)).toHaveLength(1));
    expect(wrapper).toMatchSnapshot();
  });
});
