
import React from 'react';
import { render } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import ScrollToTop from './scrollToTop';

describe('Scroll To Top', () => {
  it('should call window scrollTo function on load', () => {
    render(
      <MemoryRouter initialEntries={[ { pathname: '/' } ]}>
        <ScrollToTop />
      </MemoryRouter>
    );

    expect(window.scrollTo).toHaveBeenCalledTimes(1);
  });
});
