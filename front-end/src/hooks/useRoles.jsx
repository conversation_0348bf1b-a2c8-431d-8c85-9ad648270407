/* eslint-disable camelcase */
import { useEffect, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import { getTeams } from '../store/actions/teams';
import { getRoles } from '../store/actions/roles';
import { mapObjectToArray } from '../constants';
import { TEAMS_FLAGS } from '../constants/permissionsList';

export const useRoles = ({
  team_id,
  status,
  search,
  sort,
  limit,
  pageNumber,
}) => {
  const dispatch = useDispatch();

  const {
    roles: { items: roles, isLoading: rolesLoading, pagination },
    teams: { items: teams, isLoading: teamsLoading },
    authenticated: currentUser,
  } = useSelector((state) => state);

  const loading = rolesLoading || teamsLoading;
  const rolesList = useMemo(() => mapObjectToArray(roles || {}), [ roles ]);
  const teamsList = useMemo(() => mapObjectToArray(teams || {}), [ teams ]);

  const { canViewAllRoles } = useMemo(() => currentUser.permissionLevels || {}, [ currentUser ]);

  useEffect(() => {
    dispatch(getTeams({
      flag: TEAMS_FLAGS.SKIP_ALL,
      ...(!canViewAllRoles && {
        id: currentUser.team_id,
      }),
    }));
    dispatch(getRoles({
      team_id,
      status,
      search,
      sort,
      limit,
      pageNumber,
      ...(!canViewAllRoles && {
        team_id: currentUser.team_id,
      }),
    }));
  }, []);

  const mapRoles = (role) => {
    // resolve team
    const team = teamsList.find(team => team.id === role.team_id);

    return {
      ...role,
      teamActive: team?.active,
      teamName: team?.name,
    };
  };

  const processedRoles = useMemo(
    () => rolesList.map(mapRoles),
    [ loading, teamsList ]
  );

  return {
    loading,
    roles: processedRoles,
    teams: teamsList,
    pagination,
    currentUser,
  };
};
