/* eslint-disable react/prop-types */
import React from 'react';
import { renderHook } from '@testing-library/react-hooks';
import useRedirect from './useRedirect';
import { Provider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { access, permissions } from '../utils/testing-library-utils-data';

describe('useRedirect', () => {
  test.skip('Should redirect to estore', () => {
    const mockStore = configureMockStore([ thunk ]);
    const solStore = mockStore({
      campaigns: {
        pagination: {
          total: 100,
        },
      },
      authenticated: {
        permissions,
        access: { ...access,
'campaigns': {
          'containers': {
            'storefront': {
              'manage': [
                'sf-marquee-bankingaccounts',
              ],
            },

          },
          'pages': {
            'storefront': {
              'manage': [
                'sf-wealthmanagement',
              ],
            },
          },
        },
ccau_campaign: {} },
      },
    });

    const wrapper = ({ children }) => (
      <Provider store={solStore}>{ children }</Provider>
    );

    const { result } = renderHook(() => useRedirect('campaigns'), { wrapper });
    const hasAccess = result.current.redirectTo;
    expect(hasAccess).toStrictEqual('estore');
  });
});
