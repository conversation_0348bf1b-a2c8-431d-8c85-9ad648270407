/* eslint-disable react/prop-types */
import React from 'react';
import { renderHook } from '@testing-library/react-hooks';
import { Provider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { useTeams } from './useTeams';

describe('use teams ', () => {
  const mockTeams = {
    1: {
      id: 1,
      name: 'Pigeon Team',
      description: 'Team added at DB implementation',
      active: true,
      ownedApplications: [
        'SOL',
        'eExperience Storefront',
        'Nova Mobile',
        'Phoenix',
        'ABM',
        'Hubble',
        'Starburst',
        'NEO',
        'Orion',
      ],
      permissionLevels: [
        'admin',
        'alerts_approve',
        'alerts_create',
        'alerts_delete',
        'alerts_manage',
        'alerts_update',
        'users_manage',
        'users_manage_super',
        'users_update',
        'users_view',
        'users_view_super',
        'variable_mappings_approve',
        'teams_manage',
        'teams_manage_super',
        'teams_view',
        'canViewAllTeams',
        'users_create',
        'users_delete',
        'roles_manage_super',
        'roles_update',
        'roles_view',
        'roles_view_super',
        'sol-campaigns_view',
        'storefront-campaigns_view',
        'pega_variable_mapping_manage',
        'pega_variable_mapping_review',
        'pega_variable_mapping_view',
        'roles_create',
        'roles_delete',
        'roles_manage',
        'kt_variable_mapping_view',
        'pages_manage',
        'pages_manage_super',
        'pages_view',
        'pages_view_super',
        'pega_variable_mapping_approve',
        'containers_manage_super',
        'containers_view',
        'containers_view_super',
        'kt_variable_mapping_approve',
        'kt_variable_mapping_manage',
        'kt_variable_mapping_review',
        'campaigns_update_status_reviewed_draft',
        'campaigns_update_status_reviewed_published',
        'campaigns_update_status_submitted_draft',
        'campaigns_update_status_submitted_reviewed',
        'campaigns_view',
        'containers_manage',
        'campaigns_delete',
        'campaigns_manage',
        'campaigns_review',
        'campaigns_update_details',
        'campaigns_update_disable',
        'campaigns_update_status_draft_submitted',
        'applications_manage',
        'applications_manage_super',
        'applications_view',
        'applications_view_super',
        'campaigns_approve',
        'campaigns_create',
        'alerts_update_details',
        'alerts_update_disable',
        'alerts_update_status_draft_submitted',
        'alerts_update_status_submitted_draft',
        'alerts_update_status_submitted_published',
        'alerts_view',
      ],
      ownerRoleId: 1,
      access: {
        containers: [],
        ruleSubTypes: [],
        pages: [],
      },
      accessApplicationIds: [],
    },
    2: {
      id: 2,
      name: 'SOL',
      description: 'SOL',
      active: true,
      ownedApplications: [],
      permissionLevels: [
        'alerts_approve',
        'alerts_manage',
        'alerts_view',
        'applications_manage_super',
        'applications_view_super',
        'campaigns_approve',
        'canViewAllTeams',
        'users_manage',
        'users_manage_super',
        'users_view',
        'users_view_super',
        'roles_manage_super',
        'roles_view',
        'roles_view_super',
        'teams_manage',
        'teams_manage_super',
        'teams_view',
        'pages_view_super',
        'pega_variable_mapping_approve',
        'pega_variable_mapping_manage',
        'pega_variable_mapping_review',
        'pega_variable_mapping_view',
        'roles_manage',
        'campaigns_manage',
        'campaigns_review',
        'campaigns_view',
        'containers_manage_super',
        'containers_view_super',
        'pages_manage_super',
      ],
      owners: [
        {
          name: 'sol member',
          email: '<EMAIL>',
          sid: 's2607442',
          active: true,
        },
      ],
      access: {
        containers: [
          {
            application_id: 3,
            rule_type_id: 1,
            container_id: 1,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 1,
            container_id: 1,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 1,
            container_id: 2,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 1,
            container_id: 2,
            access: 'manage',
          },
          {
            application_id: 4,
            rule_type_id: 1,
            container_id: 3,
            access: 'view',
          },
          {
            application_id: 4,
            rule_type_id: 1,
            container_id: 3,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            container_id: 4,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            container_id: 4,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            container_id: 5,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            container_id: 5,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            container_id: 6,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            container_id: 6,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            container_id: 7,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            container_id: 7,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            container_id: 8,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            container_id: 8,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            container_id: 9,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            container_id: 9,
            access: 'manage',
          },
          {
            application_id: 5,
            rule_type_id: 2,
            container_id: 12,
            access: 'view',
          },
          {
            application_id: 5,
            rule_type_id: 2,
            container_id: 12,
            access: 'manage',
          },
          {
            application_id: 5,
            rule_type_id: 2,
            container_id: 13,
            access: 'view',
          },
          {
            application_id: 5,
            rule_type_id: 2,
            container_id: 13,
            access: 'manage',
          },
          {
            application_id: 8,
            rule_type_id: 2,
            container_id: 11,
            access: 'view',
          },
          {
            application_id: 8,
            rule_type_id: 2,
            container_id: 11,
            access: 'manage',
          },
          {
            application_id: 1,
            rule_type_id: 3,
            container_id: 14,
            access: 'view',
          },
          {
            application_id: 1,
            rule_type_id: 3,
            container_id: 14,
            access: 'manage',
          },
          {
            application_id: 1,
            rule_type_id: 3,
            container_id: 15,
            access: 'view',
          },
          {
            application_id: 1,
            rule_type_id: 3,
            container_id: 15,
            access: 'manage',
          },
          {
            application_id: 1,
            rule_type_id: 3,
            container_id: 16,
            access: 'view',
          },
          {
            application_id: 1,
            rule_type_id: 3,
            container_id: 16,
            access: 'manage',
          },
          {
            application_id: 2,
            rule_type_id: 4,
            container_id: 17,
            access: 'view',
          },
          {
            application_id: 2,
            rule_type_id: 4,
            container_id: 17,
            access: 'manage',
          },
        ],
        ruleSubTypes: [
          {
            application_id: 3,
            rule_type_id: 2,
            rule_sub_type_id: 1,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            rule_sub_type_id: 1,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            rule_sub_type_id: 2,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            rule_sub_type_id: 2,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            rule_sub_type_id: 3,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            rule_sub_type_id: 3,
            access: 'manage',
          },
          {
            application_id: 5,
            rule_type_id: 2,
            rule_sub_type_id: 1,
            access: 'view',
          },
          {
            application_id: 5,
            rule_type_id: 2,
            rule_sub_type_id: 1,
            access: 'manage',
          },
          {
            application_id: 7,
            rule_type_id: 2,
            rule_sub_type_id: 3,
            access: 'view',
          },
          {
            application_id: 7,
            rule_type_id: 2,
            rule_sub_type_id: 3,
            access: 'manage',
          },
          {
            application_id: 8,
            rule_type_id: 2,
            rule_sub_type_id: 3,
            access: 'view',
          },
          {
            application_id: 8,
            rule_type_id: 2,
            rule_sub_type_id: 3,
            access: 'manage',
          },
          {
            application_id: 1,
            rule_type_id: 3,
            rule_sub_type_id: 1,
            access: 'view',
          },
          {
            application_id: 1,
            rule_type_id: 3,
            rule_sub_type_id: 1,
            access: 'manage',
          },
          {
            application_id: 1,
            rule_type_id: 3,
            rule_sub_type_id: 2,
            access: 'view',
          },
          {
            application_id: 1,
            rule_type_id: 3,
            rule_sub_type_id: 2,
            access: 'manage',
          },
          {
            application_id: 2,
            rule_type_id: 4,
            rule_sub_type_id: 1,
            access: 'view',
          },
          {
            application_id: 2,
            rule_type_id: 4,
            rule_sub_type_id: 1,
            access: 'manage',
          },
          {
            application_id: 2,
            rule_type_id: 4,
            rule_sub_type_id: 2,
            access: 'view',
          },
          {
            application_id: 2,
            rule_type_id: 4,
            rule_sub_type_id: 2,
            access: 'manage',
          },
        ],
        pages: [
          {
            application_id: 3,
            rule_type_id: 1,
            page_id: 2,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 1,
            page_id: 2,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 1,
            page_id: 3,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 1,
            page_id: 3,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 1,
            page_id: 4,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 1,
            page_id: 4,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 1,
            page_id: 5,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 1,
            page_id: 5,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 1,
            page_id: 6,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 1,
            page_id: 6,
            access: 'manage',
          },
          {
            application_id: 4,
            rule_type_id: 1,
            page_id: 1,
            access: 'view',
          },
          {
            application_id: 4,
            rule_type_id: 1,
            page_id: 1,
            access: 'manage',
          },
          {
            application_id: 7,
            rule_type_id: 1,
            page_id: 7,
            access: 'view',
          },
          {
            application_id: 7,
            rule_type_id: 1,
            page_id: 7,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            page_id: 2,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            page_id: 2,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            page_id: 3,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            page_id: 3,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            page_id: 4,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            page_id: 4,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            page_id: 5,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            page_id: 5,
            access: 'manage',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            page_id: 6,
            access: 'view',
          },
          {
            application_id: 3,
            rule_type_id: 2,
            page_id: 6,
            access: 'manage',
          },
          {
            application_id: 5,
            rule_type_id: 2,
            page_id: 9,
            access: 'view',
          },
          {
            application_id: 5,
            rule_type_id: 2,
            page_id: 9,
            access: 'manage',
          },
          {
            application_id: 7,
            rule_type_id: 2,
            page_id: 7,
            access: 'view',
          },
          {
            application_id: 7,
            rule_type_id: 2,
            page_id: 7,
            access: 'manage',
          },
          {
            application_id: 8,
            rule_type_id: 2,
            page_id: 8,
            access: 'view',
          },
          {
            application_id: 8,
            rule_type_id: 2,
            page_id: 8,
            access: 'manage',
          },
          {
            application_id: 1,
            rule_type_id: 3,
            page_id: 10,
            access: 'view',
          },
          {
            application_id: 1,
            rule_type_id: 3,
            page_id: 10,
            access: 'manage',
          },
          {
            application_id: 1,
            rule_type_id: 3,
            page_id: 11,
            access: 'view',
          },
          {
            application_id: 1,
            rule_type_id: 3,
            page_id: 11,
            access: 'manage',
          },
          {
            application_id: 2,
            rule_type_id: 4,
            page_id: 12,
            access: 'view',
          },
          {
            application_id: 2,
            rule_type_id: 4,
            page_id: 12,
            access: 'manage',
          },
          {
            application_id: 2,
            rule_type_id: 4,
            page_id: 13,
            access: 'view',
          },
          {
            application_id: 2,
            rule_type_id: 4,
            page_id: 13,
            access: 'manage',
          },
        ],
      },
      accessApplicationIds: [ 3, 4, 5, 8, 1, 2, 7 ],
    },
  };

  const mockUsers = {
    1: {
      id: 1,
      name: 'Donovan Palma',
      email: '<EMAIL>',
      sid: 's7435292',
      active: true,
      team_id: 1,
      roles: [ 1, 5 ],
    },
    2: {
      id: 2,
      name: 'Alison Findlay',
      email: '<EMAIL>',
      sid: 's1278623',
      active: true,
      team_id: 1,
      roles: [ 1, 5, 6 ],
    },
    3: {
      id: 3,
      name: 'Bruce Kirkey',
      email: '<EMAIL>',
      sid: 's3894179',
      active: true,
      team_id: 1,
      roles: [ 1, 5, 6 ],
    },
    4: {
      id: 4,
      name: 'Alex',
      email: '<EMAIL>',
      sid: 's5136141',
      active: true,
      team_id: 1,
      roles: [ 1, 5 ],
    },
    5: {
      id: 5,
      name: 'Tony Wang Mina',
      email: '<EMAIL>',
      sid: 's1990028',
      active: true,
      team_id: 1,
      roles: [ 1, 8 ],
    },
    6: {
      id: 6,
      name: 'Mina Makar',
      email: '<EMAIL>',
      sid: 's7459156',
      active: true,
      team_id: 1,
      roles: [ 1, 5 ],
    },
    7: {
      id: 7,
      name: 'Ankit Awasthi',
      email: '<EMAIL>',
      sid: 's1838652',
      active: true,
      team_id: 1,
      roles: [ 1, 5 ],
    },
    8: {
      id: 8,
      name: 'Feriyal',
      email: '<EMAIL>',
      sid: 's6821653',
      active: true,
      team_id: 1,
      roles: [ 1, 5 ],
    },
    9: {
      id: 9,
      name: 'Test1',
      email: '<EMAIL>',
      sid: 's9999999',
      active: true,
      team_id: 1,
      roles: [ 1, 5 ],
    },
    10: {
      id: 10,
      name: 'Mike Haas',
      email: '<EMAIL>',
      sid: 's5891700',
      active: true,
      team_id: 1,
      roles: [ 1, 5 ],
    },
    11: {
      id: 11,
      name: 'Sahil Marwaha',
      email: '<EMAIL>',
      sid: 's3738290',
      active: true,
      team_id: 1,
      roles: [ 1, 5 ],
    },
    12: {
      id: 12,
      name: 'Holly McLean',
      email: '<EMAIL>',
      sid: 's7599382',
      active: true,
      team_id: 1,
      roles: [ 1, 5 ],
    },
    13: {
      id: 13,
      name: 'Anthony Brennan',
      email: '<EMAIL>',
      sid: 's7596102',
      active: true,
      team_id: 1,
      roles: [ 1, 5 ],
    },
    14: {
      id: 14,
      name: 'Nithin Gubbala',
      email: '<EMAIL>',
      sid: 's4602461',
      active: true,
      team_id: 1,
      roles: [ 1, 5, 6 ],
    },
    15: {
      id: 15,
      name: 'Allyson yang',
      email: '<EMAIL>',
      sid: 's6115460',
      active: true,
      team_id: 1,
      roles: [ 1, 5, 6 ],
    },
    16: {
      id: 16,
      name: 'no access',
      email: '<EMAIL>',
      sid: 's123456',
      active: true,
      team_id: 1,
      roles: [ 5 ],
    },
    17: {
      id: 17,
      name: 'sol member',
      email: '<EMAIL>',
      sid: 's2607442',
      active: true,
      team_id: 2,
      roles: [ 7, 8 ],
    },
  };

  const mockApplications = {
    '1': {
        'id': 1,
        'name': 'SOL',
        'description': null,
        'applicationId': 'sol',
        'status': true,
        'rule_version': 2,
        'contentful_space': '4szkx38resvm',
        'team_id': 1,
        'platforms': [
            'Web',
        ],
        'platformIds': [
            1,
        ],
        'ruleTypeIds': [
            3,
            5,
            6,
        ],
        'ruleTypes': [
            'vignette',
            'SOL Priority Campaign (Vignette)',
            'SOL Broadcast Campaign (Vignette)',
        ],
        'ruleSubTypeIds': [
            1,
            2,
        ],
        'ruleSubTypes': [
            'targeted',
            'mass',
        ],
        'team': [
            'Pigeon Team',
        ],
    },
    '2': {
        'id': 2,
        'name': 'eExperience Storefront',
        'description': null,
        'applicationId': 'storefront',
        'status': true,
        'rule_version': 2,
        'contentful_space': '4szkx38resvm',
        'team_id': 1,
        'platforms': [
            'Web',
        ],
        'platformIds': [
            1,
        ],
        'ruleTypeIds': [
            4,
        ],
        'ruleTypes': [
            'estore',
        ],
        'ruleSubTypeIds': [
            1,
            2,
        ],
        'ruleSubTypes': [
            'targeted',
            'mass',
        ],
        'team': [
            'Pigeon Team',
        ],
    },
    '3': {
        'id': 3,
        'name': 'Nova Mobile',
        'description': 'Nova',
        'applicationId': 'nova',
        'status': true,
        'rule_version': 1,
        'contentful_space': '4szkx38resvm',
        'team_id': 1,
        'platforms': [
            'iOS',
            'Android',
        ],
        'platformIds': [
            2,
            3,
        ],
        'ruleTypeIds': [
            1,
            2,
        ],
        'ruleTypes': [
            'alert',
            'campaign',
        ],
        'ruleSubTypeIds': [
            1,
            2,
            3,
        ],
        'ruleSubTypes': [
            'targeted',
            'mass',
            'message',
        ],
        'team': [
            'Pigeon Team',
        ],
    },
    '4': {
        'id': 4,
        'name': 'Phoenix',
        'description': 'Phoenix Login Web App',
        'applicationId': 'phoenix',
        'status': true,
        'rule_version': 1,
        'contentful_space': '4szkx38resvm',
        'team_id': 1,
        'platforms': [
            'Web',
        ],
        'platformIds': [
            1,
        ],
        'ruleTypeIds': [
            1,
        ],
        'ruleTypes': [
            'alert',
        ],
        'ruleSubTypeIds': [
            1,
            2,
            3,
        ],
        'ruleSubTypes': [
            'targeted',
            'mass',
            'message',
        ],
        'team': [
            'Pigeon Team',
        ],
    },
    '5': {
        'id': 5,
        'name': 'ABM',
        'description': 'ABM',
        'applicationId': 'abm',
        'status': true,
        'rule_version': 1,
        'contentful_space': '4szkx38resvm',
        'team_id': 1,
        'platforms': [
            'Web',
        ],
        'platformIds': [
            1,
        ],
        'ruleTypeIds': [
            2,
        ],
        'ruleTypes': [
            'campaign',
        ],
        'ruleSubTypeIds': [
            1,
        ],
        'ruleSubTypes': [
            'targeted',
        ],
        'team': [
            'Pigeon Team',
        ],
    },
    '6': {
        'id': 6,
        'name': 'Hubble',
        'description': 'Hubble Web App',
        'applicationId': 'hubble',
        'status': true,
        'rule_version': 1,
        'contentful_space': '4szkx38resvm',
        'team_id': 1,
        'platforms': [
            'Web',
        ],
        'platformIds': [
            1,
        ],
        'ruleTypeIds': [
            2,
        ],
        'ruleTypes': [
            'campaign',
        ],
        'ruleSubTypeIds': [
            1,
        ],
        'ruleSubTypes': [
            'targeted',
        ],
        'team': [
            'Pigeon Team',
        ],
    },
    '7': {
        'id': 7,
        'name': 'Starburst',
        'description': 'iTrade Mobile Application',
        'applicationId': 'starburst',
        'status': true,
        'rule_version': 1,
        'contentful_space': '4szkx38resvm',
        'team_id': 1,
        'platforms': [
            'iOS',
            'Android',
        ],
        'platformIds': [
            2,
            3,
        ],
        'ruleTypeIds': [
            1,
            2,
        ],
        'ruleTypes': [
            'alert',
            'campaign',
        ],
        'ruleSubTypeIds': [
            3,
        ],
        'ruleSubTypes': [
            'message',
        ],
        'team': [
            'Pigeon Team',
        ],
    },
    '8': {
        'id': 8,
        'name': 'NEO',
        'description': 'Neo Web Application',
        'applicationId': 'neo',
        'status': true,
        'rule_version': 1,
        'contentful_space': '4szkx38resvm',
        'team_id': 1,
        'platforms': [
            'Web',
        ],
        'platformIds': [
            1,
        ],
        'ruleTypeIds': [
            2,
        ],
        'ruleTypes': [
            'campaign',
        ],
        'ruleSubTypeIds': [
            3,
        ],
        'ruleSubTypes': [
            'message',
        ],
        'team': [
            'Pigeon Team',
        ],
    },
    '9': {
        'id': 9,
        'name': 'Orion',
        'description': 'Orion Web Application',
        'applicationId': 'orion',
        'status': true,
        'rule_version': 1,
        'contentful_space': '4szkx38resvm',
        'team_id': 1,
        'platforms': [
            'Web',
        ],
        'platformIds': [
            1,
        ],
        'ruleTypeIds': [
            2,
        ],
        'ruleTypes': [
            'campaign',
        ],
        'ruleSubTypeIds': [
            1,
            2,
            3,
        ],
        'ruleSubTypes': [
            'targeted',
            'mass',
            'message',
        ],
        'team': [
            'Pigeon Team',
        ],
    },
  };

  const mockRuleTypes = [
    { 'id': 1, 'rule_type': 'alert', 'slug': 'alert' },
    { 'id': 2, 'rule_type': 'campaign', 'slug': 'campaign' },
    { 'id': 3, 'rule_type': 'vignette', 'slug': 'vignette' },
    { 'id': 4, 'rule_type': 'estore', 'slug': 'estore' },
    { 'id': 5, 'rule_type': 'SOL Priority Campaign (Vignette)', 'slug': 'vignette-priority' },
    { 'id': 6, 'rule_type': 'SOL Broadcast Campaign (Vignette)', 'slug': 'vignette-broadcast' },
  ];

  test.skip('Should return all teams ', () => {
    const mockStore = configureMockStore([ thunk ]);
    const solStore = mockStore({
      users: { items: mockUsers },
      teams: { items: mockTeams },
      applications: { items: mockApplications },
      ruleTypes: { items: mockRuleTypes },
      authenticated: { permissionLevels: [ { canViewAllTeams: true } ] },
    });

    const wrapper = ({ children }) => (
      <Provider store={solStore}>{ children }</Provider>
    );

    const { result } = renderHook(() => useTeams({}), { wrapper });
    expect(result.current.teams[0].id).toStrictEqual(1);
  });

  test.skip('Should return empty array', () => {
    const mockStore = configureMockStore([ thunk ]);
    const solStore = mockStore({
      users: { items: null },
      teams: { items: null },
      applications: { items: null },
      ruleTypes: { items: null },
      authenticated: { },
    });

    const wrapper = ({ children }) => (
      <Provider store={solStore}>{ children }</Provider>
    );

    const { result } = renderHook(() => useTeams({}), { wrapper });
    expect(result.current.teams.length).toStrictEqual(0);
  });
});
