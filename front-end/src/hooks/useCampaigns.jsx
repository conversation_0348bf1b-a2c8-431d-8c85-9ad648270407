import { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import { getMessageCentreCampaigns } from '../store/actions/message-centre-campaigns';

export const useCampaigns = (filters) => {
  const dispatch = useDispatch();
  const {
    messageCentreCampaigns: {
      items: campaigns,
      pagination,
      isLoading: campaignsLoading,
      isSetCampaignActiveLoading,
    },
    authenticated: currentUser,
  } = useSelector((state) => state);

  useEffect(() => {
    const campaignFilters = { ...filters };
    const date = new Date();
    const now = date.toISOString();
    if (campaignFilters.status === 'N') {
      campaignFilters.end_date_gt = now;
    } else if (campaignFilters.status === 'E') {
      campaignFilters.status = 'N';
      campaignFilters.end_date = now;
    }
    dispatch(getMessageCentreCampaigns(campaignFilters));
  }, [ filters ]);

  return {
    campaignsLoading,
    isSetCampaignActiveLoading,
    campaigns,
    pagination,
    currentUser,
  };
};
