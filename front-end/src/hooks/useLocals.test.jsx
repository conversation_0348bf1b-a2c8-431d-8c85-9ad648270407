import { renderHook } from '@testing-library/react-hooks';
import { useLocals } from './useLocals';
import mockAxios from 'axios';

const mockApplications = { '1': { 'applicationId': 1, 'contentful_space': '4szkx38resvm' } };

const mockLocalesRes = {
    data: {
        total: 2,
        items: [
            { code: 'en-US', name: 'English (United States)', default: true },
            { code: 'fr', name: 'French' },
        ],
    },
    notifications: [],
};

describe('use locals ', () => {
    beforeEach(() => {
        mockAxios.reset();
    });

    test('Should return all languages ', async() => {
        mockAxios.get.mockImplementation(url => Promise.resolve(mockLocalesRes));
        const { result, waitForNextUpdate } = renderHook(() => useLocals({ selectedApp: 1, applications: mockApplications }));
        await waitForNextUpdate();
        expect(result.current.languages.languagesCode).toStrictEqual([ 'en', 'fr' ]);
    });

    test('Should return all languagesCode ', async() => {
        mockAxios.get.mockImplementation(url => Promise.resolve(mockLocalesRes));
        const mockOnGetLocals = jest.fn();
        const { result, waitForNextUpdate } = renderHook(() => useLocals({ selectedApp: 1, applications: mockApplications, onGetLocals: mockOnGetLocals }));
        await waitForNextUpdate();
        expect(result.current.languages.languagesCode).toStrictEqual([ 'en', 'fr' ]);
        expect(mockOnGetLocals).toHaveBeenCalled();
    });
    test('Should throw error ', () => {
        mockAxios.get.mockImplementation(url => Promise.reject(new Error('Something went wrong')));
        const { result } = renderHook(() => useLocals({ selectedApp: 1, applications: mockApplications }));
        expect(result.current.languages.languagesCode).toStrictEqual([]);
    });
});
