import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { canViewManageRuleType } from '../components/rules/utils';

export const useRedirect = (url = '') => {
  const {
    authenticated: { permissions, access },
  } = useSelector((state) => state);

  const [ redirectTo, setRedirectTo ] = useState('');

  const priorityUrls = [ 'campaigns', 'ccau_campaigns', 'sol', 'estore', 'alerts' ];

  const checkIfUserHasAccessToUrl = (targetUrl) => {
    const { viewCampaigns, viewCCAUCampaigns, viewSOL, viewStoreFront, viewAlert } = canViewManageRuleType(access, permissions);
    switch (targetUrl) {
      case 'campaigns':
        return viewCampaigns;
      case 'ccau_campaigns':
        return viewCCAUCampaigns;
      case 'sol':
        return viewSOL;
      case 'estore':
          return viewStoreFront;
      case 'alerts':
        return viewAlert;
    }
  };

  const shouldRedirectTo = () => {
    return priorityUrls.find(url => checkIfUserHasAccessToUrl(url));
  };

  useEffect(() => {
    if (!access || !permissions) return;
    if (url) {
      return setRedirectTo(checkIfUserHasAccessToUrl(url) ? false : shouldRedirectTo());
    } else {
      return setRedirectTo(shouldRedirectTo());
    }
  }, []);

  return {
    redirectTo,
  };
};

export default useRedirect;
