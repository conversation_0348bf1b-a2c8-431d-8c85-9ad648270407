import { useSelector } from 'react-redux';
import { mapObjectToArray } from '../constants';
import { groupBy, flattenDeep } from 'lodash';
import { permissions } from '../constants/permissionsList';

const placementsOptions = [
  {
    label: 'Applications (own application)',
    id: 1,
    disabled: {
      view: true,
      manage: true,
    },
    permission: permissions.placement.applications,
  },
  {
    label: 'Applications (all applications)',
    id: 2,
    permission: permissions.placement.superApplications,
  },
  {
    label: 'Pages (own application)',
    id: 3,
    disabled: {
      view: true,
      manage: true,
    },
    permission: permissions.placement.pages,
  },
  {
    label: 'Pages (all applications)',
    id: 4,
    permission: permissions.placement.superPages,
  },
  {
    label: 'Containers (own application)',
    id: 5,
    disabled: {
      view: true,
      manage: true,
    },
    permission: permissions.placement.containers,
  },
  {
    label: 'Containers (all applications)',
    id: 6,
    permission: permissions.placement.superContainers,
  },
];

const accessesOptions = [
  {
    label: 'Teams (own team)',
    id: 1,
    permission: permissions.accessControl.teams,
    disabled: {
      view: false,
      manage: false,
    },
  },
  {
    label: 'Teams (all teams)',
    id: 2,
    permission: permissions.accessControl.superTeams,
    disabled: {
      view: false,
      manage: true,
    },
  },
  {
    label: 'Users (own team)',
    id: 3,
    disabled: {
      view: true,
      manage: true,
    },
    permission: permissions.accessControl.users,
  },
  {
    label: 'Users (all teams)',
    id: 4,
    permission: permissions.accessControl.superUsers,
  },
  {
    label: 'Roles (own team)',
    id: 5,
    disabled: {
      view: true,
      manage: true,
    },
    permission: permissions.accessControl.roles,
  },
  {
    label: 'Roles (all teams)',
    id: 6,
    permission: permissions.accessControl.superRoles,
  },
];

const variableMappingOptions = [
  {
    label: 'Pega',
    id: 1,
    permission: permissions.variableMapping.pega,
  },
];

const messageCentreOptions = [
  {
    label: 'Campaigns',
    id: 1,
    permission: permissions.messageCentre.campaigns,
  },
  {
    label: 'Messages',
    id: 2,
    permission: permissions.messageCentre.messages,
  },
];

const offerManagementOptions = [
  {
    label: 'Offers',
    id: 1,
    permission: permissions.offers,
  },
];

const defaultGroup = {
  type: 'group',
  expanded: false,
  checked: {
    view: false,
    manage: false,
  },
  disabled: {
    view: false,
    manage: false,
  },
  children: [],
};

const defaultItem = {
  type: 'item',
  checked: {
    view: false,
    manage: false,
  },
  disabled: {
    view: false,
    manage: false,
  },
};

export const useManageData = ({
  initialSelectionsAccess,
  initialSelectionsPermissions,
}) => {
  const {
    applications: { items: applications, isLoading: appLoading },
    pages: { items: pages, isLoading: pagesLoading },
    containers: { items: containers, isLoading: containersLoading },
    ruleSubTypes: { items: ruleSubTypes, isLoading: ruleSubTypesLoading },
    ruleTypes: { items: ruleTypes, isLoading: ruleTypesLoading },
  } = useSelector((state) => state);

  const getInitialChecked = ({ key, itemKey, id, app, ruleTypeId }) => {
    const accessView = initialSelectionsAccess[key]
      ? !!initialSelectionsAccess[key].find(
        (p) =>
          p.rule_type_id === ruleTypeId &&
            p.application_id === app.id &&
            (p.access === 'view' || p.access === 'manage') &&
            p[itemKey] === id
      )
      : false;
    const accessManage = initialSelectionsAccess[key]
      ? !!initialSelectionsAccess[key].find(
        (p) =>
          p.rule_type_id === ruleTypeId &&
            p.application_id === app.id &&
            p.access === 'manage' &&
            p[itemKey] === id
      )
      : false;

    return {
      view: accessView,
      manage: accessManage,
    };
  };

  const mapPages = groupBy(pages, 'application');
  const mapContainers = groupBy(containers, 'application');

  const filterCampaigns = (app) => {
    // includes campaign, ccau_campaign , vignette, storefront etc.
    return app.ruleTypes.some(ruleType => ruleType !== 'alert');
  };

  const filterAlerts = (app) => {
    return app.ruleTypes.includes('alert');
  };

  // This function returns the tree structure for Campaigns Tab
  const mapCampaigns = (app) => {
    if (pagesLoading) return false;
    const filteredContainer = (mapContainers[app.id] ? mapContainers[app.id] : []).filter((c) => c.rule_type !== 'alert');
    const filtedPages = filteredContainer.length ? [ ...new Set(flattenDeep(filteredContainer.map(c => c.pages))) ] : [];
    const ruleType = app.rule_version === 1
    ? ruleTypes?.find((rule) => rule.rule_type === app.ruleTypes.find(r => r === 'campaign' || r === 'ccau_campaign'))
    : ruleTypes?.find((rule) => rule.rule_type === app.ruleTypes[0]);

    // pages under campaigns
    const appPages = {
      ...defaultGroup,
      label: 'Pages',
      id: `campaigns-${app?.id}-pages`,
      children: filtedPages.map((pageId) => {
        const page = mapPages[app?.id] ? mapPages[app.id].find(p => p.id === pageId) : {};
        if (!page) return false;
        const checked = getInitialChecked({
          key: 'pages',
          itemKey: 'page_id',
          id: page?.id,
          app,
          ruleTypeId: ruleType?.id,
        });
        return {
          ...defaultItem,
          label: page?.name,
          id: `campaigns-${app?.id}-pages-${page?.id}`,
          data: {
            page_id: page?.id,
            application_id: page?.application,
            rule_type_id: ruleType?.id,
            dataType: 'pages',
          },
          checked,
        };
      }).filter(_ => !!_),
    };

    // container under campaigns
    const appContainers = {
      ...defaultGroup,
      label: 'Containers',
      id: `campaigns-${app.id}-containers`,
      children: (mapContainers[app.id] ? mapContainers[app.id] : [])
        .filter((c) => c.rule_type !== 'alert')
        .map((container) => {
          const checked = getInitialChecked({
            key: 'containers',
            itemKey: 'container_id',
            id: container.id,
            app,
            ruleTypeId: ruleType?.id,
          });
          return {
            ...defaultItem,
            label: container.name,
            id: `campaigns-${app.id}-containers-${container.id}`,
            data: {
              application_id: container.application,
              container_id: container.id,
              rule_type_id: ruleType?.id,
              dataType: 'containers',
            },
            checked,
          };
        }),
    };

    // campaign rule subtypes (targeted, mass, message)
    const ruleSubTypesApp = app.ruleSubTypeIds.map((a) => {
      const ruleSubType = ruleSubTypes?.find((r) => r.id === a);
      return {
        id: a,
        description: ruleSubType?.description,
      };
    });

    const appCampaignTypes = {
      ...defaultGroup,
      label: 'Campaign type',
      id: `campaigns-${app.id}-campaign-type`,
      children: ruleSubTypesApp?.map((r) => {
        const checked = getInitialChecked({
          key: 'ruleSubTypes',
          itemKey: 'rule_sub_type_id',
          id: r.id,
          app,
          ruleTypeId: ruleType?.id,
        });
        return {
          ...defaultItem,
          label: r.description,
          id: 'campaigns-' + app.id + '-campaign-type-' + r.description,
          data: {
            rule_sub_type_id: r.id,
            rule_type_id: ruleType?.id,
            application_id: app.id,
            dataType: 'ruleSubTypes',
          },
          checked,
        };
      }),
    };

    const application = {
      ...defaultGroup,
      label: `${app.name} Campaigns`,
      id: `campaigns-${app.id}`,
    };

    if (appContainers.children.length) {
      application.children = [ ...application.children, appContainers ];
    }
    if (appPages.children.length) {
      application.children = [ ...application.children, appPages ];
    }
    if (appContainers.children.length || appPages.children.length) {
      application.children = [ ...application.children, appCampaignTypes ];
      return application;
    }
    return false;
  };

  // This function returns the tree structure for Alerts Tab
  const mapAlerts = (app) => {
    if (pagesLoading) return false;
    const filteredContainer = (mapContainers[app.id] ? mapContainers[app.id] : []).filter((c) => c.rule_type === 'alert');
    const filtedPages = filteredContainer.length ? [ ...new Set(flattenDeep(filteredContainer.map(c => c.pages))) ] : [];
    const ruleType = ruleTypes?.find((rule) => rule.rule_type === 'alert');

    // pages under alerts
    const appPages = {
      ...defaultGroup,
      label: 'Pages',
      id: `alerts-${app?.id}-pages`,
      children: filtedPages.map((pageId) => {
        const page = mapPages[app?.id] ? mapPages[app.id].find(p => p.id === pageId) : {};
        const checked = getInitialChecked({
          key: 'pages',
          itemKey: 'page_id',
          id: page?.id,
          app,
          ruleTypeId: ruleType.id,
        });

        return {
          ...defaultItem,
          label: page?.name,
          id: `alerts-${app?.id}-pages-${page?.id}`,
          data: {
            rule_type_id: ruleType?.id,
            page_id: page?.id,
            application_id: page?.application,
            dataType: 'pages',
          },
          checked,
        };
      }),
    };
    // conatiner under alerts
    const appContainers = {
      ...defaultGroup,
      label: 'Containers',
      id: `alerts-${app.id}-containers`,
      children: (mapContainers[app.id] ? mapContainers[app.id] : [])
        .filter((c) => c.rule_type === 'alert')
        .map((container) => {
          const checked = getInitialChecked({
            key: 'containers',
            itemKey: 'container_id',
            id: container.id,
            app,
            ruleTypeId: ruleType.id,
          });

          return {
            ...defaultItem,
            label: container.name,
            id: `alerts-${app.id}-containers-${container.id}`,
            data: {
              application_id: container.application,
              container_id: container.id,
              rule_type_id: ruleType?.id,
              dataType: 'containers',
            },
            checked,
          };
        }),
    };
    const application = {
      ...defaultGroup,
      label: `${app.name} alerts`,
      id: `alerts-${app.id}`,
    };

    if (appContainers.children.length) {
      application.children = [ ...application.children, appContainers ];
    }
    if (appPages.children.length) {
      application.children = [ ...application.children, appPages ];
    }
    if (appContainers.children.length || appPages.children.length) {
      return application;
    }
    return false;
  };

  const mapPlacements = (placement) => {
    return {
      type: 'item',
      label: placement.label,
      id: `placements-${placement.id}`,
      disabled: placement.disabled
        ? placement.disabled
        : {
          view: false,
          manage: false,
        },
      checked: {
        view: initialSelectionsPermissions.includes(placement.permission.view),
        manage: initialSelectionsPermissions.includes(
          placement.permission.manage
        ),
      },
      data: {
        ...placement,
        dataType: 'permissions',
      },
    };
  };

  const mapAccesses = (access) => {
    return {
      type: 'item',
      label: access.label,
      id: `access-${access.id}`,
      disabled: access.disabled
        ? access.disabled
        : {
          view: false,
          manage: false,
        },
      checked: {
        view: initialSelectionsPermissions.includes(access.permission.view),
        manage: initialSelectionsPermissions.includes(access.permission.manage),
      },
      data: {
        ...access,
        dataType: 'permissions',
      },
    };
  };

  const mapVariableMapping = (variable) => {
    return {
      type: 'item',
      label: variable.label,
      id: `variable-${variable.id}`,
      disabled: {
        view: false,
        manage: false,
      },
      checked: {
        view: initialSelectionsPermissions.includes(variable.permission.view),
        manage: initialSelectionsPermissions.includes(
          variable.permission.manage
        ),
      },
      data: {
        ...variable,
        dataType: 'permissions',
      },
    };
  };

  const mapMessageCentreAccesses = (option) => {
    return {
      type: 'item',
      label: option.label,
      id: `message-centre-${option.id}`,
      disabled: {
        view: false,
        manage: false,
      },
      checked: {
        view: initialSelectionsPermissions.includes(option.permission.view),
        manage: initialSelectionsPermissions.includes(
          option.permission.manage
        ),
      },
      data: {
        ...option,
        dataType: 'permissions',
      },
    };
  };

  const mapOfferManagementAccesses = (option) => {
    return {
      type: 'item',
      label: option.label,
      id: `offers-${option.id}`,
      disabled: {
        view: false,
        manage: false,
      },
      checked: {
        view: initialSelectionsPermissions.includes(option.permission.view),
        manage: initialSelectionsPermissions.includes(
          option.permission.manage
        ),
      },
      data: {
        ...option,
        dataType: 'permissions',
      },
    };
  };

  return {
    campaigns: mapObjectToArray(applications || {})
      .filter(filterCampaigns)
      .map(mapCampaigns)
      .filter((c) => !!c),
    alerts: mapObjectToArray(applications || {})
      .filter(filterAlerts)
      .map(mapAlerts)
      .filter((a) => !!a),
    placements: placementsOptions.map(mapPlacements),
    accesses: accessesOptions.map(mapAccesses),
    variables: variableMappingOptions.map(mapVariableMapping),
    messageCentreAccesses: messageCentreOptions.map(mapMessageCentreAccesses),
    offerManagementAccesses: offerManagementOptions.map(mapOfferManagementAccesses),
    loading:
      appLoading ||
      pagesLoading ||
      containersLoading ||
      ruleSubTypesLoading ||
      ruleTypesLoading,
  };
};
