/* eslint-disable camelcase */
import { useEffect, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import { mapObjectToArray } from '../constants';
import { getApplications } from '../store/actions/applications';

export const useApplications = ({
  team_id,
  platform_id,
  rule_type_id,
  status,
  search,
  sort,
  limit,
  pageNumber,
}) => {
  const dispatch = useDispatch();
  const {
    applications: { items: applications, isLoading: applicationsLoading, pagination },
    teams: { items: teams, isLoading: teamsLoading },
    platforms: { items: platforms },
    ruleTypes: { items: ruleTypes },
    authenticated: currentUser,
  } = useSelector((state) => state);

  const loading = applicationsLoading;
  const applicationsList = useMemo(() => mapObjectToArray(applications || {}), [ applications ]);
  const platformsList = useMemo(() => mapObjectToArray(platforms || {}), [ platforms ]);
  const ruleTypesList = useMemo(() => mapObjectToArray(ruleTypes || {}), [ ruleTypes ]);
  const teamsList = useMemo(() => mapObjectToArray(teams || {}), [ teams ]);

  const { canViewAllApplications } = useMemo(() => currentUser.permissionLevels || {}, [ currentUser ]);

  useEffect(() => {
    dispatch(getApplications({
      team_id,
      platform_id,
      rule_type_id,
      status,
      search,
      sort,
      limit,
      pageNumber,
      ...(!canViewAllApplications && {
        team_id: currentUser.team_id,
      }),
    }));
  }, []);

  return {
    loading,
    teamsLoading,
    currentUser,
    teams: teamsList,
    applications: applicationsList,
    platforms: platformsList,
    ruleTypes: ruleTypesList,
    pagination,
  };
};
