import { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import { getMessageCentreMessages } from '../store/actions/message-centre-messages';

export const useMesssages = (filters) => {
  const dispatch = useDispatch();
  const {
    messageCentreMessages: {
      items: messages,
      pagination,
      isLoading: messagesLoading,
    },
    authenticated: currentUser,
  } = useSelector((state) => state);

  useEffect(() => {
    dispatch(getMessageCentreMessages(filters));
  }, [ filters ]);

  return {
    messagesLoading,
    messages,
    pagination,
    currentUser,
  };
};
