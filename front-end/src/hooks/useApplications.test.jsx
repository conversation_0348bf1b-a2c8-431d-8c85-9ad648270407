import React from 'react';
import { renderHook } from '@testing-library/react-hooks';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { Provider } from 'react-redux';
import { useApplications } from './useApplications';

describe('useApplications', () => {
    test('Should return empty state', () => {
        const mockStore = configureMockStore([ thunk ]);
        const store = mockStore(
       {
            applications: { items: undefined, isLoading: false, pagination: {} },
            teams: { items: undefined, isLoading: false },
            platforms: { items: undefined, isLoading: false },
            ruleTypes: { items: undefined, isLoading: false },
            authenticated: {},
        });

        // eslint-disable-next-line react/prop-types
        const wrapper = ({ children }) => (
            <Provider store={store}>{ children }</Provider>
        );

        const { result } = renderHook(() =>
            useApplications({
                team_id: 1,
                platform_id: 2,
                rule_type_id: 3,
                status: 'active',
                search: 'keyword',
                sort: 'name',
                limit: 10,
                pageNumber: 1,
            }),
            { wrapper }
        );

        expect(result.current.applications).toStrictEqual([]);
        expect(result.current.teams).toStrictEqual([]);
        expect(result.current.platforms).toStrictEqual([]);
        expect(result.current.currentUser).toStrictEqual({});
    });
});
