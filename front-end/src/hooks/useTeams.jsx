/* eslint-disable camelcase */
import { useEffect, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import { mapObjectToArray } from '../constants';
import { getTeams } from '../store/actions/teams';
import { getApplications } from '../store/actions/applications';
import { getUsers } from '../store/actions/users';
import { getRuleTypes } from '../store/actions/rule-types';

export const useTeams = ({
  application_id,
  functionality_id,
  owner_id,
  active,
  search,
  sort,
  limit,
  flag,
  pageNumber,
}) => {
  const dispatch = useDispatch();
  const {
    applications: { items: applications, isLoading: applicationsLoading },
    teams: { items: teams, isLoading: teamsLoading, pagination, teamOwners },
    users: { items: users, isLoading: usersLoading },
    ruleTypes: { items: ruleTypes },
    authenticated: currentUser,
  } = useSelector((state) => state);

  const loading = usersLoading || teamsLoading || applicationsLoading;
  const applicationsList = useMemo(() => mapObjectToArray(applications || {}), [ applications ]);
  const usersList = useMemo(() => mapObjectToArray(users || {}), [ users ]);
  const teamsList = useMemo(() => mapObjectToArray(teams || {}), [ teams ]);
  const ruleTypesList = useMemo(() => mapObjectToArray(ruleTypes || {}), [ ruleTypes ]);

  const { canViewAllTeams } = useMemo(() => currentUser.permissionLevels || {}, [ currentUser ]);

  useEffect(() => {
    dispatch(getApplications());
    dispatch(getUsers(!canViewAllTeams && {
      team_id: currentUser.team_id,
    }));
    dispatch(getTeams({
      application_id,
      functionality_id,
      owner_id,
      active,
      search,
      sort,
      limit,
      flag,
      pageNumber,
      ...(!canViewAllTeams && {
        id: currentUser.team_id,
      }),
    }));
    if (!ruleTypes) {
      dispatch(getRuleTypes());
    }
  }, []);

  const mapTeams = (team) => {
    const ownedApplications = applicationsList.filter(app => app.team_id === team.id).map(app => app.name);
    return {
      ...team,
      ownedApplications,
    };
  };

  const processedTeams = useMemo(
    () => teamsList.map(mapTeams),
    [ loading, teamsList, applicationsList ]
  );

  return {
    loading,
    teams: processedTeams,
    teamOwners,
    currentUser,
    applications: applicationsList,
    users: usersList,
    ruleTypes: ruleTypesList,
    pagination,
  };
};
