import { useEffect, useState } from 'react';
import { getLocales } from '../api/preview';
import { groupBy, split } from 'lodash';

export const useLocals = ({ applications, selectedApp, onGetLocals }) => {
    const [ languages, setLanguages ] = useState({ locals: [], languagesCode: [] });

    useEffect(() => {
        const fetchLocals = async() => {
            try {
                const { contentful_space: space } = Object.values(applications).find(a => a.applicationId === selectedApp);
                const { items: locals } = await getLocales({ space });
                const languagesOptions = groupBy(locals, ({ code }) => split(code, '-').at(0));
                onGetLocals && onGetLocals(languagesOptions);
                setLanguages({ locals, languagesCode: Object.keys(languagesOptions) });
            } catch (e) {
                console.warn('error while fetching locals');
            }
        };
        fetchLocals();
    }, [ selectedApp ]);
    return { languages };
};
