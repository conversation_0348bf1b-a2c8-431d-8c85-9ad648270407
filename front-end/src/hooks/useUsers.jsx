/* eslint-disable camelcase */
import { useEffect, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import { getTeams } from '../store/actions/teams';
import { getUsers } from '../store/actions/users';
import { getRoles } from '../store/actions/roles';
import { mapObjectToArray, sanitizeRoleName } from '../constants';
import { TEAMS_FLAGS } from '../constants/permissionsList';

export const useUsers = ({
  team_id,
  role_id,
  active,
  search,
  sort,
  limit,
  pageNumber,
}) => {
  const dispatch = useDispatch();
  const {
    users: { items: users, isLoading: usersLoading, pagination },
    roles: { items: roles, isLoading: rolesLoading },
    teams: { items: teams, isLoading: teamsLoading },
    authenticated: currentUser,
  } = useSelector((state) => state);

  const loading = usersLoading || rolesLoading || teamsLoading;
  const usersList = useMemo(() => mapObjectToArray(users || {}), [ users ]);
  const teamsList = useMemo(() => mapObjectToArray(teams || {}), [ teams ]);
  const rolesList = useMemo(() => mapObjectToArray(roles || {}), [ roles ]);

  const { canViewAllUsers, canEditAllUsers, canEditOwnTeamUsers } = useMemo(() => currentUser.permissionLevels || {}, [ currentUser ]);

  useEffect(() => {
    dispatch(getTeams({
      flag: TEAMS_FLAGS.SKIP_ALL_EXCEPT_TEAM_OWNERS,
      ...(!canViewAllUsers && {
        id: currentUser.team_id,
      }),
    }));
    dispatch(getUsers({
      team_id,
      role_id,
      active,
      search,
      sort,
      limit,
      pageNumber,
      ...(!canViewAllUsers && {
        team_id: currentUser.team_id,
      }),
    }));
    dispatch(getRoles({
      ...(!canViewAllUsers && {
        team_id: currentUser.team_id,
      }),
    }));
  }, []);

  const mapUsers = (user) => {
    // resolve team
    const team = teamsList.find(team => team.id === user.team_id);

    // resolve roles
    const roleNames = user.roles.map(roleId => {
      const role = rolesList.find(r => r.id === roleId);
      return sanitizeRoleName(role?.name);
    });

    // resolve status
    const teamOwnerRoleId = team?.ownerRoleId;
    const teamOwners = teamOwnerRoleId && usersList.filter(u => u.roles?.includes(teamOwnerRoleId));
    const isOnlyActiveTeamOwner = teamOwners?.length &&
      teamOwners.filter(owner => owner.active).length === 1 &&
      teamOwners.find(owner => owner.active).sid === user?.sid;
    const isTeammate = user.team_id === currentUser.team_id;
    const cantEdit = !(isTeammate && canEditOwnTeamUsers) && !canEditAllUsers;
    const status = {
      active: user.active,
      disabled: loading || cantEdit || isOnlyActiveTeamOwner || !team?.active,
    };
    return {
      id: user.id,
      name: user.name,
      sid: user.sid,
      teamActive: team?.active,
      teamName: team?.name,
      roleNames,
      status,
    };
  };

  const processedUsers = useMemo(
    () => usersList.map(mapUsers),
    [ loading, usersList, teamsList, rolesList ]
  );

  return {
    users: processedUsers,
    loading,
    roles: rolesList,
    teams: teamsList,
    pagination,
    currentUser,
  };
};
