import { useEffect, useState, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { mapObjectToArray } from '../constants';
import { getTeams } from '../store/actions/teams';
import { getRoles } from '../store/actions/roles';
import { addAlert } from '../store/actions/alertBanner';
import * as usersApi from '../api/users';

import { TEAMS_FLAGS } from '../constants/permissionsList';

export const useUser = ({ id = '', reset, watch, getValues, setValue }) => {
  const dispatch = useDispatch();
  /**
   * get All users, teams, roles and authenticated user from state ( redux )
   */
  const {
    teams: { items: teams, isLoading: teamsLoading },
    roles: { items: roles, isLoading: rolesLoading },
    authenticated: currentUser,
  } = useSelector((state) => state);

  const [ user, setUser ] = useState({});
  const [ userLoading, setUserLoading ] = useState(true);
  const [ isFormDisabled, setIsFormDisabled ] = useState(false);
  const [ isSoleTeamOwner, setIsSoleTeamOwner ] = useState(false);
  const [ isOnlyActiveTeamOwner, setIsOnlyActiveTeamOwner ] = useState(false);
  const [ disabledRoleIds, setDisabledRoleIds ] = useState([]);

  const { canViewAllUsers, canEditAllUsers, canEditOwnTeamUsers } = currentUser.permissionLevels || {};

  /**
   * get ALL teams and roles
   */
  useEffect(() => {
    dispatch(getTeams({
      flag: TEAMS_FLAGS.SKIP_ACCESS,
      ...(!canEditAllUsers && {
        id: currentUser.team_id,
      }),
    }));
    dispatch(getRoles({
      ...(!canEditAllUsers && !canViewAllUsers && {
        team_id: currentUser.team_id,
      }),
    }));
  }, []);

  const dataLoading = useMemo(() => {
    return rolesLoading || teamsLoading;
  }, [ teamsLoading, rolesLoading ]);

  useEffect(() => {
    if (!teams || !roles) return;

    /**
     * get the user by user id
     */
    const getUser = async() => {
      if (dataLoading) return;
      try {
        const user = await usersApi.getUser(id);
        setUser(user);
        // reset form values with user data
        reset(user);
        // get user's team - will use it to get the disabled roles .
        const userTeam = Object.values(teams || {}).find(
          (team) => team.id === Number(user.team_id)
        );

        // If userTeam is undefined then user may not have edit all permission and user details page needs to be disabled for eternal users
        if (!userTeam) setIsFormDisabled(true);

        // get team's role - will use it to get the disabled roles .
        const teamRoles = mapObjectToArray(roles || {}).filter(
          (role) => role.team_id === Number(user.team_id)
        );
        if (userTeam) {
        // disable viewer role + owner role incase this user is the only owner for this team
        const activeTeamMates = (await usersApi.getUsers({ active: true, teamId: user.team_id }))?.items || [];
        userTeam.owners = activeTeamMates.filter(u => u.roles.includes(userTeam.ownerRoleId));
        }
        getDisabledRolesId(userTeam, teamRoles);
        const editingOwnTeamMember = user.team_id === currentUser?.team_id;
        const canEditOwnTeam = canEditOwnTeamUsers && editingOwnTeamMember;
        setIsFormDisabled(!canEditAllUsers && !canEditOwnTeam);
      } catch (error) {
        dispatch(addAlert({ message: 'An error occurred loading this user.' }));
      } finally {
        setUserLoading(false);
      }
    };
    if (id) {
      getUser();
    } else {
      // Since the user doesn't have USERS_MANAGE_SUPER the team dropdown should be hidden and the team_id in the payload set to the current user's team_id
      if (!canEditAllUsers) {
        setValue('team_id', currentUser.team_id);
        handleChangeTeam(currentUser.team_id);
      }
      setUserLoading(false);
    }
    // flag to disable the form in case the current user ( logged in user ) doesn't have a permission to edit all users or own user under the current user's team
    if (!canEditOwnTeamUsers) {
      setIsFormDisabled(true);
    }
  }, [ dataLoading ]);

  /**
   *
   * @param {Team} team
   * @param {Role[]} teamRoles
   * @param {boolean} enableViewer  - this flag for select the viewer role ,it will be false when in the initial render
   */
  const getDisabledRolesId = (team, teamRoles, enableViewer = false) => {
    const disabledRoleIds = [];

    // Auto select viewer role
    const viewerRole = teamRoles.find(({ name }) => name.includes('Viewer'));

    if (viewerRole && enableViewer) {
      setValue('roles', [ viewerRole.id ]);
    }

    // disable viewer role
    viewerRole && disabledRoleIds.push(viewerRole.id);

    // disable owner role if user is the only active team owner
    const teamOwners = team?.owners || [];
    const activeTeamOwners = teamOwners.filter((o) => o.active);
    const soleOwnerCheck =
      teamOwners.length === 1 && teamOwners[0].sid === getValues('sid');
    const soleActiveOwnerCheck =
      activeTeamOwners.length === 1 &&
      activeTeamOwners[0].sid === getValues('sid');
    (soleOwnerCheck || soleActiveOwnerCheck) &&
      disabledRoleIds.push(team.ownerRoleId);
    setIsSoleTeamOwner(soleOwnerCheck);
    setIsOnlyActiveTeamOwner(soleActiveOwnerCheck);
    setDisabledRoleIds(disabledRoleIds);
  };

  const handleChangeTeam = (teamId) => {
    const newTeam = Object.values(teams || {}).find(
      (team) => team.id === Number(teamId)
    );
    if (!newTeam.active && getValues('active')) {
      setValue('active', false);
    }
    const teamRoles = mapObjectToArray(roles || {}).filter(
      (role) => role.team_id === Number(teamId)
    );
    getDisabledRolesId(newTeam, teamRoles, true);
  };

  useEffect(() => {
    if (userLoading || dataLoading) return;
    handleChangeTeam(getValues('team_id'));
  }, [ watch('team_id') ]);

  return {
    user,
    isLoading: userLoading || dataLoading,
    isFormDisabled,
    isSoleTeamOwner,
    isOnlyActiveTeamOwner,
    teams,
    disabledRoleIds,
    roles: mapObjectToArray(roles || {}).filter(
      (role) => role.team_id === Number(getValues('team_id'))
    ),
    currentUser,
  };
};

export default useUser;
