import { useEffect, useState, useCallback, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import { addAlert } from '../store/actions/alertBanner';
import { getTeams } from '../store/actions/teams';
import { getPlatforms } from '../store/actions/platforms';
import { getRuleTypes } from '../store/actions/rule-types';
import { getRuleSubTypes } from '../store/actions/ruleSubTypes';
import { TEAMS_FLAGS } from '../constants/permissionsList';
import { createIdFromName } from '../utils';
import { validateRuleType, validateRuleSubType } from '../utils/validation';
import * as applicationApi from '../api/applications';

export const useApplication = ({ id = '', watch, setValue, getValues, reset }) => {
    const dispatch = useDispatch();
    const [ applicationIdValue, setApplicationIdValue ] = useState('');
    const [ applicationLoading, setApplicationLoading ] = useState(true);
    const [ application, setApplication ] = useState({});
    const [ isFormDisabled, setIsFormDisabled ] = useState(false);
    const {
        teams: { items: teams, isLoading: teamsLoading },
        authenticated: currentUser,
        platforms: { items: platforms, isLoading: platformsLoading },
        ruleTypes: { items: ruleTypes, isLoading: ruleTypesLoading },
        ruleSubTypes: { items: ruleSubTypes, isLoading: ruleSubTypesLoading },
    } = useSelector((state) => state);

    const canEditAll = useMemo(() => !!currentUser.permissions['admin'] || !!currentUser.permissions['applications_manage_super'], [ currentUser ]);
    const canEdit = useMemo(() => canEditAll || !!currentUser.permissions['applications_manage'], [ currentUser ]);

    const dataLoading = useMemo(() => {
        return teamsLoading || platformsLoading || ruleTypesLoading || ruleSubTypesLoading;
    }, [ teamsLoading, platformsLoading, ruleTypesLoading, ruleSubTypesLoading ]);

    const validateRuleTypeSelection = useCallback(
        validateRuleType(ruleTypes),
        [ ruleTypes ]
    );

    const validateRuleSubTypeSelection = useCallback(
        validateRuleSubType(ruleTypes, ruleSubTypes, getValues('ruletypeids')),
        [ ruleTypes, ruleSubTypes, watch('ruletypeids') ]
    );

    const isCampaignRuleTypeSelected = useMemo(() => {
        return (getValues('ruletypeids') && ruleTypes)
            ? getValues('ruletypeids').some(ruleTypeId => {
                return [ 'campaign', 'ccau_campaign' ].includes(ruleTypes.find(({ id }) => id === ruleTypeId)?.slug);
            })
            : false;
    }, [ watch('ruletypeids'), ruleTypes ]);

    /**
    * get ALL teams, platforms, ruletypes and rulesubtypes
    */
    useEffect(() => {
        dispatch(getTeams({
            flag: TEAMS_FLAGS.SKIP_ACCESS,
        }));
        dispatch(getPlatforms());
        dispatch(getRuleTypes());
        dispatch(getRuleSubTypes());
    }, []);

    useEffect(() => {
        if (dataLoading) return;
        if (id) {
            try {
                const getApplication = async() => {
                    const applicationData = await applicationApi.getApplication(id);
                    setApplication(applicationData);
                    reset({
                        ...applicationData,
                        platformids: applicationData.platformIds,
                        ruletypeids: applicationData.ruleTypeIds,
                        rulesubtypeids: applicationData.ruleSubTypeIds,
                    });

                    if (applicationData.id) {
                        // enabled if user has applications_manage_super permission, or if
                        // user has applications_manage permission and their team owns the application
                        const isOwnTeamsApp = applicationData.team_id === currentUser.team_id;
                        setIsFormDisabled(!(canEditAll || (canEdit && isOwnTeamsApp)));
                    } else {
                        // enabled if user has any containers_manage permission - other fields will be filtered accordingly
                        setIsFormDisabled(!canEdit);
                    }
                    setApplicationLoading(false);
                };
                getApplication();
            } catch (error) {
                dispatch(addAlert({ message: 'An error occurred loading this application.' }));
                setApplicationLoading(false);
            }
        } else {
            setApplicationLoading(false);
        }
    }, [ dataLoading, currentUser ]);

    useEffect(() => {
        if (getValues('name') && getValues('name') !== application.name && !id) {
            const applicationId = createIdFromName(getValues('name'));
            watch('name') && setValue('applicationId', applicationId);
            setApplicationIdValue(applicationId);
        } else {
            setApplicationIdValue(application.applicationId);
        }
    }, [ watch('name') ]);

    return {
        isLoading: applicationLoading || dataLoading,
        isFormDisabled,
        teams,
        applicationIdValue,
        platforms,
        ruleTypes,
        validateRuleTypeSelection,
        isCampaignRuleTypeSelected,
        ruleSubTypes,
        validateRuleSubTypeSelection,
    };
};

export default useApplication;
