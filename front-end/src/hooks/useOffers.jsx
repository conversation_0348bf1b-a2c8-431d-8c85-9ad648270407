import { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import { getOffers } from '../store/actions/offers';
import { getUsers } from '../store/actions/users';
import { getTeams } from '../store/actions/teams';

export const useOffers = (filters, initialize = false) => {
  const dispatch = useDispatch();
  const {
    offers: {
      items: offers,
      pagination,
      isLoading: offersLoading,
      isUpdateOfferStatusLoading,
    },
    users: { items: users },
    authenticated: currentUser,
  } = useSelector((state) => state);

  useEffect(() => {
    dispatch(getOffers(filters));
    if (initialize && (!users || !users.length)) {
      dispatch(getUsers());
      dispatch(getTeams());
    }
  }, [ filters ]);

  return {
    offersLoading,
    offers,
    pagination,
    currentUser,
    isUpdateOfferStatusLoading,
  };
};
