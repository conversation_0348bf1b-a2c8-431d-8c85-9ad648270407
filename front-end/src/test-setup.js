import { configure } from 'enzyme';
import Adapter from 'enzyme-adapter-react-16';
import { createStore, applyMiddleware } from 'redux';
import thunk from 'redux-thunk';
import 'jest-canvas-mock';
import '@testing-library/jest-dom';
import { TextEncoder, TextDecoder } from 'util';
import rootReducer from './store/reducers';

// Polyfill TextEncoder/TextDecoder for jsdom/Jest (needed by jspdf >= 3.0.2)
if (typeof global !== 'undefined') {
  if (!global.TextEncoder) global.TextEncoder = TextEncoder;
  if (!global.TextDecoder) global.TextDecoder = TextDecoder;
}
if (typeof window !== 'undefined') {
  if (!window.TextEncoder) window.TextEncoder = TextEncoder;
  if (!window.TextDecoder) window.TextDecoder = TextDecoder;
}

global.newStore = () => createStore(rootReducer, applyMiddleware(thunk));

global.snapshot = wrapper => (
  test('Snapshot', () => {
    expect(wrapper).toMatchSnapshot();
  })
);

window.scrollTo = jest.fn();
window.resizeTo = jest.fn();

configure({ adapter: new Adapter() });

process.env.ADMIN_URL = '/api-base-url/';
