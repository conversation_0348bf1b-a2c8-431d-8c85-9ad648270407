import {
  STATUS,
  CONTENT_SPACE_ID,
  modalTypes,
  ruleTypes,
  setBrowserTitle,
  sortByUpdatedAt,
  mapIdToKey,
  mapKeyToId,
  mapTruePropsToArray,
  mapArrayToTrueProps,
  mapObjectToArray,
  mapObjectPropertyToArray,
  mapAlertStatusToTitle,
  mapCampaignStatusToTitle,
  openPreviewWindow,
  changeFocus,
  scrollToTop,
  formatWord,
  trim,
  addThousandSeparator,
  removeThousandSeparator,
} from './index';

describe('STATUS', () => {
  global.snapshot(STATUS);
});

describe('CONTENT_SPACE_ID', () => {
  global.snapshot(CONTENT_SPACE_ID);
});

describe('modalTypes', () => {
  global.snapshot(modalTypes);
});

describe('ruleTypes', () => {
  global.snapshot(ruleTypes);
});

describe('setBrowserTitle', () => {
  setBrowserTitle('test-title');
  global.snapshot(document.title);
});

describe('sortByUpdatedAt', () => {
  const test = [
    {
      'updated_at': 2,
    },
    {
      'updated_at': 1,
    },
    {
      'updated_at': 3,
    },
  ];
  global.snapshot(sortByUpdatedAt(test));
});

describe('mapIdToKey', () => {
  const test = [
    {
      'id': '1',
      'name': 'Admin',
    },
    {
      'id': '2',
      'name': 'Workflow Manager',
    },
    {
      'id': '3',
      'name': 'Campaign Manager',
    },
  ];
  global.snapshot(mapIdToKey(test));
});

describe('mapKeyToId', () => {
  const test = {
    1: 'Admin',
    2: 'Workflow Manager',
    3: 'Campaign Manager',
  };
  global.snapshot(mapKeyToId(test));
});

describe('mapTruePropsToArray', () => {
  const test = {
    a: true,
    b: true,
  };
  global.snapshot(mapTruePropsToArray(test));
});

describe('mapArrayToTrueProps', () => {
  const test = [ 'a', 'b' ];
  global.snapshot(mapArrayToTrueProps(test));
});

describe('mapObjectToArray', () => {
  const test = {
    a: { testProp: 'a' },
    b: { testProp: 'b' },
  };
  global.snapshot(mapObjectToArray(test));
});

describe('mapObjectPropertyToArray', () => {
  const test = {
    a: { testProp1: 'test-prop-1a', testProp2: 'test-prop-2a' },
    b: { testProp1: 'test-prop-1b', testProp2: 'test-prop-2b' },
  };
  const result1 = mapObjectPropertyToArray(test, 'testProp1');
  expect(result1).toStrictEqual([ 'test-prop-1a', 'test-prop-1b' ]);
  const result2 = mapObjectPropertyToArray(test, 'testProp2');
  expect(result2).toStrictEqual([ 'test-prop-2a', 'test-prop-2b' ]);
});

describe('mapAlertStatusToTitle', () => {
  const titles = {};
  for (const status in STATUS) {
    titles[status] = mapAlertStatusToTitle(STATUS[status]);
  }
  global.snapshot(titles);
});

describe('mapCampaignStatusToTitle', () => {
  const titles = {};
  for (const status in STATUS) {
    titles[status] = mapCampaignStatusToTitle(STATUS[status]);
  }
  global.snapshot(titles);
});

describe('openPreviewWindow', () => {
  window.open = jest.fn();
  openPreviewWindow('test-type', 'test-id', 'test-container', 'nova', true)();
  const args = window.open.mock.calls.pop();
  global.snapshot(args);
});

describe('changeFocus', () => {
  it('With Element', () => {
    const el = document.createElement('div');
    el.setAttribute('id', 'test-id');
    changeFocus(el);
  });

  it('Empty', () => {
    changeFocus();
  });
});

describe('scrollToTop ', () => {
  scrollToTop();
});

describe('formatWord', () => {
  it('Default', () => {
    expect(formatWord('word')).toBe('word');
  });

  it('Plural', () => {
    expect(formatWord('word', { plural: true })).toBe('words');
  });

  it('Capitalize', () => {
    expect(formatWord('word', { capitalize: true })).toBe('Word');
  });

  it('Plural and Capitalize', () => {
    expect(formatWord('word', { plural: true, capitalize: true })).toBe('Words');
  });

  it('Empty String', () => {
    expect(formatWord('', { plural: true, capitalize: true })).toBe('');
  });
});

describe('trim', () => {
  it('should trim extra space', () => {
    expect(trim('word ')).toBe('word');
  });
});

describe('addThousandSeparator', () => {
  it('should retun 0', () => {
    expect(addThousandSeparator(0)).toBe('0');
  });

  it('should remove any non-numeric characters', () => {
    expect(addThousandSeparator('ab1000')).toBe('1,000');
  });

  it('should remove any leading zeros', () => {
    expect(addThousandSeparator('001000')).toBe('1,000');
  });
});

describe('removeThousandSeparator', () => {
  it('should remove Thousand Separator', () => {
    expect(removeThousandSeparator('1,000')).toBe('1000');
  });
});
