export const routes = {
  'Campaigns': [
    {
      to: '/campaigns',
      name: 'Campaigns',
      section: 'Campaigns', // keeps backward compatibility to permissions checking
    }, {
      to: '/campaigns/sol',
      name: 'SOL',
      section: 'sol_campaigns',
    }, {
      to: '/campaigns/estore',
      name: 'Storefront',
      section: 'storefront_campaigns',
    },
    {
      to: '/ccau_campaigns',
      name: 'CCAU',
      section: 'ccau_campaigns',
    },
  ],
  'Alerts': [
    {
      to: '/alerts',
      name: '<PERSON><PERSON><PERSON>',
      section: 'Alerts',
    },
  ],
  'Placement': [
    {
      to: '/applications',
      name: 'Applications',
      section: 'Applications',
    }, {
      to: '/containers',
      name: 'Containers',
      section: 'Containers',
    }, {
      to: '/pages',
      name: 'Pages',
      section: 'Pages',
    },
  ],
  'Access': [
    {
      to: '/teams',
      name: 'Teams',
      section: 'teams',
    }, {
      to: '/users',
      name: 'Users',
      section: 'Users',
    }, {
      to: '/roles',
      name: 'Roles',
      section: 'Roles',
    } ],
  'Mapping': [
    {
      to: '/variable-mapping/active',
      name: 'Active',
      section: 'variable_mappings',
    }, {
      to: '/variable-mapping/pending',
      name: 'Pending',
      section: 'variable_mappings',
    }, {
      to: '/variable-mapping/draft',
      name: 'Draft',
      section: 'variable_mappings',
    },
  ],
  'Message Centre': [
    {
      to: '/message-centre/campaigns',
      name: 'Campaigns',
      section: 'message_centre_campaigns',
    },
    {
      to: '/message-centre/messages',
      name: 'Messages',
      section: 'message_centre_messages',
    },
  ],
  'Offer Management': [
    {
      to: '/offers',
      name: 'Offers',
      section: 'Offers',
    },
  ],
};
