export const productCategories = [
  'banking',
  'borrowing',
  'investing',
];

export const productCategoryExclusionSuffix = '_exclusions';

export const productExclusionCategories = productCategories.map(p => `${p}${productCategoryExclusionSuffix}`);

export const ownership = {
  retail: 'R',
  business: 'B',
};

export const productFilterQuery = {
  anyOf: 'any_of',
  allOf: 'all_of',
  noneOf: 'none_of',
};
