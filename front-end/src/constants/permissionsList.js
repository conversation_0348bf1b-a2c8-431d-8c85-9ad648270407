import { permissions as allPermissions } from '../../../src/permissions/permission-constants';

export const permissions = {
  placement: {
    applications: {
      view: allPermissions.APPLICATIONS_VIEW,
      manage: allPermissions.APPLICATIONS_MANAGE,
    },
    superApplications: {
      view: allPermissions.APPLICATIONS_VIEW_SUPER,
      manage: allPermissions.APPLICATIONS_MANAGE_SUPER,
    },
    pages: {
      view: allPermissions.PAGES_VIEW,
      manage: allPermissions.PAGES_MANAGE,
    },
    superPages: {
      view: allPermissions.PAGES_VIEW_SUPER,
      manage: allPermissions.PAGES_MANAGE_SUPER,
    },
    containers: {
      view: allPermissions.CONTAINERS_VIEW,
      manage: allPermissions.CONTAINERS_MANAGE,
    },
    superContainers: {
      view: allPermissions.CONTAINERS_VIEW_SUPER,
      manage: allPermissions.CONTAINERS_MANAGE_SUPER,
    },
  },
  accessControl: {
    teams: {
      view: allPermissions.TEAMS_VIEW,
      manage: allPermissions.TEAMS_MANAGE,
    },
    superTeams: {
      view: allPermissions.TEAMS_VIEW_SUPER,
      manage: allPermissions.TEAMS_MANAGE_SUPER,
    },
    users: {
      view: allPermissions.USERS_VIEW,
      manage: allPermissions.USERS_MANAGE,
    },
    superUsers: {
      view: allPermissions.USERS_VIEW_SUPER,
      manage: allPermissions.USERS_MANAGE_SUPER,
    },
    roles: {
      view: allPermissions.ROLES_VIEW,
      manage: allPermissions.ROLES_MANAGE,
    },
    superRoles: {
      view: allPermissions.ROLES_VIEW_SUPER,
      manage: allPermissions.ROLES_MANAGE_SUPER,
    },
  },
  variableMapping: {
    pega: {
      view: allPermissions.PEGA_VARIABLE_MAPPING_VIEW,
      manage: allPermissions.PEGA_VARIABLE_MAPPING_MANAGE,
    },
  },
  messageCentre: {
    campaigns: {
      view: allPermissions.MESSAGE_CENTRE_CAMPAIGNS_VIEW,
      manage: allPermissions.MESSAGE_CENTRE_CAMPAIGNS_MANAGE,
    },
    messages: {
      view: allPermissions.MESSAGE_CENTRE_MESSAGES_VIEW,
      manage: allPermissions.MESSAGE_CENTRE_MESSAGES_MANAGE,
    },
  },
  offers: {
    view: allPermissions.OFFERS_VIEW,
    manage: allPermissions.OFFERS_MANAGE,
  },
};

export const STATIC_PERMISSION_SECTIONS = {
  placement: [
    allPermissions.APPLICATIONS_VIEW,
    allPermissions.APPLICATIONS_MANAGE,
    allPermissions.APPLICATIONS_VIEW_SUPER,
    allPermissions.APPLICATIONS_MANAGE_SUPER,
    allPermissions.PAGES_VIEW,
    allPermissions.PAGES_MANAGE,
    allPermissions.PAGES_VIEW_SUPER,
    allPermissions.PAGES_MANAGE_SUPER,
    allPermissions.CONTAINERS_VIEW,
    allPermissions.CONTAINERS_MANAGE,
    allPermissions.CONTAINERS_VIEW_SUPER,
    allPermissions.CONTAINERS_MANAGE_SUPER,
  ],
  accessControl: [
    allPermissions.TEAMS_VIEW,
    allPermissions.TEAMS_MANAGE,
    allPermissions.TEAMS_MANAGE_SUPER,
    allPermissions.TEAMS_MANAGE_SUPER,
    allPermissions.USERS_VIEW,
    allPermissions.USERS_MANAGE,
    allPermissions.USERS_VIEW_SUPER,
    allPermissions.USERS_MANAGE_SUPER,
    allPermissions.ROLES_VIEW,
    allPermissions.ROLES_MANAGE,
    allPermissions.ROLES_VIEW_SUPER,
    allPermissions.ROLES_MANAGE_SUPER,
  ],
  variableMapping: [
    allPermissions.PEGA_VARIABLE_MAPPING_VIEW,
    allPermissions.PEGA_VARIABLE_MAPPING_MANAGE,
  ],
  messageCentre: [
    allPermissions.MESSAGE_CENTRE_CAMPAIGNS_VIEW,
    allPermissions.MESSAGE_CENTRE_CAMPAIGNS_MANAGE,
    allPermissions.MESSAGE_CENTRE_MESSAGES_VIEW,
    allPermissions.MESSAGE_CENTRE_MESSAGES_MANAGE,
  ],
  offers: [
    allPermissions.OFFERS_VIEW,
    allPermissions.OFFERS_MANAGE,
    allPermissions.OFFERS_REVIEW,
    allPermissions.OFFERS_APPROVE,
  ],
};

export const TEAMS_FLAGS = {
  SKIP_ACCESS: 'skipAccess',
  SKIP_ALL_EXCEPT_TEAM_OWNERS: 'skipAllExceptTeamOwners',
  SKIP_ALL: 'skipAll',
  INCLUDE_FUNCTIONALITIES: 'includeFunctionalities',
};

export const FUNCTIONALITY_LIST = [
  { label: 'Access Control', value: 'accessControl' },
  { label: 'Alert', value: 'alert' },
  { label: 'Campaign', value: 'campaign' },
  { label: 'Estore', value: 'estore' },
  { label: 'Placement', value: 'placement' },
  { label: 'Variable Mapping', value: 'variableMapping' },
  { label: 'Vignette', value: 'vignette' },
  { label: 'Message Centre', value: 'messageCentre' },
  { label: 'Offer Management', value: 'offers' },
];

export const mapPermissionsToPermissionLevels = (permissionsList) => {
  // Placement
  const canViewAllApplications = !!permissionsList['admin'] || !!permissionsList['applications_view_super'];
  const canEditAllApplications = !!permissionsList['admin'] || !!permissionsList['applications_manage_super'];
  const canViewAllContainers = !!permissionsList['admin'] || !!permissionsList['containers_view_super'];
  const canEditAllContainers = !!permissionsList['admin'] || !!permissionsList['containers_manage_super'];
  const canViewAllPages = !!permissionsList['admin'] || !!permissionsList['pages_view_super'];
  const canEditAllPages = !!permissionsList['admin'] || !!permissionsList['pages_manage_super'];
  // Access
  const canViewAllTeams = !!permissionsList['admin'] || !!permissionsList['teams_view_super'];
  const canEditAllTeams = !!permissionsList['admin'] || !!permissionsList['teams_manage_super'];
  const canViewAllUsers = !!permissionsList['admin'] || !!permissionsList['users_view_super'];
  const canEditAllUsers = !!permissionsList['admin'] || !!permissionsList['users_manage_super'];
  const canViewAllRoles = !!permissionsList['admin'] || !!permissionsList['roles_view_super'];
  const canEditAllRoles = !!permissionsList['admin'] || !!permissionsList['roles_manage_super'];
  return {
    // Placement
    canViewAllApplications,
    canViewOwnTeamApplications: canViewAllApplications || !!permissionsList['applications_view'],
    canEditAllApplications,
    canEditOwnTeamApplications: canEditAllApplications || !!permissionsList['applications_manage'],
    canViewAllContainers,
    canViewOwnTeamContainers: canViewAllContainers || !!permissionsList['containers_view'],
    canEditAllContainers,
    canEditOwnTeamContainers: canEditAllContainers || !!permissionsList['containers_manage'],
    canViewAllPages,
    canViewOwnTeamPages: canViewAllPages || !!permissionsList['pages_view'],
    canEditAllPages,
    canEditOwnTeamPages: canEditAllPages || !!permissionsList['pages_manage'],
    // Access
    canViewAllTeams,
    canViewOwnTeam: canViewAllTeams || !!permissionsList['teams_view'],
    canEditAllTeams,
    canEditOwnTeam: canEditAllTeams || !!permissionsList['teams_manage'],
    canViewAllUsers,
    canViewOwnTeamUsers: canViewAllUsers || !!permissionsList['users_view'],
    canEditAllUsers,
    canEditOwnTeamUsers: canEditAllUsers || !!permissionsList['users_manage'],
    canViewAllRoles,
    canViewOwnTeamRoles: canViewAllRoles || !!permissionsList['roles_view'],
    canEditAllRoles,
    canEditOwnTeamRoles: canEditAllRoles || !!permissionsList['roles_manage'],
  };
};

export default allPermissions;
