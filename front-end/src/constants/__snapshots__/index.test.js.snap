// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CONTENT_SPACE_ID Snapshot 1`] = `"4szkx38resvm"`;

exports[`STATUS Snapshot 1`] = `
{
  "ACTIVE": "active",
  "DELETED": "deleted",
  "DRAFT": "draft",
  "EXPIRED": "expired",
  "INACTIVE": "inactive",
  "PENDING": "pending",
  "PUBLISHED": "published",
  "REVIEWED": "reviewed",
  "SUBMITTED": "submitted",
  "TERMINATED": "terminated",
  "UPCOMING": "upcoming",
  "UPDATED": "updated",
}
`;

exports[`mapAlertStatusToTitle Snapshot 1`] = `
{
  "ACTIVE": "Create a New Alert",
  "DELETED": "Create a New Alert",
  "DRAFT": "Edit an Alert",
  "EXPIRED": "Create a New Alert",
  "INACTIVE": "Create a New Alert",
  "PENDING": "Create a New Alert",
  "PUBLISHED": "View an Alert",
  "REVIEWED": "Create a New Alert",
  "SUBMITTED": "View an Alert",
  "TERMINATED": "Create a New Alert",
  "UPCOMING": "Create a New Alert",
  "UPDATED": "Create a New Alert",
}
`;

exports[`mapArrayToTrueProps Snapshot 1`] = `
{
  "a": true,
  "b": true,
}
`;

exports[`mapCampaignStatusToTitle Snapshot 1`] = `
{
  "ACTIVE": "Create a New Campaign",
  "DELETED": "Create a New Campaign",
  "DRAFT": "Edit a Campaign",
  "EXPIRED": "Create a New Campaign",
  "INACTIVE": "View a Campaign",
  "PENDING": "Create a New Campaign",
  "PUBLISHED": "View a Campaign",
  "REVIEWED": "View a Campaign",
  "SUBMITTED": "View a Campaign",
  "TERMINATED": "View a Campaign",
  "UPCOMING": "Create a New Campaign",
  "UPDATED": "Create a New Campaign",
}
`;

exports[`mapIdToKey Snapshot 1`] = `
{
  "1": {
    "id": "1",
    "name": "Admin",
  },
  "2": {
    "id": "2",
    "name": "Workflow Manager",
  },
  "3": {
    "id": "3",
    "name": "Campaign Manager",
  },
}
`;

exports[`mapKeyToId Snapshot 1`] = `
[
  {
    "id": "1",
    "name": "Admin",
  },
  {
    "id": "2",
    "name": "Workflow Manager",
  },
  {
    "id": "3",
    "name": "Campaign Manager",
  },
]
`;

exports[`mapObjectToArray Snapshot 1`] = `
[
  {
    "testProp": "a",
  },
  {
    "testProp": "b",
  },
]
`;

exports[`mapTruePropsToArray Snapshot 1`] = `
[
  "a",
  "b",
]
`;

exports[`modalTypes Snapshot 1`] = `
{
  "CONTENT": "CONTENT",
  "FRAGMENT": "FRAGMENT",
}
`;

exports[`openPreviewWindow Snapshot 1`] = `
[
  "/preview/true/test-type/test-id/test-container/undefined/nova/undefined",
  "winname",
  "directories=0,titlebar=0,toolbar=0,location=0,status=0,menubar=0,scrollbars=0,resizable=0,width=480,height=720",
]
`;

exports[`ruleTypes Snapshot 1`] = `
{
  "ALERT": "alert",
  "CAMPAIGN": "campaign",
  "CCAU_CAMPAIGN": "ccau_campaign",
  "ESTORE": "estore",
  "NOVA": "nova",
  "SOL": "sol",
  "VIGNETTE": "vignette",
  "VIGNETTE_BROADCAST": "vignette-broadcast",
  "VIGNETTE_PRIORITY": "vignette-priority",
}
`;

exports[`setBrowserTitle Snapshot 1`] = `"test-title - Pigeon Messaging Cluster"`;

exports[`sortByUpdatedAt Snapshot 1`] = `
[
  {
    "updated_at": 3,
  },
  {
    "updated_at": 2,
  },
  {
    "updated_at": 1,
  },
]
`;
