export const PIGEON_TEAM = 'Pigeon Team';

export const OFFER_ID_ERROR_MSG = 'Offer ID should follow this format: OFR/BEN/PER-{numeric value max length 10}. For example: OFR-1234';

export const STATUS = {
  DRAFT: 'draft',
  SUBMITTED: 'submitted',
  REVIEWED: 'reviewed',
  PUBLISHED: 'published',
  PENDING: 'pending',
  EXPIRED: 'expired',
  UPCOMING: 'upcoming',
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  TERMINATED: 'terminated',
  DELETED: 'deleted',
  // offers api status active -> update
  UPDATED: 'updated',
};

export const DARK_MODE_CAMPAIGNS = [
  'standingCampaignPreview',
  'targetedCampaignPreview',
  'priorityBoxPreview',
  'nova__rewards-campaign-preview',
  'nova__rewards-reward-preview',
  'nova__rewards-offer-preview',
  'itrade__priority-preview',
  'ccau_intercept_details',
];

export const PREVIEW_NEW_CAMPAINGNS = [ 'targetedCampaignPreview', 'standingCampaignPreview' ];

export const DATE_TIME_PRESENTATION_FORMAT = 'MM/DD/YYYY hh:mm A';
export const DATE_PRESENTATION_FORMAT = 'MM/DD/YYYY';
export const TIME_PRESENTATION_FORMAT = 'hh:mm A';
export const DATE_ISO_FORMAT = 'YYYY-MM-DDTHH:mm:ss.sssZ';
export const SHORT_MONTH_FORMAT = 'DD/MMM/YYYY';
export const LONG_MONTH_FORMAT = 'MMM DD, YYYY';

export const MAX_SCENE_POINTS = 1000000;

export const CONTENT_SPACE_ID = '4szkx38resvm';

export const CONTENTFUL_SPACES = {
  pigeon: {
    name: 'Pigeon',
    id: '4szkx38resvm',
  },
  ccau: {
    name: 'CCAU',
    id: 'gk5ecj6dpckc',
  },
  wealth: {
    name: 'Wealth',
    id: 'aqy9l22vkp1s',
  },
};

export const CAMPAIGN_BOX_CONTAINER = 'orion-campaign';

export const modalTypes = {
  CONTENT: 'CONTENT',
  FRAGMENT: 'FRAGMENT',
};

export const platformsUI = {
  ios: 'iOS',
  android: 'Android',
  web: 'Web',
};

export const keyCodes = {
  ESC: 27,
  TAB: 9,
};

export const ruleTypes = {
  ALERT: 'alert',
  NOVA: 'nova',
  CAMPAIGN: 'campaign',
  CCAU_CAMPAIGN: 'ccau_campaign',
  VIGNETTE: 'vignette',
  SOL: 'sol',
  VIGNETTE_BROADCAST: 'vignette-broadcast',
  VIGNETTE_PRIORITY: 'vignette-priority',
  ESTORE: 'estore',
};

export const ruleTypesDisplayName = {
  [ruleTypes.ALERT]: 'Alert',
  [ruleTypes.CAMPAIGN]: 'Campaign',
  [ruleTypes.NOVA]: 'Nova Campaign',
  [ruleTypes.VIGNETTE]: 'SOL Campaign',
  [ruleTypes.SOL]: 'SOL Campaign',
  [ruleTypes.VIGNETTE_BROADCAST]: 'SOL Campaign - Broadcast',
  [ruleTypes.VIGNETTE_PRIORITY]: 'SOL Campaign - Priority',
  [ruleTypes.ESTORE]: 'Storefront Campaign',
  [ruleTypes.CCAU_CAMPAIGN]: 'Campaign',
};

export const ruleSubTypesDisplayName = {
  targeted: 'Targeted Campaigns',
  mass: 'Mass Campaigns',
  message: 'Mass Messages',
  offer: 'Offer',
  wealth: 'Mass Campaign (Wealth)',
};

export const ruleSubTypes = {
  TARGETED: 'targeted',
  MASS: 'mass',
  MESSAGE: 'message',
  OFFER: 'offer',
  WEALTH: 'wealth',
};

export const ruleRouteApiMapping = {
  estore: ruleTypes.ESTORE,
  sol: 'vignette',
};

export const statusByType = {
  [ruleTypes.ALERT]: {
    pending: [ STATUS.DRAFT, STATUS.SUBMITTED ],
    published: [ STATUS.UPCOMING, STATUS.EXPIRED, STATUS.ACTIVE, STATUS.INACTIVE ],
  },
  [ruleTypes.CAMPAIGN]: {
    pending: [ STATUS.DRAFT, STATUS.SUBMITTED, STATUS.REVIEWED ],
    published: [ STATUS.UPCOMING, STATUS.EXPIRED, STATUS.ACTIVE, STATUS.INACTIVE ],
  },
  [ruleTypes.CCAU_CAMPAIGN]: {
    pending: [ STATUS.DRAFT, STATUS.SUBMITTED, STATUS.REVIEWED ],
    published: [ STATUS.UPCOMING, STATUS.EXPIRED, STATUS.ACTIVE, STATUS.INACTIVE ],
  },
  [ruleTypes.VIGNETTE]: [
    STATUS.DRAFT,
    STATUS.SUBMITTED,
    STATUS.REVIEWED,
    STATUS.PUBLISHED,
    STATUS.TERMINATED,
  ],
  [ruleTypes.SOL]: [
    STATUS.DRAFT,
    STATUS.SUBMITTED,
    STATUS.REVIEWED,
    STATUS.PUBLISHED,
    STATUS.TERMINATED,
  ],
  [ruleTypes.ESTORE]: [
    STATUS.DRAFT,
    STATUS.SUBMITTED,
    STATUS.REVIEWED,
    STATUS.PUBLISHED,
    STATUS.TERMINATED,
  ],
};

export const urlErrorMessages = {
  AUTH_ERROR: 'An error occurred during authorization process.',
  USER_DEACTIVATED: 'Your user is deactivated. Unable to log in.',
  USER_NONEXIST: 'Your user does not exist. Unable to log in.',
};

export const WEB_FRAGMENT_RESULTS_LIMIT = 10;

export const emailRegex = /\S+@\S+\.\S+/;

export const setBrowserTitle = (title) => {
  document.title = `${title} - Pigeon Messaging Cluster`;
};

export const sortByUpdatedAt = (data) =>
  data.sort((a, b) => (a.updated_at < b.updated_at ? 1 : -1));

export const mapPropToKey = (data, prop) => data.reduce((o, i) => ({ ...o, [i[prop]]: i }), {});
export const mapIdToKey = (data) => mapPropToKey(data, 'id');
export const mapNameToKey = (data) => mapPropToKey(data, 'name');
export const mapKeyToId = (data) => Object.keys(data).map((i) => ({ id: i, name: data[i] }));

export const mapTruePropsToArray = (data) => Object.keys(data).filter((i) => data[i]);
export const mapArrayToTrueProps = (data) => data.reduce((o, i) => ({ ...o, [i]: true }), {});

export const mapObjectToArray = (data) => Object.keys(data).map((i) => data[i]);
export const mapObjectPropertyToArray = (data, property) =>
  Object.values(data || {}).map((i) => i[property]);

export const mapAlertStatusToTitle = (status) => {
  switch (status) {
    case STATUS.DRAFT:
      return 'Edit an Alert';
    case STATUS.SUBMITTED:
    case STATUS.PUBLISHED:
      return 'View an Alert';
    default:
      return 'Create a New Alert';
  }
};

export const mapCampaignStatusToTitle = (status, campaignType = ruleTypes.CAMPAIGN) => {
  const mappedCampaignType = ruleRouteApiMapping[campaignType] || campaignType;
  const campaignDisplayName = ruleTypesDisplayName[mappedCampaignType];
  switch (status) {
    case STATUS.DRAFT:
      return `Edit a ${campaignDisplayName}`;
    case STATUS.SUBMITTED:
    case STATUS.REVIEWED:
    case STATUS.PUBLISHED:
    case STATUS.INACTIVE:
    case STATUS.TERMINATED:
      return `View a ${campaignDisplayName}`;
    default:
      return `Create a New ${campaignDisplayName}`;
  }
};

export const openPreviewWindow =
  (contentType, contentId, container, isDismissible, contentfulSpace, application, languages) =>
  () => {
    let dimensions = {};
    switch (contentType) {
      case 'alert':
        dimensions = { width: 375, height: 500 };
        break;
      case 'abmCampaign':
      case 'abm__campaign':
        // width = (component: 1024px) + 2 * (horizontal-margin: 12px) + (scrollbar-width: ~16px) = 1064px
        // height = (component: 768px)  + 2 * (vertical-padding: 30px) + (preview-header: 92px) = 920px
        dimensions = { width: 1064, height: 920 };
        break;
      case 'solLogoutCampaign':
        dimensions = { width: 1025, height: 440 };
        break;
      default:
        dimensions = { width: 480, height: 720 };
        break;
    }
    window.open(
      `/preview/${contentfulSpace}/${contentType}/${contentId}/${container}/${application}/${isDismissible}/${languages}`,
      'winname',
      `directories=0,titlebar=0,toolbar=0,location=0,status=0,menubar=0,scrollbars=0,resizable=0,width=${dimensions.width},height=${dimensions.height}`,
    );
  };

export const changeFocus = (e) => {
  const el = typeof e === 'string' ? document.getElementById(e) : e;
  if (el) {
    el.tabIndex = 0;
    el.focus();
    el.blur();
    el.removeAttribute('tabindex');
  }
};

export const scrollToTop = () => {
  changeFocus('content');
  window.scrollTo(0, 0);
};

export const formatWord = (word, { plural = false, capitalize = false } = {}) => {
  if (!word) {
    return word;
  }
  if (plural) {
    word += 's';
  }
  if (capitalize) {
    word = word.charAt(0).toUpperCase() + word.substring(1);
  }
  return word;
};

export const trim = (s) => (typeof s === 'string' ? s.trim() : s);

export const addThousandSeparator = (input) => {
  if (input === 0) {
    return '0';
  }
  if (!input) return '';
  let inputString = input.toString();

  // remove any non-numeric characters
  inputString = inputString.replace(/\D/g, '');

  // remove any leading zeros
  inputString = Number(inputString).toString();

  // add the comma separator
  return inputString.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

export const removeThousandSeparator = (val) => val.replace(/,/g, '');

export const sanitizeRoleName = (roleName) => {
  if (!roleName) return roleName;
  if (roleName.includes('Viewer')) return 'Viewer';
  if (roleName.includes('Team owner')) return 'Team owner';
  return roleName;
};
