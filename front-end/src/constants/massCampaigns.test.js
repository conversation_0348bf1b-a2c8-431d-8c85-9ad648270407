import { productCategories, productExclusionCategories, productCategoryExclusionSuffix, ownership, productFilterQuery } from './massCampaigns';

describe('mass campaigns constants', () => {
    it('check the values', () => {
        expect(productCategories).toStrictEqual([
            'banking',
            'borrowing',
            'investing',
        ]);

        expect(productCategoryExclusionSuffix).toStrictEqual('_exclusions');
        expect(ownership).toStrictEqual({
            retail: 'R',
            business: 'B',
        });
        expect(productFilterQuery).toStrictEqual({
            anyOf: 'any_of',
            allOf: 'all_of',
            noneOf: 'none_of',
        });
        expect(productExclusionCategories).toStrictEqual([
            'banking_exclusions',
            'borrowing_exclusions',
            'investing_exclusions',
        ]);
    });
});
