// vendor css
@import '~canvas-core-react/lib/CanvasStyles.css';
@import '~react-datetime/css/react-datetime.css';

// vendor sass
@import '~canvas-core-react/lib/scss/CanvasSCSS';

// media query variables
@import '~sass-mq';
$mq-breakpoints: (
  mobile: map-get(map-get($break-points, sm), min-width),
  tablet: map-get(map-get($break-points, md), min-width),
  desktop: map-get(map-get($break-points, lg), min-width),
);

// admin sass
@import '../components/autosuggest/autosuggest.scss';
@import '../components/autosuggest/tag.scss';
@import '../components/autosuggest-v2/autosuggest.scss';
@import '../components/modal/versionTargetingModal.scss';
@import '../components/versionTargeting/savedVersions.scss';
@import '../components/listing/filter.scss';
@import '../components/listing/list.scss';
@import '../components/advancedTargeting/index.scss';
@import '../components/rules/advancedTargetingSection.scss';
@import '../components/formFields/index.scss';
@import '../components/core/index.scss';
@import '../components/rules/details.scss';
@import '../components/rules/DetailsCards/targetingMetadata.scss';
@import '../components/rules/DetailsCards/productTargeting/productTargeting.scss';
@import '../components/modal/contentModal.scss';
@import '../components/modal/exportModal.scss';
@import '../components/onboarding/index.scss';
@import '../components/teams/index.scss';
@import '../components/users/index.scss';
@import '../components/roles/index.scss';
@import '../components/variableMapping/variableMapping.scss';
@import '../components/pageInfo/pageInfo.scss';
@import '../components/pageActions/pageActions.scss';
@import '../components/teams/TeamOwner/autoComplete.scss';
@import '../components/teams/Access/team-access.scss';
@import '../components//messageCentre/campaigns/details.scss';
@import '../components/messageCentre/messages/details.scss';
@import '../components/messageCentre/messages/listContainer.scss';
@import '../components/offerManagement/details.scss';
@import '../components/offerManagement/selectedContentTable.scss';
@import '../components/offerManagement/offerAssigneeSelection.scss';
@import '../components/offerManagement/multiProvinceStateSelection.scss';

#app {
  overflow-y: hidden;
}

.button--skip-link {
  -webkit-box-shadow: 0 0 0 0.4rem #fff, 0 0 0 0.6rem #8894a0;
  box-shadow: 0 0 0 0.4rem #fff, 0 0 0 0.6rem #8894a0;
}

.card {
  &--skip-link {
    left: -100rem;
    top: -100rem;
    width: 0.1rem;
    height: 0.1rem;
    overflow: hidden;
    position: absolute;

    &.focus-within {
      display: block;
      top: 2%;
      left: 50%;
      transform: translate(-50%, -2%);
      overflow: visible;
      height: auto;
      width: auto;
      padding: 2.4rem 3rem;
      z-index: 1;
      position: fixed;
    }
  }
}

.details {
  &__action-bar {
    display: flex;
    justify-content: space-between;
  }

  &__action-buttons {
    display: flex;
  }

  &__action-button {
    margin-left: 3rem;

    &:first-child {
      margin-left: 0;
    }
  }

  &__action-info {
    height: 0;
    margin-top: 1rem;
  }

  &__content-table {
    table-layout: auto;
    white-space: normal;

    .table__header-item {
      text-transform: uppercase;
    }
  }

  .form__input-group {
    padding: 0;
    margin: 0;
  }

  &__group-legend {
    margin-bottom: 1.6rem;
  }

  &__autosuggest-table {
    @include mq($from: tablet) {
      display: flex;
    }
  }

  &__styled-autosuggest {
    .autosuggest__input-wrapper {
      border: 0.1rem solid #ddd;
      overflow: hidden;
      border-radius: 1rem;
      padding-left: 1rem;
      padding-right: 1rem;
    }

    &__error {
      color: #cb061d;
      font-family: $font-bold-family;
      font-size: 1.6rem;
      font-weight: bolder;
      line-height: 2.4rem;
      display: block;
      padding: 0 0.1rem;
    }

    &__label {
      font-family: $font-bold-family;
      font-size: 1.6rem;
      font-weight: bolder;
      line-height: 2.4rem;
      color: $brand-black;
      display: block;
      padding: 0 0.1rem;
    }
  }

  &__autosuggest-column {
    & > div {
      margin-bottom: 1.6rem;
    }

    @include mq($from: tablet) {
      width: calc(50% - 1.8rem / 2);

      & > div:not(:last-child) {
        margin-bottom: 1.6rem;
      }

      &:first-child {
        margin-right: 1.8rem;
      }
    }
  }

  &__radio-container {
    display: flex;
    align-items: center;
    margin-top: 3.6rem;

    .form__input--radio,
    .RadioButton__container { // new Canvas radio button
      margin-top: 0;

      &:not(:first-child) {
        margin-left: 2.6rem;
      }
    }
  }

  &__no-legend {
    display: flex;
    align-items: center;
    margin-top: 1.8rem;
    margin-bottom: 0;

    & > :not(:first-child) {
      margin-left: 2.6rem;
    }
  }

  legend.input-group__legend {
    margin-bottom: 1rem;
  }
}

.padding-side {
  padding: 0 5rem;
}

.margin-half {
  margin: 1.8rem 0;
}

.side-by-side {
  @include mq($from: tablet) {
    display: flex;
    justify-content: space-between;

    > * {
      width: 45%;
    }
  }
}

#login {
  margin: 0 auto;
  max-width: 50rem;
  position: relative;
  transform: translateY(-50%);
  top: calc(50vh - 7.25rem - 3.6rem);

  button {
    margin: 0 auto;
    display: block;
  }

  @include mq($until: desktop) {
    top: calc(50vh - 8.575rem - 3.6rem);
  }
}

.centered {
  margin-left: auto;
  margin-right: auto;
}

.content-width {
  max-width: 144rem;
}

#content-main {
  @extend .centered;
  @extend .content-width;

  min-height: calc(100vh - 14.5rem - 7.2rem);

  @include mq($until: desktop) {
    min-height: calc(100vh - 17.15rem - 7.2rem);
  }
}

#tabbed-nav {
  @extend .centered;
  @extend .content-width;
}

// IE11 fix
main {
  display: block;
}

.webfragment-preview {
  &__label {
    font-family: $font-bold-family;
    font-size: 1.6rem;
    padding-bottom: 1.2rem;
  }

  &__controls {
    width: 50%;
    float: left;

    &:nth-child(2) {
      width: calc(50% - 1.5rem);
      margin-left: 1.5rem;
    }
  }

  &__preview {
    &-wrap {
      margin-top: 1.5rem;
      position: absolute;
      top: 20rem;
      bottom: 12rem;
      left: 5rem;
      right: 5rem;
    }

    iframe {
      resize: both;
      display: block;
      background-color: #fff;
      border: 0;
      box-shadow: 0 0 0.5rem #ddd;
      width: 100%;
      height: 100%;
    }

    top: 3rem;
    bottom: 0;
    left: 0;
    right: 0;
    box-shadow: 0 0 0.5rem #ddd;
    position: absolute;
    background-position: 0 0, 1rem 1rem;
    background-size: 2rem 2rem;
    background-image:
      linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%, #eee 100%),
      linear-gradient(45deg, #eee 25%, white 25%, white 75%, #eee 75%, #eee 100%);
  }
}

// Canvas bug, can contribute back
.Input__input {
  height: 4.8rem;
}

// Canvas bug, can contribute back
.form__input-group .Checkbox__container .Error__container {
  display: none;
}

// Canvas override - reported and waiting for Canvas response
.Filter__select {
  padding: 1.5rem 4.2rem 1.5rem 1.8rem;
}

.Checkbox__container .Label__label {
  color: $brand-black;
  font-weight: bold; // Temporary solution, after updating the Canvas, we will have the option labelWeight to bold it. (https://core-react-ist.apps.stg.azr-cc-pcf.cloud.bns/storybook/?path=/story/component-checkbox--single-checkbox&args=labelWeight:bold)
}

#platforms-group-inputgroup .Checkbox__container .Error__container {
  display: none;
}

.Modal.Modal--open {
  z-index: 11000; // main nav has z-index 10000
}

.content {
  &__alert-banner {
    margin-top: 2.4rem;

    .AlertBanner__text {
      white-space: pre-wrap;
    }
  }
}

#variable-mapping {
  .pending-metadata-card {
    display: flex;
    align-items: center;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .button--text {
    padding-top: 1.4rem;
  }

  td .InputContainer__input div.Label,
  th .InputContainer__input div.Label {
    display: none;
  }

  td .InputContainer__input,
  th .InputContainer__input {
    width: 100%;
  }

  thead {
    padding-top: 0;
  }

  .table__header-item {
    font-weight: 600;
  }

  td,
  th,
  .TableHead__headColumn {
    padding: 1rem;
    vertical-align: top;
  }

  #table-edit_draft_variable_set_table td,
  #table-edit_draft_variable_set_table th {
    padding: 0.5rem 1rem;
  }

  .delete-column {
    width: 10rem;
  }

  .format-column,
  .type-column {
    width: 20rem;
  }

  .old-mapping-format {
    text-decoration: line-through;
    padding-right: 1rem;
  }

  .text-n {
    border: 0.2rem solid green;
    color: green;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    text-transform: uppercase;
    display: inline;
  }

  .text-m {
    border: 0.2rem solid orange;
    color: orange;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    text-transform: uppercase;
    display: inline;
  }

  .text-d {
    border: 0.2rem solid red;
    color: red;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    text-transform: uppercase;
    display: inline;
  }

  .centered {
    text-align: center;
  }

  .assigned-at,
  .assigned-by,
  .assigned-to {
    margin-right: 1rem;
    font-weight: bold;
  }

  .assigned-at,
  .assigned-by {
    margin-left: 3rem;
  }

  .details__action-button {
    margin-left: 1rem;
  }

  .TableBody__bodyRow {
    align-items: center;
  }

  #table-edit_draft_variable_set_table tr {
    border-bottom: unset;
  }

  .Card__container {
    margin-bottom: 3rem;
  }

  .mapping-tabs,
  .details__action-bar {
    margin-bottom: 3rem;
  }

  .details__description-error {
    margin-top: 1rem;
  }

  td input.Input__input,
  th input.Input__input {
    height: 4.8rem;
    font-size: 1.4rem;
    padding: 1rem 0.1rem;
    border-bottom: 0.1rem solid $canvas-gray-600;
    line-height: 2.4rem;
  }

  .Input__input.TextArea__textarea {
    height: auto;
    font-size: 1rem;
  }

  #table-edit_draft_variable_set_table .TextButton__button {
    margin: unset;
  }

  .Selector__select {
    font-size: 1.6rem;
  }

  .details__action-bar.right {
    justify-content: flex-end;
  }

  .Tabs__list {
    justify-content: left;
  }
}
