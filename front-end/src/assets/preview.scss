@import '~canvas-core-react/lib/scss/CanvasSCSS';
@import '../components/preview/OrionInboxPreview.scss';

body {
  margin: 0;
}

.preview {
  &--alert {
    display: flex;
    padding: 1.6rem;
    padding-top: 25.5rem;
  }

  &--alert-dark {
    background: #000;
  }

  .tab__bar {
    border: none;
  }

  &__alert-banner {
    align-self: center;
  }

  &__header {
    overflow: hidden;
    padding: 0.5rem;
    border-bottom: 0.1rem solid #ddd;
    position: fixed;
    width: 100%;
    z-index: 100000;
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: stretch;

    &__row {
      display: flex;
      width: 100%;
      align-items: flex-end;
      justify-content: space-between;
      padding-top: 2rem;

      .Label {
        margin-right: 1rem;
      }
    }

    &__full-width {
      width: 100%;
    }

    &--left {
      float: left;
      padding: 0.4rem;
      transform: scale(0.8);

      label {
        font-size: 1.6rem;
      }
    }

    &--download {
      display: flex;
      align-items: center;
    }

    &--download span {
      text-align: center;
    }

    &--download-button {
      display: flex;
      align-items: center;
      flex-direction: column;

      svg {
        align-self: unset !important;
      }
    }

    &--right {
      float: right;
      transform: scale(0.8);
    }
  }

  &__help {
    transition: all 0.2s ease;
    max-height: 0;
    overflow: hidden;
    padding: 0;
    opacity: 0;

    &--open {
      max-height: 10rem;
      padding: 1rem;
      opacity: 1;
    }
  }

  &__help-icon {
    float: left;
  }

  &__help-icon svg path:first-child {
    fill: #8230df;
  }

  &__help-text {
    margin-left: 2.5rem;
    font-weight: bold;
    font-family: $font-bold-family;
    font-size: 1.3rem;
    color: #8230df;
  }

  &__login {
    button {
      margin: 0 auto;
    }

    padding: 3rem;
  }

  // TODO: Remove slider color overrides upon completion of https://jira.agile.bns/browse/CANVAS-2392
  .ToggleSwitch__slider {
    &::after {
      box-shadow: 0 0 0 0.2rem white, 0 0 0 0.4rem $canvas-dark-blue;
    }
  }

  .ToggleSwitch__input {
    &:checked {
      ~ .ToggleSwitch__slider {
        background-color: $canvas-dark-blue;
      }
    }
  }

  .SvgIcon__icon {
    &.icon-on {
      g {
        g {
          stroke: $canvas-dark-blue;
        }
      }
    }
  }

  &__preview-content {
    padding-top: 15.5rem;
  }
}

.preview-renderer {
  border-top: none;

  &__button {
    cursor: pointer;
    display: flex;
    border-color: white;
    background-color: white;
    width: 100%;
    justify-content: space-between;
    align-items: center;
  }

  .Modal__container {
    padding-top: 15.5rem;
  }

  .abm-pacc-details * {
    max-height: unset !important;
  }
}
