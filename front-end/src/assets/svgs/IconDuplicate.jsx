import React from 'react';
import PropTypes from 'prop-types';

const getColor = (color) => {
  if (color === 'blue') {
    return '#009dd6';
  } else {
    return '#333333';
  }
};

const IconDuplicate = ({ className, color }) => (
  <svg className={className} width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="1.5" y="4.5" width="12" height="12" stroke={getColor(color)}/>
    <path fillRule="evenodd" clipRule="evenodd" d="M5 2H16V13H15V14H16H17V13V2V1H16H5H4V2V3H5V2Z" fill={getColor(color)}/>
  </svg>

);

IconDuplicate.propTypes = {
  className: PropTypes.string,
  color: PropTypes.string,
};

export default IconDuplicate;
