import { actionType, closeAllModals, openContentModal, openFragmentModalAndLoadFragments } from './modal';

describe('actionType', () => {
  global.snapshot(actionType);
});

describe('MODAL_CLOSE_ALL', () => {
  const action = closeAllModals();
  it('action has right format', () => {
    expect(action.type).toBe(actionType.MODAL_CLOSE_ALL);
  });
});

describe('MODAL_OPEN_CONTENT', () => {
  const action = openContentModal();
  it('action has right format', () => {
    expect(action.type).toBe(actionType.MODAL_OPEN_CONTENT);
  });
});

describe('Modal actions tests', () => {
  const action = openFragmentModalAndLoadFragments('mockType');
  const mockDispatch = jest.fn();
  action(mockDispatch);
  expect(mockDispatch).toHaveBeenCalledTimes(2);
});
