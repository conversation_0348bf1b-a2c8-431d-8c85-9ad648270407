import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';

import {
  actionType,
  getPages,
  pageLoading,
  pageData,
  populatePageDetails,
  initializePageForm,
  savePageValues,
} from './pages';

import { addSnackbar } from './snackbar';
import { addAlert } from './alertBanner';

import { initialize } from 'redux-form';
import mockAxios from 'axios';

const mockStore = configureMockStore([ thunk ]);
const dispatch = jest.fn();
const data = { payload: 'test-payload', name: 'Heyo' };

describe('Page actions', () => {
  afterEach(() => {
    mockAxios.reset();
    dispatch.mockClear();
  });

  describe('getPages', () => {
    test('successful api call', async() => {
      mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data }));
      await getPages()(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(pageLoading());
      expect(actions[1][0]).toStrictEqual(pageData(data));
      expect(actions[2][0]).toStrictEqual(pageLoading(false));
    });

    test('failed api call', async() => {
      mockAxios.get.mockImplementationOnce(() => Promise.reject(new Error('error!')));
      await getPages()(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(pageLoading(true));
      expect(actions[1][0]).toStrictEqual(addAlert({ message: 'Failed to get pages.' }));
      expect(actions[2][0]).toStrictEqual(pageLoading(false));
    });
  });

  describe('initializePageForm', () => {
    test('on a new page', async() => {
      mockAxios.get.mockImplementation(() => Promise.resolve({ data }));
      await initializePageForm()(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(expect.any(Function)); // call to getPages
      expect(actions[1][0]).toStrictEqual(expect.any(Function)); // call to getApplications
      expect(actions[2][0]).toStrictEqual(initialize('pageDetails', { status: true, name: '' }));
    });

    test('on an existing page', async() => {
      mockAxios.get.mockImplementation(() => Promise.resolve({ data }));
      await initializePageForm('existingPageId')(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(expect.any(Function)); // getPages
      expect(actions[1][0]).toStrictEqual(expect.any(Function)); // getApplications
      expect(actions[2][0]).toStrictEqual(pageLoading(true));
      expect(actions[3][0]).toStrictEqual(populatePageDetails(data));
      expect(actions[4][0]).toStrictEqual(initialize('pageDetails', data));
    });

    test('something went wrong', async() => {
      mockAxios.get.mockImplementation(() => Promise.reject(new Error('error!')));
      await initializePageForm('existingPageId')(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(expect.any(Function)); // getPages
      expect(actions[1][0]).toStrictEqual(expect.any(Function)); // getApplications
      expect(actions[2][0]).toStrictEqual(pageLoading(true));
      expect(actions[3][0]).toStrictEqual(addAlert({ message: 'An error occurred during page creation setup.' }));
      expect(actions[4][0]).toStrictEqual(pageLoading(false));
    });
  });

  describe('savePageValues', () => {
    test('on existing page', async() => {
      const store = mockStore({});
      mockAxios.patch.mockImplementationOnce(() => Promise.resolve({ data }));
      mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data: 'mockUserData' }));
      const mockValues = { name: 'Heyo' };
      await store.dispatch(savePageValues('fakeId', mockValues));
      const actions = store.getActions();
      expect(actions[0]).toStrictEqual({ type: actionType.PAGE_SAVING });
      expect(actions[1]).toStrictEqual(addSnackbar({ message: ` was updated successfully`, 'bold': 'Page' }));
      expect(actions[2]).toStrictEqual({ type: actionType.PAGE_SAVED });
      expect(mockAxios.patch).toHaveBeenCalledWith('/pages/fakeId', mockValues, undefined); // the undefined param are the options
      expect(mockAxios.get).toHaveBeenCalledWith('/users/current', undefined);
    });

    test('on new page', async() => {
      mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data }));
      const mockValues = { name: 'Heyo' };
      await savePageValues(undefined, mockValues)(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual({ type: actionType.PAGE_SAVING });
      expect(actions[1][0]).toStrictEqual(addSnackbar({ message: ` was created successfully`, bold: 'Page' }));
      expect(actions[2][0]).toStrictEqual({ type: actionType.PAGE_SAVED });
      expect(mockAxios.post).toHaveBeenCalledWith('/pages', mockValues, undefined);
    });

    test('failed save', async() => {
      mockAxios.post.mockImplementationOnce(() => Promise.reject(new Error('error!')));
      try {
        const promise = await savePageValues(undefined, {})(dispatch);
        const actions = dispatch.mock.calls;
        expect(actions[0][0]).toStrictEqual({ type: actionType.PAGE_SAVING });
        expect(actions[1][0]).toStrictEqual(addAlert({ message: `Failed to save page "Heyo."` }));
        expect(mockAxios.post).toHaveBeenCalledWith('/pages', {}, undefined);
        expect(promise).toThrow(new Error('error!'));
      } catch (e) {}
    });
  });
});
