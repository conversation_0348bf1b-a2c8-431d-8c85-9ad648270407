import mockAxios from 'axios';

import {
    offersData,
    offersLoading,
    getOffers,
    setOfferActive,
    setUpdateOfferStatusLoading,
    offerError,
    actionType,
} from './offers';
import { addAlert } from './alertBanner';

const dispatch = jest.fn();

const OFFSET = 0;
const LIMIT = 30;

const data = {
    total: 1,
    limit: LIMIT,
    offset: OFFSET,
    items: [ {
      'offer_id': 'OFR-1',
      'title': 'Offer title',
      'category': 'Benefits',
      'priority': 1,
      'start_date': '2024-09-01T04:00:00.000Z',
      'expiry_date': '2025-02-20T05:00:00.000Z',
      'languages': [
        'E',
        'F',
      ],
    } ],
};

describe('Offers actions', () => {
    afterEach(() => {
        mockAxios.reset();
        dispatch.mockClear();
    });

    describe('Get Offers', () => {
        test('Successfull call', async() => {
            mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data }));
            await getOffers()(dispatch);
            const actions = dispatch.mock.calls;
            expect(actions[0][0]).toStrictEqual(offersLoading());
            expect(actions[1][0]).toStrictEqual(offersData(data));
            expect(actions[2][0]).toStrictEqual(offersLoading(false));
        });

        test('Failed call', async() => {
            mockAxios.get.mockImplementationOnce(() => Promise.reject(new Error('error!')));
            await getOffers()(dispatch);
            const actions = dispatch.mock.calls;
            expect(actions[0][0]).toStrictEqual(offersLoading(true));
            expect(actions[1][0]).toStrictEqual(addAlert({ message: 'Failed to get offers.' }));
            expect(actions[2][0]).toStrictEqual(offersLoading(false));
        });
    });

    describe('setOfferActive', () => {
        const data = {
          updatedOffer: {
            data: {
              offer_id: 'test-id',
              offer_status: 'ACTIVE',
            },
          },
        };

        test('successful api call - activate', async() => {
            mockAxios.put.mockImplementationOnce(() => Promise.resolve({ data }));
            await setOfferActive('test-id')(dispatch);
            const actions = dispatch.mock.calls;
            expect(actions[0][0]).toStrictEqual(setUpdateOfferStatusLoading());
            expect(actions[1][0]).toStrictEqual({
              type: actionType.OFFER_ACTIVATE,
              data: {
                id: 'test-id',
                status: 'ACTIVE',
              },
            });
            expect(actions[2][0]).toStrictEqual(setUpdateOfferStatusLoading(false));
        });

        test('successful api call - deactivate', async() => {
          mockAxios.put.mockImplementationOnce(() => Promise.resolve({
            data: {
              updatedOffer: {
                data: {
                  offer_id: 'test-id',
                  offer_status: 'INACTIVE',
                },
              },
            },
          }));
          await setOfferActive('test-id', false)(dispatch);
          const actions = dispatch.mock.calls;
          expect(actions[0][0]).toStrictEqual(setUpdateOfferStatusLoading());
          expect(actions[1][0]).toStrictEqual({
            type: actionType.OFFER_DEACTIVATE,
            data: {
              id: 'test-id',
              status: 'INACTIVE',
            },
          });
          expect(actions[2][0]).toStrictEqual(setUpdateOfferStatusLoading(false));
        });

        test('failed api call', async() => {
          mockAxios.put.mockImplementationOnce(() => Promise.reject(new Error('error!')));
          await setOfferActive('test-id', true)(dispatch);
          const actions = dispatch.mock.calls;
          expect(actions[0][0]).toStrictEqual(setUpdateOfferStatusLoading(true));
          expect(actions[1][0]).toStrictEqual(offerError());
          expect(actions[2][0]).toStrictEqual(setUpdateOfferStatusLoading(false));
        });
      });
});
