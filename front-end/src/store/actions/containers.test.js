
import { initialize } from 'redux-form';
import mockAxios from 'axios';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';

import {
  actionType,
  getContainers,
  initializeContainerForm,
  saveContainerValues,
  containerLoading,
  containerData,
  populateContainerDetails,
} from './containers';

import { addSnackbar } from './snackbar';
import { addAlert } from './alertBanner';

const mockStore = configureMockStore([ thunk ]);
const dispatch = jest.fn();
const data = { payload: 'test-payload', name: 'Hiya' };

describe('Container actions', () => {
  afterEach(() => {
    mockAxios.reset();
    dispatch.mockClear();
  });

  describe('getContainers', () => {
    test('successful api call', async() => {
      mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data }));
      await getContainers()(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(containerLoading());
      expect(actions[1][0]).toStrictEqual(containerData(data));
      expect(actions[2][0]).toStrictEqual(containerLoading(false));
    });

    test('failed api call', async() => {
      mockAxios.get.mockImplementationOnce(() => Promise.reject(new Error('error!')));
      await getContainers()(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(containerLoading());
      expect(actions[1][0]).toStrictEqual(addAlert({ message: 'Failed to get containers.' }));
      expect(actions[2][0]).toStrictEqual(containerLoading(false));
    });
  });

  describe('initializeContainerForm', () => {
    test('on a new container', async() => {
      mockAxios.get.mockImplementation(() => Promise.resolve({ data }));
      await initializeContainerForm()(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(expect.any(Function)); // call to getPages
      expect(actions[1][0]).toStrictEqual(expect.any(Function)); // call to getApplications
      expect(actions[2][0]).toStrictEqual(expect.any(Function)); // call to getContainers
      expect(actions[3][0]).toStrictEqual(expect.any(Function)); // call to getRuleTypes
      expect(actions[4][0]).toStrictEqual(initialize('containerDetails', { status: true, pages: {} }));
    });

    test('on an existing container', async() => {
      const mockData = {
        content_type: JSON.stringify([ 'alert' ]),
        status: true,
      };

      const expectedInitializationData = {
        status: true,
        content_type: [ 'alert' ],
      };
      mockAxios.get.mockImplementation(() => Promise.resolve({ data: mockData }));
      await initializeContainerForm('existingContainerId')(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(expect.any(Function)); // call to getPages
      expect(actions[1][0]).toStrictEqual(expect.any(Function)); // call to getApplications
      expect(actions[2][0]).toStrictEqual(expect.any(Function)); // call to getContainers
      expect(actions[3][0]).toStrictEqual(expect.any(Function)); // call to getRuleTypes
      expect(actions[4][0]).toStrictEqual(containerLoading());
      expect(actions[5][0]).toStrictEqual(populateContainerDetails(expectedInitializationData));
      expect(actions[6][0]).toStrictEqual(initialize('containerDetails', expectedInitializationData));
    });

    test('on an existing container (no content_type)', async() => {
      const mockData = {
        status: true,
      };

      const expectedInitializationData = {
        status: true,
        content_type: '',
      };
      mockAxios.get.mockImplementation(() => Promise.resolve({ data: mockData }));
      await initializeContainerForm('existingContainerId')(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[5][0]).toStrictEqual(populateContainerDetails(expectedInitializationData));
    });

    test('something went wrong', async() => {
      mockAxios.get.mockImplementation(() => Promise.reject(new Error('error!')));
      await initializeContainerForm('existingContainerId')(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(expect.any(Function)); // call to getPages
      expect(actions[1][0]).toStrictEqual(expect.any(Function)); // call to getApplications
      expect(actions[2][0]).toStrictEqual(expect.any(Function)); // call to getContainers
      expect(actions[3][0]).toStrictEqual(expect.any(Function)); // call to getRuleTypes
      expect(actions[4][0]).toStrictEqual(containerLoading());
      expect(actions[5][0]).toStrictEqual(addAlert({ message: 'An error occurred loading this container.' }));
      expect(actions[6][0]).toStrictEqual(containerLoading(false));
    });
  });

  describe('saveContainerValues', () => {
    afterEach(() => {
      mockAxios.reset();
    });

    test('on existing container', async() => {
      mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data: 'mockUserData' }));
      mockAxios.patch.mockImplementationOnce(() => Promise.resolve({ data }));
      const store = mockStore({});
      const mockValues = { name: 'Hiya' };
      await store.dispatch(saveContainerValues('fakeId', mockValues));
      const actions = store.getActions();
      expect(actions[0]).toStrictEqual({ type: actionType.CONTAINER_SAVING });
      expect(actions[1]).toStrictEqual(addSnackbar({ bold: 'Container', message: ` was edited successfully` }));
      expect(actions[2]).toStrictEqual({ type: actionType.CONTAINER_SAVED });
      expect(mockAxios.patch).toHaveBeenCalledWith('/containers/fakeId', mockValues, undefined); // the undefined param are the options
      expect(mockAxios.get).toHaveBeenCalledWith('/users/current', undefined);
    });

    test('on new container', async() => {
      mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data: 'mockUserData' }));
      mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data }));
      const store = mockStore({});
      const mockValues = { name: 'Hiya' };
      await store.dispatch(saveContainerValues(undefined, mockValues));
      const actions = store.getActions();
      expect(actions[0]).toStrictEqual({ type: actionType.CONTAINER_SAVING });
      expect(actions[1]).toStrictEqual(addSnackbar({ bold: 'Container', message: ` was created successfully` }));
      expect(actions[2]).toStrictEqual({ type: actionType.CONTAINER_SAVED });
      expect(mockAxios.post).toHaveBeenCalledWith('/containers', mockValues, undefined);
      expect(mockAxios.get).toHaveBeenCalledWith('/users/current', undefined);
    });

    test('failed save', async() => {
      mockAxios.post.mockImplementationOnce(() => Promise.reject(new Error('error!')));
      try {
        const promise = await saveContainerValues(undefined, {})(dispatch);
        const actions = dispatch.mock.calls;
        expect(actions[0][0]).toStrictEqual({ type: actionType.CONTAINER_SAVING });
        expect(actions[1][0]).toStrictEqual(addAlert({ message: `Failed to save container "Hiya."` }));
        expect(mockAxios.post).toHaveBeenCalledWith('/containers', {}, undefined);
        expect(promise).toThrow(new Error('error!'));
      } catch (e) {}
    });
  });
});
