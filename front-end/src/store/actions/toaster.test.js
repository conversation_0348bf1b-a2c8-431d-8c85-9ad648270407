import { actionType, addToast, flushToasts, removeToast } from './toaster';

describe('toaster action creators', () => {
  it('addToast to have correct action type and have a valid payload', () => {
    const action = addToast({ key: 'toast1' });
    expect(action.type).toBe(actionType.TOAST_ADD);
  });

  it('flushToasts to have correct action type and have a valid payload', () => {
    const action = flushToasts();
    expect(action.type).toBe(actionType.TOASTS_FLUSH);
  });

  it('removeToast to have correct action type and have a valid payload', () => {
    const action = removeToast('key');
    expect(action.type).toBe(actionType.TOAST_REMOVE);
  });
});
