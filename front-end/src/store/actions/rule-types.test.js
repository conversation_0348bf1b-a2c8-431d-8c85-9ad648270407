import { getRuleTypes } from './rule-types';
import { timeout } from '../../utils';

describe('rule types action creators', () => {
  it('should fire an action creator to fetch list of platforms', async() => {
    const mockDispatch = jest.fn();
    const action = getRuleTypes({});
    action(mockDispatch);
    expect(mockDispatch).toHaveBeenCalled();
    await timeout(200);
    expect(mockDispatch.mock.calls[1][0].type).toStrictEqual('RULE_TYPE_DATA');
  });
});
