import { actionType, getUserData, userAuth, userUnauth } from './auth';
import mockAxios from 'axios';

const dispatch = jest.fn();

describe('actionType', () => {
  global.snapshot(actionType);
});

describe('getUserData()', () => {
  getUserData()(dispatch);

  it('action failed', () => {
    mockAxios.fail = true;
    getUserData()(dispatch);
    mockAxios.reset();
  });
});

describe('USER_AUTH', () => {
  const action = userAuth({});
  it('action has right format', () => {
    expect(action.type).toBe(actionType.USER_AUTH);
  });
});

describe('USER_UNAUTH', () => {
  const action = userUnauth();
  it('action has right format', () => {
    expect(action.type).toBe(actionType.USER_UNAUTH);
  });
});
