import {
  actionType,
  getVignettes,
  vignetteLoading,
  vignetteData,
  populateVignetteDetails,
  updateVignetteDetails,
} from './vignettes';
import mockAxios from 'axios';

describe('vignette actions', () => {
  const mockDataValue = 'pigeon-admin-data';
  const dispatch = jest.fn();

  global.snapshot(actionType);

  it('vignetteLoading has correct format', () => {
    const action = vignetteLoading();
    expect(action.type).toBe(actionType.VIGNETTE_LOADING);
    expect(action.data).toBe(true);
  });

  it('vignetteData has correct format', () => {
    const action = vignetteData(mockDataValue);
    expect(action.type).toBe(actionType.VIGNETTE_DATA);
    expect(action.data).toBe(mockDataValue);
  });

  it('populateVignetteDetails has correct format', () => {
    const action = populateVignetteDetails(mockDataValue);
    expect(action.type).toBe(actionType.VIGNETTE_DETAILS_DATA);
    expect(action.data).toBe(mockDataValue);
  });

  it('updateVignetteDetails has correct format', () => {
    const action = updateVignetteDetails(mockDataValue);
    expect(action.type).toBe(actionType.VIGNETTE_DETAILS_UPDATE);
    expect(action.data).toBe(mockDataValue);
  });

  describe('getVignettes', () => {
    getVignettes()(dispatch);
    const action = dispatch.mock.calls.pop()[0];
    it('action being dispatched', () => {
      expect(action.type).toBe(actionType.VIGNETTE_LOADING);
      expect(action.data).toBe(true);
    });

    it('action failed', async() => {
      mockAxios.fail = true;
      getVignettes()(dispatch);
      mockAxios.reset();
    });
  });
});
