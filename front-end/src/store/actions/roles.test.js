import { actionType, getRoles, roleLoading, roleListData, populateRoleDetails } from './roles';
import mockAxios from 'axios';

const dispatch = jest.fn();
const data = { payload: 'test-payload' };

describe('actionType', () => {
  global.snapshot(actionType);
});

describe('getRoles()', () => {
  getRoles()(dispatch);
  const action = dispatch.mock.calls.pop()[0];
  it('action being dispatched', () => {
    expect(action.type).toBe(actionType.ROLE_LOADING);
    expect(action.data).toBe(true);
  });

  it('action failed', () => {
    mockAxios.fail = true;
    getRoles()(dispatch);
    mockAxios.reset();
  });
});

describe('ROLE_LOADING', () => {
  const action = roleLoading();
  it('action has right format', () => {
    expect(action.type).toBe(actionType.ROLE_LOADING);
    expect(action.data).toBe(true);
  });
});

describe('ROLE_LOADING(false)', () => {
  const action = roleLoading(false);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.ROLE_LOADING);
    expect(action.data).toBe(false);
  });
});

describe('ROLE_DATA', () => {
  const action = roleListData(data);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.ROLE_DATA);
    expect(action.data).toBe(data);
  });
});

describe('ROLE_DETAILS_DATA', () => {
  const action = populateRoleDetails(data);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.ROLE_DETAILS_DATA);
    expect(action.data).toBe(data);
  });
});
