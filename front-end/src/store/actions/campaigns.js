import {
  getCampaigns as getCampaignsReq,
  setCampaignActive as setCampaignsActiveReq,
  deleteCampaign as deleteCampaignReq,
} from '../../api/campaigns';
import { getUsers } from './users';
import { addSnackbar } from './snackbar';
import qs from 'qs';

export const actionType = {
  CAMPAIGN_LOADING: 'CAMPAIGN_LOADING',
  SET_CAMPAIGN_ACTIVE_LOADING: 'SET_CAMPAIGN_ACTIVE_LOADING',
  CAMPAIGN_DATA: 'CAMPAIGN_DATA',
  CAMPAIGN_DETAILS_DATA: 'CAMPAIGN_DETAILS_DATA',
  CAMPAIGN_DETAILS_UPDATE: 'CAMPAIGN_DETAILS_UPDATE',
  CAMPAIGN_ACTIVATE: 'CAMPAIGN_ACTIVATE',
  CAMPAIGN_DEACTIVATE: 'CAMPAIGN_DEACTIVATE',
  CAMPAIGN_DELETE: 'CAMPAIGN_DELETE',
  CAMPAIGN_ERROR: 'CAMPAIGN_ERROR',
  CAMPAIGN_EXPORT: 'CAMPAIGN_EXPORT',
};

/** Campaigns - Thunks **/
export const getCampaigns = ({ pageNumber = 1, limit = 10, ...query } = {}, initialize = false) => dispatch => {
  dispatch(campaignLoading());
  if (initialize) {
    dispatch(getUsers());
  }
  getCampaignsReq({ offset: (pageNumber - 1) * limit, limit, ...query })
    .then(data => {
      dispatch(campaignData(data));
    })
    .catch(() => {
      dispatch(campaignLoading(false));
    });
};

export const exportCampaigns = (query = {}, exportOptions) => async dispatch => {
  dispatch(campaignExport());
  window.open(`api/v1/export-campaign-rules${qs.stringify({ ...query, export: exportOptions.join(',') }, { addQueryPrefix: true })}`);
};

export const setCampaignActive = (id, status, active = true) => async dispatch => {
  dispatch(setCampaignActiveLoading());
  try {
    const response = await setCampaignsActiveReq(id, status, active);
    dispatch(campaignActive(active, response));
    dispatch(setCampaignActiveLoading(false));
  } catch (e) {
    dispatch(campaignError());
    dispatch(setCampaignActiveLoading(false));
  }
};

export const deleteCampaign = id => async(dispatch, getState) => {
  try {
    await deleteCampaignReq(id);
    const { campaigns: { items } } = getState();
    dispatch({
      type: actionType.CAMPAIGN_DELETE,
      data: { id },
    });
    dispatch(addSnackbar({ message: `Campaign "${items[id]?.name || id}" deleted successfully.` }));
  } catch (e) {
    dispatch(campaignError());
  }
};

/** Campaigns - Action Creators **/
export const campaignLoading = (status = true) => ({
  type: actionType.CAMPAIGN_LOADING,
  data: status,
});

export const campaignData = data => ({
  type: actionType.CAMPAIGN_DATA,
  data,
});

export const campaignDelete = data => ({
  type: actionType.CAMPAIGN_DELETE,
  data,
});

export const setCampaignActiveLoading = (status = true) => ({
  type: actionType.SET_CAMPAIGN_ACTIVE_LOADING,
  data: status,
});

export const campaignActive = (active, response) => ({
  type: actionType[`CAMPAIGN_${active ? 'ACTIVATE' : 'DEACTIVATE'}`],
  data: { id: response.id, disabled: response.disabled },
});

export const campaignExport = data => ({
  type: actionType.CAMPAIGN_EXPORT,
  data,
});

export const populateCampaignDetails = data => ({
  type: actionType.CAMPAIGN_DETAILS_DATA,
  data,
});

export const updateCampaignDetails = data => ({
  type: actionType.CAMPAIGN_DETAILS_UPDATE,
  data,
});

export const campaignError = data => ({
  type: actionType.CAMPAIGN_ERROR,
  data,
});
