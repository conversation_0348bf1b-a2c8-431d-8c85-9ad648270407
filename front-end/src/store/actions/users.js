import * as usersApi from '../../api/users';

import { addSnackbar } from './snackbar';
import { addAlert } from '../thunks/alert';
import { getUserData } from './auth';
import { getTeams } from './teams';

export const actionType = {
  USER_LOADING: 'USER_LOADING',
  USER_LIST_DATA: 'USER_LIST_DATA',
  USER_DETAILS_DATA: 'USER_DETAILS_DATA',
};

export const userLoading = (status = true) => ({
  type: actionType.USER_LOADING,
  data: status,
});

export const userListData = data => ({
  type: actionType.USER_LIST_DATA,
  data,
});

export const populateUserDetails = data => ({
  type: actionType.USER_DETAILS_DATA,
  data,
});

export const getUsers = (query = {}) => async dispatch => {
  dispatch(userLoading());
  try {
    const data = await usersApi.getUsers(query);
    dispatch(userListData(data));
    dispatch(userLoading(false));
    return data;
  } catch (e) {
    dispatch(addAlert({ message: 'Failed to get users' }));
    dispatch(userLoading(false));
  }
};

export const saveUserValues = (id, values) => async dispatch => {
  try {
    const user = await (id ? usersApi.updateUser(id, values) : usersApi.createUser(values));
    await dispatch(getUserData()); // refresh current user's access & permissions after updating a user
    dispatch(addSnackbar({ bold: 'User', message: ` was ${id ? 'updated' : 'created'} successfully` }));
    dispatch(populateUserDetails(user));
  } catch (e) {
    dispatch(addAlert({ message: `Failed to ${id ? 'update' : 'create'} user "${values.name}." ${e.response?.data?.message}` }));
    dispatch(userLoading(false));
    throw e;
  }
};

export const setUserActivation = (id, active = true, query) => async dispatch => {
  dispatch(userLoading());
  try {
    await (active ? usersApi.activateUser(id) : usersApi.deactivateUser(id));
    const data = await usersApi.getUsers(query);
    dispatch(getTeams()); // TODO: pass flag
    dispatch(userListData(data));
    dispatch(addSnackbar({ bold: 'User', message: ` was ${active ? 'activated' : 'deactivated'} successfully` }));
    dispatch(userLoading(false));
  } catch (e) {
    if (e?.response?.status === 401) {
      // if user has been deactivated, subsequent api calls will be rejected with 401
      // in which case, logout action is already dispatched, no more processing needed
      return;
    }
    dispatch(addAlert({ message: 'Failed to activate/deactivate user.' }));
    dispatch(userLoading(false));
  }
};
