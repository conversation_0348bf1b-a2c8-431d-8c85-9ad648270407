// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Global actionType Snapshot 1`] = `
{
  "ALERT_ACTIVATE": "ALERT_ACTIVATE",
  "ALERT_ADD": "ALERT_ADD",
  "ALERT_DATA": "ALERT_DATA",
  "ALERT_DEACTIVATE": "ALERT_DEACTIVATE",
  "ALERT_DELETE": "ALERT_DELETE",
  "ALERT_DETAILS_DATA": "ALERT_DETAILS_DATA",
  "ALERT_DETAILS_UPDATE": "ALERT_DETAILS_UPDATE",
  "ALERT_ERROR": "ALERT_ERROR",
  "ALERT_EXPORT": "ALERT_EXPORT",
  "ALERT_LOADING": "ALERT_LOADING",
  "ALERT_REMOVE": "ALERT_REMOVE",
  "APPLICATION_DETAILS_DATA": "APPLICATION_DETAILS_DATA",
  "APPLICATION_LIST_DATA": "APPLICATION_LIST_DATA",
  "APPLICATION_LOADING": "APPLICATION_LOADING",
  "CAMPAIGN_ACTIVATE": "CAMPAIGN_ACTIVATE",
  "CAMPAIGN_DATA": "CAMPAIGN_DATA",
  "CAMPAIGN_DEACTIVATE": "CAMPAIGN_DEACTIVATE",
  "CAMPAIGN_DELETE": "CAMPAIGN_DELETE",
  "CAMPAIGN_DETAILS_DATA": "CAMPAIGN_DETAILS_DATA",
  "CAMPAIGN_DETAILS_UPDATE": "CAMPAIGN_DETAILS_UPDATE",
  "CAMPAIGN_ERROR": "CAMPAIGN_ERROR",
  "CAMPAIGN_EXPORT": "CAMPAIGN_EXPORT",
  "CAMPAIGN_LOADING": "CAMPAIGN_LOADING",
  "CLEAR_SELECTED_WEB_FRAGMENT": "CLEAR_SELECTED_WEB_FRAGMENT",
  "CONTAINER_DETAILS_DATA": "CONTAINER_DETAILS_DATA",
  "CONTAINER_LIST_DATA": "CONTAINER_LIST_DATA",
  "CONTAINER_LOADING": "CONTAINER_LOADING",
  "CONTAINER_SAVED": "CONTAINER_SAVED",
  "CONTAINER_SAVING": "CONTAINER_SAVING",
  "CONTENT_ITEMS_DATA": "CONTENT_ITEMS_DATA",
  "CONTENT_ITEMS_LOADING": "CONTENT_ITEMS_LOADING",
  "CONTENT_ITEM_DATA": "CONTENT_ITEM_DATA",
  "CONTENT_ITEM_LOADING": "CONTENT_ITEM_LOADING",
  "CONTENT_TYPES_DATA": "CONTENT_TYPES_DATA",
  "CONTENT_TYPES_LOADING": "CONTENT_TYPES_LOADING",
  "MESSAGE_CENTRE_CAMPAIGN_ACTIVATE": "MESSAGE_CENTRE_CAMPAIGN_ACTIVATE",
  "MESSAGE_CENTRE_CAMPAIGN_DATA": "MESSAGE_CENTRE_CAMPAIGN_DATA",
  "MESSAGE_CENTRE_CAMPAIGN_DEACTIVATE": "MESSAGE_CENTRE_CAMPAIGN_DEACTIVATE",
  "MESSAGE_CENTRE_CAMPAIGN_LOADING": "MESSAGE_CENTRE_CAMPAIGN_LOADING",
  "MESSAGE_CENTRE_MESSAGE_DATA": "MESSAGE_CENTRE_MESSAGE_DATA",
  "MESSAGE_CENTRE_MESSAGE_LOADING": "MESSAGE_CENTRE_MESSAGE_LOADING",
  "MESSAGE_CENTRE_SET_CAMPAIGN_ACTIVE_LOADING": "MESSAGE_CENTRE_SET_CAMPAIGN_ACTIVE_LOADING",
  "MODAL_CLOSE_ALL": "MODAL_CLOSE_ALL",
  "MODAL_OPEN_CONTENT": "MODAL_OPEN_CONTENT",
  "MODAL_OPEN_FRAGMENT": "MODAL_OPEN_FRAGMENT",
  "OFFERS_DATA": "OFFERS_DATA",
  "OFFERS_LOADING": "OFFERS_LOADING",
  "OFFER_ACTIVATE": "OFFER_ACTIVATE",
  "OFFER_DEACTIVATE": "OFFER_DEACTIVATE",
  "OFFER_DELETE": "OFFER_DELETE",
  "OFFER_ERROR": "OFFER_ERROR",
  "PAGE_DETAILS_DATA": "PAGE_DETAILS_DATA",
  "PAGE_LIST_DATA": "PAGE_LIST_DATA",
  "PAGE_LOADING": "PAGE_LOADING",
  "PAGE_SAVED": "PAGE_SAVED",
  "PAGE_SAVING": "PAGE_SAVING",
  "PERMISSION_DATA": "PERMISSION_DATA",
  "PERMISSION_DETAILS_DATA": "PERMISSION_DETAILS_DATA",
  "PERMISSION_LOADING": "PERMISSION_LOADING",
  "PLATFORMS_DATA": "PLATFORMS_DATA",
  "PLATFORMS_LOADING": "PLATFORMS_LOADING",
  "ROLE_DATA": "ROLE_DATA",
  "ROLE_DETAILS_DATA": "ROLE_DETAILS_DATA",
  "ROLE_LOADING": "ROLE_LOADING",
  "RULE_SUB_TYPE_DATA": "RULE_SUB_TYPE_DATA",
  "RULE_SUB_TYPE_LOADING": "RULE_SUB_TYPE_LOADING",
  "RULE_TYPE_DATA": "RULE_TYPE_DATA",
  "RULE_TYPE_LOADING": "RULE_TYPE_LOADING",
  "SELECT_WEB_FRAGMENT": "SELECT_WEB_FRAGMENT",
  "SET_ALERT_ACTIVE_LOADING": "SET_ALERT_ACTIVE_LOADING",
  "SET_CAMPAIGN_ACTIVE_LOADING": "SET_CAMPAIGN_ACTIVE_LOADING",
  "SET_UPDATE_OFFER_STATUS_LOADING": "SET_UPDATE_OFFER_STATUS_LOADING",
  "SNACKBAR_ADD": "SNACKBAR_ADD",
  "SNACKBAR_REMOVE": "SNACKBAR_REMOVE",
  "TEAMS_LIST_DATA": "TEAMS_LIST_DATA",
  "TEAMS_LOADING": "TEAMS_LOADING",
  "TEAM_DETAILS_DATA": "TEAM_DETAILS_DATA",
  "TEAM_OWNERS_DATA": "TEAM_OWNERS_DATA",
  "TOASTS_FLUSH": "TOASTS_FLUSH",
  "TOAST_ADD": "TOAST_ADD",
  "TOAST_REMOVE": "TOAST_REMOVE",
  "USER_AUTH": "USER_AUTH",
  "USER_DETAILS_DATA": "USER_DETAILS_DATA",
  "USER_LIST_DATA": "USER_LIST_DATA",
  "USER_LOADING": "USER_LOADING",
  "USER_UNAUTH": "USER_UNAUTH",
  "VARS_ACTIVE_SET_UPDATED": "VARS_ACTIVE_SET_UPDATED",
  "VARS_APPROVERS_LOADED": "VARS_APPROVERS_LOADED",
  "VARS_DRAFT_SET_UPDATED": "VARS_DRAFT_SET_UPDATED",
  "VARS_EDITED_SET_UPDATED": "VARS_EDITED_SET_UPDATED",
  "VARS_MAPPING_LOADING": "VARS_MAPPING_LOADING",
  "VARS_PENDING_SET_UPDATED": "VARS_PENDING_SET_UPDATED",
  "VARS_TYPES_LOADED": "VARS_TYPES_LOADED",
  "VIGNETTE_DATA": "VIGNETTE_DATA",
  "VIGNETTE_DETAILS_DATA": "VIGNETTE_DETAILS_DATA",
  "VIGNETTE_DETAILS_UPDATE": "VIGNETTE_DETAILS_UPDATE",
  "VIGNETTE_LOADING": "VIGNETTE_LOADING",
  "WEB_FRAGMENTS_DATA": "WEB_FRAGMENTS_DATA",
  "WEB_FRAGMENTS_LOADING": "WEB_FRAGMENTS_LOADING",
}
`;
