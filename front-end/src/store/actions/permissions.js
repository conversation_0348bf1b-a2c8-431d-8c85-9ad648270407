import { getPermissions as getPermissionsReq } from '../../api/permissions';

export const actionType = {
  PERMISSION_LOADING: 'PERMISSION_LOADING',
  PERMISSION_DATA: 'PERMISSION_DATA',
  PERMISSION_DETAILS_DATA: 'PERMISSION_DETAILS_DATA',
};

/** Permissions - Thunks **/
export const getPermissions = () => dispatch => {
  dispatch(permissionLoading());
  getPermissionsReq()
    .then(data => {
      dispatch(permissionData(data));
    })
    .catch(() => {
      dispatch(permissionLoading(false));
    });
};

/** Permissions - Action Creators **/
export const permissionLoading = (status = true) => ({
  type: actionType.PERMISSION_LOADING,
  data: status,
});
export const permissionData = data => ({
  type: actionType.PERMISSION_DATA,
  data,
});

export const populatePermissionDetails = data => ({
  type: actionType.PERMISSION_DETAILS_DATA,
  data,
});
