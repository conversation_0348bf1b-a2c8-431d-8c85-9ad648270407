import { scrollToTop } from '../../constants';

export const actionType = {
  ALERT_ADD: 'ALERT_ADD',
  ALERT_REMOVE: 'ALERT_REMOVE',
};

const alertTypes = {
  alert: 'alert',
};

export const addAlert = ({ message, type = alertTypes.alert }) => {
  scrollToTop();
  return {
    type: actionType.ALERT_ADD,
    data: { message, type },
  };
};

export const removeAlert = () => ({
  type: actionType.ALERT_REMOVE,
});
