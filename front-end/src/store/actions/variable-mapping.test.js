import mockAxios from 'axios';
import {
  actionType,
  approversLoaded,
  getVariableMapping,
  submitForApproval,
  getApprovers,
  getTypes,
  variableMappingLoading,
  createMappingSet,
  deleteDraftSet,
  rejectPendingSet,
  approvePendingSet,
  updateApprover,
  activeSetUpdated,
  editedSetUpdated,
  draftSetUpdated,
  pendingSetUpdated,
} from './variable-mapping';

const createdSetRes = {
  created_at: '2021-12-08T20:36:27.337Z',
  created_by: 's1990028',
  status: 'active',
  updated_at: null,
  updated_by: null,
  approver_sid: 's1990028',
  variables: [
    {
      variable_campaign: 'test',
      variable_template: 'SOLUI_TEST_END',
      variable_type: 'text',
      variable_type_label: 'Text',
    },
  ],
};
const draftSetRes = { ...createdSetRes, status: 'draft' };
const pendingSetRes = { ...createdSetRes, status: 'pending' };
const deletedSetRes = { ...createdSetRes, status: 'deleted' };
const editedSet = {
  ...createdSetRes,
  updated_at: '2021-12-09T21:29:50.130Z',
  updated_by: 's1990028',
  description: 'desc',
};

describe('thunks to dispatch', () => {
  beforeEach(() => {
    mockAxios.reset();
  });

  afterAll(() => {
    mockAxios.reset();
  });

  const getState = () => {
    return { variableMapping: {} };
  };

  mockAxios.post.mockImplementation(() => Promise.resolve({ data: { data: draftSetRes } }));
  mockAxios.patch.mockImplementation((url, newSet) => {
    if (!url.startsWith('/variable-mappings/sets/')) return Promise.resolve({});
    const newStatus = newSet?.status;
    let resBody;
    switch (newStatus) {
      case 'draft': resBody = draftSetRes; break;
      case 'deleted': resBody = deletedSetRes; break;
      case 'active': resBody = pendingSetRes; break;
      case 'pending': resBody = pendingSetRes; break;
      default: resBody = pendingSetRes;
    }
    return Promise.resolve({ data: { data: resBody } });
  });

  it('getVariableMapping active', async() => {
    const dispatch = jest.fn();
    await getVariableMapping({ status: 'active' })(dispatch, getState);
    const dispatchCalls = dispatch.mock.calls;
    expect(dispatchCalls).toMatchObject([
      [ { data: true, type: 'VARS_MAPPING_LOADING' } ],
      [ { data: { variables: [] }, type: 'VARS_ACTIVE_SET_UPDATED' } ],
      [ { data: {}, type: 'VARS_DRAFT_SET_UPDATED' } ],
      [ { data: {}, type: 'VARS_EDITED_SET_UPDATED' } ],
      [ { data: {}, type: 'VARS_PENDING_SET_UPDATED' } ],
      [ { data: false, type: 'VARS_MAPPING_LOADING' } ],
    ]);
  });

  it('createMappingSet', async() => {
    const dispatch = jest.fn();
    await createMappingSet(editedSet)(dispatch);
    const dispatchCalls = dispatch.mock.calls;
    const data = { ...draftSetRes, description: editedSet.description };
    expect(dispatchCalls).toMatchObject([
      [ { type: actionType.VARS_DRAFT_SET_UPDATED, data } ],
      [ { type: actionType.VARS_EDITED_SET_UPDATED, data } ],
    ]);
  });

  it('deleteDraftSet', async() => {
    const dispatch = jest.fn();
    await deleteDraftSet(editedSet)(dispatch);
    const dispatchCalls = dispatch.mock.calls;
    expect(dispatchCalls[0][0].type).toStrictEqual(actionType.VARS_DRAFT_SET_UPDATED);
    expect(dispatchCalls[0][0].data).toBeFalsy();
    expect(dispatchCalls[1][0].type).toStrictEqual(actionType.VARS_EDITED_SET_UPDATED);
    expect(dispatchCalls[1][0].data).toBeFalsy();
  });

  it('rejectPendingSet', async() => {
    const dispatch = jest.fn();
    await rejectPendingSet(editedSet)(dispatch);
    const dispatchCalls = dispatch.mock.calls;
    const data = { ...draftSetRes, description: editedSet.description };
    expect(dispatchCalls).toMatchObject([
      [ { type: actionType.VARS_DRAFT_SET_UPDATED, data } ],
      [ { type: actionType.VARS_EDITED_SET_UPDATED, data } ],
      [ { type: actionType.VARS_PENDING_SET_UPDATED } ],
    ]);
  });

  it('approvePendingSet', async() => {
    const dispatch = jest.fn();
    await approvePendingSet(editedSet)(dispatch);
    const dispatchCalls = dispatch.mock.calls;
    const data = { ...pendingSetRes, description: editedSet.description };
    expect(dispatchCalls).toMatchObject([
      [ { type: actionType.VARS_ACTIVE_SET_UPDATED, data } ],
      [ { type: actionType.VARS_PENDING_SET_UPDATED } ],
    ]);
  });

  it('submitForApproval', async() => {
    const dispatch = jest.fn();
    mockAxios.put.mockImplementationOnce(() => Promise.resolve({ data: { data: draftSetRes } }));

    await submitForApproval(editedSet)(dispatch);
    const dispatchCalls = dispatch.mock.calls;
    expect(dispatchCalls[2][0].data).toBeFalsy();
    const data = { ...pendingSetRes, description: editedSet.description };
    expect(dispatchCalls).toMatchObject([
      [ { type: actionType.VARS_PENDING_SET_UPDATED, data } ],
      [ { type: actionType.VARS_DRAFT_SET_UPDATED } ],
      [ { type: actionType.VARS_EDITED_SET_UPDATED } ],
    ]);
  });

  it('getTypes', async() => {
    const dispatch = jest.fn();
    const types = [
      { id: 1, name: 'date', description: 'Date (MMDDYYY)' },
      { id: 2, name: 'text', description: 'Text' },
      { id: 3, name: 'currency', description: 'Currency' },
      { id: 4, name: 'account-number-mask', description: 'Masked Account Number' },
      { id: 5, name: 'number', description: 'Number' },
      { id: 6, name: 'account', description: 'Account Number' },
      { id: 7, name: 'gic-special-term', description: 'GIC Special Term' },
      { id: 8, name: 'gic-special-rate', description: 'GIC Special Rate' },
    ];
    mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data: { data: types } }));
    await getTypes()(dispatch);
    const action = dispatch.mock.calls.pop()[0];
    expect(action.type).toStrictEqual(actionType.VARS_TYPES_LOADED);
    expect(action.data).toStrictEqual(types);
  });

  it('getApprovers', async() => {
    const dispatch = jest.fn();
    const users = [ { id: 1, name: 'test', email: '<EMAIL>', sid: 's1990028', active: true, roles: [ 1 ] } ];
    mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data: [
      { full_name: 'test', sid: 's1990028' },
    ] }));
    await getApprovers(users)(dispatch);
    const action = dispatch.mock.calls.pop()[0];
    expect(action.type).toStrictEqual(actionType.VARS_APPROVERS_LOADED);
    expect(action.data[0].sid).toStrictEqual(users[0].sid);
  });

  it('updateApprover', async() => {
    const dispatch = jest.fn();
    const getState = () => ({ variableMapping: { pendingSet: { ...pendingSetRes, approver_sid: 's123' } } });
    await updateApprover('s123')(dispatch, getState);
    const action = dispatch.mock.calls.pop()[0];
    expect(action.type).toStrictEqual(actionType.VARS_PENDING_SET_UPDATED);
    expect(action.data).toStrictEqual(getState().variableMapping.pendingSet);
  });

  it('should handle fetch failure', async() => {
    const dispatch = jest.fn();
    mockAxios.fail = true;
    await getVariableMapping({})(dispatch, getState);
    const dispatchCalls = dispatch.mock.calls;
    expect(dispatchCalls).toMatchObject([
      [ { data: true, type: 'VARS_MAPPING_LOADING' } ],
      [ { data: { message: undefined, type: 'alert' }, type: 'ALERT_ADD' } ],
      [ { data: false, type: 'VARS_MAPPING_LOADING' } ],
    ]);
  });
});

describe('action creators', () => {
  it('variableMappingLoading', () => {
    let action = variableMappingLoading();
    expect(action).toStrictEqual({ type: actionType.VARS_MAPPING_LOADING, data: true });
    action = variableMappingLoading(false);
    expect(action).toStrictEqual({ type: actionType.VARS_MAPPING_LOADING, data: false });
  });

  it('simple data update actions', () => {
    const mockData = { data: 'data' };
    const results = [
      [ activeSetUpdated(mockData), actionType.VARS_ACTIVE_SET_UPDATED ],
      [ editedSetUpdated(mockData), actionType.VARS_EDITED_SET_UPDATED ],
      [ draftSetUpdated(mockData), actionType.VARS_DRAFT_SET_UPDATED ],
      [ pendingSetUpdated(mockData), actionType.VARS_PENDING_SET_UPDATED ],
      [ approversLoaded(mockData), actionType.VARS_APPROVERS_LOADED ],
    ];
    results.forEach(r => expect(r[0]).toStrictEqual({ type: r[1], data: mockData }));
  });
});
