import { getPlatforms } from './platforms';
import { timeout } from '../../utils';

describe('platforms action creators', () => {
  it('should fire an action creator to fetch list of platforms', async() => {
    const mockDispatch = jest.fn();
    const action = getPlatforms({});
    action(mockDispatch);
    expect(mockDispatch).toHaveBeenCalled();
    await timeout(200);
    expect(mockDispatch.mock.calls[1][0].type).toStrictEqual('PLATFORMS_DATA');
  });
});
