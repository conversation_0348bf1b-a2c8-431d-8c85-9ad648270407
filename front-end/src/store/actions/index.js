import { actionType as authActions } from './auth';
import { actionType as teamsActions } from './teams';
import { actionType as alertActions } from './alerts';
import { actionType as applicationActions } from './applications';
import { actionType as campaignActions } from './campaigns';
import { actionType as vignetteActions } from './vignettes';
import { actionType as userActions } from './users';
import { actionType as variableMappingActions } from './variable-mapping';
import { actionType as permissionActions } from './permissions';
import { actionType as containerActions } from './containers';
import { actionType as pageActions } from './pages';
import { actionType as roleActions } from './roles';
import { actionType as messageCentreCampaignsActions } from './message-centre-campaigns';
import { actionType as messageCentreMessagesActions } from './message-centre-messages';
import { actionType as offersActions } from './offers';

import { actionType as contentActions } from './content';
import { actionType as modalActions } from './modal';

import { actionType as toasterActions } from './toaster';
import { actionType as ruleTypeActions } from './rule-types';
import { actionType as ruleSubTypeActions } from './ruleSubTypes';
import { actionType as platformsActions } from './platforms';
import { actionType as alertBannerActions } from './alertBanner';
import { actionType as snackbarActions } from './snackbar';

const actionType = {
  ...authActions,

  ...alertActions,
  ...applicationActions,
  ...campaignActions,
  ...vignetteActions,
  ...userActions,
  ...variableMappingActions,
  ...permissionActions,
  ...containerActions,
  ...pageActions,
  ...roleActions,

  ...contentActions,
  ...modalActions,

  ...toasterActions,
  ...teamsActions,
  ...ruleTypeActions,
  ...ruleSubTypeActions,
  ...platformsActions,
  ...alertBannerActions,
  ...snackbarActions,
  ...messageCentreCampaignsActions,
  ...messageCentreMessagesActions,
  ...offersActions,
};

export default actionType;
