import {
  getRuleTypes as getRuleTypesReq,
} from '../../api/rule-types';

export const actionType = {
  RULE_TYPE_LOADING: 'RULE_TYPE_LOADING',
  RULE_TYPE_DATA: 'RULE_TYPE_DATA',
};

export const getRuleTypes = (query = {}) => async dispatch => {
  dispatch(ruleTypesLoading());
  try {
    const data = await getRuleTypesReq(query);
    dispatch(ruleTypesData(data));
  } catch (err) {
    dispatch(ruleTypesLoading(false));
  }
};

export const ruleTypesLoading = (status = true) => ({
  type: actionType.RULE_TYPE_LOADING,
  data: status,
});
export const ruleTypesData = data => ({
  type: actionType.RULE_TYPE_DATA,
  data,
});
