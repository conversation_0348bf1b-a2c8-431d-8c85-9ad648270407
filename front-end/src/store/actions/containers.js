import { initialize } from 'redux-form';

import * as containerApi from '../../api/containers';

// actions
import { getPages } from './pages';
import { getApplications } from './applications';
import { getRuleTypes } from './rule-types';
import { addAlert } from './alertBanner';
import { addSnackbar } from './snackbar';
import { getUserData } from './auth';
import { jsonParseOr } from '../../utils';

export const actionType = {
  CONTAINER_LOADING: 'CONTAINER_LOADING', // any container loading
  CONTAINER_LIST_DATA: 'CONTAINER_LIST_DATA', // getting all containers
  CONTAINER_DETAILS_DATA: 'CONTAINER_DETAILS_DATA', // individual container
  CONTAINER_SAVED: 'CONTAINER_SAVED',
  CONTAINER_SAVING: 'CONTAINER_SAVING',
};

/** Containers - Action Creators **/
export const containerLoading = (status = true) => ({
  type: actionType.CONTAINER_LOADING,
  data: status,
});

export const containerData = (data) => ({
  type: actionType.CONTAINER_LIST_DATA,
  data,
});

export const populateContainerDetails = data => ({
  type: actionType.CONTAINER_DETAILS_DATA,
  data,
});

/** Containers - Thunks **/
export const initializeContainerForm = id => async dispatch => {
  dispatch(getPages());
  dispatch(getApplications());
  dispatch(getContainers());
  dispatch(getRuleTypes());

  if (!id) {
    dispatch(initialize('containerDetails', { status: true, pages: { } }));
    return;
  }

  try {
    dispatch(containerLoading());
    const { data } = await containerApi.getContainer(id);
    dispatch(populateContainerDetails(data));
    // data payloader
    data.content_type = jsonParseOr(data?.content_type || '');
    dispatch(initialize('containerDetails', data));
  } catch (e) {
    dispatch(addAlert({ message: 'An error occurred loading this container.' }));
    dispatch(containerLoading(false));
  }
};

export const getContainers = (query = {}) => async dispatch => {
  dispatch(containerLoading());
  try {
    const data = await containerApi.getContainers(query);
    dispatch(containerData(data));
    dispatch(containerLoading(false));
  } catch (e) {
    dispatch(addAlert({ message: 'Failed to get containers.' }));
    dispatch(containerLoading(false));
  }
};

export const saveContainerValues = (id, values, shouldDisplayActivateSnackBar) => dispatch => {
  return new Promise(async(resolve, reject) => {
    dispatch({ type: actionType.CONTAINER_SAVING });
    try {
      const savedContainer = id ? await containerApi.patchContainer(id, values) : await containerApi.postContainer(values);
      dispatch(addSnackbar({ message: id ? ` was edited successfully` : ` was created successfully`, bold: 'Container' }));
      if (shouldDisplayActivateSnackBar) {
        dispatch(addSnackbar({ message: values.status ? ` was reactivated successfully` : ` was deactivated successfully`, bold: 'Container' }));
      }
      dispatch({ type: actionType.CONTAINER_SAVED });
      dispatch(populateContainerDetails(savedContainer.data));
      dispatch(getUserData()); // container access have changed, refresh current user's access in frontend store
      resolve();
    } catch (e) {
      dispatch(addAlert({ message: `Failed to save container "${values.name}."` }));
      reject(e);
    }
  });
};

export const setContainerActivation = (id, active = true, query) => async dispatch => {
  dispatch(containerLoading());
  try {
    await (active ? containerApi.activateContainer(id) : containerApi.deactivateContainer(id));
    const data = await containerApi.getContainers(query);
    dispatch(containerData(data));
    dispatch(containerLoading(false));
    dispatch(addSnackbar({ bold: 'Container', message: active ? ` was reactivated successfully` : ` was deactivated successfully` }));
  } catch (e) {
    dispatch(addAlert({ message: 'Failed to activate/deactivate container.' }));
    dispatch(containerLoading(false));
  }
};
