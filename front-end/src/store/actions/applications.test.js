import {
  getApplications,
  saveApplicationValues,
  initializeApplicationForm,
  applicationLoading,
  applicationDetailsData,
} from './applications';

import { addSnackbar } from './snackbar';
import { addAlert } from './alertBanner';

import { CONTENT_SPACE_ID } from '../../constants';

import { initialize } from 'redux-form';
import mockAxios from 'axios';

describe('Application action creators', () => {
  const mockDispatch = jest.fn();
  const mockCallback = jest.fn();
  const payload = {
    applicationId: 'nova',
    contentful_space: 'test-space',
    description: 'Test description',
    name: 'Nova Mobile',
    platformIds: [ 1 ],
    ruleSubTypeIds: [],
    ruleTypeIds: [ 1 ],
    status: true,
    team_id: '1',
  };

  beforeEach(() => {
    mockAxios.reset();
    mockDispatch.mockClear();
    mockCallback.mockClear();
  });

  describe('getApplications', () => {
    it('get list of apps - should set loading state and set data when request returns', async() => {
      mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data: payload }));
      await getApplications({})(mockDispatch);
      const actions = mockDispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(applicationLoading(true));
      expect(actions[1][0].type).toStrictEqual('APPLICATION_LIST_DATA');
    });

    it('get list of apps - something went wrong', async() => {
      mockAxios.get.mockImplementationOnce(() => Promise.reject(new Error('error!')));
      try {
        const promise = await getApplications({})(mockDispatch);
        const actions = mockDispatch.mock.calls;
        expect(actions[0][0]).toStrictEqual(applicationLoading(true));
        expect(actions[1][0]).toStrictEqual(applicationLoading(false));
        expect(promise).toThrow(new Error('error!'));
      } catch (e) {}
    });
  });

  describe('saveApplicationValues', () => {
    test('on existing application', async() => {
      mockAxios.patch.mockImplementationOnce(() => Promise.resolve({ data: payload }));
      await saveApplicationValues('test-id', payload)(mockDispatch);
      const actions = mockDispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(applicationLoading(true));
      expect(actions[1][0]).toStrictEqual(addSnackbar({ message: ` was updated successfully`, bold: 'Application' }));
      expect(mockAxios.patch).toHaveBeenCalledWith('/applications/test-id', payload, undefined); // the undefined param are the options
    });

    test('on new application', async() => {
      mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: payload }));
      await saveApplicationValues(undefined, payload)(mockDispatch);
      const actions = mockDispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(applicationLoading(true));
      expect(actions[1][0]).toStrictEqual(addSnackbar({ message: ` was created successfully`, bold: 'Application' }));
      expect(mockAxios.post).toHaveBeenCalledWith('/applications', payload, undefined);
    });

    test('failed save', async() => {
      mockAxios.post.mockImplementationOnce(() => Promise.reject(new Error('error!')));
      try {
        const promise = await saveApplicationValues(undefined, {})(mockDispatch);
        const actions = mockDispatch.mock.calls;
        expect(actions[0][0]).toStrictEqual(applicationLoading(true));
        expect(actions[1][0]).toStrictEqual(addAlert({ message: `Failed to save application "Nova."` }));
        expect(mockAxios.post).toHaveBeenCalledWith('/applications', {}, undefined);
        expect(promise).toThrow(new Error('error!'));
      } catch (e) {}
    });
  });

  describe('initializeApplicationForm', () => {
    it('on a new application', async() => {
      mockAxios.get.mockImplementation(() => Promise.resolve({ data: payload }));
      await initializeApplicationForm(undefined)(mockDispatch);
      const actions = mockDispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(expect.any(Function)); // call to getTeams
      expect(actions[1][0]).toStrictEqual(expect.any(Function)); // call to getRuleTypes
      expect(actions[2][0]).toStrictEqual(expect.any(Function)); // call to getRuleSubTypes
      expect(actions[2][0]).toStrictEqual(expect.any(Function)); // call to getPlatforms
      expect(actions[4][0]).toStrictEqual(
        initialize('appDetails', { status: true, contentful_space: CONTENT_SPACE_ID })
      );
    });

    it('on an existing application', async() => {
      const mockAppRes = {
        ...payload,
        id: 1,
        ruleTypeIds:
        payload.ruleTypes,
        platformIds: payload.platforms,
        ruleSubTypeIds: payload.ruleSubTypes,
      };
      const mockAppState = { ...mockAppRes };
      mockAxios.get.mockImplementation(url => Promise.resolve({
        data: url === '/applications' ? [ mockAppRes ] : mockAppRes,
      }));
      await initializeApplicationForm('1')(mockDispatch);
      const actions = mockDispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(expect.any(Function)); // call to getTeams
      expect(actions[1][0]).toStrictEqual(expect.any(Function)); // call to getRuleTypes
      expect(actions[2][0]).toStrictEqual(expect.any(Function)); // call to getRuleSubTypes
      expect(actions[2][0]).toStrictEqual(expect.any(Function)); // call to getPlatforms
      expect(actions[4][0]).toStrictEqual(applicationLoading(true));
      expect(actions[5][0]).toStrictEqual(applicationDetailsData(mockAppState));
      expect(actions[6][0]).toStrictEqual(initialize('appDetails', mockAppState));
    });

    it('something went wrong', async() => {
      mockAxios.get.mockImplementation(() => Promise.reject(new Error('error!')));
      try {
        const promise = await initializeApplicationForm('1')(mockDispatch);
        const actions = mockDispatch.mock.calls;
        expect(actions[5][0]).toStrictEqual(applicationLoading(true));
        expect(actions[6][0]).toStrictEqual(addAlert({ message: 'An error occurred during page initialization.' }));
        expect(actions[7][0]).toStrictEqual(applicationLoading(false));
        expect(promise).toThrow(new Error('error!'));
      } catch (e) {}
    });
  });
});
