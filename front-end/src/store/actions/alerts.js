import { getUsers } from './users';
import { getApplications } from './applications';
import { getAlerts as getAlertsReq, deleteAlert as deleteAlertReq, setAlertActive as setAlertActiveReq } from '../../api/alerts';
import { addSnackbar } from './snackbar';
import qs from 'qs';

export const actionType = {
  ALERT_LOADING: 'ALERT_LOADING',
  SET_ALERT_ACTIVE_LOADING: 'SET_ALERT_ACTIVE_LOADING',
  ALERT_DATA: 'ALERT_DATA',
  ALERT_DETAILS_DATA: 'ALERT_DETAILS_DATA',
  ALERT_DETAILS_UPDATE: 'ALERT_DETAILS_UPDATE',
  ALERT_ACTIVATE: 'ALERT_ACTIVATE',
  ALERT_DEACTIVATE: 'ALERT_DEACTIVATE',
  ALERT_DELETE: 'ALERT_DELETE',
  ALERT_EXPORT: 'ALERT_EXPORT',
  ALERT_ERROR: 'ALERT_ERROR',
};

/** Alerts - Thunks **/
export const getAlerts = ({ pageNumber = 1, limit = 10, ...query } = {}, initialize = false) => dispatch => {
  dispatch(alertLoading());
  if (initialize) {
    dispatch(getUsers());
    dispatch(getApplications());
  }
  getAlertsReq({ offset: (pageNumber - 1) * limit, limit, ...query })
    .then(data => {
      dispatch(alertData(data));
    })
    .catch(() => {
      dispatch(alertLoading(false));
    });
};

export const exportAlerts = (query = {}, exportOptions) => async dispatch => {
  dispatch(alertExport());
  window.open(`api/v1/export-alert-rules${qs.stringify({ ...query, export: exportOptions.join(',') }, { addQueryPrefix: true })}`);
};

export const setAlertActive = (id, status, active = true) => async dispatch => {
  dispatch(setAlertActiveLoading());
  try {
    const response = await setAlertActiveReq(id, status, active);
    dispatch(alertActive(active, response));
    dispatch(setAlertActiveLoading(false));
  } catch (e) {
    dispatch(alertError());
    dispatch(setAlertActiveLoading(false));
  }
};

export const deleteAlert = id => async(dispatch, getState) => {
  try {
    await deleteAlertReq(id);
    const { alerts: { items } } = getState();
    dispatch({
      type: actionType.ALERT_DELETE,
      data: { id },
    });
    dispatch(addSnackbar({ message: `Alert "${items[id]?.name || id}" deleted successfully.` }));
  } catch (e) {
    dispatch(alertError());
  }
};

/** Alerts - Action Creators **/
export const alertLoading = (status = true) => ({
  type: actionType.ALERT_LOADING,
  data: status,
});

export const alertData = data => ({
  type: actionType.ALERT_DATA,
  data,
});

export const setAlertActiveLoading = (status = true) => ({
  type: actionType.SET_ALERT_ACTIVE_LOADING,
  data: status,
});

export const alertActive = (active, response) => ({
  type: actionType[`ALERT_${active ? 'ACTIVATE' : 'DEACTIVATE'}`],
  data: { id: response.id, disabled: response.disabled },
});

export const alertExport = data => ({
  type: actionType.ALERT_EXPORT,
  data,
});

export const populateAlertDetails = data => ({
  type: actionType.ALERT_DETAILS_DATA,
  data,
});

export const updateAlertDetails = data => ({
  type: actionType.ALERT_DETAILS_UPDATE,
  data,
});

export const alertError = data => ({
  type: actionType.ALERT_ERROR,
  data,
});

export const alertDelete = data => ({
  type: actionType.ALERT_DELETE,
  data,
});
