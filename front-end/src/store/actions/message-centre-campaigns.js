import {
    getMessageCentreCampaigns as getMessageCentreCampaignsReq,
    setMessageCentreCampaignActive as setMessageCentreCampaignsActiveReq,
} from '../../api/message-centre-campaigns';
import { addAlert } from './alertBanner';

export const actionType = {
    MESSAGE_CENTRE_CAMPAIGN_LOADING: 'MESSAGE_CENTRE_CAMPAIGN_LOADING',
    MESSAGE_CENTRE_CAMPAIGN_DATA: 'MESSAGE_CENTRE_CAMPAIGN_DATA',
    MESSAGE_CENTRE_CAMPAIGN_ACTIVATE: 'MESSAGE_CENTRE_CAMPAIGN_ACTIVATE',
    MESSAGE_CENTRE_CAMPAIGN_DEACTIVATE: 'MESSAGE_CENTRE_CAMPAIGN_DEACTIVATE',
    MESSAGE_CENTRE_SET_CAMPAIGN_ACTIVE_LOADING: 'MESSAGE_CENTRE_SET_CAMPAIGN_ACTIVE_LOADING',
};

/** Campaigns - Thunks **/
export const getMessageCentreCampaigns = ({ pageNumber = 1, limit = 10, ...query } = {}) => async dispatch => {
    try {
        dispatch(campaignLoading());
        const data = await getMessageCentreCampaignsReq({ offset: (pageNumber - 1) * limit, limit, ...query });
        dispatch(campaignData(data));
        dispatch(campaignLoading(false));
    } catch (error) {
        dispatch(addAlert({ message: 'Failed to get message centre campaigns.' }));
        dispatch(campaignLoading(false));
    }
};

export const setCampaignActive = (id, status) => async dispatch => {
    dispatch(setCampaignActiveLoading());
    try {
      const response = await setMessageCentreCampaignsActiveReq(id, status);
      const active = status !== 'S';
      dispatch(campaignActive(active, response));
      dispatch(setCampaignActiveLoading(false));
    } catch (e) {
      dispatch(addAlert({ message: 'Failed to update campaign status.' }));
      dispatch(setCampaignActiveLoading(false));
    }
  };

/** Message Centre Campaigns - Action Creators **/
export const campaignLoading = (status = true) => ({
    type: actionType.MESSAGE_CENTRE_CAMPAIGN_LOADING,
    data: status,
});

export const campaignData = data => ({
    type: actionType.MESSAGE_CENTRE_CAMPAIGN_DATA,
    data,
});

export const setCampaignActiveLoading = (status = true) => ({
    type: actionType.MESSAGE_CENTRE_SET_CAMPAIGN_ACTIVE_LOADING,
    data: status,
  });

export const campaignActive = (active, response) => ({
    type: actionType[`MESSAGE_CENTRE_CAMPAIGN_${active ? 'ACTIVATE' : 'DEACTIVATE'}`],
    data: { id: response.id, msg_status: response.msg_status },
  });
