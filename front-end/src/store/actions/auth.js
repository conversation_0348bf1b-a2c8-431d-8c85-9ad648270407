import { getUserData as getUserDataReq, logout as logoutReq } from '../../api/auth';
import { removeAlert } from './alertBanner';

export const actionType = {
  USER_AUTH: 'USER_AUTH',
  USER_UNAUTH: 'USER_UNAUTH',
};

/** Authenticate - Thunk **/
export const getUserData = () => async dispatch => {
  try {
    const data = await getUserDataReq();
    dispatch(userAuth(data));
  } catch (err) {}
};

export const logout = () => async dispatch => {
  const data = await logoutReq();
  dispatch(userUnauth());

  if (data.wamLogoutUrl) {
    window.location.assign(decodeURIComponent(data.wamLogoutUrl));
  } else {
    dispatch(removeAlert());
  }
};

/** Authenticate - Action Creators **/
export const userAuth = data => ({
  type: actionType.USER_AUTH,
  data,
});

export const userUnauth = () => ({
  type: actionType.USER_UNAUTH,
});
