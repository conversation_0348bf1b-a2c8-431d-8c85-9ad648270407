import api from '../../api/variable-mapping';
import typeMap from '../../components/variableMapping/typeMap';
import { addAlert } from './alertBanner';

export const actionType = {
  VARS_MAPPING_LOADING: 'VARS_MAPPING_LOADING',
  VARS_ACTIVE_SET_UPDATED: 'VARS_ACTIVE_SET_UPDATED',
  VARS_EDITED_SET_UPDATED: 'VARS_EDITED_SET_UPDATED',
  VARS_DRAFT_SET_UPDATED: 'VARS_DRAFT_SET_UPDATED',
  VARS_PENDING_SET_UPDATED: 'VARS_PENDING_SET_UPDATED',
  VARS_TYPES_LOADED: 'VARS_TYPES_LOADED',
  VARS_APPROVERS_LOADED: 'VARS_APPROVERS_LOADED',
};

// if set is editable on current page, human readable label need to be mapped to variable type data
const mapVariableNames = set => ({
  ...set,
  variables: set.variables.map(v => ({
    variable_campaign: v.variable_campaign,
    variable_template: v.variable_template,
    variable_type: v.variable_type,
    variable_type_label: typeMap[v.variable_type],
  })),
});

export const getVariableMapping = ({ status }) => async(dispatch, getState) => {
  const { variableMapping: { activeSet, editedSet, pendingSet } } = getState();
  let loading;
  try {
    // fetch active set, this is needed on all pages
    if (!activeSet) {
      loading = true;
      dispatch(variableMappingLoading());
      const newActiveSets = await api.getMappings({ status: 'active' });
      const nonEmptySets = newActiveSets && newActiveSets?.length > 0;
      const newActiveSet = nonEmptySets ? mapVariableNames(newActiveSets[0]) : { variables: [] };
      dispatch(activeSetUpdated(newActiveSet));
    }

    // fetch draft set, this is needed on active and draft pages
    if ([ 'active', 'draft' ].includes(status) && !editedSet) {
      !loading && dispatch(variableMappingLoading());
      loading = true;
      const newDraftSets = await api.getMappings({ status: 'draft' });
      const nonEmptySets = newDraftSets && newDraftSets.length > 0;
      const newDraftSet = nonEmptySets ? mapVariableNames(newDraftSets[0]) : {};
      dispatch(draftSetUpdated(newDraftSet));
      dispatch(editedSetUpdated(newDraftSet));
    }

    // fetch pending set, this is needed on active and pending pages
    if ([ 'active', 'pending' ].includes(status) && !pendingSet) {
      !loading && dispatch(variableMappingLoading());
      loading = true;
      const newPendingSets = await api.getMappings({ status: 'pending' });
      const nonEmptySets = newPendingSets && newPendingSets.length > 0;
      const newPendingSet = nonEmptySets ? mapVariableNames(newPendingSets[0]) : {};
      dispatch(pendingSetUpdated(newPendingSet));
    }
  } catch (e) {
    dispatch(addAlert({ message: e.message }));
  } finally {
    loading && dispatch(variableMappingLoading(false));
  }
};

export const createMappingSet = (newSet = { variables: [] }) => async(dispatch) => {
  const draft = newSet.variable_set_id ? await api.editDraft(newSet) : await api.createMappingSet(newSet);
  const newDraft = mapVariableNames({ ...draft, description: newSet.description });
  dispatch(draftSetUpdated(newDraft));
  dispatch(editedSetUpdated(newDraft));
};

export const deleteDraftSet = set => async(dispatch) => {
  await api.updateMappings(set, 'deleted');
  dispatch(draftSetUpdated());
  dispatch(editedSetUpdated());
};

export const rejectPendingSet = pendingSet => async(dispatch) => {
  const res = await api.updateMappings(pendingSet, 'draft');
  const draft = mapVariableNames({ ...res, description: pendingSet.description });
  dispatch(draftSetUpdated(draft));
  dispatch(editedSetUpdated(draft));
  dispatch(pendingSetUpdated());
};

export const approvePendingSet = pendingSet => async(dispatch) => {
  const newActiveSet = await api.updateMappings(pendingSet, 'active');
  dispatch(activeSetUpdated(mapVariableNames({ ...newActiveSet, description: pendingSet.description })));
  dispatch(pendingSetUpdated());
};

export const submitForApproval = (editedSet, approverSid) => async(dispatch) => {
  let toApprove = editedSet;
  if (toApprove.variable_set_id === undefined) {
    toApprove = await api.createMappingSet(editedSet);
  }
  const withNewApprover = {
    ...toApprove,
    approver_sid: approverSid,
  };
  const updatedSet = await api.editDraft(withNewApprover);
  const pendingSet = await api.updateMappings(updatedSet, 'pending');
  dispatch(pendingSetUpdated({ ...pendingSet, description: editedSet.description }));
  dispatch(draftSetUpdated());
  dispatch(editedSetUpdated());
};

export const getApprovers = (users) => async(dispatch) => {
  const approvers = (await api.getApprovers()).map((a) =>
    users.find(u => u.sid === a.sid)
  );
  dispatch(approversLoaded(approvers));
};

export const getTypes = () => async(dispatch) => {
  const types = await api.getTypes();
  dispatch(typesLoaded(types));
};

export const updateApprover = approverSid => async(dispatch, getState) => {
  const currentPendingSet = getState().variableMapping.pendingSet;
  await api.editDraft({
    ...currentPendingSet,
    approver_sid: approverSid,
  });
  dispatch(pendingSetUpdated({ ...currentPendingSet, approver_sid: approverSid }));
};

// Action Creators
export const variableMappingLoading = (status = true) => ({ type: actionType.VARS_MAPPING_LOADING, data: status });
export const activeSetUpdated = data => ({ type: actionType.VARS_ACTIVE_SET_UPDATED, data });
export const editedSetUpdated = data => ({ type: actionType.VARS_EDITED_SET_UPDATED, data });
export const draftSetUpdated = data => ({ type: actionType.VARS_DRAFT_SET_UPDATED, data });
export const pendingSetUpdated = data => ({ type: actionType.VARS_PENDING_SET_UPDATED, data });
export const typesLoaded = data => ({ type: actionType.VARS_TYPES_LOADED, data });
export const approversLoaded = data => ({ type: actionType.VARS_APPROVERS_LOADED, data });
