import { getMessageCentreMessages as getMessageCentreMessagesReq } from '../../api/message-centre-messages';
import { addAlert } from './alertBanner';

export const actionType = {
  MESSAGE_CENTRE_MESSAGE_LOADING: 'MESSAGE_CENTRE_MESSAGE_LOADING',
  MESSAGE_CENTRE_MESSAGE_DATA: 'MESSAGE_CENTRE_MESSAGE_DATA',
};

/** Campaigns - Thunks **/
export const getMessageCentreMessages =
  ({ pageNumber = 1, limit = 10, ...query } = {}) =>
  async(dispatch) => {
    try {
      const { searchQuery, searchField } = query;
      let data;
      if (!searchQuery || !searchField) {
        data = {
          items: [],
          total: 0,
          offset: 0,
          limit: 10,
        };
      } else {
        dispatch(messageLoading());
        data = await getMessageCentreMessagesReq({
          offset: (pageNumber - 1) * limit,
          limit,
          ...query,
        });
      }
      dispatch(messageData(data));
      dispatch(messageLoading(false));
    } catch (error) {
      dispatch(addAlert({ message: 'Failed to get message centre messages.' }));
      dispatch(messageLoading(false));
    }
  };

/** Message Centre Campaigns - Action Creators **/
export const messageLoading = (status = true) => ({
  type: actionType.MESSAGE_CENTRE_MESSAGE_LOADING,
  data: status,
});

export const messageData = (data) => ({
  type: actionType.MESSAGE_CENTRE_MESSAGE_DATA,
  data,
});
