import {
  actionType,
  getCampaigns,
  campaignLoading,
  campaignData,
  campaignError,
  campaignExport,
  deleteCampaign,
  exportCampaigns,
  populateCampaignDetails,
  setCampaignActive,
  setCampaignActiveLoading,
  updateCampaignDetails,
} from './campaigns';
import { addSnackbar } from './snackbar';
import mockAxios from 'axios';

const dispatch = jest.fn();
const data = { payload: 'test-payload' };
window.open = jest.fn();

describe('actionType', () => {
  global.snapshot(actionType);
});

describe('getCampaigns()', () => {
  getCampaigns()(dispatch);
  const action = dispatch.mock.calls.pop()[0];
  it('action being dispatched', () => {
    expect(action.type).toBe(actionType.CAMPAIGN_LOADING);
    expect(action.data).toBe(true);
  });

  it('action failed', () => {
    mockAxios.fail = true;
    getCampaigns()(dispatch);
    mockAxios.reset();
  });
});

describe('CAMPAIGN_LOADING', () => {
  const action = campaignLoading();
  it('action has right format', () => {
    expect(action.type).toBe(actionType.CAMPAIGN_LOADING);
    expect(action.data).toBe(true);
  });
});

describe('CAMPAIGN_LOADING(false)', () => {
  const action = campaignLoading(false);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.CAMPAIGN_LOADING);
    expect(action.data).toBe(false);
  });
});

describe('CAMPAIGN_DATA', () => {
  const action = campaignData(data);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.CAMPAIGN_DATA);
    expect(action.data).toBe(data);
  });
});

describe('CAMPAIGN_DETAILS_DATA', () => {
  const action = populateCampaignDetails(data);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.CAMPAIGN_DETAILS_DATA);
    expect(action.data).toBe(data);
  });
});

describe('CAMPAIGN_DETAILS_UPDATE', () => {
  const action = updateCampaignDetails(data);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.CAMPAIGN_DETAILS_UPDATE);
    expect(action.data).toBe(data);
  });
});

describe('Campaigns actions', () => {
  beforeEach(() => {
    mockAxios.reset();
    dispatch.mockClear();
  });

  describe('setCampaignActive', () => {
    const data = {
      id: 'test-id',
      disabled: true,
    };

    test('successful api call - activate', async() => {
      mockAxios.patch.mockImplementationOnce(() => Promise.resolve({ data }));
      await setCampaignActive('test-id', 'published')(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(setCampaignActiveLoading());
      expect(actions[1][0]).toStrictEqual({
        type: actionType.CAMPAIGN_ACTIVATE,
        data,
      });
      expect(actions[2][0]).toStrictEqual(setCampaignActiveLoading(false));
    });

    test('successful api call - deactivate', async() => {
      mockAxios.patch.mockImplementationOnce(() => Promise.resolve({ data }));
      await setCampaignActive('test-id', 'published', false)(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(setCampaignActiveLoading());
      expect(actions[1][0]).toStrictEqual({
        type: actionType.CAMPAIGN_DEACTIVATE,
        data,
      });
      expect(actions[2][0]).toStrictEqual(setCampaignActiveLoading(false));
    });

    test('failed api call', async() => {
      mockAxios.patch.mockImplementationOnce(() => Promise.reject(new Error('error!')));
      await setCampaignActive('test-id', 'published', true)(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(setCampaignActiveLoading(true));
      expect(actions[1][0]).toStrictEqual(campaignError());
      expect(actions[2][0]).toStrictEqual(setCampaignActiveLoading(false));
    });
  });

  describe('exportCampaigns', () => {
    test('successful window open', async() => {
      await exportCampaigns({}, [ 'external_ref', 'name', 'content_id' ])(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(campaignExport());
      expect(window.open).toHaveBeenCalled();
    });
  });

  describe('deleteCampaign', () => {
    const id = 'test-id';
    const getState = () => {
      return { campaigns: { items: { 'test-id': { name: 'test-name' } } } };
    };

    test('successful api call ', async() => {
      mockAxios.delete.mockImplementationOnce(() => Promise.resolve({}));
      await deleteCampaign(id)(dispatch, getState);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual({
        type: actionType.CAMPAIGN_DELETE,
        data: { id },
      });
      expect(actions[1][0]).toStrictEqual(addSnackbar({ message: `Campaign "test-name" deleted successfully.` }));
    });

    test('failed api call', async() => {
      mockAxios.delete.mockImplementationOnce(() => Promise.reject(new Error('error!')));
      await deleteCampaign(id)(dispatch, getState);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(campaignError());
    });
  });
});
