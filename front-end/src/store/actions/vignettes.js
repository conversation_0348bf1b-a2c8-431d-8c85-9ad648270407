import { getVignettes as getVignettesReq } from '../../api/vignettes';
// TODO
export const actionType = {
  VIGNETTE_LOADING: 'VIGNETTE_LOADING',
  VIGNETTE_DATA: 'VIGNETTE_DATA',
  VIGNETTE_DETAILS_DATA: 'VIGNETTE_DETAILS_DATA',
  VIGNETTE_DETAILS_UPDATE: 'VIGNETTE_DETAILS_UPDATE',
};

/** Vignettes - Thunks **/
export const getVignettes = ({ page = 1, limit = 10, ...query } = {}) => dispatch => {
  dispatch(vignetteLoading());
  getVignettesReq({ offset: (page - 1) * limit, limit, ...query })
    .then(data => {
      dispatch(vignetteData(data));
    })
    .catch(() => {
      dispatch(vignetteLoading(false));
    });
};

/** Vignettes - Action Creators **/
export const vignetteLoading = (status = true) => ({
  type: actionType.VIGNETTE_LOADING,
  data: status,
});
export const vignetteData = data => ({
  type: actionType.VIGNETTE_DATA,
  data,
});

export const populateVignetteDetails = data => ({
  type: actionType.VIGNETTE_DETAILS_DATA,
  data,
});

export const updateVignetteDetails = data => ({
  type: actionType.VIGNETTE_DETAILS_UPDATE,
  data,
});
