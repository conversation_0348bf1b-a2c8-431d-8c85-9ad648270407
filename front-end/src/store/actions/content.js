import {
  getContentTypes as getContentTypesReq,
  getContentItems as getContentItemsReq,
  getContentById as getContentByIdReq,
  getWebFragments as getWebFragmentsReq,
  getWebFragment as getWebFragmentReq } from '../../api/content';
import { WEB_FRAGMENT_RESULTS_LIMIT, ruleTypes } from '../../constants';

export const actionType = {
  CONTENT_TYPES_LOADING: 'CONTENT_TYPES_LOADING',
  CONTENT_TYPES_DATA: 'CONTENT_TYPES_DATA',
  CONTENT_ITEM_LOADING: 'CONTENT_ITEM_LOADING',
  CONTENT_ITEM_DATA: 'CONTENT_ITEM_DATA',
  CONTENT_ITEMS_LOADING: 'CONTENT_ITEMS_LOADING',
  CONTENT_ITEMS_DATA: 'CONTENT_ITEMS_DATA',
  WEB_FRAGMENTS_LOADING: 'WEB_FRAGMENTS_LOADING',
  WEB_FRAGMENTS_DATA: 'WEB_FRAGMENTS_DATA',
  SELECT_WEB_FRAGMENT: 'SELECT_WEB_FRAGMENT',
  CLEAR_SELECTED_WEB_FRAGMENT: 'CLEAR_SELECTED_WEB_FRAGMENT',
};

/** Content - Thunks **/
export const getContentTypes = (contentfulSpace) => dispatch => {
  dispatch(contentTypesLoading());
  getContentTypesReq(contentfulSpace)
    .then(data => dispatch(contentTypesData(data.items)));
};

export const getContentItems = ({ page = 1, limit = 10, type, contentfulSpace, ...query } = {}) => dispatch => {
  dispatch(contentItemsLoading(type));
  getContentItemsReq({ offset: (page - 1) * limit, limit, type, contentfulSpace, ...query })
    .then(data => dispatch(contentItemsData(type, data)));
};

export const getContentById = (type, id, contentfulSpace) => async dispatch => {
  dispatch(contentItemLoading());
  const response = await getContentByIdReq(type, id, contentfulSpace);
  dispatch(contentItemData(response));
};

/** Content - Action Creators **/
export const contentTypesLoading = (status = true) => ({
  type: actionType.CONTENT_TYPES_LOADING,
  data: status,
});
export const contentTypesData = data => ({
  type: actionType.CONTENT_TYPES_DATA,
  data,
});

export const contentItemLoading = (status = true) => ({
  type: actionType.CONTENT_ITEM_LOADING,
  data: status,
});
export const contentItemData = data => ({
  type: actionType.CONTENT_ITEM_DATA,
  data,
});

export const contentItemsLoading = (type, status = true) => ({
  type: actionType.CONTENT_ITEMS_LOADING,
  data: status,
  contentType: type,
});
export const contentItemsData = (type, data) => ({
  type: actionType.CONTENT_ITEMS_DATA,
  data,
  contentType: type,
});

export const webFragmentsLoading = () => ({
  type: actionType.WEB_FRAGMENTS_LOADING,
});
export const webFragmentsData = data => ({
  type: actionType.WEB_FRAGMENTS_DATA,
  data,
});
export const getWebFragments = ({ name = '', offset = 0, limit = WEB_FRAGMENT_RESULTS_LIMIT, type } = {}) => dispatch => {
  // @todo we are having a fun time with vignette vs sol naming. future refactor make everything SOL
  const searchQuery = { offset, limit, space: type === ruleTypes.VIGNETTE ? 'sol' : type };

  // omitting the name from the search query is equivalent to getting all the results. Only search for valid names
  if (name.length >= 2 && name.trim() !== '') {
    searchQuery.name = name;
  }

  dispatch(webFragmentsLoading());
  getWebFragmentsReq(searchQuery)
    .then(data => {
      dispatch(webFragmentsData({ items: data.items, name, offset: data.offset, limit: data.limit, total: data.total }));
    });
};

// only for retrieval of additional content metadata when details form is loaded as all we have is the content_id
export const getWebFragmentMetadata = (contentId) => dispatch => {
  dispatch(webFragmentsLoading());
  getWebFragmentReq(contentId)
    .then(data => {
      dispatch(selectWebFragment(data));
    });
};

export const selectWebFragment = data => ({
  type: actionType.SELECT_WEB_FRAGMENT,
  data,
});

export const clearSelectedWebFragment = () => ({
  type: actionType.CLEAR_SELECTED_WEB_FRAGMENT,
});
