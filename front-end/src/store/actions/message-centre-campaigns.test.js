import mockAxios from 'axios';

import {
    campaignData,
    campaignLoading,
    getMessageCentreCampaigns,
    setCampaignActive,
    campaignActive,
    setCampaignActiveLoading,
} from './message-centre-campaigns';
import { addAlert } from './alertBanner';

const dispatch = jest.fn();

const OFFSET = 0;
const LIMIT = 30;

const data = {
    total: 1,
    limit: LIMIT,
    offset: OFFSET,
    items: [ {
        id: 1,
        type: 'ABM',
        external_ref: 'PACC',
        channel: 'Mobile',
        language: 'English',
        status: 'New',
        priority: 'Normal',
        start_date: '2020-01-01',
        end_date: '2020-01-01',
        name: `Momentum No Fee PA`,
    } ],
};

describe('Message centre actions', () => {
    afterEach(() => {
        mockAxios.reset();
        dispatch.mockClear();
    });

    describe('Get campaigns', () => {
        test('Successfull call', async() => {
            mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data }));
            await getMessageCentreCampaigns()(dispatch);
            const actions = dispatch.mock.calls;
            expect(actions[0][0]).toStrictEqual(campaignLoading());
            expect(actions[1][0]).toStrictEqual(campaignData(data));
            expect(actions[2][0]).toStrictEqual(campaignLoading(false));
        });

        test('Failed call', async() => {
            mockAxios.get.mockImplementationOnce(() => Promise.reject(new Error('error!')));
            await getMessageCentreCampaigns()(dispatch);
            const actions = dispatch.mock.calls;
            expect(actions[0][0]).toStrictEqual(campaignLoading(true));
            expect(actions[1][0]).toStrictEqual(addAlert({ message: 'Failed to get message centre campaigns.' }));
            expect(actions[2][0]).toStrictEqual(campaignLoading(false));
        });
    });

    describe('Set campaign Active', () => {
        test('Successfull call', async() => {
            mockAxios.patch.mockImplementationOnce(() => Promise.resolve({ data: { ...data.items[0] } }));
            await setCampaignActive()(dispatch);
            const actions = dispatch.mock.calls;
            expect(actions[0][0]).toStrictEqual(setCampaignActiveLoading(true));
            expect(actions[1][0]).toStrictEqual(campaignActive(true, data.items[0]));
            expect(actions[2][0]).toStrictEqual(setCampaignActiveLoading(false));
        });

        test('Failed call', async() => {
            mockAxios.patch.mockImplementationOnce(() => Promise.reject(new Error('error!')));
            await setCampaignActive()(dispatch);
            const actions = dispatch.mock.calls;
            expect(actions[0][0]).toStrictEqual(setCampaignActiveLoading(true));
            expect(actions[1][0]).toStrictEqual(addAlert({ message: 'Failed to update campaign status.' }));
            expect(actions[2][0]).toStrictEqual(setCampaignActiveLoading(false));
        });
    });
});
