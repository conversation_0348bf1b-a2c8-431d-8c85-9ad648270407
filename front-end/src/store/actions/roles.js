import { initialize } from 'redux-form';

import * as rolesApi from '../../api/roles';
import { getTeams } from './teams';
import { addAlert } from '../thunks/alert';
import { addSnackbar } from './snackbar';

import { TEAMS_FLAGS } from '../../constants/permissionsList';

export const actionType = {
  ROLE_LOADING: 'ROLE_LOADING',
  ROLE_DATA: 'ROLE_DATA',
  ROLE_DETAILS_DATA: 'ROLE_DETAILS_DATA',
};

/** Roles - Action Creators **/
export const roleLoading = (status = true) => ({
  type: actionType.ROLE_LOADING,
  data: status,
});

export const roleListData = data => ({
  type: actionType.ROLE_DATA,
  data,
});

export const populateRoleDetails = data => ({
  type: actionType.ROLE_DETAILS_DATA,
  data,
});

/** Roles - Thunks **/
export const initializeRoleForm = (roleId, teamId) => async dispatch => {
  dispatch(getTeams({
    flag: TEAMS_FLAGS.SKIP_ACCESS,
    ...(teamId && { id: teamId }),
  }));

  if (!roleId) {
    dispatch(initialize('roleDetails', { status: true }));
    return;
  }

  const initForm = async() => {
    try {
      dispatch(roleLoading());
      const data = await rolesApi.getRole(roleId);
      dispatch(initialize('roleDetails', data));
      return data;
    } catch {
      dispatch(addAlert({ message: 'An error occurred loading this role.' }));
    }
  };

  const data = await initForm();
  dispatch(roleLoading(false));
  return data;
};

export const getRoles = (query = {}) => async dispatch => {
  dispatch(roleLoading());
  try {
    const roles = await rolesApi.getRoles(query);
    dispatch(roleListData(roles));
  } catch (e) {
    dispatch(addAlert({ message: 'An error occurred loading roles' }));
  } finally {
    dispatch(roleLoading(false));
  }
};

export const saveRoleValues = (id, values) => dispatch => (
  new Promise(async(resolve, reject) => {
    try {
      const role = await (id ? rolesApi.updateRole(id, values) : rolesApi.createRole(values));
      dispatch(addSnackbar({ bold: 'Role', message: ` was ${id ? 'updated' : 'created'} successfully` }));
      dispatch(populateRoleDetails(role));
      resolve();
    } catch (e) {
      dispatch(addAlert({ message: `Failed to ${id ? 'update' : 'create'} role "${values.name}."` }));
      reject(e);
    }
  })
);

export const setRoleActivation = (id, active = true, query) => async dispatch => {
  dispatch(roleLoading());
  try {
    await (active ? rolesApi.activateRole(id) : rolesApi.deactivateRole(id));
    const data = await rolesApi.getRoles(query);
    dispatch(roleListData(data));
    dispatch(addSnackbar({ bold: 'Role', message: ` was ${active ? 'activated' : 'deactivated'} successfully` }));
    dispatch(roleLoading(false));
  } catch (e) {
    dispatch(addAlert({ message: 'Failed to activate/deactivate role.' }));
    dispatch(roleLoading(false));
  }
};
