import {
  actionType,
  getContentTypes,
  getContentItems,
  getContentById,
  contentTypesData,
  contentTypesLoading,
  contentItemsData,
  contentItemsLoading,
  contentItemData,
  contentItemLoading,
  selectWebFragment,
  clearSelectedWebFragment,
  getWebFragments,
} from './content';
import mockAxios from 'axios';

const dispatch = jest.fn();
const testType = 'test-type';
const testId = 'test-id';
const testTypes = [ { id: 'id1' }, { id: 'id2' } ];
const testItems = [ 'test1', 'test2' ];
const testItem = {
  content: {
    name: 'name1',
  },
};

describe('actionType', () => {
  global.snapshot(actionType);
});

describe('getContentTypes()', () => {
  getContentTypes()(dispatch);
  const action = dispatch.mock.calls.pop()[0];
  it('action being dispatched', () => {
    expect(action.type).toBe(actionType.CONTENT_TYPES_LOADING);
  });
});
describe('CONTENT_TYPES_LOADING', () => {
  const action = contentTypesLoading();
  it('action has right format', () => {
    expect(action.type).toBe(actionType.CONTENT_TYPES_LOADING);
    expect(action.data).toBe(true);
  });
});
describe('CONTENT_TYPES_DATA', () => {
  const action = contentTypesData(testTypes);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.CONTENT_TYPES_DATA);
    expect(Object.values(action.data)).toStrictEqual(testTypes);
  });
});

describe('getContentItems()', () => {
  getContentItems()(dispatch);
  const action = dispatch.mock.calls.pop()[0];
  it('action being dispatched', () => {
    expect(action.type).toBe(actionType.CONTENT_ITEMS_LOADING);
  });
});
describe('CONTENT_ITEMS_LOADING', () => {
  const action = contentItemsLoading(testType);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.CONTENT_ITEMS_LOADING);
    expect(action.contentType).toBe(testType);
    expect(action.data).toBe(true);
  });
});
describe('CONTENT_ITEMS_DATA', () => {
  const action = contentItemsData(testType, testItems);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.CONTENT_ITEMS_DATA);
    expect(action.contentType).toBe(testType);
    expect(action.data).toBe(testItems);
  });
});

describe('SELECT_WEB_FRAGMENT', () => {
  const action = selectWebFragment('7777');
  it('action has right format', () => {
    expect(action.type).toBe(actionType.SELECT_WEB_FRAGMENT);
    expect(action.data).toBe('7777');
  });
});
describe('CLEAR_SELECTED_WEB_FRAGMENT', () => {
  const action = clearSelectedWebFragment();
  it('action has right format', () => {
    expect(action.type).toBe(actionType.CLEAR_SELECTED_WEB_FRAGMENT);
  });
});
describe('GET_WEB_FRAGMENTS', () => {
  it('has correctly set defaults', () => {
    const mockDispatch = jest.fn();
    getWebFragments()(mockDispatch);
    expect(mockDispatch).toHaveBeenCalled();
  });

  it('calls with a number longer than 1 character', () => {
    mockAxios.get.mockImplementationOnce(() =>
      Promise.resolve({
        data: {
          items: [
            { web_fragment_id: 3, content: 'bla' },
            { web_fragment_id: 4, content: 'bla' },
            { web_fragment_id: 5, content: 'bla' },
          ],
          total: 4,
        } })
    );
    const mockDispatch = jest.fn();
    getWebFragments({ name: 'sam' })(mockDispatch);
    expect(mockDispatch).toHaveBeenCalled();
    mockAxios.reset();
  });
});

describe('Content actions', () => {
  beforeEach(() => {
    mockAxios.reset();
    dispatch.mockClear();
  });

  describe('getContentById', () => {
    test('successful api call ', async() => {
      mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data: testItem }));
      await getContentById(testType, testId)(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(contentItemLoading());
      expect(actions[1][0]).toStrictEqual(contentItemData(testItem));
    });
  });
});
