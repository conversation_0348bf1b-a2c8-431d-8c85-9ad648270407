import { actionType, getPermissions, permissionLoading, permissionData, populatePermissionDetails } from './permissions';
import mockAxios from 'axios';

const dispatch = jest.fn();
const data = { payload: 'test-payload' };

describe('actionType', () => {
  global.snapshot(actionType);
});

describe('getPermissions()', () => {
  getPermissions()(dispatch);
  const action = dispatch.mock.calls.pop()[0];
  it('action being dispatched', () => {
    expect(action.type).toBe(actionType.PERMISSION_LOADING);
    expect(action.data).toBe(true);
  });

  it('action failed', () => {
    mockAxios.fail = true;
    getPermissions()(dispatch);
    mockAxios.reset();
  });
});

describe('PERMISSION_LOADING', () => {
  const action = permissionLoading();
  it('action has right format', () => {
    expect(action.type).toBe(actionType.PERMISSION_LOADING);
    expect(action.data).toBe(true);
  });
});

describe('PERMISSION_LOADING(false)', () => {
  const action = permissionLoading(false);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.PERMISSION_LOADING);
    expect(action.data).toBe(false);
  });
});

describe('PERMISSION_DATA', () => {
  const action = permissionData(data);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.PERMISSION_DATA);
    expect(action.data).toBe(data);
  });
});

describe('PERMISSION_DETAILS_DATA', () => {
  const action = populatePermissionDetails(data);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.PERMISSION_DETAILS_DATA);
    expect(action.data).toBe(data);
  });
});
