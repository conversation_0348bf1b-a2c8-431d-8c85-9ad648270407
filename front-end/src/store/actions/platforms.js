import { getPlatforms as getPlatformsReq } from '../../api/platforms';

export const actionType = {
  PLATFORMS_LOADING: 'PLATFORMS_LOADING',
  PLATFORMS_DATA: 'PLATFORMS_DATA',
};

export const getPlatforms = (query = {}) => async dispatch => {
  dispatch(platformsLoading());
  try {
    const data = await getPlatformsReq(query);
    dispatch(platformsData(data));
  } catch (err) {
    dispatch(platformsLoading(false));
  }
};

export const platformsLoading = (status = true) => ({
  type: actionType.PLATFORMS_LOADING,
  data: status,
});
export const platformsData = data => ({
  type: actionType.PLATFORMS_DATA,
  data,
});
