import mockAxios from 'axios';

import {
    messageData,
    messageLoading,
    getMessageCentreMessages,
} from './message-centre-messages';
import { addAlert } from './alertBanner';

const dispatch = jest.fn();

const OFFSET = 0;
const LIMIT = 10;

const data = {
    total: 0,
    limit: LIMIT,
    offset: OFFSET,
    items: [],
};

const query = { searchQuery: 'test', searchField: 'cssNumber' };

describe('Message centre actions', () => {
    afterEach(() => {
        mockAxios.reset();
        dispatch.mockClear();
    });

    describe('Get messages', () => {
        test('Successfull call', async() => {
            mockAxios.get.mockImplementationOnce(() => Promise.resolve({ data }));
            await getMessageCentreMessages(query)(dispatch);
            const actions = dispatch.mock.calls;
            expect(actions[0][0]).toStrictEqual(messageLoading());
            expect(actions[1][0]).toStrictEqual(messageData(data));
            expect(actions[2][0]).toStrictEqual(messageLoading(false));
        });

        test('Failed call', async() => {
            mockAxios.get.mockImplementationOnce(() => Promise.reject(new Error('error!')));
            await getMessageCentreMessages(query)(dispatch);
            const actions = dispatch.mock.calls;
            expect(actions[0][0]).toStrictEqual(messageLoading(true));
            expect(actions[1][0]).toStrictEqual(addAlert({ message: 'Failed to get message centre messages.' }));
            expect(actions[2][0]).toStrictEqual(messageLoading(false));
        });
    });
});
