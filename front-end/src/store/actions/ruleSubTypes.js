import {
  getRuleSubTypes as getRuleSubTypesReq,
} from '../../api/ruleSubTypes';

export const actionType = {
  RULE_SUB_TYPE_LOADING: 'RULE_SUB_TYPE_LOADING',
  RULE_SUB_TYPE_DATA: 'RULE_SUB_TYPE_DATA',
};

export const getRuleSubTypes = (query = {}) => async dispatch => {
  dispatch(ruleSubTypesLoading());
  try {
    const data = await getRuleSubTypesReq(query);
    dispatch(ruleSubTypesData(data));
  } catch (err) {
    dispatch(ruleSubTypesLoading(false));
  }
};

export const ruleSubTypesLoading = (status = true) => ({
  type: actionType.RULE_SUB_TYPE_LOADING,
  data: status,
});
export const ruleSubTypesData = data => ({
  type: actionType.RULE_SUB_TYPE_DATA,
  data,
});
