import { actionType, addAlert, removeAlert } from './alertBanner';

describe('alertBanner action types tests', () => {
  it('actionTypes should be correct', () => {
    expect(typeof actionType).toStrictEqual('object');
    expect(actionType.ALERT_ADD).toStrictEqual('ALERT_ADD');
    expect(actionType.ALERT_REMOVE).toStrictEqual('ALERT_REMOVE');
  });

  it('should return a correct add alert action object', () => {
    expect(addAlert({
      message: 'test',
      type: 'warning-test',
    })).toStrictEqual({
      data: {
        message: 'test',
        type: 'warning-test',
      },
      type: 'ALERT_ADD',
    });
  });

  it('should return a correct remove alert action object', () => {
    expect(removeAlert()).toStrictEqual({
      type: 'ALERT_REMOVE',
    });
  });
});
