import { pick } from 'ramda';

import * as teamsApi from '../../api/teams';

import { addSnackbar } from './snackbar';
import { addAlert } from '../thunks/alert';
import { getApplications } from './applications';
import { getPages } from './pages';
import { getContainers } from './containers';
import { getUsers } from './users';
import { getUserData } from './auth';
import { getRuleTypes } from './rule-types';
import { getRuleSubTypes } from './ruleSubTypes';

export const actionType = {
  TEAMS_LOADING: 'TEAMS_LOADING',
  TEAMS_LIST_DATA: 'TEAMS_LIST_DATA',
  TEAM_DETAILS_DATA: 'TEAM_DETAILS_DATA',
  TEAM_OWNERS_DATA: 'TEAM_OWNERS_DATA',
};

export const teamsLoading = (status = true) => ({
  type: actionType.TEAMS_LOADING,
  data: status,
});

export const teamsListData = data => ({
  type: actionType.TEAMS_LIST_DATA,
  data,
});

export const teamOwnersData = data => ({
  type: actionType.TEAM_OWNERS_DATA,
  data,
});

export const teamsDetailsData = data => ({
  type: actionType.TEAM_DETAILS_DATA,
  data,
});

export const editTeamPageOnload = () => async dispatch => {
  dispatch(getUsers());
  dispatch(getContainers());
  dispatch(getApplications());
  dispatch(getPages());
  dispatch(getRuleSubTypes());
  dispatch(getRuleTypes());
  dispatch(getTeams());
};

export const initializeTeamForm = (id, users, teams) => async dispatch => {
  if (!id) {
    const blankFormData = {
      access: { containers: [], ruleSubTypes: [], pages: [] },
      active: true,
      owners: [],
      permissions: [ 'users_view', 'users_manage', 'roles_view', 'roles_manage' ],
    };
    return blankFormData;
  }

  try {
    const currentTeam = teams.find(team => team.id === Number(id));
    currentTeam.owners = users
      .filter(u => u.roles.includes(currentTeam.ownerRoleId))
      // only a subset of user properties are used on edit teams form
      .map(u => pick([ 'id', 'name', 'email', 'sid', 'active' ], u));
    delete currentTeam.ownerRoleId;
    return currentTeam;
  } catch (e) {
    dispatch(addAlert({ message: 'An error occurred loading this team.' }));
  }
};

export const getTeams = (query = {}) => async dispatch => {
  dispatch(teamsLoading());
  try {
    const data = await teamsApi.getTeams(query);
    dispatch(teamsListData(data));
    dispatch(teamsLoading(false));
  } catch (e) {
    dispatch(addAlert({ message: 'Failed to get teams.' }));
    dispatch(teamsLoading(false));
  }
};

export const getTeamOwners = (query = {}) => async dispatch => {
  const data = await teamsApi.getTeamOwners(query);
  dispatch(teamOwnersData(data));
};

export const saveTeamValues = (id, values) => dispatch => (
  new Promise(async(resolve, reject) => {
    try {
      dispatch(teamsLoading());
      const team = await (id ? teamsApi.updateTeam(id, values) : teamsApi.createTeam(values));
      dispatch(getUserData()); // refresh current user's access & permissions after updating team
      dispatch(addSnackbar({ bold: 'Team', message: ` was ${id ? 'updated' : 'created'} successfully` }));
      dispatch(teamsDetailsData(team));
      dispatch(teamsLoading(false));
      resolve();
    } catch (e) {
      dispatch(addAlert({ message: `Failed to ${id ? 'update' : 'create'} team "${values.name}."` }));
      reject(e);
      dispatch(teamsLoading(false));
    }
  })
);

export const setTeamActivation = (id, active, activateChildren, query = {}) => async dispatch => {
  dispatch(teamsLoading());
  try {
    await (active ? teamsApi.activateTeam(id, { activateChildren }) : teamsApi.deactivateTeam(id));
    if (query) {
      const data = await teamsApi.getTeams(query);
      dispatch(addSnackbar({ bold: 'Team', message: ` was ${active ? 'reactivated' : 'deactivated'} successfully` }));
      dispatch(teamsListData(data));
    }
    dispatch(teamsLoading(false));
  } catch (e) {
    if (e?.response?.status === 401) {
      // if user has been deactivated, subsequent api calls will be rejected with 401
      // in which case, logout action is already dispatched, no more processing needed
      return;
    }
    dispatch(addAlert({ message: 'Failed to activate/deactivate team.' }));
    dispatch(teamsLoading(false));
  }
};
