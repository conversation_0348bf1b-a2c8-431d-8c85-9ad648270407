import {
  actionType,
  getAlerts,
  alertLoading,
  alertData,
  populateAlertDetails,
  updateAlertDetails,
  setAlertActive,
  setAlertActiveLoading,
  alertExport,
  alertError,
  deleteAlert,
  exportAlerts,
} from './alerts';
import { addSnackbar } from './snackbar';
import mockAxios from 'axios';

const dispatch = jest.fn();
const data = { payload: 'test-payload' };
window.open = jest.fn();

describe('actionType', () => {
  global.snapshot(actionType);
});

describe('getAlerts()', () => {
  getAlerts()(dispatch);
  const action = dispatch.mock.calls.pop()[0];
  it('action being dispatched', () => {
    expect(action.type).toBe(actionType.ALERT_LOADING);
    expect(action.data).toBe(true);
  });

  it('action failed', () => {
    mockAxios.fail = true;
    getAlerts()(dispatch);
    mockAxios.reset();
  });
});

describe('ALERT_LOADING', () => {
  const action = alertLoading();
  it('action has right format', () => {
    expect(action.type).toBe(actionType.ALERT_LOADING);
    expect(action.data).toBe(true);
  });
});

describe('ALERT_LOADING(false)', () => {
  const action = alertLoading(false);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.ALERT_LOADING);
    expect(action.data).toBe(false);
  });
});

describe('ALERT_DATA', () => {
  const action = alertData(data);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.ALERT_DATA);
    expect(action.data).toBe(data);
  });
});

describe('ALERT_DETAILS_DATA', () => {
  const action = populateAlertDetails(data);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.ALERT_DETAILS_DATA);
    expect(action.data).toBe(data);
  });
});

describe('ALERT_DETAILS_UPDATE', () => {
  const action = updateAlertDetails(data);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.ALERT_DETAILS_UPDATE);
    expect(action.data).toBe(data);
  });
});

describe('Alerts actions', () => {
  beforeEach(() => {
    mockAxios.reset();
    dispatch.mockClear();
  });

  describe('setAlertActive', () => {
    const data = {
      id: 'test-id',
      disabled: true,
    };

    test('successful api call - activate', async() => {
      mockAxios.patch.mockImplementationOnce(() => Promise.resolve({ data }));
      await setAlertActive('test-id', 'published')(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(setAlertActiveLoading());
      expect(actions[1][0]).toStrictEqual({
        type: actionType.ALERT_ACTIVATE,
        data,
      });
      expect(actions[2][0]).toStrictEqual(setAlertActiveLoading(false));
    });

    test('successful api call - deactivate', async() => {
      mockAxios.patch.mockImplementationOnce(() => Promise.resolve({ data }));
      await setAlertActive('test-id', 'published', false)(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(setAlertActiveLoading());
      expect(actions[1][0]).toStrictEqual({
        type: actionType.ALERT_DEACTIVATE,
        data,
      });
      expect(actions[2][0]).toStrictEqual(setAlertActiveLoading(false));
    });

    test('failed api call', async() => {
      mockAxios.patch.mockImplementationOnce(() => Promise.reject(new Error('error!')));
      await setAlertActive('test-id', 'published', true)(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(setAlertActiveLoading(true));
      expect(actions[1][0]).toStrictEqual(alertError());
      expect(actions[2][0]).toStrictEqual(setAlertActiveLoading(false));
    });
  });

  describe('exportAlert', () => {
    test('successful window open', async() => {
      await exportAlerts({}, [ 'external_ref', 'name', 'content_id' ])(dispatch);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(alertExport());
      expect(window.open).toHaveBeenCalled();
    });
  });

  describe('deleteAlert', () => {
    const id = 'test-id';
    const getState = () => {
      return { alerts: { items: { 'test-id': { name: 'test-name' } } } };
    };

    test('successful api call ', async() => {
      mockAxios.delete.mockImplementationOnce(() => Promise.resolve({}));
      await deleteAlert(id)(dispatch, getState);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual({
        type: actionType.ALERT_DELETE,
        data: { id },
      });
      expect(actions[1][0]).toStrictEqual(addSnackbar({ message: `Alert "test-name" deleted successfully.` }));
    });

    test('failed api call', async() => {
      mockAxios.delete.mockImplementationOnce(() => Promise.reject(new Error('error!')));
      await deleteAlert(id)(dispatch, getState);
      const actions = dispatch.mock.calls;
      expect(actions[0][0]).toStrictEqual(alertError());
    });
  });
});
