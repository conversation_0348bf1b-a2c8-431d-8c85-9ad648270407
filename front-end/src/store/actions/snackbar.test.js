import { actionType, addSnackbar, removeSnackbar } from './snackbar';

describe('Snackbar actions', () => {
  it('should create correct action object when creating a snackbar', () => {
    const expected = {
      type: actionType.SNACKBAR_ADD,
      data: { message: 'mock-message', isOpen: true, bold: '' },
    };
    expect(addSnackbar({ message: 'mock-message' })).toStrictEqual(expected);
  });

  it('should create correct action object when removing snackbar', () => {
    const expected = {
      type: actionType.SNACKBAR_REMOVE,
      data: {
        isOpen: false,
      },
    };
    expect(removeSnackbar()).toStrictEqual(expected);
  });
});
