import {
  getOffers as getOfferReq,
  updateOfferStatus as updateOfferStatusReq,
  deleteOffer as deleteOfferReq,
} from '../../api/offers';
import { OFFERS_UPDATE_STATUS } from '../../components/offerManagement/constants';
import { addAlert } from './alertBanner';
import { addSnackbar } from './snackbar';

export const actionType = {
  OFFERS_LOADING: 'OFFERS_LOADING',
  OFFERS_DATA: 'OFFERS_DATA',
  OFFER_DELETE: 'OFFER_DELETE',
  OFFER_ERROR: 'OFFER_ERROR',
  SET_UPDATE_OFFER_STATUS_LOADING: 'SET_UPDATE_OFFER_STATUS_LOADING',
  OFFER_ACTIVATE: 'OFFER_ACTIVATE',
  OFFER_DEACTIVATE: 'OFFER_DEACTIVATE',
};

/** Offers - Thunks **/
export const getOffers = ({ pageNumber = 1, limit = 10, ...query } = {}) => async dispatch => {
  try {
      dispatch(offersLoading());
      const data = await getOfferReq({ offset: (pageNumber - 1) * limit, limit, ...query });
      dispatch(offersData(data));
      dispatch(offersLoading(false));
  } catch (error) {
      dispatch(addAlert({ message: 'Failed to get offers.' }));
      dispatch(offersLoading(false));
  }
};

export const setOfferActive = (id, active = true) => async dispatch => {
  try {
    dispatch(setUpdateOfferStatusLoading());
    const response = await updateOfferStatusReq(id, active ? OFFERS_UPDATE_STATUS.RESUME : OFFERS_UPDATE_STATUS.PAUSE);
    dispatch(updateOfferStatus(active, response.updatedOffer));
    dispatch(setUpdateOfferStatusLoading(false));
  } catch (e) {
    dispatch(offerError());
    dispatch(setUpdateOfferStatusLoading(false));
  }
};

export const deleteOffer = id => async(dispatch, getState) => {
  try {
    await deleteOfferReq(id);
    const { offers: { items } } = getState();
    dispatch({
      type: actionType.OFFER_DELETE,
      data: { id },
    });
    dispatch(addSnackbar({ message: `Offer "${items[id]?.name || id}" deleted successfully.` }));
  } catch (e) {
    dispatch(offerError());
  }
};

/** Offers - Action Creators **/
export const offersLoading = (status = true) => ({
  type: actionType.OFFERS_LOADING,
  data: status,
});

export const offersData = data => ({
  type: actionType.OFFERS_DATA,
  data,
});

export const offerDelete = data => ({
  type: actionType.OFFER_DELETE,
  data,
});

export const setUpdateOfferStatusLoading = (status = true) => ({
  type: actionType.SET_UPDATE_OFFER_STATUS_LOADING,
  data: status,
});

export const updateOfferStatus = (active, response) => ({
  type: actionType[`OFFER_${active ? 'ACTIVATE' : 'DEACTIVATE'}`],
  data: { id: response.data.offer_id, status: response.data.offer_status },
});

export const offerError = data => ({
  type: actionType.OFFER_ERROR,
  data,
});
