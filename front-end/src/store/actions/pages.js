import * as pagesApi from '../../api/pages';
import { initialize } from 'redux-form';
import { getApplications } from './applications';
import { addSnackbar } from './snackbar';
import { addAlert } from './alertBanner';
import { getUserData } from './auth';

export const actionType = {
  PAGE_LOADING: 'PAGE_LOADING',
  PAGE_LIST_DATA: 'PAGE_LIST_DATA',
  PAGE_DETAILS_DATA: 'PAGE_DETAILS_DATA',
  PAGE_SAVING: 'PAGE_SAVING',
  PAGE_SAVED: 'PAGE_SAVED',
};

/** Pages - Action Creators **/
export const pageLoading = (status = true) => ({
  type: actionType.PAGE_LOADING,
  data: status,
});

export const pageData = data => ({
  type: actionType.PAGE_LIST_DATA,
  data,
});

export const populatePageDetails = data => ({
  type: actionType.PAGE_DETAILS_DATA,
  data,
});

/** Pages - Thunks **/
export const initializePageForm = id => async dispatch => {
  dispatch(getPages());
  dispatch(getApplications());
  if (!id) {
    dispatch(initialize('pageDetails', { status: true, name: '' }));
    return;
  }

  try {
    dispatch(pageLoading(true));
    const req = await pagesApi.getPage(id);
    dispatch(populatePageDetails(req.data));
    dispatch(initialize('pageDetails', req.data));
  } catch (e) {
    dispatch(addAlert({ message: 'An error occurred during page creation setup.' }));
  }
  dispatch(pageLoading(false));
};

export const getPages = (query = {}) => async dispatch => {
  dispatch(pageLoading(true));
  try {
    const data = await pagesApi.getPages(query);
    dispatch(pageData(data));
    dispatch(pageLoading(false));
  } catch (e) {
    dispatch(addAlert({ message: 'Failed to get pages.' }));
    dispatch(pageLoading(false));
  }
};

export const savePageValues = (id, values) => dispatch => {
  return new Promise(async(resolve, reject) => {
    dispatch({ type: actionType.PAGE_SAVING });
    try {
      const savedPage = id ? await pagesApi.patchPage(id, values) : await pagesApi.postPage(values);
      dispatch(addSnackbar({ message: id ? ` was updated successfully` : ` was created successfully`, bold: 'Page' }));
      dispatch({ type: actionType.PAGE_SAVED });
      dispatch(populatePageDetails(savedPage.data));
      dispatch(getUserData()); // container access have changed, refresh current user's access in frontend store
      resolve();
    } catch (e) {
      dispatch(addAlert({ message: `Failed to save page "${values.name}."` }));
      reject(e);
    }
  });
};

export const setPageActivation = (id, query, active = true) => async dispatch => {
  dispatch(pageLoading());
  try {
    await (active ? pagesApi.activatePage(id) : pagesApi.deactivatePage(id));
    const data = await pagesApi.getPages(query);
    dispatch(pageData(data));
    dispatch(pageLoading(false));
    dispatch(addSnackbar({ bold: 'Page', message: active ? ` was reactivated successfully.` : ` was deactivated successfully.` }));
  } catch (e) {
    dispatch(addAlert({ message: 'Failed to activate/deactivate container.' }));
    dispatch(pageLoading(false));
  }
};
