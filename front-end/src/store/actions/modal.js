import { getWebFragments } from '../actions/content';

export const actionType = {
  MODAL_CLOSE_ALL: 'MODAL_CLOSE_ALL',
  MODAL_OPEN_CONTENT: 'MODAL_OPEN_CONTENT',
  MODAL_OPEN_FRAGMENT: 'MODAL_OPEN_FRAGMENT',
};

/** Modal - Action Creators **/
export const closeAllModals = () => ({
  type: actionType.MODAL_CLOSE_ALL,
});

export const openContentModal = () => ({
  type: actionType.MODAL_OPEN_CONTENT,
});

export const openFragmentModal = () => ({
  type: actionType.MODAL_OPEN_FRAGMENT,
});

export const openFragmentModalAndLoadFragments = (type) => (dispatch) => {
  dispatch(openFragmentModal());
  dispatch(getWebFragments({ type }));
};
