import { actionType, getUsers, userLoading, userListData, populateUserDetails } from './users';
import mockAxios from 'axios';

const dispatch = jest.fn();
const data = { payload: 'test-payload' };

describe('actionType', () => {
  global.snapshot(actionType);
});

describe('getUsers()', () => {
  getUsers()(dispatch);
  const action = dispatch.mock.calls.pop()[0];
  it('action being dispatched', () => {
    expect(action.type).toBe(actionType.USER_LOADING);
    expect(action.data).toBe(true);
  });

  it('action failed', () => {
    mockAxios.fail = true;
    getUsers()(dispatch);
    mockAxios.reset();
  });
});

describe('USER_LOADING', () => {
  const action = userLoading();
  it('action has right format', () => {
    expect(action.type).toBe(actionType.USER_LOADING);
    expect(action.data).toBe(true);
  });
});

describe('USER_LOADING(false)', () => {
  const action = userLoading(false);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.USER_LOADING);
    expect(action.data).toBe(false);
  });
});

describe('USER_LIST_DATA', () => {
  const action = userListData(data);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.USER_LIST_DATA);
    expect(action.data).toBe(data);
  });
});

describe('USER_DETAILS_DATA', () => {
  const action = populateUserDetails(data);
  it('action has right format', () => {
    expect(action.type).toBe(actionType.USER_DETAILS_DATA);
    expect(action.data).toBe(data);
  });
});
