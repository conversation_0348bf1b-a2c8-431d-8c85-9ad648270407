import { initialize } from 'redux-form';

import * as applicationsApi from '../../api/applications';

import { addSnackbar } from './snackbar';
import { addAlert } from './alertBanner';
import { getRuleTypes } from './rule-types';
import { getPlatforms } from './platforms';
import { getTeams } from './teams';
import { getRuleSubTypes } from './ruleSubTypes';

import { CONTENT_SPACE_ID } from '../../constants';
import { TEAMS_FLAGS } from '../../constants/permissionsList';

export const actionType = {
  APPLICATION_LOADING: 'APPLICATION_LOADING',
  APPLICATION_LIST_DATA: 'APPLICATION_LIST_DATA',
  APPLICATION_DETAILS_DATA: 'APPLICATION_DETAILS_DATA',
};

/** Applications - Action Creators **/
export const applicationLoading = (status = true) => ({
  type: actionType.APPLICATION_LOADING,
  data: status,
});

export const applicationListData = data => ({
  type: actionType.APPLICATION_LIST_DATA,
  data,
});

export const applicationDetailsData = (data) => ({
  type: actionType.APPLICATION_DETAILS_DATA,
  data,
});

/** Applications - Thunks **/
export const initializeApplicationForm = (id, teamId) => async dispatch => {
  const accessQuery = { active: true, flag: TEAMS_FLAGS.SKIP_ACCESS };
  dispatch(getTeams(teamId ? { ...accessQuery, id: teamId } : accessQuery));
  dispatch(getRuleTypes());
  dispatch(getRuleSubTypes());
  dispatch(getPlatforms());

  if (!id) {
    dispatch(initialize('appDetails', { status: true, contentful_space: CONTENT_SPACE_ID }));
    return;
  }

  dispatch(applicationLoading(true));
  try {
    const data = await applicationsApi.getApplication(id);
    dispatch(applicationDetailsData(data));
    dispatch(initialize('appDetails', data));
  } catch (e) {
    dispatch(addAlert({ message: 'An error occurred during page initialization.' }));
    dispatch(applicationLoading(false));
  }
};

export const getApplications = (query = {}) => async dispatch => {
  dispatch(applicationLoading(true));
  try {
    const data = await applicationsApi.getApplications(query);
    dispatch(applicationListData(data));
  } catch (err) {
    dispatch(applicationLoading(false));
  }
};

export const saveApplicationValues = (id, values) => async dispatch => {
  dispatch(applicationLoading());
  try {
    const payload = {
      team_id: values.team_id,
      applicationId: values.applicationId,
      description: values.description || '',
      contentful_space: values.contentful_space || '',
      name: values.name,
      platformIds: values.platformIds || [],
      ruleTypeIds: values.ruleTypeIds || [],
      ruleSubTypeIds: values.ruleSubTypeIds || [],
      status: values.status,
    };
    await (id ? applicationsApi.updateApplication(id, payload) : applicationsApi.createApplication(payload));
    dispatch(addSnackbar({ message: id ? ` was updated successfully` : ` was created successfully`, bold: 'Application' }));
  } catch (e) {
    dispatch(addAlert({ message: `Failed to save application "${values.name}." ${e?.response?.data?.message}` }));
    dispatch(applicationLoading(false));
    return (e);
  }
};

export const setApplicationActivation = (id, active = true, activateChildren = false, query) => async dispatch => {
  dispatch(applicationLoading());
  try {
    await (active ? applicationsApi.activateApplication(id, { activateChildren }) : applicationsApi.deactivateApplication(id));
    const data = await applicationsApi.getApplications(query);
    dispatch(applicationListData(data));
    dispatch(applicationLoading(false));
    dispatch(addSnackbar({ bold: 'Application', message: active ? ` was reactivated successfully.` : ` was deactivated successfully.` }));
  } catch (e) {
    dispatch(addAlert({ message: 'Failed to activate/deactivate application.' }));
    dispatch(applicationLoading(false));
  }
};
