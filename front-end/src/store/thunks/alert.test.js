import {
  addAlert,
} from './alert';

const TEST_MSG = 'test msg';
const thunk = addAlert({ message: TEST_MSG });
const dispatch = jest.fn();
const getState = () => ({});

describe('alert thunk tests', () => {
  beforeEach(() => {
    dispatch.mockClear();
  });

  it('should return a correct add alert action object', () => {
    thunk(dispatch, getState);
    const dispatchedAction = dispatch.mock.calls[0][0];
    expect(dispatchedAction).toStrictEqual({
      data: { message: TEST_MSG, type: 'alert' },
      type: 'ALERT_ADD',
    });
  });

  it('should ignore alert if user was kicked out mid-session', () => {
    thunk(dispatch, () => ({ authenticated: { loggedOut: true } }));
    expect(dispatch).not.toHaveBeenCalled();
  });
});
