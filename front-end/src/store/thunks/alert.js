import { addAlert as addAlertBanner } from '../actions/alertBanner';

// https://redux.js.org/usage/writing-logic-thunks
// thunk action creator
export function addAlert({ message }) {
  // thunk function
  return async function addAlertThunk(dispatch, getState) {
    // flag in store to indicate user was kicked out mid-session
    const isUnauthed = getState().authenticated?.loggedOut;
    !isUnauthed && dispatch(addAlertBanner({ message }));
  };
};
