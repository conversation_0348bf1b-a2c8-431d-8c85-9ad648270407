import { createStore, applyMiddleware, compose } from 'redux';
import thunk from 'redux-thunk';

import { setupInterceptors } from '../api';
import rootReducer from '../store/reducers';

const configureStore = () => {
  // enable in dev env for redux tracing
  // const composeEnhancers = (window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ &&
  //     window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({ trace: true, traceLimit: 25 })) ||
  //   compose;
  const composeEnhancers = window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose;
  const store = createStore(rootReducer, composeEnhancers(applyMiddleware(thunk)));
  setupInterceptors(store);
  return store;
};

export default configureStore;
