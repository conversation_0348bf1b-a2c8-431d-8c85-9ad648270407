import vignette, { vignetteDetails } from './vignettes';
import { vignetteLoading, vignetteData, populateVignetteDetails, updateVignetteDetails } from '../actions/vignettes';

const mockData = {
  total: 2524,
  offset: 0,
  limit: 10,
  items: [
    {
      id: 86,
      rule_id: 'jAYDAdpTyjir',
      name: 'asdasdasdasdasdasdasd',
      status: 'submitted',
      start_date: '2019-10-02T04:00:00.000Z',
      end_date: '2019-10-31T04:00:00.000Z',
      urgent: true,
      disabled: false,
      deleted: false,
      message_type: null,
      system_message: null,
      created_by: 'Oleg',
      updated_by: 'Oleg',
      created_at: '2019-10-01T20:13:08.376Z',
      updated_at: '2019-10-01T20:13:08.376Z',
      contents: [
        {
          page: 'pigeon-qa-activities',
          container: 'SOL_container',
          content_space: 'db',
          content_type: 'webfragment',
          content_id: '7048',
          container_disabled: false,
          subject: null,
        },
      ],
      targeting: {
        campaign_name: null,
        language_code: 'en',
        country_code: 'CA',
      },
    },
  ],
};

const detailsData = {
  id: 87,
  rule_id: 's3eEXpqoPb58',
  name: 'Mikes Campaign',
  status: 'draft',
  start_date: '2019-10-01T04:00:00.000Z',
  end_date: '2019-10-09T04:00:00.000Z',
  urgent: false,
  disabled: false,
  deleted: false,
  message_type: null,
  system_message: null,
  created_by: 's5891700',
  updated_by: 's5891700',
  created_at: '2019-10-02T03:05:02.003Z',
  updated_at: '2019-10-02T03:05:02.003Z',
  contents: [
    {
      page: 'activities',
      container: 'solcontainer',
      content_space: 'db',
      content_type: 'webfragment',
      content_id: '7048',
      container_disabled: false,
      subject: null,
    },
    {
      page: 'accounts',
      container: 'solcontainer',
      content_space: 'db',
      content_type: 'webfragment',
      content_id: '7048',
      container_disabled: false,
      subject: null,
    },
  ],
  targeting: {
    campaign_name: null,
    language_code: 'en',
    country_code: 'CA',
    platforms: [
      {
        platform_name: 'web',
        platform_version: null,
      },
    ],
    products: [
      {
        attribute_mode: 'or',
        attribute_type: 'BU',
        attribute_values: [
          'B:',
          'CASL:',
        ],
      },
      {
        attribute_mode: 'not',
        attribute_type: 'BU',
        attribute_values: [
          'R:',
          'SDBI:SLP',
        ],
      },
      {
        attribute_mode: 'and',
        attribute_type: 'REGS',
        attribute_values: [
          'B',
          'I',
        ],
      },
      {
        attribute_mode: 'not',
        attribute_type: 'REGS',
        attribute_values: [
          'S',
        ],
      },
      {
        attribute_mode: 'and',
        attribute_type: 'PR1',
        attribute_values: [
          'Banking:DDA:DDA:DDA:',
          'Banking:DDA:DDA:DDA:CA',
          'Borrowing:Mortgage:MOR:MOR:MT',
          'Investing:IP:IP:IPP:',
          'BRK:Brokerage:SDBI:SL:',
        ],
      },
      {
        attribute_mode: 'not',
        attribute_type: 'PR1',
        attribute_values: [
          'Banking:DDA:DDA:DDA:SC',
          'Borrowing:Mortgage:MOR:MOR:',
          'Investing:BNS:IP:NRS:',
          'BRK:Brokerage:SDBI:SL:1',
        ],
      },
      {
        attribute_mode: 'or',
        attribute_type: 'PROV',
        attribute_values: [
          'ON',
        ],
      },
    ],
  },
};

describe('vignette reducer', () => {
  let state;

  it('Initial State', () => {
    state = vignette(state, {});
    expect(state).toStrictEqual({});
  });

  it('Loading', () => {
    state = vignette(state, vignetteLoading());
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got data', () => {
    state = vignette(state, vignetteData(mockData));
    expect(state).toHaveProperty('data');
    expect(state.data).toBe(mockData);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });
});

describe('vignetteDetails reducer', () => {
  let state;

  it('Initial State', () => {
    state = vignetteDetails(state, {});
    expect(state).toMatchSnapshot();
  });

  it('Add Values To Vignette Details Form', () => {
    state = vignetteDetails(state, populateVignetteDetails(detailsData));
    expect(state).toMatchSnapshot();
  });

  it('Update Vignette Details', () => {
    state = vignetteDetails(state, updateVignetteDetails(detailsData));
    expect(state).toMatchSnapshot();
  });

  it('Populate Vignette Details (targeted)', () => {
    detailsData.targeting.campaign_name = 'hey there';
    state = vignetteDetails(state, populateVignetteDetails(detailsData));
    expect(state).toMatchSnapshot();
  });

  it('Populate Vignette Details (no products)', () => {
    delete detailsData.targeting.products;
    state = vignetteDetails(state, populateVignetteDetails(detailsData));
    expect(state).toMatchSnapshot();
  });
});
