import reducer from './ruleSubTypes';
import actions from '../actions';

describe('RuleSubTypes reducer test', () => {
  it('should return default state', () => {
    const state = reducer(undefined, undefined);
    expect(state).toStrictEqual({
      items: null,
      isLoading: false,
    });
  });
  it('should set loading state', () => {
    const state = reducer({}, {
      type: actions.RULE_SUB_TYPE_LOADING,
      data: true,
    });
    expect(state.isLoading).toStrictEqual(true);
  });
  it('should update data', () => {
    const state = reducer({}, {
      type: actions.RULE_SUB_TYPE_DATA,
      data: [],
    });
    expect(state.items).toStrictEqual([]);
  });
});
