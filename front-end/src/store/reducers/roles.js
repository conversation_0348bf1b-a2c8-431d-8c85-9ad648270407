import actions from '../actions';

const initialState = {
  items: null,
  isLoading: false,
  pagination: {
    offset: 0,
    total: null,
    limit: 0,
  },
};

export default (state = initialState, action = {}) => {
  switch (action.type) {
    case actions.ROLE_LOADING:
      return { ...state, isLoading: action.data };
    case actions.ROLE_DETAILS_DATA:
      return {
        ...state,
        items: {
          ...state.items,
          [action.data.id]: {
            ...action.data,
          },
        },
      };
    case actions.ROLE_DATA:
      return {
        items: action.data.items,
        pagination: {
          offset: action.data.offset,
          total: action.data.total,
          limit: action.data.limit,
        },
        isLoading: false,
      };
    default:
      return state;
  }
};
