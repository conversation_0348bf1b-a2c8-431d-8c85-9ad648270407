import actions from '../actions';

export const permissionDetails = (state = { values: {} }, action = {}) => {
  switch (action.type) {
    case actions.PERMISSION_DETAILS_DATA:
      return {
        ...state,
        values: {
          ...state.values,
          ...action.data,
        },
      };
    default:
      return state;
  }
};

export default (state = {}, action = {}) => {
  switch (action.type) {
    case actions.PERMISSION_LOADING:
      return { ...state, isLoading: action.data };
    case actions.PERMISSION_DATA:
      return { items: action.data, isLoading: false };
    default:
      return state;
  }
};
