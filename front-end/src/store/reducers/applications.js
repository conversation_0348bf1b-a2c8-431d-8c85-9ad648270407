import actions from '../actions';

const initialState = {
  isLoading: false,
  items: [],
  pagination: {},
};

const applications = (state = initialState, action = {}) => {
  switch (action.type) {
    case actions.APPLICATION_LOADING:
      return { ...state, isLoading: action.data };
    case actions.APPLICATION_LIST_DATA:
      return {
        ...state,
        items: action.data.items,
        pagination: {
          offset: action.data.offset,
          total: action.data.total,
          limit: action.data.limit,
        },
        isLoading: false,
      };
    case actions.APPLICATION_DETAILS_DATA:
      return {
        ...state,
        items: {
          ...state.items,
          [action.data.id]: { ...action.data },
        },
        isLoading: false,
      };
    default:
      return state;
  }
};

export default applications;
