import actions from '../actions/index.js';

const initialState = {
    items: [],
    pagination: {
        offset: 0,
        total: null,
        limit: 0,
    },
};

export default (state = initialState, action = {}) => {
    switch (action.type) {
        case actions.MESSAGE_CENTRE_MESSAGE_LOADING:
            return { ...state, isLoading: action.data };
        case actions.MESSAGE_CENTRE_MESSAGE_DATA:
            return {
                items: action.data.items,
                pagination: {
                    offset: action.data.offset,
                    total: action.data.total,
                    limit: action.data.limit,
                },
                isLoading: false,
            };
        default:
            return state;
    }
};
