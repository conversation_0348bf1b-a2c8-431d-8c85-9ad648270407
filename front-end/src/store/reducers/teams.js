/* eslint-disable camelcase */
import actions from '../actions';

const initialState = {
  isLoading: false,
  items: null,
};

export default (state = initialState, action = {}) => {
  switch (action.type) {
    case actions.TEAMS_LOADING:
      return {
        ...state,
        isLoading: action.data,
      };
    case actions.TEAMS_LIST_DATA:
      return {
        ...state,
        items: action.data.items,
        pagination: {
          offset: action.data.offset,
          total: action.data.total,
          limit: action.data.limit,
        },
        isLoading: false,
      };
    case actions.TEAM_OWNERS_DATA:
      return {
        ...state,
        teamOwners: action.data,
      };
    case actions.TEAM_DETAILS_DATA:
      return {
        ...state,
        items: {
          ...state.items,
          [action.data._id]: {
            ...action.data,
          },
        },
      };
    default:
      return state;
  }
};
