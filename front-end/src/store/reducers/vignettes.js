import actions from '../actions';

export const vignetteDetails = (state = { values: {} }, action = {}) => {
  const { data, type } = action;
  switch (type) {
    case actions.VIGNETTE_DETAILS_DATA:
      const values = {
        ...state.values,
        ...data,
        start_date: data.start_date,
        end_date: data.end_date,
        campaign_name: data.targeting.campaign_name,
        language: data.targeting.language_code,
        type: data.targeting.campaign_name ? 'targeted' : 'mass',
      };
      if (data.contents && data.contents.length) {
        values.container = data.contents[0].container;
        values.pages = data.contents.map(i => i.page);
        values.content_id = data.contents[0].content_id;
        values.subject_line = data.contents[0].subject;
      }
      const { products } = data.targeting;
      if (products) {
        products.forEach(p => {
          if (p.attribute_type === 'BU') {
            if (p.attribute_mode === 'not') {
              values.BU_NOT = p.attribute_values;
            } else {
              values.BU = p.attribute_values;
              values.BU_RADIO = 'BU_' + p.attribute_mode.toUpperCase();
            }
          }

          if (p.attribute_type === 'DEVC') {
            if (p.attribute_mode === 'not') {
              values.DEVC_NOT = p.attribute_values;
            } else {
              values.DEVC = p.attribute_values;
              values.DEVC_RADIO = 'DEVC_' + p.attribute_mode.toUpperCase();
            }
          }
          if (p.attribute_type === 'REGS') {
            if (p.attribute_mode === 'not') {
              values.REGS_NOT = p.attribute_values;
            } else {
              values.REGS = p.attribute_values;
              values.REGS_RADIO = 'REGS_' + p.attribute_mode.toUpperCase();
            }
          }
          const keys = [ 'PR1', 'PR2', 'PR3', 'PR4' ];
          const dataKeys = { PR1: 'bankingProducts', PR2: 'borrowingProducts', PR3: 'investingRetail', PR4: 'investingWealth' };
          keys.forEach((value) => {
            if (data.metadata && p.attribute_type === 'PR') {
              const key = p.attribute_mode === 'not' ? `${value}_NOT` : value;
              values[key] = [];
              const advancedTargetedProducts = data.metadata[dataKeys[value]] || [];
              (p.attribute_values || []).forEach(
                (code) => {
                  const productMatch = advancedTargetedProducts.filter(m => m.code === code).length > 0;
                  if (productMatch) {
                    values[key].push(code);
                  }
                });
            }
          });
          if (p.attribute_type === 'PR' && p.attribute_mode !== 'not') {
            values.PR_RADIO = `PR_${p.attribute_mode.toUpperCase()}`;
          }

          if (p.attribute_type === 'PROV') {
            values.PROV = p.attribute_values;
          }
        });
      }
      return {
        ...state,
        values,
      };
    case actions.VIGNETTE_DETAILS_UPDATE:
      return {
        ...state,
        values: {
          ...state.values,
          ...data,
        },
      };
    default:
      return state;
  }
};

export default (state = {}, action = {}) => {
  switch (action.type) {
    case actions.VIGNETTE_LOADING:
      return { ...state, isLoading: action.data };
    case actions.VIGNETTE_DATA:
      return { data: action.data, isLoading: false };
    default:
      return state;
  }
};
