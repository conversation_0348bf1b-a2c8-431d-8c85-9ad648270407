import reducer from './rule-types';
import { actionType } from '../actions/rule-types';

describe('Rule Types reducer', () => {
  it('should set the state when loading indicator is changed', () => {
    const newState = reducer({ isLoading: false }, {
      type: actionType.RULE_TYPE_LOADING,
      data: true,
    });
    expect(newState.isLoading).toStrictEqual(true);
  });

  it('should set the items when they are available', () => {
    const mockItems = [ {
      id: 123,
      name: 'rule-type-mock-name',
    } ];
    const newState = reducer({ isLoading: false }, {
      type: actionType.RULE_TYPE_DATA,
      data: mockItems,
    });
    expect(newState.items).toStrictEqual(mockItems);
  });

  it('should return the same state if action isn\'t known', () => {
    const intialState = {
      isLoading: false,
      items: [ {
        id: 123,
        name: 'rule-type-mock-name',
      } ],
    };
    const newState = reducer(intialState, {
      type: '___ACTION_UNKNOWN',
    });
    expect(newState).toStrictEqual(intialState);
  });
});
