import messages from './message-centre-messages';
import {
  messageLoading,
  messageData,
} from '../actions/message-centre-messages';
import testData from '../../../../src/__mocks__/mockMessageDetail';

const listTestData = {
  items: [
    testData,
  ],
  total: 100,
  offset: 1,
  limit: 10,
};

const stateTransformOfData = {
  items: [ testData ],
  pagination: {
    total: listTestData.total,
    offset: listTestData.offset,
    limit: listTestData.limit,
  },
  isLoading: false,
};

describe('messages reducer', () => {
  let state;

  it('Initial State', () => {
    state = messages(state, {});
    expect(state).toStrictEqual({
      items: [],
      pagination: {
        limit: 0,
        offset: 0,
        total: null,
      },
    });
  });

  it('Loading', () => {
    state = messages(state, messageLoading());
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got data', () => {
    state = messages(state, messageData(listTestData));
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual(stateTransformOfData);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Load again', () => {
    state = messages(state, messageLoading());
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual({
      ...stateTransformOfData,
      isLoading: true,
    });
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Load error', () => {
    state = messages(state, messageLoading(false));
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual(stateTransformOfData);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });
});
