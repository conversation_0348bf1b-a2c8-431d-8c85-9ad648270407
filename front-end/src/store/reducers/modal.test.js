import modal from './modal';
import { closeAllModals, openContentModal, openFragmentModal } from '../actions/modal';
import { modalTypes } from '../../constants';

describe('modal reducer', () => {
  let state;

  it('Initial State', () => {
    state = modal(state, {});
    expect(state).toBeNull();
  });

  it('default params', () => {
    state = modal();
    expect(state).toBeNull();
  });

  it('Open Content Modal', () => {
    state = modal(state, openContentModal());
    expect(state).toBe(modalTypes.CONTENT);
  });

  it('Open open fragment', () => {
    state = modal(state, openFragmentModal());
    expect(state).toBe(modalTypes.FRAGMENT);
  });

  it('Close All', () => {
    state = modal(state, closeAllModals());
    expect(state).toBeNull();
  });
});
