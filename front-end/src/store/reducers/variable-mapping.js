import { isEmpty } from 'ramda';
import actions from '../actions';

const initialState = {};

export default (state = initialState, action = {}) => {
  switch (action.type) {
    case actions.VARS_MAPPING_LOADING: return { ...state, isLoading: action.data };
    case actions.VARS_ACTIVE_SET_UPDATED: return { ...state, activeSet: action.data };
    case actions.VARS_EDITED_SET_UPDATED: return { ...state, editedSet: action.data };
    case actions.VARS_DRAFT_SET_UPDATED: return {
      ...state,
      draftSet: action.data,
      hasDraft: !!action.data && !isEmpty(action.data),
    };
    case actions.VARS_PENDING_SET_UPDATED: return {
      ...state,
      pendingSet: action.data,
      hasPending: !!action.data && !isEmpty(action.data),
    };
    case actions.VARS_TYPES_LOADED: return { ...state, types: action.data };
    case actions.VARS_APPROVERS_LOADED: return { ...state, approvers: action.data };
    default: return state;
  }
};
