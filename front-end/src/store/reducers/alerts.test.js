import alerts, { alertDetails } from './alerts';
import {
  alertActive,
  alertLoading,
  alertData,
  populateAlertDetails,
  setAlertActiveLoading,
  updateAlertDetails,
  alertDelete,
} from '../actions/alerts';

const testData = {
  'id': 'string',
  'created_at': '2018-09-19T15:00:37.327Z',
  'updated_at': '2018-09-19T15:00:37.327Z',
  'name': 'Interac outage alert 2018-01-01',
  'start_at': '2018-09-19T15:00:37.327Z',
  'end_at': '2018-09-19T15:00:37.327Z',
  'content_space': '4szkx38resvm',
  'content_type': 'creditCardOffer',
  'content_id': '2vZDr0xewgYwaWmywGICw4',
  'anonymous': true,
  'authenticated': true,
  'container': 'PRE_LOGIN',
  'platforms': [
    'ios',
    'android',
  ],
  'app_version': '1.0.42',
  'status': 'draft',
  'disabled': false,
  'created_by': 'string',
  'updated_by': 'string',
  platforms_targeting: [ {
    platform: 'ios',
    items: [
      { app_version: '>=1.2.3', device_model: 'iphone11', os_version: '13.2.3 - 13.3.4' },
      { device_model: 'iphone10' },
    ],
    v: 1,
  } ],
};

const listTestData = {
  items: [
    testData,
  ],
  total: 100,
  offset: 1,
  limit: 10,
};

const stateTransformOfData = {
  items: {
    [testData.id]: testData,
  },
  pagination: {
    total: listTestData.total,
    offset: listTestData.offset,
    limit: listTestData.limit,
  },
  isLoading: false,
};

describe('alerts reducer', () => {
  let state;

  it('Initial State', () => {
    state = alerts(state, {});
    expect(state).toStrictEqual({
      items: {},
      pagination: {
        limit: 0,
        offset: 0,
        total: null,
      },
    });
  });

  it('Loading', () => {
    state = alerts(state, alertLoading());
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got data', () => {
    state = alerts(state, alertData(listTestData));
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual(stateTransformOfData);
  });

  it('Load again', () => {
    state = alerts(state, alertLoading());
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual({
      ...stateTransformOfData,
      isLoading: true,
    });
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Load error', () => {
    state = alerts(state, alertLoading(false));
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual(stateTransformOfData);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Loading - Set alert active/inactive', () => {
    state = alerts(state, setAlertActiveLoading());
    expect(state).toHaveProperty('isSetAlertActiveLoading');
    expect(state.isSetAlertActiveLoading).toBe(true);
  });

  it('Set campaign active/inactive', () => {
    state = alerts(state, alertActive(true, { id: 'string', disabled: false }));
    expect(state).toHaveProperty('items');
    expect(state).toHaveProperty('isSetAlertActiveLoading');
    expect(state).toStrictEqual({
      ...stateTransformOfData,
      isSetAlertActiveLoading: true,
    });
  });

  it('Done loading - Set campaign active/inactive', () => {
    state = alerts(state, setAlertActiveLoading(false));
    expect(state).toHaveProperty('isSetAlertActiveLoading');
    expect(state.isSetAlertActiveLoading).toBe(false);
  });

  it('delete alert ', () => {
    state = alerts(stateTransformOfData, alertDelete({ id: 'string' }));
    expect(state.items).toStrictEqual({});
  });

  it('test default values', () => {
    state = alerts();
    expect(state.items).toStrictEqual({});
  });

  it('test default values - alertDetails', () => {
    state = alertDetails();
    expect(state).toStrictEqual({ values: {} });
  });
});

describe('alertDetails reducer', () => {
  let state;

  it('Initial State', () => {
    state = alertDetails(state, {});
    expect(state).toMatchSnapshot();
  });

  it('Add Values To Alert Details Form', () => {
    state = alertDetails(state, updateAlertDetails(testData));
    expect(state).toMatchSnapshot();
  });

  it('Populate Alert Details', () => {
    state = alertDetails(state, populateAlertDetails(testData));
    expect(state).toMatchSnapshot();
  });
});
