import applicationsReducer from './applications';
import { applicationLoading, applicationListData, applicationDetailsData } from '../actions/applications';

const testData = {
  items: [ {
    'id': 1,
    'name': 'Nova Mobile',
    'applicationId': 'nova',
  } ],
  pagination: {},
};

const initialState = {
  isLoading: false,
  items: [],
  pagination: {},
};

describe('applications reducer', () => {
  let state;

  it('Initial State', () => {
    state = applicationsReducer(state);
    expect(state).toStrictEqual(initialState);
  });

  it('Loading', () => {
    state = applicationsReducer(state, applicationLoading(true));
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got data', () => {
    state = applicationsReducer(state, applicationListData(testData));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual(testData.items);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Load again', () => {
    state = applicationsReducer(state, applicationLoading(true));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual(testData.items);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Load error', () => {
    state = applicationsReducer(state, applicationLoading(false));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual(testData.items);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Populate Application Details', () => {
    state = applicationsReducer(state, applicationDetailsData(testData.items[0]));
    expect(state).toMatchSnapshot();
  });
});
