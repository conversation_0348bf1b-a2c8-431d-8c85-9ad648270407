import actions from '../actions';
import { mapIdTo<PERSON>ey } from '../../constants';

export default (state = {}, action = {}) => {
  let newState;
  switch (action.type) {
    case actions.CONTENT_TYPES_LOADING:
      return {
        ...state,
        isLoading: action.data,
      };
    case actions.CONTENT_TYPES_DATA:
      return {
        ...state,
        isLoading: false,
        types: {
          ...mapIdToKey(action.data),
        },
      };
    case actions.CONTENT_ITEM_LOADING:
      return {
        ...state,
        isLoading: action.data,
      };
    case actions.CONTENT_ITEM_DATA:
      return {
        ...state,
        isLoading: false,
        contentItem: action.data,
      };
    case actions.CONTENT_ITEMS_LOADING:
      newState = {
        ...state,
        types: {
          ...state.types,
        },
      };
      newState.types[action.contentType] = {
        ...state.types[action.contentType],
        isLoading: action.data,
      };
      return newState;
    case actions.CONTENT_ITEMS_DATA:
      newState = {
        ...state,
        types: {
          ...state.types,
        },
      };
      newState.types[action.contentType] = {
        ...state.types[action.contentType],
        ...action.data,
        isLoading: false,
      };
      newState.types[action.contentType].items.forEach((i) => {
        i.name = i.content[state.types[action.contentType].display_field];
      });
      return newState;
    case actions.WEB_FRAGMENTS_LOADING:
      return {
        ...state,
        webFragments: {
          ...state.webFragments,
          isLoading: true },
      };
    case actions.WEB_FRAGMENTS_DATA:
      return {
        ...state,
        webFragments: { ...state.webFragments, ...action.data, isLoading: false },
      };
    case actions.SELECT_WEB_FRAGMENT:
      return {
        ...state,
        webFragments: {
          ...state.webFragments,
          selected: { ...action.data },
        },
      };
    case actions.CLEAR_SELECTED_WEB_FRAGMENT:
      return {
        ...state,
        webFragments: {},
      };
    default:
      return state;
  }
};
