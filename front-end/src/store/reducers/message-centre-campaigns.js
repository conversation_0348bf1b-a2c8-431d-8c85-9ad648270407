import actions from '../actions';

const initialState = {
  items: [],
  pagination: {
    offset: 0,
    total: null,
    limit: 0,
  },
};

export default (state = initialState, action = {}) => {
  switch (action.type) {
    case actions.MESSAGE_CENTRE_CAMPAIGN_LOADING:
      return { ...state, isLoading: action.data };
    case actions.MESSAGE_CENTRE_SET_CAMPAIGN_ACTIVE_LOADING:
      return { ...state, isSetCampaignActiveLoading: action.data };
    case actions.MESSAGE_CENTRE_CAMPAIGN_DATA:
      return {
        items: action.data.items,
        pagination: {
          offset: action.data.offset,
          total: action.data.total,
          limit: action.data.limit,
        },
        isLoading: false,
      };
    case actions.MESSAGE_CENTRE_CAMPAIGN_ACTIVATE:
    case actions.MESSAGE_CENTRE_CAMPAIGN_DEACTIVATE:
      // update the disabled property in place on the activated/deactivated rule
      return {
        ...state,
        items: state.items.map((item) => {
          if (item.id === action.data.id) item.msg_status = action.data.msg_status;
          return item;
        }),
      };
    default:
      return state;
  }
};
