import content from './content';
import {
  contentTypesLoading,
  contentTypesData,
  contentItemLoading,
  contentItemData,
  contentItemsLoading,
  contentItemsData,
  actionType,
} from '../actions/content';

const contentTypes = [
  {
    'id': 'offerBenefitsOfferPoint',
    'name': 'Benefit Item',
    'display_field': 'name',
  },
  {
    'id': 'alert',
    'name': 'Alert',
    'display_field': 'name',
  },
  {
    'id': 'offerBenefits',
    'name': 'Benefits List',
    'display_field': 'name',
  },
];

const contentItemMock = {
  content: {
    name: 'name1',
  },
};

const contentMock = {
  items: [
    {
      'id': '1qPO9I8x5KcKQO2YimaUIu',
      'type': 'alert',
      'language': 'en-US',
      'created_at': '2018-09-25T18:34:20.083Z',
      'updated_at': '2018-09-25T18:34:20.083Z',
      'content': {
        'name': '20180920 - Alert demo',
        'title': 'Alert title english',
        'message': 'An alert in english',
      },
    },
    {
      'id': '1wFGoXv0lWgyA4iCO6UWEs',
      'type': 'alert',
      'language': 'en-US',
      'created_at': '2018-09-25T18:33:31.436Z',
      'updated_at': '2018-09-25T18:33:31.436Z',
      'content': {
        'name': 'Demo Alert',
        'title': 'Demo Alert September 19',
        'message': 'This alert is to demo the alert api in action.',
      },
    },
    {
      'id': '7aOy2zXupiig82w8KmYi8q',
      'type': 'alert',
      'language': 'en-US',
      'created_at': '2018-09-25T18:33:57.269Z',
      'updated_at': '2018-09-25T18:33:57.269Z',
      'content': {
        'name': 'Sample Alert #1',
        'title': 'Sorry, Scotiabank mobile banking is currently unavailable.',
        'message': 'We\'re working on getting it back up and running. Please try again later.',
      },
    },
  ],
};

describe('content reducer', () => {
  let state;

  it('Initial State', () => {
    state = content(state, {});
    expect(state).toStrictEqual({});
  });

  it('default  params', () => {
    state = content();
    expect(state).toStrictEqual({});
  });

  it('Loading Types', () => {
    state = content(state, contentTypesLoading());
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got Types data', () => {
    state = content(state, contentTypesData(contentTypes));
    expect(state).toHaveProperty('types');
    expect(Object.values(state.types)).toStrictEqual(contentTypes);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Loading Item', () => {
    state = content(state, contentItemLoading());
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got Item data', () => {
    state = content(state, contentItemData(contentItemMock));
    expect(state).toHaveProperty('contentItem');
    expect(state.contentItem).toStrictEqual(contentItemMock);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Loading Items', () => {
    state = content(state, contentItemsLoading('alert'));
    expect(state.types.alert).toHaveProperty('isLoading');
    expect(state.types.alert.isLoading).toBe(true);
  });

  it('Got Items data', () => {
    state = content(state, contentItemsData('alert', contentMock));
    expect(state.types.alert).toHaveProperty('items');
    expect(state.types.alert.items[0].name).toBe(state.types.alert.items[0].content.name);
    expect(state.types.alert.items).toMatchSnapshot();
  });

  it('web fragments loading', () => {
    expect(content({}, {
      type: actionType.WEB_FRAGMENTS_LOADING,
    })).toStrictEqual({
      webFragments: {
        isLoading: true,
      },
    });
  });

  it('select web fragments', () => {
    expect(content({ webFragments: {} }, {
      type: actionType.SELECT_WEB_FRAGMENT,
      data: {
        webFragmentSpecificProp: 1,
      },
    })).toStrictEqual({ webFragments: { selected: { webFragmentSpecificProp: 1 } } });
  });

  it('web fragments loaded, data set', () => {
    expect(content({}, {
      type: actionType.WEB_FRAGMENTS_DATA,
      data: {
        webFragmentSpecificProp: 1,
      },
    })).toStrictEqual({
      webFragments: {
        isLoading: false,
        webFragmentSpecificProp: 1,
      },
    });
  });

  it('web fragments clear', () => {
    expect(content({}, {
      type: actionType.CLEAR_SELECTED_WEB_FRAGMENT,
    })).toStrictEqual({
      webFragments: {},
    });
  });
});
