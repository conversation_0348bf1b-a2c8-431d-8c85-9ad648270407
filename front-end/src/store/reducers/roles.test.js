import roles from './roles';
import { roleLoading, roleListData, populateRoleDetails } from '../actions/roles';

const testData = {
  'id': 1,
  'name': 'Admin',
  'permissions': [],
};

describe('roles reducer', () => {
  let state;
  let pagination = {
    limit: 0,
    offset: 0,
    total: null,
  };
  it('Initial State', () => {
    state = roles(state, {});
    expect(state).toStrictEqual({ items: null, isLoading: false, pagination });
  });

  it('Loading', () => {
    state = roles(state, roleLoading());
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got data', () => {
    state = roles(state, roleListData({ items: [ testData ] }));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual([ testData ]);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Load again', () => {
    state = roles(state, roleLoading());
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual([ testData ]);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Load error', () => {
    state = roles(state, roleLoading(false));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual([ testData ]);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Populate Role Details', () => {
    state = roles(state, populateRoleDetails(testData));
    expect(state).toMatchSnapshot();
  });
});
