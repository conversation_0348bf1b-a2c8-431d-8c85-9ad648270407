/* eslint-disable camelcase */
import actions from '../actions';
import { jsonParseOr } from '../../utils';

const initialState = {
  isLoading: false,
  items: [],
  pagination: {},
};

export default (state = initialState, action = {}) => {
  switch (action.type) {
    case actions.CONTAINER_LOADING:
      return { ...state, isLoading: action.data };
    case actions.CONTAINER_LIST_DATA:
      return {
        items: [ ...action.data.items ].map(container => ({
          ...container,
          content_type: jsonParseOr(container?.content_type || ''),
        })),
        pagination: {
          offset: action.data.offset,
          total: action.data.total,
          limit: action.data.limit,
        },
        isLoading: false,
      };
    case actions.CONTAINER_DETAILS_DATA:
      return {
        ...state,
        items: [ ...state.items ].map(container => {
          if (container.id === action.data?.id) {
            return {
              ...action.data,
              content_type: jsonParseOr(action.data?.content_type),
            };
          }
          return container;
        }),
        loading: false,
      };
    default:
      return state;
  }
};
