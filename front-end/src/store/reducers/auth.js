import actions from '../actions';
import { mapArrayToTrueProps } from '../../constants';
import { mapPermissionsToPermissionLevels } from '../../constants/permissionsList';

export const authenticated = (state = {}, action = {}) => {
  switch (action.type) {
    case actions.USER_UNAUTH:
      return {
        loggedOut: true,
      };
    case actions.USER_AUTH:
      const permissions = mapArrayToTrueProps(action.data?.permissions || []);
      return {
        ...action.data,
        permissions,
        permissionLevels: mapPermissionsToPermissionLevels(permissions),
      };
    default:
      return state;
  }
};
