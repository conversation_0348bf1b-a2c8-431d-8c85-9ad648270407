import reducer from './toaster';
import { addToast, flushToasts, removeToast } from '../actions/toaster';

describe('toasts reducer', () => {
  it('should add a new toast into state', () => {
    const oldState = [];
    const newState = reducer(oldState, addToast({}));
    expect(newState).toHaveLength(1);
  });

  it('should remove all toasts from state', () => {
    const oldState = [ {}, {}, {} ];
    const newState = reducer(oldState, flushToasts());
    expect(newState).toHaveLength(0);
  });

  it('should remove one toast by key', () => {
    const oldState = [ {
      key: 1,
    }, {
      key: 2,
    }, {
      key: 3,
    } ];
    const expectedState = [
      {
        key: 1,
      },
      {
        key: 3,
      },
    ];
    const newState = reducer(oldState, removeToast(2));
    expect(newState).toHaveLength(2);
    expect(newState).toStrictEqual(expectedState);
  });
});
