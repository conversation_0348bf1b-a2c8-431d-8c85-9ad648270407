import actions from '../actions';
import { mapArrayToTrueProps, mapIdToKey } from '../../constants/index.js';
import { convertFromSemver } from '../../components/versionTargeting/constants';
import { getCampaignTypeUsingExternalRef } from '../../utils/index.js';

export const campaignDetails = (state = { values: {} }, action = {}) => {
  switch (action.type) {
    case actions.CAMPAIGN_DETAILS_DATA:
      const { productBook, ...allData } = action.data;
      const campaignType = getCampaignTypeUsingExternalRef(action.data.external_ref);

      // For wealth campaigns, use languages from by_demographic if mass_targeting.languages is missing
      let languagesArray = allData?.mass_targeting?.languages || [];
      if (campaignType === 'wealth' && allData?.mass_targeting?.by_demographic?.languages) {
        // If languages is empty at root level but exists in by_demographic, use those
        if (!languagesArray.length && allData.mass_targeting.by_demographic.languages.length) {
          languagesArray = allData.mass_targeting.by_demographic.languages;
        }
      }

      const data = {
        ...state,
        values: {
          ...state.values,
          ...allData,
          start_at: action.data.start_at,
          end_at: action.data.end_at,
          platforms: mapArrayToTrueProps(action.data.platforms),
          platforms_targeting: convertFromSemver(action.data.platforms_targeting),
          type: campaignType,
          mass_targeting: { ...allData.mass_targeting,
            device_lock: allData?.mass_targeting?.device_lock === undefined ? 'none' : allData.mass_targeting.device_lock ? 'on' : 'off',
            enrollment_status: mapArrayToTrueProps(allData?.mass_targeting?.enrollment_status || []),
            languages: mapArrayToTrueProps(languagesArray),
          },
        },
      };
      return data;
    case actions.CAMPAIGN_DETAILS_UPDATE:
      return {
        ...state,
        values: {
          ...state.values,
          ...action.data,
        },
      };
    default:
      return state;
  }
};

const initialState = {
  items: {},
  pagination: {
    offset: 0,
    total: null,
    limit: 0,
  },
};

export default (state = initialState, action = {}) => {
  switch (action.type) {
    case actions.CAMPAIGN_LOADING:
      return { ...state, isLoading: action.data };
    case actions.SET_CAMPAIGN_ACTIVE_LOADING:
      return { ...state, isSetCampaignActiveLoading: action.data };
    case actions.CAMPAIGN_DATA:
      return {
        items: mapIdToKey(action.data.items),
        pagination: {
          offset: action.data.offset,
          total: action.data.total,
          limit: action.data.limit,
        },
        isLoading: false,
      };
    case actions.CAMPAIGN_ACTIVATE:
    case actions.CAMPAIGN_DEACTIVATE:
      // update the disabled property in place on the activated/deactivated rule
      return {
        ...state,
        items: {
          ...state.items,
          [action.data.id]: {
            ...state.items[action.data.id],
            disabled: action.data.disabled,
          },
        },
      };
    case actions.CAMPAIGN_DELETE:
      const copiedItems = { ...state.items };
      delete copiedItems[action.data.id];
      return {
        ...state,
        items: copiedItems,
      };
    default:
      return state;
  }
};
