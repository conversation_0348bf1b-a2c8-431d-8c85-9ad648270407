import actions from '../actions';

const initialState = {
  items: [],
  pagination: {
    offset: 0,
    total: null,
    limit: 0,
  },
};

export default (state = initialState, action = {}) => {
  switch (action.type) {
    case actions.OFFERS_LOADING:
      return { ...state, isLoading: action.data };
    case actions.SET_UPDATE_OFFER_STATUS_LOADING:
      return { ...state, isUpdateOfferStatusLoading: action.data };
    case actions.OFFERS_DATA:
      return {
        items: action.data.data,
        pagination: {
          offset: action.data.offset,
          total: action.data.total,
          limit: action.data.limit,
        },
        isLoading: false,
      };
    case actions.OFFER_ACTIVATE:
    case actions.OFFER_DEACTIVATE:
      return {
        ...state,
        items: state.items.map((item) => {
          if (item.offer_id === action.data.id) {
            item.offer_status = action.data.status;
          }
          return item;
        }),
      };
    case actions.OFFER_DELETE:
      return {
        ...state,
        items: state.items.filter((item) => {
          return item.offer_id !== action.data.id;
        }),
      };
    default:
      return state;
  }
};
