import campaigns, { campaignDetails } from './campaigns';
import {
  campaignLoading,
  campaignData,
  populateCampaignDetails,
  campaignActive,
  setCampaignActiveLoading,
  updateCampaignDetails,
  campaignDelete,
} from '../actions/campaigns';

const testData = {
  'id': 'string',
  'created_at': '2018-09-28T16:36:53.753Z',
  'updated_at': '2018-09-28T16:36:53.753Z',
  'name': 'Diamond VISA promotion 2022-09',
  'start_at': '2018-09-28T16:36:53.753Z',
  'end_at': '2018-09-28T16:36:53.753Z',
  'content_space': '4szkx38resvm',
  'content_type': 'creditCardOffer',
  'content_id': '2vZDr0xewgYwaWmywGICw4',
  'container': 'my-activity',
  'pages': [
    'accounts',
    'login',
  ],
  'external_ref': 'OID0001',
  'platforms': [
    'ios',
    'android',
  ],
  'urgent': false,
  'status': 'status',
  'disabled': false,
  'created_by': 'string',
  'updated_by': 'string',
  'mass_targeting': {
    'v': 1,
    'by_product': {
      'any_of': [ {
        'ownership': 'R',
        'code': 'BSA',
        'sub_code': 'MP',
      },
      {
        'ownership': 'R',
        'code': 'BSA',
        'sub_code': 'P1',
      },
      {
        'ownership': 'R',
        'code': 'SAV',
        'sub_code': 'MS',
      },
      {
        'ownership': 'R',
        'code': 'BSA',
        'sub_code': 'P3',
      },
      {
        'ownership': 'R',
        'code': 'BSA',
        'sub_code': 'P4',
      },
      {
        'ownership': 'R',
        'code': 'BSA',
        'sub_code': 'P5',
      },
      {
        'ownership': 'R',
        'code': 'BSA',
        'sub_code': 'P2',
      } ],
    },
  },
  productBook: [ {
    BU: {
      ownership: 'B',
      code: 'AAA',
      sub_code: 'BBB',
    },
  } ],
  platforms_targeting: [ {
    platform: 'ios',
    items: [
      { app_version: '>=1.2.3', device_model: 'iphone11', os_version: '13.2.3 - 13.3.4' },
      { device_model: 'iphone10' },
    ],
    v: 1,
  } ],
};

const listTestData = {
  items: [
    testData,
  ],
  total: 100,
  offset: 1,
  limit: 10,
};

const stateTransformOfData = {
  items: {
    [testData.id]: testData,
  },
  pagination: {
    total: listTestData.total,
    offset: listTestData.offset,
    limit: listTestData.limit,
  },
  isLoading: false,
};

describe('campaigns reducer', () => {
  let state;

  it('Initial State', () => {
    state = campaigns(state, {});
    expect(state).toStrictEqual({
      items: {},
      pagination: {
        limit: 0,
        offset: 0,
        total: null,
      },
    });
  });

  it('Loading', () => {
    state = campaigns(state, campaignLoading());
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got data', () => {
    state = campaigns(state, campaignData(listTestData));
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual(stateTransformOfData);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Load again', () => {
    state = campaigns(state, campaignLoading());
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual({
      ...stateTransformOfData,
      isLoading: true,
    });
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Load error', () => {
    state = campaigns(state, campaignLoading(false));
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual(stateTransformOfData);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Loading - Set campaign active/inactive', () => {
    state = campaigns(state, setCampaignActiveLoading());
    expect(state).toHaveProperty('isSetCampaignActiveLoading');
    expect(state.isSetCampaignActiveLoading).toBe(true);
  });

  it('Set campaign active/inactive', () => {
    state = campaigns(state, campaignActive(true, { id: 'string', disabled: false }));
    expect(state).toHaveProperty('items');
    expect(state).toHaveProperty('isSetCampaignActiveLoading');
    expect(state).toStrictEqual({
      ...stateTransformOfData,
      isSetCampaignActiveLoading: true,
    });
  });

  it('Done loading - Set campaign active/inactive', () => {
    state = campaigns(state, setCampaignActiveLoading(false));
    expect(state).toHaveProperty('isSetCampaignActiveLoading');
    expect(state.isSetCampaignActiveLoading).toBe(false);
  });

  it('delete campaign', () => {
    state = campaigns(state, campaignData(listTestData));
    state = campaigns(state, campaignDelete({ id: 'string' }));
    expect(state.items).toStrictEqual({});
  });

  it('default params', () => {
    state = campaigns();
    expect(state).toStrictEqual({
      items: {},
      pagination: {
        limit: 0,
        offset: 0,
        total: null,
      },
    });
  });
});

describe('campaignDetails reducer', () => {
  let state;

  it('Initial State', () => {
    state = campaignDetails(state, {});
    expect(state).toMatchSnapshot();
  });

  it('default params State', () => {
    state = campaignDetails();
    expect(state).toMatchSnapshot({ values: {} });
  });

  it('Add Values To Campaign Details Form', () => {
    state = campaignDetails(state, updateCampaignDetails(testData));
    expect(state).toMatchSnapshot();
  });

  it('Populate Campaign Details', () => {
    state = campaignDetails(state, populateCampaignDetails(testData));
    expect(state).toMatchSnapshot();
  });

  it('Populate Campaign Details with device lock undefined', () => {
    state = campaignDetails(state, populateCampaignDetails({ ...testData, mass_targeting: { ...testData.mass_targeting } }));
    expect(state).toMatchSnapshot();
  });

  it('Populate Campaign Details with device lock is true', () => {
    state = campaignDetails(state, populateCampaignDetails({ ...testData, mass_targeting: { ...testData.mass_targeting, device_lock: true } }));
    expect(state).toMatchSnapshot();
  });

  it('Populate Campaign Details with device lock is false', () => {
    state = campaignDetails(state, populateCampaignDetails({ ...testData, mass_targeting: { ...testData.mass_targeting, device_lock: false } }));
    expect(state).toMatchSnapshot();
  });

  it('Populate Campaign Details - mass', () => {
    state = campaignDetails(state, populateCampaignDetails({ ...testData, external_ref: 'MASS' }));
    expect(state).toMatchSnapshot();
  });
});
