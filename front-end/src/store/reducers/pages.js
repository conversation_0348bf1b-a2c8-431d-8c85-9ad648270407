import actions from '../actions';

const initialState = {
  isLoading: false,
  items: [],
  pagination: {},
};

const pages = (state = initialState, action = {}) => {
  switch (action.type) {
    case actions.PAGE_LOADING:
      return { ...state, isLoading: action.data };
    case actions.PAGE_LIST_DATA:
      return {
        ...state,
        items: action.data.items,
        pagination: {
          offset: action.data.offset,
          total: action.data.total,
          limit: action.data.limit,
        },
        isLoading: false,
      };
    case actions.PAGE_DETAILS_DATA:
      return {
        ...state,
        items: {
          ...state.items,
          [action.data.id]: { ...action.data },
        },
      };
    default:
      return state;
  }
};

export default pages;
