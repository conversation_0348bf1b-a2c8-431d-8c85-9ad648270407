import teams from './teams';
import { teamsLoading, teamsListData, teamOwnersData, teamsDetailsData } from '../actions/teams';

const testData = {
    id: 1,
    name: 'Pigeon Team',
    description: 'Team added at DB implementation',
    active: true,
    applications: [ 'nova' ],
    functionality: [ '1' ],
    access: { },
    applicationIds: undefined,
  };

describe('teams reducer', () => {
  let state;
  it('Initial State', () => {
    state = teams(state, {});
    expect(state).toStrictEqual({ items: null, isLoading: false });
  });
  it('Initial State default', () => {
    state = teams();
    expect(state).toStrictEqual({ items: null, isLoading: false });
  });

  it('Loading', () => {
    state = teams(state, teamsLoading());
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got data', () => {
    state = teams(state, teamsListData({ items: [ testData ] }));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual([ testData ]);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Got data - team owner', () => {
    const teamOwners = { id: 1 };
    state = teams(state, teamOwnersData(teamOwners));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual([ testData ]);
    expect(state).toHaveProperty('isLoading');
    expect(state.teamOwners).toStrictEqual(teamOwners);
    expect(state.isLoading).toBe(false);
  });

  it('team details', () => {
    state = teams(state, teamsDetailsData({ _id: '_id', name: 'test' }));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual({ '0': { 'access': {}, 'active': true, 'applicationIds': undefined, 'applications': [ 'nova' ], 'description': 'Team added at DB implementation', 'functionality': [ '1' ], 'id': 1, 'name': 'Pigeon Team' }, '_id': { '_id': '_id', 'name': 'test' } });
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });
});
