import actions from '../actions';
import { mapArrayToTrueProps, mapIdToKey } from '../../constants';
import { convertFromSemver } from '../../components/versionTargeting/constants';

export const alertDetails = (state = { values: {} }, action = {}) => {
  switch (action.type) {
    case actions.ALERT_DETAILS_DATA:
      return {
        ...state,
        values: {
          ...state.values,
          ...action.data,
          start_at: action.data.start_at,
          end_at: action.data.end_at,
          platforms_targeting: convertFromSemver(action.data.platforms_targeting),
          platforms: mapArrayToTrueProps(action.data.platforms),
        },
      };
    case actions.ALERT_DETAILS_UPDATE:
      return {
        ...state,
        values: {
          ...state.values,
          ...action.data,
        },
      };
    default:
      return state;
  }
};

const initialState = {
  items: {},
  pagination: {
    offset: 0,
    total: null,
    limit: 0,
  },
};

export default (state = initialState, action = {}) => {
  switch (action.type) {
    case actions.ALERT_LOADING:
      return { ...state, isLoading: action.data };
    case actions.SET_ALERT_ACTIVE_LOADING:
      return { ...state, isSetAlertActiveLoading: action.data };
    case actions.ALERT_DATA:
      return {
        items: mapIdToKey(action.data.items),
        pagination: {
          offset: action.data.offset,
          total: action.data.total,
          limit: action.data.limit,
        },
        isLoading: false,
      };
    case actions.ALERT_ACTIVATE:
    case actions.ALERT_DEACTIVATE:
      // update the disabled property in place on the activated/deactivated rule
      return {
        ...state,
        items: {
          ...state.items,
          [action.data.id]: {
            ...state.items[action.data.id],
            disabled: action.data.disabled,
          },
        },
      };
    case actions.ALERT_DELETE:
      const copiedItems = { ...state.items };
      delete copiedItems[action.data.id];
      return {
        ...state,
        items: copiedItems,
      };
    default:
      return state;
  }
};
