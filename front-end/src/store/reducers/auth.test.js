import { authenticated } from './auth';
import { userAuth, userUnauth } from '../actions/auth';

describe('authenticated reducer', () => {
  let state;

  it('Initial State', () => {
    state = authenticated(state, {});
    expect(state).toStrictEqual({});
  });

  it('Unauth', () => {
    state = authenticated(state, userUnauth());
    expect(state).toStrictEqual({ loggedOut: true });
  });

  it('Persist', () => {
    state = authenticated(state, {});
    expect(state).toStrictEqual({ loggedOut: true });
  });

  it('Auth', () => {
    state = authenticated(state, userAuth({
      permissions: [ 'teams_view', 'teams_view_super' ],
    }));
    expect(state).toStrictEqual({
      permissions: {
        teams_view: true,
        teams_view_super: true,
      },
      permissionLevels: {
        canEditAllApplications: false,
        canEditAllContainers: false,
        canEditAllPages: false,
        canEditAllRoles: false,
        canEditAllTeams: false,
        canEditAllUsers: false,
        canEditOwnTeam: false,
        canEditOwnTeamApplications: false,
        canEditOwnTeamContainers: false,
        canEditOwnTeamPages: false,
        canEditOwnTeamRoles: false,
        canEditOwnTeamUsers: false,
        canViewAllApplications: false,
        canViewAllContainers: false,
        canViewAllPages: false,
        canViewAllRoles: false,
        canViewAllTeams: true,
        canViewAllUsers: false,
        canViewOwnTeam: true,
        canViewOwnTeamApplications: false,
        canViewOwnTeamContainers: false,
        canViewOwnTeamPages: false,
        canViewOwnTeamRoles: false,
        canViewOwnTeamUsers: false,
      },
    });
  });

  it('default params', () => {
    state = authenticated();
    expect(state).toStrictEqual({});
  });

  it('Auth - default', () => {
    state = authenticated(state, userAuth());
    expect(state).toStrictEqual({
      permissions: {},
      permissionLevels: {
        canEditAllApplications: false,
        canEditAllContainers: false,
        canEditAllPages: false,
        canEditAllRoles: false,
        canEditAllTeams: false,
        canEditAllUsers: false,
        canEditOwnTeam: false,
        canEditOwnTeamApplications: false,
        canEditOwnTeamContainers: false,
        canEditOwnTeamPages: false,
        canEditOwnTeamRoles: false,
        canEditOwnTeamUsers: false,
        canViewAllApplications: false,
        canViewAllContainers: false,
        canViewAllPages: false,
        canViewAllRoles: false,
        canViewAllTeams: false,
        canViewAllUsers: false,
        canViewOwnTeam: false,
        canViewOwnTeamApplications: false,
        canViewOwnTeamContainers: false,
        canViewOwnTeamPages: false,
        canViewOwnTeamRoles: false,
        canViewOwnTeamUsers: false,
      },
    });
  });
});
