import { combineReducers } from 'redux';
import { reducer as formReducer } from 'redux-form';

import { authenticated } from './auth';

import alerts, { alertDetails } from './alerts';
import applications from './applications';
import campaigns, { campaignDetails } from './campaigns';
import vignettes, { vignetteDetails } from './vignettes';
import users from './users';
import variableMapping from './variable-mapping';
import containers from './containers';
import pages from './pages';
import roles from './roles';
import permissions, { permissionDetails } from './permissions';
import ruleTypes from './rule-types';
import ruleSubTypes from './ruleSubTypes';
import platforms from './platforms';
import teams from './teams';
import content from './content';
import modal from './modal';
import toasts from './toaster';
import alertBanner from './alertBanner';
import snackbar from './snackbar';
import messageCentreCampaigns from './message-centre-campaigns';
import messageCentreMessages from './message-centre-messages';
import offers from './offers';

const rootReducer = combineReducers({
  form: formReducer.plugin({
    alertDetails,
    campaignDetails,
    vignetteDetails,
    permissionDetails,
  }),
  authenticated,
  alerts,
  applications,
  campaigns,
  rules: vignettes,
  users,
  variableMapping,
  permissions,
  containers,
  pages,
  roles,
  content,
  modal,
  toasts,
  teams,
  ruleTypes,
  ruleSubTypes,
  platforms,
  alertBanner,
  snackbar,
  messageCentreCampaigns,
  messageCentreMessages,
  offers,
});

export default rootReducer;
