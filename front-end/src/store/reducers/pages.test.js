import pages from './pages';
import { pageLoading, pageData, populatePageDetails } from '../actions/pages';

const testData = {
  items: [ {
    'id': 1,
    'name': 'activity',
  } ],
  pagination: {},
};

const initialState = {
  isLoading: false,
  items: [],
  pagination: {},
};

describe('pages reducer', () => {
  let state;

  it('Initial State', () => {
    state = pages(state);
    expect(state).toStrictEqual(initialState);
  });

  it('Loading', () => {
    state = pages(state, pageLoading());
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got data', () => {
    state = pages(state, pageData(testData));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual(testData.items);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Load again', () => {
    state = pages(state, pageLoading());
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual(testData.items);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Load error', () => {
    state = pages(state, pageLoading(false));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual(testData.items);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Populate Page Details', () => {
    state = pages(state, populatePageDetails(testData.items[0]));
    expect(state).toMatchSnapshot();
  });
});
