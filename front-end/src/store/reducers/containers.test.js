import containers from './containers';
import { containerLoading, containerData, populateContainerDetails } from '../actions/containers';

const testData = {
  items: [
    {
      'content_type': '',
      'id': 1,
      'name': 'my-activity',
      'pages': [ 1, 2 ],
    },
    {
      'content_type': '',
      'id': 2,
      'name': 'offers-and-programs',
      'pages': [ 1, 2 ],
    },
  ],
  pagination: {},
};

describe('containers reducer', () => {
  let state;

  it('Initial State', () => {
    state = containers(state, {});
    expect(state).toStrictEqual({ isLoading: false, items: [], pagination: {} });
  });

  it('default params', () => {
    state = containers();
    expect(state).toStrictEqual({ isLoading: false, items: [], pagination: {} });
  });

  it('Loading', () => {
    state = containers(state, containerLoading());
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got data', () => {
    state = containers(state, containerData(testData));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual(testData.items);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Load again', () => {
    state = containers(state, containerLoading());
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual(testData.items);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Load error', () => {
    state = containers(state, containerLoading(false));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual(testData.items);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Populate container details', () => {
    state = containers(state, populateContainerDetails(testData.items[0]));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual(testData.items);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });
});
