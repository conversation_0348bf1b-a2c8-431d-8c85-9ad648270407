import reducer from './snackbar';
import { actionType } from '../actions/snackbar';

describe('Snackbar reducer test', () => {
  it('should add a new snackbar', () => {
    const mockSnackbar = {
      content: 'mock-content',
    };
    const state = reducer({}, {
      type: actionType.SNACKBAR_ADD,
      data: mockSnackbar,
    });
    expect(state).toStrictEqual(mockSnackbar);
  });
  it('should close the snackbar', () => {
    const state = reducer({}, {
      type: actionType.SNACKBAR_REMOVE,
      data: {
        isOpen: false,
      },
    });
    expect(state.isOpen).toStrictEqual(false);
  });
});
