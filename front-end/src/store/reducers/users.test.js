import users from './users';
import { userLoading, userListData, populateUserDetails } from '../actions/users';

const testData = {
  'id': 1,
  'name': '<PERSON><PERSON>',
  'email': '<EMAIL>',
  'sid': 's1081456',
  'updated_at': '2019-02-12T02:21:12.003Z',
  'active': true,
  'roles': [
    1,
    2,
    3,
    4,
  ],
};

describe('users reducer', () => {
  let state;
  const pagination = {
    limit: 0,
    offset: 0,
    total: null,
  };
  it('Initial State', () => {
    state = users(state, {});
    expect(state).toStrictEqual({ items: null, isLoading: false, pagination });
  });

  it('Loading', () => {
    state = users(state, userLoading());
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got data', () => {
    state = users(state, userListData({ items: [ testData ] }));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual([ testData ]);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Load again', () => {
    state = users(state, userLoading());
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual([ testData ]);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Load error', () => {
    state = users(state, userLoading(false));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual([ testData ]);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Populate User Details', () => {
    state = users(state, populateUserDetails(testData));
    expect(state).toMatchSnapshot();
  });
});
