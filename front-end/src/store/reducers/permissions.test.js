import permissions from './permissions';
import { permissionLoading, permissionData } from '../actions/permissions';

const testData = {
  'name': 'admin',
};

describe('permissions reducer', () => {
  let state;

  it('Initial State', () => {
    state = permissions(state, {});
    expect(state).toStrictEqual({});
  });

  it('Loading', () => {
    state = permissions(state, permissionLoading());
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got data', () => {
    state = permissions(state, permissionData([ testData ]));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual([ testData ]);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Load again', () => {
    state = permissions(state, permissionLoading());
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual([ testData ]);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Load error', () => {
    state = permissions(state, permissionLoading(false));
    expect(state).toHaveProperty('items');
    expect(state.items).toStrictEqual([ testData ]);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });
});
