import campaigns from './message-centre-campaigns';
import { campaignLoading, campaignData } from '../actions/message-centre-campaigns';
import testData from '../../../../src/__mocks__/mockMessageCentreCampaign';

const listTestData = {
  items: [ testData ],
  total: 100,
  offset: 1,
  limit: 10,
};

const stateTransformOfData = {
  items: [ testData ],
  pagination: {
    total: listTestData.total,
    offset: listTestData.offset,
    limit: listTestData.limit,
  },
  isLoading: false,
};

describe('campaigns reducer', () => {
  let state;

  it('Initial State', () => {
    state = campaigns(state, {});
    expect(state).toStrictEqual({
      items: [],
      pagination: {
        limit: 0,
        offset: 0,
        total: null,
      },
    });
  });

  it('Loading', () => {
    state = campaigns(state, campaignLoading());
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got data', () => {
    state = campaigns(state, campaignData(listTestData));
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual(stateTransformOfData);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Load again', () => {
    state = campaigns(state, campaignLoading());
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual({
      ...stateTransformOfData,
      isLoading: true,
    });
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Load error', () => {
    state = campaigns(state, campaignLoading(false));
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual(stateTransformOfData);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });
});
