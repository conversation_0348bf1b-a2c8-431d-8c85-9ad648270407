import reducer from './variable-mapping';
import actions from '../actions';

describe('variable mapping reducers', () => {
  const state = {};
  const mockSet = {
    type: 'VARS_ACTIVE_SET_UPDATED',
    data: {
      variable_set_id: 106,
      created_at: '2021-12-08T20:27:59.884Z',
      created_by: 's1990028',
      status: 'active',
      updated_at: '2021-12-08T20:36:13.916Z',
      updated_by: 's1990028',
      description: 'test',
      approver_sid: 's1990028',
      variables: [
        {
          variable_campaign: 'test',
          variable_template: 'SOLUI_TEST_END',
          variable_type: 'date',
          variable_type_label: 'Date',
        },
      ],
    },
  };

  it('mapping loading', () => {
    const action = { type: actions.VARS_MAPPING_LOADING, data: true };
    const newState = reducer(state, action);
    expect(newState).toStrictEqual({ ...state, isLoading: true });
  });

  it('active set updated', () => {
    const action = { type: actions.VARS_ACTIVE_SET_UPDATED, data: mockSet };
    const newState = reducer(state, action);
    expect(newState).toStrictEqual({ ...state, activeSet: mockSet });
  });

  it('edited set updated', () => {
    const action = { type: actions.VARS_EDITED_SET_UPDATED, data: mockSet };
    const newState = reducer(state, action);
    expect(newState).toStrictEqual({ ...state, editedSet: mockSet });
  });

  it('draft set updated', () => {
    let action = { type: actions.VARS_DRAFT_SET_UPDATED, data: mockSet };
    let newState = reducer(state, action);
    expect(newState).toStrictEqual({ ...state, draftSet: mockSet, hasDraft: true });

    action = { type: actions.VARS_DRAFT_SET_UPDATED, data: {} };
    newState = reducer(state, action);
    expect(newState).toStrictEqual({ ...state, draftSet: {}, hasDraft: false });
  });

  it('pending set updated', () => {
    let action = { type: actions.VARS_PENDING_SET_UPDATED, data: mockSet };
    let newState = reducer(state, action);
    expect(newState).toStrictEqual({ ...state, pendingSet: mockSet, hasPending: true });

    action = { type: actions.VARS_PENDING_SET_UPDATED, data: {} };
    newState = reducer(state, action);
    expect(newState).toStrictEqual({ ...state, pendingSet: {}, hasPending: false });
  });

  it('approvers loaded', () => {
    const approvers = [
      {
        id: 6,
        name: 'Dominick Han',
        email: '<EMAIL>',
        sid: 's1081456',
        active: true,
        roles: [ 1 ],
      },
    ];
    const action = { type: actions.VARS_APPROVERS_LOADED, data: approvers };
    const newState = reducer(state, action);
    expect(newState).toStrictEqual({ ...state, approvers });
  });
});
