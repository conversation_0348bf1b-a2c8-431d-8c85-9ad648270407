// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`alertDetails reducer Add Values To Alert Details Form 1`] = `
{
  "values": {
    "anonymous": true,
    "app_version": "1.0.42",
    "authenticated": true,
    "container": "PRE_LOGIN",
    "content_id": "2vZDr0xewgYwaWmywGICw4",
    "content_space": "4szkx38resvm",
    "content_type": "creditCardOffer",
    "created_at": "2018-09-19T15:00:37.327Z",
    "created_by": "string",
    "disabled": false,
    "end_at": "2018-09-19T15:00:37.327Z",
    "id": "string",
    "name": "Interac outage alert 2018-01-01",
    "platforms": [
      "ios",
      "android",
    ],
    "platforms_targeting": [
      {
        "items": [
          {
            "app_version": ">=1.2.3",
            "device_model": "iphone11",
            "os_version": "13.2.3 - 13.3.4",
          },
          {
            "device_model": "iphone10",
          },
        ],
        "platform": "ios",
        "v": 1,
      },
    ],
    "start_at": "2018-09-19T15:00:37.327Z",
    "status": "draft",
    "updated_at": "2018-09-19T15:00:37.327Z",
    "updated_by": "string",
  },
}
`;

exports[`alertDetails reducer Initial State 1`] = `
{
  "values": {},
}
`;

exports[`alertDetails reducer Populate Alert Details 1`] = `
{
  "values": {
    "anonymous": true,
    "app_version": "1.0.42",
    "authenticated": true,
    "container": "PRE_LOGIN",
    "content_id": "2vZDr0xewgYwaWmywGICw4",
    "content_space": "4szkx38resvm",
    "content_type": "creditCardOffer",
    "created_at": "2018-09-19T15:00:37.327Z",
    "created_by": "string",
    "disabled": false,
    "end_at": "2018-09-19T15:00:37.327Z",
    "id": "string",
    "name": "Interac outage alert 2018-01-01",
    "platforms": {
      "android": true,
      "ios": true,
    },
    "platforms_targeting": [
      {
        "items": [
          [
            {
              "targetingCriteria": "greaterEqual",
              "version": "1.2.3",
              "versionType": "app",
            },
            {
              "maxVersion": "13.3.4",
              "minVersion": "13.2.3",
              "targetingCriteria": "range",
              "versionType": "os",
            },
            {
              "model": "iphone11",
              "targetingCriteria": "equal",
              "versionType": "device",
            },
          ],
          [
            {
              "model": "iphone10",
              "targetingCriteria": "equal",
              "versionType": "device",
            },
          ],
        ],
        "platform": "ios",
        "v": 1,
      },
    ],
    "start_at": "2018-09-19T15:00:37.327Z",
    "status": "draft",
    "updated_at": "2018-09-19T15:00:37.327Z",
    "updated_by": "string",
  },
}
`;
