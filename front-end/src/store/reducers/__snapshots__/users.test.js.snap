// Jest <PERSON> v1, https://goo.gl/fbAQLP

exports[`users reducer Populate User Details 1`] = `
{
  "isLoading": false,
  "items": [
    {
      "active": true,
      "email": "<EMAIL>",
      "id": 1,
      "name": "<PERSON><PERSON>",
      "roles": [
        1,
        2,
        3,
        4,
      ],
      "sid": "s1081456",
      "updated_at": "2019-02-12T02:21:12.003Z",
    },
  ],
  "pagination": {
    "limit": undefined,
    "offset": undefined,
    "total": undefined,
  },
  "values": {
    "active": true,
    "email": "<EMAIL>",
    "id": 1,
    "name": "<PERSON><PERSON>",
    "roles": {
      "1": true,
      "2": true,
      "3": true,
      "4": true,
    },
    "sid": "s1081456",
    "updated_at": "2019-02-12T02:21:12.003Z",
  },
}
`;
