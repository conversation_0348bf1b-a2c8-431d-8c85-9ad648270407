// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`vignetteDetails reducer Add Values To Vignette Details Form 1`] = `
{
  "values": {
    "BU": [
      "B:",
      "CASL:",
    ],
    "BU_NOT": [
      "R:",
      "SDBI:SLP",
    ],
    "BU_RADIO": "BU_OR",
    "PROV": [
      "ON",
    ],
    "REGS": [
      "B",
      "I",
    ],
    "REGS_NOT": [
      "S",
    ],
    "REGS_RADIO": "REGS_AND",
    "campaign_name": null,
    "container": "solcontainer",
    "content_id": "7048",
    "contents": [
      {
        "container": "solcontainer",
        "container_disabled": false,
        "content_id": "7048",
        "content_space": "db",
        "content_type": "webfragment",
        "page": "activities",
        "subject": null,
      },
      {
        "container": "solcontainer",
        "container_disabled": false,
        "content_id": "7048",
        "content_space": "db",
        "content_type": "webfragment",
        "page": "accounts",
        "subject": null,
      },
    ],
    "created_at": "2019-10-02T03:05:02.003Z",
    "created_by": "s5891700",
    "deleted": false,
    "disabled": false,
    "end_date": "2019-10-09T04:00:00.000Z",
    "id": 87,
    "language": "en",
    "message_type": null,
    "name": "Mikes Campaign",
    "pages": [
      "activities",
      "accounts",
    ],
    "rule_id": "s3eEXpqoPb58",
    "start_date": "2019-10-01T04:00:00.000Z",
    "status": "draft",
    "subject_line": null,
    "system_message": null,
    "targeting": {
      "campaign_name": null,
      "country_code": "CA",
      "language_code": "en",
      "platforms": [
        {
          "platform_name": "web",
          "platform_version": null,
        },
      ],
      "products": [
        {
          "attribute_mode": "or",
          "attribute_type": "BU",
          "attribute_values": [
            "B:",
            "CASL:",
          ],
        },
        {
          "attribute_mode": "not",
          "attribute_type": "BU",
          "attribute_values": [
            "R:",
            "SDBI:SLP",
          ],
        },
        {
          "attribute_mode": "and",
          "attribute_type": "REGS",
          "attribute_values": [
            "B",
            "I",
          ],
        },
        {
          "attribute_mode": "not",
          "attribute_type": "REGS",
          "attribute_values": [
            "S",
          ],
        },
        {
          "attribute_mode": "and",
          "attribute_type": "PR1",
          "attribute_values": [
            "Banking:DDA:DDA:DDA:",
            "Banking:DDA:DDA:DDA:CA",
            "Borrowing:Mortgage:MOR:MOR:MT",
            "Investing:IP:IP:IPP:",
            "BRK:Brokerage:SDBI:SL:",
          ],
        },
        {
          "attribute_mode": "not",
          "attribute_type": "PR1",
          "attribute_values": [
            "Banking:DDA:DDA:DDA:SC",
            "Borrowing:Mortgage:MOR:MOR:",
            "Investing:BNS:IP:NRS:",
            "BRK:Brokerage:SDBI:SL:1",
          ],
        },
        {
          "attribute_mode": "or",
          "attribute_type": "PROV",
          "attribute_values": [
            "ON",
          ],
        },
      ],
    },
    "type": "mass",
    "updated_at": "2019-10-02T03:05:02.003Z",
    "updated_by": "s5891700",
    "urgent": false,
  },
}
`;

exports[`vignetteDetails reducer Initial State 1`] = `
{
  "values": {},
}
`;

exports[`vignetteDetails reducer Populate Vignette Details (no products) 1`] = `
{
  "values": {
    "BU": [
      "B:",
      "CASL:",
    ],
    "BU_NOT": [
      "R:",
      "SDBI:SLP",
    ],
    "BU_RADIO": "BU_OR",
    "PROV": [
      "ON",
    ],
    "REGS": [
      "B",
      "I",
    ],
    "REGS_NOT": [
      "S",
    ],
    "REGS_RADIO": "REGS_AND",
    "campaign_name": "hey there",
    "container": "solcontainer",
    "content_id": "7048",
    "contents": [
      {
        "container": "solcontainer",
        "container_disabled": false,
        "content_id": "7048",
        "content_space": "db",
        "content_type": "webfragment",
        "page": "activities",
        "subject": null,
      },
      {
        "container": "solcontainer",
        "container_disabled": false,
        "content_id": "7048",
        "content_space": "db",
        "content_type": "webfragment",
        "page": "accounts",
        "subject": null,
      },
    ],
    "created_at": "2019-10-02T03:05:02.003Z",
    "created_by": "s5891700",
    "deleted": false,
    "disabled": false,
    "end_date": "2019-10-09T04:00:00.000Z",
    "id": 87,
    "language": "en",
    "message_type": null,
    "name": "Mikes Campaign",
    "pages": [
      "activities",
      "accounts",
    ],
    "rule_id": "s3eEXpqoPb58",
    "start_date": "2019-10-01T04:00:00.000Z",
    "status": "draft",
    "subject_line": null,
    "system_message": null,
    "targeting": {
      "campaign_name": "hey there",
      "country_code": "CA",
      "language_code": "en",
      "platforms": [
        {
          "platform_name": "web",
          "platform_version": null,
        },
      ],
    },
    "type": "targeted",
    "updated_at": "2019-10-02T03:05:02.003Z",
    "updated_by": "s5891700",
    "urgent": false,
  },
}
`;

exports[`vignetteDetails reducer Populate Vignette Details (targeted) 1`] = `
{
  "values": {
    "BU": [
      "B:",
      "CASL:",
    ],
    "BU_NOT": [
      "R:",
      "SDBI:SLP",
    ],
    "BU_RADIO": "BU_OR",
    "PROV": [
      "ON",
    ],
    "REGS": [
      "B",
      "I",
    ],
    "REGS_NOT": [
      "S",
    ],
    "REGS_RADIO": "REGS_AND",
    "campaign_name": "hey there",
    "container": "solcontainer",
    "content_id": "7048",
    "contents": [
      {
        "container": "solcontainer",
        "container_disabled": false,
        "content_id": "7048",
        "content_space": "db",
        "content_type": "webfragment",
        "page": "activities",
        "subject": null,
      },
      {
        "container": "solcontainer",
        "container_disabled": false,
        "content_id": "7048",
        "content_space": "db",
        "content_type": "webfragment",
        "page": "accounts",
        "subject": null,
      },
    ],
    "created_at": "2019-10-02T03:05:02.003Z",
    "created_by": "s5891700",
    "deleted": false,
    "disabled": false,
    "end_date": "2019-10-09T04:00:00.000Z",
    "id": 87,
    "language": "en",
    "message_type": null,
    "name": "Mikes Campaign",
    "pages": [
      "activities",
      "accounts",
    ],
    "rule_id": "s3eEXpqoPb58",
    "start_date": "2019-10-01T04:00:00.000Z",
    "status": "draft",
    "subject_line": null,
    "system_message": null,
    "targeting": {
      "campaign_name": "hey there",
      "country_code": "CA",
      "language_code": "en",
      "platforms": [
        {
          "platform_name": "web",
          "platform_version": null,
        },
      ],
      "products": [
        {
          "attribute_mode": "or",
          "attribute_type": "BU",
          "attribute_values": [
            "B:",
            "CASL:",
          ],
        },
        {
          "attribute_mode": "not",
          "attribute_type": "BU",
          "attribute_values": [
            "R:",
            "SDBI:SLP",
          ],
        },
        {
          "attribute_mode": "and",
          "attribute_type": "REGS",
          "attribute_values": [
            "B",
            "I",
          ],
        },
        {
          "attribute_mode": "not",
          "attribute_type": "REGS",
          "attribute_values": [
            "S",
          ],
        },
        {
          "attribute_mode": "and",
          "attribute_type": "PR1",
          "attribute_values": [
            "Banking:DDA:DDA:DDA:",
            "Banking:DDA:DDA:DDA:CA",
            "Borrowing:Mortgage:MOR:MOR:MT",
            "Investing:IP:IP:IPP:",
            "BRK:Brokerage:SDBI:SL:",
          ],
        },
        {
          "attribute_mode": "not",
          "attribute_type": "PR1",
          "attribute_values": [
            "Banking:DDA:DDA:DDA:SC",
            "Borrowing:Mortgage:MOR:MOR:",
            "Investing:BNS:IP:NRS:",
            "BRK:Brokerage:SDBI:SL:1",
          ],
        },
        {
          "attribute_mode": "or",
          "attribute_type": "PROV",
          "attribute_values": [
            "ON",
          ],
        },
      ],
    },
    "type": "targeted",
    "updated_at": "2019-10-02T03:05:02.003Z",
    "updated_by": "s5891700",
    "urgent": false,
  },
}
`;

exports[`vignetteDetails reducer Update Vignette Details 1`] = `
{
  "values": {
    "BU": [
      "B:",
      "CASL:",
    ],
    "BU_NOT": [
      "R:",
      "SDBI:SLP",
    ],
    "BU_RADIO": "BU_OR",
    "PROV": [
      "ON",
    ],
    "REGS": [
      "B",
      "I",
    ],
    "REGS_NOT": [
      "S",
    ],
    "REGS_RADIO": "REGS_AND",
    "campaign_name": null,
    "container": "solcontainer",
    "content_id": "7048",
    "contents": [
      {
        "container": "solcontainer",
        "container_disabled": false,
        "content_id": "7048",
        "content_space": "db",
        "content_type": "webfragment",
        "page": "activities",
        "subject": null,
      },
      {
        "container": "solcontainer",
        "container_disabled": false,
        "content_id": "7048",
        "content_space": "db",
        "content_type": "webfragment",
        "page": "accounts",
        "subject": null,
      },
    ],
    "created_at": "2019-10-02T03:05:02.003Z",
    "created_by": "s5891700",
    "deleted": false,
    "disabled": false,
    "end_date": "2019-10-09T04:00:00.000Z",
    "id": 87,
    "language": "en",
    "message_type": null,
    "name": "Mikes Campaign",
    "pages": [
      "activities",
      "accounts",
    ],
    "rule_id": "s3eEXpqoPb58",
    "start_date": "2019-10-01T04:00:00.000Z",
    "status": "draft",
    "subject_line": null,
    "system_message": null,
    "targeting": {
      "campaign_name": null,
      "country_code": "CA",
      "language_code": "en",
      "platforms": [
        {
          "platform_name": "web",
          "platform_version": null,
        },
      ],
      "products": [
        {
          "attribute_mode": "or",
          "attribute_type": "BU",
          "attribute_values": [
            "B:",
            "CASL:",
          ],
        },
        {
          "attribute_mode": "not",
          "attribute_type": "BU",
          "attribute_values": [
            "R:",
            "SDBI:SLP",
          ],
        },
        {
          "attribute_mode": "and",
          "attribute_type": "REGS",
          "attribute_values": [
            "B",
            "I",
          ],
        },
        {
          "attribute_mode": "not",
          "attribute_type": "REGS",
          "attribute_values": [
            "S",
          ],
        },
        {
          "attribute_mode": "and",
          "attribute_type": "PR1",
          "attribute_values": [
            "Banking:DDA:DDA:DDA:",
            "Banking:DDA:DDA:DDA:CA",
            "Borrowing:Mortgage:MOR:MOR:MT",
            "Investing:IP:IP:IPP:",
            "BRK:Brokerage:SDBI:SL:",
          ],
        },
        {
          "attribute_mode": "not",
          "attribute_type": "PR1",
          "attribute_values": [
            "Banking:DDA:DDA:DDA:SC",
            "Borrowing:Mortgage:MOR:MOR:",
            "Investing:BNS:IP:NRS:",
            "BRK:Brokerage:SDBI:SL:1",
          ],
        },
        {
          "attribute_mode": "or",
          "attribute_type": "PROV",
          "attribute_values": [
            "ON",
          ],
        },
      ],
    },
    "type": "mass",
    "updated_at": "2019-10-02T03:05:02.003Z",
    "updated_by": "s5891700",
    "urgent": false,
  },
}
`;
