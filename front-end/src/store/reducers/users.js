import actions from '../actions';
import { mapArrayToTrueProps } from '../../constants';

const initialState = {
  isLoading: false,
  items: null,
  pagination: {
    offset: 0,
    total: null,
    limit: 0,
  },
};

export default (state = initialState, action = {}) => {
  switch (action.type) {
    case actions.USER_LOADING:
      return { ...state, isLoading: action.data };
    case actions.USER_LIST_DATA:
      return {
        items: action.data.items,
        pagination: {
          offset: action.data.offset,
          total: action.data.total,
          limit: action.data.limit,
        },
        isLoading: false,
      };
    case actions.USER_DETAILS_DATA:
      return {
        ...state,
        values: {
          ...state.values,
          ...action.data,
          roles: mapArrayToTrueProps(action.data.roles),
        },
      };
    default:
      return state;
  }
};
