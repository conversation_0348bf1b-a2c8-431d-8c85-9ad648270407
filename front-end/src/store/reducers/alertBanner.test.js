import reducer from './alertBanner';
import { actionType } from '../actions/alertBanner';

describe('alert banner tests', () => {
  it('should update state correctly when adding an alert banner', () => {
    expect(reducer({}, {
      type: actionType.ALERT_ADD,
      data: {
        mockProp: 1,
      },
    })).toStrictEqual({
      mockProp: 1,
    });
  });

  it('should update state correctly when removing an alert banner', () => {
    expect(reducer({}, {
      type: actionType.ALERT_REMOVE,
    })).toStrictEqual({});
  });

  it('test default params', () => {
    expect(reducer({})).toStrictEqual({});
  });
});
