import reducer from './platforms';
import { actionType } from '../actions/platforms';

describe('Platforms reducer', () => {
  it('should set loading state to platforms struct', () => {
    const newState = reducer({}, {
      type: actionType.PLATFORMS_LOADING,
      data: true,
    });
    expect(newState.isLoading).toStrictEqual(true);
  });
  it('should set loaded items to platforms items', () => {
    const mockItems = [ {
      platform_name: 'mock_platform_name',
    } ];
    const newState = reducer({}, {
      type: actionType.PLATFORMS_DATA,
      data: mockItems,
    });
    expect(newState.items).toStrictEqual(mockItems);
  });
});
