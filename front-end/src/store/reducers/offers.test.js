import offers from './offers';
import { offersLoading, offersData, setUpdateOfferStatusLoading, updateOfferStatus, offerDelete } from '../actions/offers';
import testData from '../../../../src/__mocks__/mockOffer';

const listTestData = {
  data: [ testData ],
  total: 100,
  offset: 1,
  limit: 10,
};

const stateTransformOfData = {
  items: [ testData ],
  pagination: {
    total: listTestData.total,
    offset: listTestData.offset,
    limit: listTestData.limit,
  },
  isLoading: false,
};

describe('offers reducer', () => {
  let state;

  it('Initial State', () => {
    state = offers(state, {});
    expect(state).toStrictEqual({
      items: [],
      pagination: {
        limit: 0,
        offset: 0,
        total: null,
      },
    });
  });

  it('Loading', () => {
    state = offers(state, offersLoading());
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Got data', () => {
    state = offers(state, offersData(listTestData));
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual(stateTransformOfData);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Load again', () => {
    state = offers(state, offersLoading());
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual({
      ...stateTransformOfData,
      isLoading: true,
    });
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(true);
  });

  it('Load error', () => {
    state = offers(state, offersLoading(false));
    expect(state).toHaveProperty('items');
    expect(state).toStrictEqual(stateTransformOfData);
    expect(state).toHaveProperty('isLoading');
    expect(state.isLoading).toBe(false);
  });

  it('Loading - Set offer active/inactive', () => {
    state = offers(state, setUpdateOfferStatusLoading());
    expect(state).toHaveProperty('isUpdateOfferStatusLoading');
    expect(state.isUpdateOfferStatusLoading).toBe(true);
  });

  it('Set offer active/inactive', () => {
    state = offers(state, updateOfferStatus(true, { data: { id: 'string', status: 'string' } }));
    expect(state).toHaveProperty('items');
    expect(state).toHaveProperty('isUpdateOfferStatusLoading');
    expect(state).toStrictEqual({
      ...stateTransformOfData,
      isUpdateOfferStatusLoading: true,
    });
  });

  it('Done loading - Set offer active/inactive', () => {
    state = offers(state, setUpdateOfferStatusLoading(false));
    expect(state).toHaveProperty('isUpdateOfferStatusLoading');
    expect(state.isUpdateOfferStatusLoading).toBe(false);
  });

  it('delete offer', () => {
    state = offers(state, offersData(listTestData));
    state = offers(state, offerDelete({ id: 'OFF-1' }));
    expect(state.items).toStrictEqual([]);
  });
});
