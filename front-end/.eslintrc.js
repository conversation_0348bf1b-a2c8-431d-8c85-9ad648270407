module.exports = {
  'parser': 'babel-eslint',
  'extends': [
    '@scotia/eslint-config-scotiabank',
    'plugin:react/recommended',
    'plugin:jest/recommended',
    'plugin:import/errors',
    'plugin:import/warnings'
  ],
  'plugins': [
    'jest',
    'react',
  ],
  'env': {
    'jest': true,
    'jest/globals': true,
  },
  'settings': {
    "import/extensions": [
      ".js",
      ".jsx"
    ],
    'import/resolver': { // https://stackoverflow.com/questions/********/using-eslint-with-typescript-unable-to-resolve-path-to-module
      'node': {
        'extensions': ['.js', '.jsx', '.ts', '.tsx'],
        'moduleDirectory': ['node_modules', 'src/'],
      },
    },
    'react': {
      'version': 'detect'
    }
  },
  'rules': {
    'jest/no-disabled-tests': 'warn',
    'jest/no-focused-tests': 'error',
    'jest/no-identical-title': 'error',
    'jest/no-jest-import': 'error',
    'jest/valid-expect': 'error',

    'jest/no-jasmine-globals': 'error',
    'jest/no-test-prefixes': 'error',

    'jest/prefer-strict-equal': 'warn',
    'jest/prefer-to-be-null': 'warn',
    'jest/prefer-to-be-undefined': 'warn',
    'jest/prefer-to-have-length': 'warn',
    'react/display-name': 0,

    'react/jsx-curly-spacing': [ 2, {
      'when': 'never',
      'children': {
        'when': 'always',
      },
    } ],
    'comma-dangle': [ 'error', 'always-multiline' ],
    'import/no-unresolved': [2, {commonjs: true, amd: true}],
    'import/no-named-as-default': 0,

    'template-curly-spacing': 'off',
    'indent': 'off',
  },
  'parserOptions': {
    'ecmaFeatures': {
      'legacyDecorators': true,
    },
  },
};
