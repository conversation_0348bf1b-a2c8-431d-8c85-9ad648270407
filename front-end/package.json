{"name": "@scotia/pigeon-admin-ui", "version": "0.0.0", "files": ["build"], "engines": {"node": "22.13.1", "npm": "^10.9.2"}, "scripts": {"stylelint": "stylelint **/*.scss", "stylelint:fix": "npm run stylelint -- --fix", "lint": "eslint src --ext .js,.jsx", "lint:fix": "eslint --fix src --ext .js,.jsx", "test": "TZ=utc jest src/  --runInBand --forceExit", "build": "webpack --mode production", "start": "webpack-dev-server", "analyze": "webpack --profile --json > stats.json && webpack-bundle-analyzer stats.json"}, "devDependencies": {"@babel/core": "7.26.0", "@babel/plugin-proposal-class-properties": "7.5.5", "@babel/plugin-proposal-decorators": "7.4.4", "@babel/plugin-proposal-optional-chaining": "7.12.13", "@babel/preset-env": "7.26.0", "@babel/preset-react": "7.0.0", "@scotia/eslint-config-scotiabank": "1.0.1", "@testing-library/jest-dom": "6.2.0", "@testing-library/react": "12.1.2", "@testing-library/user-event": "10.4.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.2", "babel-jest": "^29.7.0", "babel-loader": "^8.1.0", "babel-plugin-module-resolver": "^4.0.0", "conventional-changelog-eslint": "^3.0.9", "copy-webpack-plugin": "^10.2.4", "css-minimizer-webpack-plugin": "^6.0.0", "enzyme": "3.10.0", "enzyme-adapter-react-16": "1.14.0", "enzyme-to-json": "3.3.5", "eslint": "6.1.0", "eslint-plugin-jest": "22.14.0", "eslint-plugin-react": "7.14.3", "file-loader": "6.2.0", "html-loader": "^1.3.2", "html-webpack-harddisk-plugin": "2.0.0", "html-webpack-plugin": "^5.0.0", "identity-obj-proxy": "3.0.0", "jest": "29.7.0", "jest-canvas-mock": "^2.5.2", "jest-dom": "^4.0.0", "jest-environment-jsdom": "29.7.0", "mini-css-extract-plugin": "1.6.2", "node-polyfill-webpack-plugin": "^3.0.0", "redux-mock-store": "1.5.4", "redux-test-utils": "0.3.0", "sass": "1.22.9", "sass-loader": "13.3.2", "sass-mq": "5.0.1", "stylelint": "^16.18.0", "stylelint-config-sass-guidelines": "^12.1.0", "stylelint-config-standard": "^38.0.0", "webpack": "^5.88.2", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.2.1"}, "dependencies": {"@testing-library/react-hooks": "^8.0.1", "axios": "1.12.1", "canvas-core-react": "13.11.0", "canvg": "^4.0.1", "classnames": "^2.3.1", "cookie": "^1.0.2", "core-js": "2.6.8", "css-loader": "6.9.0", "ejs": "3.1.9", "history": "4.9.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.2", "lodash.throttle": "^4.1.1", "math-random": "1.0.4", "moment": "^2.29.4", "pigeon-pigeon-web-renderer": "^4.0.17", "prop-types": "15.7.2", "qs": "^6.14.0", "ramda": "^0.28.0", "react": "16.14.0", "react-datetime": "2.16.3", "react-dom": "16.13.1", "react-hook-form": "^7.30.0", "react-redux": "7.1.0", "react-router": "^5.1.2", "react-router-dom": "^5.1.2", "redux": "4.0.4", "redux-form": "8.3.8", "redux-thunk": "2.3.0", "sass-mq": "5.0.1", "semver": "^7.5.2", "styled-components": "^5.3.1"}, "overrides": {"axios": "1.12.1", "brace-expansion": "2.0.2", "@azure/identity": "4.10.1", "@azure/core-tracing": "^1.0.1", "form-data": "^4.0.4", "sha.js": "2.4.12"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/test-setup.js"], "coverageReporters": ["json", "lcov", "text", "html"], "coverageThreshold": {"global": {"branches": 75, "functions": 75, "lines": 75, "statements": 75}}, "coveragePathIgnorePatterns": [], "moduleNameMapper": {".+\\.(css|styl|less|sass|scss|png|jpg|svg|ttf|woff|woff2)$": "identity-obj-proxy"}, "snapshotSerializers": ["enzyme-to-json/serializer"], "collectCoverage": true, "collectCoverageFrom": ["src/**/*.(js|jsx)"]}}