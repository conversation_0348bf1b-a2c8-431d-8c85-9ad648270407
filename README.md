# Pigeon Admin Portal

#### Try live

- **IST**: https://pigeon-admin-ist.apps.cloud.bns/
- **UAT**: https://pigeon-admin-uat.apps.cloud.bns/
- **Production**: https://pigeon-admin.apps.cloud.bns/

## Run on your machine

### Clone repo and install dependacies

```
git clone https://bitbucket.agile.bns/scm/pigeon/admin.git

cd admin

# Install backend dependancies
# if on Windows, manually apply postinstall script changes to npmrc as needed, then disable the postinstall script
npm install

```

### Get the database running on local

Follow the README in this repo to setup local Docker container with a database
for Pigeon:
https://bitbucket.agile.bns/projects/PIGEON/repos/docker-db-v2/browse Once the
docker container is running in a separate process, you can continue to the next
step.

### Sync local database with IST/UAT database

Follow the README in this repo to sync the local database started in the
previous step with IST/UAT database:
https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-db-sync/browse

### Adding external services URL to no_proxy/NO_PROXY list

In order to make requests to other services, you'll need to add them into your
no_proxy list in ~/.zshrc or ~/.bash_profile. In this case, we need to add
WAM-IST, rule-api-ist, and content-api-ist.

```bash
export no_proxy=$no_proxy,wam-ist.cloud.bns,rule-api-ist.apps.cloud.bns,content-api-ist.apps.cloud.bns
export NO_PROXY=$NO_PROXY
```

- Failing to add `wam-ist.cloud.bns` in the no_proxy list will cause this error:
  {"code":"ERROR_WAM_AUTHORIZE"}
- Failing to add `rule-api-ist.apps.cloud.bns` and
  `content-api-ist.apps.cloud.bns` will cause these errors:
  - HTTP 500 GET
    /api/v1/campaign-rules?offset=0&limit=10&sort=-updated_at&type=campaign
    274ms
  - HTTP 500 GET
    /api/v1/alert-rules?offset=0&limit=10&sort=-updated_at&type=alert 289ms
  - HTTP 500 GET /api/v1/variable-mappings/sets?status=active 252ms

### Environment variables

Admin uses `dotenv` to manage environment variables.\
There is an example env file in the root of this repo `.env.example`, copy it with
a new name `.env` for the project to start utizing the env variables:

```bash
cp .env.example .env

# Reach out to other team members to get the latest environment variables
```

### Run the project

You will neen to run 2 terminals (3 with database) to run front end and back end
of the application:

#### Terminal 1 (front end)

```
cd front-end
npm start
```

#### Terminal 2 (backend)

```
npm start
```

### Unit Tests

```
npm run test
```

Note that if you're on windows, due to our reliance on node's path module to
resolve file paths, any unit test which has had its snapshot generated from
non-Windows environment will fail.
https://nodejs.org/api/path.html#path_path_normalize_path.

Even though windows recognize both forward and backslash as valid path segment
characters, path module will always generate backslash for Windows and forward
slash for POSIX when asked to resolve a path.

## Functionality

Pigeon Admin portal is a front end application that allows teams across the
organization to publish messages across digital channels of Scotiabank.

Currently, you can publish:

1. Pre-login alerts for the Scotiabank mobile app (Nova)
2. Campaigns inside the Scotiabank mobile app (Nova)
3. Campaigns for the online banking site (SOL) - (interim while SOL is being
   rebuilt)
4. Campaigns for the Products and Services (which is an interim solution before
   Products and Services being rebuilt Hubble is the name for the new project)

## Architecture

Admin portal consists of the two parts and a database:

### 1) Front end application

#### Tech stack:

**Function:** Provides a visual interface to manage alerts, campaigns,
containers, users.\
**UI Library:** React\
**State management:** Redux\
**Testing:** Jest, React testing library, Enzyme (being migrated to React testing
library)

### 2) Backend

**Function:** Mostly acts as a proxy to other microservices, however also
implements it's own REST interfaces for internal cosumption (for example for
Container management, user management - by talking to the DB directly). It also
gives other added functionality better described in integrations section.\
**Tech stack:** Node.js (10.10.0), Express JS, Jest, Knex (as Query Builder for SQL)

### 3) Database:

MSSQL (Managed by Azure)

## Integrations

Pigeon Admin makes calls and talks to other microservies:

1. [Rule API](https://bitbucket.agile.bns/projects/PIGEON/repos/rule-api/) -
   Pigeon Ecosystem - used to get a list of rules (campaigns/alerts), create and
   modify rules.
2. [Content API](https://bitbucket.agile.bns/projects/PIGEON/repos/content-api/) -
   Pigeon Ecosystem - used to retrieve a list of contents that can be attached
   to the rule (for mobile campaigns and alerts) and to get a list of web
   fragments for SOL and P&S Interim Solutions.
3. **Passport** an internal Scotiabank OAuth provider that allows users to
   authenticate with sID/password combo.
4. [Contentful](https://www.contentful.com/) - although Contentful API is not
   untilized directly. Admin works hand to hand with
   [**Contentful Live Preview extension**](https://bitbucket.agile.bns/projects/PIGEON/repos/contentful-live-preview-extension/browse)
   to provide a Live preview of campaigns to users. A user is able to see what
   their content looks like in a template as they work on it. The communication
   between a widget inside Contentful and Admin preview window is implemented
   via `window.postMessage` protocol. You can see the implementation in
   `front-end/components/preview/index.jsx`.

## Updating Pigeon Web Renderer

- View all packages of pigeon-web-renderer
  `npm view pigeon-pigeon-web-renderer versions`
- Install specfic exact version of pigeon-web-renderer
  `npm i pigeon-pigeon-web-renderer@<version> --save --save-exact`
