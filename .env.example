NODE_ENV=development
NODE_TLS_REJECT_UNAUTHORIZED=0
SERVER_PORT=8090

RULE_API_URL=https://rule-api-ist.apps.cloud.bns
CONTENT_API_URL=https://content-api-ist.apps.cloud.bns/v1
ADMIN_REDIRECT_URL=http://localhost:8090

CAMPAIGN_MANAGEMENT_API_URL=http://localhost:3001
OFFERS_API_URL=

KNEX_DB_NAME=master
KNEX_DB_USER=sa
KNEX_DB_PWD=Password1!
KNEX_DB_PORT=1433
KNEX_DB_SERVER=127.0.0.1

CDP_SECRET_SSO_PRIVATE_KEY="********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
CDP_SECRET_S2S_PRIVATE_KEY="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
PASSPORT_PUBLIC_KEY="LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF3YXk2N1dzbTNQd0wxM0ZGUXY4cApCKy9yUFBPWHEwQVJ4VFc0ZkRQdHBuM0dtbkNpWTdmMVg0U2s1SXllOFlmc1JMWllCaFV0a2RSclozYlBKS2p1CkljVEVDMkl6L3RoODZZKzZqbTA1RUVZRVJoN2lPS1pNQ0x0VnNsa2tSVzV4OE1OYWpvVG5idlBZbEZwYVo5bFUKN3Z5bGJ0UjBEVmc5TmhsbkJTbkxaeDBCZE1YTThvQXBvWXFTN1V4cXZycFNTNzBwUW9RcUdYamtpMmlPMGZZUwpmTmxvc1R0Nm1JQm5ENlBXRVNkTHBRRGI3MzlRenkxYXVYT2M4VDRKUUZXem5OVmdTOTIxKzh4YmZSN0QzeU5TClBTYWR1WXdYUDhoTUwzbyt2Y0NjUGtZZ2xoenl3Szd2RFVwQjFic1pvZW9FVzc2UlhGaXlCK2pFYVVmSnB1R3EKM1FJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg=="
PASSPORT_API_TIMEOUT=3000
PASSPORT_API_S2S_SCOPE=ca:baas:alert-rules:read,ca:baas:alert-rules:write,ca:baas:campaign-rules:read,ca:baas:campaign-rules:write,ca:baas:contents:read,ca:baas:rules:read,ca:baas:rules:write,cdb.pigeon.campaign-management.campaigns.read,cdb.pigeon.campaign-management.campaigns.write,cdb.pigeon.campaign-management.offers.read,cdb.pigeon.campaign-management.offers.write,customer.offer-management.bjqx.admin.read,customer.offer-management.bjqx.admin.write,customer.offer-management.bjqx.admin.delete
PASSPORT_API_S2S_TTL=60
PASSPORT_API_S2S_ALGORITHM=RS256
PASSPORT_API_S2S_CLIENTID=f4dd8dcc-8229-45dc-88a0-3e720745ef20
PASSPORT_API_URI=https://passport-oauth-ist.apps.cloud.bns/oauth2/v1
PASSPORT_URL=https://passport-oauth-ist.apps.cloud.bns
PASSPORT_CLIENT_ASSERTION=urn:ietf:params:oauth:client-assertion-type:jwt-bearer
PASSPORT_CLIENT_ID=d6bad7e0-3a85-4370-b8d2-bd2e44b4e09e
PASSPORT_REDIRECT_URL=http://localhost:8090

AUTH_S2S_CLAIM_EXPIRESIN='1h'
AUTH_S2S_CLAIM_NOTBEFORE='-10s'
AUTH_S2S_PUBLIC_KEY=

FRONTEND_URL=http://localhost:8080
USER_TOKEN_DURATION=60
SECURE_COOKIE=0
CONTENTFUL_ENTRY_URL=https://app.contentful.com/spaces/%s/environments/IST/entries/%s

# content security policy
CSP_IMAGE_SRC='["https://images.ctfassets.net","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net","data:"]'
CSP_FRAME_SRC='["https://*.demdex.net"]'
CSP_SCRIPT_SRC='["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net"]'
CSP_CONNECT_SRC='["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net","http://localhost:8090","ws://localhost:8090"]'

# Rate limiting fallback config
RATE_LIMIT_CLIENT_MAX=180
RATE_LIMIT_OVERALL_MAX=2000
RATE_LIMIT_CDP_TRUSTED_IP='***********/23'
CDP_SECRET_LAUNCH_DARKLY_SDK_KEY=
PROXY_URL=http://pp-webproxy.bns:8080

# WAM authentication
WAM_TOKEN_URL=https://wam-ist.cloud.bns/sso/oauth2/bns/access_token
WAM_TOKEN_INFO_URL=https://wam-ist.cloud.bns/sso/oauth2/bns/tokeninfo
WAM_AUTHORIZE_URL=https://wam-ist.cloud.bns/sso/oauth2/bns/authorize
WAM_CLIENT_ID=PigeonAdmin
CDP_SECRET_WAM_CLIENT_SECRET=
WAM_REDIRECT_URL=http://localhost:8090/authorization
WAM_POST_LOGOUT_REDIRECT_URL='https://wam-ist.cloud.bns/sso/XUI/?realm=/bns#logout/&goto=http://localhost:8090'
WAM_JWKS_URL=https://wam-ist.cloud.bns/sso/oauth2/bns/connect/jwk_uri
VALID_RETURN_DOMAIN=localhost
REDIS_URL='redis://localhost:6379'

OFFERS_MANAGEMENT_API_URL=https://cdb-int-offer-management-nane2-ist.nonp.atlas.bns/v1
