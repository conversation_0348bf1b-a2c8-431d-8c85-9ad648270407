build:
  template: 'npm:generic'
  substitutions:
    _ARTIFACTORY_PATH: 'pigeon-admin'
    _SONAR_SCAN_STRATEGY: 'SKIP'
    _FORTIFY_SCAN_STRATEGY: 'SKIP'
    _FORTIFY_EXCLUDE: '*.test.js *.test.jsx'
    _BLACKDUCK_SCAN_STRATEGY: 'SKIP'
    _VERSION_STRATEGY: DEV_TEAM
    _NODEJS_VERSION: 22.13.1
  steps:
    - name: ${NODE_STEP}
      workdir: '/workspace'
      env:
        - '_IS_PUBLISHING_ARTIFACT=${_IS_PUBLISHING_ARTIFACT}'
        - 'ARTIFACTORY_USERNAME_READONLY=${_ACCP_SECRET_ENV_ARTIFACTORY_USERNAME_READONLY}'
        - 'ARTIFACTORY_PASSWORD_READONLY=${_ACCP_SECRET_ENV_ARTIFACTORY_PASSWORD_READONLY}'
      args:
        - sh
        - -c
        - -e
        - |
          '
          set -eo pipefail;
          AUTH=`echo -n "${ARTIFACTORY_USERNAME_READONLY}:${ARTIFACTORY_PASSWORD_READONLY}" | base64`;
          echo "
          //af.cds.bns/artifactory/api/npm/virtual-npm-bns/:_auth=$AUTH
          always-auth=true
          email=<EMAIL>
          lockfile-version=2
          registry=https://af.cds.bns/artifactory/api/npm/virtual-npm-bns
          loglevel=verbose
          cafile=/etc/ssl/certs/root.cer
          " > /root/.npmrc;
          export PATH=/root/node/bin:$PATH;
          npm run check;
          [ ${_IS_PUBLISHING_ARTIFACT} == 0 ] || npm run bumpVersion;
          '