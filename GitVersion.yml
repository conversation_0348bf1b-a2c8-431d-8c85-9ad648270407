assembly-versioning-scheme: MajorMinorPatch
assembly-file-versioning-scheme: MajorMinorPatch
mode: ContinuousDelivery
tag-prefix: '[vV]'
continuous-delivery-fallback-tag: 'prerelease'
major-version-bump-message: '\+semver:\s?(breaking|major)'
minor-version-bump-message: '\+semver:\s?(feature|minor)'
patch-version-bump-message: '\+semver:\s?(fix|patch)'
no-bump-message: '\+semver:\s?(none|skip)'
legacy-semver-padding: 4
build-metadata-padding: 4
commits-since-version-source-padding: 4
tag-pre-release-weight: 60000
commit-message-incrementing: Enabled
branches:
  feature:
    mode: ContinuousDeployment
    tag: useBranchName
    increment: None
    prevent-increment-of-merged-branch-version: false
    track-merge-target: false
    regex: ^(feature|chore|bugfix)[/-]
    source-branches:
      - main
      - release
      - feature
      - hotfix
    tracks-release-branches: false
    is-release-branch: false
    is-mainline: false
    pre-release-weight: 30000
  main:
    mode: ContinuousDelivery
    tag: ''
    increment: None
    prevent-increment-of-merged-branch-version: true
    track-merge-target: false
    regex: ^main$
    source-branches:
    - release
    tracks-release-branches: false
    is-release-branch: false
    is-mainline: true
    pre-release-weight: 55000
  release:
    mode: ContinuousDeployment
    tag: rc.{BranchName}
    increment: Minor
    prevent-increment-of-merged-branch-version: true
    track-merge-target: false
    regex: ^release?[/-]
    source-branches:
    - main
    - release
    tracks-release-branches: false
    is-release-branch: true
    is-mainline: false
    pre-release-weight: 30000
  pull-request:
    mode: ContinuousDelivery
    tag: PullRequest
    increment: Inherit
    prevent-increment-of-merged-branch-version: false
    tag-number-pattern: '[/-](?<number>\d+)'
    track-merge-target: false
    regex: ^(pull|pull\-requests|pr)[/-]
    source-branches:
    - main
    - release
    - feature
    - hotfix
    tracks-release-branches: false
    is-release-branch: false
    is-mainline: false
    pre-release-weight: 30000
  hotfix:
    mode: ContinuousDeployment
    tag: hotfix.{BranchName}
    increment: Patch
    prevent-increment-of-merged-branch-version: false
    track-merge-target: false
    regex: ^hotfix(es)?[/-]
    source-branches:
    - main
    tracks-release-branches: false
    is-release-branch: false
    is-mainline: false
    pre-release-weight: 30000
ignore:
  sha: []
commit-date-format: yyyy-MM-dd
merge-message-formats: {}
update-build-number: true